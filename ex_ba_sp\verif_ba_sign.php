<?php

ob_start();
session_start();
include_once('../include/ex_fungsi.php');
include_once('../include/e_sign.php');
include_once('helper.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$no_ba = $_POST['no_ba'];

$signType = (isset($_POST['sign_type']) && $_POST['sign_type'] == 'otp') ? 'otp' : 'keyla';
$isSignTypeOtp = $signType == 'otp';
$isSignTypeKeyla = $signType == 'keyla';

$isSubmitResendOtp = isset($_POST['submit']) && $_POST['submit'] == 'resend_otp';
$isSubmitOtp = isset($_POST['submit']) && $_POST['submit'] == 'submit_otp';

$postData = array();
$blacklistedPostData = array('submit');

foreach ($_POST as $key => $value) {
    if (in_array($key, $blacklistedPostData)) {
        continue;
    }

    $postData[$key] = $value;
}

function showErrorMessage($message)
{
    global $postData;

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-danger" role="alert">
            <form method="POST" action="">
                <?php foreach ($postData as $key => $value) : ?>
                    <input type="hidden" name="<?= $key ?>" value="<?= $value ?>" />
                <?php endforeach ?>

                <strong>Error!</strong>
                <br>
                <br>
                <div class="" role="alert"><?= $message ?></div>
                <br>
                <a id="coba-lagi-btn" href="" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Coba Lagi&nbsp;&nbsp;&gt;&gt;</a>
                <a href="kabiro_ba_trans.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>

                <button id="submit-btn" type="submit" style="display: none;">submit</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('coba-lagi-btn').addEventListener('click', function(e) {
            e.preventDefault();

            document.getElementById('submit-btn').click();
        });
    </script>
<?php
}

// $query = "SELECT * FROM EX_BA WHERE NO_BA_EX = '$no_ba'";
// $sql = oci_parse($conn, $query);
// oci_execute($sql);

// $data = oci_fetch_array($sql);

// $no_ba_v = $data['NO_BA'];
// $org_v = $data['ORG'];
// $no_vendor_v = $data['NO_VENDOR'];
// $nama_vendor_v = $data['NAMA_VENDOR'];
// $total_semen_v = $data['KLAIM_SEMEN'];
// $total_ppdks_v = $data['PPDKS'];
// $total_inv_v = $data['TOTAL_INV'];
$query_ba = "SELECT
                        EX_BA.ID,
                        EX_BA.NO_BA,
                        EX_BA.NO_VENDOR,
                        EX_BA.TOTAL_INV,
                        EX_BA.PAJAK_INV,
                        EX_BA.NAMA_VENDOR,
                        EX_BA.KLAIM_KTG,
                        EX_BA.KLAIM_SEMEN,
                        EX_BA.PDPKS,
                        EX_BA.PDPKK,
                        EX_BA.DELETE_MARK,
                        EX_BA.ORG,
                        EX_BA.TOTAL_INVOICE,
                        EX_BA.TGL_BA,
                        EX_BA.STATUS_BA,
                        EX_BA.FILENAME,
                        EX_BA.ALASAN_REJECT,
                        EX_BA.ID_USER_APPROVAL,
                        EX_BA.SIGN_STATUS_2,
                        EX_BA.SIGN_ORDER_ID_2,
                        EX_BA.SIGN_TOKEN_2,
                        SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
                        SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
                        SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
                        SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
                        SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
                        SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
                      SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
                      SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
                      SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
                      SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
                        to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
                    FROM
                        EX_BA
                        JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
                    WHERE EX_BA.DELETE_MARK = '0' 
                        AND EX_BA.NO_BA = :no_ba
                    GROUP BY EX_BA.ID,
                        EX_BA.NO_BA,
                        EX_BA.NO_VENDOR,
                        EX_BA.TOTAL_INV,
                        EX_BA.PAJAK_INV,
                        EX_BA.NAMA_VENDOR,
                        EX_BA.KLAIM_KTG,
                        EX_BA.KLAIM_SEMEN,
                        EX_BA.PDPKS,
                        EX_BA.PDPKK,
                        EX_BA.DELETE_MARK,
                        EX_BA.ORG,
                        EX_BA.TOTAL_INVOICE,
                        EX_BA.TGL_BA,
                        EX_BA.STATUS_BA,
                        EX_BA.FILENAME,
                        EX_BA.ALASAN_REJECT,
                        EX_BA.SIGN_STATUS_2,
                        EX_BA.SIGN_ORDER_ID_2,
                        EX_BA.SIGN_TOKEN_2,
                        EX_BA.ID_USER_APPROVAL
                    ORDER BY
                        EX_BA.ID DESC";
// echo $query_ba;
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data = oci_fetch_array($sql_ba);
// print_r($data);exit;
$no_ba_v = $data['NO_BA'];
$org_v = $data['ORG'];
$no_vendor_v = $data['NO_VENDOR'];
$nama_vendor_v = $data['NAMA_VENDOR'];
$total_semen_v = $data['TOTAL_KLAIM_SEMEN'];
$total_kantong_v = $data['TOTAL_KLAIM_KTG'];
$total_ppdks_v = $data['PDPKS'];
$total_inv_v = $data['SHP_COST'];
//INSERT LOG HISTORY BA
$email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
<div align=\"center\">
<thead>
<tr class=\"quote\">
<td ><strong>&nbsp;&nbsp;No.</strong></td>
<td align=\"center\"><strong>ORG</strong></td>
<td align=\"center\"><strong>BA REKAPITULASI</strong></td>
<td align=\"center\"><strong>EKSPEDITUR</strong></td>
<td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
<td align=\"center\"><strong>KLAIM SEMEN</strong></td>
<td align=\"center\"><strong>PDPKS</strong></td>
<td align=\"center\"><strong>TOTAL</strong></td>
<td align=\"center\"><strong>STATUS</strong></td>
</tr>
</thead>
<tbody>";

$email_content_table .= " 
<td align=\"center\">1</td>
<td align=\"center\">".$org_v."</td>       
<td align=\"center\">".$no_ba_v."</td>
<td align=\"center\">".$no_vendor_v."</td>
<td align=\"center\">".$nama_vendor_v."</td>
<td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
<td align=\"center\">Open</td>
</tr>";

$tableName = 'EX_BA';
$field_id = array('NO_BA_EX');
$value_id = array("$no_ba");

$signStatus = $data['SIGN_STATUS_2'];
$isDocBelumDikirim = !$signStatus || $signStatus == 0;
$isDocSent = $signStatus == 1;
$isOtpSent = $signStatus == 2;
$isSigned = $signStatus == 3;
$isDownloaded = $signStatus == 4;

$eSign = new ESign();

$orderId = $data['SIGN_ORDER_ID_2'];

if ($isDocBelumDikirim) {
    // Get file content as base64
    $fileName = $data['FILENAME'];
    $fileContent = file_get_contents('upload/' . $fileName);
    $fileContent = base64_encode($fileContent);

    try {
        $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$_SESSION['user_id'];
// echo $sql;
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		$mailTo = $row[ALAMAT_EMAIL];

        // Send dokumen ke peruri
        $orderId = $eSign->uploadDocument(array(
            // TODO: change the email
            // 'email' => '<EMAIL>',
            'email' => $mailTo,
            'fileName' => $fileName,
            'base64Document' => $fileContent,
            'lowerLeftX' => '345',
            'lowerLeftY' => '503',
            'upperRightX' => '446',
            'upperRightY' => '571',
            'page' => '1',
            'varReason' => 'Approve BASTP SM of Operational Transportation',
        ));
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    // Save order id yang didapatkan dari eSign
    $fieldNames = array('SIGN_ORDER_ID_2', 'SIGN_STATUS_2');
    $fieldData = array($orderId, '1');
    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data['SIGN_ORDER_ID_2'] = $orderId;
    $isDocBelumDikirim = false;
    $isDocSent = true;
}

if ($isSignTypeOtp && ($isDocSent || $signStatus >= 1) && !$isSubmitResendOtp && !$isSubmitOtp) {
    $dataGetOtp = null;

    try {
        $dataGetOtp = $eSign->getOtp($data['SIGN_ORDER_ID_2']);
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $fieldNames = array('SIGN_STATUS_2', 'SIGN_TOKEN_2');
    $fieldData = array('2', $dataGetOtp->token);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $isDocSent = false;
    $isOtpSent = true;
}

if ($isDocSent || $isOtpSent) {
    try {
        // cek status doc
        $resp = $eSign->checkDoc($data['SIGN_ORDER_ID_2']);

        // update status di DB
        if($resp){
            $fieldNames = array('SIGN_STATUS_2');
            $fieldData = array(3);
            $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
            $data_invoice_ba['SIGN_STATUS_2'] = 3;

            $isDocSent = false;
            // $isOtpSent = false;
            $isSigned = true;
        }
    } catch (Exception $e) {
        // $message = $e->getMessage();
        // showErrorMessage($message);
        // exit;
    }
}

if ($isSignTypeOtp && $isOtpSent && $isSubmitResendOtp && !$isSubmitOtp) {
    try {
        $dataGetOtp = $eSign->getOtp($orderId);
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $fieldNames = array('SIGN_TOKEN_2', 'SIGN_STATUS_2');
    $fieldData = array($dataGetOtp->token, '2');

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $message = 'Kode OTP berhasil dikirim ulang';
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <form method="POST" action="">
                <?php foreach ($postData as $key => $value) : ?>
                    <input type="hidden" name="<?= $key ?>" value="<?= $value ?>" />
                <?php endforeach ?>

                <strong>Pesan!</strong>
                <br>
                <br>
                <div class="alert alert-warning" role="alert"><?= $message ?></div>
                <br>
                <a id="coba-lagi-btn" href="" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>

                <button id="submit-btn" type="submit" style="display: none;">submit</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('coba-lagi-btn').addEventListener('click', function(e) {
            e.preventDefault();

            document.getElementById('submit-btn').click();
        });
    </script>
<?php
    exit;
}

if (($isSignTypeKeyla || ($isSignTypeOtp && $isOtpSent)) && $isSubmitOtp) {
    $otpCode = $_POST['OTP'];
    $keylaCode = $_POST['keyla'];

    $resultCode = null;

    try {
        if ($isSignTypeKeyla) {
            $resultCode = $eSign->signing(array(
                'otpCode' => '',
                'orderId' => $orderId,
                'token' => $keylaCode,
            ));
        } else if ($isSignTypeOtp) {
            $resultCode = $eSign->signing(array(
                'otpCode' => $otpCode,
                'orderId' => $orderId,
                'token' => $data['SIGN_TOKEN_2'],
            ));
        } else {
            throw new Exception('Tipe sign tidak dikenali');
        }
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $fieldNames = array('SIGN_STATUS_2');
    $fieldData = array('3');

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    //sendEmail
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA = :no_ba and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_bind_by_name($query, ":no_ba", $no_ba);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    // $mailTo .= ', <EMAIL>';
    $mailCc = '';
    
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve BASTP', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }
    //end senEmail

    $isDocSent = false;
    $isOtpSent = false;
    $isSigned = true;
}

if ($isSigned) {
    $fileName = $data['FILENAME'];

    $pdfBase64Encoded = null;

    try {
        $pdfBase64Encoded = $eSign->downloadDocument($orderId);
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    if (!file_exists('upload')) {
        mkdir('upload', 0777, true);
    }

    $pdfBase64Decoded = base64_decode($pdfBase64Encoded);
    $pdf = fopen("upload/$fileName", 'w');
    fwrite($pdf, $pdfBase64Decoded);
    fclose($pdf);

    $fieldNames = array('SIGN_STATUS_2');
    $fieldData = array('4');

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $isSigned = false;
    $isDownloaded = true;
}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Sign Dokumen BASTP</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />

    <style>
        table.excel {
            border-style: ridge;
            border-width: 1;
            border-collapse: collapse;
            font-family: sans-serif;
            font-size: 12px;
        }

        table.excel thead th,
        table.excel tbody th {
            background: #CCCCCC;
            border-style: ridge;
            border-width: 1;
            text-align: center;
            vertical-align: bottom;
        }

        table.excel tbody th {
            text-align: center;
            width: 20px;
        }

        table.excel tbody td {
            vertical-align: bottom;
        }

        table.excel tbody td {
            padding: 0 3px;
            border: 1px solid #EEEEEE;
        }
    </style>
</head>

<body>
    <div align="center">
        <table width="800" align="center" class="adminheading" border="0">
            <tr>
                <th class="da2">Sign BASTP</th>
            </tr>
        </table>
    </div>

    <form method="post" name="import" id="import" action="">
        <?php if ($isDownloaded) : ?>
            <button type="submit" id="submit_sign">Submit</button>
        <?php endif ?>

        <?php foreach ($postData as $key => $value) : ?>
            <input type="hidden" name="<?= $key ?>" value="<?= $value ?>" />
        <?php endforeach ?>

        <table width="800" align="center" class="adminform">
            <tr height="30">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso" colspan="3">
                    <h3 style="margin-bottom: 0 !important;">&nbsp;&nbsp;E-Sign Kabiro</h3>
                </td>
            </tr>
            <?php if ($isSignTypeOtp) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;OTP</td>
                    <td class="puso" width="20">:</td>
                    <td><input type="number" id="OTP" name="OTP" /></td>
                </tr>
            <?php endif ?>
            <?php if ($isSignTypeKeyla) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;Token Keyla</td>
                    <td class="puso" width="20">:</td>
                    <td><input type="text" id="keyla" name="keyla" autocomplete="off" /></td>
                </tr>
            <?php endif ?>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td>
                    <button id="sign_btn" type="submit" name="submit" value="submit_otp" class="btn btn-primary" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">
                        Sign
                    </button>

                    <button type="submit" name="submit" id="resend_otp" value="resend_otp" class="btn btn-secondary" style="display: none;">Resend Kode OTP</button>
                    <?php if ($isSignTypeOtp) : ?>
                        <a id="resend_otp_link" href="">
                            Kirim Ulang OTP
                        </a>
                    <?php endif ?>
                </td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>

            </tr>
        </table>
    </form>
    <br><br>

    <div align="center">
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

    <?php if ($isDownloaded) : ?>
        <script>
            const form = document.getElementById('import');
            const submitBtn = document.getElementById('submit_sign');

            form.action = 'komentar.php';
            submitBtn.click();
        </script>
    <?php endif ?>

    <?php if (!$isDownloaded) : ?>
    <script>
        const form = document.getElementById('import');
        const signBtn = document.getElementById('sign_btn');

        const resendOtpBtn = document.getElementById('resend_otp');
        const resendOtpLink = document.getElementById('resend_otp_link');
        
        form.addEventListener('submit', function() {
            signBtn.innerHTML = 'Loading...';
        });

        resendOtpLink.addEventListener('click', function(e) {
            e.preventDefault();

            resendOtpLink.innerHTML = 'Loading...';

            resendOtpBtn.click();
        });
    </script>
    <?php endif ?>
</body>

</html>