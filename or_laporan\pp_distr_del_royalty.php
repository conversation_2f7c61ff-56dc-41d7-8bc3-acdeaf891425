<? 

require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


$no_pp = '';
$sold_to = '';
$ship_to = '';
$produk = '';
$jenis_kirim = '';
$jenis_order = '';
$tgl1 = '';
$tgl2 = '';
$branch_plant = '';




session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$user_name=$_SESSION['user_name'];
$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="pp_distr_del_royalty.php";
$no_pp = $_POST['no_pp'];
$branch_plant = $_POST['branch_plant'];

$ship_to = $_POST['ship_to'];
$sold_to = $_POST['sold_to'];
$produk = $_POST['produk'];
$jenis_kirim = $_POST['jenis_kirim'];
$jenis_order = $_POST['jenis_order'];
$tgl1 = $_POST['tgl1'];
$tgl2 = $_POST['tgl2'];

$currentPage="pp_distr_del_royalty.php";
$komen="";
if(isset($_POST['cari'])){
	if($no_pp=="" and $branch_plant=="" and $ship_to == "" and $produk == "" and $jenis_kirim == ""  and $tgl1 == "" and $tgl2 == "" and $sold_to == "" and $jenis_order == ""){
		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE DELETE_MARK = '0' AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND no_shp_old is null and FLAG_LELANG is null AND STATUS_LINE IN ('OPEN','PROCESS') AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0)
 AND SO_TYPE <> 'ZRE' AND KODE_PRODUK like '121-301-7%' ORDER BY NO_PP ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE STATUS_LINE IN ('OPEN','PROCESS') AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND KODE_PRODUK like '121-301-7%' AND ";
		if($no_pp!=""){
		$sql.=" NO_PP LIKE '%$no_pp%' ";
		$pakeor=1;
		}
		if($branch_plant!=""){
			if($pakeor==1){
			$sql.=" AND BPLANT LIKE '$branch_plant' ";
			}else{
			$sql.=" BPLANT LIKE '$branch_plant' ";
			$pakeor=1;
			}
		}
		if($ship_to!=""){
			if($pakeor==1){
			$sql.=" AND SHIP_TO LIKE '%$ship_to%' ";
			}else{
			$sql.=" SHIP_TO LIKE '%$ship_to%' ";
			$pakeor=1;
			}
		}
		if($sold_to!=""){
			if($pakeor==1){
			$sql.=" AND SOLD_TO LIKE '%$sold_to%' ";
			}else{
			$sql.=" SOLD_TO LIKE '%$sold_to%' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KODE_PRODUK LIKE '%$produk%' ";
			}else{
			$sql.=" KODE_PRODUK LIKE '%$produk%' ";
			$pakeor=1;
			}
		}
		if($jenis_kirim!=""){
			if($pakeor==1){
			$sql.=" AND INCOTERM LIKE '$jenis_kirim' ";
			}else{
			$sql.=" INCOTERM LIKE '$jenis_kirim' ";
			$pakeor=1;
			}
		}
		if($jenis_order!=""){
			if($pakeor==1){
				if ($jenis_order == 'standart') {
					$sql.=" AND SO_TYPE<>'ZPR' AND SO_TYPE <> 'ZRE' ";
				}else{
					$sql.=" AND SO_TYPE = 'ZPR' ";
				}
			}else{
				if ($jenis_order == 'standart') {
					$sql.=" SO_TYPE<>'ZPR' AND SO_TYPE <> 'ZRE' ";
				}else{
					$sql.=" SO_TYPE = 'ZPR' ";
				}
			$pakeor=1;
			}
		}
		if($tgl1!="" or $tgl2!=""){

			if ($tgl1=="")$tgl1_sql = "01-01-1990";
			else $tgl1_sql = $tgl1;
			if ($tgl2=="")$tgl2_sql = "12-12-9999";
			else $tgl2_sql = $tgl2;
			if($pakeor==1){
			$sql.=" AND TGL_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			}else{
			$sql.=" TGL_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			$pakeor=1;
			}
		}
		$sql.=" AND no_shp_old is null and FLAG_LELANG is null AND DELETE_MARK = '0' AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0)
 ORDER BY NO_PP ASC";
	}
	//echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_pp_v[]=$row[NO_PP];
		$tgl_kirim_v[]=$row[TGL_KIRIM_PP];
		$tgl_pp_v[]=$row[TGL_PP];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$sold_to_v[]=$row[SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$nama_ship_to_v[]=$row[NAMA_SHIP_TO];
		$alamat_v[]=$row[ALAMAT_SHIP_TO];
		$qty_v[]=$row[QTY_PP];
		$id_v[]=$row[ID];  
		$status_v[]=$row[STATUS];  
	}
	$total=count($no_pp_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Permintaan Pembelian :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Edit  Permintaan Pembelian </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Pencarian Permintaan Pembelian </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>"  >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Permintaan Pembelian </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" value="<?=$no_pp?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Branch Plant </td>
      <td  class="puso">:</td>
      <td ><select name="branch_plant" id="Branch Plant" >
        <option value="">---Pilih---</option>
        <? $fungsi->or_jns_plant($branch_plant); ?>
      </select>
      </td>
    </tr>
    <tr>
      <td  class="puso">Sold To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="sold_to" name="sold_to"  value="<?=$sold_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="ship_to" name="ship_to"  value="<?=$ship_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Produk </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="produk" name="produk"  value="<?=$produk?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Jenis Pengiriman </td>
      <td  class="puso">:</td>
      <td ><select name="jenis_kirim" id="Jenis Pengiriman" >
          <option value="">---Pilih---</option>
          <? $fungsi->or_jenis_kirim($jenis_kirim); ?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Jenis Order </td>
      <td  class="puso">:</td>
      <td ><select name="jenis_order" id="jenis_order" required>
          <option value="">---Pilih---</option>
          <option <? echo $jenis_order == 'standart' ? 'selected' : '' ?> value="standart">Standart</option>
          <option <? echo $jenis_order == 'proyek' ? 'selected' : '' ?> value="proyek">Proyek</option>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal PP</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl1?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl2?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="14"><span class="style5">&nbsp;Tabel Data Approval Permintaan Pembelian </span></th>
	</tr>
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >Tanggal  PP</strong></td>
		<td align="center"><strong>Ship To  </strong></td>
		 <td align="center"><strong>Nama Ship To   </strong></td>
		 <td align="center"><strong>Alamat Ship To   </strong></td>
		 <td align="center"><strong>Kode Produk</strong></td>
		 <td align="center"><strong>Produk</strong></td>
		 <td align="center"><strong>Qty</strong></td>
		 <td align="center"><strong>Tgl Kirim</strong></td>
		 <td align="center"><strong>Status</strong></td>
		 <td align="center"><strong>Update</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	

		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp_v[$i]; ?></td>
		<td align="center"><? echo $tgl_pp_v[$i]; ?></td>
		<td align="center"><? echo $ship_to_v[$i]; ?></td>
		<td align="left"><? echo $nama_ship_to_v[$i]; ?></td>
		<td align="left"><? echo $alamat_v[$i]; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="left"><? echo $nama_produk_v[$i]; ?></td>
		<td align="right"><? echo number_format($qty_v[$i],0,",","."); ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $status_v[$i]; ?></td>
		<td align="center"><a href="pp_detil_edit_royalty.php?id_detail=<?=$id_v[$i]?>" ><img src="../images/edit.png" border="0" /></a></td>
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="14" align="center">
		<a href="pp_distr_del_royalty.php" target="isi" class="button">Back</a>		 </td>
		
		</td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
