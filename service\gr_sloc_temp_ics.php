<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'GET':

    $strSo = "SELECT
                OR_TRANS_DTL.NO_SO,
                OR_TRANS_DTL.PLANT
              FROM
                OR_TRANS_HDR
              LEFT JOIN OR_TRANS_DTL ON
                OR_TRANS_HDR.NO_PP = OR_TRANS_DTL.NO_PP
              WHERE
                OR_TRANS_HDR.NOTE = 'ics_pp'
                AND OR_TRANS_HDR.ORG IN ('PTSC', 'ID50', '1000')
                AND OR_TRANS_DTL.STATUS_LINE = 'APPROVE'
                -- AND (OR_TRANS_HDR.CREATE_DATE BETWEEN TO_DATE('".date('Y-m-d',strtotime('-5 days'))."', 'YYYY-MM-DD') AND TO_DATE('".date('Y-m-d')."', 'YYYY-MM-DD'))
                AND (TO_CHAR(OR_TRANS_HDR.CREATE_DATE, 'YYYY-MM-DD') BETWEEN '".date('Y-m-d',strtotime('-5 days'))."' AND '".date('Y-m-d')."')";
  
    $querySo = @oci_parse($fautoris->koneksi(), $strSo);
    @oci_execute($querySo);
    $listSo = array();
    $i=0;
    while($row=oci_fetch_array($querySo)){
        $listSo[$i] = $row;
        $i++;
    }
    // $rowSo = oci_fetch_array($querySo, OCI_ASSOC);    
    // $listSo['NO_SO'] = $rowSo["NO_SO"];
    // $listSo['PLANT'] = $rowSo["PLANT"];
    // echo "<pre>";
    // print_r($strSo);
    // echo "</pre>";

    // echo "<br> =====> Data SO <pre>";
    // print_r($listSo);
    // echo "</pre>";
    $lookUpGi = array();

    foreach ($listSo as $k => $v) {
      $url = 'https://integrasi-api.sig.id/apimd/lookupgoodsissuevercc/dev';//$results['URL'];
      $data = array(
          "Token" =>"aSsMx7GV0HFGzlufM4DH",// $results['TOKEN'],
          "Plant" =>$v['PLANT'],
          "DistributionChannel" => null,
          "SONumber" => $v['NO_SO'],
          "VehicleNumber" => null,
          "StartGoodsIssueDate" => date('Y-m-d',strtotime('-5 days')),
          "EndGoodsIssueDate" => date('Y-m-d'),
          'SystemID' => 'QASSO'
      );

      // echo "<br> =====> Data Request (".$k.") <pre>";
      // print_r($data);
      // echo "</pre>";
  
      $options = array(
          'http' => array(
              'header' => "Content-type: application/json\r\n",
              'method' => 'POST',
              'content' => json_encode($data),
          )
      );
  
      $context = stream_context_create($options);
      $result = file_get_contents($url, false, $context); 
      
      $response = json_decode($result);   
  
      if (isset($response->Data) && count($response->Data) != 0) {
        $responseLGi = json_decode(json_encode($response->Data), true);
        foreach ($responseLGi as $kk => $vv) {
          array_push($lookUpGi, $vv);
        }
      }
      // $lookUpGi = $response->Data;
    }

    // echo "<br> =====> Data Lookup GI <pre>";
    // print_r($lookUpGi);
    // echo "</pre>";
    // exit;

    if (count($lookUpGi) > 0) {
      
      $countGi = 0;
      foreach ($lookUpGi as $k => $v) {
        
        $strmap = "SELECT * FROM MAPPING_PO_ICS WHERE COMPANY_CODE = 'PTSC' AND SHIPPING_POINT = 'I300' AND  FLAG_DEL='X'";
    
        $querymap = @oci_parse($fautoris->koneksi(), $strmap);
        @oci_execute($querymap);
        $rowmap = oci_fetch_array($querymap, OCI_ASSOC);    
        $plant_tujuan = $rowmap["SOLD_TO_PARTY"];
    
        $firstDigit = substr((string)$plant_tujuan, 0, 1);
        $bukrs = $firstDigit . '000';

        // echo " <br> =====> GI DATE (".$k.") <pre>";
        // print_r($v["GoodsIssueDateStr"]);
        // echo "</pre>";
        
        // echo " <br> =====> GI DATE FORMATED (".$k.") <pre>";
        // print_r(date('Y', strtotime($v["GoodsIssueDateStr"])));
        // echo "</pre>";

        // echo " <br> =====> Data Map Lookup GI (".$k.") <pre>";
        // print_r($v);
        // echo "</pre>";

        $param["GI_NUMBER"] = '4900000181';
        $param["GI_YEAR"] = date('Y', strtotime($v["GoodsIssueDateStr"]));
        $param["ORG"] = 'PTSC';
        $param["PLANT"] = $plant_tujuan;
        $param["PLANT_SOURCE"] = $v["Plant"];
        $param["MATERIAL"] = '140000001922';
        $param["MATERIAL_DESC"] = 'DYNAMIX SERBA GUNA 50 KG BAG PLASTIC BAG';
        $param["BUDAT"] = date('Ymd');
        $param["BLDATE"] = date('Ymd');
        $param["DO_NUMBER"] = $v["DONo"];
        $param["SO_NUMBER"] = $v["SONo"];
        $param["DO_QTY"] = $v["DOQty"];
        $param["MEINS"] = 'TO';
        $param["NOPOL"] = $v["VehicleNumber"];
        $param["NAMA_KAPAL"] = '';
        $param["BUKRS"] = $bukrs;
        $param["PO_NUMBER"] = '5300005506';

        // echo "<pre>";
        // print_r($param);
        // echo "</pre>";
        
        $get = new gr_sloc_temp_ics();
        $result = $get->sv_data($param);
    
        if ($result["TYPE"]) {
          $message = $result["MESSAGE"] ? $result["MESSAGE"] : "Success GR Process";
          $responseRequest = array(
            'ResponseCode' => 200,
            'responseMessage' => $message
          );
          $countGi++;
        }else{
          $responseRequest = array(
            'ResponseCode' => 500,
            'responseMessage' => "Failed GR Process"
          );
        }
          
        $byLog = 'gr_sloc_temp_ics';
        $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, 'Scheduller');
      }
  
      $message = "Success Process ".$countGi." GI";
    } else {
      $message = "Data GI Not Found";
    }
    

    $responseRequest = array(
      'responseCode' => 200,
      'responseMessage' => $message
    );
    header('Content-Type: application/json');
    echo json_encode($responseRequest);
    
    break;
}

class gr_sloc_temp_ics
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function sv_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCMM_ICS_GRPROCESS");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {
    $fce->I_INPUT["MBLNR_GI"] = $param["GI_NUMBER"];
    $fce->I_INPUT["MJAHR_GI"] = $param["GI_YEAR"];
    $fce->I_INPUT["VKORG"] = $param["ORG"];
    $fce->I_INPUT["KUNNR"] = $param["PLANT"];
    $fce->I_INPUT["VSTEL"] = $param["PLANT_SOURCE"];
    $fce->I_INPUT["MATNR"] = $param["MATERIAL"];
    $fce->I_INPUT["ARKTX"] = $param["MATERIAL_DESC"];
    $fce->I_INPUT["BUDAT"] = $param["BUDAT"];
    $fce->I_INPUT["BLDAT"] = $param["BLDATE"];
    $fce->I_INPUT["VBELN"] = $param["DO_NUMBER"];
    $fce->I_INPUT["POSNR"] = "10";
    $fce->I_INPUT["VGBEL"] = $param["SO_NUMBER"];
    $fce->I_INPUT["VGPOS"] = "10";
    $fce->I_INPUT["LFIMG"] = $param["DO_QTY"];
    $fce->I_INPUT["MEINS"] = $param["MEINS"];
    $fce->I_INPUT["NOPOL"] = $param["NOPOL"];
    $fce->I_INPUT["BNAME"] = $param["NAMA_KAPAL"];
    $fce->I_INPUT["BUKRS"] = $param["BUKRS"];
    $fce->I_INPUT["EBELN"] = $param["PO_NUMBER"];
    $fce->I_INPUT["EBELP"] = "10";
    
    $fce->I_LGORT = "IC01";
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->E_RETURN->Reset();
      $return = $fce->E_MESSAGE;
    }
    return $return;
  }

  function dataLogSdd($data) {
        $sqlInsert = "INSERT INTO LOG_ICS_SBI 
                        (GI_NUMBER,
                        GI_YEAR,
                        ORG,
                        PLANT,
                        PLANT_SOURCE,
                        MATERIAL,
                        MATERIAL_DESC,
                        BUDAT,
                        BLDATE,
                        DO_NUMBER,
                        SO_NUMBER,
                        DO_QTY,
                        MEINS,
                        NOPOL,
                        NAMA_KAPAL,
                        BUKRS,
                        PO_NUMBER,
                        LOG_DATE) 
                      VALUES (:GI_NUMBER,
                              :GI_YEAR,
                              :ORG,
                              :PLANT,
                              :PLANT_SOURCE,
                              :MATERIAL,
                              :MATERIAL_DESC,
                              :BUDAT,
                              :BLDATE,
                              :DO_NUMBER,
                              :SO_NUMBER,
                              :DO_QTY,
                              :MEINS,
                              :NOPOL,
                              :NAMA_KAPAL,
                              :BUKRS,
                              :PO_NUMBER,
                              SYSDATE)";
        
        $stmtInsert = oci_parse($fautoris->koneksi(), $sqlInsert);
        oci_bind_by_name($stmtInsert, ':NO_PP', $nopp);
        oci_bind_by_name($stmtInsert, ':SOLD_TO', $soldto);
        oci_bind_by_name($stmtInsert, ':NAMA_SOLD_TO', $nm_soldto);
        oci_bind_by_name($stmtInsert, ':SHIPTO', $shipto);
        oci_bind_by_name($stmtInsert, ':NAMA_SHIPTO', $nm_shipto);
        oci_bind_by_name($stmtInsert, ':R_KOSONG', $rkosong);
        oci_bind_by_name($stmtInsert, ':QTY_PP', $qtypp);
        oci_bind_by_name($stmtInsert, ':CATATAN', $catatan);
        oci_bind_by_name($stmtInsert, ':TGL_SCHEDULE', $tgl_schedule);
        oci_execute($stmtInsert);
    }
}
