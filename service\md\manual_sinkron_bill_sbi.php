<?php
session_start();
require_once("saprfc.php");
include("koneksi.php");
require_once("SAPDataModule_Connection.php");

$page = "manual_sinkron_bill_sbi.php";
$currentPage = "manual_sinkron_bill_sbi.php";
$dtBill = array();
require_once("../autorisasi.php");
$fautoris = new autorisasi();
if (isset($_POST['datasinkron'])) {

  //     try {

  //     // uncomment if in server/prod
  //     // ================================ get list do rfc =================================
  //     $sap_con = new SAPDataModule_Connection();
  //     $sap = $sap_con->getConnSAP(); //getConnSAP_Dev(); //
  //     #/SAP Connection #

  //     if (!$sap_con) {echo "Koneksi oracle gagal";exit;}
  //     $fce = &$sap->NewFunction("ZCSD_GET_DO_AUTOGR");
  //     if ($fce == false) {$sap->PrintStatus();exit;}

  //     $date1 = str_replace("-","", $_POST['startdate']);
  //     $date2 = str_replace("-","", $_POST['enddate']);

  //     // $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => $date);
  //     $tmp = array("SIGN" => 'I', "OPTION" => 'BT', "LOW" => "$date1", "HIGH" => "$date2");
  //     $fce->LR_CREATE_DATE->Append($tmp);

  //     $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "");
  //     $fce->LR_BILLING->Append($tmp);

  //     $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "PTSC");
  //     $fce->LR_VKORG->Append($tmp);
  //     $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "ID50");
  //     $fce->LR_VKORG->Append($tmp);

  //     $fce->Call();
  // //    echo "<pre>";
  // //    print_r($fce);
  // //    exit();
  //     $data = array();
  //     if ($fce->GetStatus() == SAPRFC_OK) {

  //         $fce->T_DATA_AUTOGR->Reset();
  //         $i = 0;
  //         $insert_sukses = 0;
  //         $insert_gagal = 0;

  //         $nomorarray = 1;

  //         while ($fce->T_DATA_AUTOGR->Next()) {

  //             $expo = $fce->T_DATA_AUTOGR->row;

  //             if ($expo['NO_DO']) {
  //                 $url = 'https://sip.solusibangunindonesia.com/APIMD/LookupBillingStatus';
  //                 $data = array(
  //                    "Token"=> "aSsMx7GV0HFGzlufM4DH", 
  //                    "SystemID"=> "QASSO", 
  //                    "Sign"=> "I", 
  //                    "High"=> "{$expo['NO_DO']}", 
  //                    "Low"=> "{$expo['NO_DO']}", 
  //                    "Option"=> "EQ"
  //                 );

  //                 $options = array(
  //                     'http' => array(
  //                     'header' => "Content-type: application/json\r\n",
  //                     'method' => 'POST',
  //                     'content' => json_encode($data),
  //                     )
  //                 );

  //                 $context = stream_context_create($options);
  //                 $result = file_get_contents($url, false, $context);
  //                 $response = json_decode($result, true);

  //                 if(empty($response['Data'])){
  //                     $dataarray5[$nomorarray] = array(
  //                     "NMORG" => $expo['NMORG'],
  //                     "NMPLAN" => $expo['NMPLAN'],
  //                     "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
  //                     "NO_DO" => $expo['NO_DO'],
  //                     "NO_SO" => $expo['NO_SO'],
  //                     "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
  //                     "NO_DO_REFF" => $expo['NO_DO_REFF'],
  //                     "CREATE_DATE" => $expo['CREATE_DATE'],
  //                     "NO_BILLING" => "Belum Ada Billing",
  //                     "FKIMG" => "Belum Ada Billing",
  //                     "NETPR" => "Belum Ada Billing",
  //                     );
  //                 }
  //                 }


  //                 $nomorarray++;

  //                 $param = $response->Data[0];
  //                 if ($param->NO_DO) {
  //                     //print_r($param);
  //                     $hasil = array(array("NO_DO"=>$param->NO_DO, "BILLING"=>$param->NO_BILLING, "FKIMG"=>$param->FKIMG, "NETPR"=>$param->NETPR));

  //                     // =================================== pass to rfc SI =================================
  //                     $saprfc_id = "ZCSD_UPDATE_BILL_AUTO_GR";
  //                     //echo "<h1>".$saprfc_id."</h1>";
  //                     //echo "started @ ".date(DATE_RFC822);
  //                     // Create saprfc-instance
  //                     $sap2 = new saprfc(array(
  //                             "logindata"=>array(
  //                                 "ASHOST"=>$saplogin_ashost		//host
  //                                 ,"SYSNR"=>$saplogin_sysnr		// system number
  //                                 ,"CLIENT"=>$saplogin_client		// client
  //                                 ,"USER"=>$saplogin_user			// user
  //                                 ,"PASSWD"=>$saplogin_passwd		// password
  //                                 )
  //                             ,"show_errors"=>false				// let class printout errors
  //                             ,"debug"=>false));					// detailed debugging information

  //                     $parse_array = array();
  //                     $parse_array[] = array("TABLE","T_UPD",$hasil);
  //                     $result=$sap2->callFunction($saprfc_id,$parse_array);

  //                     // Call successfull?
  //                     if ($sap2->getStatus() == SAPRFC_OK) {
  //                         //echo $sap2->getStatusText()."</p>";
  //                     }else { 
  //                        // echo $sap2->getStatusText()."</p>";
  //                     }
  //                     //print_r($sap2);

  //                     // Logoff/Close saprfc-connection 
  //                     $sap2->logoff();
  //                     //echo "<p>finished @ ".date(DATE_RFC822)."</p>";
  //                     //echo "<p>Jumlah Record : $no</p>";
  //                     // =================================== pass to rfc SI =================================
  //                 }
  //             }
  //             //echo "$i, ";

  //             $i++;
  //         //echo "<p>finished @ZCSD_GET_DO_AUTOGR</p>";
  //         //echo "<p>Jumlah Record : $i</p>";
  //     } else {
  //         $fce->PrintStatus();
  //     }
  //     // echo json_encode($data);
  //     // print_r($data);

  //     $fce->Close();
  //     $sap->Close();
  //     // ================================ get list do rfc =================================

  //  } catch (Throwable $th) {
  //      //throw $th;
  //      //print_r($th);
  //  }
  // try {
  //     $hasil = array(array("NO_DO"=>"5207535404", "BILLING"=>"5207535401", "FKIMG"=>"1000", "NETPR"=>"100000"),array("NO_DO"=>"0074016827", "BILLING"=>"0074016821", "FKIMG"=>"1000", "NETPR"=>"100000"));

  //     // =================================== pass to rfc SI =================================
  //     $saprfc_id = "ZCSD_UPDATE_BILL_AUTO_GR";
  //     //echo "<h1>".$saprfc_id."</h1>";
  //     //echo "started @ ".date(DATE_RFC822);
  //     // Create saprfc-instance
  //     $sap2 = new saprfc(array(
  //             "logindata"=>array(
  //                 "ASHOST"=>$saplogin_ashost		//host
  //                 ,"SYSNR"=>$saplogin_sysnr		// system number
  //                 ,"CLIENT"=>$saplogin_client		// client
  //                 ,"USER"=>$saplogin_user			// user
  //                 ,"PASSWD"=>$saplogin_passwd		// password
  //                 )
  //             ,"show_errors"=>false				// let class printout errors
  //             ,"debug"=>false));					// detailed debugging information

  //     $parse_array = array();
  //     $parse_array[] = array("TABLE","T_UPD",$hasil);
  //     $result=$sap2->callFunction($saprfc_id,$parse_array);

  //     // Call successfull?
  //     if ($sap2->getStatus() == SAPRFC_OK) {
  //         //echo $sap2->getStatusText()."</p>";
  //     }else { 
  //        // echo $sap2->getStatusText()."</p>";
  //     }
  //     //print_r($sap2);

  //     // Logoff/Close saprfc-connection 
  //     $sap2->logoff();
  //     //echo "<p>finished @ ".date(DATE_RFC822)."</p>";
  //     //echo "<p>Jumlah Record : $no</p>";
  // } catch (Throwable $th) {
  //     //throw $th;
  // }

}

if (isset($_POST['cari'])) {
  $date1 = str_replace("-", "", $_POST['startdate']);
  $date2 = str_replace("-", "", $_POST['enddate']);
  $timestamp1 = strtotime($date1);
  $timestamp2 = strtotime($date2);
  $selisihHari = ($timestamp2 - $timestamp1) / (60 * 60 * 24);
  if ($selisihHari > 3) {
    header("Location: " . $_SERVER['REQUEST_URI']);
    return;
  } else {
    // $
    try {
      $dtBill = array();

      // uncomment if in server/prod
      // ================================ get list do rfc =================================
      $sap_con = new SAPDataModule_Connection();
      $sap = $sap_con->getConnSAP(); //getConnSAP_Dev(); //
      #/SAP Connection #

      if (!$sap_con) {
        echo "Koneksi oracle gagal";
        exit;
      }
      $fce = &$sap->NewFunction("ZCSD_GET_DO_AUTOGR");
      if ($fce == false) {
        $sap->PrintStatus();
        exit;
      }

      $date1 = str_replace("-", "", $_POST['startdate']);
      $date2 = str_replace("-", "", $_POST['enddate']);
      // $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => $date);
      $tmp = array("SIGN" => 'I', "OPTION" => 'BT', "LOW" => "$date1", "HIGH" => "$date2");
      $fce->LR_CREATE_DATE->Append($tmp);

      $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "");
      $fce->LR_BILLING->Append($tmp);

      $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "PTSC");
      $fce->LR_VKORG->Append($tmp);
      $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "ID50");
      $fce->LR_VKORG->Append($tmp);

      $fce->Call();
      $data = array();
      if ($fce->GetStatus() == SAPRFC_OK) {

        $fce->T_DATA_AUTOGR->Reset();
        $i = 0;
        $insert_sukses = 0;
        $insert_gagal = 0;
        $nomordopertama = "5207535404";

        $nomorarray = 1;
       
       $json_string = json_encode($fce->T_DATA_AUTOGR); // Konversi ke string JSON
$json_data = substr($json_string, 0, 2000); // Ambil hanya 2000 karakter pertama
$data_user= $_SESSION['id_user'];
 $DATEE = date('Y-m-d');
          $conn = $fautoris->koneksi();
           $sql = "INSERT INTO ZMD_LOG_SBI (SEND_PARAM, RETURN_PARAM, USER_SAVE, NO_PP, PESAN, PESAN_DETAIL, TGL) VALUES ('$json_data', '$date1', '$date2', '$data_user','manual_sinkron_bill_sbimd', 'manual_sinkron_bill_sbimd', TO_DATE('$DATEE', 'YYYY-MM-DD'))";
            $query = @oci_parse($conn, $sql);
            @oci_execute($query);

        while ($fce->T_DATA_AUTOGR->Next()) {

          $expo = $fce->T_DATA_AUTOGR->row;

          //            echo '<pre>';
          //            print_r($expo);
         

          if ($expo['NO_DO']) {
            //$url = 'https://sip.solusibangunindonesia.com/APIMD/LookupBillingStatus';
			$url = 'https://integrasi-api.sig.id/apimd/lookupbillingstatus/dev';
            $data = array(
              "Token" => "aSsMx7GV0HFGzlufM4DH",
              "SystemID" => "QASSO",
              "Sign" => "I",
              "High" => "{$expo['NO_DO']}",
              "Low" => "{$expo['NO_DO']}",
              "Option" => "EQ"
            );

            $options = array(
              'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data),
              )
            );

            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            $response = json_decode($result, true);

            // if(empty($response['Data'])){
            //     $dataarray5[$nomorarray] = array(
            //     "NMORG" => $expo['NMORG'],
            //     "NMPLAN" => $expo['NMPLAN'],
            //     "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
            //     "NO_DO" => $expo['NO_DO'],
            //     "NO_SO" => $expo['NO_SO'],
            //     "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
            //     "NO_DO_REFF" => $expo['NO_DO_REFF'],
            //     "CREATE_DATE" => $expo['CREATE_DATE'],
            //     "NO_BILLING" => "Belum Ada Billing",
            //     "FKIMG" => "Belum Ada Billing",
            //     "NETPR" => "Belum Ada Billing",
            //     );
            // }else{
            // foreach ($response['Data'] as $key =>$value){  

            //     $dataarray5[$nomorarray] = array(
            //     "NMORG" => $expo['NMORG'],
            //     "NMPLAN" => $expo['NMPLAN'],
            //     "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
            //     "NO_DO" => $expo['NO_DO'],
            //     "NO_SO" => $expo['NO_SO'],
            //     "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
            //     "NO_DO_REFF" => $expo['NO_DO_REFF'],
            //     "CREATE_DATE" => $expo['CREATE_DATE'],
            //     "NO_BILLING" => $value['NO_BILLING'],
            //     "FKIMG" => $value['FKIMG'],
            //     "NETPR" => $value['NETPR'],
            //     );
            //     $tmpdt = array(
            //         "NO_DO" => $value['NO_DO'],
            //         "NO_BILLING" => $value['NO_BILLING'],
            //         "PPN" => $value['PPN'],
            //         "PPH" => $value['PPH'],
            //         "FKIMG" => $value['FKIMG'],
            //         "NETPR" => $value['NETPR'],
            //     );
            //     $dtBill[$nomorarray] = $tmpdt;
            // }
            // }



            //print_r($expo);
            //echo "<br/>";
            //                echo "<pre>";
            //                print_r($response);
            //print_r($response->Data[0]);

            // $param = $response->Data[0];
             echo '<pre>';
                print_r($response);
                echo '</pre>';
             //   exit();

            if (!empty($response['Data'])) {
              //print_r($param);

              foreach ($response['Data'] as $key => $value) {
                // print_r($value);
                $hasil = array(array("NO_DO" => $value['NO_DO'], "BILLING" => $value['NO_BILLING'], "PPN" => $value['PPN'], "PPH" => $value->PPH, "FKIMG" => $value['FKIMG'], "NETPR" => $value['NETPR'], "COUNT_BILL_SBI" => $value['COUNT_ITEM_BILL'], "FKDAT" => str_replace('-', '', $value['FKDAT']), "VRKME" => $value['VRKME']));

                // =================================== pass to rfc SI =================================
                $saprfc_id = "ZCSD_UPDATE_BILL_AUTO_GR";
                //echo "<h1>".$saprfc_id."</h1>";
                //echo "started @ ".date(DATE_RFC822);
                // Create saprfc-instance
                //                        $sap2 = new saprfc(array(
                //                                "logindata"=>array(
                //                                    "ASHOST"=>$saplogin_ashost		//host
                //                                    ,"SYSNR"=>$saplogin_sysnr		// system number
                //                                    ,"CLIENT"=>$saplogin_client		// client
                //                                    ,'R3NAME'=>$sapr3name
                //                                    ,'GROUP'=>$sapgroup
                //                                    ,"USER"=>$saplogin_user			// user
                //                                    ,"PASSWD"=>$saplogin_passwd		// password
                //                                    ,"CODEPAGE"=>$sapcode
                //                                    )
                //                                ,"show_errors"=>false				// let class printout errors
                //                                ,"debug"=>false));					// detailed debugging information

                //                        $parse_array = array();
                //                        $parse_array[] = array("TABLE","T_UPD",$hasil);
                //                        $result=$sap2->callFunction($saprfc_id,$parse_array);
                // Call successfull?
                // $status = "Gagal";
                // if ($sap2->getStatus() == SAPRFC_OK) {
                //     $status = "Sukses";
                //     //echo $sap2->getStatusText()."</p>";
                // }else { 
                //    // echo $sap2->getStatusText()."</p>";
                // }

                // // Logoff/Close saprfc-connection 
                // $sap2->logoff();

                $sap_con2 = new SAPDataModule_Connection();
                $sap2 = $sap_con2->getConnSAP(); //getConnSAP_Dev(); //
                if (!$sap_con) {
                  echo "Koneksi oracle gagal";
                  exit;
                }
                $fce2 = &$sap2->NewFunction("ZCSD_UPDATE_BILL_AUTO_GR");
                if ($fce2 == false) {
                  $fce2->PrintStatus();
                  exit;
                }

                $tmp = array($hasil);
                $tmp = $tmp[0];
                //                        echo '<pre>';
                //                        print_r($tmp);
                //                        echo '<pre>';
                //                        foreach ($tmp as $key => $value) {


                // echo '<pre>';
                // print_r($tmp);
                $fce2->T_UPD->row["NO_DO"] = $tmp[0]["NO_DO"];
                $fce2->T_UPD->row["BILLING"] = $tmp[0]["BILLING"];
                $fce2->T_UPD->row["PPN"] = $tmp[0]["PPN"];
                $fce2->T_UPD->row["PPH"] = $tmp[0]["PPH"];
                $fce2->T_UPD->row["FKIMG"] = $tmp[0]["FKIMG"];
                $fce2->T_UPD->row["NETPR"] = $tmp[0]["NETPR"];
                $fce2->T_UPD->row["FKDAT"] = $tmp[0]["FKDAT"];
                $fce2->T_UPD->row["COUNT_BILL_SBI"] = $tmp[0]["COUNT_BILL_SBI"];
                $fce2->T_UPD->row["VRKME"] = $tmp[0]["VRKME"];
                $fce2->T_UPD->Append($fce2->T_UPD->row);


                $fce2->Call();
                // print_r($fce2->T_UPD->row);
                // print_r($hasil);


                // Call successfull?
                $status = "Gagal";
                if ($fce2->getStatus() == SAPRFC_OK) {
                  $status = "Sukses";
                  //echo $sap2->getStatusText()."</p>";
                } else {
                  // echo $sap2->getStatusText()."</p>";
                }
                //print_r($sap2);

                // Logoff/Close saprfc-connection 
                //                        $fce2->logoff();
                $fce2->Close();
                $sap2->Close();

                $dataarray5[$nomorarray] = array(
                  "NMORG" => $expo['NMORG'],
                  "NMPLAN" => $expo['NMPLAN'],
                  "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
                  "NO_DO" => $expo['NO_DO'],
                  "NO_SO" => $expo['NO_SO'],
                  "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
                  "NO_DO_REFF" => $expo['NO_DO_REFF'],
                  "CREATE_DATE" => $expo['CREATE_DATE'],
                  "NO_BILLING" => $value['NO_BILLING'],
                  "FKIMG" => $value['FKIMG'],
                  "NETPR" => $value['NETPR'],
                  "SYNC" => $status,
                );
                //echo "<p>finished @ ".date(DATE_RFC822)."</p>";
                //echo "<p>Jumlah Record : $no</p>";
                // =================================== pass to rfc SI =================================
              }
            } else {

              $dataarray5[$nomorarray] = array(
                "NMORG" => $expo['NMORG'],
                "NMPLAN" => $expo['NMPLAN'],
                "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
                "NO_DO" => $expo['NO_DO'],
                "NO_SO" => $expo['NO_SO'],
                "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
                "NO_DO_REFF" => $expo['NO_DO_REFF'],
                "CREATE_DATE" => $expo['CREATE_DATE'],
                "NO_BILLING" => "Belum Ada Billing",
                "FKIMG" => "Belum Ada Billing",
                "NETPR" => "Belum Ada Billing",
                "SYNC" => "Gagal",
              );
            }
            $nomorarray++;
            // echo '<pre>';
            // print_r($sap2);
            // echo '</pre>';
          }

          // echo "$i, ";

          $i++;
          // if ($i==10) exit;
        }

        //echo "<p>finished @ZCSD_GET_DO_AUTOGR</p>";
        //echo "<p>Jumlah Record : $i</p>";
      } else {
        $fce->PrintStatus();
      }
      // echo "<pre>";
      // print_r($dtBill);
      // echo json_encode($data);
      // print_r($data);

      $fce->Close();
      $sap->Close();
      // ================================ get list do rfc =================================

    } catch (Throwable $th) {
      //throw $th;
      //print_r($th);
    }


    $data2 = 0;
  }
}


?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
  <title>Aplikasi SGG Online: Lihat Data Permintaan Pembelian :)</title>
  <script language="JavaScript" type="text/javascript" src="calendar/arsip.javascript.js"></script>
  <script language="JavaScript" src="calendar/JSCookMenu_mini.js" type="text/javascript"></script>
  <!-- import the calendar script -->
  <script type="text/javascript" src="calendar/calendar_mini.js"></script>
  <!-- import the language module -->
  <script type="text/javascript" src="calendar/lang/calendar-en.js"></script>
  <link href="calendar/calendar-mos.css" rel="stylesheet" type="text/css">
  <link href="Template_css/template_css.css" rel="stylesheet" type="text/css" />
  <link href="Template_css/admin_login.css" rel="stylesheet" type="text/css" />
  <link href="Template_css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
  <div align="center">
    <table width="600" align="center" class="adminheading" border="0">
      <tr>
        <th class="kb2">Manual Sinkron Billing SBI/SBA</th>
      </tr>
    </table>
  </div>
  <?
  if ($data < 1) {

  ?>

    <div align="center">
      <table width="600" align="center" class="adminlist">
        <tr>
          <th align="left" colspan="4"> &nbsp;Form Manual Sinkron Billing SBI/SBA </th>
        </tr>
      </table>
    </div>

    <form id="form1" name="form1" method="post" action="<? echo $page; ?>">
      <table width="600" align="center" class="adminform">
        <tr width="174">
          <td class="puso">&nbsp;</td>
          <td class="puso">&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr width="174">
          <td class="puso">Start Date </td>
          <td class="puso">:</td>
          <td><input type="date" id="startdate" name="startdate" value="<?php echo date("dmY"); ?>" />
            <input name="cari" type="submit" class="button" id="cari" value="Proses" />
        </tr>
        <tr>
          <td class="puso">End Date </td>
          <td class="puso">:</td>
          <td><input type="date" id="enddate" name="enddate" value="<?php echo date("dmY"); ?>" onchange="cek()" />
        </tr>
        <tr>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td rowspan="2">
        </tr>
        <tr>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td class="ThemeOfficeMenu">&nbsp;</td>
        </tr>
      </table>
    </form>

  <? } ?>

  <br />
  <br />
  <?
  if ($data > 0) {
    $startdate = str_replace("-", "", $_POST['startdate']);
    $enddate = str_replace("-", "", $_POST['enddate']);
    $tahun1 = substr($startdate, -8, -4);
    $bulan1 = substr($startdate, 4, 2);
    $tanggal1 = substr($startdate, -2);
    $tahun2 = substr($enddate, -8, -4);
    $bulan2 = substr($enddate, 4, 2);
    $tanggal2 = substr($enddate, -2);

  ?>
    <div align="center">
      <table width="95%" align="center">
        <tr>
          <th align="right" colspan="4"><span>
            </span></th>
        </tr>
      </table>
    </div>
    <div align="center">
      <table width="95%" align="center" class="adminlist">
        <tr>
          <th align="left" colspan="4"><span class="style5">&nbsp; Tabel Manual Sinkron Billing SBI/SBA Periode: &nbsp;<?php echo $tanggal1 . "/" . $bulan1 . "/" . $tahun1 . "-" . $tanggal2 . "/" . $bulan2 . "/" . $tahun2; ?> </span></th>
        </tr>
      </table>
    </div>
    <div align="center">
      <table width="95%" align="center" class="adminlist">
        <tr class="quote">
          <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
          <td align="center"><strong>NMORG</strong></td>
          <td align="center"><strong>NMPLAN</strong></td>
          <td align="center"><strong>NO_TRANSAKSI</strong></td>
          <td align="center"><strong>NO_DO</strong></td>
          <td align="center"><strong>NO_SO</strong></td>
          <td align="center"><strong>NO_FO_REFICS</strong></td>
          <td align="center"><strong>NO_DO_REFF</strong></td>
          <td align="center"><strong>CREATE_DATE</strong></td>
          <td align="center"><strong>NO_BILLING</strong></td>
          <td align="center"><strong>QTY_BILLING</strong></td>
          <td align="center"><strong>NET PRICE</strong></td>
          <td align="center"><strong>SYNC</strong></td>
        </tr>

        <?php
        $data2 = -1;
        foreach ($dataarray5 as $key => $value) {

          $data2++;
          $b = $data2 + 1;
          if (($data2 % 2) == 0) {
            echo "<tr class='row0'>";
          } else {
            echo "<tr class='row1'>";
          }

        ?>
          <td align="center"><?php echo $b; ?></td>
          <td align="center"><?php echo $value['NMORG']; ?></td>
          <td align="center"><?php echo $value['NMPLAN']; ?></td>
          <td align="center"><?php echo $value['NO_TRANSAKSI']; ?></td>
          <td align="center"><?php echo $value['NO_DO']; ?></td>
          <td align="center"><?php echo $value['NO_SO']; ?></td>
          <td align="center"><?php echo $value['NO_PO_REFICS']; ?></td>
          <td align="center"><?php echo $value['NO_DO_REFF']; ?></td>
          <?php
          $tahun = substr($value['CREATE_DATE'], -8, -4);
          $bulan = substr($value['CREATE_DATE'], 4, 2);
          $tanggal = substr($value['CREATE_DATE'], -2);
          ?>
          <td align="center"><?php echo $tanggal . "-" . $bulan . "-" . $tahun; ?></td>
          <td align="center"><?php echo $value['NO_BILLING']; ?></td>
          <td align="center"><?php echo $value['FKIMG']; ?></td>
          <td align="center"><?php echo $value['NETPR']; ?></td>
          <td align="center"><?php echo $value['SYNC']; ?></td>

          </tr>
        <?php
        } ?>
        <tr class="quote">
          <td colspan="15" align="center">
            <a href="manual_sinkron_bill_sbi.php" target="isi" class="button">Back</a>
          </td>
        </tr>
      </table>
      <br>
      <form id="" name="" method="post" action="<? echo $page; ?>">
        <input type="text" name="startdate" value="<?php echo $startdate; ?>" hidden>
        <input type="text" name="enddate" value="<?php echo $enddate; ?>" hidden>
        <!-- <input name="datasinkron" type="submit" class="button" id="datasinkron" value="Data Sinkron" /> -->
      </form>
    </div>
  <? } ?>
  <p>&nbsp;</p>
  <script>
    function cek() {
      let tanggal1 = new Date(document.getElementById('startdate').value);
      let tanggal2 = new Date(document.getElementById('enddate').value);
      var selisihWaktu = Math.abs(tanggal2 - tanggal1);
      var selisihHari = selisihWaktu / (1000 * 60 * 60 * 24);
      if (selisihHari > 3) {
        alert("Silisih Waktu Melebihi 3 Hari Silahkan Sesuaikan Tanggal Kembali");
      }
    }
    let cari = document.getElementById('cari');
    cari.addEventListener('click', function(e) {
      let tanggal1 = new Date(document.getElementById('startdate').value);
      let tanggal2 = new Date(document.getElementById('enddate').value);
      var selisihWaktu = Math.abs(tanggal2 - tanggal1);
      var selisihHari = selisihWaktu / (1000 * 60 * 60 * 24);
      if (selisihHari > 3) {
        alert("Silisih Waktu Melebihi 3 Hari Silahkan Sesuaikan Tanggal Kembali");
        e.preventDefault();
        location.reload();
      }
    })
  </script>
</body>


</html>
