<? 
session_start();
include_once ('../include/ex_fungsi.php');
include_once ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$user_id=$_SESSION['user_id'];
$no_ba = isset($_GET['no_ba']) ? $_GET['no_ba']: '';
$user_name=$_SESSION['user_name'];
// echo $no_inv;
 
	$sqlchek = "select EB.*,  NAMA, NAMA_LENGKAP from EX_BA_TRACK EB
	JOIN TB_USER_BOOKING TUB ON TUB.ID = TO_NUMBER(EB.CREATED_BY)
	WHERE EB.NO_BA =  :no_ba ORDER BY  EB.ID ASC";
	// echo $sqlchek;
	$querycek = oci_parse($conn, $sqlchek);
	oci_bind_by_name($querycek, ":no_ba", $no_ba);
	oci_execute($querycek);
	 
 
?>

<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../include/jquery.min.js"></script>
<script type="text/javascript">
	function checkstatus() {
		var tdok = $("#tdok").val();
		var datacek = 0;
		var tanya;
		$.each($('.cek:checked'), function(index, val) {
    		datacek++;
    	});
    	if (datacek == tdok) {
    		tanya = confirm('Dokumen Telah lengkap, apakah anda yakin untuk melanjutkan proses PPL ?');
    	}else{
    		tanya = confirm('Dokumen Tidak lengkap, apakah anda yakin untuk melanjutkan proses Pengembalian Ke vendor ?');
    	}
    	// alert(tanya);
    	return tanya;
	}
</script>
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<?php if ($_POST['save']): ?>
	<center>Data berhasil disimpan <br><input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" />	</center>
<?php else: ?>
<br /><br /> 
<table  class="adminlist" cellspacing="0" cellpadding="0" border="0" width="80%" align="center">
		  
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Tanggal</strong></td>
									<td align="center" style="width: 100px;"><strong>Status</strong></td>
									<td align="center" style="width: 120px;"><strong>User</strong></td>
									<td align="center" style="width: 200px;"><strong>Keterangan</strong></td> 
								</tr>
							</thead>
							
							<tbody id="isiLampiran">
								<?php if (isset($_GET['no_ba'])) { ?>
								<tbody id="isiLampiran">
									<?php 
									$b = 0;  
									while ($datafunc = oci_fetch_assoc($querycek)) {
										$nama_lengkap = $datafunc['NAMA_LENGKAP'];
										if($datafunc['CREATED_BY'] == '0'){
											$nama_lengkap = "SYSTEM";
										}
										?>     
										<tr class='row-file'>
											<td align="center"><?php echo ++$b; ?></td>
											<td align="center"><?php echo $datafunc['CREATED_AT']; ?></td>
											<td align="center"><?php echo $datafunc['VALUE_BA']; ?></td>
											<td align="center"><?php echo $nama_lengkap; ?></td>
											<td align="center"><?php echo $datafunc['KOMENTAR_REJECT']; ?></td>
										</tr>
									<?php } ?>
								</tbody>
								<?php } ?>
							</tbody>
				 
			 
		</table> 
<br /><br />
 
</div>
</div>
<p>&nbsp;</p>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<?php endif; ?>
</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
