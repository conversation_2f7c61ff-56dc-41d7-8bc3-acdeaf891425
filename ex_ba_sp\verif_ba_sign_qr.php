<?php
session_start();
include('../include/ex_fungsi.php');
require 'library/phpqrcode/qrlib.php';
require 'library/tcpdf/tcpdf.php';
require 'library/fpdi/fpdi.php';
require_once 'helper.php';
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$user = new User_SP();
$kabiro = $user->get_kabiro();
$ids_pejabat = array();
foreach($kabiro as $p){
    array_push($ids_pejabat, $p['ID']);
};


$user_id = $_SESSION['user_id'];
$username = $_SESSION['user_name'];

if(empty($user_id)){
    $msg = "Harap login terlebih dahulu";
    showMessage($msg, false);
    exit;
}

if(in_array($user_id, $ids_pejabat)){
    $isAuthorize = true;
};

function showMessage($msg, $isValid = true)
{
    global $no_ba;

    if($isValid){
?>
        <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
            <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
                <div class="alert alert-info" role="alert">
                    <strong>Pesan!</strong>
                    <br>
                    <br>
                    <div class="alert alert-warning" role="alert"><?= $msg ?></div>
                    <a href="kabiro_ba_trans.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
                </div>
            </div>
<?php        
    }else{
?>
        <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
            <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
                <div class="alert alert-danger" role="alert">
                    <strong>Error!</strong>
                    <br>
                    <br>
                    <div class="" role="alert"><?= $msg ?></div>
                    <br>
                    <a href="verif_ba_sign_qr.php?no_ba=<?= $no_ba ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Coba Lagi&nbsp;&nbsp;&gt;&gt;</a>
                    <a href="kabiro_ba_trans.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
                </div>
            </div>
<?php
    }
}

if(!$isAuthorize){
    $msg = "Anda tidak berhak akses menu ini";
    showMessage($msg, false);
    exit;
}

$no_ba = $_GET['no_ba'];

$query_ba = "SELECT
EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL,
SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
FROM
EX_BA
JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
WHERE EX_BA.DELETE_MARK = '0' 
AND EX_BA.NO_BA = :no_ba
GROUP BY EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL
ORDER BY
EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data_ba = oci_fetch_array($sql_ba);
$status_ba = $data_ba['STATUS_BA'];
$no_ba = $data_ba['NO_BA'];
$id_ba = $data_ba['ID'];
$filename = $data_ba['FILENAME'];

if($status_ba == 40){

    if (!$data_ba['FILENAME']) {
    // $pdfExporter = new PdfExporter();
    // $response = $pdfExporter->beritaAcara($data['NO_BA']);

    // // Menyimpan pdf ke dalam file di CSMS
    // $pdf = fopen('upload/' . $filename, 'w');
    // fwrite($pdf, $response);
    // fclose($pdf);

    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$id_ba");

    // Update filename
    $filename = "DokumenBA-$no_ba.pdf";
    $fieldNames = array('FILENAME');
    $fieldData = array($filename);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data['FILENAME'] = $filename;
    }

    // URL to be encoded in QR Code
    $param = array(
        "no_ba" => $no_ba,
        "level" => "kabiro"
    );
    $param = base64_encode(json_encode($param));
    $signUrl = get_base_url() . "ex_ba_sp/api/verify_sign_ba.php?kode=" . $param;
    $qrFile = dirname(__FILE__) . "/upload/qr_code.png";
    // Generate QR Code
    QRcode::png($signUrl, $qrFile, QR_ECLEVEL_L, 5);

    // Open existing pdf
    $pdf = new FPDI();
    $pdf->AddPage();
    $pdf->setSourceFile( dirname(__FILE__) . '/upload/' . $filename); // Load existing PDF
    $tplIdx = $pdf->importPage(1);
    $pdf->useTemplate($tplIdx, 0, 0, 210);

    // Embed QR Code
    $pdf->Image('upload/qr_code.png', 135, 95, 20, 20, 'PNG');
    $pdf->Output(dirname(__FILE__) . '/upload/' . $filename, 'F');

    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
    $field_data = array("APPROVE KABIRO", "SYSDATE", "$username", "APPROVE KABIRO");
    $tablename = "EX_TRANS_HDR";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
    $field_data = array("50", "SYSDATE", "$username");
    $tablename = "EX_BA";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    // track
    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
    $field_data = array("$no_ba","50","Completed","$user_id","SYSDATE");
    $tablename = "EX_BA_TRACK";
    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

    //sendEmail
    $email_content_table = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA = :no_ba and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_bind_by_name($query, ":no_ba", $no_ba);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    $mailCc = '';
    
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }
    //end senEmail

    $msg = 'Dokumen BASTP berhasil disetujui';
}else if($status_ba >= 50){
    $msg = 'Dokumen BASTP sudah disetujui';
}else{
    $msg = 'Dokumen BASTP sedang tidak dalam status perlu persetujuan kabiro';
}
showMessage($msg, true);
?>