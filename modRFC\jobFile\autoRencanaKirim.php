<?php
// include('../../include/or_fungsi.php'); // harus ada include fungsi ketika di run by path

	class autoRencanaKirim
	{
		private $or_fungsi;
		private $conn;
		private $logfile;
		public $msg;
		public $dataFuncRFC;
		private $status;
		// private $dummyData;

		function __construct() {
			$this->status = 'SUKSES';
			$this->fungsi = new or_fungsi();
			$this->conn = $this->fungsi->or_koneksi();
			// $this->dummyData = []; // inisialisasi data dummy
			// $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
		}
	
		function saveLog() {
			$this->msg = substr($this->msg, 0, 900);
			$sqllog = "INSERT INTO RFC_LOG VALUES ('AUTO_RENCANA_KIRIM',SYSDATE,'" . $this->msg . "')";
			$querylog = oci_parse($this->conn, $sqllog);
			if ($querylog) {
				$execlog = @oci_execute($querylog);
			}
			//set running down
			$sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0, RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
			$query_run = oci_parse($this->conn, $sqlset_run);
			oci_execute($query_run);
			//end set
		}
		

		function run() {
			$this->msg .= "Start " . get_class($this) . "pada tanggal jam " . date('d-m-Y H:i:s') . ".<pre>";
			$datapp = array();

			unset($arraData);

            //---------------------- Tanggal Hari Ini ----------------------//
			$alreadyShownTotal = false;
            $alreadyShown = false;
			$sql = "SELECT
						* 
					FROM
						M_SETAUTO_RENCANA_KIRIM_PO 
					WHERE 
						DELETE_MARK = 0
						AND STATUS_PO = 0 ";
			echo "ini" . $sql;
			$stmt = oci_parse($this->conn, $sql);
			oci_execute($stmt);
			
			$result = array(); // gunakan sintaks array lama
			while ($row = oci_fetch_assoc($stmt)) {
                echo "perulangan " . $row;
				$results[] = $row; // Menyimpan NO_PO ke dalam array
			}

			if (empty($results)) {
				echo "Tidak ada data di pada tabel setting auto rencana kirim.";
				$this->msg .= "Tidak ada data di pada tabel setting auto rencana kirim.<pre>";
                $this->status = 'GAGAL';
                $this->saveLog();
			} else {
                foreach ($results as $row) {
                    $org = $row['ORG'];
                    $plant = $row['PLANT'];
                    $material = $row['MATERIAL'];
                    $satuan = $row['SATUAN'];
                    $unit = $row['UNIT'];
                    $valid_from = date('Ymd', strtotime($row['VALID_FROM']));
                    $valid_to = date('Ymd', strtotime($row['VALID_TO']));
                    $nopo = $row['NO_PO'];
                    $status_po = $row['STATUS_PO'];
                    $created_at = $row['CREATED_AT'];

                    // OPENING Z_ZAPPSD_PO_OPEN
                    $sap = new SAPConnection();
                    $sap->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
                    if ($sap->GetStatus() == SAPRFC_OK)
                        $sap->Open();
                        // echo $sap->PrintStatus();
                    if ($sap->GetStatus() != SAPRFC_OK) {
                        $this->msg .= $sap->PrintStatus();
                        $this->status = 'GAGAL';
                        $this->saveLog();
                        return false;
                    }

                    $fce = $sap->NewFunction ("Z_ZAPPSD_PO_OPEN_NW");
                    if ($fce == false ) { 
                        $sap->PrintStatus(); 
                        exit; 
                    }

                    //------------------- Set parameter export -------------------//
                    $fce->I_BUKRS = $org;
                    $fce->I_EINDT_FR = $valid_from;
                    $fce->I_EINDT_TO = $valid_to;

                    $fce->LR_PO->row["SIGN"]='I';
                    $fce->LR_PO->row["OPTION"]='EQ';
                    $fce->LR_PO->row["LOW"]=$nopo;
                    $fce->LR_PO->row["HIGH"]='';
                    $fce->LR_PO->Append($fce->LR_PO->row);

                    //------------------- Panggil fungsi SAP -------------------//
                    echo "<pre>";
                    print_r($fce);
                    echo "<pre>";
                    $fce->Call();

                    if ($fce->GetStatus() == SAPRFC_OK ) {
                        $fce->T_DATA->Reset();
                        $s=0;
                            
                        while ( $fce->T_DATA->Next() ){ 
                            $datadoc[$s]=$fce->T_DATA->row;
                            $werks[$s]= $fce->T_DATA->row["WERKS"];
                            $name1[$s] = $fce->T_DATA->row['NAME1'];
                            $lgort[$s] = $fce->T_DATA->row['LGORT'];
                            $lgobe[$s] = $fce->T_DATA->row['LGOBE'];
                            $reswk[$s] = $fce->T_DATA->row['RESWK'];
                            $name2[$s] = $fce->T_DATA->row['NAME2'];
                            $ebeln[$s] = $fce->T_DATA->row['EBELN'];
                            $ebelp[$s] = $fce->T_DATA->row['EBELP'];
                            $matnr[$s] = $fce->T_DATA->row['MATNR'];
                            $txz01[$s] = $fce->T_DATA->row['TXZ01'];
                            $eindt[$s] = $fce->T_DATA->row['EINDT'];
                            $bzirk[$s] = $fce->T_DATA->row['BZIRK'];
                            $bztxt[$s] = $fce->T_DATA->row['BZTXT'];
                            $menge[$s] = $fce->T_DATA->row['MENGE'];
                            $meins[$s] = $fce->T_DATA->row['MEINS'];
                            $menge_rel[$s] = $fce->T_DATA->row['MENGE_REL'];
                            $ntgew[$s] = $fce->T_DATA->row['NTGEW'];
                            $route[$s] = $fce->T_DATA->row['ROUTE'];
                            $s++;
                        }
                            
                        $total=count($datadoc);
                    } else {
                        $fce->PrintStatus();
                    }
                    
                    $fce->Close();	
                    $sap->Close();
                    //------------------- ENDING Z_ZAPPSD_PO_OPEN -------------------//

                    if (!$alreadyShownTotal) {
                        echo "<br>ini total = " . $total;
                        $alreadyShownTotal = true;
                    }
                    if ($total == 0) {
                        if (!$alreadyShown) {
                            echo "<br>Tidak ada data di RFC (Z_ZAPPSD_PO_OPEN_NW).";
                            $this->msg .= "Tidak ada data di RFC (Z_ZAPPSD_PO_OPEN_NW).<pre>";
                            $this->status = 'GAGAL';
                            $this->saveLog();
                            $alreadyShown = true;
                        }
                    } else {
                        for ($j = 0; $j <= $total; $j++) {
                            echo "<br>ini tanggal " . $eindt[$j];
                            $tglkemarin =  date('Ymd', strtotime('-1 day'));
                            if ($eindt[$j] == $tglkemarin) {
                                // if ($eindt[$j] ==  '20250505') {
                                $tanggal_h1 = $eindt[$j];
                                $po_create_h1 += $menge[$j]; // TOTAL PO CREATE H-1
                                $po_release_h1 += $menge_rel[$j]; // TOTAL PO RELEASE H-1
                                echo "Update berhasil! atas No Po ".$ebeln[$j].". PO Create H-1 terupdate dengan nilai = " . number_format($menge[$j], 0, ',', '.')  . " dan PO Release H-1 bernilai = " . number_format($menge_rel[$j], 0, ',', '.') . ".<br>\n";
                                $this->msg .= "<br>Update berhasil! atas No Po ".$ebeln[$j].". PO Create H-1 terupdate dengan nilai = " . number_format($menge[$j], 0, ',', '.')  . " dan PO Release H-1 bernilai = " . number_format($menge_rel[$j], 0, ',', '.') . ".<br>\n";
                            } else {
                                $po_create_h1 += 0;
                                $po_release_h1 += 0;
                                echo "<br>PADA TANGGAL = " . $eindt[$j] . ". Data H-1 Tidak ada.";
                                $this->msg .= "<br>PADA TANGGAL = " . $eindt[$j] . ". Data H-1 Tidak ada.";
                                $this->status = 'GAGAL';
                                $this->saveLog();
                            }

                            $tglhariini =  date('Ymd');
                            // if($eindt[$j] == '20250506') {
                            if($eindt[$j] == $tglhariini) {
                                $tanggalpo = $eindt[$j];
                                $kdmaterial = explode("-",  $matnr[$j]);
                                $kdmaterial = $kdmaterial[0] . '-' . $kdmaterial[1];
                                $unit_mat = number_format($ntgew[$j], 0, ',', '.');
                                $nopo = $ebeln[$j];
                                $plant_pengirim = $reswk[$j];
                                $plant_tujuan = $werks[$j];
                                $nama_plant = $name2[$j];

                                //------------------- mengambil data stok min -------------------//
                                $sql2 = "SELECT * FROM M_DATA_KETAHANAN_STOK_MIN WHERE PLANT_PENGIRIM = '$reswk[$j]' AND PLANT_TUJUAN = '$werks[$j]' AND MATERIAL = '$matnr[$j]' AND ORG = '$org' AND delete_mark = 0";
                                echo "<br>sql 2 = " .$sql2;
                                $strstokmin = oci_parse($this->conn, $sql2);
                                oci_execute($strstokmin);
                                $rowstokmin = oci_fetch_array($strstokmin, OCI_ASSOC);

                                $stokmin = $rowstokmin['JML_KETAHANAN_STOK_MIN'];
                                $kapasitas_gudang = $rowstokmin['KAPASITAS_GUDANG'];

                                //----------  mengambil data target pengeluaran harian -----------//
                                $sql3 = "SELECT SUM(scm.QTY_HARIAN) AS TARGET_HARIAN, zthn.DISTRIBUTOR FROM ZSD_TARGET_HARIAN_SCM scm 
                                JOIN ZSD_TARGET_HARIAN_NEW zthn ON scm.PLANT = zthn.PLANT AND scm.DISTRIK = zthn.DISTRIK WHERE scm.PLANT = '$werks[$j]' 
                                AND scm.MATERIAL = '$matnr[$j]' AND zthn.TIPE = '$kdmaterial' AND scm.DISTRIK = '$bzirk[$j]' AND scm.FLAG_DEL = 'X' GROUP BY zthn.DISTRIBUTOR";
                                echo "<br> sql 3 " . $sql3;
                                    
                                $strqtyharian = oci_parse($this->conn, $sql3);
                                oci_execute($strqtyharian);
                                $rowqtyharian = oci_fetch_array($strqtyharian, OCI_ASSOC);

                                $target_pengeluaran_harian = $rowqtyharian['TARGET_HARIAN'];
                                $dist234 = $rowqtyharian['DISTRIBUTOR'];
                                $distrik234 = $bzirk[$j];

                                $total_po_create += $menge[$j]; // TOTAL PO CREATE
                                $total_po_release += $menge_rel[$j]; // TOTAL PO RELEASE
                                // echo "Update berhasil! atas No Po ".$ebeln[$j].". PO Create terupdate dengan nilai = " . number_format($menge[$j], 0, ',', '.')  . " dan PO Release bernilai = " . number_format($menge_rel[$j], 0, ',', '.') . ".<br>\n";
                            } else {
                                $unit_mat = 0;
                                $stokmin = 0;
                                $kapasitas_gudang = 0;
                                $target_pengeluaran_harian = 0;
                                $total_po_create += 0;
                                $total_po_release += 0;
                                echo "<br> Pada tanggal hari ini = " . $tglhariini . "Data Kosong";
                                $this->msg .= "<br>Pada tanggal hari ini = " . $tglhariini . "Data Kosong";
                                $this->status = 'GAGAL';
                                $this->saveLog();
                            }
                        }

                        // OPENING ZRMM_STOCK_GIGR
                        $sap2 = new SAPConnection();
                        $sap2->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
                        if ($sap2->GetStatus() == SAPRFC_OK)
                            $sap2->Open();
                            // echo $sap->PrintStatus();
                        if ($sap2->GetStatus() != SAPRFC_OK) {
                            $this->msg .= $sap2->PrintStatus();
                            $this->status = 'GAGAL';
                            $this->saveLog();
                            return false;
                        }

                        $fce2 = $sap2->NewFunction ("ZRMM_STOCK_GIGR");
                        if ($fce2 == false ) { 
                            $sap2->PrintStatus(); 
                            exit; 
                        }

                        $fce2->T_MATNR->row["SIGN"]='I';
                        $fce2->T_MATNR->row["OPTION"]='EQ';
                        $fce2->T_MATNR->row["LOW"]=$material;
                        $fce2->T_MATNR->row["HIGH"]='';
                        $fce2->T_MATNR->Append($fce2->T_MATNR->row);

                        $fce2->T_BUKRS->row["SIGN"]='I';
                        $fce2->T_BUKRS->row["OPTION"]='EQ';
                        $fce2->T_BUKRS->row["LOW"]=$org;
                        $fce2->T_BUKRS->row["HIGH"]='';
                        $fce2->T_BUKRS->Append($fce2->T_BUKRS->row);

                        $fce2->T_WERKS->row["SIGN"]='I';
                        $fce2->T_WERKS->row["OPTION"]='BT';
                        $fce2->T_WERKS->row["LOW"]=$plant;
                        $fce2->T_WERKS->row["HIGH"]='';
                        $fce2->T_WERKS->Append($fce2->T_WERKS->row);

                        $fce2->T_BUDAT->row["SIGN"]='I';
                        $fce2->T_BUDAT->row["OPTION"]='BT';
                        $fce2->T_BUDAT->row["LOW"]=$valid_from;
                        $fce2->T_BUDAT->row["HIGH"]=$valid_to;
                        $fce2->T_BUDAT->Append($fce2->T_BUDAT->row);

                        // Panggil fungsi SAP
                        echo "<br><br>ini sap ke dua<br>";
                        echo "<pre>";
                        print_r($fce2);
                        echo "<pre>";
                        $fce2->Call();

                        if ($fce2->GetStatus() == SAPRFC_OK ) {
                            $fce2->IT_HASIL->Reset();
                            $s=0;
                                
                            while ( $fce2->IT_HASIL->Next() ){ 
                                $datadoc2[$s]=$fce2->IT_HASIL->row;
                                $matnr2[$s] = $fce2->IT_HASIL->row['MATNR'];
                                $matdesc2[$s] = $fce2->IT_HASIL->row['MATDESC'];
                                $werks2[$s] = $fce2->IT_HASIL->row['WERKS'];
                                $firstmenge2[$s] = $fce2->IT_HASIL->row['FIRSTMENGE'];
                                $isuedmenge2[$s] = $fce2->IT_HASIL->row['ISUEDMENGE'];
                                $recievemenge2[$s] = $fce2->IT_HASIL->row['RECIEVEMENGE'];
                                $endmenge2[$s] = $fce2->IT_HASIL->row['ENDMENGE'];
                                $meins2[$s] = $fce2->IT_HASIL->row['MEINS'];
                                $closingvalue[$s] = $fce2->IT_HASIL->row['CLOSINGVALUE'];
                                $waers2[$s] = $fce2->IT_HASIL->row['WAERS'];
                                $totalissued[$s] = $fce2->IT_HASIL->row['TOTALISSUED'];
                                $start_date2[$s] = $fce2->IT_HASIL->row['START_DATE'];
                                $end_date2[$s] = $fce2->IT_HASIL->row['END_DATE'];
                                $s++;
                            }
                                
                            $total2=count($datadoc2);
                        } else {
                            $fce2->PrintStatus();
                        }

                        $fce2->Close();	
                        $sap2->Close();

                        $sampai1 = 1;
                        for ($k = 0; $k <= $sampai1 - 1; $k++) {
                            // Formula Perhitungan 
                            $nilai_gr = $recievemenge2[$k];
                            $stok_awal = $firstmenge2[$k];
                            $plant2 = $werks2[$k];				
                        }
                        //ENDING ZRMM_STOCK_GIGR

                        // OPENING Z_ZAPPSD_RPT_REAL_STO
                        $sap3 = new SAPConnection();
                        $sap3->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
                        if ($sap3->GetStatus() == SAPRFC_OK)
                            $sap3->Open();

                        if ($sap3->GetStatus() != SAPRFC_OK) {
                            $this->msg .= $sap3->PrintStatus();
                            $this->status = 'GAGAL';
                            $this->saveLog();
                            return false;
                        }

                        $fce3 = $sap3->NewFunction("Z_ZAPPSD_RPT_REAL_STO");
                        if ($fce3 == false) {
                            $sap3->PrintStatus();
                            exit;
                        }
                        
                        // echo "plant = " . $plant;
                        $fce3->X_TGL1 = $valid_from;
                        $fce3->X_TGL2 = $valid_to;
                        // $fce3->X_WERKS = $plant;
                        $fce3->X_STATUS = '70';
                        $fce3->X_VKORG = $org;
                        $fce3->X_ITEM_NO = $material;
                        $fce3->X_WO_KONFIRMASI = 'X';
                        $fce3->X_PO = 'X';

                        $fce3->LR_EBELN->row["SIGN"] = 'I';
                        $fce3->LR_EBELN->row["OPTION"] = 'EQ';
                        $fce3->LR_EBELN->row["LOW"] = $nopo;
                        $fce3->LR_EBELN->row["HIGH"] = '';
                        $fce3->LR_EBELN->Append($fce3->LR_EBELN->row);

                        // Panggil fungsi SAP
                        echo "<br><br>ini sap ke tiga<br>";
                        echo "<pre>";
                        print_r($fce3);
                        echo "<pre>";
                        $fce3->Call();

                        if ($fce3->GetStatus() == SAPRFC_OK) {
                            $fce3->ZDATA->Reset();
                            $s = 0;

                            while ($fce3->ZDATA->Next()) {
                                $datadoc3[$s] = $fce3->ZDATA->row;
                                $no_gi[$s] = $fce3->ZDATA->row['NO_GI'];
                                $nopo_rpt[$s] = $fce3->ZDATA->row['NO_PO'];
                                $tgl_match2[$s] = $fce3->ZDATA->row['TGL_MATCH2'];

                                // Tampilkan data
                                
                                $tgl_hari_ini1=  date('Ymd', strtotime('-1 day'));
                                        
                                        // if($tgl_match2[$s]  == '20250506') {
                                if ($tgl_match2[$s] == $tgl_hari_ini1) {
                                    // Pastikan nilai KWANTUMX valid
                                    if (!empty($fce3->ZDATA->row['KWANTUMX'])) {
                                        $total_qty[$s] = (float) $fce3->ZDATA->row['KWANTUMX']; // Konversi ke float
                                    } else {
                                        $total_qty[$s] = 0; // Jika kosong, set ke 0
                                    }

                                    $total_nilai_gi += $total_qty[$s]; // Menambahkan nilai GI ke total

                                    echo "No GI Berhasil diambil!!! Ini NO GI = " . $no_gi[$s] . " dari NO PO = " . $nopo_rpt[$s] . " dan Ini jumlah QTY-nya = ".$total_qty[$s].".<br>\n";
                                    $this->msg .= "<br>No GI Berhasil diambil!!! Ini NO GI = " . $no_gi[$s] . " dari NO PO = " . $nopo_rpt[$s] . " dan Ini jumlah QTY-nya = ".$total_qty[$s].".<br>\n";
                                } else {
                                    $total_nilai_gi += 0;
                                    echo "<br> total_nilai_gi kosong DIKARENANAKAN TIDAK ADA DATA GI KOSONG, NO RPT KOSONG, DAN NO QTY JUGA KOSONG";
                                    $this->msg .= "<br>total_nilai_gi kosong DIKARENANAKAN TIDAK ADA DATA GI KOSONG, NO RPT KOSONG, DAN NO QTY JUGA KOSONG";
                                    $this->status = 'GAGAL';
                                    $this->saveLog();
                                }
                                $s++;
                            }

                            $total3 = count($datadoc3);
                        } else {
                            $fce3->PrintStatus();
                        }

                        $fce3->Close();
                        $sap3->Close();
                    }
                }
                if($total == 0) {
                    echo "<br>Tidak ada data di RFC (Z_ZAPPSD_PO_OPEN_NW).";
                    $this->msg .= "Tidak ada data di RFC (Z_ZAPPSD_PO_OPEN_NW).<pre>";
                    $this->status = 'GAGAL';
                    $this->saveLog();
                } else {
                // PEREHITUNGAN
                    $stok_akhir = $stok_awal + $nilai_gr - $total_nilai_gi;
                    $sisa_po =  $total_po_create - $total_po_release;
                    $sisa_po_h1 = $po_create_h1 - $po_release_h1;
                    $target_stok = $target_pengeluaran_harian * $stokmin;
                    $ket_stok = $stok_akhir / $target_pengeluaran_harian;
                    $frcst_stok = $stok_awal + $po_release_h1 - $target_pengeluaran_harian;

                    if ($frcst_stok > $target_stok) {
                        $rencana_kirim = 0;
                    }else {
                        $rencana_kirim = $target_stok - $frcst_stok;
                    }

                    // PERHITUNGAN SPACE GD
                    $space_gd = $kapasitas_gudang - $stok_akhir;

                    // PERHITUNGAN LS
                    $ls = ($stok_akhir / $kapasitas_gudang) * 100;

                    
                    $this->sendRencanaKirimToMaster($org, $plant_pengirim, $plant_tujuan, $material, $satuan, $rencana_kirim, $nopo, $nama_plant, $unit_mat);

                    // Tambahkan informasi total ke message
                    $total_po_create = number_format($total_po_create, 0, ',', '.');
                    $total_po_release = number_format($total_po_release, 0, ',', '.');
                    $sisa_po = number_format($sisa_po, 0, ',', '.');
                    $nilai_gr = number_format($nilai_gr, 0, ',', '.');
                    $total_nilai_gi = $total_nilai_gi == 0 ? "-" : $total_nilai_gi . " Zak";
                    $stok_awal = number_format($stok_awal, 0, ',', '.');
                    $stok_akhir = number_format($stok_akhir, 0, ',', '.');
                    $rencana_kirim = number_format($rencana_kirim, 0, ',', '.');
                    $target_stok = number_format($target_stok, 0, ',', '.');
                    $frcst_stok = number_format($frcst_stok, 0, ',', '.');
                    $target_pengeluaran_harian = number_format($target_pengeluaran_harian, 0, ',', '.');
                    $ket_stok = number_format($ket_stok, 0, ',', '.');
                    $space_gd = number_format($space_gd, 0, ',', '.');
                    $ls = number_format($ls, 0, ',', '.');
                    $stokmin = number_format($stokmin, 0, ',', '.');
                    $kapasitas_gudang = number_format($kapasitas_gudang, 0, ',', '.');
                    $tanggalpo = date('Y-m-d H:i:s', strtotime($tanggalpo));

                    $po_create_h1 = number_format($po_create_h1, 0, ',', '.');
                    $po_release_h1 = number_format($po_release_h1, 0, ',', '.');
                    $sisa_po_h1 = number_format($sisa_po_h1, 0, ',', '.');
                    $tanggal_h1 = date('d.m.Y', strtotime($tanggal_h1));

                    $this->rencanaKirimLog($total_po_create, $total_po_release, $sisa_po, $nilai_gr, $total_nilai_gi, $stok_awal, $stok_akhir, $rencana_kirim, $target_stok, $frcst_stok, $target_pengeluaran_harian, 
                                            $ket_stok, $space_gd, $ls, $stokmin,  $kapasitas_gudang, $tanggalpo, $po_create_h1, $po_release_h1, $sisa_po_h1, $tanggal_h1);

                    $this->msg .= "Jumlah Rencana Kirim = " . number_format($rencana_kirim, 0, ',', '.') . ".<pre>";
                    
                    $this->saveLog();
				}
			}
		}

		function sendRencanaKirimToMaster($org, $plant_pengirim, $plant_tujuan, $material, $satuan, $jml_rencana_kirim, $nopo, $nama_plant, $unit_mat) {
			$tgl_rencana_kirim = date('Y-m-d', strtotime('+1 day'));

			$sqlrencanakirim="INSERT INTO MASTER_RENCANAKIRIM_GP 
				(ORG, PLANT_PENGIRIM, PLANT_TUJUAN, MATERIAL, SATUAN, TGL_RENCANA_KIRIM, JML_RENCANA_KIRIM, CREATE_DATE, DELETE_MARK, NO_PO, PLANT_NAMA, UNIT_MAT, CREATE_BY) 
			VALUES 
				('$org', '$plant_pengirim', '$plant_tujuan', '$material', '$satuan', TO_DATE('$tgl_rencana_kirim', 'YYYY-MM-DD'), '$jml_rencana_kirim', SYSDATE, '0', '$nopo', '$nama_plant', '$unit_mat', 'SCHEDULER')";
			$mysql_setrencanakirim = oci_parse($this->conn, $sqlrencanakirim);
			oci_execute($mysql_setrencanakirim);

			if ($mysql_setrencanakirim) {
				// echo "Penambahan rencana kirim sukses dengan nopo = " . $nopo . ". dan jumlah rencana kirim = " . $jml_rencana_kirim . ".<pre>";
				$this->msg .= "Penambahan rencana kirim sukses dengan nopo = " . $nopo . ". dan jumlah rencana kirim = " . $jml_rencana_kirim . ".<pre>";
			} else {
				// echo "Penambahan rencana kirim gagal.<pre>";
				$this->msg .= "Penambahan rencana kirim gagal.<pre>";
			}
		}

		function rencanaKirimLog($PO_CREATE, $PO_RELEASE, $SISA_PO, $GR, $GI, $STOK_AWAL,  $STOK_AKHIR, $RENCANA_KIRIM, $TARGET_STOK, $FRCST_STOK, $TARGET_HARIAN, $KET_STOK, $SPACE_GD, $LS, $KET_STOK_MIN, $KAPASITAS_GUDANG, $TANGGAL_PO, $PO_CREATE_H1, $PO_RELEASE_H1, $SISA_PO_H1, $TANGGAL_H1) {
			$sqllog2= "INSERT INTO AUTO_RENCANA_KIRIM_LOG (PO_CREATE, PO_RELEASE_H1, SISA_PO, GR, GI, STOK_AWAL,  
							STOK_AKHIR, RENCANA_KIRIM, TARGET_STOK, FRCST_STOK, TARGET_HARIAN, KET_STOK, SPACE_GD, LS, KET_STOK_MIN, KAPASITAS_GUDANG, TANGGAL_PO) 
						values ('".$PO_CREATE."', '".$PO_RELEASE."', '".$SISA_PO."', '".$GR."', 
							'".$GI."', '". $STOK_AWAL."', '". $STOK_AKHIR."', '".$RENCANA_KIRIM."', '".$TARGET_STOK."', 
							'".$FRCST_STOK."', '".$TARGET_HARIAN."', '".$KET_STOK."', '".$SPACE_GD."', '".$LS."', '".$KET_STOK_MIN."', 
							'".$KAPASITAS_GUDANG."', TO_DATE('".$TANGGAL_PO."', 'YYYY-MM-DD HH24:MI:SS'))";
			$log_setrencanakirim = oci_parse($this->conn,$sqllog2);
			oci_execute($log_setrencanakirim);

			if ($log_setrencanakirim) {
				echo "<br><table border='1' cellpadding='5' cellspacing='0'style='width:100%'>";
				echo "<tr>
						<th rowspan='2'>Tanggal</th>
						<th colspan='3'>Simulasi PO STO</th>
						<th colspan='11'>Operasional Gudang STO</th>
						<th colspan='2'>Data Master DC</th>
					</tr>";
				echo "<tr>
						<td>PO Create</td>
						<td>Release PO ( Rilis H-1 )</td>
						<td>Sisa PO</td>
						<td>GR (Plant Tujuan)</td>
						<td>GI ( SPJ sudah GI/IDOC) (Plant Tujuan)</td>
						<td>Stok Awal ( SAP / DWH) (Plant Tujuan)</td>
						<td>Stok Akhir ( SAP/DWH) (Plant Tujuan)</td>
						<td>Renc. Kirim</td>
						<td>Target Stok</td>
						<td>Frcst stok H+1</td>
						<td>Target Pengeluaran Harian</td>
						<td>Ket Stok</td>
						<td>Space Gd</td>
						<td style='width:10px'>LS</td>
						<td>Ket Stok Min</td>
						<td>Kapasitas</td>
					</tr>";
				echo "<tr>
					<td> " . $TANGGAL_H1 ."</td>
					<td> " . $PO_CREATE_H1 ."</td>
					<td> " . $PO_RELEASE_H1 ."</td>
					<td> " . $SISA_PO_H1 ."</td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
					<td> </td>
				</tr>";
				echo "<tr>
						<td> " . $TANGGAL_PO ."</td>
						<td> " . $PO_CREATE ."</td>
						<td> " . $PO_RELEASE ."</td>
						<td> " . $SISA_PO ."</td>
						<td> " . $GR ."</td>
						<td> " . $GI ."</td>
						<td> " . $STOK_AWAL ."</td>
						<td> " . $STOK_AKHIR ."</td>
						<td> " . $RENCANA_KIRIM ."</td>
						<td> " . $TARGET_STOK ."</td>
						<td> " . $FRCST_STOK ."</td>
						<td> " . $TARGET_HARIAN ."</td>
						<td> " . $KET_STOK ."</td>
						<td> " . $SPACE_GD ."</td>
						<td style='width:10px'>" . $LS." %</td>
						<td> " . $KET_STOK_MIN ."</td>
						<td> " . $KAPASITAS_GUDANG ."</td>
						
					</tr>";
				echo "</table>";
			} else {
				echo "Penambahan LOG GAGAL.<pre>";
			}
		}
		
	}

?>
