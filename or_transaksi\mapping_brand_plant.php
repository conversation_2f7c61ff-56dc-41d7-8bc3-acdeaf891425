<?
/*
 * @liyantanto
 */
session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();


$targetVolume='mappingBrandPlantAct.php';
$titlepage='Mapping Brand Plant';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$distr_id=$_SESSION['distr_id'];
$mp_coics=$fungsi->getComin($conn,$user_org);
$nama = $fungsi->arrayorg();

$soldto=$fungsi->sapcode($distr_id);
// $soldto = str_replace(PHP_EOL,"\<br />", $sld) . " \\"; 
// $soldto=sprintf("%010s",$_SESSION['distr_id']);

//$user_id='mady';
$com=$user_org;
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,3);
	else return '0';
}
function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}
$waktu=date("d-m-Y");
$year = date("Y");
$yeara = date("Y", strtotime('+1year', strtotime($year)));
//$halaman_id=1896;//dev
//$halaman_id=3753;//prod
$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">				
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>
	 <a href="../index.php">Login....</a>
<?

exit();
}


?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<style type="text/css">
      #outtable{
        padding:1px;
        border:1px solid #e3e3e3;
        width:600px;
        border-radius: 5px;
      }
 
      .short{
        width: 50px;
      }
 
      .normal{
        width: 150px;
      }
      .tabel_1{
        border-collapse: collapse;
        font-family: arial;
        color:#5E5B5C;
      }

      .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .btn:not([disabled]) {
        color: white;
    }

    .icon-upload {
     background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
     background: transparent url("icon/excel.png") no-repeat scroll center center;
    }

    .icon-mail {
     background: transparent url("icon/send-mail.png") no-repeat scroll center center;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }
 
      thead th{
        text-align: left;
        padding: 7px;
      }
 
      tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
      }

      
</style>
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>
<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px">
        <thead>
            <tr>
                <th field="ck" checkbox="true"></th>     
                <th field="BRAND" width="300">BRAND</th>
                <th field="ORG_MD" width="300">ORG MD</th>
                <th field="PLANT_MD" width="300">PLANT MD</th>
                <th field="ORG_OPCO" width="300">ORG OPCO</th>
                <th field="PLANT_OPCO" width="300">PLANT OPCO</th>
                <th field="ROYALTY" width="300">ROYALTY</th>
                <th field="CREATED_BY" width="200">CREATED BY</th>
                <th field="CREATED_AT" width="100">CREATED DATE</th>
                <th field="UPDATED_BY" width="200">UPDATED BY</th>
                <th field="UPDATED_AT" width="100">UPDATED DATE</th>
            </tr>
        </thead>
    </table>
    <div id="toolbar">
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">Add</a> -->
           
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" onclick="newAct()" plain="true">Add</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="updateAppAct()">Edit</a> 
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="cancelAct()">Update</a> -->
            
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" plain="true" onclick="cancelAct()">delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-upload" href="template_xls/template_mapping_brand_plant.xls" >Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-excel" onclick="uploadAct()">Upload Excel</a>
            <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
            <!-- <select id="filter_status" class="easyui-combobox" iconCls="icon-search" name="filter_status" style="width:150px;">
                    <option value="Waiting Approve">Waiting Approve</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="ALL">ALL</option>
                </select> -->
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-mail" onclick="maintainCC()" plain="true">Maintain CC Email</a> -->

    </div>

    <div id="toolbarcc">
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">Add</a> -->
           
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" onclick="newActcc()" plain="true">Add CC</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="updateAppActcc()">Edit</a> 
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" plain="true" onclick="deleteAct()">delete</a>

            <!-- <a class="easyui-linkbutton" plain="true" iconCls="icon-upload" href="template_xls/template_mapping_cc_email.xls" >Download Template CC</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-excel" onclick="uploadActCC()">Upload Excel CC</a> -->
            <!-- <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a> -->

    </div>

    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle"><?=$titlepage;?></div>
            <form id="fm" method="post" novalidate>

                <!-- <div class="fitem" id="hideshipto">
                    <label>Code Shipto:</label>
                    <input type="text" class="easyui-combogrid" id="shipto" name="shipto" style="width:200px;" required="true"> 
                </div> -->
                <input type="hidden"  id="ID" name="ID">
                <!-- <div class="fitem">
                    <label>Brand</label>
                    <input type="text" class="easyui-textbox" id="brand" name="brand" style="width:200px;" required="true"> 
                </div> -->

                <div class="fitem">
                    <label>Brand</label>
                    <input type="text" class="easyui-combogrid" id="brand" name="brand" style="width:200px;" required="true"> 
                </div>

                <div class="fitem">
                    <label>Org. MD</label>
                    <input type="text" class="easyui-combogrid" id="org_md" name="org_md" style="width:200px;" required="true"> 
                </div>

                <div class="fitem">
                    <label>Plant MD</label>
                    <input type="text" class="easyui-combogrid" id="plant" name="plant" style="width:200px;" required="true"> 
                </div>
                
                <div class="fitem">
                    <label>Org. OPCO</label>
                    <input type="text" class="easyui-combogrid" id="org_opco" name="org_opco" style="width:200px;" required="true"> 
                </div>

                <div class="fitem">
                    <label>Plant OPCO</label>
                    <input type="text" class="easyui-combogrid" id="plant_opco" name="plant_opco" style="width:200px;" required="true"> 
                </div>
                
                <div class="fitem">
                    <label>Royalty</label>
                    <select class="easyui-combobox" id="royalty" name="royalty" style="width:200px;" required="true">
                        <option value="Y">YES</option>
                        <option value="X" selected>NO</option>
                    </select>
                </div>
            </form>
        </div>


        <div id="dlgAddCc" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true" buttons="#dlg-buttons-cc">
        <div class="ftitle"><?=$titlepage;?></div>
            <form id="fm2" method="post" novalidate>

                <div class="fitem" >
                    <label>Code kota:</label>
                    <input type="text" class="easyui-combogrid" id="kdkota" name="kdkota" style="width:200px;" required="true"> 
                    <input type="hidden"  id="ID" name="ID">
                </div>
                <div class="fitem">
                    <label>CC Email</label>
                    <input type="textarea" class="easyui-textbox" id="ccemail" name="ccemail" style="width:200px; height: 70px;" required="true"> 
                    <input type="hidden"  id="ktName" name="ktName">
                </div>
                
                 
            </form>
        </div>


        <div id="dlgCC" class="easyui-dialog" style="width:1200px;height:500px;padding:10px 20px" closed="true">
        <div class="ftitle">Maintain CC Email</div>
          
        <table id="dgcc" title="Maintain CC Email" class="easyui-datagrid" style="width:auto;height:350px">
        <thead>
            <tr>     
                <th field="KD_KOTA" width="150">Kode Kota</th>
                <th field="NM_KOTA" width="300">Nama Kota</th>
                <th field="CC_EMAIL" width="300">CC Email</th>
                <th field="CREATED_AT" width="300">CREATED AT</th>
                <th field="CREATED_BY" width="200">CREATED BY</th>
            </tr>
        </thead>
    </table>


        </div>




        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px" id="close">Cancel</a>
        </div>

        <div id="dlg-buttons-cc">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveActCC()" style="width:90px" id="savedataCC">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlgAddCc').dialog('close')" style="width:90px" id="close">Cancel</a>
        </div>
    </div>

    <div id="dlg_detail" class="easyui-dialog" style="width:800px; height:500px;padding:1px 20px" closed="true" buttons="#dlg_detail-buttons">
        <form id="fm_detail" method="post" novalidate>
            <div style="margin:10px 0;"></div>
            <div class="easyui-panel" id="judul_form" title="Target Harian Perdistributor" style="width:100%; padding:30px 60px;">
                <form id="ff_detail" method="post">
                    <div class="fitem">
                        <label style="width:40%">NO. Distributor : </label>
                        <input style="width:40%" readonly required="true" id="no_distr_detail" name="no_distr_detail" class="easyui-textbox">
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Nama Distributor : </label>
                        <input style="width:40%" readonly required="true" id="nama_distr_detail" name="nama_distr_detail" class="easyui-textbox">       
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Tipe Semen : </label>
                        <input style="width:40%" readonly required="true" id="tipe_semen_distr_detail" name="tipe_semen_distr_detail" class="easyui-textbox">
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Nama Distrik : </label>
                        <input style="width:40%" readonly required="true" id="distrik_distr_detail" name="distrik_distr_detail" class="easyui-textbox">
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Periode : </label>
                        <input style="width:40%" readonly required="true" id="periode_distr_detail" name="periode_distr_detail" class="easyui-textbox">       
                    </div>
                    <br>
                    <div id="detail_distr_harian"></div>
                </form>
                <div style="text-align:center;padding:5px 0">
                </div>
            </div>    
        </form>

        <div id="dlg_detail-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="updateAct()" style="width:90px" id="update_target">Update</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_detail').dialog('close')" style="width:90px;" id="close_detail">Cancel</a>
        </div>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload" name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px" id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>


    <div id="dlg_uploadcc" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttonscc">
        <form id="uploadFormcc" name="importcc" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_uploadcc" name="file_uploadcc" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttonscc">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadActcc()" style="width:90px" id="saveUploadcc">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_uploadcc').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>


    <!-- maintain email cc -->
    

<script type="text/javascript">

 $('#dist').combogrid({
        panelWidth:450,
        url: 'getdata_distsap.php',
        idField:'KUNNR',
        textField:'NAME1',
        fitColumns:true,
        method: 'get',
        loadMsg: 'Searching...',
        onSelect: function(index,row){
            var desc = row.NAME1;  // the product's description
            var kode = row.KUNNR;
//            $('#dist').val(desc);
            $('#dist').textbox('setValue', desc); 
            $('#nm_dist').val(kode);
        },    
        onChange:  function(newValue,oldValue) {
            $('#dist').textbox('setValue','');
//            $('#nm_dist').textbox('setValue','');
        },
        columns:[[
        {field:'KUNNR',title:'Kode',align:'center',width:100},
        {field:'NAME1',title:'Distributor',align:'center',width:350}
        ]]
    });


 $(function(){
    $("#dgcc").datagrid({
            url:'mappingBrandPlantAct.php?act=showcc',
            singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbarcc'
            
    });
    $('#dgcc').datagrid('enableFilter');    
 });


 $(function(){
    $("#dg").datagrid({
            url:'mappingBrandPlantAct.php?act=show',
            // singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');    
 });

 $('#brand').combogrid({
        panelWidth:200,
        url:'cSourcePlant_v2.php?act=getBrandRoyalty',
        idField:'BRAND',
        textField:'BRAND',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var nama = row.NAME1;
            $('#shipto_name').val(nama);
        },
        columns:[[
        {field:'BRAND',title:'BRAND',align:'BRAND',width:100},
        ]]
    });


    function getPlant(param){
        $('#plant').combogrid({
            panelWidth:400,
            url:'cSourcePlant_v2.php?act=getAllPlant&org='+ param,
            idField:'WERKS',
            textField:'NAME1',
            fitColumns:true,
            mode:'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            columns:[[
                {field:'WERKS',title:'PLANT',align:'PLANT',width:50},
                {field:'NAME1',title:'NAMA PLANT',align:'NAMA PLANT',width:150},
            ]],
            loadFilter: function(data){
                if ($.isArray(data)){
                    data = {total:data.length,rows:data};
                }
                $.map(data.rows, function(row){
                    row.NAME1 = row.WERKS+' - '+row.NAME1;
                });
                return data;
            }
        });
    }
    // $('#plant').combogrid({
    //     panelWidth:200,
    //     url:'cSourcePlant.php?act=getAllPlant',
    //     idField:'WERKS',
    //     textField:'NAME1',
    //     fitColumns:true,
    //     mode:'remote',
    //     loadMsg: 'Searching...',
    //     // pagination: true,
    //     onSelect: function(index,row){
    //         var kode = row.WERKS;
    //     },
    //     columns:[[
    //     {field:'WERKS',title:'PLANT',align:'PLANT',width:50},
    //     {field:'NAME1',title:'NAMA PLANT',align:'NAMA PLANT',width:150},
    //     ]]
    // });
    function getPlantOpco(param){
        $('#plant_opco').combogrid({
            panelWidth:200,
            url:'cSourcePlant_v2.php?act=getAllPlant&org='+ param,
            idField:'WERKS',
            textField:'NAME1',
            fitColumns:true,
            mode:'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            onSelect: function(index,row){
                var kode = row.WERKS;
            },
            columns:[[
            {field:'WERKS',title:'PLANT',align:'PLANT',width:50},
            {field:'NAME1',title:'NAMA PLANT',align:'NAMA PLANT',width:150},
            ]],
            loadFilter: function(data){
                if ($.isArray(data)){
                    data = {total:data.length,rows:data};
                }
                $.map(data.rows, function(row){
                    row.NAME1 = row.WERKS+' - '+row.NAME1;
                });
                return data;
            }
        });
    }
    
    $('#org_md').combogrid({
        panelWidth:200,
        url:'cSourcePlant_v2.php?act=getOrg',
        idField:'ORG',
        textField:'NAMA_ORG',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var kode = row.ORG;
            getPlant(kode)
        },
        columns:[[
        {field:'ORG',title:'ORG',align:'ORG',width:50},
        {field:'NAMA_ORG',title:'NAMA ORG',align:'NAMA ORG',width:150},
        ]],
        loadFilter: function(data){
            if ($.isArray(data)){
                data = {total:data.length,rows:data};
            }
            $.map(data.rows, function(row){
                row.NAMA_ORG = row.ORG+' - '+row.NAMA_ORG;
            });
            return data;
        }
    });
    
    $('#org_opco').combogrid({
        panelWidth:200,
        url:'cSourcePlant_v2.php?act=getOrg',
        idField:'ORG',
        textField:'NAMA_ORG',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var kode = row.ORG;
            getPlantOpco(kode)
        },
        columns:[[
        {field:'ORG',title:'ORG',align:'ORG',width:50},
        {field:'NAMA_ORG',title:'NAMA ORG',align:'NAMA ORG',width:150},
        ]],
        loadFilter: function(data){
            if ($.isArray(data)){
                data = {total:data.length,rows:data};
            }
            $.map(data.rows, function(row){
                row.NAMA_ORG = row.ORG+' - '+row.NAMA_ORG;
            });
            return data;
        }
    });
    
     $('#DISTRIK').combogrid({
        panelWidth:400,
        url: 'getdata_kotasap.php',
        idField:'BZIRK',
        textField:'BZTXT',
        fitColumns:true,
        mode:'remote',
        method: 'get',
        loadMsg: 'Searching...',
        onSelect: function(index,row){
            var nama = row.BZTXT;
            $('#NAMA_DISTRIK').val(nama);
            document.getElementById("NAMA_DISTRIK").value = nama;
        },
        columns:[[
        {field:'BZIRK',title:'DISTRIK',align:'center',width:30},
        {field:'BZTXT',title:'NAMA DISTRIK',align:'center',width:100}
        ]]
    });

    $('#filter_status').combobox({
        onChange: function(item) {
            var value = '';
            if (item == 'Waiting Approve') {
                value = '1';
            }else if (item == 'Approved') {
                value = '2';
            } else if (item == 'Rejected') {
                value = '3';
            }else{
                value = '4';
            }
            
            aksiGet(value)

        }
    });

    function aksiGet(value) {
        $('#dg').datagrid({
            url:'mappingBrandPlantAct.php?act=show&filter_status='+value
        });
            
          
        //  $('#dg').datagrid('enableFilter'); 
    }
    $('#shipto').combogrid({
        panelWidth:500,
        url:'mappingBrandPlantAct.php?act=getShipto',
        idField:'KUNN2',
        textField:'NAME1',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var nama = row.NAME1;
            $('#shipto_name').val(nama);
        },
        columns:[[
        {field:'KUNN2',title:'CODE SHIPTO',align:'CODE SHIPTO',width:100},
        {field:'NAME1',title:'NAME SHIPTO',align:'NAME SHIPTO',width:100},
        {field:'STRAS',title:'ADDRESS',align:'ADDRESS',width:100}
        ]]
    });


    $('#kdkota').combogrid({
        panelWidth:500,
        url:'mappingBrandPlantAct.php?act=getKota',
        idField:'KD_KOTA',
        textField:'NM_KOTA',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var nama = row.NM_KOTA;
            $('#ktName').val(nama);
        },
        columns:[[
        {field:'KD_KOTA',title:'KODE KOTA',align:'KODE KOTA',width:100},
        {field:'NM_KOTA',title:'NAMA KOTA',align:'NAMA KOTA',width:100}
        ]]
    });

    $('#app1').combogrid({
        panelWidth:500,
        url:'mappingBrandPlantAct.php?act=app1',
        idField:'NAMA',
        textField:'NAMA_LENGKAP',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        // pageSize:20,
        rownumbers:true,
        
        columns:[[
        {field:'NAMA',title:'Username',align:'Username',width:100},
        {field:'NAMA_LENGKAP',title:'Nama Lengkap',align:'Nama Lengkap',width:100},
        {field:'ALAMAT_EMAIL',title:'Email',align:'Email',width:100}
        ]]
    });

    $("#btnExport").click(function() {        
        var myData = $('#dg').datagrid('getData');        
        var mapForm = document.createElement("form");
        mapForm.id = "formexport";
        mapForm.target = "dialogSave";
        mapForm.method = "POST";
        mapForm.action = "exportMapping.php?act=mappingBrandPlant";        
        $.each(myData.rows, function(k,v){
            $.each(v, function(k2, v2){
                var hiddenField = document.createElement("input");              
                hiddenField.type = "hidden";
                hiddenField.name = "data[" + k + "][" + k2 + "]";
                hiddenField.value = v2;
                mapForm.appendChild(hiddenField);
            });
        });            
        document.body.appendChild(mapForm);
        mapForm.submit();
        document.body.removeChild(mapForm);
        
    });

    $('#app2').combogrid({
        panelWidth:500,
        url:'mappingBrandPlantAct.php?act=app1',
        idField:'NAMA',
        textField:'NAMA_LENGKAP',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        
        columns:[[
        {field:'NAMA',title:'Username',align:'Username',width:100},
        {field:'NAMA_LENGKAP',title:'Nama Lengkap',align:'Nama Lengkap',width:100},
        {field:'ALAMAT_EMAIL',title:'Email',align:'Email',width:100}
        ]]
    });



var url;
function newAct(value){
    $('#dlg').dialog('open').dialog('setTitle','Create');
    $("#hideshipto").show();
    //$('#SOLD_TO').textbox('setValue', $(this).val());
    $('#fm').form('clear');
    $("#soldto").textbox('setValue', value);
    url = 'mappingBrandPlantAct.php?act=add_brand_plant';
}


function newActcc(value){
    $('#dlgAddCc').dialog('open').dialog('setTitle','Create');
    //$('#SOLD_TO').textbox('setValue', $(this).val());
    $('#fm2').form('clear');
    url = 'mappingBrandPlantAct.php?act=addcc';
}


function maintainCC(value){
    $('#dlgCC').dialog('open').dialog('setTitle','Create');
 
}

function newAct(value){
    $('#dlg').dialog('open').dialog('setTitle','Create');
    $("#hideshipto").show();
    //$('#SOLD_TO').textbox('setValue', $(this).val());
    $('#fm').form('clear');
    $("#soldto").textbox('setValue', value);
    url = 'mappingBrandPlantAct.php?act=add_brand_plant';
}

function editAct(){
    var row = $('#dg').datagrid('getSelected');
    if (row){
        $('#dlg').dialog('open').dialog('setTitle','Edit');
        var idnh = row.ID;
        $('#fm').form('load',row);
        $('#ORG').combo('readonly', true);
        $('#TIPE_SEMEN').combo('readonly', true);
        $('#DISTRIK').combo('readonly', true);
        $('#BULAN').combo('readonly', true);
        $('#TAHUN').combo('readonly', true);
        url = 'mappingBrandPlantAct.php?act=edit&id='+row.ID;
    }
}

// function downloadExcel() {
//         let url = "cMbrand.php?act=downloadTemplate";
//         window.open(url);
//     }

function editadmDetailAct(){
    var row = $('#dg').datagrid('getSelected');
    // Waktu Sesuai data yang mau di Edit
    var bln = row.PERIODE.substr(0, 2);
    var thn = row.PERIODE.substr(2, 4);
    
    var tgl_1 = (bln+"/01/"+thn);
    var parse_1 = new Date(tgl_1).getTime() / 1000;

    // Waktu Hari ini
    var date = new Date();
    var month = date.getMonth()+1;
    var yy = date.getYear();
    var year = (yy < 1000) ? yy + 1900 : yy;

    var tgl_2 = (month+'/01/'+year);
    var parse_2 = new Date(tgl_2).getTime() / 1000;
    
    var diff = Math.floor(parse_1 - parse_2) / 2592000;
    // console.log(diff);
    if (row){
       
            url_data = 'mappingBrandPlantAct.php?act=detail&sold_to='+row.SOLD_TO+'&tipe_semen='+row.ID_TIPE_SEMEN+'&distrik='+row.DISTRIK+'&periode='+row.PERIODE;
            $.ajax({
                url: url_data,
                type:'GET',
                dataType:'json',
                async:false,
                success: function (data) {
                    $('#update_target').show();
                    $('#dlg_detail').dialog('open').dialog('setTitle','Edit Target Harian');
                    $('#fm_detail').form('load',row);

                    var periode = row.BULAN+' - '+row.TAHUN;

                    $('#no_distr_detail').textbox('setValue', row.SOLD_TO);
                    $('#nama_distr_detail').textbox('setValue', row.NAME1);
                    $('#tipe_semen_distr_detail').textbox('setValue', row.TIPE_SEMEN);
                    $('#distrik_distr_detail').textbox('setValue', row.DISTRIK);
                    $('#periode_distr_detail').textbox('setValue', periode);

                    var teks = '';
                    var no = 1;

                    teks += '<div id="outtable">'+
                                '<table cellspacing="0" width="600" class="adminform tabel_1">'+
                                    '<thead>'+
                                        '<tr>'+
                                            '<th><strong>No.</strong></th>'+
                                            '<th><strong>Tanggal Target</strong></th>'+
                                            '<th><strong>Target Harian</strong></th>'+
                                            // '<th><strong>Action</strong></th>'+
                                        '</tr>'+
                                    '</thead>';
                    
                    var banyak_data = data.length;
                    if (banyak_data == '') {
                        teks += '<tbody>'+
                                    '<tr>'+
                                        '<td colspan="5"><center><h3 style="color:red;">Data Kosong.</h3></center></td>'+
                                    '</tr>'+
                                '<tbody>';
                    } else {
                        for (var i = 0; i < banyak_data; i++) {
                            
                            teks += '<tbody>'+
                                        '<tr>'+
                                            '<td>'+no+++'</td>'+
                                            '<td>'+data[i]['TARGET_DATE']+'</td>'+
                                            '<td><input required="true" name="edit_target[]" id="'+data[i]['TARGET']+'" value="'+data[i]['TARGET']+'" class="easyui-textbox">'+
                                                 '<input type="hidden" name="id_target[]" id="'+data[i]['ID']+'" value="'+data[i]['ID']+'" class="easyui-textbox"></td>'+
                                            // '<td><a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editadmAct('+data[i]['ID']+')">Edit</a></td>'+
                                        '</tr>'+
                                    '<tbody>';
                        }
                    }
                        teks += '</table>'+
                            '</div>';
                    $('#detail_distr_harian').html(teks);

                    $('.tambah_disable').attr('disabled', 'true');

                    $('.id_bast_hidden').hide();
                }
            });
       
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di edit !', 'info');
    }
}

/*function editadmAct(id) {
    url_data = 'cMbrand.php?act=data_target&id='+id;
    $.ajax({
        url: url_data,
        type:'GET',
        dataType:'json',
        async:false,
        success: function (data) {
            console.log(data);
            var bln = data[0].PERIODE;
            var BULAN = bln.substr(0, 2)
            // $('#ORG').combo('readonly', true);
            $('#distrik_show').show();
            $('#distrik_show_tambah').hide();
            $('#distributor_tambah').hide();
            $('#distributor_edit').show();
            $('#id_distributor').hide();

            $('#id_distributor').textbox('setValue', data[0].SOLD_TO);
            $('#nama_distributor').textbox('setValue', data[0].NAME1);
            $('#DISTRIK_EDIT').textbox('setValue', data[0].DISTRIK);
            $('#TIPE_SEMEN').combobox('setValue', data[0].TIPE_SEMEN);
            $('#BULAN').combobox('setValue', BULAN);
            $('#TAHUN').combobox('setValue', data[0].TAHUN);
            $('#TARGET').textbox('setValue', data[0].TARGET);

            $('#nama_distributor').textbox('readonly');
            $('#TIPE_SEMEN').combobox('readonly');
            $('#DISTRIK_EDIT').textbox('readonly');
            $('#BULAN').combobox('readonly');
            $('#TAHUN').combobox('readonly');

            $('#dlg').dialog('open').dialog('setTitle','Edit');
            // var idnh = id;
            // $('#fm').form('load',row);
            url = 'cMbrand.php?act=editAdm&id='+id;
        }
    });
}*/

function updateAct(){
    var id_target   = [''];
    var edit_target = [''];
    
    var id_target_input     = document.getElementsByName('id_target[]');
    var edit_target_input   = document.getElementsByName('edit_target[]');

    for (var i = 0; i < id_target_input.length; i++) {
        var id_target_2 = id_target_input[i];
        id_target[i]    = id_target_2.value;

        var edit_target_2   = edit_target_input[i];
        edit_target[i]      = edit_target_2.value;

        // alert("id="+id_target_2.value+". value_2="+edit_target_2.value);
    }

    $('#fm_detail').form('submit',{
        url: 'mappingBrandPlantAct.php?act=editAdm&id_target_ku='+id_target+'&edit_target_ku='+edit_target,
        onSubmit: function() {
            if($(this).form('validate')){
                $.messager.progress({
                    title:'Please waiting',
                    msg:'Loading data...'
                });
            }
            return $(this).form('validate');
        },
        success: function(result){        
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
                $.messager.progress('close');
                $('#dlg_detail').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: result.success
                });

                $.messager.progress('close');
                $('#dlg_detail').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}


function saveAct(){
    $('#fm').form('submit',{
        url: url,
        onSubmit: function(){ 
            return $(this).form('validate');
        },
        success: function(result){
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: 'Success'
                });
                $('#dlg').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}

function saveActCC(){
    $('#fm2').form('submit',{
        url: url,
        onSubmit: function(){ 
            return $(this).form('validate');
        },
        success: function(result){
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: 'Success Insert Data'
                });
                // dlgCC
                $('#dlgAddCc').dialog('close'); // close the dialog
                $('#dgcc').datagrid('reload'); // reload the user data
            }
        }
    });
}

function cek(val,row){
        if(val=='1'){
            // return "<span style='color:yellow;'>Waiting Approve</span>";
            return `<span><button style="width:120px" class="btn yellow">Waiting Approve</button></span>`;
                                
        }else if (val=='2') {
            return `<span><button style="width:120px" class="btn green">Approved</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        } else {
            // return "<span style='color:red;'>Rejected</span>";
            return `<span><button style="width:120px" class="btn red">Rejected</button></span>`;
        } 
            
    }

function cancelAct(){
    var row = $('#dg').datagrid('getSelections');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('mappingBrandPlantAct.php?act=del&',{data:row},function(result){
            if (result.success){
                $('#dg').datagrid('reload'); // reload the user data
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.success
                });
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function deleteAct(){
    var row = $('#dgcc').datagrid('getSelected');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('mappingBrandPlantAct.php?act=delcc&',{id:row.ID},function(result){
            if (result.success){
                $('#dgcc').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function updateAppAct(){
    var row = $('#dg').datagrid('getSelected');
    if(row) {
            url = 'mappingBrandPlantAct.php?act=updateApp';
            $("#dlg").dialog('open').dialog('setTitle', 'Edit');
            $("#fm").form('clear');
            $("#fm").form("load", row);
            // console.log(row);
            
            // getPlant(row.ORG_MD, row.PLANT_MD);
            // getPlantOpco(row.ORG_OPCO, PLANT_OPCO);
            $("#brand").combogrid('setValue', row.BRAND);
            $("#org_md").combogrid('setValue', row.ORG_MD);
            $("#org_opco").combogrid('setValue', row.ORG_OPCO);
            $("#plant").combogrid('setValue', row.PLANT_MD);
            $("#plant_opco").combogrid('setValue', row.PLANT_OPCO);
            $("#royalty").combobox('setValue', row.IS_ROYALTY);
            // $("#royalty").val(row.IS_ROYALTY);
            // console.log(row.name);
            // $.ajax({
            //     url:'cSourcePlant.php?act=getAllPlant&org='+ row.ORG_MD + '&q='+ row.PLANT_MD,
            //     type:'GET',
            //     dataType:'json',
            //     async:false,
            //     success: function (data) {
            //         console.log("plant md", data);
            //         $("#plant").textbox('setValue', {WERKS : data[0].WERKS, NAME1 : data[0].WERKS + ' - ' + data[0].NAME1});
            //     }
            // });
            
            // $.ajax({
            //     url:'cSourcePlant.php?act=getAllPlant&org='+ row.ORG_OPCO + '&q='+ row.PLANT_OPCO,
            //     type:'GET',
            //     dataType:'json',
            //     async:false,
            //     success: function (data) {
            //         console.log("plant opco", data);
            //         $("#plant_opco").textbox('setValue', data[0].WERKS + ' - ' + data[0].NAME1);
            //         // $("#plant_opco").textbox('setValue', {WERKS : data[0].WERKS, NAME1 : data[0].WERKS + ' - ' + data[0].NAME1});
            //     }
            // });
            
        }
        else {
            $.messager.alert('Error', 'Select one of the data to be edited', 'error');
        }
}

function updateAppActcc(){
    var row = $('#dgcc').datagrid('getSelected');
    if(row) {
            $("#dlgAddCc").dialog('open').dialog('setTitle', 'Edit');
            $("#fm2").form('clear');
            $("#fm2").form("load", row);
            $("#kdkota").combogrid('setValue', row.KD_KOTA);
            $("#ktname").textbox('setValue', row.NM_KOTA);
            $("#ccemail").textbox('setValue', row.CC_EMAIL);
            // console.log(row.name);
            
            url = 'mappingBrandPlantAct.php?act=updateAppcc';
            
        }
        else {
            $.messager.alert('Error', 'Select one of the data to be edited', 'error');
        }
}

function downloadAct() {
    <?php if($distr_id == ''){?>
        self.location="template_xls/template_target_distributor_bulanan.xls";
    <?php } else { ?>
        self.location="template_xls/template_target_perdistributor.xls";
    <?php } ?>
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle','Upload Mapping Brand Plant');
    $('#uploadForm').form('clear');
    // url = 'cMbrand.php?act=upload_file';
}

function uploadActCC() {
    $('#dlg_uploadcc').dialog('open').dialog('setTitle','Upload Mapping CC Email');
    $('#uploadForm').form('clear');
    // url = 'cMbrand.php?act=upload_file';
}

function saveUploadAct() {
        $('#uploadForm').form('submit',{
            url: 'mappingBrandPlantAct.php?act=upload_file',
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    // $('#dlg_upload').dialog('close'); // close the dialog
                    // $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
  
}

function saveUploadActcc() {
        $('#uploadFormcc').form('submit',{
            url: 'mappingBrandPlantAct.php?act=upload_filecc',
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    $('#dlg_uploadcc').dialog('close'); // close the dialog
                    $('#dgcc').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg_uploadcc').dialog('close'); // close the dialog
                    $('#dgcc').datagrid('reload'); // reload the user data
                }
            }
        });
  
}



function detailAct(){
    var row = $('#dg').datagrid('getSelected');
    if (row){
        url_data = 'mappingBrandPlantAct.php?act=detail&sold_to='+row.SOLD_TO+'&tipe_semen='+row.ID_TIPE_SEMEN+'&distrik='+row.DISTRIK+'&periode='+row.PERIODE;
        $.ajax({
            url: url_data,
            type:'GET',
            dataType:'json',
            async:false,
            success: function (data) {
                $('#update_target').hide();
                $('#dlg_detail').dialog('open').dialog('setTitle','Detail Target Harian');
                $('#fm_detail').form('load',row);

                var periode = row.BULAN+' - '+row.TAHUN;

                $('#no_distr_detail').textbox('setValue', row.SOLD_TO);
                $('#nama_distr_detail').textbox('setValue', row.NAME1);
                $('#tipe_semen_distr_detail').textbox('setValue', row.TIPE_SEMEN);
                $('#distrik_distr_detail').textbox('setValue', row.DISTRIK);
                $('#periode_distr_detail').textbox('setValue', periode);

                var teks = '';
                var no = 1;

                teks += '<div id="outtable">'+
                            '<table cellspacing="0" width="600" class="adminform tabel_1">'+
                                '<thead>'+
                                    '<tr>'+
                                        '<th><strong>No.</strong></th>'+
                                        '<th><strong>Tanggal Target</strong></th>'+
                                        '<th><strong>Target Harian</strong></th>'+
                                    '</tr>'+
                                '</thead>';
                
                var banyak_data = data.length;
                if (banyak_data == '') {
                    teks += '<tbody>'+
                                '<tr>'+
                                    '<td colspan="5"><center><h3 style="color:red;">Data Kosong.</h3></center></td>'+
                                '</tr>'+
                            '<tbody>';
                } else {
                    for (var i = 0; i < banyak_data; i++) {
                        
                        teks += '<tbody>'+
                                    '<tr>'+
                                        '<td>'+no+++'</td>'+
                                        '<td>'+data[i]['TARGET_DATE']+'</td>'+
                                        '<td>'+data[i]['TARGET']+'</td>'+
                                    '</tr>'+
                                '<tbody>';
                    }
                }
                    teks += '</table>'+
                        '</div>';
                $('#detail_distr_harian').html(teks);

                $('.tambah_disable').attr('disabled', 'true');

                $('.id_bast_hidden').hide();
            }
        });
    }else{
        $.messager.alert('Confirm','Pilih baris data yang ingin di lihat terlebih dahulu!','info');
    }
}

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
