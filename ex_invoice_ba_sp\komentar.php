<?php 
session_start();
include ('../include/email.php');
$email = new Email();
require_once ("potonganoa_formula.php");
require_once('phpmailer.php');
require_once('class.smtp.php');
// mulai session untuk semua formula
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
.style1 {font-size: 20px}
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<script src="../include/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Untitled Document</title>
</head>

<body>
<p>&nbsp;</p>
<p>&nbsp;</p>
<? 
include ('../include/ex_fungsi.php');
//include ('../../../../prod/sd/sdonline/include/ex_fungsi.php');

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$user_name_cek_id=$_SESSION['user_name'];
if($user_name_cek_id != ""){
$action=$_POST['action'];

// Menangkap data upload file dan mengkonversi ke JSON
$upload_data = array();
$upload_dir = "lampiran/"; // Direktori untuk menyimpan file upload

// Pastikan direktori ada
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

if (!empty($_FILES)) {
    foreach ($_FILES as $key => $file) {
        if ($file['error'] == UPLOAD_ERR_OK && $file['size'] > 0) {
            // Validasi ukuran file (max 5MB)
            if ($file['size'] > 5 * 1024 * 1024) {
                echo "<script>alert('File " . $file['name'] . " terlalu besar. Maksimal 5MB.');</script>";
                continue;
            }
            
            // Generate nama file unik
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_filename = date('YmdHis') . '_' . $key . '_' . uniqid() . '.' . $file_extension;
            $target_path = $upload_dir . $unique_filename;
            
            // Pindahkan file ke direktori tujuan
            if (move_uploaded_file($file['tmp_name'], $target_path)) {
                $upload_info = array(
                    'original_name' => $file['name'],
                    'saved_name' => $unique_filename,
                    'file_path' => $target_path,
                    'type' => $file['type'],
                    'size' => $file['size'],
                    'upload_time' => date('Y-m-d H:i:s'),
                    'uploaded_by' => $_SESSION['user_name'],
                    'status' => 'uploaded'
                );
                $upload_data[$key] = $upload_info;
            } else {
                echo "<script>alert('Gagal mengupload file " . $file['name'] . "');</script>";
            }
        }
    }
}

// Menyimpan data upload dalam JSON format untuk dikirim ke formula_prod.php
$_POST['upload_data_json'] = json_encode($upload_data);

echo $action;
echo "<pre>";
print_r($_POST);
echo "</pre>";
// Hapus exit untuk melanjutkan pemrosesan
// exit;
include ('formula_prod.php');
}else{
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Session Login Anda Habis.... \n Silahkan Login Ulang...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?
exit();
}

?>

<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
<div class="alert alert-info" role="alert">
<strong>Pesan!</strong>
<?=$show_ket;?>
<br/>
<a href="<? echo $habis ?>">&lt;&lt; &nbsp;kembali&nbsp;&gt;&gt;</a>
<? if ($action == "create_invoice_bag_darat" or $action == "create_invoice_curah_darat" ){?>
<a href="print_invoice.php?no_invoice=<?=$no_invoice_in?>">&lt;&lt; &nbsp;cetak&nbsp;&gt;&gt;</a>
<? }?>

<? if ($action == "generate_ppl" and $pass_trn > 1){ ?>
<? 
/* <a href="run_ppl_trn.php?no_invoice=<?=$no_invoice?>">&lt;&lt; &nbsp;PPL TURUNAN&nbsp;&gt;&gt;</a> */
}
?>
</div>
</div>
<? include ('ekor.php'); ?>
</p>
<p>&nbsp;</p>
<p>&nbsp;</p>

</body>
</html>
