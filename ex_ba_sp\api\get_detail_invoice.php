<?php

// --- Konfigurasi username & password ---
$valid_users = array(
    "smbr-jaya" => "i-love-smbr"
);

// --- <PERSON>bil credential dari header Authorization ---
if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("success" => false, "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

// --- Validasi login ---
if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("success" => false, "message" => "Invalid credentials"));
    exit;
}

// Untuk PHP 5.2: Baca raw input karena php://input tidak otomatis di-parse
$rawInput = file_get_contents('php://input');
$request = json_decode($rawInput, true); // true = array, bukan object

// Siapkan response default
$response = array(
    "status" => "400",
    "message" => "Parameter tidak sesuai",
    "data" => array()
);

// Cek apakah parameter sesuai
if (
    isset($request['invoicedocnumber']) && 
    isset($request['fiscalyear']) &&
    isset($request['headerdata'])
) {

    $result = array();

    $response['status'] = "200";
    $response['message'] = "Success Invoke";
    $response['data'] = $result;
}

// Set response header
header('Content-Type: application/json');
echo json_encode($response);
?>
