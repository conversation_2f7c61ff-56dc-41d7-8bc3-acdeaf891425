<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=3135;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
	?>
	<SCRIPT LANGUAGE="JavaScript">
	alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
	</SCRIPT>
	<a href="../index.php">Login</a>
	<?
	exit;
}


$page="list_ba_approval.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$no_ba = $_POST['no_ba'];
// $no_invoice_expeditur = $_POST['no_invoice_expeditur'];

$currentPage="list_ba_approval.php";
$komen="";
if(isset($_POST['cari'])){
	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_ba == ""){
		$sql= "
			SELECT
				A.*,
				TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1,
				B.NO_INVOICE AS NO_INVOICE_BA,
				B.STATUS_BA_INVOICE
			FROM
				EX_BA A
				LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1
			WHERE
				A.DELETE_MARK ='0'
				AND A.ORG in ($orgIn)
				AND A.NO_BA IS NOT NULL
				AND B.DIPAKAI = 1
				AND (
					B.STATUS_BA_INVOICE = '20'
					OR B.STATUS_BA_INVOICE = '30'
				)
			ORDER BY
				A.ORG,
				A.NO_VENDOR,
				A.NO_BA DESC
		";
	} else {
		$pakeor=0;
		$sql= "
			SELECT
				A.*,
				TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1,
				B.NO_INVOICE AS NO_INVOICE_BA,
				B.STATUS_BA_INVOICE
			FROM
				EX_BA A
				LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1
			WHERE
		";
		if($vendor!=""){
		$sql.=" A.NO_VENDOR LIKE '$vendor'";
		$pakeor=1;
		}
		if($tanggal_mulai!="" or $tanggal_selesai!=""){

			if ($tanggal_mulai=="")
			$tanggal_mulai_sql = "01-01-1990";
			else
			$tanggal_mulai_sql = $tanggal_mulai;

			if ($tanggal_selesai=="")
			$tanggal_selesai_sql = "12-12-9999";
			else
			$tanggal_selesai_sql = $tanggal_selesai;

			if($pakeor==1){
			$sql.=" AND A.TGL_BA BETWEEN TO_DATE('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_DATE('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
			}else{
			$sql.=" A.TGL_BA BETWEEN TO_DATE('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_DATE('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
			$pakeor=1;
			}
		}
		if($no_ba!=""){
			if($pakeor==1){
			$sql.=" AND A.NO_BA LIKE '$no_ba' ";
			}else{
			$sql.=" A.NO_BA LIKE '$no_ba' ";
			$pakeor=1;
			}
		}
		$sql.=" AND A.DELETE_MARK ='0' AND A.ORG in ($orgIn) AND A.NO_BA IS NOT NULL AND B.DIPAKAI = 1 AND (B.STATUS_BA_INVOICE = '20' OR B.STATUS_BA_INVOICE = '30') ORDER BY A.ORG, A.NO_VENDOR, A.NO_BA DESC";
	}
	// echo $sql; exit;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$com[]=$row[ORG];
		$no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
		$no_invoice_ba_v[]=$row[NO_INVOICE_BA];
		$no_ba_v[]=$row[NO_BA];
		$no_invoice_v[]=$row[NO_INVOICE];
		$no_invoice_ex_v[]=$row[NO_INVOICE_EX];
		$vendor_v[]=$row[NO_VENDOR];
		$nama_vendor_v[]=$row[NAMA_VENDOR];
		$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
		$tgl_invoice_v[]=$row[TGL_INVOICE];
		$tgl_ba_v[]=$row[TGL_BA];
		$klaim_semen_v[]=$row[KLAIM_SEMEN];
		$klaim_ktg_v[]=$row[KLAIM_KTG];
		$pdpks_v[]=$row[PDPKS]; 
		$pend_ktg_v[]=$row[PDPKK]; 
		$pajak_v[]=$row[PAJAK_INV];
		$total_klaim_v[]=$row[TOTAL_INV];
		$no_baf[]=$row[NO_BAF];
		$status[]=$row[STATUS_BA];
		$status_invoice[]=$row[STATUS_BA_INVOICE];
		// }
	}
	$total=count($no_ba_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Invoice BA Rekapitulasi</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Invoice BA Rekapitulasi </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Berita Acara</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba?>"/></td>
    </tr>
    <!-- <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_expeditur?>"/></td>
    </tr> -->
    <tr>
      <td  class="puso">Periode Berita Acara </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>" />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
	<? } ?>
<br />
<br />
<?
	if($total>0){

?>
<form id="data_claim" name="data_claim" method="post" action="komentar.php" >

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td align="center"><strong>No.</strong></td>
		<td align="center"><strong>Org</strong></td>
		<td align="center"><strong>No Invoice</strong></td>
		<td align="center"><strong>Tgl BA</strong></td>
		<td align="center"><strong>Total SPJ</strong></td>
		<td align="center"><strong>Status</strong></td>
		<td align="center"><strong>Vendor</strong></td>
		<td align="center"><strong>Aksi</strong></td>
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
                $orgCom="orgke".$i;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

		<td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $com[$i]; ?><input name="<?=$orgCom;?>" id="<?=$orgCom;?>" type="hidden" value="<?=$com[$i];?>" /></td>
		<? 
		$no_cek=$no_ba_v[$i];
		$sql_print= "SELECT KODE_PRODUK FROM EX_TRANS_HDR WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0' GROUP BY KODE_PRODUK ";
		$query_print= oci_parse($conn, $sql_print);
		oci_execute($query_print);
	
		$row_print=oci_fetch_array($query_print);
		$kode_produk=$row_print[KODE_PRODUK];
		
		$rest = substr($kode_produk, 0, -5);
		?>	
		<td align="center"><a href="javascript:popUp('print_invoice_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_invoice_ba_v[$i]; ?></a></td>
		<!-- <td align="center"><? echo $no_invoice_ex_v[$i]; ?></td> -->
		<!-- <td align="center"><? echo $vendor_v[$i]; ?></td> -->
		<!-- <td align="center"><? echo $no_pajak_ex_v[$i]; ?></td> -->
		<td align="center"><? echo $tgl_ba_v[$i]; ?></td>
		<td align="center"><? echo number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."); ?></td>
		<td align="center"><?php
			if($status_invoice[$i] == '') {
				if($status[$i] == '50') {
					echo 'BA Approved';
				}
			} else {
				if($status_invoice[$i] == '10') { // upload invoice
					echo 'Upload Invoice';
				} else if($status_invoice[$i] == '20') { // waiting approval
					echo 'Waiting Approval';
				} else if($status_invoice[$i] == '30') { // revisi
					echo 'Revisi';
				} else if($status_invoice[$i] == '40') { // reject
					echo 'Rejected';
				} else if($status_invoice[$i] == '50') { // approve
					echo 'Approved';
				}
			}
		?></td>
		<td align="center"><? echo $nama_vendor_v[$i]; ?></td>
		<td align="center"><?php
			if($status_invoice[$i] == '') {
				if($status[$i] == '50') {
					echo '<a href="create_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Create</a></td>';
				}
			} else {
				if($status_invoice[$i] == '10') { // upload invoice
					echo '<a href="upload_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Detail</a></td>';
				} else if($status_invoice[$i] == '20') { // waiting approval
					echo '<a href="approval_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Detail</a></td>';
				} else if($status_invoice[$i] == '30') { // revisi
					echo '<a href="approval_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Detail</a></td>';
				} else if($status_invoice[$i] == '40') { // reject
					echo '<a href="revisi_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Detail</a></td>';
				} else if($status_invoice[$i] == '50') { // approve
					echo '<a href="detail_invoice_ba.php?no_ba='.$no_ba_v[$i].'">Detail</a></td>';
				}
			}
		?></td>
		</tr>
	  <? } ?>
			<tr class="quote">
				<td colspan="8" align="center" style="padding: 8px;">
					<a href="<?=$page?>" class="button">Back</a>
				</td>
			</tr>
		</tbody>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
