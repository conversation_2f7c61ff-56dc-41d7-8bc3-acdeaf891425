<?
/*
 * @liyantanto
 */
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();


$targetVolume='maintenancePeriodePenagihanMsaAct.php';
$titlepage='Maintenance Periode Penagihan';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$distr_id=$_SESSION['distr_id'];
$mp_coics=$fungsi->getComin($conn,$user_org);
$nama = $fungsi->arrayorg();

$soldto=$fungsi->sapcode($distr_id);
// $soldto = str_replace(PHP_EOL,"\<br />", $sld) . " \\"; 
// $soldto=sprintf("%010s",$_SESSION['distr_id']);

//$user_id='mady';
$com=$user_org;
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,3);
	else return '0';
}
function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}
$waktu=date("d-m-Y");
$year = date("Y");
$yeara = date("Y", strtotime('+1year', strtotime($year)));
//$halaman_id=1896;//dev
//$halaman_id=3753;//prod
$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);
$url = basename($_SERVER['SCRIPT_NAME']);

if ($url != 'login.php') {
    // Check if the user is logged in
    if (empty($_SESSION['user_id'])) {
		// var_dump ($_SESSION);
		// die;
        echo '<script type="text/javascript">';
        echo 'window.location.href = "https://csms.sig.id/sdonline/login.php";';
        echo '</script>';
        exit;
    }
}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<style type="text/css">
      #outtable{
        padding:1px;
        border:1px solid #e3e3e3;
        width:600px;
        border-radius: 5px;
      }
 
      .short{
        width: 50px;
      }
 
      .normal{
        width: 150px;
      }
      .tabel_1{
        border-collapse: collapse;
        font-family: arial;
        color:#5E5B5C;
      }

      .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .btn:not([disabled]) {
        color: white;
    }

    .icon-upload {
     background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
     background: transparent url("icon/excel.png") no-repeat scroll center center;
    }

    .icon-mail {
     background: transparent url("icon/send-mail.png") no-repeat scroll center center;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }
 
      thead th{
        text-align: left;
        padding: 7px;
      }
 
      tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
      }

      
</style>
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>
<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px">
        <thead>
            <tr>
                <th field="ck" checkbox="true"></th>     
                <th field="BUKRS" width="50">ORG</th>
                <th field="BUTXT" width="300">ORG DESCRIPTION</th>
                <th field="BRAND" width="100">BRAND</th>
                <th field="KONTRAK" width="100">PERIODE</th>
                <th field="TAHUN" width="50">YEAR</th>
            </tr>
        </thead>
    </table>
    <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="updateAppAct()">Edit</a> 
            <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
            <!-- <select id="filter_status" class="easyui-combobox" iconCls="icon-search" name="filter_status" style="width:150px;">
                    <option value="Waiting Approve">Waiting Approve</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="ALL">ALL</option>
                </select> -->
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-mail" onclick="maintainCC()" plain="true">Maintain CC Email</a> -->

    </div>

    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle"><?=$titlepage;?></div>
            <form id="fm" method="post" novalidate>

                <!-- <div class="fitem" id="hideshipto">
                    <label>Code Shipto:</label>
                    <input type="text" class="easyui-combogrid" id="shipto" name="shipto" style="width:200px;" required="true"> 
                </div> -->
                <input type="hidden"  id="ID" name="ID">
                <div class="fitem">
                    <label>ORG</label>
                    <input class="easyui-textbox" id="org" name="org" style="width:200px;" required="true" readonly> 
                </div>
                <div class="fitem">
                    <label>ORG DESCRIPTION</label>
                    <input class="easyui-textbox" id="org_description" name="org_description" style="width:200px;" required="true" readonly> 
                </div>
                <div class="fitem">
                    <label>BRAND</label>
                    <input class="easyui-textbox" id="brand" name="brand" style="width:200px;" required="true" readonly> 
                </div>
                <div class="fitem">
                    <label>PERIODE</label>
                    <select class="easyui-combobox" id="periode" name="periode" style="width:200px;" required="true">
                        <option value="1">Januari</option>
                        <option value="2">Februari</option>
                        <option value="3">Maret</option>
                        <option value="4">April</option>
                        <option value="5">Mei</option>
                        <option value="6">Juni</option>
                        <option value="7">Juli</option>
                        <option value="8">Agustus</option>
                        <option value="9">September</option>
                        <option value="10">Oktober</option>
                        <option value="11">November</option>
                        <option value="12">Desember</option>
                    </select>
                </div>
                <div class="fitem">
                    <label>YEAR</label>
                    <input class="easyui-textbox" id="year" name="year" style="width:200px;" required="true"> 
                </div>
            </form>
        </div>

        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px" id="close">Cancel</a>
        </div>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload" name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px" id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>

    <!-- maintain email cc -->
    

<script type="text/javascript">

 $(function(){
    $("#dgcc").datagrid({
            url:'maintenancePeriodePenagihanMsaAct.php?act=showcc',
            singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbarcc'
            
    });
    $('#dgcc').datagrid('enableFilter');    
 });


 $(function(){
    $("#dg").datagrid({
            url:'maintenancePeriodePenagihanMsaAct.php?act=show',
            // singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');    
 });

    $("#btnExport").click(function() {        
        var myData = $('#dg').datagrid('getData');        
        var mapForm = document.createElement("form");
        mapForm.id = "formexport";
        mapForm.target = "dialogSave";
        mapForm.method = "POST";
        mapForm.action = "maintenancePeriodePenagihanMsaAct.php?act=export_data";        
        $.each(myData.rows, function(k,v){
            $.each(v, function(k2, v2){
                var hiddenField = document.createElement("input");              
                hiddenField.type = "hidden";
                hiddenField.name = "data[" + k + "][" + k2 + "]";
                hiddenField.value = v2;
                mapForm.appendChild(hiddenField);
            });
        });            
        document.body.appendChild(mapForm);
        mapForm.submit();
        document.body.removeChild(mapForm);
        
    });



var url;

function newAct(value){
    $('#dlg').dialog('open').dialog('setTitle','Create');
    //$('#SOLD_TO').textbox('setValue', $(this).val());
    $('#fm').form('clear');
    url = 'maintenancePeriodePenagihanMsaAct.php?act=add';
}


function saveAct(){
    $.messager.confirm('Confirm','are you sure to save this transaction?',function(r){
        $('#fm').form('submit',{
            url: url,
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    });    
}


function cancelAct(){
    var row = $('#dg').datagrid('getSelections');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('maintenancePeriodePenagihanMsaAct.php?act=del&',{data:row},function(result){
            if (result.success){
                $('#dg').datagrid('reload'); // reload the user data
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.success
                });
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function deleteAct(){
    var row = $('#dgcc').datagrid('getSelected');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('maintenancePeriodePenagihanMsaAct.php?act=del&',{id:row.ID},function(result){
            if (result.success){
                $('#dgcc').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function updateAppAct(){
    var row = $('#dg').datagrid('getSelected');
    console.log(row);
    
    if(row) {
            $("#dlg").dialog('open').dialog('setTitle', 'Edit');
            // $("#fm").form('clear');
            $("#fm").form("load", row);
            // $('#brand').textbox({editable: false, value: row.BRAND});
            // $("#kode_material").textbox('setValue', row.KODE_MATERIAL);
            $("#org").textbox('setValue', row.BUKRS);
            $("#org_description").textbox('setValue', row.BUTXT);
            $("#brand").textbox('setValue', row.BRAND);
            $("#periode").combobox('setValue', row.KONTRAK);
            $("#year").textbox('setValue', row.TAHUN);
            
            url = 'maintenancePeriodePenagihanMsaAct.php?act=updateApp';
            
        }
        else {
            $.messager.alert('Error', 'Select one of the data to be edited', 'error');
        }
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle','Upload Maintenance Plant SO');
    $('#uploadForm').form('clear');
    // url = 'cMbrand.php?act=upload_file';
}

function saveUploadAct() {
        $('#uploadForm').form('submit',{
            url: 'maintenancePeriodePenagihanMsaAct.php?act=upload_file',
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
  
}

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
