<?
session_start();
include('../include/or_fungsi.php');
$fungsi = new or_fungsi();
$conn = $fungsi->or_koneksi();

require_once '../include/oracleDev.php';
$fungsi2 = new conntoracleDEVSD();
$connDEVSD = $fungsi2->DEVSDdb();

//Format Nilai
function showNilai2($nilai)
{
    if ($nilai > 0) return number_format($nilai, 2);
    else return '0';
}

//$hakakses=array("admin");
//$halaman_id=1484;//dev
//$halaman_id=2873;//prod
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'];
$user_org = $_SESSION['user_org'];
$namauser = $_SESSION['nama_lengkap'];
$distr = $_SESSION['distr_id'];
$distr = $fungsi->sapcode($distr);

$importtargetVolume = 'list_target_harian_new_brand1.php';
$waktu = date("d-m-Y");
$komen = '';

function tglIndo($param)
{
    $tahun = substr($param, 0, 4);
    $bulan = substr($param, 4, 2);
    $tgl = substr($param, 6, 2);
    $format = $tgl . "-" . $bulan . "-" . $tahun;
    return $format;
}
function timeIndo($param)
{
    $jam = substr($param, 0, 2);
    $menit = substr($param, 2, 2);
    $detik = substr($param, 4, 2);
    $format = $jam . ":" . $menit . ":" . $detik;
    return $format;
}
function showNilaiQTY($nilai)
{
    if ($nilai > 0) return number_format($nilai, 0, ",", ".");
    else return '0';
}
function showProce($nilai)
{
    if ($nilai > 0) return number_format($nilai, 2) . "%";
    else return '0';
}
function getColorProce($nilai)
{
    #Color 
    if ($nilai <= 0) {
        $nColor = '#22CE0F';
    } else if (trim($nilai) >= 100) {
        $nColor = '#22CE0F';
    } else if (trim($nilai) >= 80) {
        $nColor = '#FFFF99';
    } else if (trim($nilai) <= 79) {
        $nColor = '#FF5B53';
    }
    if (trim($nilai) >= 100) {
        $nColor = '#22CE0F';
    } else $nColor = '#FF5B53';

    return $nColor;
}

function getRilisPlant($getorg, $tipe, $from, $to)
{
    global $conn;

    $sqlRealTo = "SELECT 
                        COM,
                        substr(ITEM_NO,0,7) as TIPE,
                        to_char(TGL_MATCH2,'YYYYMMDD') as TGL_MATCH2,
                        BRAN1,
                        SOLD_TO,KOTA,
                        sum(kwantumx) as REALTO 
                    FROM zreport_rpt_real 
                    WHERE to_char(TGL_MATCH2,'YYYYMMDD') between '$from' and '$to'
                        and order_type <>'ZNL'
                        and ((item_no like '121-301%' and item_no <> '121-301-0240') or (item_no like '121-302%'))
                        and substr(ITEM_NO,0,7)='$tipe'
                        and com = '$getorg'
                        and no_polisi <> 'S11LO'
                        and sold_to  like '0000000%'
                    GROUP BY 
                        COM,
                        substr(ITEM_NO,0,7),
                        to_char(TGL_MATCH2,'YYYYMMDD'),
                        BRAN1,
                        SOLD_TO,
                        KOTA";

    $query = oci_parse($conn, $sqlRealTo);
    oci_execute($query);
    while ($row = oci_fetch_array($query)) {
        $dataRE[$row['COM'] . $row['TIPE'] . $row['TGL_MATCH2'] . $row['BRAN1'] . $row['SOLD_TO'] . $row['KOTA']] = $row['REALTO'];
    }
    return $dataRE;
}

// function getData($id)
// {
//     global $conn;

//     $sql = "SELECT * FROM ZSD_TARGET_HARIAN_NEW_BRAND WHERE ID = :id";
//     $query = oci_parse($conn, $sql);
//     oci_bind_by_name($query, ':id', $id);

//     if (oci_execute($query)) {
//         $row = oci_fetch_assoc($query);
//         return $row;
//     } else {
//         return array('error' => 'Failed to fetch data');
//     }
// }

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->getmainhalam_id($conn, $dirr);
// if ($fungsi->keamanan($halaman_id, $user_id) == 0) {


// ?>

//     <SCRIPT LANGUAGE="JavaScript">
//         alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
//     </SCRIPT>


//     <a href="../index.php">Login....</a>
// <?

//     exit();
// }

$reportexel = "";
$reportexel = $_POST['reportexel'];
$tgl_fr = date("d-m-Y", strtotime("+1 days"));
$tgl_to = date("d-m-Y", strtotime("+1 days"));


if ($reportexel == 'report') {

    //echo 'tekan data';
    //================================ eksport data ================================
    include('../Spreadsheet/Excel/Writer.php');
    $todate = date("Ymd");
    $xls = new Spreadsheet_Excel_Writer();
    $titt = "LaporanTargetHarianNewBrand_" . $todate . ".xls";
    $xls->send($titt);
    //======================================================================================================
}
include('../include/validasi.php');


if (isset($_POST['Hapus'])) {
    $id = $_POST['idDelete'];
    $sql = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND
        SET 
            del_mark = 1,
            LASTUPDATE_BY = '$user_id',
            LASTUPDATE_DATE = sysdate
        WHERE ID = $id";

    $query1 = oci_parse($conn, $sql);
    $upd = oci_execute($query1);

    $messData = "<span style='text-align:center;'>Hapus Data Berhasil</span> <br>";
}

if (isset($_POST['Edit'])) {
    $id = $_POST['idEdit'];
    $tipe =  $_POST['tipeEdit'];
    $tgl_target = isset($_POST['tanggalTargetEdit']) ? $_POST['tanggalTargetEdit'] : '';
    $district = $_POST['districtEdit'];
    $distributor = $_POST['distributorEdit'];
    $target = $_POST['targetEdit'];
    $brand = $_POST['brandEdit'];
    $status_brand = $_POST['statusBrandEdit'];
    $persentase = $_POST['porsiEdit'];
    $user_id = $_SESSION['user_id'];

    $tgl_target_format = date('Y-m', strtotime($tgl_target));

    $sqlSodlto = "SELECT
                    SOLD_TO 
                FROM
                    MAPPING_SOLDTO_PLAFON
                WHERE
                    DEL_MARK = '0'
                ORDER BY
                    SOLD_TO";

    $querySoldto = oci_parse($conn, $sqlSodlto);
    oci_execute($querySoldto);
    
    $cekSoldto = array();
    $i=0;
    while($rowSoldto=oci_fetch_array($querySoldto)){
        $cekSoldto[$i] = $rowSoldto['SOLD_TO'];
        $i++;
    }

    if (in_array($distributor, $cekSoldto)) {
        $plafon = "SELECT
                        mmw.KODE_DISTRIK,
                        CASE
                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SISA_PLAFON
                            ELSE tb2.SISA_PLAFON
                        END AS TARGET,
                        CASE
                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SEGMEN
                            ELSE tb2.SEGMEN
                        END AS SEGMEN
                    FROM
                        MAPPING_MASTER_WILAYAH mmw
                    LEFT JOIN (
                        SELECT
                            mpt.*,
                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                            CASE
                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                ELSE tbt2.TARGET
                            END AS TARGET_SPC,
                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                                                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                                    ELSE tbt2.TARGET
                                                                END, 0) AS SISA_PLAFON,
                            mms.SEGMEN,
                            prov.NM_PROV AS NAMA_PROVINSI
                        FROM
                            MAPPING_PLAFON_TARGET mpt
                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                            mpt.KD_PROP = prov.KD_PROV
                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                            mpt.PERIODE = mms.PERIODE
                            AND mpt.REGIONAL = mms.REGIONAL
                            AND mpt.KD_PROP = mms.KD_PROP
                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                            AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                            AND mpt.BRAND = mms.BRAND
                            AND mms.DEL_MARK = '0'
                        LEFT JOIN (
                            SELECT
                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                mmw.KODE_REGION,
                                mmw.KODE_PROVINSI,
                                mmw.DISTRIK_RET,
                                tb1.BRAND,
                                sum(tb1.TARGET) AS TARGET
                            FROM
                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                tb1.DISTRIK = mmw.KODE_DISTRIK
                            WHERE
                                tb1.DEL_MARK = '0'
                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_target_format'
                                    AND tb1.BRAND = '$brand'
                                GROUP BY
                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                    mmw.KODE_REGION,
                                    mmw.KODE_PROVINSI,
                                    mmw.DISTRIK_RET,
                                    tb1.BRAND) tbt1 ON
                            tbt1.PERIODE = mpt.PERIODE
                            AND tbt1.BRAND = mpt.BRAND
                            AND tbt1.KODE_REGION = mpt.REGIONAL
                            AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                            AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                        LEFT JOIN (
                            SELECT
                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                mmw.KODE_REGION,
                                mmw.KODE_PROVINSI,
                                tb1.BRAND,
                                sum(tb1.TARGET) AS TARGET
                            FROM
                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                tb1.DISTRIK = mmw.KODE_DISTRIK
                            WHERE
                                tb1.DEL_MARK = '0'
                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_target_format'
                                    AND tb1.BRAND = '$brand'
                                GROUP BY
                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                    mmw.KODE_REGION,
                                    mmw.KODE_PROVINSI,
                                    tb1.BRAND) tbt2 ON
                            tbt2.PERIODE = mpt.PERIODE
                            AND tbt2.BRAND = mpt.BRAND
                            AND tbt2.KODE_REGION = mpt.REGIONAL
                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                        WHERE
                            mpt.DEL_MARK = '0'
                            AND mpt.PERIODE = '$tgl_target_format'
                            AND mpt.BRAND = '$brand'
                        ORDER BY
                            mpt.PERIODE DESC,
                            mpt.REGIONAL,
                            mpt.KD_PROP,
                            mpt.DISTRIK_RET) tb1 ON
                        mmw.DISTRIK_RET = tb1.DISTRIK_RET
                    LEFT JOIN (
                        SELECT
                            mpt.*,
                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                            CASE
                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                ELSE tbt2.TARGET
                            END AS TARGET_SPC,
                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                                                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                                    ELSE tbt2.TARGET
                                                                END, 0) AS SISA_PLAFON,
                            mms.SEGMEN,
                            prov.NM_PROV AS NAMA_PROVINSI
                        FROM
                            MAPPING_PLAFON_TARGET mpt
                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                            mpt.KD_PROP = prov.KD_PROV
                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                            mpt.PERIODE = mms.PERIODE
                            AND mpt.REGIONAL = mms.REGIONAL
                            AND mpt.KD_PROP = mms.KD_PROP
                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                                AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                                    AND mpt.BRAND = mms.BRAND
                                    AND mms.DEL_MARK = '0'
                                LEFT JOIN (
                                    SELECT
                                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                        mmw.KODE_REGION,
                                        mmw.KODE_PROVINSI,
                                        mmw.DISTRIK_RET,
                                        tb1.BRAND,
                                        sum(tb1.TARGET) AS TARGET
                                    FROM
                                        ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                    LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                        tb1.DISTRIK = mmw.KODE_DISTRIK
                                    WHERE
                                        tb1.DEL_MARK = '0'
                                        AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_target_format'
                                            AND tb1.BRAND = '$brand'
                                        GROUP BY
                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                            mmw.KODE_REGION,
                                            mmw.KODE_PROVINSI,
                                            mmw.DISTRIK_RET,
                                            tb1.BRAND) tbt1 ON
                                    tbt1.PERIODE = mpt.PERIODE
                                        AND tbt1.BRAND = mpt.BRAND
                                        AND tbt1.KODE_REGION = mpt.REGIONAL
                                        AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                                        AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                                    LEFT JOIN (
                                        SELECT
                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                            mmw.KODE_REGION,
                                            mmw.KODE_PROVINSI,
                                            tb1.BRAND,
                                            sum(tb1.TARGET) AS TARGET
                                        FROM
                                            ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                        LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                            tb1.DISTRIK = mmw.KODE_DISTRIK
                                        WHERE
                                            tb1.DEL_MARK = '0'
                                            AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_target_format'
                                                AND tb1.BRAND = '$brand'
                                            GROUP BY
                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                mmw.KODE_REGION,
                                                mmw.KODE_PROVINSI,
                                                tb1.BRAND) tbt2 ON
                                        tbt2.PERIODE = mpt.PERIODE
                                            AND tbt2.BRAND = mpt.BRAND
                                            AND tbt2.KODE_REGION = mpt.REGIONAL
                                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                                        WHERE
                                            mpt.DEL_MARK = '0'
                                            AND mpt.PERIODE = '$tgl_target_format'
                                            AND mpt.BRAND = '$brand'
                                            AND mpt.DISTRIK_RET IS NULL
                                        ORDER BY
                                            mpt.PERIODE DESC,
                                            mpt.REGIONAL,
                                            mpt.KD_PROP,
                                            mpt.DISTRIK_RET) tb2 ON
                        mmw.KODE_PROVINSI = tb2.KD_PROP
                    WHERE
                        mmw.KODE_DISTRIK = '".$district."' ";
        
        $queryPlafon = oci_parse($conn, $plafon);
        oci_execute($queryPlafon);
        $rowPlafon = oci_fetch_array($queryPlafon);

        $cek_plafon = isset($rowPlafon['TARGET']) && $rowPlafon['TARGET'] ? $rowPlafon['TARGET'] : 0;
        $cek_plafon_segmen = isset($rowPlafon['SEGMEN']) && $rowPlafon['SEGMEN'] ? $rowPlafon['SEGMEN'] : 'MB';
        
        if (($target > (($cek_plafon + $target) + 2) || $cek_plafon == 0) && $cek_plafon_segmen == "FB") {
            $messData = "<span style='text-align:center;'>Gagal Edit, Target melebihi SNOP yang telah ditentukan (".number_format($cek_plafon, 0, ',', '.').") </span> <br>";
        }else {
            $sql = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND
                    SET 
                        -- TIPE = :tipe,
                        -- DISTRIK = :district,
                        -- DISTRIBUTOR = :distributor,
                        TARGET = :target,
                        PERSENTASE = :persentase,
                        STATUS_BRAND = :status_brand,
                        LASTUPDATE_BY = :user_id,
                        LASTUPDATE_DATE = sysdate
                    WHERE ID = :id";
        
            $query1 = oci_parse($conn, $sql);
            oci_bind_by_name($query1, ':target', $target);
            oci_bind_by_name($query1, ':persentase', $persentase);
            oci_bind_by_name($query1, ':status_brand', $status_brand);
            oci_bind_by_name($query1, ':user_id', $user_id);
            oci_bind_by_name($query1, ':id', $id);
            // 
            // oci_bind_by_name($query1, ':tipe', $tipe);
            // oci_bind_by_name($query1, ':district', $district);
            // oci_bind_by_name($query1, ':distributor', $distributor);
            $upd = oci_execute($query1);
        
            // var_dump($sql);
        
            $messData = "<span style='text-align:center;'>Update Data Berhasil</span> <br>";
        }
    } else {
        $sql = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND
                SET 
                    -- TIPE = :tipe,
                    -- DISTRIK = :district,
                    -- DISTRIBUTOR = :distributor,
                    TARGET = :target,
                    PERSENTASE = :persentase,
                    STATUS_BRAND = :status_brand,
                    LASTUPDATE_BY = :user_id,
                    LASTUPDATE_DATE = sysdate
                WHERE ID = :id";
    
        $query1 = oci_parse($conn, $sql);
        oci_bind_by_name($query1, ':target', $target);
        oci_bind_by_name($query1, ':persentase', $persentase);
        oci_bind_by_name($query1, ':status_brand', $status_brand);
        oci_bind_by_name($query1, ':user_id', $user_id);
        oci_bind_by_name($query1, ':id', $id);
        // 
        // oci_bind_by_name($query1, ':tipe', $tipe);
        // oci_bind_by_name($query1, ':district', $district);
        // oci_bind_by_name($query1, ':distributor', $distributor);
        $upd = oci_execute($query1);
    
        // var_dump($sql);
    
        $messData = "<span style='text-align:center;'>Update Data Berhasil</span> <br>";
    }

    
}

if (isset($_POST['Tambah'])) {
    $org2t = trim($_POST['org']);

    $mp_coics = $fungsi->getComin($conn, $user_org);
    if (count($mp_coics) > 0) {
        unset($inorg);
        $orgcounter = 0;
        foreach ($mp_coics as $keyOrg => $valorgm) {
            $inorg .= "'" . $keyOrg . "',";
            $orgcounter++;
        }
        $inorg = rtrim($inorg, ',');
    } else {
        $inorg = $user_org;
    }
    $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE DELETE_MARK = 0";
    $sqlbrand = "SELECT BRAND FROM MASTER_BRAND WHERE DEL_MARK = 'X'";
    $qbrand = oci_parse($conn, $sqlbrand);
    oci_execute($qbrand);
    $arrbrand = array();
    while ($rbrand = oci_fetch_assoc($qbrand)) {
        array_push($arrbrand, "'" . $rbrand['BRAND'] . "'");
    }
    $arrbrand = implode(",", $arrbrand);


    $sold_tof = trim($_POST['sold_to']);
    $nama_sold_to = trim($_POST['nama_sold_to']);
    $kode_distrikf = trim($_POST['kode_distrik']);
    $nama_distrik = trim($_POST['nama_distrik']);
    $tipecode = trim($_POST['tipe']);
    $brandcode = trim($_POST['brand']);

    $tglm = $_POST['tgl1'];
    list($day, $month, $year) = split("-", $tglm);
    // $tglm = $year . $month . $day;
    $tglm = $year.'-'.$month;
    $tgls = $_POST['tgl2'];
    list($day1, $month1, $year1) = split("-", $tgls);
    // $tgls = $year1 . $month1 . $day1;
    $tgls = $year1.'-'.$month1;
    unset($sqlfilter);
    unset($sqlfilterc);
    unset($sqlakhir);
    unset($groupall);
    if ($org2t != '' && $tglm != '' && $tgls != '') {

        if (date("Y-m", strtotime("$tgls -30 day")) <= $tglm) {

            if ($kode_distrikf != '') {
                $sqlfilter .= " and tb1.DISTRIK='$kode_distrikf' ";
                $sqlfilterc .= " and KODE_TUJUAN='$kode_distrikf' ";
                $groupall .= " and DISTRIK='$kode_distrikf' ";
            }
            if ($sold_tof != '') {
                $sqlfilter .= " and tb1.DISTRIBUTOR='$sold_tof' ";
            }

            if ($brandcode != '') {
                $sqlakhir .= " and tb1.BRAND ='$brandcode'";
            } else if ($tipecode == '121-301') {
                //            $sqlakhir .= " OR PLANTSET IN (".$arrbrand.")";
            } else if ($tipecode == '121-302') {
                $sqlakhir .= "";
            } else {
                //            $sqlakhir.= " and PLANTSET NOT IN (".$arrbrand.")";
            }

            if ($tipecode == '121-301' || $tipecode == '121-701') {
                $filtertgl = "and to_char(tb1.TANGGAL_TARGET,'YYYY-MM') between '$tglm' and '$tgls'";
                $filtertgl2 = "and to_char(TGL_KIRIM_PP,'YYYYMMDD') between '$tglm' and '$tgls'";
            } else {
                $filtertgl = "and to_char(tb1.TANGGAL_TARGET,'YYYY-MM') between '$tglm' and '$tgls'";
                $filtertgl2 = "and to_char(TGL_KIRIM_PP,'YYYYMM') = '" . substr($tglm, 0, -2) . "'";
            }

            if ($tipecode == '121-301BULANAN') {
                $tipecode = '121-301';
            }

            if ($tipecode == '121-701BULANAN') {
                $tipecode = '121-701';
            }

            // $kondisiKonvertMaterial="
            //         case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-5026') then QTY_PP*40/1000
            //         when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then QTY_PP*50/1000
            //         else QTY_PP
            //         end as QTY
            // ";

            $kondisiKonvertMaterial = "case when KODE_PRODUK in (SELECT MATNR FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 WHERE NTGEW LIKE '4%') then QTY_PP*40/1000
                when KODE_PRODUK in (SELECT MATNR FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 WHERE NTGEW LIKE '5%') then QTY_PP*50/1000
                when KODE_PRODUK in (SELECT MATNR FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 WHERE NTGEW='2000.000') then QTY_PP*2
                else QTY_PP
                end as QTY";

            //Menampung data order
            $sqltranclog = "TRUNCATE TABLE ZREPORT_ORDER_LOG";
            $query = oci_parse($conn, $sqltranclog);
            oci_execute($query);

            // Log
            $sqlinsertlog = "
                    INSERT INTO ZREPORT_ORDER_LOG
                    (
                    SOLD_TO,
                    PLANT_ASAL,
                    KODE_TUJUAN,
                    TGL_KIRIM_PP,
                    PP_OPEN,
                    PP_REAL,
                    PP_OPENPP
                    )
                    select tb88.*,tb99.PP_OPENPP from (
                        select tb66.*,tb77.PP_REAL from (
                        select SOLD_TO,PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP,nvl(SUM(QTY),0) as PP_OPEN from (
                        select tb3.*,tb2.*,
                        $kondisiKonvertMaterial
                        from (
                        select SOLD_TO,NO_PP,PLANT_ASAL from OR_TRANS_HDR 
                        where DELETE_MARK=0 and ORG in($inorg) and PLANT_ASAL IS NOT NULL and FLAG_LELANG is null and NO_SO_OLD is null
                        and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                        )tb3 inner join (
                            select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY_PPA) as QTY_PP
                            from(
                            select tbdtl1.*,
                            case when tbdtl1.QTY_APPROVE is not null then tbdtl1.QTY_APPROVE
                            else tbdtl1.QTY_PP
                            end as QTY_PPA
                            from OR_TRANS_DTL tbdtl1 where DELETE_MARK=0 and STATUS_LINE<>'REJECTED'
                            $filtertgl2 
                            and KODE_PRODUK like '$tipecode%' $sqlfilterc
                            )
                            group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP
                        )tb2 on(tb3.NO_PP=tb2.NO_PP)
                        )tb3
                        group by PLANT_ASAL,SOLD_TO,KODE_TUJUAN,TGL_KIRIM_PP
                        )tb66 left join (
                            select SOLD_TO,PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY) as PP_REAL from (
                            select tb3.*,tb2.*,
                            $kondisiKonvertMaterial
                            from (
                            select SOLD_TO,NO_PP,PLANT_ASAL from OR_TRANS_HDR 
                            where DELETE_MARK=0 and ORG in($inorg) and PLANT_ASAL IS NOT NULL and NO_SO_OLD is null
                            and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                            )tb3 inner join (
                                select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_APPROVE as TGL_KIRIM_PP,nvl(SUM(QTY_PPA),0) as QTY_PP
                                from(
                                select tbdtl2.*,
                                case when tbdtl2.QTY_APPROVE is not null then tbdtl2.QTY_APPROVE
                                else tbdtl2.QTY_PP
                                end as QTY_PPA
                                from OR_TRANS_DTL tbdtl2 where DELETE_MARK=0 and STATUS_LINE='APPROVE' and NO_SO is not null
                                    $filtertgl2 
                                and KODE_PRODUK like '$tipecode%' $sqlfilterc
                                )
                                group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_APPROVE
                            )tb2 on(tb3.NO_PP=tb2.NO_PP)
                            )tb3
                            group by PLANT_ASAL,SOLD_TO,KODE_TUJUAN,TGL_KIRIM_PP

                        )tb77 on(tb66.PLANT_ASAL=tb77.PLANT_ASAL and tb66.SOLD_TO=tb77.SOLD_TO and tb66.KODE_TUJUAN=tb77.KODE_TUJUAN and tb66.TGL_KIRIM_PP=tb77.TGL_KIRIM_PP)
                        )tb88 left join(
                            select SOLD_TO,PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP,nvl(SUM(QTY),0) as PP_OPENPP from (
                            select tb3.*,tb2.*,
                            $kondisiKonvertMaterial
                            from (
                            select SOLD_TO,NO_PP,PLANT_ASAL from OR_TRANS_HDR 
                            where DELETE_MARK=0 and ORG in($inorg) and PLANT_ASAL IS NOT NULL and FLAG_LELANG is not null and NO_SO_OLD is null
                            and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                            )tb3 inner join (
                                select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY_PPA) as QTY_PP
                                from(
                                select tbdtl3.*,
                                case when tbdtl3.QTY_APPROVE is not null then tbdtl3.QTY_APPROVE
                                else tbdtl3.QTY_PP
                                end as QTY_PPA
                                from OR_TRANS_DTL tbdtl3 where DELETE_MARK=0 and STATUS_LINE<>'REJECTED'
                            $filtertgl2 
                                and KODE_PRODUK like '$tipecode%' $sqlfilterc
                                )
                                group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP
                            )tb2 on(tb3.NO_PP=tb2.NO_PP)
                            )tb3
                            group by PLANT_ASAL,SOLD_TO,KODE_TUJUAN,TGL_KIRIM_PP
                        )tb99 on(tb88.PLANT_ASAL=tb99.PLANT_ASAL and tb88.SOLD_TO=tb99.SOLD_TO and tb88.KODE_TUJUAN=tb99.KODE_TUJUAN and tb88.TGL_KIRIM_PP=tb99.TGL_KIRIM_PP)
                        ";
                //    echo "tipe tidak ada";
                //    echo "<br>"; 
                //  echo $sqlinsertlog;
            $queryg = oci_parse($conn, $sqlinsertlog);
            oci_execute($queryg);

            if ($_POST['tipe']) {
                $sql = "SELECT 
                            tb1.ID, 
                            tb1.TIPE,
                            tb1.BRAND, 
                            tb1.STATUS_BRAND, 
                            tb1.TARGET, 
                            tb1.TANGGAL_TARGET, 
                            tb1.DISTRIK, 
                            tb1.DISTRIBUTOR, 
                            tb1.PERSENTASE, 
                            tb2.NAME1 AS distributor_name,
                            zmk.NM_KOTA,
                            coalesce(tbpp.QTY_PP,0) QTY_PP, 
                            coalesce(tbpp.QTY_APPROVE,0) QTY_APPROVE,
                            mmb.FLAGING AS FLAG_ON_OFF,
                            CASE 
                                WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
                                WHEN mmw.KODE_REGION IN ('1', '2', '6') AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
                                else mms3.SEGMEN
                            END AS STATUS_BRAND_NEW
                        FROM 
                            ZSD_TARGET_HARIAN_NEW_BRAND tb1
                        LEFT JOIN 
                            RFC_Z_ZCSD_DIST tb2 ON tb2.KUNNR = tb1.DISTRIBUTOR 
                        LEFT JOIN 
                            ZSD_BLACKLISTDIST tb3 ON tb3.SOLD_TO = tb1.DISTRIBUTOR 
                            AND tb3.AREA IN (
                                SELECT KD_AREA 
                                FROM ZREPORT_M_KOTA 
                                WHERE KD_KOTA = tb1.DISTRIK 
                                GROUP BY KD_AREA
                            ) 
                            AND tb3.FROM_AKTIF <= tb1.TANGGAL_TARGET 
                            AND tb3.TO_AKTIF >= tb1.TANGGAL_TARGET 
                            AND tb3.DELETE_MARK = 0 
                            AND tb3.STATUS = 'APPROVE'
                        LEFT JOIN (
                            SELECT coalesce(assoc.SOLDTO_ASSOC,SOLD_TO) SOLD_TO, BRAND, KODE_TUJUAN, TGL_KIRIM_PPF, SUM(QTY_PP_TON) AS QTY_PP, SUM(QTY_APPROVE_TON) AS QTY_APPROVE
                            FROM (
                                SELECT a.*
                                        ,(
                                            CASE								
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '4%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_PP * 40 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '5%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_PP * 50 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW = '2000.000'
                                                ) THEN a.QTY_PP * 2 
                                                ELSE a.QTY_PP 
                                            END
                                        ) AS QTY_PP_TON, 
                                        (
                                            CASE								
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '4%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_APPROVE * 40 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '5%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_APPROVE * 50 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW = '2000.000'
                                                ) THEN a.QTY_APPROVE * 2 
                                                ELSE a.QTY_APPROVE 
                                            END
                                        ) AS QTY_APPROVE_TON ,
                                    to_char(a.TGL_LEADTIME,'YYYY-MM') TGL_KIRIM_PPF,
                                    scm.BRAND
                                    FROM OR_TRANS_HDR_V a
                                    /*LEFT JOIN (
                                    SELECT scm.PERIODE, scm.PLANT, scm.BRAND, scm.MATERIAL, scm.DISTRIK, scm.INCOTERM, scm.TIPE_ORDER 
                                    FROM ZSD_TARGET_HEADER_SCM scm
                                    WHERE scm.FLAG_DEL != 'Y'
                                    AND scm.MATERIAL LIKE '121-301-7%'
                                    GROUP BY scm.PERIODE, scm.PLANT, scm.BRAND, scm.MATERIAL, scm.DISTRIK, scm.INCOTERM, scm.TIPE_ORDER 
                                    )scm ON a.KODE_PRODUK=scm.MATERIAL AND to_char(a.TGL_LEADTIME,'MM-YYYY')=scm.PERIODE
                                        AND a.KODE_TUJUAN=scm.DISTRIK AND a.INCOTERM=scm.INCOTERM 
                                        AND a.PLANT_ASAL=scm.PLANT
                                        AND ((a.SO_TYPE <> 'ZPR' AND scm.TIPE_ORDER != '1') OR (a.SO_TYPE='ZPR' AND scm.TIPE_ORDER='1'))*/
                                    LEFT JOIN MAPPING_MATERIAL_BRAND scm on scm.KODE_MATERIAL=a.KODE_PRODUK and scm.FLAG_DEL != 'Y'
                                    WHERE
                                    a.STATUS_LINE <> 'REJECTED'
                                    AND a.KODE_PRODUK like '121-301-7%'
                                    AND a.NO_SO_OLD IS NULL 
                                        AND (
                                        a.TIPEPP <> 'NEW PROYEK' 
                                        OR a.TIPEPP IS NULL 
                                        OR (
                                            a.TIPEPP = 'NEW PROYEK' 
                                            AND a.SO_TYPE = 'ZPR' 
                                            AND a.STATUS = 'APPROVE'
                                        )
                                    )                             
                            )a
                            LEFT JOIN MAPPING_SOLDTO_ASSOC assoc on assoc.FLAG_DEL != 'Y' and assoc.SOLDTO=a.SOLD_TO
                            GROUP BY coalesce(assoc.SOLDTO_ASSOC,SOLD_TO), BRAND, KODE_TUJUAN, TGL_KIRIM_PPF
                        ) tbpp ON tb1.DISTRIBUTOR = tbpp.SOLD_TO 
                            AND TO_CHAR(tb1.TANGGAL_TARGET, 'YYYY-MM') = tbpp.TGL_KIRIM_PPF 
                            AND tb1.DISTRIK = tbpp.KODE_TUJUAN
                            AND tb1.BRAND = tbpp.BRAND
                        LEFT JOIN ZREPORT_M_KOTA zmk ON tb1.distrik = zmk.KD_KOTA 
                        LEFT JOIN MAINTENANCE_MAPPING_BRAND mmb ON 
                            tb1.DISTRIK = mmb.DISTRIK 
                            AND TO_CHAR(tb1.TANGGAL_TARGET, 'YYYY-MM') = TO_CHAR(mmb.PERIODE, 'YYYY-MM')
                            AND mmb.DELETE_MARK = '0'
                        LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                            tb1.DISTRIK = mmw.KODE_DISTRIK
                        LEFT JOIN
                            (
                            SELECT
                                DISTINCT
                                mms.BRAND,
                                mms.DISTRIK_RET,
                                mms.SEGMEN
                            FROM
                                MASTER_MAPPING_SEGMEN mms
                            JOIN (
                                SELECT
                                    BRAND,
                                    DISTRIK_RET,
                                    MAX(PERIODE) AS MAX_PERIODE
                                FROM
                                    MASTER_MAPPING_SEGMEN
                                WHERE
                                    DEL_MARK = '0'
                                GROUP BY
                                    BRAND,
                                    DISTRIK_RET
                                                ) latest
                                                ON
                                mms.BRAND = latest.BRAND
                                AND mms.DISTRIK_RET = latest.DISTRIK_RET
                                AND mms.PERIODE = latest.MAX_PERIODE
                            WHERE
                                mms.DEL_MARK = '0' ) mms1 ON
                            mmw.KODE_REGION IN ('3', '4', '5')
                            AND tb1.BRAND = mms1.BRAND
                            AND mmw.DISTRIK_RET = mms1.DISTRIK_RET
                        LEFT JOIN
                            (
                            SELECT
                                DISTINCT
                                mms.BRAND,
                                mms.KD_PROP,
                                mms.KD_DISTRIK,
                                mms.SEGMEN
                            FROM
                                MASTER_MAPPING_SEGMEN mms
                            JOIN (
                                SELECT
                                    BRAND,
                                    KD_PROP,
                                    KD_DISTRIK,
                                    MAX(PERIODE) AS MAX_PERIODE
                                FROM
                                    MASTER_MAPPING_SEGMEN
                                WHERE
                                    DEL_MARK = '0'
                                GROUP BY
                                    BRAND,
                                    KD_PROP,
                                    KD_DISTRIK
                                                        ) latest
                                                            ON
                                mms.BRAND = latest.BRAND
                                AND mms.KD_PROP = latest.KD_PROP
                                AND mms.KD_DISTRIK = latest.KD_DISTRIK
                                AND mms.PERIODE = latest.MAX_PERIODE
                            WHERE
                                mms.DEL_MARK = '0') mms2 ON
                            mmw.KODE_REGION IN ('1', '2', '6')
                            AND tb1.BRAND = mms2.BRAND
                            AND mmw.KODE_PROVINSI = mms2.KD_PROP
                            AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK
                        LEFT JOIN
                            (
                            SELECT
                                DISTINCT
                                mms.BRAND,
                                mms.KD_PROP,
                                mms.SEGMEN
                            FROM
                                MASTER_MAPPING_SEGMEN mms
                            JOIN (
                                SELECT
                                    BRAND,
                                    KD_PROP,
                                    MAX(PERIODE) AS MAX_PERIODE
                                FROM
                                    MASTER_MAPPING_SEGMEN
                                WHERE
                                    DEL_MARK = '0'
                                    AND KD_DISTRIK IS NULL
                                GROUP BY
                                    BRAND,
                                    KD_PROP,
                                    KD_DISTRIK
                                                        ) latest
                                                            ON
                                mms.BRAND = latest.BRAND
                                AND mms.KD_PROP = latest.KD_PROP
                                AND mms.PERIODE = latest.MAX_PERIODE
                            WHERE
                                mms.DEL_MARK = '0'
                                AND KD_DISTRIK IS NULL) mms3 ON
                            mmw.KODE_REGION IN ('1', '2', '6')
                            AND tb1.BRAND = mms3.BRAND
                            AND mmw.KODE_PROVINSI = mms3.KD_PROP
                        WHERE 
                            tb1.DEL_MARK = '0'
                            AND tb1.TIPE = '$tipecode' " . $filtertgl . "
                            $sqlfilter
                            $sqlakhir 
                            order by tb1.DISTRIBUTOR, tb1.DISTRIK, case WHEN STATUS_BRAND_NEW = 'MB' OR STATUS_BRAND_NEW = 'CB' THEN '1' else '2' end, tb1.BRAND ";
                //  echo '<pre>';echo $sql;
                $query = oci_parse($conn, $sql);
                oci_execute($query);
                unset($dataTa);
                while ($row = oci_fetch_array($query)) {
                    $dataTa[] = $row;
                }
                
                $total = count($dataTa);
                $komen = "Data tidak ditemukan ...!!";


                if ($total > 0) {
                    $RealTOrilis = getRilisPlant($org2t, $tipecode, $tglm, $tgls);

                    if ($org2t == '2000'  or $org2t == '5000' or $org2t == '7000') {
                        $org1 = 'ZSG1';
                    } elseif ($org2t == '3000') {
                        $org1 = 'ZSP1';
                    } elseif ($org2t == '6000') {
                        $org1 = 'ZTL1';
                    } else {
                        $org1 = 'ZST1';
                    }
                    $sql = "SELECT * FROM RFC_Z_ZCSD_DIST WHERE ZKTOKD = '" . $org1 . "'";
                    if ($sold_tof) {
                        $sql .= preg_match("/WHERE/i", $sql) ? " AND " : " WHERE ";
                        $sql .= "KUNNR = '" . $sold_tof . "'";
                    }
                    $query = oci_parse($conn, $sql);
                    oci_execute($query);
                    while ($datafunc = oci_fetch_assoc($query)) {
                        $dataDISTname[$datafunc["KUNNR"]] = $datafunc["NAME1"];
                    }

                    $sql = "SELECT 
                            tb1.ID, 
                            tb1.TIPE,
                            tb1.BRAND, 
                            tb1.STATUS_BRAND, 
                            tb1.TARGET, 
                            tb1.TANGGAL_TARGET, 
                            tb1.DISTRIK, 
                            tb1.DISTRIBUTOR, 
                            tb1.PERSENTASE, 
                            tb2.NAME1 AS distributor_name,
                            zmk.NM_KOTA,
                            coalesce(tbpp.QTY_PP,0) QTY_PP, 
                            coalesce(tbpp.QTY_APPROVE,0) QTY_APPROVE,
                            mmb.FLAGING AS FLAG_ON_OFF
                        FROM 
                            ZSD_TARGET_HARIAN_NEW_BRAND tb1
                        LEFT JOIN 
                            RFC_Z_ZCSD_DIST tb2 ON tb2.KUNNR = tb1.DISTRIBUTOR 
                        LEFT JOIN 
                            ZSD_BLACKLISTDIST tb3 ON tb3.SOLD_TO = tb1.DISTRIBUTOR 
                            AND tb3.AREA IN (
                                SELECT KD_AREA 
                                FROM ZREPORT_M_KOTA 
                                WHERE KD_KOTA = tb1.DISTRIK 
                                GROUP BY KD_AREA
                            ) 
                            AND tb3.FROM_AKTIF <= tb1.TANGGAL_TARGET 
                            AND tb3.TO_AKTIF >= tb1.TANGGAL_TARGET 
                            AND tb3.DELETE_MARK = 0 
                            AND tb3.STATUS = 'APPROVE'
                        LEFT JOIN (                            
                           SELECT coalesce(assoc.SOLDTO_ASSOC,SOLD_TO) SOLD_TO, BRAND, KODE_TUJUAN, TGL_KIRIM_PPF, SUM(QTY_PP_TON) AS QTY_PP, SUM(QTY_APPROVE_TON) AS QTY_APPROVE
                            FROM (
                                SELECT a.*
                                        ,(
                                            CASE								
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '4%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_PP * 40 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '5%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_PP * 50 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW = '2000.000'
                                                ) THEN a.QTY_PP * 2 
                                                ELSE a.QTY_PP 
                                            END
                                        ) AS QTY_PP_TON, 
                                        (
                                            CASE								
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '4%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_APPROVE * 40 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW LIKE '5%' 
                                                    AND WERKS = a.PLANT_ASAL
                                                ) THEN a.QTY_APPROVE * 50 / 1000 
                                                WHEN a.KODE_PRODUK IN (
                                                    SELECT MATNR 
                                                    FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                                                    WHERE NTGEW = '2000.000'
                                                ) THEN a.QTY_APPROVE * 2 
                                                ELSE a.QTY_APPROVE 
                                            END
                                        ) AS QTY_APPROVE_TON ,
                                    to_char(a.TGL_LEADTIME,'YYYY-MM') TGL_KIRIM_PPF,
                                    scm.BRAND
                                    FROM OR_TRANS_HDR_V a
                                    /*LEFT JOIN (
                                    SELECT scm.PERIODE, scm.PLANT, scm.BRAND, scm.MATERIAL, scm.DISTRIK, scm.INCOTERM, scm.TIPE_ORDER 
                                    FROM ZSD_TARGET_HEADER_SCM scm
                                    WHERE scm.FLAG_DEL != 'Y'
                                    AND scm.MATERIAL LIKE '121-301-7%'
                                    GROUP BY scm.PERIODE, scm.PLANT, scm.BRAND, scm.MATERIAL, scm.DISTRIK, scm.INCOTERM, scm.TIPE_ORDER 
                                    )scm ON a.KODE_PRODUK=scm.MATERIAL AND to_char(a.TGL_LEADTIME,'MM-YYYY')=scm.PERIODE
                                        AND a.KODE_TUJUAN=scm.DISTRIK AND a.INCOTERM=scm.INCOTERM 
                                        AND a.PLANT_ASAL=scm.PLANT
                                        AND ((a.SO_TYPE <> 'ZPR' AND scm.TIPE_ORDER != '1') OR (a.SO_TYPE='ZPR' AND scm.TIPE_ORDER='1'))*/
                                    LEFT JOIN MAPPING_MATERIAL_BRAND scm on scm.KODE_MATERIAL=a.KODE_PRODUK and scm.FLAG_DEL != 'Y'
                                    WHERE
                                    a.STATUS_LINE <> 'REJECTED'
                                    AND a.KODE_PRODUK like '121-301-7%'
                                    AND a.NO_SO_OLD IS NULL 
                                        AND (
                                        a.TIPEPP <> 'NEW PROYEK' 
                                        OR a.TIPEPP IS NULL 
                                        OR (
                                            a.TIPEPP = 'NEW PROYEK' 
                                            AND a.SO_TYPE = 'ZPR' 
                                            AND a.STATUS = 'APPROVE'
                                        )
                                    )                             
                            )a
                            LEFT JOIN MAPPING_SOLDTO_ASSOC assoc on assoc.FLAG_DEL != 'Y' and assoc.SOLDTO=a.SOLD_TO
                            GROUP BY coalesce(assoc.SOLDTO_ASSOC,SOLD_TO), BRAND, KODE_TUJUAN, TGL_KIRIM_PPF
                        ) tbpp ON tb1.DISTRIBUTOR = tbpp.SOLD_TO 
                            AND TO_CHAR(tb1.TANGGAL_TARGET, 'YYYY-MM') = tbpp.TGL_KIRIM_PPF 
                            AND tb1.DISTRIK = tbpp.KODE_TUJUAN
                            AND tb1.BRAND = tbpp.BRAND
                        LEFT JOIN ZREPORT_M_KOTA zmk ON tb1.distrik = zmk.KD_KOTA 
                        LEFT JOIN MAINTENANCE_MAPPING_BRAND mmb ON 
                            tb1.DISTRIK = mmb.DISTRIK 
                            AND TO_CHAR(tb1.TANGGAL_TARGET, 'YYYY-MM') = TO_CHAR(mmb.PERIODE, 'YYYY-MM')
                        WHERE 
                            tb1.DEL_MARK = '0'
                            AND tb1.TIPE = '$tipecode' " . $filtertgl . "
                            $sqlfilter
                            $sqlakhir 
                            order by tb1.DISTRIBUTOR, tb1.DISTRIK, case when tb1.STATUS_BRAND='MB' then '1' else '2' end, tb1.BRAND";

                    // echo '<pre>';echo $sql;exit;
                    $query = oci_parse($conn, $sql);
                    oci_execute($query);
                    unset($dataTaAll);
                    while ($row = oci_fetch_array($query)) {
                        $dataTaAll[] = $row;
                    }
                }
            }
        } else {
            $komen = 'Delivery Date should not be more than 30 days ...!';
        }
    } else {
        echo "<script>alert('Silahkan cek pencarian... ');</script>";
    }
}



$sqlbrands = "SELECT * FROM MASTER_BRAND WHERE DEL_MARK = 'X'";
$querybrands = oci_parse($conn, $sqlbrands);
oci_execute($querybrands);
unset($dataBrand);
while ($row = oci_fetch_array($querybrands)) {
    $dataBrand[] = $row;
}

$selectedBrand = isset($brandcode) ? $brandcode : '';
$optionsBrands = "";
foreach ($dataBrand as $brand) {
    $selected = ($brand['BRAND'] == $selectedBrand) ? 'selected' : '';
    $optionsBrands .= "<option value='" . $brand['BRAND'] . "' $selected>" . $brand['BRAND'] . "</option>";
}


$tgl1_value = !empty($tglm) ? date('d-m-Y', strtotime($tglm)) : $tgl_fr;
$tgl2_value = !empty($tgls) ? date('d-m-Y', strtotime($tgls)) : $tgl_to;


?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Daftar Target Distributor</title>
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <!-- import the calendar script -->
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <!-- import the language module -->
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <?
    if ($reportexel == '') {
    ?>
        <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
        <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
        <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
        <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
        <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
        <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <? } ?>
    <script src="../js/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.head').click(function(e) {
                e.preventDefault();
                $(this).closest('span').find('.content').slideToggle();
            });
        });
    </script>
    <style type="text/css">
        .head {
            display: block;
        }

        .content {
            display: none;
        }
    </style>
</head>
<style>
    .readonly {
        background-color: #E9ECEF !important;
    }

    .bg-white {
        background-color: #fff !important;
    }

    table.excel {
        border-style: ridge;
        border-width: 1;
        border-collapse: collapse;
        font-family: sans-serif;
        font-size: 12px;
    }

    table.excel thead th,
    table.excel tbody th {
        background: #CCCCCC;
        border-style: ridge;
        border-width: 1;
        text-align: center;
        vertical-align: bottom;
    }

    table.excel tbody th {
        text-align: center;
        width: 20px;
    }

    table.excel tbody td {
        vertical-align: bottom;
    }

    table.excel tbody td {
        padding: 0 3px;
        border: 1px solid #EEEEEE;
    }

    * {
        outline: 0 !important;
    }

    .custom-modal {
        position: fixed;
        overflow: auto;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgb(0 0 0 / 60%);
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        padding: 30px;
    }

    .custom-modal-dialog {
        max-width: 620px;
        width: 100%;
        border-radius: 0px;
        position: relative;
    }

    .custom-modal-content {
        background: #ffffff;
        padding: 30px 30px;
        border-radius: 10px;
    }

    .close-modal {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 30px;
        height: 30px;
        background: #f56142;
        opacity: 1;
        color: #ffffff;
        border-radius: 100%;
        border: 2px solid #ffffff;
        z-index: 9;
        box-shadow: 0px 0px 30px 0px rgb(0 0 0 / 8%);
        padding: 0;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
    }

    .custom-modal {
        opacity: 0;
        visibility: hidden;
    }

    body.modal-open .custom-modal {
        opacity: 1;
        visibility: visible;
    }

    .custom-modal .custom-modal-dialog {
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -ms-transform: scale(0);
        -o-transform: scale(0);
        transform: scale(0);
    }

    body.modal-open .custom-modal .custom-modal-dialog {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    }

    .custom-modal,
    body .custom-modal,
    body.modal-open .custom-modal .custom-modal-dialog,
    body .custom-modal .custom-modal-dialog {
        -webkit-transition: all 0.5s;
        -moz-transition: all 0.5s;
        -ms-transition: all 0.5s;
        -o-transition: all 0.5s;
        transition: all 0.5s;
    }

    @media (max-width: 575.98px) {}
</style>

<script>
    // Modal Edit
    $(document).ready(function() {
        function clearFormModal() {
            $('input[name="idEdit"]').val('');
            $('input[name="tipeEdit"]').val('');
            $('input[name="distributorEdit"]').val('');
            $('input[name="tanggalTargetEdit"]').val('');
            $('input[name="districtEdit"]').val('');
            $('input[name="kotaEdit"]').val('');
            $('input[name="targetEdit"]').val('');
            $('input[name="brandEdit"]').val('');
            $('input[name="statusBrandEdit"]').val('');
        }

        function generateContentEdit() {
            document.getElementById('generateContent').innerHTML = '';
            let htmlContent = `
                    <table width="100%" border="0" class="adminform" align="center">
                        <tr>
                            <td class="puso">Tipe</td>
                            <td class="puso">:</td>
                            <td>
                                <input id="idEdit" name="idEdit" type="hidden" value="">
                                <input type="text" class="readonly" value="" name="tipeEdit" readonly>
                            </td>

                        </tr>
                        <tr>
                            <td class="puso">Distributor</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="" name="distributorEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Tanggal</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="" name="tanggalTargetEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">District</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="" name="districtEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Kota/Provinsi</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="" name="kotaEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Brand</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="" name="brandEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Status Brand</td>
                            <td class="puso">:</td>
                            <td>
                                <select class="bg-white" name="statusBrandEdit">
                                    <option value="FB">FB</option>
                                    <option value="MB">MB</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Target</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="number" class="bg-white" value="" name="targetEdit">
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Create PP (TO)</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="0" name="createPPEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Approve SO (TO)</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="0" name="approveSOEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Realisasi (TO)</td>
                            <td class="puso">:</td>
                            <td>
                                <input type="text" class="readonly" value="0" name="realisasiEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Persen</td>
                            <td class="puso">:</td>
                            <td>
                                <input class="readonly" type="text" value="0" name="persenEdit" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Porsi</td>
                            <td class="puso">:</td>
                            <td>
                                <input class="bg-white" type="number" value="0" name="porsiEdit">
                            </td>
                        </tr>
                    </table>
                    <div style="margin-top: 20px; text-align:center;">
                        <input name="Edit" type="submit" class="button" id="Edit" value="Simpan" />
                    </div>
                `;
            document.getElementById('generateContent').innerHTML = htmlContent;
        }

        function generateContentDelete() {
            document.getElementById('generateContent').innerHTML = '';
            let html = `
                    <h3 style="text-align: center;">
                        Apakah anda yakin ingin menghapus data ? 
                    </h3>
                    <input id="idDelete" name="idDelete" type="hidden" value="">
                <div style="margin-top: 20px; text-align:center;">
                    <input name="Hapus" type="submit" class="button" id="Hapus" value="Hapus" />
                </div>
            `;
            document.getElementById('generateContent').innerHTML = html;
        }

        $('.modal-edit').on('click', function() {
            let rowId = $(this).data('id');
            let distrik=$(this).data('distrik');
            let distributor=$(this).data('distributor')
            let reqModal = $(this).data('modal-id');
            if (reqModal == 'edit') {
                generateContentEdit();
                $.ajax({
                    url: 'C_list_target_harian_new_brand1.php',
                    method: 'GET',
                    data: {
                        id: rowId,
                        distrik:distrik,
                        distributor:distributor
                    },
                    success: function(response) {
                        clearFormModal()
                        let data = JSON.parse(response);
                        console.log("OK", data);
                        $('input[name="idEdit"]').val(data.ID);
                        $('input[name="tipeEdit"]').val(data.TIPE);
                        $('input[name="distributorEdit"]').val(data.DISTRIBUTOR);
                        $('input[name="tanggalTargetEdit"]').val(data.TANGGAL_TARGET);
                        $('input[name="districtEdit"]').val(data.DISTRIK);
                        $('input[name="kotaEdit"]').val(data.NM_KOTA);
                        $('input[name="targetEdit"]').val(data.TARGET);
                        $('input[name="brandEdit"]').val(data.BRAND);
                        $('input[name="statusBrandEdit"]').val(data.STATUS_BRAND);
                        $('input[name="createPPEdit"]').val(data.QTY_APPROVE);
                        $('input[name="approveSOEdit"]').val(data.QTY_PP);
                        $('input[name="persenEdit"]').val(data.QTY_PP / data.TARGET);
                        $('input[name="porsiEdit"]').val(data.PERSENTASE);
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });

            } else {
                generateContentDelete();
                $('input[name="idDelete"]').val('');
                $('input[name="idDelete"]').val(rowId);
            }


            // $('#' + modalId).addClass('modal-open');
            $('body').addClass('modal-open');
        });

        // $('.modal-edit').on('click', function() {
        //     let rowId = $(this).data('id');
        //     console.log(rowId);
        //     $('body').addClass("modal-open");
        // });
        $('.close-modal').on('click', function() {
            $('body').removeClass("modal-open");
        });
    });

    var message = "You dont have permission to right click";

    function clickIE() {
        if (document.all) {
            (message);
            return
            false;
        }
    }

    function clickNS(e) {
        if (document.layers || (document.getElementById && !document.all)) {
            if (e.which == 2 || e.which == 3) {
                (message);
                return
                false;
            }
        }
    }

    if (document.layers) {
        document.captureEvents(Event.MOUSEDOWN);
        document.onmousedown = clickNS;
    } else {
        document.onmouseup = clickNS;
        document.oncontextmenu = clickIE;
    }

    document.oncontextmenu = new Function("return false")

    function getXMLHTTP() {
        var xmlhttp = false;
        try {
            xmlhttp = new XMLHttpRequest();
        } catch (e) {
            try {
                xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {
                try {
                    xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e1) {
                    xmlhttp = false;
                }
            }
        }
        return
        xmlhttp;
    }

    function
    popUp(URL) {
        day = new Date();
        id = day.getTime();
        eval("page" + id + " = window.open(URL, '" + id + "', 'toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=1,width=900,height=600,left = 34, top = 102 ');");
    }

    function findshipto() {
        var
            com_sold =
            document.getElementById('sold_to');
        var
            com_kode_distrik =
            document.getElementById('kode_distrik');
        if (com_sold.value !=
            '') {
            var strURL = "cari_shipto2.php?&sold_to=" + com_sold.value + "&nourut=1&aktif=loadkap";
            popUp(strURL);
        } else {
            alert("Sold To is not null ");
        }
    }

    function
    ketik_shipto(obj) {
        var com_sold = document.getElementById('sold_to');
        var com_kode_distrik = document.getElementById('kode_distrik');
        var strURL = "ketik_shiptoadm2.php?shipto=" + obj.value + "&sold_to=" + com_sold.value + "&nourut=1";
        var req = getXMLHTTP();
        if (com_sold.value != '') {
            if (req) {
                req.onreadystatechange = function() {
                    if (req.readyState == 4) {
                        // only if "OK"
                        if (req.status == 200) {
                            document.getElementById("shiptodiv").innerHTML = req.responseText;
                        } else {
                            alert("There was a problem while using XMLHTTP: \n " + req.statusText);
                        }
                    }
                }
                req.open("GET", strURL, true);
                req.send(null);
            }
        } else {
            alert("Sold To is not null ");
        }
    }

    function
    findplant() {
        var comorg = document.getElementById('org');
        var strURL = "cari_plant.php?org=" + comorg.value;
        popUp(strURL);
    }

    function
    ketik_plant(obj) {
        var com = document.getElementById('org');
        var nilai_tujuan = obj.value;
        var cplan = document.getElementById('nama_plant');
        cplan.value = "";
        var strURL = "ketik_plant.php?org=" + com.value + "&plant=" + nilai_tujuan;
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                if (req.readyState == 4) {
                    // only if "OK"
                    if (req.status == 200) {
                        document.getElementById('plantdiv').innerHTML = req.responseText;
                    } else {
                        alert("There was a problem while using XMLHTTP: \n " + req.statusText);
                    }
                }
            }
            req.open("GET", strURL, true);
            req.send(null);
        }
    }

    function
    finddistrik() {
        var strURL = "cari_bzirk.php";
        popUp(strURL);
    }

    function ketik_bzirk(obj) {
        var com_org = document.getElementById('org');
        var strURL = "ketik_bzirk.php?org=" + com_org.value + "&kotac=" + obj.value;
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                if (req.readyState == 4) {
                    // only if "OK"
                    if (req.status == 200) {
                        document.getElementById("kotadiv").innerHTML = req.responseText;
                    } else {
                        alert("There was a problem while using XMLHTTP: \n " + req.statusText);
                    }
                }
            }
            req.open("GET", strURL, true);
            req.send(null);
        }
    }

    function
    finddistr(org) {
        var com_org = document.getElementById('org');
        var strURL = "cari_distr.php?org=" + com_org.value;
        popUp(strURL);
    }

    function formreq(org, tipe, distributor, distrik, tanggal, plant, id) {
        var strURL = "formreqjatah.php?org=" + org + "&tipe=" + tipe + "&distributor=" + distributor + "&distrik=" + distrik +
            "&tanggal=" +
            tanggal +
            "&plant=" +
            plant +
            "&r=" +
            id;
        popUp(strURL);
    }

    function formhistory(org, tipe, distributor, distrik, tanggal, plant, id) {
        var strURL = "formhistory.php?org=" + org + "&tipe=" + tipe + "&distributor=" + distributor + "&distrik=" + distrik + "&tanggal=" + tanggal + "&plant=" + plant + "&r=" + id;
        popUp(strURL);
    }

    function ketik_distr(obj) {
        var com_org = document.getElementById('org');
        var strURL = "ketik_distr.php?org=" + com_org.value + "&distr=" + obj.value;
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                if (req.readyState == 4) {
                    // only if "OK"
                    if (req.status == 200) {
                        document.getElementById("distrdiv").innerHTML = req.responseText;
                    } else {
                        alert("There was a problem while using XMLHTTP: \n " + req.statusText);
                    }
                }
            }
            req.open("GET", strURL, true);
            req.send(null);
        }
    }

    function IsNumeric(obj) // check for valid numeric strings 
    {
        var strValidChars = "0123456789";
        var strChar;
        var strString = obj.value;
        if (strString.length == 0) {
            alert("Harus Diisi Angka..!!!");
            obj.value = "";
            return false;
        } else { // test strString consists of valid characters listed above
            for (i = 0; i < strString.length; i++) {
                strChar = strString.charAt(i);
                if (strValidChars.indexOf(strChar) == -1) {
                    alert("Hanya Masukkan Angka 0-9...!");
                    return false;
                }
            }
        }
    }

    function IsNumeric2(obj, volIndex) // check for valid numeric strings 
    {
        var strValidChars = "0123456789.";
        var strChar;
        var strString = obj.value;
        var valVolIndex = volIndex;
        //alert ("dsda "+valVolIndex);
        if (strString.length == 0) {
            alert(" Harus Diisi Angka..!!!");
            return false;
            // } else if (strString> valVolIndex){
            // alert("Adjusmant tidak boleh lebih dari Indexnya ..!!!");
            // return false;
        } else { // test strString consists of valid characters listed above
            for (i = 0; i < strString.length; i++) {
                strChar = strString.charAt(i);
                if (strValidChars.indexOf(strChar) == -1) {
                    alert("Hanya Masukkan Angka 0-9...!");
                    return false;
                }
            }
        }
    }


    function cek_datatambah() {
        if (validasi('org', '', 'R')) {
            document.hasil = true;
            return true;
        } else {
            document.hasil = false;
            return false;
        }
    }

    function showhidekota() {
        var tipe = $("#tipe").val();
        if (tipe == '121-302') {
            $("#contentkota").hide();
            $("#kode_distrik").val('');
            $("#nama_distrik").val('');
        } else {
            $("#contentkota").show();
        }
    }
</script>

<body>
    <? if ($reportexel == '') { ?>
        <div align="center">
            <table width="800" align="center" class="adminheading" border="0">
                <tr>
                    <th class="da2">Daftar Target Distributor</th>
                </tr>
            </table>
        </div>

        <div align="center">
            <table width="700" align="center" class="adminlist">
                <tr>
                    <th align="left" colspan="3">
                        <span class="style5">&nbsp;Form Search
                        </span>
                    </th>
                </tr>
            </table>
        </div>
        <form id="fsimpan" name="fsimpan" method="post" action="<?= $importtargetVolume; ?>" onSubmit="cek_datatambah();return document.hasil">
            <table width="700" border="0" class="adminform" align="center">
                <tr>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td class="ThemeOfficeMenu" colspan="2">&nbsp;</td>
                </tr>
                <tr>
                    <td class="puso">Tipe</td>
                    <td class="puso">:</td>
                    <td>
                        <SELECT NAME="tipe" id="tipe" onChange="showhidekota();">
                            <option value="121-301BULANAN" <? if ($_POST[tipe] == '121-301BULANAN') echo ' selected' ?>>&nbsp;Zak Bulanan&nbsp;</option>
                            <option value="121-302" <? if ($_POST[tipe] == '121-302') echo ' selected' ?>>&nbsp;Curah&nbsp;</option>
                            <option value="121-701" <? if ($_POST[tipe] == '121-701') echo ' selected' ?>>&nbsp;Mortar Zak&nbsp;</option>
                            <option value="121-701BULANAN" <? if ($_POST[tipe] == '121-701BULANAN') echo ' selected' ?>>&nbsp;Mortar Zak Bulanan&nbsp;</option>
                        </SELECT>
                    </td>
                </tr>
                <tr>
                    <td class="puso">Tanggal Target</td>
                    <td class="puso">:</td>
                    <td>
                        <input name="tgl1" type="text" id="tgl1" size=12 value="<?= $tgl1_value ?>" onClick="return showCalendar('tgl1');" onblur="validateDate();" />&nbsp; s.d &nbsp;
                        <input name="tgl2" type="text" id="tgl2" size=12 value="<?= $tgl2_value ?>" onClick="return showCalendar('tgl2');" onblur="validateDate();" />&nbsp;
                        <input type="hidden" id="tgl3" value="<?= date('Ymd'); ?>" />
                    </td>
                </tr>
                <? if ($distr == '') { ?>
                    <tr>
                        <td class="puso">Distributor <?= $user_org ?> </td>
                        <td class="puso">:</td>
                        <td><input name="org" type="hidden" id="org" value="<?= $user_org ?>" />
                            <div id="distrdiv">
                                <input name="sold_to" id="sold_to" type="text" class="inputlabel" size="10" maxlength="10" value="<?= $sold_tof ?>" onChange="ketik_distr(this)" /> &nbsp;&nbsp;&nbsp;&nbsp;
                                <input name="nama_sold_to" id="nama_sold_to" class="inputlabel" type="text" size="30" value="<?= $nama_sold_to ?>" /> &nbsp;&nbsp;&nbsp;&nbsp;
                                <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()" />
                            </div>
                        </td>
                    </tr>
                <? } else { ?>
                    <tr>
                        <td class="puso">Distributor </td>
                        <td class="puso">:</td>
                        <td><input name="org" type="hidden" id="org" value="<?= $user_org ?>" />
                            <div id="distrdiv">
                                <input name="sold_to" id="sold_to" type="text" class="inputlabel" size="10" maxlength="10" value="<?= $distr; ?>" /> &nbsp;&nbsp;&nbsp;&nbsp;
                                <input name="nama_sold_to" id="nama_sold_to" class="inputlabel" type="text" size="30" value="<?= $namauser; ?>" /> &nbsp;&nbsp;&nbsp;&nbsp;
                    </tr>
                <? } ?>
                <tr id="contentkota" <? if ($_POST[tipe] == '121-302') echo 'style="display:none;"'; ?>>
                    <td class="puso">Kota </td>
                    <td class="puso">:</td>
                    <td>
                        <div id="kotadiv">
                            <input type="text" class="inputlabel" id="kode_distrik" name="kode_distrik" value="<?= $kode_distrikf ?>" onchange="ketik_bzirk(this)" size="10">
                            <input type="text" class="inputlabel" id="nama_distrik" name="nama_distrik" value="<?= $nama_distrik ?>" size="20" readonly="true">
                            <input name="btn_distrik" type="button" class="button" id="btn_distrik" value="..." onClick="finddistrik()" />
                            <input name="val_error_distrik" type="hidden" id="val_error_distrik" value="0" />
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><strong>Brand</strong></td>
                    <td width="10"><strong>:</strong></td>
                    <td width="153" colspan="2">
                        <SELECT NAME="brand" id="brand">
                            <option value="">All</option>
                            <?php echo $optionsBrands; ?>
                        </SELECT>
                    </td>
                </tr>
                <tr>
                    <td class="puso">&nbsp;</td>
                    <td class="puso">&nbsp;</td>
                    <td>
                        <input type="checkbox" name="reportexel" value="report">Report ke Excel
                    </td>
                </tr>
                <tr>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td class="ThemeOfficeMenu" colspan="2">
                        <input name="Tambah" type="submit" class="button" id="Tambah" value="Cari" />
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td colspan="2">&nbsp;</td>
                </tr>
            </table>
        </form>
    <? } ?>
    <br><br>
    <?
    if ($messData) {
    ?>
        <table align="center" class="adminform" width="600">
            <tr align="center">
                <td class="puso" align="center">
                    <? echo "<br>" . $messData . "<br>"; ?>
                </td>
            </tr>
        </table>
    <? } ?>
    <p>&nbsp;</p>
    <div align="center">
        <table width="1024" align="center" class="adminlist">
            <tr>
                <th align="left" colspan="16">
                    <span class="style5">&nbsp;Tabel Target Harian Distributor</span>
                </th>
            </tr>
        </table>
    </div>
    <div align="center">
        <table id="test1" width="1024" align="center" <? if ($reportexel != '') {
                                                            echo ' border="1" ';
                                                        } ?>class="adminlist">
            <thead>
                <tr class="quote">
                    <td width="3">
                        <div align="center"><strong>No.</strong></div>
                    </td>
                    <td width="20" align="center"><strong>Kode Distributor</strong></td>
                    <td width="50" align="center"><strong>Nama Distributor</strong></td>
                    <td width="50" align="center"><strong>Tanggal Target</strong></td>
                    <td width="50" align="center"><strong>Kota / Provinsi</strong></td>
                    <td width="70" align="center"><strong>Nama Kota / Provinsi</strong></td>
                    <td width="20" align="center"><strong>Brand</strong></td>
                    <td width="10" align="center"><strong>Status Brand</strong></td>
                    <td width="10" align="center"><strong>Kuota Brand/<br>Sisa Target</strong></td>
                    <td width="10" align="center"><strong>Target (TO)</strong></td>
                    <td width="10" align="center"><strong>Create PP (TO)</strong></td>
                    <td width="10" align="center"><strong>Approve SO (TO)</strong></td>
                    <td width="10" align="center"><strong>Realisasi (TO)</strong></td>
                    <td width="1" align="center"><strong>Persen</strong></td>
                    <td width="1" align="center"><strong>Porsi</strong></td>
                    <td width="1" align="center"><strong>Action</strong></td>
                    <? if ($distr != '') { ?>
                        <td width="1" align="center"><strong>Action</strong></td>
                    <? } ?>
                    <td width="5" align="center"><strong>ON/OFF</strong></td>
                    <td width="10" align="center"><strong>Kuota Brand (Distributor)</strong></td>
                </tr>
            </thead>
            <tbody>
                <?
                if ($total > 0) {
                    $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE DELETE_MARK = 0";
                    $sqlbrand = "select BRAND FROM MASTER_BRAND WHERE DEL_MARK = 'X'";
                    $qbrand = oci_parse($conn, $sqlbrand);
                    oci_execute($qbrand);
                    $arrbrand = array();
                    while ($rbrand = oci_fetch_assoc($qbrand)) {
                        array_push($arrbrand, $rbrand['BRAND']);
                    }
                ?>
                    <? 
                        $curr_distributor = '';
                        $curr_distrik = '';
                        $curr_real_mb = 0;
                        $curr_target_mb = 0;
                    ?>
                    <? for ($i = 0; $i < $total; $i++) {
                        $rowke = "rowke" . $i;
                        if (($i % 2) == 0) {
                            echo "<tr class='row0' id='$rowke' >";
                        } else {
                            echo "<tr class='row1' id='$rowke' >";
                        }
                        $no = $i + 1;
                        $kei = $i;
                        $urutke = "urutke" . $i;

                        $comget = $dataTa[$i]['ORG'];
                        $tipeget = $dataTa[$i]['TIPE'];
                        $distget = $dataTa[$i]['DISTRIBUTOR'];
                        $kotaget = $dataTa[$i]['DISTRIK'];
                        if($distget != $curr_distributor || $kotaget != $curr_distrik){
                            $curr_distributor = $distget;
                            $curr_distrik = $kotaget;
                            $curr_real_mb = 0;
                            $curr_target_mb = 0;
                        }
                        if ($tipeget == '121-302') {
                            $kotaget = '10' . substr($kotaget, 0, 2);
                        }
                        $brand = $dataTa[$i]['BRAND'];
                        $kirimget = $dataTa[$i]['TANGGAL_TARGET'];
                        if($dataTa[$i]['STATUS_BRAND_NEW'] == 'MB' || $dataTa[$i]['STATUS_BRAND_NEW'] == 'CB'){
                            $sisa_brand = $dataTa[$i]['TARGET'] - $dataTa[$i]['QTY_PP'];
                            $curr_real_mb += $dataTa[$i]['QTY_PP'];
                            $curr_target_mb += $dataTa[$i]['TARGET'];
                        }
                        else{
                            $sisa_brand = $curr_target_mb > 0 ? round(($curr_real_mb / $curr_target_mb) * $dataTa[$i]['TARGET'], 2) - $dataTa[$i]['QTY_PP'] : ($dataTa[$i]['TARGET'] - $dataTa[$i]['QTY_PP']);
                        }

                    ?>
                        <td align="center"><? echo $no . "."; ?></td>
                        <!--        <td align="center"><?= $comget; ?></td>-->
                        <!--        <td align="center"><?= $tipeget; ?></td>-->
                        <!-- <td align="center"><?= number_format($distget); ?></td> -->
                        <!-- <td align="center"><?= $dataDISTname[$distget]; ?></td> -->
                        <td align="center"><?= $dataTa[$i]['DISTRIBUTOR']; ?></td>
                        <td align="center"><?= $dataTa[$i]['DISTRIBUTOR_NAME']; ?></td>
                        <td align="center"><?= $dataTa[$i]['TANGGAL_TARGET']; ?></td>
                        <td align="center"><?= $kotaget; ?></td>
                        <td align="center"><?= $dataTa[$i]['NM_KOTA']; ?></td>
                        <td align="center"><?= $brand; ?></td>
                        <td align="center"><?= $dataTa[$i]['STATUS_BRAND_NEW']; ?></td>
                        <td align="center"><?= number_format($sisa_brand, 0, ",", "."); ?></td>
                        <?
                        $porsi = $dataTa[$i]['PERSENTASE'];
                        $targetHDist = $dataTa[$i]['TARGET'];
                        $realHDist = $dataTa[$i]['QTY_PP'];
                        // $realHDistlelang=$dataTa[$i]['PP_OPENPP'];
                        $realDFHDist = $dataTa[$i]['QTY_APPROVE'];
                        $keyAA = $comget . $tipeget . $kirimget . $brand . $distget . $kotaget;
                        $realplant = $RealTOrilis[$keyAA];
                        // var_dump($realplant);
                        $persenDist = @($realHDist / $targetHDist) * 100;
                        $nColorB = getColorProce($persenDist);
                        $btnreq = '';
                        $ts1 = strtotime(date('Y-m-d'));
                        if ($tipecode == '121-301' && in_array($plantsetget, $arrbrand)) {
                            $ts2 = strtotime($dataTa[$i]['TANGGAL_TARGET']);
                        } else {
                            $ts2 = strtotime(date('Y-m-31', strtotime($dataTa[$i]['TANGGAL_TARGET'])));
                        }
                        $diff = $ts2 - $ts1;
                        $diff = $diff / 86400; //86400 adalah second sehari
                        // var_dump($diff);
                        if ($distr != '') {
                            $seconds_diff = $ts2 - $ts1;
                            if (@$dataTa[$i]['STATUS'] || $dataTa[$i]['STATUS'] != '0') {
                                if ($diff >= 0) {
                                    $btnreq = '<input name="req" type="submit" class="button" id="req' . $i . '" value="Request Tambah Jatah" onClick="formreq(\'' . $dataTa[$i]['ORG'] . '\',\'' . $dataTa[$i]['TIPE'] . '\',\'' . $dataTa[$i]['DISTRIBUTOR'] . '\',\'' . $dataTa[$i]['DISTRIK'] . '\',\'' . $dataTa[$i]['TANGGAL_TARGET'] . '\',\'' . $dataTa[$i]['PLANTSET'] . '\',\'' . $i . '\')" />';
                                } else {
                                    $btnreq = '';
                                }
                            } else if (@$dataTa[$i]['STATUS_REQUEST'] == 0) {
                                $btnreq = 'Menunggu Approval';
                            }
                        }
                        ?>
                        <td align="right"><?= showNilaiQTY($targetHDist - $targetappHDist); ?></td>
                        <td align="right"><?= showNilaiQTY($realHDist); ?></td>
                        <td align="right"><?= showNilaiQTY($realDFHDist); ?></td>
                        <td align="right"><?= showNilaiQTY($realplant); ?></td>
                        <td align="center" bgcolor="<?= $nColorB; ?>"><?= showProce($persenDist); ?></td>
                        <td align="right"><?= showNilaiQTY($dataTa[$i]['PERSENTASE']); ?></td>
                        <? if ($distr != '') { ?>
                            <td align="center" id="row<?= $i; ?>"><?= $btnreq . '<input name="history" type="submit" class="button" id="history' . $i . '" value="History" onClick="formhistory(\'' . $dataTa[$i]['ORG'] . '\',\'' . $dataTa[$i]['TIPE'] . '\',\'' . $dataTa[$i]['DISTRIBUTOR'] . '\',\'' . $dataTa[$i]['DISTRIK'] . '\',\'' . $dataTa[$i]['TANGGAL_TARGET'] . '\',\'' . $dataTa[$i]['PLANTSET'] . '\',\'' . $i . '\')" />'; ?></td>
                        <? } ?>
                        </td>
                        <td>
                            <a href="javascript:void(0)" class="modal-edit" data-modal-id="edit" data-id="<?= $dataTa[$i]['ID'] ?>" data-distributor="<?= $dataTa[$i]['DISTRIBUTOR'];?>" data-distrik="<?= $kotaget; ?>">
                                Edit
                            </a>
                            <!-- <a href="javascript:void(0)"  style="margin-left: 2px;" class="modal-edit" data-modal-id="delete" data-id="<?= $dataTa[$i]['ID'] ?>">
                                Hapus
                            </a> -->
                        </td>
                        <td align="center"><?= $dataTa[$i]['FLAG_ON_OFF'] == '1' ? "OFF" : "ON" ; ?></td>
                        <td align="right"><?= $dataTa[$i]['FLAG_ON_OFF'] == '1' && ($dataTa[$i]['STATUS_BRAND_NEW'] == "FB" || $dataTa[$i]['STATUS_BRAND_NEW'] == "AB") ? number_format(($targetHDist - $targetappHDist) - $realHDist, 0, ",", ".") : number_format($sisa_brand, 0, ",", "."); ?></td>

                        </tr>
                    <?
                    }
                } else {
                    ?>
                    <tr class="row1">
                        <td align="center" colspan="16"><? echo $komen; ?></td>
                    </tr>
                <?
                }
                if (($total > 0) && ($reportexel == '')) { ?>
                    <tr class="quote">
                        <td colspan="18" align="center">&nbsp;</td>
                    </tr>
                <? } ?>
            </tbody>
        </table>
    </div>
    <? if ($distr == '') { ?>
        <br>
        <span>
            <a href='#' class='head'>
                <div align="center">
                    <table width="800" align="center" class="adminlist">
                        <tr>
                            <th align="left" colspan="11">
                                <span class="style5">&nbsp;Target Harian All</span>
                            </th>
                        </tr>
                    </table>
                </div>
            </a>
            <div class='content'>
                <div align="center">
                    <table id="test1" width="800" align="center" <? if ($reportexel != '') {
                                                                        echo ' border="1" ';
                                                                    } ?>class="adminlist">
                        <thead>
                            <tr class="quote">
                                <td width="3">
                                    <div align="center"><strong>No.</strong></div>
                                </td>
                                <!--            <td width="3" align="center"><strong>Org</strong></td>-->
                                <!--            <td width="5" align="center"><strong>Tipe</strong></td>-->
                                <td width="10" align="center"><strong>Kode Kota</strong></td>
                                <td width="40" align="center"><strong>Kota</strong></td>
                                <!-- <td width="10" align="center"><strong>Plant</strong></td> -->
                                <td width="40" align="center"><strong>Brand</strong></td>
                                <td width="20" align="center"><strong>Tanggal</strong></td>
                                <td width="6" align="center"><strong>Target (TO)</strong></td>
                                <td width="6" align="center"><strong>Target Request Approve (TO)</strong></td>
                                <td width="6" align="center"><strong>Total PP (TO)</strong></td>
                                <td width="1" align="center"><strong>Persen</strong></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?

                            if ($total > 0) {
                                $i = 0;
                                foreach ($dataTaAll as $key => $valueA) {
                                    $rowke = "rowke" . $i;
                                    if (($i % 2) == 0) {
                                        echo "<tr class='row0' id='$rowke' >";
                                    } else {
                                        echo "<tr class='row1' id='$rowke' >";
                                    }
                                    $no = $i + 1;
                                    $kei = $i;
                                    $urutke = "urutke" . $i;
                            ?>
                                    <td align="center"><? echo $no . "."; ?></td>
                                    <td align="center"><?= $valueA['DISTRIK']; ?></td>
                                    <td align="center"><?= $valueA['NM_KOTA']; ?></td>
                                    <td align="center"><?= $valueA['BRAND']; ?></td>
                                    <!-- <td align="left"><?= $valueA['NAME']; ?></td> -->
                                    <td align="center"><?= $valueA['TANGGAL_TARGET']; ?></td>
                                    <?
                                    $targetH = $valueA['TARGET'];
                                    $targetappH = $valueA['TARGET_APPROVED'];
                                    $realH = $valueA['REAL'];
                                    $persen = @($realH / $targetH) * 100;
                                    $nColorB = getColorProce($persen);
                                    ?>
                                    <td align="right"><?= showNilaiQTY($targetH); ?></td>
                                    <td align="right"><?= showNilaiQTY($targetappH); ?></td>
                                    <td align="right"><?= showNilaiQTY($realH); ?></td>
                                    <td align="center" bgcolor="<?= $nColorB; ?>"><?= showProce($persen); ?></td>
                                    </tr>
                                <?
                                    $i++;
                                }
                            }
                            if (($total > 0) && ($reportexel == '')) { ?>
                                <tr class="quote">
                                    <td colspan="11" align="center">&nbsp;</td>
                                </tr>
                            <? } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </span>
    <? } ?>
    <?
    if ($reportexel == '') {
        include('../include/ekor.php');
    }
    ?>

    <!-- Modal -->
    <div id="editModal" class="custom-modal">
        <div class="custom-modal-dialog">
            <div class="custom-modal-content">
                <span class="close-modal">X</span>
                <div class="custom-modal-body">
                    <div class="custom-modal-inner">
                        <!-- Contetn here -->
                        <form method="POST" action="<?= $importtargetVolume; ?>">
                            <div id="generateContent">

                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript">
        function validateDate() {
            let startDateStr = document.getElementById('tgl1').value;
            let endDateStr = document.getElementById('tgl2').value;

            let startDateParts = startDateStr.split('-');
            let endDateParts = endDateStr.split('-');

            let startDate = new Date(startDateParts[2], startDateParts[1] - 1, startDateParts[0]);
            let endDate = new Date(endDateParts[2], endDateParts[1] - 1, endDateParts[0]);

            if (endDate < startDate) {
                alert("Tanggal akhir tidak boleh lebih kecil dari tanggal awal.");
                return false;
            }
            return true;
        }
    </script>
</body>

</html>
