<?php
require_once ('pgr_sanitizer.php');

function sanitize_global_input(){
    if (!empty($_REQUEST)) {
        $_REQUEST = sanitize_input($_REQUEST, TRUE);
    }   

    if (!empty($_POST)) {
        $_POST= sanitize_input($_POST, TRUE);
    }

    if (!empty($_GET)) {
        $_GET= sanitize_input($_GET, TRUE);
    }

    if (!empty($_COOKIE)) {
        $_COOKIE= sanitize_input($_COOKIE, TRUE);
    }
}

