<?php

require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

session_start();
include('../include/or_fungsi.php');
include('../include/validasi.php');
include('../include/API.php');
$fungsi = new or_fungsi();
$conn = $fungsi->or_koneksi();

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->getmainhalam_id($conn, $dirr);
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];

$mp_coics = $fungsi->getComin($conn, $user_org);
if (count($mp_coics) > 0) {
    unset($inorg);
    $orgcounter = 0;
    foreach ($mp_coics as $keyOrg => $valorgm) {
        $inorg .= "'" . $keyOrg . "',";
        $orgcounter++;
    }
    $inorg = rtrim($inorg, ',');
} else {
    $inorg = $user_org;
}
// if ($fungsi->keamanan($halaman_id, $user_id) == 0) {
if (false) {
?>
    <SCRIPT LANGUAGE="JavaScript">
        <!--
        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
        //
        -->
    </SCRIPT>

    <a href="../index.php">Login....</a>
<?php

    exit();
}

if (isset($_POST['download_invoice_ar'])) {
    $bastp = $_POST['BASTP'];
    $pemilik_brand = $_POST['PEMILIK_BRAND'];

    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }

    $fce = $sap->NewFunction("ZCFM_DISP_BASTB_FILE_MDR");
    // echo "mantap";
    // exit;
    if ($fce == false) {
        $sap->PrintStatus();
        exit;
    }

    $fce->I_BUKRS = $bastp;
    $fce->I_BASTP = $pemilik_brand;
    $fce->I_DISP = 'X';


    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_BASTBF->Reset();
        while ($fce->T_BASTBF->Next()) {
            $list_file[] = $fce->T_BASTBF->row;
        }
    }

    $total = count($list_file);
    echo json_encode($list_file);
    // if ($total < 1) {
    //     echo json_encode("Tidak Ada Data Yang Ditemukan");
    // } else {
    //     echo json_encode($list_file);
    // }
    exit;
}

$page = "list_bamsa_ar.php";
$total = 69-69;
$tahun = isset($_POST['TAHUN']) ? $_POST['TAHUN'] : '';
$sales_org = isset($_POST['SALES_ORG']) ? $_POST['SALES_ORG'] : '';
$document_number = isset($_POST['DOCUMENT_NUMBER']) ? $_POST['DOCUMENT_NUMBER'] : '';
$bastb = isset($_POST['BASTB']) ? $_POST['BASTB'] : '';
$brand = isset($_POST['BRAND']) ? $_POST['BRAND'] : '';
$tgl_create_from = isset($_POST['TGL_CREATE_BASTB_FROM']) ? $_POST['TGL_CREATE_BASTB_FROM'] : '';
$tgl_create_to = isset($_POST['TGL_CREATE_BASTB_TO']) ? $_POST['TGL_CREATE_BASTB_TO'] : '';

$param['I_TRANSACTION_TYPE'] = "R";


$currentPage = "list_bamsa_ar.php";
if (isset($_POST['cari'])) {
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }

    $fce = $sap->NewFunction("ZDIS_INT_MON_BASTB");
    // echo "mantap";
    if ($fce == false) {
        $sap->PrintStatus();
        exit;
    }

    // $fce->COMPANY_CODE = $user_org;
    $fce->COMPANY_CODE = '7900';
    $fce->TAHUN = $tahun;
    // $fce->VENDOR = sprintf("%010s", $vendor);
    $fce->SALES_ORG = $sales_org;
    // $fce->SALES_ORG = "3000";
    $fce->DOCUMENT_NUMBER = $document_number;
    $fce->BASTB = $bastb;
    $fce->BRAND = $brand;
    $fce->I_TRANSACTION_TYPE = 'R';

    $TGL_CREATE_BASTB_FROM = !empty($tgl_create_from) ? strval(date('Ymd', strtotime($tgl_create_from))) : "";
    $fce->TGL_CREATE_BASTB_FROM = $TGL_CREATE_BASTB_FROM;

    $TGL_CREATE_BASTB_TO = !empty($tgl_create_to) ? strval(date('Ymd', strtotime($tgl_create_to))) : "";
    $fce->TGL_CREATE_BASTB_TO = $TGL_CREATE_BASTB_TO;

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_DATA->Reset();
        while ($fce->T_DATA->Next()) {
            $list_bastp[] = $fce->T_DATA->row;
        }
    }

    $fce->Close();

    $fce = $sap->NewFunction("ZDIS_INT_MON_BASTB");
    // echo "mantap";
    if ($fce == false) {
        $sap->PrintStatus();
        exit;
    }
    
    $fce->COMPANY_CODE = '7000';
    $fce->TAHUN = $tahun;
    // $fce->VENDOR = sprintf("%010s", $vendor);
    $fce->SALES_ORG = '7000';
    // $fce->SALES_ORG = "3000";
    $fce->DOCUMENT_NUMBER = $document_number;
    $fce->BASTB = $bastb;
    $fce->BRAND = $brand;
    $fce->I_TRANSACTION_TYPE = 'R';

    $TGL_CREATE_BASTB_FROM = !empty($tgl_create_from) ? strval(date('Ymd', strtotime($tgl_create_from))) : "";
    $fce->TGL_CREATE_BASTB_FROM = $TGL_CREATE_BASTB_FROM;

    $TGL_CREATE_BASTB_TO = !empty($tgl_create_to) ? strval(date('Ymd', strtotime($tgl_create_to))) : "";
    $fce->TGL_CREATE_BASTB_TO = $TGL_CREATE_BASTB_TO;

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_DATA->Reset();
        while ($fce->T_DATA->Next()) {
            $list_bastp[] = $fce->T_DATA->row;
        }
    }

    $fce->Close();
    $sap->Close();
    // echo '<br>7000:';
    // print_r($fce);

    // var_dump($list_bastp);
    // die();

    // var_dump($list_bastp);
    // die();
    $total = count($list_bastp);
    if ($total < 1) $komen = "Tidak Ada Data Yang Ditemukan";
}
?>
<script language=javascript>
    var message = "You dont have permission to right click";

    function clickIE() {
        if (document.all) {
            (message);
            return false;
        }
    }

    function clickNS(e) {
        if (document.layers || (document.getElementById && !document.all)) {
            if (e.which == 2 || e.which == 3) {
                (message);
                return false;
            }
        }
    }

    if (document.layers) {
        document.captureEvents(Event.MOUSEDOWN);
        document.onmousedown = clickNS;
    } else {
        document.onmouseup = clickNS;
        document.oncontextmenu = clickIE;
    }

    document.oncontextmenu = new Function("return false")

    function getXMLHTTP() {
        var xmlhttp = false;
        try {
            xmlhttp = new XMLHttpRequest();
        } catch (e) {
            try {
                xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {
                try {
                    xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e1) {
                    xmlhttp = false;
                }
            }
        }
        return xmlhttp;
    }
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Aplikasi SGG Online: List Data BA MSA</title>
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <!-- import the calendar script -->
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <!-- import the language module -->
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script type="text/javascript" src="../include/jquery.min.js"></script>

    <style>
        .animate-top {
            position: relative;
            animation: animatetop 0.4s
        }

        @keyframes animatetop {
            from {
                top: -300px;
                opacity: 0
            }

            to {
                top: 0;
                opacity: 1
            }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.275);
        }

        .modal-content {
            margin: 5% auto;
            width: 500px;
            max-width: 90%;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.175);
            border-radius: .3rem;
            outline: 0;
        }

        .modal-header {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            padding: 5px 1rem 5px 1rem;
            border-bottom: 1px solid #e9ecef;
            border-top-left-radius: .3rem;
            border-top-right-radius: .3rem;
        }

        .modal-title {
            margin-bottom: 0;
            line-height: 1.5;
            margin-top: 0;
            font-size: 1.25rem;
        }

        .modal-header .close {
            float: right;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: .5;
            padding: 1rem;
            margin: -1rem -1rem -1rem auto;
            background-color: transparent;
            border: 0;
        }

        .close:not(:disabled):not(.disabled) {
            cursor: pointer;
        }

        .modal-body {
            flex: 1 1 auto;
            padding: 1rem;
        }

        .modal-body p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .modal-footer {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            justify-content: flex-end;
            padding: 5px 1rem 5px 1rem;
            border-top: 1px solid #e9ecef;
        }

        .modal-footer>* {
            margin: 5px;
        }

        /* buttons */
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            border: 1px solid transparent;
            padding: 5px 10px;
            cursor: pointer;
            font-size: inherit;
        }

        .btn:focus,
        .btn:hover {
            text-decoration: none;
        }

        .btn-primary {
            color: #fff;
            background-color: #da3e24;
            border-color: #da3e24;
        }

        .btn-primary:hover {
            color: #fff;
            background-color: #d33a21;
            border-color: #d33a21;
        }

        .btn-secondary {
            color: #fff;
            background-color: #7c8287;
            border-color: #7c8287;
        }

        .btn-secondary:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .readonly {
            border: solid 2px #33333312;
            background: #f6f6f6;
        }

        button.disabled {
            color: #a7a7a7;
        }

        .btn-success {
            margin-top: 30px;
            background: green;
            color: #fff;
            padding: 10px 20px;
            margin-bottom: 15px;
        }
    </style>
</head>
<script>
    function detailBASTB(bukrs, bastb) {
        var strURL = "invoice_royalty.php?bukrs=" + bukrs + "&bastb=" + bastb;
        popUp(strURL);
    }
</script>

<body>
    <div align="center">
        <table width="600" align="center" class="adminheading" border="0">
            <tr>
                <th class="kb2">Daftar BA MSA AR</th>
            </tr>
        </table>
    </div>
    <?
    if ($total < 1) {
    ?>

        <div align="center">
            <table width="600" align="center" class="adminlist">
                <tr>
                    <th align="left" colspan="4"> &nbsp;Form SearchPP / SO </th>
                </tr>
            </table>
        </div>

        <form id="form1" name="form1" method="post" action="<? echo $page; ?>">
            <table width="600" align="center" class="adminform">
                <tr width="174">
                    <td class="puso">&nbsp;</td>
                    <td class="puso">&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr width="174">
                    <td class="puso">TAHUN </td>
                    <td class="puso">:</td>
                    <td><input type="text" id="TAHUN" name="TAHUN" value="<?= isset($tahun) ? $tahun : date('Y') ?>" /></td>
                </tr>
                <tr width="174">
                    <td class="puso">No BAMSA </td>
                    <td class="puso">:</td>
                    <td><input type="text" id="BASTB" name="BASTB" value="<?= $bastb ?>" /></td>
                </tr>
                <tr width="174">
                    <td class="puso">No SO </td>
                    <td class="puso">:</td>
                    <td><input type="text" id="DOCUMENT_NUMBER" name="DOCUMENT_NUMBER" value="<?= $document_number ?>" /></td>
                </tr>
               <!-- <tr width="174">
                    <td class="puso">No Vendor </td>
                    <td class="puso">:</td>
                    <td><input type="text" id="VENDOR" name="VENDOR" value="<?= $vendor ?>" /></td>
                </tr> -->
                <tr>
                    <td class="puso">Brand </td>
                    <td class="puso">:</td>
                    <td>
                        <select name="BRAND" id="BRAND">
                            <option value="DYNAMIX">DYNAMIX</option>
                            <option value="ANDALAS">ANDALAS</option>
                            <option value="MASONRY">MASONRY</option>
                        </select>
                        <!-- <input type="text" id="BRAND" name="BRAND" value="<?= $brand ?>" /> -->
                    </td>
                </tr>
                <tr>
                    <td class="puso">Tanggal Mulai BAMSA </td>
                    <td class="puso">:</td>
                    <td><input name="TGL_CREATE_BASTB_FROM" type="text" id="TGL_CREATE_BASTB_FROM" size=12 value="<?= $tgl_create_from; ?>" onClick="return showCalendar('TGL_CREATE_BASTB_FROM');" /></td>
                </tr>
                <tr>
                    <td class="puso">Tanggal Akhir BAMSA </td>
                    <td class="puso">:</td>
                    <td><input name="TGL_CREATE_BASTB_TO" type="text" id="TGL_CREATE_BASTB_TO" size=12 value="<?= $tgl_create_to; ?>" onClick="return showCalendar('TGL_CREATE_BASTB_TO');" /></td>
                </tr>
                <tr>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />
                </tr>
                <tr>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                    <td class="ThemeOfficeMenu">&nbsp;</td>
                </tr>
            </table>
        </form>
    <? } ?>
    <br />
    <br />
    <? if ($total > 0) { ?>
        <div align="center">
            <table width="95%" align="center">
                <tr>
                    <th align="right" colspan="4"><span>
                        </span></th>
                </tr>
            </table>
        </div>
        <div align="center">
            <table width="95%" align="center" class="adminlist">
                <tr>
                    <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA MSA </span></th>
                </tr>
            </table>
        </div>
        <div align="center">
            <table width="95%" align="center" class="adminlist">
                <tr class="quote">
                    <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
                    <!-- <td align="center"><strong>Sales Org</strong></td> -->
                    <td align="center"><strong>Company Produsen</strong></td>
                    <td align="center"><strong>No. BAMSA</strong></td>
                    <!-- <td align="center"><strong>NBASTB </strong></td> -->
                    <!-- <td align="center"><strong>Tanggal Mulai</strong></td> -->
                    <!-- <td align="center"><strong>Tanggal Selesai</strong></td> -->
                    <td align="center"><strong>Volume</strong></td>
                    <td align="center"><strong>UOM</strong></td>
                    <td align="center"><strong>Nilai Billing</strong></td>
                    <td align="center"><strong>Fee</strong></td>
                    <td align="center"><strong>PPN</strong></td>
                    <td align="center"><strong>PPH</strong></td>
                    <td align="center"><strong>Tot. Fee</strong></td>
                    <!-- <td align="center"><strong>Pemilik Brand</strong></td>
                    <td align="center"><strong>Brand</strong></td> -->
                    <td align="center"><strong>No. Invoice AR</strong></td>
                    <td align="center"><strong>No. Faktur</strong></td>
                    <td align="center"><strong>Status Approval</strong></td>
                    <!-- <td align="center"><strong>File</strong></td> -->
                    <td align="center"><strong>Action</strong></td>
                </tr>
                <?php
                $totalTOTRYLT = 0;
                foreach ($list_bastp as $no => $_row) {
                    $class = ($no % 2) == 0 ? 'row0' : 'row1';
                    $totalTOTRYLT += $_row['TOTRYLT'] ;
                    $dataAttributes = htmlspecialchars(json_encode($_row));
                    $disabled = isset($_row['INVOICE_AR']) && $_row['INVOICE_AR'] != '';
                    $backgroundStyle = $_row['SMKPB'] == 'X' ? 'background-color: green; color: white;' : '';
                    $disabledRegenerate = isset($_row['SPRD2']) && $_row['SPRD2'] == '';

                ?>
                    <tr class="<?= $class ?>">
                        <td align="center"><?= ($no + 1) ?></td>
                        <td align="center"><input type="hidden" name="VKORG_" value="<?= $_row['VKORG'] ?>"><?= $_row['VKORG'] ?></td>
                        <!-- <td align="center"><input type="hidden" name="BUKRS_" value="<?= $_row['BUKRS'] ?>"><?= $_row['BUKRS'] ?></td> -->
                        <td align="center"><input type="hidden" name="BASTB_" value="<?= $_row['BASTB'] ?>"><?= $_row['BASTB'] ?></td>
                        <!-- <td align="center"><input type="hidden" name="NBASTB_" value="<?= $_row['NBASTB'] ?>"><?= $_row['NBASTB'] ?></td>
            <td align="center"><input type="hidden" name="BEGDA_" value="<?= date('d-m-Y', strtotime($_row['BEGDA'])) ?>"><?= date('d-m-Y', strtotime($_row['BEGDA'])) ?></td>
            <td align="center"><input type="hidden" name="ENDDA_" value="<?= date('d-m-Y', strtotime($_row['ENDDA'])) ?>"><?= date('d-m-Y', strtotime($_row['ENDDA'])) ?></td> -->
                        <td align="center"><input type="hidden" name="NTGEW_" value="<?= $_row['NTGEW'] ?>"><?= number_format($_row['NTGEW'], 0)  ?></td>
                        <td align="center"><input type="hidden" name="GEWEI_" value="<?= $_row['GEWEI'] ?>"><?= $_row['GEWEI'] ?></td>
                        <td align="center"><input type="hidden" name="TOTBIL_" value="<?= number_format($_row['TOTBIL'] * 100 , 0) ?>"><?= number_format($_row['TOTBIL'] * 100 , 0) ?></td>
                        <td align="center"><input type="hidden" name="NETWR_" value="<?= number_format($_row['NETWR'] * 100, 0) ?>"><?= number_format($_row['NETWR'] * 100, 0) ?></td>
                        <td align="center"><input type="hidden" name="PPN_" value="<?= number_format($_row['PPN'] * 100, 0) ?>"><?= number_format($_row['PPN'] * 100, 0) ?></td>
                        <td align="center"><input type="hidden" name="PPH_" value="<?= number_format($_row['PPH'] * 100, 0) ?>"><?= number_format($_row['PPH'] * 100, 0) ?></td>
                        <td align="center"><input type="hidden" name="TOTRYLT_" value="<?= number_format($_row['TOTRYLT'] * 100, 0) ?>"><?= number_format($_row['TOTRYLT'] * 100, 0) ?></td>
                        <!-- <td align="center"><input type="hidden" name="PEMILIK_BRAND_" value="<?= $_row['PEMILIK_BRAND'] ?>"><?= $_row['PEMILIK_BRAND'] ?></td>
                        <td align="center"><input type="hidden" name="BRAND_" value="<?= $_row['BRAND'] ?>"><?= $_row['BRAND'] ?></td> -->
                        <td align="center" style=""><input type="hidden" name="INVOICE_AR_" value="<?= $_row['INVOICE_AR'] ?>"><?= !empty($_row['INVOICE_AR']) ? $_row['INVOICE_AR'] : '-' ?></td>
                        <td align="center" style=""><input type="hidden" name="FPNUM_" value="<?= $_row['FPNUM'] ?>"><?= !empty($_row['FPNUM']) ? $_row['FPNUM'] : '-' ?></td>
                        <td align="center" style="<?= $backgroundStyle ?>"><input type="hidden" name="STATUS_INVOICE_" value="<?= $_row['SMKPB'] ?>"><?= $_row['SMKPB'] == 'X' ? 'Approve' : 'Open' ?></td>
                        <!-- <td align="center"><input type="hidden" name="DOWNLOAD_INVOICE_" value=""><i class="fa-solid fa-download"></i></td> -->
                        <td align="center">
                            <input type="button" class="button" id="btn_detail" value="Detail" onClick="detailBASTB('<?= $_row['BUKRS'] ?>','<?= $_row['BASTB'] ?>')" />
                            <!-- <button type="button" class="button mbtn" data-detail="<?= $dataAttributes ?>">
                                Generate
                            </button> -->
                            <button type="button" class="button <?= $disabled ? 'disabled' : 'mbtn' ?>" data-detail="<?= $disabled ? '' : $dataAttributes ?>" <?= $disabled ? 'disabled' : '' ?>>
                                Generate
                            </button>
                            <button type="button" class="button send_email" data-detail="<?= $dataAttributes ?>">
                                Send Email
                            </button>
                            <button type="button" class="button upload-modal" data-detail="<?= $dataAttributes ?>">
                                Upload
                            </button>
                            <button class="button download_files" data-download="<?= $dataAttributes ?>">
                                <i class="fa-solid fa-download"></i>
                            </button>
                        </td>
                    </tr>

                <?php } ?>
                <!-- <tr>
                <td colspan="13" align="right"><strong>Total Royalty:</strong></td>
                <td align="center"><strong><?= number_format($totalTOTRYLT, 3) ?></strong></td>
                </tr> -->
                <tr class="quote">
                    <td colspan="15" align="center">
                        <a href="list_bamsa_ar.php" target="isi" class="button">Back</a>
                    </td>
                </tr>
            </table>
        </div>


        <!-- The Modal -->
        <div id="modalDialog" class="modal">
            <div class="modal-content animate-top">
                <form method="post" action="komentar_royalty.php">
                    <div class="modal-header">
                        <h2>Are you sure Generate AR?</h2>
                        <button type="button" class="close">
                            <span aria-hidden="true">x</span>
                        </button>
                    </div>
                    <div class="modal-body">

                        <table>
                            <input name="action" type="hidden" value="invoice_msa" />
                            <input name="generate_type" type="hidden" value="AR" />
                            <tr>
                                <td class="puso"></td>
                                <td class="puso"></td>
                                <td><input type="hidden" name="VKORG" value="" class="readonly">
                                <input type="hidden" name="CPUDT" value="" class="CPUDT">
                                    <!-- <span class="VKORG"></span> -->
                                </td>
                            </tr>
                            <tr>
                                <td class="puso">Company Produsen</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="VKORG" value="" class="readonly"><span class="VKORG"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">No. BAMSA</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="BASTB" value="" class="readonly"> <span class="BASTB"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">Volume</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="NTGEW" value="" class=""> <span class="NTGEW"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">UOM</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="GEWEI" value="" class=""><span class="GEWEI"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">Nilai Billing</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="NETWR" value="" class=""> <span class="NETWR"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">Fee</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="TOTBIL" value="" class=""><span class="TOTBIL"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">PPN</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="PPN" value="" class=""><span class="PPN"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">PPH</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="PPH" value="" class=""><span class="PPH"></span></td>
                            </tr>

                            <tr>
                                <td class="puso">Tot. Fee</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="TOTRYLT" value="" class=""><span class="TOTRYLT"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">Pemilik Brand</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="PEMILIK_BRAND" value="" class="">
                                    <span class="PEMILIK_BRAND"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="puso">Brand</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="BRAND" value="" class=""><span class="BRAND"></span></td>
                            </tr>
                            <tr>
                                <td class="puso">No. Invoice AR</td>
                                <td class="puso">:</td>
                                <td><input type="hidden" name="INVOICE_AR" value="" class=""><span class="INVOICE_AR"></span></td>
                                <input type="hidden" name="FPNUM" value="" class="">
                            </tr>
                        </table>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary close">Cancel</button>
                        <input type='submit' class="btn btn-primary" value="Generate AR" style="z-index: 0;" />
                    </div>
                </form>
            </div>
        </div>

        <div id="modalDownload" class="modal">
            <div class="modal-content animate-top">
                <div class="modal-header">
                    <h2>File BAMSA</h2>
                    <button type="button" class="close">
                        <span aria-hidden="true">x</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table id="listInvoice">

                    </table>
                </div>
            </div>
        </div>

        <div id="modalUpload" class="modal">
            <div class="modal-content animate-top">
                <div class="modal-header">
                    <h2>Upload</h2>
                    <button type="button" class="close">
                        <span aria-hidden="true">x</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table width="100%" align="center" class="adminform">
                        <tr>
                            <td class="puso">&nbsp;</td>
                            <td class="puso">&nbsp;</td>
                            <td class="puso">&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="puso">No Faktur</td>
                            <td class="puso">:</td>
                            <td>
                                <input id="no_faktur" name="no_faktur" type="text" value="">
                            </td>
                        </tr>
                        <tr>
                            <td class="puso">Upload Pajak</td>
                            <td class="puso">:</td>
                            <td> <input id="fileInput" name="file" type="file" class="button"></td>
                        </tr>
                        <tr>
                            <td class="puso">&nbsp;</td>
                            <td class="puso">&nbsp;</td>
                            <td class="puso">&nbsp;</td>
                        </tr>
                    </table>
                    <div style="margin-top: 20px; text-align:center;">
                        <button id="saveUploadAndFaktur" class="button">Simpan</button>
                    </div>

                </div>
            </div>
        </div>

        <div id="modalNotif" class="modal">
            <div class="modal-content animate-top">
                <div class="modal-header">
                    <h2></h2>
                    <button type="button" class="close">
                        <span aria-hidden="true">x</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="text-align: center;">
                        <h2 id="textNotif"></h2>
                        <button id="buttonNotif" type="button" class="btn btn-success close">OK</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let modal = $('#modalDialog');
            let btn = $(".mbtn");
            let span = $(".close");

            function number_format(number, decimals, dec_point, thousands_sep) {
                number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
                let n = !isFinite(+number) ? 0 : +number,
                    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
                    sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
                    dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
                    s = '',
                    toFixedFix = function(n, prec) {
                        let k = Math.pow(10, prec);
                        return '' + (Math.round(n * k) / k).toFixed(prec);
                    };

                s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
                if (s[0].length > 3) {
                    s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
                }
                if ((s[1] || '').length < prec) {
                    s[1] = s[1] || '';
                    s[1] += new Array(prec - s[1].length + 1).join('0');
                }
                return s.join(dec);
            }

            function notif(type, text) {
                let title = '';
                switch (type) {
                    case 'success':
                        buttonClass = 'btn-success';
                        break;
                    case 'error':
                        buttonClass = 'btn-danger';
                        break;
                    default:
                        buttonClass = '';
                }
                $('#textNotif').text(text);
                $('#buttonNotif').removeClass('btn-success btn-danger btn-warning').addClass(buttonClass);
                $("#modalNotif").show();
            }



            $(document).ready(function() {

                function clearModalInputs() {
                    $('#modalDialog input').val('');
                }

                $(".download_files").on('click', function() {
                    let datas = $(this).data('download');
                    let BASTB = datas.BASTB;
                    let PEMILIKBRAND = datas.PEMILIK_BRAND;

                    $.ajax({
                        url: 'C_list_bamsa.php',
                        type: 'POST',
                        data: {
                            BASTP: BASTB,
                            PEMILIK_BRAND: PEMILIKBRAND,
                            download: true
                        },
                        success: function(response) {
                            let fileNames = JSON.parse(response);
                            console.log(fileNames);
                            $("#modalDownload").show();
                            $('#listInvoice').empty();

                            if (fileNames === 'Tidak Ada Data Yang Ditemukan') {
                                let noDataRow = `
                                    <tr style="text-align: center;">
                                        <td colspan="3">Tidak Ada Data Yang Ditemukan</td>
                                    </tr>
                                `;
                                $('#listInvoice').append(noDataRow);
                            } else {
                                fileNames.forEach(function(fileName) {
                                    // let originalLink = "/opt/lampp/htdocs/sdonline/data/MD_paperless/" + fileName;
                                    // let base64Link = btoa(originalLink);
                                    // let encodedLink = "data:text/plain;base64," + base64Link;
                                    // let row = `
                                    //     <tr style="margin-bottom:10px;">
                                    //         <td class="puso">
                                    //             <a href="${originalLink}" target="_blank" class="button download_invoice" data-link="${encodedLink}">
                                    //                 <i class="fa-solid fa-download"></i>
                                    //             </a>
                                    //         </td>
                                    //         <td class="puso"></td>
                                    //         <td>${fileName}</td>
                                    //     </tr>
                                    // `;
                                    let base64Link = fileName.base64;
                                    let encodedLink = "data:application/pdf;base64," + base64Link;
                                    let row = `
                                        <tr style="margin-bottom:10px;">
                                            <td class="puso">
                                                <a href="${encodedLink}" target="_blank" class="button download_invoice" download="${fileName.file_name}">
                                                    <i class="fa-solid fa-download"></i>
                                                </a>
                                            </td>
                                            <td class="puso"></td>
                                            <td>${fileName.file_name}</td>
                                        </tr>
                                    `;
                                    $('#listInvoice').append(row);
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        }
                    });
                });

                $(".send_email").on('click', function() {
                    let datas = $(this).data('detail');
                    let BASTB = datas.BASTB;
                    let PEMILIKBRAND = datas.PEMILIK_BRAND;
                    let NO_INVOICE = datas.INVOICE_AR;

                    console.log(datas);
                    $.ajax({
                        url: 'C_list_bamsa.php',
                        type: 'POST',
                        data: {
                            TYPE: "AR",
                            NO_INVOICE: NO_INVOICE,
                            BASTP: BASTB,
                            PEMILIK_BRAND: PEMILIKBRAND,
                            email_msa_sbi: true
                        },
                        success: function(response) {
                            try {
                                console.log(response);
                                notif('success', 'Email Berhasil Terkirim');
                            } catch (e) {
                                console.error("Error parsing JSON response:", e);
                                notif('danger', 'Email Tidak Terkirim');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX error:", xhr.responseText);
                        }
                    });
                });

                $(".upload-modal").on('click', function() {
                    let datas = $(this).data('detail');
                    $('#saveUploadAndFaktur').data('detail', datas);
                    $("#modalUpload").show();
                });

                $("#saveUploadAndFaktur").on('click', function() {
                    let datas = $(this).data('detail');
                    let BASTB = datas.BASTB;
                    let NO_FAKTUR = $('#no_faktur').val();
                    let fileInput = document.getElementById('fileInput');

                    if (NO_FAKTUR == '') {
                        alert("Please Insert NO FAKTUR");
                        return;
                    }

                    if (NO_FAKTUR.length < 6) {
                        alert("Please insert a No Faktur (at least 6 characters).");
                        return;
                    }

                    if (fileInput.files.length === 0) {
                        alert("Please select a file before uploading.");
                        return;
                    }

                    let file = fileInput.files[0];
                    let formData = new FormData();
                    console.log(file);
                    formData.append("FILE", file);
                    formData.append("token", "1234567890");
                    formData.append("BAMSA", BASTB);
                    formData.append("NO_FAKTUR", NO_FAKTUR);

                    $.ajax({
                        url: '../service/sv_upload_file_faktur_pajak.php',
                        type: 'POST',
                        data: formData,
                        contentType: false,
                        processData: false,
                        success: function(response) {
                            console.log('File uploaded successfully');
                            console.log(response);
                            if (response.responseCode == 200) {    
                                $("#modalUpload").fadeOut();
                                notif('success', 'Berhasil');
                            }else {
                                notif('danger', 'Gagal upload faktur pajak');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX error:", xhr.responseText);
                        }
                    });
                });


                btn.on('click', function() {
                    let rowData = $(this).data('detail');
                    console.log(rowData);

                    // Company Produsen
                    $('input[name="VKORG"]').val(rowData.VKORG);
                    $('.VKORG').text(rowData.VKORG);

                    $('input[name="CPUDT"]').val(rowData.CPUDT);
                    $('.CPUDT').text(rowData.CPUDT);

                    // No. BASTB
                    $('input[name="BASTB"]').val(rowData.BASTB);
                    $('.BASTB').text(rowData.BASTB);
                    // Kuantum
                    $('input[name="NTGEW"]').val(rowData.NTGEW);
                    $('.NTGEW').text(number_format(rowData.NTGEW, 0, '.', ','));
                    // UOM / Kuantum Unit
                    $('input[name="GEWEI"]').val(rowData.GEWEI);
                    $('.GEWEI').text(rowData.GEWEI);

                    // Nilai SO / Billing
                    $('input[name="NETWR"]').val(parseFloat(rowData.TOTBIL) * 100);
                    $('.NETWR').text(number_format(parseFloat(rowData.TOTBIL) * 100 , 0, '.', ','));
                    // Royalty
                    $('input[name="TOTBIL"]').val(parseFloat(rowData.NETWR) * 100);
                    $('.TOTBIL').text(number_format(parseFloat(rowData.NETWR) * 100 , 0, '.', ','));
                    // PPN
                    $('input[name="PPN"]').val(parseFloat(rowData.PPN) * 100);
                    $('.PPN').text(number_format(parseFloat(rowData.PPN) * 100 , 0, '.', ','));
                    // PPH
                    $('input[name="PPH"]').val(parseFloat(rowData.PPH) * 100);
                    $('.PPH').text(number_format(parseFloat(rowData.PPH) * 100 , 0, '.', ','));
                    // Tot. Royalty
                    $('input[name="TOTRYLT"]').val(parseFloat(rowData.TOTRYLT) * 100);
                    $('.TOTRYLT').text(number_format(parseFloat(rowData.TOTRYLT) * 100 , 0, '.', ','));
                    // Pemilik Brand
                    $('input[name="PEMILIK_BRAND"]').val(rowData.PEMILIK_BRAND);
                    $('.PEMILIK_BRAND').text(rowData.PEMILIK_BRAND);
                    // Brand
                    $('input[name="BRAND"]').val(rowData.BRAND);
                    $('.BRAND').text(rowData.BRAND);
                    // Invoice AR
                    $('input[name="INVOICE_AR"]').val(rowData.INVOICE_AR);
                    $('.INVOICE_AR').text(rowData.INVOICE_AR);
                    
                    $('input[name="FPNUM"]').val(rowData.FPNUM);

                    modal.show();

                });

                span.on('click', function() {
                    modal.fadeOut();
                    $("#modalDownload").fadeOut();
                    $("#modalNotif").fadeOut();
                    $("#modalUpload").fadeOut();

                });
            });

            $('body').bind('click', function(e) {
                if ($(e.target).hasClass("modal")) {
                    modal.fadeOut();
                    $("#modalDownload").fadeOut();
                    $("#modalNotif").fadeOut();
                    $("#modalUpload").fadeOut();
                }
            });
        </script>


        <form id="get_detail_bastb" action="invoice_royalty.php" method="POST">
            <input type="hidden" id="bukrs_detail" name="bukrs" />
            <input type="hidden" id="bastb_detail" name="bastb" />
        </form>
    <? } ?>
