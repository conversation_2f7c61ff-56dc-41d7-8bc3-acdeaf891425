<?
require_once ('../security_helper.php');
sanitize_global_input();
################################################################################
# File ini digunakan untuk menghitung Total GROSS_AMOUNT                       #
################################################################################

        //------------------------------------------------------------------
        $sql_select_invoice_date = "SELECT to_char(TGL_INVOICE ,'YYYYMMDD') as invoice_date from EX_INVOICE where no_invoice='$no_invoice' and DELETE_MARK='0'";
        $query_date_invoice = oci_parse($conn, $sql_select_invoice_date);
        oci_execute($query_date_invoice);
        while ($baris = oci_fetch_array($query_date_invoice)) {
            $tanggal_create_invoice = $baris[INVOICE_DATE];
        }
        //------------------------------------------------------------------


        $sql= "SELECT EX_TRANS_HDR.*,to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
	$query= oci_parse($conn, $sql);
	// echo "ini querynya = " . $sql;
	oci_execute($query);
	$total_shp_cost =0;
	$total_gl_klaim = 0;
	$total_klaim_ktg=0;;
	$total_klaim_semen=0;
	$total_klaim_rezak=0;
	$total_klaim_pdpks=0;
	$pajak_shp_cost=0;
	$total_glondong=0;
	$n=1;
	$pass_trn = 0;
        $p1mna=0;
	while($row=oci_fetch_array($query)){
		$no_invoice_v=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$shp_trn_vcek=$row[NO_SHP_TRN];
		$plant_v=$row[PLANT]; 
		$nama_plant_v=$row[NAMA_PLANT]; 
		$warna_plat_v=$row[WARNA_PLAT]; 
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR]; 
		
		$tanggal_invoice_v=$row[TANGGAL_INVOICE1];
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		//echo " nilai cost hdr ".$row[SHP_COST];
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  
		$ebeln_v=$row[EBELN];  
		$ebelp_v=$row[EBELP];  
		$prctr_v=$row[PRCTR];  
		$kostl_v=$row[KOSTL];  
		$org_v=$row[ORG];  
		$nama_org_v=$row[NAMA_ORG];
		if ($row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********'){
			$total_glondong += $row[TOTAL_KTG_RUSAK];
			$total_glondong += $row[TOTAL_SEMEN_RUSAK];	
			$total_glondong += $row[TOTAL_KTG_REZAK];
			$total_glondong += $row[PDPKS];

			$total_glondong = $total_glondong - $row[KLAIM_LEBIH];
			$total_glondong = $total_glondong - $row[PDPKS_LEBIH];
		}else{  
			$total_klaim_ktg+=$row[TOTAL_KTG_RUSAK];
			$total_klaim_semen+=$row[TOTAL_SEMEN_RUSAK];
			$total_klaim_rezak+=$row[TOTAL_KTG_REZAK];
			$total_klaim_pdpks+=$row[PDPKS];
		}
		$approve_v=$row[TANGGAL_APPROVE];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		
		$inco=$row[INCO];  		
		$prctr_v=$row[KODE_SHP_COST];  		
		$no_gl_shp=$row[NO_GL_SHP];  		
		$pajak_n_v=$row[PAJAK_N];
		
		$sql_num= "SELECT sum(SHP_COST) as SHP_COST FROM EX_TRANS_COST WHERE DELETE_MARK ='0' AND NO_SHP_TRN = '$shp_trn_vcek'";
		$query_num= oci_parse($conn, $sql_num);
		oci_execute($query_num);
		$cek=1;
		$nilai_per_shp = 0;
		while($row_num=oci_fetch_array($query_num)){
                        $total_data_shp=round($row_num[SHP_COST]);//+round(0.1*$row[SHP_COST],0);
			$total_shp_cost+=round($row_num[SHP_COST]);
			$nilai_per_shp += $row_num[SHP_COST];
			$n++;
		}
	}

	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ 
		//pengujian pajak 11%
		if($tanggal_create_invoice>=20220401 && $tanggal_create_invoice<20250101){
			$pajak_shp_cost += 0.11*$total_shp_cost;
		}
		elseif($tanggal_create_invoice>=20250101){
			$pajak_shp_cost += (((11/12)*$total_shp_cost)*0.12);
		}else{
			$pajak_shp_cost += 0.1*$total_shp_cost;
		}
		//---------------------
                //liyantanto
//                $sql_pjaknew="select to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF,VENDOR from (
//                                SELECT TANGGAL_KIRIM,VENDOR FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice'
//                                order by TANGGAL_KIRIM desc
//                            ) where rownum =1";
//                $querycek= oci_parse($conn, $sql_pjaknew);
//                oci_execute($querycek);
//                $row_datap=oci_fetch_assoc($querycek);unset($tglkirimpj);
//                $tglkirimpj=$row_datap[TANGGAL_KIRIMF];
//                $VENDORpj=$row_datap[VENDOR];
//                if($VENDORpj!='0000410082'){
//                if($tglkirimpj!='' && $tglkirimpj>='20140901'){
//                    $pajak_in1 = $total_shp_cost * 0.1;
//                    $pajak_shp_cost += $pajak_in1 * 0.1;
//                    $p1mna=1;
//                }
//                }
                $sql_pjaknew="
                            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                            select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                            (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice'
                            group by NO_INVOICE)
                            order by TGL_PAJAK_EX desc
                            ) where rownum =1
                        ";
                $querycek= oci_parse($conn, $sql_pjaknew);
                oci_execute($querycek);
                $row_datap=oci_fetch_assoc($querycek);unset($tglfakturpajak);
                $tglfakturpajak=$row_datap[TGL_PAJAK_EXF];
                $VENDORpj=$row_datap[NO_VENDOR];
                if($VENDORpj!='0000410082'){
                    if($tglfakturpajak!='' && $tglfakturpajak>='20140901'){
						//pengujian pajak 11%
						if($tanggal_create_invoice>=20220401 && $tanggal_create_invoice<20250101){
                            if($VENDORpj=='0000410003'){
                                $pajak_in1 = $total_shp_cost * 0.011;
								$pajak_shp_cost += $pajak_in1 * 0.011;
								$p1mna=1;
                            } else {
								$pajak_in1 = $total_shp_cost * 0.11;
								$pajak_shp_cost += $pajak_in1 * 0.11;
								$p1mna=1;
                            }
						}
						//pengujian pajak 12%
						elseif($tanggal_create_invoice>=20250101){
                            if($VENDORpj=='0000410003'){
                                $pajak_in1 = $total_shp_cost * (11/12);
								$pajak_shp_cost += $pajak_in1 * 0.012;
								$p1mna=1;
                            } else {
                                $pajak_in1 = $total_shp_cost * (11/12);
								$pajak_shp_cost += $pajak_in1 * 0.012;
								$p1mna=1;
                            }
						}
						else{
							$pajak_in1 = $total_shp_cost * 0.1;
							$pajak_shp_cost += $pajak_in1 * 0.1;
							$p1mna=1;
						}
						//-----------------
                    }
                }
                
			if ($pajak_n_v=="Y"){
			$pajak_shp_cost = 0;
			$sql_pajak_1000= "SELECT SUM(SHP_COST) AS JUM_SHPCOST FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice'";
			$query_pajak1000= oci_parse($conn, $sql_pajak_1000);
			oci_execute($query_pajak1000);
			$row_pajak1000=oci_fetch_array($query_pajak1000);
			$kena_pajak = $row_pajak1000["JUM_SHPCOST"];
			//pajak 11%
			if($tanggal_create_invoice>=20220401 && $tanggal_create_invoice<20250101){
				$pajak_shp_cost = 0.11*$kena_pajak;
			}
			//pajak 12%
			elseif($tanggal_create_invoice>=20250101){
                $pajak_shp_cost += (((11/12)*$kena_pajak)*0.12);
			}
			else{
				$pajak_shp_cost = 0.1*$kena_pajak;
			}
			//-------------------
            if($p1mna==1){
			//pengujian pajak 11%
							if($tanggal_create_invoice>=20220401 && $tanggal_create_invoice < 20250101){
					if($VENDORpj=='0000410003'){
						$pajak_in1 = $kena_pajak * 0.011;
						$pajak_shp_cost = $pajak_in1 * 0.011;
					} else {
						$pajak_in1 = $kena_pajak * 0.11;
						$pajak_shp_cost = $pajak_in1 * 0.11;
					}
							}
							//pengujian pajak 12%
							elseif($tanggal_create_invoice>=20250101){
					if($VENDORpj=='0000410003'){
                        $pajak_in1 = $kena_pajak * 0.012;
						$pajak_shp_cost = $pajak_in1 * 0.012;
					} else {
						$pajak_in1 = $kena_pajak;
						$pajak_shp_cost = $pajak_in1 * 0.012;
					}
							}
							else{
					$pajak_in1 = $kena_pajak * 0.1;
					$pajak_shp_cost = $pajak_in1 * 0.1;
				}
							//-------------------------
            }
			
			}elseif($pajak_n_v=="N"){
			$pajak_shp_cost = 0;
			}

	}else {
		$pajak_shp_cost = 0;
	
	$UNTUK_SHP = $total_shp_cost + $pajak_shp_cost;
	
	$total=count($shp_trn_v);

		$sql_tgl= "SELECT to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EX1 FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
		$query_tgl= oci_parse($conn, $sql_tgl);
		oci_execute($query_tgl);
		
		$row_tgl=oci_fetch_array($query_tgl);
		$tgl_pajak_x=$row_tgl[TGL_PAJAK_EX1];
		
                if ($no_pajak_ex != ""){
		  $no_pajak_in = str_replace("-", "", $no_pajak_ex);
		  $no_pajak_in = str_replace(".", "", $no_pajak_in);
		}
		$bvtyp = $_POST['bvtyp'];
		
	$ke_gl =0;
	

		if ($total_glondong > 1){// unutk klaim glondong
			$ke_gl++;
			$kode_claim_gld = 'SG0006';
	
			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_gld' and ORG='$org_v' AND DELETE_MARK = '0' AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
			$query= oci_parse($conn, $mialo);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$nama_claim_gld=$row['NAMA_KOMPONEN']; 
			$keterangan_claim_gld=$row['KETERANGAN']; 
			$no_gl_claim_gld=$row['NO_GL']; 
			$tax_gld=$row['TAX_CODE']; 
			$rate_gld=$row['RATE']; 
			//echo "<br> Klaim Total Glondong ".$total_glondong;
			$pajak_gld = $total_glondong*$rate_gld;
			$total_gld = $total_glondong+$pajak_gld;

			$total_gl_klaim2 = (-1)*$total_glondong;
			$pajak_gld2 = (-1)*$pajak_gld;
			$pajak_shp_cost+=$pajak_gld2;
			$total_gld2=$total_gl_klaim2+$pajak_gld2;

		}

	$sql_new= "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_ALL_LEBIH) AS KLAIM_LEBIH, SUM(TOTAL_KLAIM_ALL) AS KLAIM_ALL, SUM(KLAIM_LEBIH) AS KLEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";

	$query_new= oci_parse($conn, $sql_new);
	oci_execute($query_new);
	while($row_new=oci_fetch_array($query_new)){
		$sold_to_new_v=$row_new[SOLD_TO];
		$klaim_ktg_new_v=$row_new[KLAIM_KTG];  
		$klaim_rezak_new_v=$row_new[KLAIM_REZAK];  
		$klaim_semen_new_v=$row_new[KLAIM_SEMEN];  
		$klaim_pdpks_new_v=$row_new[KLAIM_PDPKS];  
		$klaim_all_lebih_new_v=$row_new[KLAIM_LEBIH];  
		$klaim_all_new_v=$row_new[KLAIM_ALL];  

		$klaim_pdpks_lebih_new_v=$row_new[PDPKS_LEBIH];  
		$klaim_lebih_new_v=$row_new[KLEBIH];  
		
		if($klaim_pdpks_new_v > $klaim_pdpks_lebih_new_v)
		$klaim_pdpks_new_v = $klaim_pdpks_new_v - $klaim_pdpks_lebih_new_v;
		else 
		$klaim_pdpks_new_v = 0;

		if($klaim_semen_new_v > $klaim_lebih_new_v)
		$klaim_semen_new_v = $klaim_semen_new_v - $klaim_lebih_new_v;
		else 
		$klaim_semen_new_v = 0;
	
		if ($klaim_all_new_v > $klaim_all_lebih_new_v){

			if ($klaim_ktg_new_v > 1){// unutk klaim kantong
				$ke_gl++;
				$kode_claim_ktg = 'SG0002';
				$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_ktg' and ORG='$org_v' AND DELETE_MARK = '0' AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
				$query= oci_parse($conn, $mialo);
				oci_execute($query);
				$row=oci_fetch_array($query);
				$nama_claim_ktg=$row['NAMA_KOMPONEN']; 
				$keterangan_claim_ktg=$row['KETERANGAN']; 
				$no_gl_claim_ktg=$row['NO_GL']; 
				$tax_ktg=$row['TAX_CODE']; 
				$rate_ktg=$row['RATE']; 
	
				$pajak_ktg = $klaim_ktg_new_v*$rate_ktg;
				$total_ktg = $klaim_ktg_new_v+$pajak_ktg;
				$total_gl_klaim2 = (-1)*$klaim_ktg_new_v;
				$pajak_ktg2 = (-1)*$pajak_ktg;
				$pajak_shp_cost+=$pajak_ktg2;
				$total_ktg2=$total_gl_klaim2+$pajak_ktg2;
	
				$total_gl_klaim+=$klaim_ktg_new_v;
			}
	
	
			if ($klaim_semen_new_v > 1){// unutk klaim semen
                                $ke_gl++;
				$kode_claim_semen = 'SG0003';
                                $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' and ORG='$org_v' AND DELETE_MARK = '0' AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
				$query= oci_parse($conn, $mialo);
				oci_execute($query);
				$row=oci_fetch_array($query);
				$nama_claim_semen=$row['NAMA_KOMPONEN']; 
				$keterangan_claim_semen=$row['KETERANGAN']; 
				$no_gl_claim_semen=$row['NO_GL']; 
				$tax_semen=$row['TAX_CODE']; 
				$rate_semen=$row['RATE']; 
	
				$pajak_semen = $klaim_semen_new_v*$rate_semen;
				$total_semen = $klaim_semen_new_v+$pajak_semen;
				$total_gl_klaim2 = (-1)*$klaim_semen_new_v;
				$pajak_semen2 = (-1)*$pajak_semen;
				$pajak_shp_cost+=$pajak_semen2;
				$total_semen2=$total_gl_klaim2+$pajak_semen2;
				
				$total_gl_klaim+=$klaim_semen_new_v;
				$pass_trn += $klaim_semen_new_v;
			}
	
	
			if ($klaim_rezak_new_v > 1){// unutk klaim rezak
				$ke_gl++;
				$kode_claim_rezak = 'SG0004';
				$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' and ORG='$org_v' AND DELETE_MARK = '0' AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
				$query= oci_parse($conn, $mialo);
				oci_execute($query);
				$row=oci_fetch_array($query);
				$nama_claim_rezak=$row['NAMA_KOMPONEN']; 
				$keterangan_claim_rezak=$row['KETERANGAN']; 
				$no_gl_claim_rezak=$row['NO_GL']; 
				$tax_rezak=$row['TAX_CODE']; 
				$rate_rezak=$row['RATE']; 
	
				$pajak_rezak = $klaim_rezak_new_v*$rate_rezak;
				$total_rezak = $klaim_rezak_new_v+$pajak_rezak;
				$total_gl_klaim2 = (-1)*$klaim_rezak_new_v;
				$pajak_rezak2 = (-1)*$pajak_rezak;
				$pajak_shp_cost+=$pajak_rezak2;
				$total_rezak2=$total_gl_klaim2+$pajak_rezak2;
	
				$total_gl_klaim+=$klaim_rezak_new_v;
				$pass_trn += $klaim_rezak_new_v;
			}

				if($klaim_pdpks_new_v > 1 ){
					$ke_gl++;
					$kode_claim_pdpks = 'SG0005';
					$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_pdpks' and ORG='$org_v' AND DELETE_MARK = '0' AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
					$query= oci_parse($conn, $mialo);
					oci_execute($query);
					$row=oci_fetch_array($query);
					$nama_claim_pdpks=$row['NAMA_KOMPONEN']; 
					$keterangan_claim_pdpks=$row['KETERANGAN']; 
					$no_gl_claim_pdpks=$row['NO_GL']; 
					$tax_pdpks=$row['TAX_CODE']; 
					$rate_pdpks=$row['RATE']; 
		
					$pajak_pdpks = $klaim_pdpks_new_v*$rate_pdpks;
					$total_pdpks = $klaim_pdpks_new_v+$pajak_pdpks;
					$total_gl_klaim2 = (-1)*$klaim_pdpks_new_v;
					$pajak_pdpks2 = (-1)*$pajak_pdpks;
					$pajak_shp_cost+=$pajak_pdpks2;
					$total_pdpks2=$total_gl_klaim2+$pajak_pdpks2;
		
					$total_gl_klaim+=$klaim_pdpks_new_v;
				}

		}// end jika klaim normal lebih besar dari pada klaim lebih
			// jika klaimlebih > klaim normal maka tidak dianggap 0/ tidak ada klaim 

	}// end while
	}
		

	$jumlahd = $_POST['jumlahd'];
	$komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
	$total_d = 0;

	if (isset($_POST['komponen_biaya_d1']) and $_POST['komponen_biaya_d1']!=""){
		$ke_gl++;

		$komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
		$nama_komponen_d1 = $_POST['nama_komponen_d1'];
		$nilaid1 = $_POST['nilaid1'];
		$keterangan_d1 = $_POST['keterangan_d1'];
		$pajak_d1 = $_POST['pajak_d1'];
		$no_gl_d1 = $_POST['no_gl_d1'];
		$total_d+=$nilaid1;

			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d1' and ORG='$org_v' AND DELETE_MARK = '0'";
			$query= oci_parse($conn, $mialo);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim=$row['NO_GL']; 
			$rate_claim=$row['RATE']; 
			$cc_biaya=$row['COST_CENTER']; 
			$prctr_biaya=$row['PRCTR']; 

			$pajak_claim= $nilaid1*$rate_claim;
			$total_claim = $nilaid1+$pajak_claim;
			$pajak_shp_cost+=$pajak_claim;

		for ($i=2;$i<=$jumlahd;$i++){
		$ke_gl ++;

			$komp_d = "komponen_biaya_d".$i;
			$nama_komp_d = "nama_komponen_d".$i;
			$komp_nilaid = "nilaid".$i;
			$komp_ketd = "keterangan_d".$i;
			$komp_pajakd = "pajak_d".$i;
			$komp_gld = "no_gl_d".$i;
			
			$komponen_biaya_d = $_POST[$komp_d];
			$nama_komponen_d = $_POST[$nama_komp_d];
			$nilaid = $_POST[$komp_nilaid];
			$keterangan_d = $_POST[$komp_ketd];
			$pajak_d = $_POST[$komp_pajakd];
			$no_gl_d = $_POST[$komp_gld];
			$total_d+=$nilaid;

			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d' and ORG='$org_v' AND DELETE_MARK = '0'";
			$query= oci_parse($conn, $mialo);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim=$row['NO_GL']; 
			$rate_claim=$row['RATE']; 
			$cc_biaya=$row['COST_CENTER']; 
			$prctr_biaya=$row['PRCTR']; 

			$pajak_claim= $nilaid*$rate_claim;
			$total_claim = $nilaid+$pajak_claim;
			$pajak_shp_cost+=$pajak_claim;

		}

	}

	$jumlahk = $_POST['jumlahk'];
	$komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
	$total_k = 0;

	if (isset($_POST['komponen_biaya_k1']) and $_POST['komponen_biaya_k1']!=""){
		$komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
		$nama_komponen_k1 = $_POST['nama_komponen_k1'];
		$nilaik1 = $_POST['nilaik1'];
		$keterangan_k1 = $_POST['keterangan_k1'];
		$pajak_k1 = $_POST['pajak_k1'];
		$no_gl_k1 = $_POST['no_gl_k1'];
		$total_k+=$nilaik1;
		$ke_gl ++;
		
                        $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k1' and ORG='$org_v' AND DELETE_MARK = '0'";
			$query= oci_parse($conn, $mialo);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim=$row['NO_GL']; 
			$rate_claim=$row['RATE']; 
			$cc_biaya=$row['COST_CENTER']; 
			$prctr_biaya=$row['PRCTR']; 

			$pajak_claim= $nilaik1*$rate_claim;
			$total_claim = $nilaik1+$pajak_claim;

			$pajak_claim2= (-1)*$nilaik1*$rate_claim;
			$nilai_claim = (-1)*$nilaik1;
			$total_claim2 = $nilai_claim+pajak_claim2;

			$pajak_shp_cost+=$pajak_claim2;

		for ($i=2;$i<=$jumlahk;$i++){
		$ke_gl ++;
			$komp_k = "komponen_biaya_k".$i;
			$nama_komp_k = "nama_komponen_k".$i;
			$komp_nilaik = "nilaik".$i;
			$komp_ketk = "keterangan_k".$i;
			$komp_pajakk = "pajak_k".$i;
			$komp_glk = "no_gl_k".$i;
			
			$komponen_biaya_k = $_POST[$komp_k];
			$nama_komponen_k = $_POST[$nama_komp_k];
			$nilaik = $_POST[$komp_nilaik];
			$keterangan_k = $_POST[$komp_ketk];
			$pajak_k = $_POST[$komp_pajakk];
			$no_gl_k = $_POST[$komp_glk];
			$total_k+=$nilaik;

			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k' and ORG='$org_v' AND DELETE_MARK = '0'";
			$query= oci_parse($conn, $mialo);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim=$row['NO_GL']; 
			$rate_claim=$row['RATE']; 
			$cc_biaya=$row['COST_CENTER']; 
			$prctr_biaya=$row['PRCTR']; 

			$pajak_claim= $nilaik*$rate_claim;
			$total_claim = $nilaik+$pajak_claim;

			$pajak_claim2= (-1)*$nilaik*$rate_claim;
			$nilai_claim = (-1)*$nilaik;
			$total_claim2 = $nilai_claim+$pajak_claim2;

			$pajak_shp_cost+=$pajak_claim2;

		}
	}	
	if ($total_glondong < 1)$total_glondong = 0;
        
    //    echo "<br><b>total_shp_cost</b> : ".$total_shp_cost;
    //    echo "<br><b>total_d</b> : ". $total_d;
    //    echo "<br><b>total_k</b> : ".$total_k; //minus kredit dihilangkan di $nilai_invoice
    //    echo "<br><b>total_gl_klaim</b> : ".$total_gl_klaim;
    //    echo "<br><b>total_glondong</b> : ".$total_glondong;
    //    echo "<br><b>pajak_shp_cost</b> : ".round($pajak_shp_cost,0);
    //    	echo "<br>tot gl: ".$total_glondong;
    //            echo "<br>tot gl_klaim: ".$total_gl_klaim;
    //            echo "<br>pajak_shp_cost: ".$pajak_shp_cost;
        if($p1mna==1){
            //perubahan hitungan pajak hafidz 20 april 2022

            $nilai_invoice = $total_shp_cost  + $total_d - $total_glondong + round($pajak_shp_cost,0);
        }else{
            $nilai_invoice = $total_shp_cost  + $total_d - $total_glondong + round($pajak_shp_cost,0);
        }
	// echo "<br><b>GROSS_AMOUNT</b> : ".$nilai_invoice;

 ?>
