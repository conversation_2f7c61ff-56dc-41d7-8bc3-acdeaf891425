<?php

// --- Konfigurasi username & password ---
$valid_users = array(
    "smbr-jaya" => "i-love-smbr"
);

// --- <PERSON>bil credential dari header Authorization ---
if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("success" => false, "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

// --- Validasi login ---
if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("success" => false, "message" => "Invalid credentials"));
    exit;
}

// Untuk PHP 5.2: Baca raw input karena php://input tidak otomatis di-parse
$rawInput = file_get_contents('php://input');
$request = json_decode($rawInput, true); // true = array, bukan object

// Siapkan response default
$response = array(
    "status" => "400",
    "message" => "Parameter tidak sesuai",
    "data" => array()
);

function parse_to_float($value) {
    $value = str_replace('.', '', $value);
    $value = str_replace(',', '.', $value);
    return $value;
}

// Cek apakah parameter sesuai
if (
    isset($request['lr_exti1']) && 
    isset($request['lr_kunnr'])
) {
    $exti1 = $request['lr_exti1']['low'];

    if($exti1){
        $shipments = array();
        $shipments[] = array(
        "tknum"     => substr($exti1, -8),
        "exti1"     => $exti1,
        "fknum"     => substr($exti1, -9),
        "fkpos"     => "000001",
        "netwr"     => parse_to_float("1.500,00"),
        "fkpty"     => "Z001",
        "werks"     => "3401",
        "ebeln"     => "**********",
        "ebelp"     => "00001",
        "lblni"     => "**********",
        "stabr"     => "C",
        "kostl"     => "**********",
        "prctr"     => "3400",
        "bankn"     => "*************",
        "banka"     => "Bank Danamon",
        "brnch"     => "Cabang Bundo Kandung",
        "sakto"     => "********",
        "netwr_do"  => parse_to_float("45.000,00"),
        "ntgew"     => parse_to_float("40,000"),
        "lifnr"     => "410092",
        "name1"     => "PT SILOG DEV",
        "bvtyp"     => "IDR2",
        "bran1"     => "",
        "vtextx"    => ""
        );
    }

    $response['status'] = "200";
    $response['message'] = "Success Invoke";
    $response['data'] = $shipments;
}

// Set response header
header('Content-Type: application/json');
echo json_encode($response);
?>
