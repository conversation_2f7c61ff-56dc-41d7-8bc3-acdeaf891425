<?
/*
 * Target Volume dan Index Volume per periode ,tiap masing-masing 
 */

session_start();

require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();

$targetVolume='inputtargetvolumegp.php';
$user_id=$_SESSION['user_id'];
//$user_id='mady';

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}
$com='2000';
$item_no='121-301';
/*$halaman_id=1553;

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
 */
$total=0;
$filViewKota='';
if(isset($_POST['Save'])){
    $sampai=$_POST['total'];
    for($k=0;$k<$sampai;$k++){
	$urutke="urutke".$k;
        
        $TargetVOLGPval="KONTRAVOL".$k;
        $STATUSval="STATUSval".$k;
        $BRAN1val="BRAN1v".$k;
        $PLANTval="PLANTv".$k;
        $PLANTGPval="PLANTGPv".$k;
        $BULANval="BULANv".$k;
        $TAHUNval="TAHUNv".$k;
        $TIPEval="TIPEv".$k;
                
        $nourut=" no urut ke ".$k+1;
        if (isset($_POST[$urutke])){
			if (($_POST[$TargetVOLGPval] == "")){
				$check = false;
				$k=$sampai+2;
			}else{
				$check = true;
			}
	}
    }
    if ($check){ 
        $recDATA=array();
	for($k=0;$k<$sampai;$k++){
            $urutke="urutke".$k;
            $TargetVOLGPval="KONTRAVOL".$k;
            $STATUSval="STATUSval".$k;
            $BRAN1val="BRAN1v".$k;
            $PLANTval="PLANTv".$k;
            $PLANTGPval="PLANTGPv".$k;
            $BULANval="BULANv".$k;
            $TAHUNval="TAHUNv".$k;  
            $TIPEval="TIPEv".$k;
            
            
            if(isset($_POST[$urutke])){
             $nourutdata=$k+1;   
             $TAHUNval=$_POST[$TAHUNval];
             $BULANval=$_POST[$BULANval];
             $PLANTGPval=$_POST[$PLANTGPval];
             $PLANTval=$_POST[$PLANTval];
             $BRAN1val=$_POST[$BRAN1val];
             $STATUSval=$_POST[$STATUSval];
             $TargetVOLGPval=$_POST[$TargetVOLGPval];
             $TIPEval=$_POST[$TIPEval];
             
             $recDATA[]=$nourutdata;             
             $sqlUpdate="UPDATE ZREPORT_TARGET_PLANT SET TARGET_GP='$TargetVOLGPval',STATUS_GP='$STATUSval'
             where 
             KD_PLANT ='$PLANTval'
             and KD_KOTA is null
             and ITEM_NO='$TIPEval'
             and BULAN='$BULANval'  
             and TAHUN='$TAHUNval'  
             and BRAN12='$BRAN1val'
             and KD_PLANT_GP='$PLANTGPval' 
             and BRAN12 is not null

             ";
             //echo $sqlUpdate;
             $query= oci_parse($conn, $sqlUpdate);
             oci_execute($query);
            
              
            }
        }
        echo "<script>alert('Data ke no urut ";
        foreach ($recDATA as $key => $valueRec) {
            echo $valueRec." ";            
        }
        echo " di update... !!!');</script>";
        $komen = "Update data telah dilakukan, Silahkan Pilh parameter yang ada..!!!";
        //echo "<script type=\"text/javascript\">location.href=\"/$targetVolume\"</script>";
    }else{
	echo "<script>alert('Data Update Salah.. Silahkan Input Ulang... ');</script>";

    }
}else if(isset($_POST['Delete'])){
    $sampai=$_POST['total'];
    for($k=0;$k<$sampai;$k++){
	$urutke="urutke".$k;
        
        $TargetVOLGPval="KONTRAVOL".$k;
        $STATUSval="STATUSval".$k;
        $BRAN1val="BRAN1v".$k;
        $PLANTval="PLANTv".$k;
        $PLANTGPval="PLANTGPv".$k;
        $BULANval="BULANv".$k;
        $TAHUNval="TAHUNv".$k;
        $TIPEval="TIPEv".$k;
                
        $nourut=" no urut ke ".$k+1;
        if (isset($_POST[$urutke])){
			if (($_POST[$TargetVOLGPval] == "")){
				$check = false;
				$k=$sampai+2;
			}else{
				$check = true;
			}
	}
    }
    if ($check){ 
        $recDATA=array();
	for($k=0;$k<$sampai;$k++){
            $urutke="urutke".$k;
            $TargetVOLGPval="KONTRAVOL".$k;
            $STATUSval="STATUSval".$k;
            $BRAN1val="BRAN1v".$k;
            $PLANTval="PLANTv".$k;
            $PLANTGPval="PLANTGPv".$k;
            $BULANval="BULANv".$k;
            $TAHUNval="TAHUNv".$k;  
            $TIPEval="TIPEv".$k;
            
            
            if(isset($_POST[$urutke])){
             $nourutdata=$k+1;   
             $TAHUNval=$_POST[$TAHUNval];
             $BULANval=$_POST[$BULANval];
             $PLANTGPval=$_POST[$PLANTGPval];
             $PLANTval=$_POST[$PLANTval];
             $BRAN1val=$_POST[$BRAN1val];
             $STATUSval=$_POST[$STATUSval];
             $TargetVOLGPval=$_POST[$TargetVOLGPval];
             $TIPEval=$_POST[$TIPEval];
             
             $recDATA[]=$nourutdata;             
             $sqlUpdate="DELETE from ZREPORT_TARGET_PLANT 
             where 
             KD_PLANT ='$PLANTval'
             and KD_KOTA is null
             and ITEM_NO='$TIPEval'
             and BULAN='$BULANval'  
             and TAHUN='$TAHUNval'  
             and BRAN12='$BRAN1val'
             and KD_PLANT_GP='$PLANTGPval' 
             and BRAN12 is not null

             ";
             //echo $sqlUpdate;
             $query= oci_parse($conn, $sqlUpdate);
             oci_execute($query);
            
              
            }
        }
        echo "<script>alert('Data ke no urut ";
        foreach ($recDATA as $key => $valueRec) {
            echo $valueRec." ";            
        }
        echo " di Delete... !!!');</script>";
    }else{
	echo "<script>alert('Data Delete Salah... ');</script>";

    }
}

else if(isset($_POST['Submit']))
{
    $bulanVolum=$_POST['bulanVolum'];
    $tahunVolum=$_POST['tahunVolum'];
    $nmplant=$_POST['nmplant'];
    $nmpTIPE=$_POST['nmpTIPE'];
    $nmplantGP2=$_POST['nmplantGP'];
    $pisahPLANTGP2=explode("#", $nmplantGP2);
    //$kotaGPVolum=$pisahPLANTGP2['1'];
    $kecVolum=$pisahPLANTGP2['2'];
    $nmplantGP=$pisahPLANTGP2['0'];
    $targetGPVolum=$_POST['targetGPVolum'];
    if(($bulanVolum=='00') || ($nmplant=='00') || ($nmpTIPE=='00') || ($nmplantGP2=='00') || ($tahunVolum=='')|| ($targetGPVolum==''))
    {
      echo "<script>alert('Silahkan cek kembali parameter yang bertanda merah...!!');</script>";  
    }else{   
        
      $sqlCek= "

        SELECT count(KD_PLANT) as JUMLAH from ZREPORT_TARGET_PLANT WHERE 
         KD_PLANT ='$nmplant'
         and KD_KOTA is null
         and ITEM_NO='$nmpTIPE'
         and BULAN='$bulanVolum'  
         and TAHUN='$tahunVolum'  
         and BRAN12='$kecVolum'
         and KD_PLANT_GP='$nmplantGP' 
         and BRAN12 is not null

            ";   
       //echo $sqlCek;
        $queryCek= oci_parse($conn, $sqlCek);
        oci_execute($queryCek);
        $row=oci_fetch_array($queryCek);
        $jumlahData=$row['JUMLAH'];
        if($jumlahData=='0'){
            //echo "<script>alert('KOSONG ISI');</script>";  
            $sqlInsert2="
                INSERT INTO ZREPORT_TARGET_PLANT (
                  KD_PLANT,
                  ITEM_NO,
                  BULAN ,
                  TAHUN ,
                  BRAN12 ,
                  TARGET_GP,
                  KD_PLANT_GP 
                ) VALUES (
                  '$nmplant',
                  '$nmpTIPE',
                  '$bulanVolum',
                  '$tahunVolum',
                  '$kecVolum',
                  $targetGPVolum,
                  '$nmplantGP'
                )
                 ";
                 //echo $sqlInsert2;
                $query2= oci_parse($conn, $sqlInsert2);
                oci_execute($query2);
                echo "<script>alert('Data Berhasil ditambahkan');</script>";  
        }else{
            echo "<script>alert('Sudah ada data Target dengan parameter tersebut...!!!');</script>";  
        }
    }
}else if (isset($_POST['Filter']))
{
    $filterKota=$_POST['filterKota'];
    if ($filterKota=='00')
    {
        $filViewKota="";
    }else{
        $filViewKota="and KD_PLANT_GP='$filterKota'";
    }
}       
 
        $bulanNow=date("m");
        $tahunNow=date("Y");
        $sql= "
            
            SELECT KD_PLANT,ITEM_NO,KD_KOTA as KD_KOTA2,BULAN,TAHUN,BRAN12,KD_PLANT_GP,TARGET_GP,STATUS_GP from ZREPORT_TARGET_PLANT 
            WHERE BRAN12 is not null
            and BULAN='$bulanNow'
            and TAHUN='$tahunNow'
            $filViewKota
            

            ";   
       // echo $sql;
        $query2= oci_parse($conn, $sql);
        oci_execute($query2);
        $q = 0;
        $komen = " Tidak Ada Data Yang Di Temukan..!!!";
        while($row=oci_fetch_array($query2)){            

                    $PLANT[$q]=$row['KD_PLANT'];
                    $TIPE[$q]=$row['ITEM_NO'];
                    $BULAN[$q]=$row['BULAN'];
                    $TAHUN[$q]=$row['TAHUN'];
                    $BRAN12[$q]=$row['BRAN12'];
                    $STATUS[$q]=$row['STATUS_GP'];
                    $TARGET_GP[$q]=$row['TARGET_GP'];
                    $KD_PLANT_GP[$q]=$row['KD_PLANT_GP'];
                    
                   $q++;

        }
        $total = count($PLANT);
        


?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Target  GP</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
  <script type="text/javascript">

checked=false;
function checkedAll (frm1) {
	var aa= document.getElementById('formVolume');
	 if (checked == false)
          {
           checked = true
		   markAllRows('formVolume');
          }
        else
          {
          checked = false
		  unMarkAllRows('formVolume')
          }
 }

function markAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var input;
    var input2;
    var select;
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != true){
				checkbox.checked = true;
                                checkbox.value = '1';
				rows[i].className += ' selected';
			}
        }
        input = rows[i].getElementsByTagName( 'input' )[1];

        if ( input && input.type == 'text' ) {
			if (input.checked != true){
				input.checked = true;
                                input.disabled = '';
			}
        }
        
        select = rows[i].getElementsByTagName( 'select' )[0];

        if ( select ) {
			if (select.checked != true){
				select.checked = true;
                                select.disabled = '';
			}
        }
        
    }

    return true;
}

function unMarkAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var input;
    var input2;
    var select;
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != false){
			checkbox.checked = false;   
                        checkbox.value = '0';
                        rows[i].className = rows[i].className.replace(' selected', '');
                        
                    }
        }
        input = rows[i].getElementsByTagName( 'input' )[1];

        if ( input && input.type == 'text' ) {
			if (input.checked != false){
				input.checked = false;
                                input.disabled = 'disabled';
			}
        }
        
        select = rows[i].getElementsByTagName( 'select' )[0];

        if ( select ) {
			if (select.checked != false){
				select.checked = false;
                                select.disabled = 'disabled';
			}
        }
    }

    return true;
}

</script> 

<script> 

function cek_last(id_cek) {
		var obj = document.getElementById(id_cek);
		var cek = obj.value;	
		var kec;
		var satu_data = "0";  

		for (var keb = 0; keb < cek; keb++){
			kec = keb + 1;
                        var TargetVOLGPval = 'KONTRAVOL'+keb;
                        var com_TargetVOLGPval = document.getElementById(TargetVOLGPval);
	                var rowke = 'urutke'+keb;
			var com_rowke = document.getElementById(rowke);

			if (com_rowke.checked == true)  {
			
			satu_data = "1";  

				if ((com_TargetVOLGPval.value == "" )) {
					alert(" TARGET GP kosong, cek kemabali " + kec + " Harus Diisi.. " );
					return document.hasil = false;
					keb = cek + 2;
				}		
			}
		}
		if (satu_data == "0") {
			alert("Minimal Pilih Satu Data...");
			return document.hasil = false;
		}
		return document.hasil = true;
}
function checkForother(obj,kei) {  
	if (!document.layers) {  
          
		var TargetVOLGPval = 'KONTRAVOL'+kei;
                var com_TargetVOLGPval = document.getElementById(TargetVOLGPval);
        	 var STATUSval = 'STATUSval'+kei;
		var com_STATUSval = document.getElementById(STATUSval);
                var rowke = 'rowke'+kei;
		var com_rowke = document.getElementById(rowke);
		if (obj.value == "0") {  
			com_TargetVOLGPval.disabled="";
                        com_STATUSval.disabled="";
        		obj.value = "1"; 
		} else {  
			com_TargetVOLGPval.disabled="disabled"; 
                        com_STATUSval.disabled="disabled";
        		obj.value = "0";  
			
		}  
	} 
} 

function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }

function IsNumeric2(obj,volIndex)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.";
   var strChar;
   var strString = obj.value;
   var valVolIndex = volIndex;
   //alert ("dsda "+valVolIndex);
   if (strString.length == 0){
        alert("Harus Diisi Angka..!!!");
	 return false;
//	} else if (strString > valVolIndex){
//            alert("Adjusmant tidak boleh lebih dari Indexnya ..!!!");
//            return false;            
        }else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }

</script> 
<body>
<script type="text/javascript" language="JavaScript">
    document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
</script>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Data Target GP</th>
</tr></table>
</div>

    <form  id="form1" name="form1" method="post" action="<?=$targetVolume;?>">
	   <table width="734" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
	     </tr>
		<tr>
		  <td class="puso">&nbsp;&nbsp;&nbsp;PERIODE</td>
		  <td class="puso">:</td>
		  <td><select name="bulanVolum" id="bulanVolum">
            <option value='00'>--Pilih Bulan--</option>
            <option value='01'>Januari</option>
            <option value='02'>Pebruari</option>
            <option value='03'>Maret</option>
            <option value='04'>April</option>
            <option value='05'>Mei</option>
            <option value='06'>Juni</option>
            <option value='07'>Juli</option>
            <option value='08'>Agustus</option>
            <option value='09'>September</option>
            <option value='10'>Oktober</option>
            <option value='11'>Nopember</option>
            <option value='12'>Desember</option>
          </select>
	      <input name="tahunVolum" type="text" id="tahunVolum" size="4" maxlength="10" onBlur="javascript:IsNumeric(this)" />
                <span style="color: red;">*</span>
          </td>
         </tr>
		<tr>
		<td width="159" class="puso">&nbsp;&nbsp;&nbsp;PLANT</td>
		<td width="28" class="puso">:</td>
		<td width="529">
                    <select name="nmplant" id="nmplant">
                        <option value='00'>--Pilih Plant--</option>
                        <?
                        $sqlPlant= "select KD_PLANT,NAME from ZREPORT_M_PLANT where ORG='$com' and
                                    KD_PLANT IN 
                                    (
                                    select DISTINCT PLANT from(
                                    select DISTINCT PLANT from ZREPORT_RPT_REAL
                                    )INNER JOIN ZREPORT_TARGET_PLANT ON(

                                    KD_PLANT=PLANT
                                    )
                                    )
                                    order by KD_PLANT ASC";

                        //echo $sqlPlant;
                        $query2= oci_parse($conn, $sqlPlant);
                        oci_execute($query2);
                 
                        while($rowPlant=oci_fetch_array($query2)){ 
                            $KDplant=$rowPlant['KD_PLANT'];
                            $NAMEplant=$rowPlant['NAME'];
                        ?>
                             <option value='<?=$KDplant;?>'><?=$KDplant;?>&nbsp;&nbsp;<?=$NAMEplant;?></option>
                        <?
                        }
                        ?>
                    </select><span style="color: red;">*</span>
          </td>
		</tr>
               	<tr>
		<td width="159" class="puso">&nbsp;&nbsp;&nbsp;TIPE</td>
		<td width="28" class="puso">:</td>
		<td width="529">
                    <select name="nmpTIPE" id="nmpTIPE">
                        <option value='00'>--Pilih Tipe--</option>
                             <option value='121-301'>ZAK</option>
                             <option value='121-302'>CURAH</option>
                    </select><span style="color: red;">*</span>
                 </td>
		</tr>
               <tr>
		<td width="159" class="puso">&nbsp;&nbsp;&nbsp;PLANT GP</td>
		<td width="28" class="puso">:</td>
		<td width="529">
                    <select name="nmplantGP" id="nmplantGP">
                        <option value='00'>--Pilih Plant GP--</option>
                        <?
                        $sqlPlantGP= "
                            SELECT KUNNR,NAME1,BZIRK,BZTXT,BRAN1,BRANTXT FROM M_CUSTOMER 
                            where KUNNR like '0000002%' and BRAN1 is NOT NULL                            
                        ";
                        //echo $sqlPlantGP;
                        $query2GP= oci_parse($conn, $sqlPlantGP);
                        oci_execute($query2GP);
                 
                        while($rowPlantGP=oci_fetch_array($query2GP)){ 
                            $KDplantGP=$rowPlantGP['KUNNR'];
                            $NAMEplantGP=$rowPlantGP['NAME1'];
                            $BZIRKplantGP=trim($rowPlantGP['BZIRK']);
                            $BRAN1plantGP=trim($rowPlantGP['BRAN1']);
                        ?>
                             <option value='<?=$KDplantGP."#".$BZIRKplantGP."#".$BRAN1plantGP;?>'><?=$KDplantGP;?>&nbsp;&nbsp;<?=$NAMEplantGP;?></option>
                         
                        <?
                        }
                        ?>
                    </select><span style="color: red;">*</span>
                 </td>
		</tr>
                            
                <tr>
		<td width="159" class="puso">&nbsp;&nbsp;&nbsp;TARGET GP</td>
		<td width="28" class="puso">:</td>
		<td width="529">
                    <input name="targetGPVolum" type="text" id="targetGPVolum" size="10" maxlength="20" onBlur="javascript:IsNumeric2(this)" />
                    <span style="color: red;">*</span>
                 </td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
                <input name="Submit" type="submit" class="button" value="Submit"/>
		<input type="hidden" id="user_id" name="user_id" value="<?=$user_id?>" />
       	  </td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
	  </table>
</form>
<p></p>
<div align="center">
    <table width="734" align="center" class="adminlist">
	<tr>
         <form id="form2" name="form2" method="post" action="<?=$targetVolume;?>">   
           <th align="left" colspan="7">
                <span class="style5">&nbsp;Tabel Target GP  >> 
                <?php
                
                     if ($total >0){                        
                        echo  "Bulan: ".$bulanNow." Tahun: ".$tahunNow." COM: $com";
                    }
                ?>   
                </span> 
           <? if($total>0){ ?>
           <span style="float:right;">
            <?
            $arrayGab=array();
            for($r=0; $r<$total;$r++) {
            $arrayGab[]= $KD_PLANT_GP[$r];
            }
            $uniqArray=array_unique($arrayGab);
            ?>
            <select name="filterKota">
            <option value='00'>--Pilih PLant GP--</option>   
            <?   
            foreach ($uniqArray as $unik => $keyUnik)
            {    
            ?>
            <option value='<? echo $keyUnik;?>'><? echo $keyUnik;?></option>
            <? } ?>    
            </select>    
                <input name="Filter" type="submit" class="button" value="Filter" />      
            </span>
           <? } ?>
           </th>    
        </form>
      </tr>
  </table>
</div> 
<div align="center">
<form  name="formVolume" id="formVolume" method="post" action="<?=$targetVolume;?>" onSubmit="cek_last('total');return document.hasil">
	<table id="test1" width="734" align="center" class="adminlist">
	<thead >
         <tr class="quote">
	   <td width="61"><input type="button" class="button" onClick="checkedAll('formVolume');" value="CEK" title="CEK ALL"></strong></td>
           <td width="34"><div align="center"><strong>No.</strong></div></td>
           <td width="107" align="center"><strong>PLANT </strong></td>
           <td width="107" align="center"><strong>TIPE </strong></td>
           <td width="135" align="center"><strong>KECAMATAN</strong></td>
           <td width="165" align="center"><strong>PLANT GP</strong></td>
           <td width="53" align="center"><strong>STATUS</strong></td>
           <td width="145" align="center"><strong>TARGET </strong></td>
          	   
	  </tr>
      </thead>
	  <tbody >
<?
if($total>0){
?>              
              
                    <?  for($i=0; $i<$total;$i++) {
                        $rowke="rowke".$i;
			if(($i % 2) == 0){	 
                            echo "<tr class='row0' id='$rowke' >";
			}
			else{	
			   echo "<tr class='row1' id='$rowke' >";
			}	
                        $no=$i+1;
                        $kei=$i;
                        $urutke="urutke".$i;
                        $TargetVOLGPval="KONTRAVOL".$i;
                        $STATUSval="STATUSval".$i;
                        $BRAN1val="BRAN1v".$i;
                        $PLANTval="PLANTv".$i;
                        $PLANTGPval="PLANTGPv".$i;
                        $BULANval="BULANv".$i;
                        $TAHUNval="TAHUNv".$i;
                        $TIPEval="TIPEv".$i;

                    ?>
			
      <td align="center">
                                <input type="checkbox" id="<?=$urutke;?>" name="<?=$urutke;?>" value="0" onChange="checkForother(this,'<?=$kei?>')" />
        </td>   
                            <td align="center"><? echo $no.".";?></td>
                            <td align="center"><? echo $PLANT[$i]; ?></td>
                            <td align="center"><? echo $TIPE[$i]; ?></td>
                            <td align="center"><? echo $BRAN12[$i]; ?></td>
                            <td align="center"><? echo $KD_PLANT_GP[$i]; ?></td>
                            <td align="center">
                            <select id="<?=$STATUSval;?>" name="<?=$STATUSval;?>" disabled="disabled">
                                            <?                                            
                                            if($STATUS[$i]==1)
                                            {   
                                            ?>                                                                               
                                                    <option   value='0' >Aktif</option>
                                                    <option  value='1' selected>Inaktif</option>
                                            <?        
                                            }else{
                                                ?>
                                                    <option   value='0' selected>Aktif</option>
                                                    <option  value='1'>Inaktif</option>
                                                <?
                                            }
                                            ?>
                                            
                                </select>
                            
                            </td>
                            <td align="center">
                            <input type="text" id="<?=$TargetVOLGPval;?>" name="<?=$TargetVOLGPval;?>" value="<? echo showNilai2($TARGET_GP[$i]);?>" onBlur="javascript:IsNumeric2(this,'<?=$TARGET_GP[$i];?>')" disabled="disabled"/>
                            </td>
                            <input id="userUpdate" name="userUpdate" type="hidden" value="<? echo $user_id;?>" />
                            <input id="<?=$PLANTval;?>" name="<?=$PLANTval;?>" type="hidden" value="<? echo $PLANT[$i]; ?>" />
                            <input id="<?=$BRAN1val;?>" name="<?=$BRAN1val;?>" type="hidden" value="<? echo $BRAN12[$i]; ?>" />
                            <input id="<?=$PLANTGPval;?>" name="<?=$PLANTGPval;?>" type="hidden" value="<? echo $KD_PLANT_GP[$i]; ?>" />
                            <input id="<?=$TIPEval;?>" name="<?=$TIPEval;?>" type="hidden" value="<? echo $TIPE[$i]; ?>" />
                            
                            <input id="<?=$BULANval;?>" name="<?=$BULANval;?>" type="hidden" value="<?=$BULAN[$i];?>" />
                            <input id="<?=$TAHUNval;?>" name="<?=$TAHUNval;?>" type="hidden" value="<?=$TAHUN[$i];?>" />


                            
			</tr>
		  <?
                  }
	
}else {
?>
        <tr class="row1"><td align="center" colspan="8"><? echo $komen;?></td></tr>
<?
}
 
if($total>0){ ?>
          <tr class="quote">
		<td colspan="8" align="center">
           	<input name="Save" type="submit" class="button" id="Save" value="Save" />
                <input name="Delete" type="submit" class="button" id="Delete" value="Delete" />
		<a href="<?=$targetVolume;?>" target="isi" class="button">Cancel</a>
                <input id="total" name="total" type="hidden" value="<?=$total;?>" />
            </td>
	    </tr>
<? } ?>        
	  </tbody>
  </table>

</form>

<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
