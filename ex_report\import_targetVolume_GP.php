<?php

/*
 * Import Target Adjusment dan Ko<PERSON>rak Volume.
 * @liyantanto
 */
?>
<?
session_start();
include ('../include/ex_fungsi.php');
require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
//$halaman_id=8;

$user_id=$_SESSION['user_id'];
//$user_id='mady';
$importtargetVolume='import_targetvolume_gp.php';
$waktu=date("d-m-Y");

function updateEks_GP(){
        //$ok = new SAPDataModule_Connection();
        //$sap = $ok->getConnSAP_Dev();
        
        $fungsi=new conntoracleDEVSD();
        $conn=$fungsi->DEVSDdb();
        
        //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; 
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        //$sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                 if ($sap->GetStatus() != SAPRFC_OK ) {
                 echo $sap->PrintStatus();
                 exit;
         }
        
        if($sap) {
            $fce = &$sap->NewFunction ("Z_ZAPPSD_INDEX_EXPEDITUR");
            if ($fce == false ) { $sap->PrintStatus(); exit; }
            $dt = date("dmY");
            $querySQL = "select a.* from ZREPORT_TARGET_EXP a where a.updated = to_date('".$dt."','DDMMYYYY') and BRAN1 is not NULL";
           // echo  $querySQL;   
            $query= oci_parse($conn, $querySQL);
            oci_execute($query);
//            echo "<pre>";
//            print_r($dataRec1);
//            echo "</pre>";
            while($dataRec1=oci_fetch_array($query)){ 
                $com = $dataRec1[COM];
                $plant = $dataRec1[PLANT];
                $no_expeditur=$dataRec1[NO_EXPEDITUR];
                $kota = $dataRec1[KOTA];
                $kode_kec = $dataRec1[BRAN1];
                $tipe = $dataRec1[ITEM_NO];
                $thnbln = $dataRec1[TAHUN].$dataRec1[BULAN];
                $index = $dataRec1[VOL_INDEX_GP];
                $adjustmant = $dataRec1[ADJUSTMANT_GP];
                $plant_gp = intval($dataRec1[PLANT_GP]);            
                $kontrak = $dataRec1[KONTRAK_VOL_GP];
                $petugas = $dataRec1[PETUGAS_GP];
                $status = $dataRec1[STATUS_GP];
                
                if(trim($tipe)=='121-301') $tipe = "ZAK";
                else $tipe = "TO";
                $t_data = array(
                "ZNMORG" => "$com",
                "SPMON" => $thnbln,
                "KUNNR" => $no_expeditur,
                "WERKS" => $plant,
                "BZIRK" => $kota,
                "KODE_KEC"=>$kode_kec,
                "BASME" => $tipe,
                "STATUS_INDEX" => $status,
                "GUDANG_GP" => $index,
                "ADJUSTMENT_GP"=>$adjustmant,
                "PLANT_B"=>$plant_gp,
                "UPDATE_BY" => $petugas,
                "UPDATE_DATE" => date("Ymd")
                ); 
                $fce->T_DATA->Append($t_data);
            }
            

            $fce->I_CHECK = "X";

            $fce->Call();	
            if ($fce->GetStatus() == SAPRFC_OK ) {	
                    return $fce->STATUS.$fce->STATUS2;
            } else 
                $fce->PrintStatus();
            }
        
}

############################# READ XLS ####################
error_reporting(E_ALL ^ E_NOTICE);
require_once 'excel_reader2.php';

if(isset ($_POST['Import'])){
       $allowedExts = "xls";
       $extension = end(explode(".", $_FILES["file"]["name"]));
        if ($extension==$allowedExts)
          {
         
            //echo "Upload: " . $_FILES["file"]["name"] . "<br />";
            $pecah=$_FILES["file"]["name"];
            $pecahTanda=explode("_", $pecah);
            //print_r($pecahTanda);
            $bulan = substr($pecahTanda[1], -10, -8)."<br/>"; 
            $tahun = substr($pecahTanda[1], -8, -4)."<br/>";
            $kd_plant = substr($pecahTanda[1], -4)."<br/>";
            
                 
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file']['tmp_name']);
            $jumlah = $cell->rowcount($sheet_index=0);
            $bulanF   = $cell->val( 2,1 );
            $tahunF   = $cell->val( 2,2 );
            $kd_plantF   = $cell->val( 2,3 );
            if(($bulanF!=null)&&($tahunF!=null)&&($bulanF!=$kd_plantF)){   
             $i = 4; // dimulai dari ke dua karena baris pertama berisi title
            while( $i<=$jumlah ){
               //$cell->val( baris,kolom )
               $NO_EXPEDITUR   = $cell->val( $i,2 );
               $NAMA_EXPEDITUR   = $cell->val( $i,3 );
               $KODE_KOTA   = $cell->val( $i,4 );
               $NAMA_KOTA    = $cell->val( $i,5 );
               $KECAMATAN    = $cell->val( $i,6 );
               $PLANT_GP    = $cell->val( $i,7 );
               $ADJUSTMANT   = $cell->val( $i,12 );
               $ADJUSTMANT = ereg_replace(',', '.', $ADJUSTMANT);
               $KONTRAK_VOLUME   = $cell->val( $i,13 );
               $KONTRAK_VOLUME = ereg_replace(',', '.', $KONTRAK_VOLUME);
               $STATUS   = $cell->val( $i,14 );
               
               $sqlUpdate2="UPDATE ZREPORT_TARGET_EXP SET ADJUSTMANT_GP=$ADJUSTMANT, KONTRAK_VOL_GP=$KONTRAK_VOLUME , PETUGAS='$user_id'
               , STATUS_GP=$STATUS,UPDATED=to_date('$waktu','DD-MM-YYYY')
               WHERE BULAN=$bulanF and TAHUN='$tahunF' and PLANT='$kd_plantF' and COM='2000' and 
               NO_EXPEDITUR=$NO_EXPEDITUR and KOTA='$KODE_KOTA' and BRAN1='$KECAMATAN'and BRAN1 is not null
               and PLANT_GP=$PLANT_GP
               ";
              // echo $sqlUpdate2."<br/>";
               $query2= oci_parse($conn, $sqlUpdate2);
               oci_execute($query2);
                
               $i++;
            }

             $messSAP=updateEks_GP();
            echo "<script>alert('Update (SAP $messSAP)Target Volume GP sukses...!!');</script>"; 
            }else{
                echo "<script>alert('Format file harus dicek kembali...!!');</script>";
            }
          }
        else
          {
               echo "<script>alert('Invalid file...!!');</script>";  
          }

    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Import Data Target Volume dan Index GP</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Import Data Target Volume dan Index GP</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="<?=$importtargetVolume;?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso">&nbsp;&nbsp;&nbsp;Silakan Pilih File Excel</td>
            <td class="puso">:</td>
            <td> <input name="file" type="file"  class="button"></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Import"></td>
        </tr>

    </table>
</form>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>