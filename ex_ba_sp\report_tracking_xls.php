<?
session_start();
include('../include/ex_fungsi.php');
// include('../include/or_fungsi.php');
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

// $fungsi = new or_fungsi();
// $conn = $fungsi->or_koneksi();

$halaman_id = 4871;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];
// print_r($_SESSION);
$mp_coics = $fungsi->getComin($conn, $user_org);
// if (count($mp_coics) > 0) {
//     unset($inorg);
//     $orgcounter = 0;
//     foreach ($mp_coics as $keyOrg => $valorgm) {
//         $inorg .= "'" . $keyOrg . "',";
//         $orgcounter++;
//     }
//     $orgIn = rtrim($inorg, ',');
// } else {
//     $orgIn = $user_org;
// }



function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

$vendor=$fungsi->ex_find_vendor($conn,$user_id);

$no_ba = $_POST['no_ba'];
$tgl_ba_start = $_POST['tgl_ba_start'];
$tgl_ba_end = $_POST['tgl_ba_end'];

$data = array();

if ($vendor == "" and $no_ba == "" and $tgl_ba_start == "" and $tgl_ba_end == "") {
    $sql= "SELECT DISTINCT EX_BA.*, to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1, TB_USER_BOOKING.NAMA_LENGKAP, EX_TRANS_HDR.WARNA_PLAT  FROM EX_BA LEFT JOIN EX_TRANS_HDR ON EX_TRANS_HDR.NO_BA = EX_BA.NO_BA LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL WHERE EX_BA.DELETE_MARK ='0' AND EX_BA.ORG in ($user_org) AND EX_BA.NO_BA IS NOT NULL AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ORDER BY EX_BA.ID DESC";
} else {
// START non-prepared statement
    // $pakeor = 0;
    // $sql= "SELECT DISTINCT EX_BA.*, to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1, TB_USER_BOOKING.NAMA_LENGKAP, EX_TRANS_HDR.WARNA_PLAT FROM EX_BA LEFT JOIN EX_TRANS_HDR ON EX_TRANS_HDR.NO_BA = EX_BA.NO_BA LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL WHERE ";
    // if($vendor!=""){
    //     $sql.=" EX_BA.NO_VENDOR LIKE '$vendor'";
    //     $pakeor=1;
    //     }
    //     if($no_ba!=""){
    //         if($pakeor==1){
    //         $sql.=" AND EX_BA.NO_BA LIKE '$no_ba' ";
    //         }else{
    //         $sql.=" EX_BA.NO_BA LIKE '$no_ba' ";
    //         $pakeor=1;
    //         }
    //     }

    //     if ($tgl_ba_start != "" && $tgl_ba_end != "") {
    //         if($pakeor==1){
    //             $sql.=" AND EX_BA.TGL_BA BETWEEN TO_DATE('$tgl_ba_start','YYYY-MM-DD') AND TO_DATE('$tgl_ba_end','YYYY-MM-DD') ";
    //         }else{
    //             $sql.=" EX_BA.TGL_BA BETWEEN TO_DATE('$tgl_ba_start','YYYY-MM-DD') AND TO_DATE('$tgl_ba_end','YYYY-MM-DD') ";
    //             $pakeor=1;
    //         }
    //     } else if ($tgl_ba_start != "" && $tgl_ba_end == "") {
    //         if($pakeor==1){
    //             $sql.=" AND EX_BA.TGL_BA >= TO_DATE('$tgl_ba_start','DD-MM-YYYY') ";
    //         }else{
    //             $sql.=" EX_BA.TGL_BA >= TO_DATE('$tgl_ba_start','DD-MM-YYYY') ";
    //             $pakeor=1;
    //         }
    //     } else if ($tgl_ba_start == "" && $tgl_ba_end != "") {
    //         if($pakeor==1){
    //             $sql.=" AND EX_BA.TGL_BA <= TO_DATE('$tgl_ba_end','DD-MM-YYYY') ";
    //         }else{
    //             $sql.=" EX_BA.TGL_BA <= TO_DATE('$tgl_ba_end','DD-MM-YYYY') ";
    //             $pakeor=1;
    //         }
    //     }

    // $sql.=" AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ";
    // $sql.=" AND EX_BA.DELETE_MARK ='0' AND EX_BA.ORG in ($user_org) AND EX_BA.NO_BA IS NOT NULL ORDER BY EX_BA.ID DESC";
    // $query = oci_parse($conn, $sql);
// END non-prepared statement

// START prepared statement
    $params = array();

    $sql = "SELECT DISTINCT 
                EX_BA.*, 
                TO_CHAR(EX_BA.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1, 
                TB_USER_BOOKING.NAMA_LENGKAP, 
                EX_TRANS_HDR.WARNA_PLAT 
            FROM EX_BA 
            LEFT JOIN EX_TRANS_HDR ON EX_TRANS_HDR.NO_BA = EX_BA.NO_BA 
            LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL 
            WHERE 1=1";

    if ($user_org) {
        $sql .= " AND EX_BA.ORG = :org";
        $params[':org'] = $user_org;
    } else {
        // paksa query tidak hasilkan data jika org kosong
        $sql .= " AND 1=0";
    }

    if ($vendor != "") {
        $sql .= " AND EX_BA.NO_VENDOR LIKE :vendor";
        $params[":vendor"] = $vendor;
    }

    if ($no_ba != "") {
        $sql .= " AND EX_BA.NO_BA LIKE :no_ba";
        $params[":no_ba"] = $no_ba;
    }

    if ($tgl_ba_start != "" && $tgl_ba_end != "") {
        $sql .= " AND EX_BA.TGL_BA BETWEEN TO_DATE(:tgl_start, 'YYYY-MM-DD') AND TO_DATE(:tgl_end, 'YYYY-MM-DD')";
        $params[":tgl_start"] = $tgl_ba_start;
        $params[":tgl_end"]   = $tgl_ba_end;
    } else if ($tgl_ba_start != "" && $tgl_ba_end == "") {
        $sql .= " AND EX_BA.TGL_BA >= TO_DATE(:tgl_start, 'DD-MM-YYYY')";
        $params[":tgl_start"] = $tgl_ba_start;
    } else if ($tgl_ba_start == "" && $tgl_ba_end != "") {
        $sql .= " AND EX_BA.TGL_BA <= TO_DATE(:tgl_end, 'DD-MM-YYYY')";
        $params[":tgl_end"] = $tgl_ba_end;
    }

    $sql .= " AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50')";
    $sql .= " AND EX_BA.DELETE_MARK = '0'";
    $sql .= " AND EX_BA.NO_BA IS NOT NULL";
    $sql .= " ORDER BY EX_BA.ID DESC";

    $query = oci_parse($conn, $sql);
    foreach ($params as $key => $val) {
        oci_bind_by_name($query, $key, $params[$key]);
    }

// END prepared statement
}
// echo $sql;
oci_execute($query);
// $status = array();
// $status_ba = array();
//         $date_approve_ba = array();
//         $user_approve_ba = array();
while($row=oci_fetch_array($query)){

      $com[]=$row[ORG];
            $no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
            $no_ba_v[]=$row[NO_BA];
            $filename[]=$row[FILENAME];
            $id[]=$row[ID];
            $no_invoice_v[]=$row[NO_INVOICE];
            $no_invoice_ex_v[]=$row[NO_INVOICE_EX];
            $vendor_v[]=$row[NO_VENDOR];
            $nama_vendor_v[]=$row[NAMA_VENDOR];
            $no_pajak_ex_v[]=$row[NO_PAJAK_EX];
            $tgl_invoice_v[]=$row[TGL_INVOICE];
            $tgl_ba_v[]=$row[TGL_BA];
            $klaim_semen_v[]=$row[KLAIM_SEMEN];
            $klaim_ktg_v[]=$row[KLAIM_KTG];
            $warna_plat_v[]=$row[WARNA_PLAT]; 
            $pdpks_v[]=$row[PDPKS]; 
            $pend_ktg_v[]=$row[PDPKK]; 
            $pajak_v[]=$row[PAJAK_INV];
            $total_klaim_v[]=$row[TOTAL_INV];
            $nama_lengkap_v[]=$row[NAMA_LENGKAP];
      $no_baf[]=$row[NO_BAF];
      $status[]=$row[STATUS_BA];
      // $filename[]=$row[FILENAME];
      $alasan_reject[]=$row[ALASAN_REJECT];
      $tipe_alasan[]=$row[TIPE_ALASAN];

      // $sqlBA = "select a.STATUS_BA as STATUS_BA_TRACK,a.CREATED_AT,a.CREATED_BY,c.NAMA_LENGKAP from EX_BA_TRACK a left join EX_BA b on a.NO_BA = b.NO_BA left join TB_USER_BOOKING c on a.CREATED_BY = c.ID where a.NO_BA = $row[NO_BA] ORDER BY a.ID ASC ";   
      //       $query_BA= @oci_parse($conn, $sqlBA);
      //       @oci_execute($query_BA);
      //       $row_BA=@oci_fetch_array($query_BA);  
      //       // print_r($row_BA);exit;
      //       $status_tracking_ba[]=$row_BA[STATUS_BA_TRACK];
            // if($status_tracking_ba==10){
            //     $name_v10[]= "OPEN";
            //     $date_tracking10[] = $row_BA[CREATED_AT];
            //     $user_tracking10[] = $row_BA[NAMA_LENGKAP];
            // }else if($status_id==20){
            //     $name_v20[]= "SUBMITTED";
            //     $date_tracking20[] = $row_BA[CREATED_AT];
            //     $user_tracking20[] = $row_BA[NAMA_LENGKAP];
            // }else if($status_id==30){
            //     $name_v[]= "WAITING APPROVAL KASIE";
            //     $date_tracking[] = $row_BA[CREATED_AT];
            //     $user_tracking[] = $row_BA[NAMA_LENGKAP];
            // }else if($status_id==40){
            //     $name_v[]= "WAITING KABIRO";
            //     $date_tracking[] = $row_BA[CREATED_AT];
            //     $user_tracking[] = $row_BA[NAMA_LENGKAP];
            // }else if($status_id==50){
            //     $name_v[]= 'COMPLETED';
            //     $date_tracking[] = $row_BA[CREATED_AT];
            //     $user_tracking[] = $row_BA[NAMA_LENGKAP];
            // }else {
            //     $name_v[]= "";
            // }
            
            // $keterangan_v[]=$row_BA[KOMENTAR_REJECT];
            // $status_id=$row_BA[STATUS_BA_INVOICE]; 

      $sqlS = "select NO_INVOICE, KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_BA = $row[NO_BA] and STATUS_BA_INVOICE = 110) ";   
            $query_s= @oci_parse($conn, $sqlS);
            @oci_execute($query_s);
            $row_s=@oci_fetch_array($query_s);  
            $no_invoice_ba_v[]=$row_s[NO_INVOICE];
            $keterangan_v[]=$row_s[KOMENTAR_REJECT];
            $status_id=$row_s[STATUS_BA_INVOICE]; 
            if($status_id==10){
                $status_name_v[]= "CREATE INVOICE";
            }else if($status_id==20){
                $status_name_v[]= "UPLOAD INVOICE";
            }else if($status_id==30){
                $status_name_v[]= "REVERSED";
            }else if($status_id==40){
                $status_name_v[]= "REJECTED";
            }else if($status_id==45){
                $status_name_v[]= "CANCEL PPL & INVOICE";
            }else if($status_id==50){
                $status_name_v[]= 'APPROVE CHECKLIST DOKUMEN';
            }else if($status_id==60){
                $status_name_v[]= 'GENERATE PPL';
            }else if($status_id==70){
                $status_name_v[]= 'SIMULATE & POSTING PPL';
            }else if($status_id==80){
                $status_name_v[]= 'REJECT BY MANAJER VERIFIKASI';
            }else if($status_id==90){
                $status_name_v[]= 'APPROVED  BY MANAJER VERIFIKASI';
            }else if($status_id==100){
                $status_name_v[]= 'REJECT BY SM VERIFIKASI';
            }else if($status_id==110){
                $status_name_v[]= 'APPROVED  BY SM VERIFIKASI';
            }else {
                $status_name_v[]= "";
            }
        // }
    }
    $total=count($no_ba_v);

if ($total > 0) {
    HeaderingExcel('Tracking Dokumen BASTP plant '.$user_org.' & vendor '.$vendor.'.xls');

    // Creating a workbook
    $workbook = new Workbook("-");
    // Adding format
    $format_bold = &$workbook->add_format();
    $format_bold->set_bold();
    // Creating the first worksheet
    $worksheet1 = &$workbook->add_worksheet('Tracking BA');
    //$worksheet1->set_column(1, 1, 40);
    //$worksheet1->set_row(1, 20);

    $worksheet1->write(0, 0, 'No', $format_bold);
    $worksheet1->write(0, 1, 'Org', $format_bold);
    $worksheet1->write(0, 2, 'BASTP', $format_bold);
    $worksheet1->write(0, 3, 'No Invoice', $format_bold);
    $worksheet1->write(0, 4, 'Status Invoice', $format_bold);
    $worksheet1->write(0, 5, 'Expeditur', $format_bold);
    $worksheet1->write(0, 6, 'Nama Expeditur', $format_bold);
    $worksheet1->write(0, 7, 'Tgl BA', $format_bold);
    $worksheet1->write(0, 8, 'Warna Plat', $format_bold);
    $worksheet1->write(0, 9, 'Klaim Semen', $format_bold);
    $worksheet1->write(0, 10, 'Klaim Kantong', $format_bold);
    $worksheet1->write(0, 11, 'PDPKS', $format_bold);
    $worksheet1->write(0, 12, 'Pend. Ktg', $format_bold);
    $worksheet1->write(0, 13, 'Sub Total', $format_bold);
    $worksheet1->write(0, 14, 'Pajak (PPN)', $format_bold);
    $worksheet1->write(0, 15, 'Total', $format_bold);
    $worksheet1->write(0, 16, 'Status Tracking Create BA', $format_bold);
    $worksheet1->write(0, 17, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 18, 'User Approve', $format_bold);

    $worksheet1->write(0, 19, 'Status Tracking Approval BA', $format_bold);
    $worksheet1->write(0, 20, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 21, 'User Approve', $format_bold);

    $worksheet1->write(0, 22, 'Status Tracking Verifikasi Admin Transportasi BA', $format_bold);
    $worksheet1->write(0, 23, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 24, 'User Approve', $format_bold);

    $worksheet1->write(0, 25, 'Status Tracking Approval Kasie Transportasi BA', $format_bold);
    $worksheet1->write(0, 26, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 27, 'User Approve', $format_bold);

    $worksheet1->write(0, 28, 'Status Tracking Approval Kabiro Transportasi BA', $format_bold);
    $worksheet1->write(0, 29, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 30, 'User Approve', $format_bold);

    $worksheet1->write(0, 31, 'Status Rejected BA', $format_bold);
    $worksheet1->write(0, 32, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 33, 'User Rejected', $format_bold);

    $worksheet1->write(0, 34, 'Status Reversed BA', $format_bold);
    $worksheet1->write(0, 35, 'Tanggal Status Update', $format_bold);
    $worksheet1->write(0, 36, 'User Reversed', $format_bold);
        // $status_ba = array();

    for($i=1; $i<=$total;$i++) {

        // $item = $data[$i - 1];

        $b=$i;

        $STATUS_BA_TRACK = '';
        
       
        
        $worksheet1->write($i, 0, $b);
        $worksheet1->write_string($i, 1, $com[$i - 1]);
        $worksheet1->write_string($i, 2, $no_ba_v[$i - 1]);
        $worksheet1->write_string($i, 3, $no_invoice_ba_v[$i - 1]);
        $worksheet1->write_string($i, 4, $status_name_v[$i - 1]);
        $worksheet1->write_string($i, 5, $vendor_v[$i - 1]);
        $worksheet1->write_string($i, 6, $nama_vendor_v[$i - 1]);
        $worksheet1->write_string($i, 7, $tgl_ba_v[$i - 1]);
        $worksheet1->write_string($i, 8, $warna_plat_v[$i - 1]);
        $worksheet1->write($i, 9, $klaim_semen_v[$i - 1]);
        $worksheet1->write($i, 10, $klaim_ktg_v[$i - 1]);
        $worksheet1->write($i, 11, $pdpks_v[$i - 1]);
        $worksheet1->write($i, 12,$pend_ktg_v[$i - 1]);
        $worksheet1->write($i, 13, number_format($total_klaim_v[$i - 1],2,",","."));
        $worksheet1->write($i, 14, number_format($pajak_v[$i - 1],2,",","."));
        $worksheet1->write($i, 15, number_format($total_klaim_v[$i - 1]+$pajak_v[$i - 1] ,2,",","."));

        $sqlchek = "select EB.*,  NAMA, NAMA_LENGKAP from EX_BA_TRACK EB
        JOIN TB_USER_BOOKING TUB ON TUB.ID = TO_NUMBER(EB.CREATED_BY)
        WHERE EB.NO_BA =  '".$no_ba_v[$i - 1]."' ORDER BY  EB.ID ASC";
        // echo $sqlchek;
        $querycek = oci_parse($conn, $sqlchek);
        oci_execute($querycek);
        while ($datafunc = oci_fetch_assoc($querycek)) {
            $nama_lengkap = $datafunc['NAMA_LENGKAP'];
            if($datafunc['CREATED_BY'] == '0'){
                $nama_lengkap = "SYSTEM";
            }

            if ($datafunc['STATUS_BA'] == '10') {
                $worksheet1->write_string($i, 16, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 17, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 18, $nama_lengkap);
                
            }elseif ($datafunc['STATUS_BA'] == '20') {
                $worksheet1->write_string($i, 19, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 20, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 21, $nama_lengkap);
            }elseif ($datafunc['STATUS_BA'] == '30') {
                $worksheet1->write_string($i, 22, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 23, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 24, $nama_lengkap);
            }elseif ($datafunc['STATUS_BA'] == '40') {
                $worksheet1->write_string($i, 25, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 26, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 27, $nama_lengkap);
            }elseif ($datafunc['STATUS_BA'] == '50') {
                $worksheet1->write_string($i, 28, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 29, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 30, $nama_lengkap);
            }elseif ($datafunc['STATUS_BA'] == '11') {
                $worksheet1->write_string($i, 31, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 32, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 33, $nama_lengkap);
            }elseif ($datafunc['STATUS_BA'] == '1') {
                $worksheet1->write_string($i, 34, $datafunc['VALUE_BA']);
                $worksheet1->write_string($i, 35, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 36, $nama_lengkap);
            }

        }

    }

    $workbook->close();
}
?>