<?php
$action_push=trim($_GET['action']);

$vkorg = $_GET['xvkorg'];
$xwerks = $_GET['xwerks'];
$audatfrom = $_GET['audat_from'];
$audatto = $_GET['audat_to'];
$vbeln = $_GET['vbeln'];

$audat_from = date('Y-m-d',strtotime($audatfrom));
$audat_to = date('Y-m-d',strtotime($audatto));

switch ($action_push) {
case "push_data_so":
    if($_GET['to'] == 'update')
        $url = "http://10.4.194.150/dev/sd/sdonline/service/update_socc_fios.php?XVKORG=$vkorg&XVBELN=$vbeln&edatu_from=$audat_from&edatu_to=$audat_to&XWERKS=$xwerks";
    else
        $url = "http://10.4.194.150/dev/sd/sdonline/service/send_socc_fios.php?XVKORG=$vkorg&XVBELN=$vbeln&edatu_from=$audat_from&edatu_to=$audat_to&XWERKS=$xwerks";

    $curl = curl_init();
    curl_setopt_array($curl, array(
        // dev
        // prod
        // CURLOPT_URL => "https://csms2-api.sig.id/sdonline/service/send_socc_fios.php?XVKORG=$vkorg&XVBELN=$vbeln&edatu_from=$audat_from&edatu_to=$audat_to&XWERKS=$xwerks",
        // CURLOPT_URL => 'http://skedul.sig.id/bi/skedul/silog/send_socc-prod.php?XVKORG='.$vkorg.'&XVBELN='.$vbeln.'&edatu_from='.$audat_from.'&edatu_to='.$audat_to.'&XWERKS='.$xwerks,
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_SSL_VERIFYPEER => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        // CURLOPT_POSTFIELDS => http_build_query($postData),
    ));

    $response = curl_exec($curl);

    if (curl_errno($curl)) {
        // Handle cURL error
        print_r ('cURL Error: ' . curl_error($curl));
    } else {
        // No cURL errors, process the response
        $data_output = json_decode($response, true);
    }

    curl_close($curl);
    // $data_output = json_decode($response, true);

    if($data_output!='No Data Found' && $data_output!='Belum Mapping Soldto!!'){
        if($data_output['result_silog'] == null)
            $pesan = "result_silog null";
        else
            $pesan = $data_output['result_silog']['message'];
    }else if ($data_output=='Belum Mapping Soldto!!') {
        $pesan='Gagal Push SO, Belum Mapping Soldto!!';
    }else{
        $pesan='Gagal Push SO, Data Tidak Ada.';
    }

    if(is_array($pesan)){
        foreach ($data_output['push_data'] as $push_data) {
            $pesan .= '<br><br>'; 
            foreach ($push_data as $key => $value) {
                $pesan .= "$key = $value,<br>";
            }
        }
    }
    print_r($pesan);

break;
default:
    echo '<p style="font-family: Courier New, Courier, monospace;">Page Not Found (404)</p>';
}

?>
