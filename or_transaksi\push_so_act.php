<?php
$action_push=trim($_GET['action']);

$vkorg = $_GET['xvkorg'];
$xwerks = $_GET['xwerks'];
$audatfrom = $_GET['audat_from'];
$audatto = $_GET['audat_to'];
$vbeln = $_GET['vbeln'];

$audat_from = date('Y-m-d',strtotime($audatfrom));
$audat_to = date('Y-m-d',strtotime($audatto));

switch ($action_push) {
case "push_data_so":
  //  $curl = curl_init();

    // curl_setopt_array($curl, array(
    // CURLOPT_URL => 'skedul.sig.id/bi/skedul/silog/send_socc.php?XVKORG=7900&XVBELN=0011010486,0011010481&edatu_from=2022-10-18&edatu_to=2022-10-19',
    // CURLOPT_RETURNTRANSFER => true,
    // CURLOPT_ENCODING => '',
    // CURLOPT_MAXREDIRS => 10,
    // CURLOPT_TIMEOUT => 0,
    // CURLOPT_FOLLOWLOCATION => true,
    // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    // CURLOPT_CUSTOMREQUEST => 'POST',
    // // CURLOPT_POSTFIELDS => array('XVKORG' => '7000','XWERKS' => '7403','audat_from' => '20222-10-18','audat_to' => '2022-10-18','VBELN' => '0011010929'),
    // ));


    // curl_setopt_array($curl, array(
    //     // dev
    //     CURLOPT_URL => 'https://skedul.sig.id/bi/skedul/silog/send_socc.php?XVKORG='.$vkorg.'&XVBELN='.$vbeln.'&edatu_from='.$audat_from.'&edatu_to='.$audat_to.'&XWERKS='.$xwerks,
	// 	//CURLOPT_URL => 'http://10.15.5.71/bi/skedul/silog/send_socc.php?XVKORG='.$vkorg.'&XVBELN='.$vbeln.'&edatu_from='.$audat_from.'&edatu_to='.$audat_to.'&XWERKS='.$xwerks,
    //     // prod
    //     // CURLOPT_URL => 'http://skedul.sig.id/bi/skedul/silog/send_socc-prod.php?XVKORG='.$vkorg.'&XVBELN='.$vbeln.'&edatu_from='.$audat_from.'&edatu_to='.$audat_to.'&XWERKS='.$xwerks,
    //     CURLOPT_RETURNTRANSFER => true,
    //     CURLOPT_ENCODING => '',
    //     CURLOPT_MAXREDIRS => 10,
    //     CURLOPT_TIMEOUT => 0,
    //     CURLOPT_FOLLOWLOCATION => true,
    //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //     CURLOPT_CUSTOMREQUEST => 'GET',
        
    // ));
     $curl = curl_init();
    // $url = 'https://10.4.194.97/bi_skedul/silog/send_socc.php?XVKORG=' . $vkorg . '&XVBELN=' . $vbeln . '&edatu_from=' . $audat_from . '&edatu_to=' . $audat_to . '&XWERKS=' . $xwerks;
    $url = 'https://dev-app.sig.id/dev/sd/sdonline/service/send_socc_fios.php?XVKORG=' . $vkorg . '&XWERKS=' . $xwerks. '&edatu_from=' . $audat_from . '&edatu_to=' . $audat_to .'&XVBELN=' . $vbeln;

curl_setopt_array($curl, array(
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'GET',
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => 0,
    // CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
    // CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2// gunakan versi TLS yang aman
));

$response = curl_exec($curl);

if (curl_errno($curl)) {
    echo 'Curl error: ' . curl_error($curl);
}

curl_close($curl);


// echo $response;

  ///  $response = curl_exec($curl);
    // var_dump($response);
    // die;
    // curl_close($curl);
    $data_output = json_decode($response, true);

    if($data_output!='No Data Found'){
        $pesansukses = $data_output['result_silog']['success'];
        if($pesansukses==true){
            // header('Content-Type: application/json; charset=utf-8');
            $output=$response;
            // print_r($output);
            $pesan=$pesansukses;
            // foreach ($data_output["push_data"] as $key => $value) {
            //     $pesan .= $value["so_no"];
            //     $pesan .= " ";
            // }
            // $pesan = $pesansukses." ".$pesan;
        }else{
            $output=$response;
            $pesan=$pesansukses;
            // $pesan='Gagal Push SO, Data Tidak Ada. 1';
            // $output='No Data Found 1 = ' . $pesansukses;
            // print_r($pesansukses);
        }
    // }else if($data_output=='No Data Found'){
    //     $pesan='Gagal Push SO, Data Tidak Ada.';
    //     print_r($pesan);
    }else{
        $pesan='Gagal Push SO, Data Tidak Ada. 2';
        $output='No Data Found 2';
        // print_r($pesan);
    }

    $returnmsg=$pesan."/".$output;
    print_r($returnmsg);
    // if($vbeln=='' or $vbeln==null){
    //     $pesan='inputkan parameter untuk mendapatkan return...';
    // }
    // print_r($pesan);

break;
}

?>
