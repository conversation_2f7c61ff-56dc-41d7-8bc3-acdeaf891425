<?php

/*
 * Import Target Adjusment dan <PERSON>rak Volume.
 * @liyantanto
 *  
 */
?>
<?
session_start();
include ('../include/ex_fungsi.php');
require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
//$halaman_id=8;

$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_id='mady';
$importtargetVolume='import_targetvolume.php';
$waktu=date("d-m-Y");
$comval='2000';
$itemval='121-301';

//Koneksi SAP
//require_once ("/opt/lampp/htdocs/sgg/include/connect/SAPDataModule_Connection.php");

function updateEks(){
        //$ok = new SAPDataModule_Connection();
       // $sap = $ok->getConnSAP_Dev();
        
        $fungsi=new conntoracleDEVSD();
        $conn=$fungsi->DEVSDdb();
        
        //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; 
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        //$sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                 if ($sap->GetStatus() != SAPRFC_OK ) {
                 echo $sap->PrintStatus();
                 exit;
         }
        
        if($sap) {
            $fce = &$sap->NewFunction ("Z_ZAPPSD_INDEX_EXPEDITUR");
            if ($fce == false ) { $sap->PrintStatus(); exit; }
            $dt = date("dmY");
            $querySQL = "select a.* from ZREPORT_TARGET_EXP a where a.updated = to_date('".$dt."','DDMMYYYY') and BRAN1 is NULL";
           // echo  $querySQL;   
            $query= oci_parse($conn, $querySQL);
            oci_execute($query);
//            echo "<pre>";
//            print_r($dataRec1);
//            echo "</pre>";

            while($dataRec1=oci_fetch_array($query)){ 
                $com = $dataRec1[COM];
                $plant = $dataRec1[PLANT];
                $no_expeditur=$dataRec1[NO_EXPEDITUR];
                $kota = $dataRec1[KOTA];
                $tipe = $dataRec1[ITEM_NO];
                $thnbln = $dataRec1[TAHUN].$dataRec1[BULAN];
                $index = $dataRec1[VOL_INDEX];
                $adjustmant = $dataRec1[ADJUSTMANT];
                $kontrak = $dataRec1[KONTRAK_VOL];
                $petugas = $dataRec1[PETUGAS];
                $status = $dataRec1[STATUS];
                
                if(trim($tipe)=='121-301') $tipe = "ZAK";
                else $tipe = "TO";
                $t_data = array(
                "ZNMORG" => "$com",
                "SPMON" => $thnbln,
                "KUNNR" => $no_expeditur,
                "WERKS" => $plant,
                "BZIRK" => $kota,
                "BASME" => $tipe,
                "STATUS_INDEX" => $status,
                "INDEKS" => $index,
                "ADJUSTMENT"=>$adjustmant,   
                "KONTRAK"=>$kontrak, 
                "UPDATE_BY" => $petugas,
                "UPDATE_DATE" => date("Ymd")
                ); 
                $fce->T_DATA->Append($t_data);

            }
            

            $fce->I_CHECK = "X";

            $fce->Call();	
            if ($fce->GetStatus() == SAPRFC_OK ) {	
                    return $fce->STATUS.$fce->STATUS2;
            } else 
                $fce->PrintStatus();
            }

        
}

############################# READ XLS ####################
error_reporting(E_ALL ^ E_NOTICE);
require_once 'excel_reader2.php';

if(isset ($_POST['Import'])){
       $allowedExts = "xls";
       $extension = end(explode(".", $_FILES["file"]["name"]));
        if ($extension==$allowedExts)
          {
         
            //echo "Upload: " . $_FILES["file"]["name"] . "<br />";
            $pecah=$_FILES["file"]["name"];
            $pecahTanda=explode("_", $pecah);
            //print_r($pecahTanda);
            $bulan = substr($pecahTanda[1], -10, -8)."<br/>"; 
            $tahun = substr($pecahTanda[1], -8, -4)."<br/>";
            $kd_plant = substr($pecahTanda[1], -4)."<br/>";
            
                 
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file']['tmp_name']);
            $jumlah = $cell->rowcount($sheet_index=0);
            $bulanF   = sprintf('%02d',$cell->val( 2,1 ));
            $tahunF   = sprintf('%04d',$cell->val( 2,2 ));
            $kd_plantF   = $cell->val( 2,3 );
            if(($bulanF!=null)&&($tahunF!=null)&&($bulanF!=$kd_plantF)){   
             $i = 4; // dimulai dari ke dua karena baris pertama berisi title
            while( $i<=$jumlah ){
               //$cell->val( baris,kolom )
               $NO_EXPEDITUR   = sprintf('%010d',$cell->val( $i,2 ));
               $NAMA_EXPEDITUR   = $cell->val( $i,3 );
               $KODE_KOTA   = $cell->val( $i,4 );
               $NAMA_KOTA    = $cell->val( $i,5 );
               $ADJUSTMANT   = $cell->val( $i,9 );
               $ADJUSTMANT = ereg_replace(',', '.', $ADJUSTMANT);
               $KONTRAK_VOLUME   = $cell->val( $i,10 );
               $KONTRAK_VOLUME = ereg_replace(',', '.', $KONTRAK_VOLUME);
 	        $VOLUME   = $cell->val( $i,7 );
               $VOLUME = ereg_replace(',', '.', $VOLUME);
               $VOL_INDEX   = $cell->val( $i,8 );
               $VOL_INDEX  = ereg_replace(',', '.', $VOL_INDEX);
               $STATUS   = $cell->val( $i,11 );
               $sqlUpdate2='';
		 $sqlcek="select count(NO_EXPEDITUR) AS JUMLAH from ZREPORT_TARGET_EXP WHERE BULAN='$bulanF' and TAHUN='$tahunF' and ITEM_NO='$itemval' and PLANT='$kd_plantF' and COM='$comval' and 
               NO_EXPEDITUR='$NO_EXPEDITUR' and KOTA='$KODE_KOTA' and BRAN1 is null and KODE_DA is null";
               $query2= oci_parse($conn, $sqlcek);
               oci_execute($query2);
               $row=oci_fetch_array($query2);
               $jumlahData=$row['JUMLAH'];
               if($jumlahData=='0'){
               $sqlUpdate2="
                   INSERT INTO ZREPORT_TARGET_EXP (
                   NO_EXPEDITUR, NAMA_EXPEDITUR, 
                   KOTA, NM_KOTA, BULAN, 
                   TAHUN,PETUGAS, PLANT, COM, ITEM_NO, UPDATED, 
                   VOLUME, VOL_INDEX, ADJUSTMANT, 
                   KONTRAK_VOL, STATUS) 
                   VALUES ( '$NO_EXPEDITUR' , '$NAMA_EXPEDITUR',
                    '$KODE_KOTA','$NAMA_KOTA', '$bulanF',
                    '$tahunF','$user_name' ,'$kd_plantF','$comval','$itemval',to_date('$waktu','DD-MM-YYYY'),
                     $VOLUME,$VOL_INDEX ,$ADJUSTMANT,
                   $KONTRAK_VOLUME,$STATUS)
                  ";               
               }else{	
               $sqlUpdate2="UPDATE ZREPORT_TARGET_EXP SET ADJUSTMANT=$ADJUSTMANT, KONTRAK_VOL=$KONTRAK_VOLUME , PETUGAS='$user_name'
               , STATUS=$STATUS, UPDATED=to_date('$waktu','DD-MM-YYYY')
               WHERE BULAN='$bulanF' and TAHUN='$tahunF' and PLANT='$kd_plantF' and COM='$comval' and 
               NO_EXPEDITUR='$NO_EXPEDITUR' and KOTA='$KODE_KOTA' and BRAN1 is null and KODE_DA is null
               ";
		}
               //echo $sqlUpdate2."<br/>";
		 //exit;
               $query2= oci_parse($conn, $sqlUpdate2);
               oci_execute($query2);
                
               $i++;
            }
            $messSAP=updateEks();
            echo "<script>alert('Update (SAP $messSAP)Target Volume sukses...!!');</script>"; 
            }else{
                echo "<script>alert('Format file harus dicek kembali...!!');</script>";
            }
          }
        else
          {
               echo "<script>alert('Invalid file...!!');</script>";  
          }

    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Import Data Target Volume dan Index</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Import Data Target Volume dan Index</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="<?=$importtargetVolume;?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso">&nbsp;&nbsp;&nbsp;Silakan Pilih File Excel</td>
            <td class="puso">:</td>
            <td> <input name="file" type="file"  class="button"></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Import"></td>
        </tr>

    </table>
</form>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>