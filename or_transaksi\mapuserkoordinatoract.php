<?php

session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);
include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';


$koordinator_kode = htmlspecialchars($_REQUEST['KOORDINATOR_KODE']);
$username = strtoupper(htmlspecialchars($_REQUEST['USERNAME']));
$deskripsi = htmlspecialchars($_REQUEST['DESKRIPSI']);

$del = htmlspecialchars($_REQUEST['DEL']);

$ids = htmlspecialchars($_REQUEST['id']);

if(isset ($aksi)){
    switch($aksi) { 
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 2;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[1]) && empty($row[2])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1]) || empty($row[2])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, $row[1], $row[2])) {
                        $messageRows['database'][] = $rowNumber;
                        continue;
                    }else {
                        // Jika tidak ada masalah, lakukan upload
                        if (insert($conn, $row[1], $row[2], $user_name)) {
                            $messageRows['success'][] = $rowNumber;
                        } else {
                            $messageRows['system'][] = $rowNumber;
                        }
                    }

                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;
        case 'show' :
            {
                displayData($conn);
            }
        break;
        case 'add' :
            {
                $sqlcheck = "SELECT COUNT (*) AS JUMLAH FROM ZMD_USER_VS_KOORDINATOR WHERE KOORDINATOR_KODE = '$koordinator_kode' AND USERNAME = '$username'";
                $queryCheck = oci_parse($conn, $sqlcheck);
                oci_execute($queryCheck);
                $rowCheck = oci_fetch_array($queryCheck);
                if($rowCheck['JUMLAH']<1 or $rowCheck['JUMLAH']==0){
                    // $sql = "INSERT INTO ZMD_USER_VS_KOORDINATOR(KOORDINATOR_KODE, DESKRIPSI, USERNAME, DEL, INSERT_BY, INSERT_DATE)
                    // VALUES ('$koordinator_kode','$deskripsi','$username','0','$user_name','SYSDATE')";                            
                    // $query = oci_parse($conn, $sql);
                    // $result = oci_execute($query);

                    $sql = "INSERT INTO ZMD_USER_VS_KOORDINATOR
                    (KOORDINATOR_KODE, USERNAME, DEL, INSERT_BY, INSERT_DATE)
                    VALUES (:koordinator_kode, :username, '0', :insert_by, SYSDATE)";

                    $query = oci_parse($conn, $sql);

                    // Binding variabel ke parameter SQL
                    oci_bind_by_name($query, ':koordinator_kode', $koordinator_kode);
                    oci_bind_by_name($query, ':username', $username);
                    oci_bind_by_name($query, ':insert_by', $user_name);

                    // Eksekusi query
                    $result = oci_execute($query);


                    if ($result){
                        echo json_encode(array('success'=>true));
                    } else {
                        echo json_encode(array('errorMsg'=>'Some errors occured.'));
                    }                
                }else{
                    echo json_encode(array('errorMsg'=>'Data sudah ada.'));
                }
            }
        break;
        case 'delete' :
        {
            if($del=='Aktif'){
                $sql = "UPDATE ZMD_USER_VS_KOORDINATOR set DEL = '1' WHERE ID = '$ids'";

                $query = oci_parse($conn, $sql);
                $result = oci_execute($query);
 
                if ($result){
                    echo json_encode(array('success'=>$sql.$del));
                } else {
                     echo json_encode(array('errorMsg'=>$sql));
                }          
            } else {
                $sqlcheck = "SELECT COUNT (*) AS JUMLAH FROM ZMD_USER_VS_KOORDINATOR WHERE koordinator_kode = '$koordinator_kode' AND username = '$username' and del='0'";
                $queryCheck = oci_parse($conn, $sqlcheck);
                oci_execute($queryCheck);
                $rowCheck = oci_fetch_array($queryCheck);
                if($rowCheck['JUMLAH']<=1 or $rowCheck['JUMLAH']==0){
                    $sql = "UPDATE ZMD_USER_VS_KOORDINATOR set DEL = '0' WHERE ID = '$ids'";

                    $query = oci_parse($conn, $sql);
                    $result = oci_execute($query);
     
                    if ($result){
                        echo json_encode(array('success'=>$sql.$del));
                    } else {
                        echo json_encode(array('errorMsg'=>$sql));
                    }
                }else{
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }                
            }
        }
        break;
        case 'update' :
        {
            $sqlcheck = "SELECT COUNT (*) AS jumlah FROM ZMD_USER_VS_KOORDINATOR WHERE KOORDINATOR_KODE = '$koordinator_kode' AND USERNAME = '$username'";
            $queryCheck = oci_parse($conn, $sqlcheck);
            oci_execute($queryCheck);
            $rowCheck = oci_fetch_array($queryCheck);
            if($rowCheck['JUMLAH']<1 or $rowCheck['JUMLAH']==0){
                $sql = "UPDATE ZMD_USER_VS_KOORDINATOR 
                        SET KOORDINATOR_KODE = :koor_area, 
                            USERNAME = :username, 
                            UPDATE_BY = :update_by,
                            UPDATE_DATE = SYSDATE
                        WHERE ID = :id";

                $query = oci_parse($conn, $sql);

                // Binding parameter ke variabel PHP
                oci_bind_by_name($query, ':koor_area', $koordinator_kode);
                oci_bind_by_name($query, ':username', $username);
                oci_bind_by_name($query, ':update_by', $user_name);
                oci_bind_by_name($query, ':id', $ids);

                // Eksekusi query
                $result = oci_execute($query);                
                
                if ($result){
                    echo json_encode(array('success'=>'sukses update data'));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }                
            }else{
                echo json_encode(array('errorMsg'=>'gagal simpan, data mapppingan sudah ada.'));
            }                
        }
        break;
    }
}

function displayData($conn){
    if($conn){
        $sql = "SELECT id, KOORDINATOR_KODE, USERNAME, INSERT_DATE, INSERT_BY, UPDATE_BY, UPDATE_DATE, (CASE WHEN DEL = '0' then 'Aktif'
 Else 'Nonaktif' End) as 
        DEL  FROM ZMD_USER_VS_KOORDINATOR order by id asc";
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID']          = $row['ID'];
            $result[$i]['KOORDINATOR_KODE']          = $row['KOORDINATOR_KODE'];
            $result[$i]['USERNAME']          = $row['USERNAME'];
            $result[$i]['DEL']          = $row['DEL'];
            $result[$i]['INSERT_BY']          = $row['INSERT_BY'];
            $result[$i]['INSERT_DATE']          = $row['INSERT_DATE'];
            $result[$i]['UPDATE_BY']          = $row['UPDATE_BY'];                        
            $result[$i]['UPDATE_DATE']          = $row['UPDATE_DATE'];
            $i++;
        }
        echo json_encode($result);
        
    }
}

function checkDuplicateData($conn,$koor_area,$username) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM ZMD_USER_VS_KOORDINATOR
                WHERE 
                    KOORDINATOR_KODE = :KOORDINATOR_KODE
                    AND USERNAME = :USERNAME
                    -- AND KODE_REGION = :AREA
                ";

    $query_count = oci_parse($conn, $sql_count);
    oci_bind_by_name($query_count, ':KOORDINATOR_KODE', $koor_area);
    oci_bind_by_name($query_count, ':USERNAME', $username);
    // oci_bind_by_name($query_count, ':AREA', $area);

    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;

    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function insert($conn,$koor_area,$username,$created_by){
    $uname=strtoupper($username);
    $sql = "INSERT INTO ZMD_USER_VS_KOORDINATOR
    (KOORDINATOR_KODE,  USERNAME, DEL, INSERT_BY, INSERT_DATE)
    VALUES (:koordinator_kode, :username, '0', :insert_by, SYSDATE)";

    $query = oci_parse($conn, $sql);

    // Binding variabel ke parameter SQL
    oci_bind_by_name($query, ':koordinator_kode', $koor_area);
    oci_bind_by_name($query, ':username', $uname);
    oci_bind_by_name($query, ':insert_by', $created_by);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function adjustRowNumber($num) {
    return $num - 2;
}


?>
