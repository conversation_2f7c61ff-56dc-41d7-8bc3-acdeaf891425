<?php
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

/*
 * @liyantanto
 */
session_start();
include ('../include/or_fungsi.php');

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$dist=sprintf("%010s",$_SESSION['distr_id']);

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$soldto2=$fungsi->sapcode($distr_id);

$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
//$com = htmlspecialchars($_REQUEST['ORG']);
$soldto = $dist;//htmlspecialchars($_REQUEST['SOLD_TO']);
$tipesemen = htmlspecialchars($_REQUEST['TIPE_SEMEN']);
if($tipesemen == 'ZAK'){
    $tipesemen = '121-301';
} else if($tipesemen == 'TO'){
    $tipesemen = '121-302';
}
$distrik_add = htmlspecialchars($_REQUEST['DISTRIK']);
$tgl_target = htmlspecialchars($_REQUEST['periode']);
$bulan = substr($tgl_target,5,2); // 2018-10-21
$tahun = substr($tgl_target,0,4);
// $tahun = htmlspecialchars($_REQUEST['TAHUN']);
$target = htmlspecialchars($_REQUEST['TARGET']);


$sold_to    = htmlspecialchars($_REQUEST['sold_to']);
$ID_REQ    = htmlspecialchars($_REQUEST['id']);
$statusH    = htmlspecialchars($_REQUEST['statusHoliday']);
$tipe_semen = htmlspecialchars($_REQUEST['tipe_semen']);
$distrik    = htmlspecialchars($_REQUEST['distrik']);
$periode    = htmlspecialchars($_REQUEST['periode']);

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');
}else{
   $orgIn= $user_org;
}

/*if($bulan=='JANUARI'){
    $bulan = '01';
}elseif($bulan=='FEBRUARI'){
    $bulan = '02';
}elseif($bulan=='MARET'){
    $bulan = '03';
}elseif($bulan=='APRIL'){
    $bulan = '04';
}elseif($bulan=='MEI'){
    $bulan = '05';
}elseif($bulan=='JUNI'){
    $bulan = '06';
}elseif($bulan=='JULI'){
    $bulan = '07';
}elseif($bulan=='AGUSTUS'){
    $bulan = '08';
}elseif($bulan=='SEPTEMBER'){
    $bulan = '09';
}elseif($bulan=='OKTOBER'){
    $bulan = '10';
}elseif($bulan=='NOVEMBER'){
    $bulan = '11';
}elseif($bulan=='DESEMBER'){
    $bulan = '12';
}*/
// $bulan = 10;
$datetemp = ($bulan."/01/".$tahun);
$now = (date("m")."/01/".date("Y"));
$diff = floor(strtotime($datetemp) - strtotime($now)) / 2592000;

 function callAPI($method, $url, $param)
    {
        $content = ($method==='POST') ? json_encode($param): '';
        $options = array(
                'http' => array(
                        'header'  => "Content-type: application/json\r\n",
                        'method'  => $method,
                        'content' => $content,
                )
        );
        $context    = stream_context_create($options);
        $result     = @file_get_contents( $url, false, $context );
        $response   = json_decode($result);
        return json_encode($response);
    }

    // function getSSLPage($url) {
    //     $ch = curl_init();
    //     curl_setopt($ch, CURLOPT_HEADER, false);
    //     curl_setopt($ch, CURLOPT_URL, $url);
    //     curl_setopt($ch, CURLOPT_SSLVERSION,3); 
    //     $result = curl_exec($ch);
    //     curl_close($ch);
    //     return $result;
    // }

function updateDataHoliday($conn, $data_trgt){
    $insert=0;
    $update=0;
    $data_expired = 0;
    $h = 0;
    $user_name=$_SESSION['user_name'];

    $msgRow = "";

    foreach ($data_trgt as $key => $value) { 
        // $kode_material    = '';
        $brand = '';
        $org_md = '';
        $plant_md = '';
        $org_opco = '';
        $plant_opco = '';
        $royalty = '';
        // $username2   = '';

        //jika ada yg kosong
        // if( trim($value[2]) != "" && trim($value[3]) != ""){
            // if($soldto != '0000000000'){
                // $kode_material    = trim($value[2]);
                $brand            = trim($value[2]);
                $org_md            = trim($value[3]);
                $plant_md            = trim($value[4]);
                $org_opco            = trim($value[5]);
                $plant_opco            = trim($value[6]);
                $royalty            = trim($value[7]);
                // $username2 = trim($value[4]);
                // } else {
                    // $soldtox    = trim($value[2]);
                    // $tipesemenx = trim($value[3]);
                    // $distrikx   = trim($value[4]);
                    // $tgltargetx = trim($value[5]);
                    // $targetx    = trim($value[6]);
                    // }
                    
                    // $array1=explode("-",$tgltargetx);
                    // $tahun=$array1[0];
                    // $bulan=$array1[1];
                    
                    // $periodex = $bulan.''.$tahun;
            
            unset($val); 
            // $val['KODE_MATERIAL']     = $kode_material;
            $val['BRAND']  = strtoupper($brand);
            $val['ORG_MD']  = strtoupper($org_md);
            $val['PLANT_MD']  = strtoupper($plant_md);
            $val['ORG_OPCO']  = strtoupper($org_opco);
            $val['PLANT_OPCO']  = strtoupper($plant_opco);
            $val['IS_ROYALTY']  = strtoupper($royalty);
            // $val['USERNAME2']     = $username2;
            $val['CREATED_BY'] = $user_name;
            $val['CREATED_AT'] = date('Y-m-d H:i:s');
            $val['UPDATED_BY'] = $user_name;
            $val['UPDATED_AT'] = date('Y-m-d H:i:s');
           
            $ins=false; $upd=false;
                            

            // $tgl_data = ($bulan."/01/".$tahun);
            // $sekarang = (date("m")."/01/".date("Y"));
            // $batas_tambah = floor(strtotime($tgl_data) - strtotime($sekarang)) / 2592000;
            // echo $batas_tambah." <==> ";
            // if ($batas_tambah > 0) {
             
            if ($brand != '') {
                $data_cek = CekSelectData($conn,$val);
                if ($data_cek['JUMLAH'] > 0){
                    // $upd= UpdateData($conn,$val);
                    // $update++;
                    $msgRow .= ($key + 1).". Duplicate Data, gagal insert data <br>";
                    $data_expired++;
                } else {
                    $ins= InsertData($conn,$val);
                    if ($ins["status"] == 200) {
                        $insert++;
                    }else {
                        $msgRow .= ($key+1).". ".$ins["message"]."<br>";
                    }
                }
            }
            // } else {
            //     $data_expired = 1;
            // }
        // } //end if kosong
        
    }  //end foreach
    //============================================================================
    $msg["msg"]="";
    $msg["status"] = 500;
    if ($insert>0) {
        $msg["status"] = 200;
        $msg["msg"] .= $insert." Insert Data ";
    }else {
        if ($data_expired > 0) {
            $msg["status"]=200;
            $msg["msg"] .= $msgRow;
        } else {
            $msg["status"] = 500;
            $msg["msg"] .= $msgRow;
        }
    }
    // if ($update>0) {
    //     $msg["status"] = 200;
    //     $msg["msg"] .= $update." Update Data  ";
    // }
    
    return $msg;
}

function updateDataHolidaycc($conn, $data_trgt, $soldto){
    $insert=0;
    $update=0;
    $data_expired = 0;
    $h = 0;

    foreach ($data_trgt as $key => $value) { 
        $kd_kota    = '';
        $nm_kota = '';
        $ccmail   = '';

        //jika ada yg kosong
        // if( trim($value[2]) != "" && trim($value[3]) != ""){
            // if($soldto != '0000000000'){
                $kd_kota    = trim($value[2]);
                $nm_kota    = trim($value[3]);
                $ccmail     = trim($value[4]);
                // } else {
                    // $soldtox    = trim($value[2]);
                    // $tipesemenx = trim($value[3]);
                    // $distrikx   = trim($value[4]);
                    // $tgltargetx = trim($value[5]);
                    // $targetx    = trim($value[6]);
                    // }
                    
                    // $array1=explode("-",$tgltargetx);
                    // $tahun=$array1[0];
                    // $bulan=$array1[1];
                    
                    // $periodex = $bulan.''.$tahun;
            unset($val); 
            $dater = date('Y-m-d');
            $val['KD_KOTA']     = $kd_kota;
            $val['NM_KOTA']       = $nm_kota;
            $val['CC_EMAIL']     = $ccmail;
            $val['CREATED_BY']  = 'admin';
            $val['CREATED_AT']     = $dater;
           
            $ins=false; $upd=false;
                            

            // $tgl_data = ($bulan."/01/".$tahun);
            // $sekarang = (date("m")."/01/".date("Y"));
            // $batas_tambah = floor(strtotime($tgl_data) - strtotime($sekarang)) / 2592000;
            // echo $batas_tambah." <==> ";
            // if ($batas_tambah > 0) {
            if ($kd_kota != '') {
                $data_cek = CekSelectDatacc($conn,$val);
                if ($data_cek['JUMLAH'] > 0){
                    $upd= UpdateDatacc($conn,$val);
                    $update++;
                } else {
                    $ins= InsertDatacc($conn,$val);
                    $insert++;
                }
            }
                
            // } else {
            //     $data_expired = 1;
            // }
        // } //end if kosong
        
    }  //end foreach
    //============================================================================
    $msg="";
    if ($insert>0) {
        $msg .= $insert." Insert Data ";
    }
    if ($update>0) {
        $msg .= $update." Update Data  ";
    }
    if ($data_expired > 0) {
        $msg .= $data_expired." Gagal  ";
    }
    return $msg;
}

function CekSelectData($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_BRAND_PLANT
                    WHERE
                        BRAND = '".$data['BRAND']."' AND PLANT_MD = '".$data['PLANT_MD']."' AND PLANT_OPCO = '".$data['PLANT_OPCO']."' AND ORG_MD = '".$data['ORG_MD']."' AND ORG_OPCO = '".$data['ORG_OPCO']."' AND FLAG_DEL != 'Y' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function cekPlantOrg($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                        RFC_Z_ZAPP_SELECT_SYSPLAN
                    WHERE
                        XPARAM = '".$data['ORG']."' AND WERKS = '".$data['PLANT']."'";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function cekMasterBrand($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                        MASTER_BRAND
                    WHERE
                        BRAND = '".$data['BRAND']."'";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function CekSelectDataUpd($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_BRAND_PLANT
                    WHERE
                        BRAND = '".$data['BRAND']."' AND PLANT_MD = '".$data['PLANT_MD']."' AND PLANT_OPCO = '".$data['PLANT_OPCO']."' AND ORG_MD = '".$data['ORG_MD']."' AND ORG_OPCO = '".$data['ORG_OPCO']."' AND FLAG_DEL != 'Y' AND IS_ROYALTY = '".$data['ROYALTY']."' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];

    return $arData;
}


function CekSelectDatacc($conn,$data){
    $sql_select =  "SELECT
                        COUNT(KD_KOTA) AS JUMLAH
                    FROM
                        MAINTAIN_CC_EMAIL 
                    WHERE
                    KD_KOTA= '".$data['KD_KOTA']."' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function updatedata($conn,$data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d H:i:s');
    $sql1= "UPDATE MAPPING_BRAND_PLANT
            SET BRAND = '".$data_update['BRAND']."',
                ORG_MD = '".$data_update['ORG_MD']."',
                PLANT_MD = '".$data_update['PLANT_MD']."',
                ORG_OPCO = '".$data_update['ORG_OPCO']."',
                PLANT_OPCO = '".$data_update['PLANT_OPCO']."',
                IS_ROYALTY = '".$data_update['IS_ROYALTY']."',
                UPDATED_AT = SYSDATE,
                UPDATED_BY = '".$nama_user."'
            WHERE
                 BRAND = '".$data_update['BRAND']."' 
                 AND PLANT_MD = '".$data_update['PLANT_MD']."',
                 AND ORG_OPCO = '".$data_update['ORG_OPCO']."',
                 AND PLANT_OPCO = '".$data_update['PLANT_OPCO']."',
                 AND IS_ROYALTY = '".$data_update['IS_ROYALTY']."'";

    $query1 = oci_parse($conn, $sql1);
    $upd = oci_execute($query1);
      
    return $upd;
}

function updatedatacc($conn,$data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('d-m-Y');
    $sql1= "UPDATE MAINTAIN_CC_EMAIL 
            SET KD_KOTA = '".$data_update['KD_KOTA']."',
                NM_KOTA = '".$data_update['NM_KOTA']."',
                CC_EMAIL = '".$data_update['CC_EMAIL']."',
                UPDATED_AT = '".$date."',
                UPDATED_BY = '".$nama_user."'
            WHERE
                KD_KOTA = '".$data_update['KD_KOTA']."' ";

    $query1 = oci_parse($conn, $sql1);
    $upd = oci_execute($query1);
      
    return $upd;
}



function InsertData($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d');
        # code...
    if ($data_insert['BRAND'] != '') {
        foreach ($data_insert as $key => $value) {
            if ($key == "UPDATED_BY" || $key == "UPDATED_AT") {
                continue;
            }else {
                if ($key == "CREATED_BY") {
                    if (empty($value)) {
                        $res["status"] = 500;
                        $res["message"] = "Mohon login terlebih dahulu.";
            
                        return $res;
                    }
                }else{
                    if (empty($value)) {
                        $res["status"] = 500;
                        $res["message"] = "Mohon isikan data sesuai dengan keterangan template excel 🙏";
            
                        return $res;
                    }
                }
            }
        }
        
        $valMd["ORG"] = $data_insert["ORG_MD"];
        $valMd["PLANT"] = $data_insert["PLANT_MD"];
        
        $valOpco["ORG"] = $data_insert["ORG_OPCO"];
        $valOpco["PLANT"] = $data_insert["PLANT_OPCO"];
        
        $valBrand["BRAND"] = $data_insert["BRAND"];

        $cekPlantOrg1 = cekPlantOrg($conn, $valMd);
        $cekPlantOrg2 = cekPlantOrg($conn, $valOpco);
        
        $cekBrand = cekMasterBrand($conn, $valBrand);

        if ($cekPlantOrg1["JUMLAH"] == 0) {
            $res["status"] = 500;
            $res["message"] = "Mohon isikan data ORG dan Plant yang telah dimappingkan 🙏";

            return $res;
        }

        if ($cekPlantOrg2["JUMLAH"] == 0) {
            $res["status"] = 500;
            $res["message"] = "Mohon isikan data ORG dan Plant yang telah dimappingkan 🙏";

            return $res;
        }
        
        if ($cekBrand["JUMLAH"] == 0) {
            $res["status"] = 500;
            $res["message"] = "Mohon isikan data Brand yang tersedia pada master brand 🙏";

            return $res;
        }

        # code...
        $sql2 = "INSERT INTO MAPPING_BRAND_PLANT (PLANT_MD, PLANT_OPCO, ORG_MD, ORG_OPCO, BRAND, IS_ROYALTY, CREATED_AT, CREATED_BY, FLAG_DEL)
                 VALUES ('".$data_insert['PLANT_MD']."', '".$data_insert['PLANT_OPCO']."', '".$data_insert['ORG_MD']."', '".$data_insert['ORG_OPCO']."', '".$data_insert['BRAND']."', '".$data_insert['IS_ROYALTY']."', SYSDATE, '".$nama_user."','X')";
                //  var_dump($sql2);exit;
    
        $query2 = oci_parse($conn, $sql2);
        $ins = oci_execute($query2);


        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            $sap->PrintStatus();
            exit;
        }

        $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
        
        $fce->I_TRANSACTION_TYPE = "C";
        $fce->I_BRAND = $data_insert["BRAND"];
        $fce->I_PLANT = $data_insert["PLANT_MD"];
        $fce->I_NMORG = $data_insert["ORG_MD"];
        $fce->I_PLANT_OPCO = $data_insert["PLANT_OPCO"];
        $fce->I_NMORG_OPCO = $data_insert["ORG_OPCO"];
        $fce->I_IS_ROYALTI = $data_insert["IS_ROYALTY"] == "Y" ? "X" : "";
        
        $fce->Call();
        
        $fce->Close();
        $sap->Close();
    }    

    if ($ins) {
        $res["status"] = 200;
        $res["message"] = "Successfully upload data";
    }

    return $res;
}

function InsertDatacc($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    $date = date('d-m-Y');
   
        # code...
    $sql2 = "INSERT INTO MAINTAIN_CC_EMAIL (KD_KOTA, NM_KOTA, CC_EMAIL, CREATED_AT, CREATED_BY)
             VALUES ( '".$data_insert['KD_KOTA']."', '".$data_insert['NM_KOTA']."', '".$data_insert['CC_EMAIL']."', '".$date."', '".$nama_user."')";

    $query2 = oci_parse($conn, $sql2);
    $ins = oci_execute($query2);

    return $ins;
}

function findDataMappingBrandPlant($conn,$data){
    $sql_select =  "SELECT
                        *
                    FROM
                       MAPPING_BRAND_PLANT
                    WHERE
                        ID = '".$data['ID']."'";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    $row = oci_fetch_array($query);

    $arrData['BRAND'] = $row['BRAND'];
    $arrData['PLANT_MD'] = $row['PLANT_MD'];
    $arrData['ORG_MD'] = $row['ORG_MD'];
    $arrData['PLANT_OPCO'] = $row['PLANT_OPCO'];
    $arrData['ORG_OPCO'] = $row['ORG_OPCO'];
    $arrData['IS_ROYALTY'] = $row['IS_ROYALTY'];

    return $arrData;
}

$sql_select = "SELECT
                A.SOLD_TO,
                A.DISTRIK,
                A.PERIODE,
                A.TIPE_SEMEN,
                A.BULAN,
                A.TAHUN,
                B.NAME1,
                C.BZTXT ";

$sql_from   = "FROM
                (
                SELECT
                    ID,
                    SOLD_TO,
                    DISTRIK,
                    PERIODE,
                    TARGET,
                    TARGET_DATE,
                    TIPE_SEMEN AS ID_TIPE_SEMEN,
                CASE TIPE_SEMEN 
                    WHEN '121-301' THEN 'ZAK' 
                    WHEN '121-302' THEN 'CURAH' 
                    END AS TIPE_SEMEN,
                CASE
                    substr( PERIODE, 1, 2 ) 
                    WHEN '01' THEN 'JANUARI' 
                    WHEN '02' THEN 'FEBRUARI' 
                    WHEN '03' THEN 'MARET' 
                    WHEN '04' THEN 'APRIL' 
                    WHEN '05' THEN 'MEI' 
                    WHEN '06' THEN 'JUNI' 
                    WHEN '07' THEN 'JULI' 
                    WHEN '08' THEN 'AGUSTUS' 
                    WHEN '09' THEN 'SEPTEMBER' 
                    WHEN '10' THEN 'OKTOBER' 
                    WHEN '11' THEN 'NOVEMBER' 
                    WHEN '12' THEN 'DESEMBER' 
                    END AS BULAN,
                    substr( PERIODE, 3, 4 ) AS TAHUN 
                FROM
                    OR_TARGET_DISTR
                WHERE
                    delete_mark = 0 
                    AND update_mark = 0 ";

$sql_join       = " ) A
                LEFT JOIN RFC_Z_ZCSD_DIST B ON ( A.SOLD_TO = B.KUNNR )
                LEFT JOIN RFC_Z_ZAPPSD_BZIRK C ON ( A.DISTRIK = C.BZIRK ) ";

if(isset($aksi)){
switch($aksi) { 
  case 'show' :
    {
      
        $sql = "SELECT
        ma.*,
        CASE
            WHEN ma.IS_ROYALTY = 'Y' THEN 'YES'
            ELSE 'NO'
        END AS ROYALTY
    FROM
       MAPPING_BRAND_PLANT ma
    WHERE
        FLAG_DEL != 'Y' ";
        // var_dump($sql);
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $i=0;
        while($row=oci_fetch_array($query)){
            array_push($result, $row);
        }
        echo json_encode($result);
     }
     break;


     case 'showcc' :
        {
            $sql = "SELECT
            * 
            FROM
            MAINTAIN_CC_EMAIL";
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $i=0;
            while($row=oci_fetch_array($query)){
                array_push($result, $row);
            }
            echo json_encode($result);
         }
         break;

     case 'getShipto' :
        {   

            if (htmlspecialchars($_REQUEST['page']) != '') {
                $page = htmlspecialchars($_REQUEST['page']);
            }else{
                $page = 1;
            }

            if (htmlspecialchars($_REQUEST['row']) != '' ) {
                $rows = htmlspecialchars($_REQUEST['row']);
            }else{
                $rows = 10;
            }
            
            $offset = ($page - 1) * $rows;

            if (htmlspecialchars($_REQUEST['q']) != '') {
                $q =  htmlspecialchars($_REQUEST['q']);
            }else{
                $q = '';
            }

            $sql = "SELECT DISTINCT
            KUNN2,
            SHIPTO_NAME,
            SHIPTO_ADDR,
            NAME1,
            STRAS,
            BZIRK,
            BZTXT,
            VKBUR,
            BEZEB,
            KVGR1,
            PALLET,
            INCO1 
        FROM
            RFC_Z_ZCSD_SHIPTO 
        WHERE
            --  KUNNR = '{$soldto2}'
              KUNN2 like '%$q%' OR regexp_like(NAME1, '$q', 'i') ";

            // $mainQuery2 = $sql."limit $offset, $rows";
            // var_dump($sql);exit;
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $i=0;
            while($row=oci_fetch_array($query)){
                array_push($result, $row);
            }
            echo json_encode($result);
         }
         break;
         case 'getKota' :
            {   
    
                if (htmlspecialchars($_REQUEST['page']) != '') {
                    $page = htmlspecialchars($_REQUEST['page']);
                }else{
                    $page = 1;
                }
    
                if (htmlspecialchars($_REQUEST['row']) != '' ) {
                    $rows = htmlspecialchars($_REQUEST['row']);
                }else{
                    $rows = 10;
                }
                
                $offset = ($page - 1) * $rows;
    
                if (htmlspecialchars($_REQUEST['q']) != '') {
                    $q =  htmlspecialchars($_REQUEST['q']);
                }else{
                    $q = '';
                }
    
                $sql = "SELECT DISTINCT
                KD_KOTA,
                NM_KOTA
            FROM
                ZREPORT_M_KOTA 
            WHERE
                --  KUNNR = '{$soldto2}'
                  KD_KOTA like '%$q%' OR regexp_like(NM_KOTA, '$q', 'i') ";
    
                // $mainQuery2 = $sql."limit $offset, $rows";
                // var_dump($sql);exit;
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                $i=0;
                while($row=oci_fetch_array($query)){
                    array_push($result, $row);
                }
                echo json_encode($result);
             }
             break;
            case 'app1': {
                if (htmlspecialchars($_REQUEST['page']) != '') {
                    $page = htmlspecialchars($_REQUEST['page']);
                }else{
                    $page = 1;
                }
    
                if (htmlspecialchars($_REQUEST['row']) != '' ) {
                    $rows = htmlspecialchars($_REQUEST['row']);
                }else{
                    $rows = 10;
                }
                
                $offset = ($page - 1) * $rows;
    
                if (htmlspecialchars($_REQUEST['q']) != '') {
                    $q =  htmlspecialchars($_REQUEST['q']);
                }else{
                    $q = '';
                }
    
                $sql = "SELECT
                ID,
                NAMA,
                NAMA_LENGKAP,
                ALAMAT_EMAIL 
                FROM
                TB_USER_BOOKING
                WHERE 
                
                  NAMA like '%$q%' OR regexp_like(NAMA_LENGKAP, '$q', 'i')
                  ORDER BY ID DESC
                   ";
    
                // $mainQuery2 = $sql."limit $offset, $rows";
                // var_dump($sql);exit;
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                $i=0;
                while($row=oci_fetch_array($query)){
                    array_push($result, $row);
                }
                echo json_encode($result);

            }
            break;

            case "downloadTemplate":
                $table_name = "MAPPING_APPROVAL_HOLIDAY";
                $columns    = $model->getColumnsTable($table_name); 

                $path_xls =  "../include/excelwriter/";
                $files = array('OLEwriter.php', 'BIFFwriter.php', 'Worksheet.php', 'Workbook.php');
                foreach ($files as $file) {
                    require_once $path_xls . $file;
                }

                ob_end_clean();
                header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
                header("Content-Disposition:attachment;filename=Assign_KPI_&_Bobot_template.xls");
                header("Expires:0");
                header("Cache-Control:must-revalidate,post-check=0,pre-check=0");
                header("Pragma: public");

                $workbook   = new Workbook("-");
                $fBold      = $workbook->add_format(array('bold' => 1));

                # ======== sheets 1 ======== #
                $worksheetTemplate = &$workbook->add_worksheet('template');
                $headersTemplate = array();
                $indexPenting = array();
                $tmp_name = array("CODE_SHIPTO", "USERNAME1", "USERNAME2");
                // foreach ($columns as $key => $value) {
                //     # code...
                //     $col = $value['column_name'];
                //     if(in_array($key, $indexPenting)) {
                //         array_push($headersTemplate, $col);
                //     }
                // }

                $c=0;
                foreach ($headersTemplate as $value) {
                    $worksheetTemplate->write_string(0, $c++, $value, $fBold);
                }
                for ($i=1; $i < 5; $i++) { 
                    $worksheetTemplate->write_string($i, 0, '2'); // default value kpi_type = 2
                }

                # ======== sheets 2 ======== #
                $worksheetGuide = & $workbook->add_worksheet("Guide to fill this template");
                $header_konten = array("CODE_SHIPTO", "USERNAME1", "USERNAME2");
                $k = 0;
                foreach ($header_konten as $value) {
                    $worksheetGuide->write_string(0, $k++, $value, $fBold);
                }

                //** write guide main table */
                $indexPenting = array(1, 2, 3);
                $guide = array(
                    1=>"KODE SHIPTO, isi dengan kode SHIPTO",
                    2=>"Isi dengan Username 1 atau approval ke 1",
                    3=>"Isi dengan Username 2 atau approval ke 2",
                    
                );
                $headersTemplate = array();
                $index=0; $j = 1;
                $optionalIndex = array(7);
                foreach ($columns as $key => $value) {
                    # code...
                    $col = $value['column_name'];
                    if(in_array($key, $indexPenting)) {
                        $worksheetGuide->write_string($index+1, 0, $index+1);
                        $worksheetGuide->write_string($j, 1, $col);
                        $worksheetGuide->write_string($j, 2, $guide[$key]);
                        if (in_array($j, $optionalIndex)) {
                            $worksheetGuide->write_string($j, 3, 'Optional');
                        }else{
                            $worksheetGuide->write_string($j, 3, 'Required');
                        }
                        $index+=1;
                        $j++;
                    }
                }

                // //write master table
                // $table_names    = ['tlcc_pims_kpi_catalog'];
                // $masterData     = $model->getMasterData($table_names);

                // $row = 2; $column = 5;
                // foreach ($masterData as $key => $datas) {
                //     // write header
                //     $header_0 = array("KPI katalog ID", "Nama KPI");
                //     $headers = "header_{$key}";
                //     $cols = $column;
                //     foreach ($$headers as $i => $h_name) {
                //         if($i==0) {
                //             $nm = explode(' ID', $h_name);
                //             $worksheetGuide->write_string(0, $column, "Tabel Master {$nm[0]}", $fBold);
                //         }
                //         $worksheetGuide->write_string(1, $cols++, $h_name, $fBold);
                //     }

                //     // write content
                //     foreach ($datas as $key => $value) {
                //         $keys = array_keys($value);
                //         $worksheetGuide->write_string($row, $column, $value[$keys[0]]);
                //         $worksheetGuide->write_string($row, $column+1, $value[$keys[1]]);
                //         $row++;
                //     }

                //     $column+=3;
                //     $row = 2;
                // }

                $workbook->close();
                ob_end_clean();
                break;

    case 'data_target' :
    {
        $id = htmlspecialchars($_REQUEST['id']);

        $sql = $sql_select.", A.ID, A.ID_TIPE_SEMEN, A.TARGET, A.TARGET_DATE ".$sql_from." AND ID = $id ".$sql_join;

        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $i=0;
        while($row=oci_fetch_array($query)){
            array_push($result, $row);
        }
        echo json_encode($result);
     }
     break;
     case 'add' :
     {
        if ($user_org != '') {


                // get parameter 
                $shipto   = htmlspecialchars($_REQUEST['shipto']);
                $app1     = htmlspecialchars($_REQUEST['app1']);
                $app2     = htmlspecialchars($_REQUEST['app2']);
                $date     = $newDate = date("d-m-Y");
                

                            //cek apakah data sudah ada 
                            $cekdata = "SELECT ID FROM MAPPING_APPROVAL_HOLIDAY WHERE CODE_SHIPTO = '$shipto' ";
                            $cek = oci_parse($conn, $cekdata);
                            oci_execute($cek);
                            $hasilCek = oci_fetch_array($cek);


                            $user = $_SESSION['user_name'];
                            if (count($hasilCek[0]) < 1) {
                                # code...
                                $sql2 = "INSERT INTO MAPPING_APPROVAL_HOLIDAY ( CODE_SHIPTO, USERNAME1, USERNAME2, CREATED_AT, CREATED_BY,FLAG_DEL)
                                VALUES ('".$shipto."','".$app1."','".$app2."','".$date."','".$_SESSION['user_name']."','N')";
                                $query2 = oci_parse($conn, $sql2);
                                $ins = oci_execute($query2);

                                if ($ins){
                                    echo json_encode(array('success'=>true));
                                } else {
                                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                                }
                            
                            }else{
                                echo json_encode(array('errorMsg'=>'Duplicate Data.'));
                            }
                   

                    
           
        }else{
            echo json_encode(array('errorMsg'=>'silahkan login terlebih dahulu!!!'));
        }
     }
     break;

     case 'add_brand_plant' :
        {
            if ($user_org != '') {
                $brand = isset($_REQUEST['brand']) ? strtoupper(htmlspecialchars($_REQUEST['brand'])) : '';
                $plant = isset($_REQUEST['plant']) ? htmlspecialchars($_REQUEST['plant']) : '';
                $plant_opco = isset($_REQUEST['plant_opco']) ? htmlspecialchars($_REQUEST['plant_opco']) : '';
                $org_md = isset($_REQUEST['org_md']) ? htmlspecialchars($_REQUEST['org_md']) : '';
                $org_opco = isset($_REQUEST['org_opco']) ? htmlspecialchars($_REQUEST['org_opco']) : '';
                $royalty = isset($_REQUEST['royalty']) ? htmlspecialchars($_REQUEST['royalty']) : '';
                
                $date     = $newDate = date("d-m-Y");
                   
                $cekdata = "SELECT ID FROM MAPPING_BRAND_PLANT WHERE BRAND = :brand AND PLANT_MD = :plant AND PLANT_OPCO = :plant_opco AND ORG_MD = :org_md AND ORG_OPCO = :org_opco AND FLAG_DEL != 'Y'";
                $cek = oci_parse($conn, $cekdata);

                oci_bind_by_name($cek, ':brand', $brand);
                oci_bind_by_name($cek, ':plant', $plant);
                oci_bind_by_name($cek, ':plant_opco', $plant_opco);
                oci_bind_by_name($cek, ':org_md', $org_md);
                oci_bind_by_name($cek, ':org_opco', $org_opco);

                oci_execute($cek);

                $hasilCek = oci_fetch_array($cek);
   

                $user = $_SESSION['user_name'];
                if (count($hasilCek[0]) < 1) {
                    $sql2 = "INSERT INTO MAPPING_BRAND_PLANT (PLANT_MD, PLANT_OPCO, ORG_MD, ORG_OPCO, BRAND, IS_ROYALTY, CREATED_AT, CREATED_BY, FLAG_DEL)
                            VALUES (:plant, :plant_opco, :org_md, :org_opco, :brand, :royalty, SYSDATE, :bind_user, 'X')";

                    $query2 = oci_parse($conn, $sql2);

                    oci_bind_by_name($query2, ':plant', $plant);
                    oci_bind_by_name($query2, ':plant_opco', $plant_opco);
                    oci_bind_by_name($query2, ':org_md', $org_md);
                    oci_bind_by_name($query2, ':org_opco', $org_opco);
                    oci_bind_by_name($query2, ':brand', $brand);
                    oci_bind_by_name($query2, ':royalty', $royalty);
                    oci_bind_by_name($query2, ':bind_user', $user);
                    // echo $sql2;
                    $ins = oci_execute($query2);
                    $sap = new SAPConnection();
                    $sap->Connect("../include/sapclasses/logon_data.conf");
                    if ($sap->GetStatus() == SAPRFC_OK)
                        $sap->Open();
                    if ($sap->GetStatus() != SAPRFC_OK) {
                        $sap->PrintStatus();
                        exit;
                    }

                    $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
                    if ($fce == false) {
                        $sap->PrintStatus();
                        exit;
                    }
                    
                    $fce->I_TRANSACTION_TYPE = "C";
                    $fce->I_BRAND = $brand;
                    $fce->I_PLANT = $plant;
                    $fce->I_NMORG = $org_md;
                    $fce->I_PLANT_OPCO = $plant_opco;
                    $fce->I_NMORG_OPCO = $org_opco;
                    $fce->I_IS_ROYALTI = $royalty == "Y" ? "X" : "";
                    
                    $fce->Call();
                    
                    $fce->Close();
                    $sap->Close();
   
                    if ($ins){
                        echo json_encode(array('success'=>true));
                    } else {
                        echo json_encode(array('errorMsg'=>'Some errors occured.'));
                    }
                
                }else{
                    echo json_encode(array('errorMsg'=>'Duplicate Data.'));
                }
           }else{
               echo json_encode(array('errorMsg'=>'silahkan login terlebih dahulu!!!'));
           }
        }
        break;

     case 'addcc' :
        {
            if ($user_org != '') {
   
                $kdkota = isset($_REQUEST['kdkota']) ? htmlspecialchars($_REQUEST['kdkota']) : '';
                $ktName = isset($_REQUEST['ktName']) ? htmlspecialchars($_REQUEST['ktName']) : '';
                $ccemail = isset($_REQUEST['ccemail']) ? htmlspecialchars($_REQUEST['ccemail']) : '';

                $date     = $newDate = date("d-m-Y");
                   
                $cekdata = "SELECT ID FROM MAINTAIN_CC_EMAIL WHERE KD_KOTA = :kdkota ";
                $cek = oci_parse($conn, $cekdata);
                oci_bind_by_name($cek, ":kdkota", $kdkota);
                oci_execute($cek);
                $hasilCek = oci_fetch_array($cek);

   
                $user = $_SESSION['user_name'];
                if (count($hasilCek[0]) < 1) {
                    $sql2 = "INSERT INTO MAINTAIN_CC_EMAIL (KD_KOTA, NM_KOTA, CC_EMAIL, CREATED_AT, CREATED_BY)
                            VALUES (:kdkota, :ktName, :ccemail, :created_at, :created_by)";

                    $query2 = oci_parse($conn, $sql2);

                    oci_bind_by_name($query2, ':kdkota', $kdkota);
                    oci_bind_by_name($query2, ':ktName', $ktName);
                    oci_bind_by_name($query2, ':ccemail', $ccemail);
                    oci_bind_by_name($query2, ':created_at', $date);
                    oci_bind_by_name($query2, ':created_by', $_SESSION['user_name']);

                    $ins = oci_execute($query2);
                    if ($ins){
                        echo json_encode(array('success'=>true));
                    } else {
                        echo json_encode(array('errorMsg'=>'Some errors occured.'));
                    }
                               
                }else{
                    echo json_encode(array('errorMsg'=>'Duplicate Data.'));
                }
           }else{
               echo json_encode(array('errorMsg'=>'silahkan login terlebih dahulu!!!'));
           }
        }
        break;
     /*case 'edit' :
     {
        if($diff>=4){
            if($bulan=='JANUARI'){
                $bulan = '01';
            }elseif($bulan=='FEBRUARI'){
                $bulan = '02';
            }elseif($bulan=='MARET'){
                $bulan = '03';
            }elseif($bulan=='APRIL'){
                $bulan = '04';
            }elseif($bulan=='MEI'){
                $bulan = '05';
            }elseif($bulan=='JUNI'){
                $bulan = '06';
            }elseif($bulan=='JULI'){
                $bulan = '07';
            }elseif($bulan=='AGUSTUS'){
                $bulan = '08';
            }elseif($bulan=='SEPTEMBER'){
                $bulan = '09';
            }elseif($bulan=='OKTOBER'){
                $bulan = '10';
            }elseif($bulan=='NOVEMBER'){
                $bulan = '11';
            }elseif($bulan=='DESEMBER'){
                $bulan = '12';
            }
            if($tipesemen == 'ZAK'){
                $tipesemen = '121-301';
            } else if($tipesemen == 'CURAH'){
                $tipesemen = '121-302';
            }
            $periode = $bulan.$tahun;
            $sql= "UPDATE OR_TARGET_DISTR SET UPDATED_DATE = SYSDATE, UPDATED_BY = '$user_name', UPDATE_MARK = 1
                   WHERE ID = '$id' and delete_mark=0";
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);
            
            $sql1= "INSERT INTO OR_TARGET_DISTR (
                           SOLD_TO, TIPE_SEMEN, DISTRIK, PERIODE, TARGET, DELETE_MARK, CREATED_BY, CREATED_DATE, UPDATE_MARK) 
                           VALUES ('$soldto','$tipesemen','$distrik_add','$periode','$target','0','$user_name',SYSDATE, '0')";   
            $query1= oci_parse($conn, $sql1);
            $result1=oci_execute($query1);
            if ($result){
                echo json_encode($diff);
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        } else {
            echo json_encode(array('errorMsg'=>'Target untuk periode 3 bulan ke depan tidak dapat diubah!!!'));
        }
     }
     break;*/
     case 'editAdm' :
     {
        $array_all                   = array();
        $array_all['id_target_ku']   = array_map('trim', explode(",", $id_target_ku));
        $array_all['edit_target_ku'] = array_map('trim', explode(",", $edit_target_ku));

        $jum = count($array_all['id_target_ku']);

        for ($i=0; $i < $jum; $i++) {
            $id_target_ku     = $array_all['id_target_ku'][$i];
            $edit_target_ku   = $array_all['edit_target_ku'][$i];

            $sql_cek_target = "SELECT COUNT(ID) AS JUMLAH FROM OR_TARGET_DISTR WHERE ID = :id_target_ku AND TARGET = :edit_target_ku";
            $query_cek_target = oci_parse($conn, $sql_cek_target);

            oci_bind_by_name($query_cek_target, ':id_target_ku', $id_target_ku);
            oci_bind_by_name($query_cek_target, ':edit_target_ku', $edit_target_ku);

            oci_execute($query_cek_target);

            $row_cek_target = oci_fetch_array($query_cek_target);

            # JIKA TARGET INPUTAN == TARGET DI DATABASE SESUAI ID
            if ($row_cek_target['JUMLAH'] == 0) {

                $sql = "UPDATE OR_TARGET_DISTR SET TARGET = :edit_target_ku, UPDATED_DATE = SYSDATE, UPDATED_BY = :user_name
                        WHERE ID = :id_target_ku AND DELETE_MARK = 0";
                $query = oci_parse($conn, $sql);

                oci_bind_by_name($query, ':edit_target_ku', $edit_target_ku);
                oci_bind_by_name($query, ':user_name', $user_name);
                oci_bind_by_name($query, ':id_target_ku', $id_target_ku);

                $result = oci_execute($query);

            }
        
            if ($result){
                $show_ket = "Data berhasil di Update <br>";
                $keterangan = array('success'=>$show_ket);
            } else {
                $show_ket = "Data Gagal di Update <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
        }
        echo json_encode($keterangan);
     }
     break;
     
     /*case 'editAdm_lama' :
     {
        $sold_to = htmlspecialchars($_REQUEST['id']);
        if($bulan=='JANUARI'){
            $bulan = '01';
        }elseif($bulan=='FEBRUARI'){
            $bulan = '02';
        }elseif($bulan=='MARET'){
            $bulan = '03';
        }elseif($bulan=='APRIL'){
            $bulan = '04';
        }elseif($bulan=='MEI'){
            $bulan = '05';
        }elseif($bulan=='JUNI'){
            $bulan = '06';
        }elseif($bulan=='JULI'){
            $bulan = '07';
        }elseif($bulan=='AGUSTUS'){
            $bulan = '08';
        }elseif($bulan=='SEPTEMBER'){
            $bulan = '09';
        }elseif($bulan=='OKTOBER'){
            $bulan = '10';
        }elseif($bulan=='NOVEMBER'){
            $bulan = '11';
        }elseif($bulan=='DESEMBER'){
            $bulan = '12';
        }
        if($tipesemen == 'ZAK'){
            $tipesemen = '121-301';
        } else if($tipesemen == 'CURAH'){
            $tipesemen = '121-302';
        }
        $periode = $bulan.$tahun;
        $sql= "UPDATE OR_TARGET_DISTR SET TARGET = '$target', UPDATED_DATE = SYSDATE, UPDATED_BY = '$user_name'
               WHERE ID = '$id' and delete_mark=0";
        $query= oci_parse($conn, $sql);
        $result=oci_execute($query);

        if ($result){
            echo json_encode($sql);
        } else {
            echo json_encode(array('errorMsg'=>'Some errors occured.'));
        }
     }
     break;*/
     case 'del' :
     {

        $value = ($_POST['data']);
        $list = array();
        $gagal = 0;
        $sukses= 0;
        $i=0; 

        while($i < count($value)){
            $idDlt = $value[$i]['ID'];       
            // code update status = 4 (aprove) di tabel job_header             
            
            $sql = "UPDATE MAPPING_BRAND_PLANT SET FLAG_DEL='Y' WHERE ID = ".$idDlt;
            $query= oci_parse($conn, $sql);
            // oci_bind_by_name($sql, ":idDlt", $idDlt);
            $result=oci_execute($query);

            if($result){ 
                $sukses=$sukses+1; 

                $val['ID'] = $idDlt;
                $original_data = findDataMappingBrandPlant($conn, $val);

                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK)
                    $sap->Open();
                if ($sap->GetStatus() != SAPRFC_OK) {
                    $sap->PrintStatus();
                    exit;
                }

                $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
                if ($fce == false) {
                    $sap->PrintStatus();
                    exit;
                }
                $fce->I_TRANSACTION_TYPE = "R";
                $fce->I_BRAND = $original_data["BRAND"];
                $fce->I_PLANT = $original_data["PLANT_MD"];
                $fce->I_NMORG = $original_data["ORG_MD"];
                $fce->I_PLANT_OPCO = $original_data["PLANT_OPCO"];
                $fce->I_NMORG_OPCO = $original_data["ORG_OPCO"];
                $fce->I_IS_ROYALTI = $original_data["IS_ROYALTY"] == "Y" ? "X" : "";
                // $fce->XKUNNR = $soldto; //"dist";
                // $fce->XFLAG = 'O'; //open
                // $data = [];
                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $fce->T_DATA->Reset();
                    // print_r($fce->T_DATA);
                    // print_r($fce->T_DATA->row);
                    $itr = 0;
                    while ($fce->T_DATA->Next()) {
                        $data_sap[$itr]['MANDT'] = $fce->T_DATA->row["MANDT"];
                        $data_sap[$itr]['NUM_ID'] = $fce->T_DATA->row["NUM_ID"];
                        $data_sap[$itr]['BRAND'] = $fce->T_DATA->row["BRAND"];
                        $data_sap[$itr]['COMPANY '] = $fce->T_DATA->row["COMPANY "];
                        $data_sap[$itr]['PLANT'] = $fce->T_DATA->row["PLANT"];
                        $data_sap[$itr]['COMPANY_OPCO'] = $fce->T_DATA->row["COMPANY_OPCO"];
                        $data_sap[$itr]['PLANT_OPCO'] = $fce->T_DATA->row["PLANT_OPCO"];
                        $data_sap[$itr]['IS_ROYALTI'] = $fce->T_DATA->row["IS_ROYALTI"];
                        // print_r($fce->T_DATA->row["BRAND"]);
                        // print_r($fce->T_DATA->row["NO"]);
                        $itr++;
                    }
                }

                if ($data_sap) {
                    for ($itr_o=0; $itr_o < count($data_sap) ; $itr_o++) { 
                        $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
                        
                        $fce->I_TRANSACTION_TYPE = "D";
                        $fce->I_NO_TRANSAKSI = $data_sap[$itr_o]["NUM_ID"];
                        
                        $fce->Call();
                    }
                }
                $fce->Close();
                $sap->Close();
            }else{ 
                $gagal=$gagal+1; 
            }

            array_push($list, $ID);
          $i++;
         }  

        if ($result){
            $keterangan = array('success'=>"Data Berhasil Di Delete = ".$sukses.", gagal = ".$gagal." ! ");
        } else {
            $keterangan = array('errorMsg'=>"Data Gagal Di Delete = gagal = ".$gagal." ! ");
        }
        // }
        echo json_encode($keterangan);

     }
     break;

     case 'delcc' :
        {
           $sql = "DELETE FROM MAINTAIN_CC_EMAIL WHERE ID = :ID_REQ";
           $query= oci_parse($conn, $sql);
           oci_bind_by_name($sql, ":ID_REQ", $ID_REQ);
           $result=oci_execute($query);
   
   
           if ($result){
               $show_ket = "Data berhasil di Hapus <br>";
               $keterangan = array('success'=>$show_ket);
           } else {
               $show_ket = "Data Gagal di Dihapus <br>";
               $keterangan = array('errorMsg'=>$show_ket);
           }
           // }
           echo json_encode($keterangan);
        }
        break;

     case 'updateApp' :
        {
            // $kode_material   = htmlspecialchars($_REQUEST['kode_material']);
            $brand = isset($_REQUEST['brand']) ? htmlspecialchars($_REQUEST['brand']) : '';
            $plant = isset($_REQUEST['plant']) ? htmlspecialchars($_REQUEST['plant']) : '';
            $plant_opco = isset($_REQUEST['plant_opco']) ? htmlspecialchars($_REQUEST['plant_opco']) : '';
            $org_md = isset($_REQUEST['org_md']) ? htmlspecialchars($_REQUEST['org_md']) : '';
            $org_opco = isset($_REQUEST['org_opco']) ? htmlspecialchars($_REQUEST['org_opco']) : '';
            $royalty = isset($_REQUEST['royalty']) ? htmlspecialchars($_REQUEST['royalty']) : '';
            $ID = isset($_REQUEST['ID']) ? htmlspecialchars($_REQUEST['ID']) : '';

              
            unset($val); 
            // $val['KODE_MATERIAL']     = $kode_material;
            $val['PLANT_MD']             = strtoupper($plant);
            $val['PLANT_OPCO']             = strtoupper($plant_opco);
            $val['ORG_MD']             = strtoupper($org_md);
            $val['ORG_OPCO']             = strtoupper($org_opco);
            $val['BRAND']             = strtoupper($brand);
            $val['ROYALTY']             = strtoupper($royalty);
            $val['ID']             = $ID;

         
            $data_cek2 = CekSelectDataUpd($conn,$val);
            if ($data_cek2['JUMLAH'] > 0) {
                $show_ket = "Data Duplicate<br>";
                $keterangan = array('errorMsg'=>$show_ket);
                echo json_encode($keterangan);
            }else{
                $user = $_SESSION['user_name'];
                //  if (count($hasilCek[0]) < 1 ) {
                    # code...
                $original_data = findDataMappingBrandPlant($conn, $val);
                
                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK)
                    $sap->Open();
                if ($sap->GetStatus() != SAPRFC_OK) {
                    $sap->PrintStatus();
                    exit;
                }

                $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
                if ($fce == false) {
                    $sap->PrintStatus();
                    exit;
                }
                
                $fce->I_TRANSACTION_TYPE = "R";
                $fce->I_BRAND = $original_data["BRAND"];
                $fce->I_PLANT = $original_data["PLANT_MD"];
                $fce->I_NMORG = $original_data["ORG_MD"];
                $fce->I_PLANT_OPCO = $original_data["PLANT_OPCO"];
                $fce->I_NMORG_OPCO = $original_data["ORG_OPCO"];
                $fce->I_IS_ROYALTI = $original_data["IS_ROYALTY"] == "Y" ? "X" : "";
                
                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $fce->T_DATA->Reset();
                    
                    $itr = 0;
                    while ($fce->T_DATA->Next()) {
                        $data_sap[$itr]['MANDT'] = $fce->T_DATA->row["MANDT"];
                        $data_sap[$itr]['NUM_ID'] = $fce->T_DATA->row["NUM_ID"];
                        $data_sap[$itr]['BRAND'] = $fce->T_DATA->row["BRAND"];
                        $data_sap[$itr]['COMPANY'] = $fce->T_DATA->row["COMPANY"];
                        $data_sap[$itr]['PLANT'] = $fce->T_DATA->row["PLANT"];
                        $data_sap[$itr]['COMPANY_OPCO'] = $fce->T_DATA->row["COMPANY_OPCO"];
                        $data_sap[$itr]['PLANT_OPCO'] = $fce->T_DATA->row["PLANT_OPCO"];
                        $data_sap[$itr]['IS_ROYALTI'] = $fce->T_DATA->row["IS_ROYALTI"];
                        
                        $itr++;
                    }
                }
                
                if ($data_sap) {

                    for ($itr_o=0; $itr_o < count($data_sap) ; $itr_o++) { 
                        // sleep(5); //seconds to wait..     
                        $fce = $sap->NewFunction("ZMAP_INT_BRAND_PLANT");
                        
                        $fce->I_TRANSACTION_TYPE = "U";
                        $fce->I_NO_TRANSAKSI = $data_sap[$itr_o]["NUM_ID"];
                        $fce->I_BRAND = $brand;
                        $fce->I_PLANT = $plant;
                        $fce->I_NMORG = $org_md;
                        $fce->I_PLANT_OPCO = $plant_opco;
                        $fce->I_NMORG_OPCO = $org_opco;
                        $fce->I_IS_ROYALTI = $royalty == "Y" ? "X" : "";
                        
                        $fce->Call();
                    }
                }
                
                $fce->Close();
                $sap->Close();

                $sql = "UPDATE MAPPING_BRAND_PLANT SET PLANT_MD = :plant, PLANT_OPCO = :plant_opco, ORG_MD = :org_md, ORG_OPCO = :org_opco, BRAND = :brand, IS_ROYALTY = :royalty, UPDATED_AT = SYSDATE, UPDATED_BY = :bind_user WHERE ID = :ID";

                $query = oci_parse($conn, $sql);
                oci_bind_by_name($query, ":plant", $plant);
                oci_bind_by_name($query, ":plant_opco", $plant_opco);
                oci_bind_by_name($query, ":org_md", $org_md);
                oci_bind_by_name($query, ":org_opco", $org_opco);
                oci_bind_by_name($query, ":brand", $brand);
                oci_bind_by_name($query, ":royalty", $royalty);
                oci_bind_by_name($query, ":bind_user", $user);
                oci_bind_by_name($query, ":ID", $ID);

                
                $result = oci_execute($query);

        
                if ($result){
                    $show_ket = "Data berhasil di update <br>";
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di update <br>";
                    $keterangan = array('errorMsg'=>$show_ket);
                }
                echo json_encode($keterangan);
            }

          
           
        // }else{
        //     echo json_encode(array('errorMsg'=>'Duplicate Data.'));
        // }
          
        }
        break;

        case 'updateAppcc' :
            {
    
                $kdkota = isset($_REQUEST['kdkota']) ? htmlspecialchars($_REQUEST['kdkota']) : '';
                $ktname = isset($_REQUEST['ktname']) ? htmlspecialchars($_REQUEST['ktname']) : '';
                $ccemail = isset($_REQUEST['ccemail']) ? htmlspecialchars($_REQUEST['ccemail']) : '';
                $date = $newDate = date("d-m-Y");
                $ID = isset($_REQUEST['ID']) ? htmlspecialchars($_REQUEST['ID']) : '';
                
                $user = $_SESSION['user_name'];
                $sql = "UPDATE MAINTAIN_CC_EMAIL SET KD_KOTA = :kdkota, NM_KOTA = :ktName, CC_EMAIL = :ccemail, UPDATED_AT = :updated_at, UPDATED_BY = :updated_by WHERE ID = :ID";

                $query = oci_parse($conn, $sql);

                oci_bind_by_name($query, ':kdkota', $kdkota);
                oci_bind_by_name($query, ':ktName', $ktName);
                oci_bind_by_name($query, ':ccemail', $ccemail);
                oci_bind_by_name($query, ':updated_at', $date);
                oci_bind_by_name($query, ':updated_by', $user);
                oci_bind_by_name($query, ':ID', $ID);

                $result = oci_execute($query);
       
                if ($result){
                    $show_ket = "Data berhasil di update <br>";
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di update <br>";
                    $keterangan = array('errorMsg'=>$show_ket);
                }
                echo json_encode($keterangan);
            }
            break;

     case 'upload_file' :
     {
        ############################# READ XLS ####################
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        $allowedExts = "xls";
        $extension = end(explode(".", $_FILES["file_upload"]["name"]));
        if ($extension==$allowedExts) {

            //validasi signature excel by ibad
            $tmpFilePath = $_FILES['file_upload']['tmp_name'];
            $fileContent = file_get_contents($tmpFilePath);
            $header = substr($fileContent, 0, 8);
            $expectedHeader = "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1";

            $is_valid = 'tidak valid';
            if ($header === $expectedHeader) {
                if (strpos($fileContent, "Workbook") !== false || strpos($fileContent, "Sheet") !== false) {
                    $isValid = 'valid';
                }
            }

            if($isValid == 'valid'){
                $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
                $jumlah_row = $cell->rowcount($sheet_index=0);
                $jumlah_col = $cell->colcount($sheet_index=0);
                $kode_file  = $cell->val( 1,2 );
                
                for ($i = 5; $i <= $jumlah_row; $i++) {
                    for ($j = 1; $j <= 7; $j++) {                    
                        $ke = $i-5;
                        $data[$ke][$j]= $cell->val( $i,$j );
                    }
                }
            
                $messData = updateDataHoliday($conn, $data);
                
                if ($messData["status"] == 200){
                    $show_ket = "Data berhasil di Simpan <br>";
                    $keterangan = array('success'=>$messData["msg"]);
                } else {
                    $show_ket = "Data Gagal di Simpan <br>".$messData["msg"];
                    $keterangan = array('errorMsg'=>$show_ket);
                }
            } else{
                $show_ket = "Invalid file...2!! <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
        } else {
            $show_ket = "Invalid file...!! <br>";
            $keterangan = array('errorMsg'=>$show_ket);
        }
        echo json_encode($keterangan);
     }
     break;

     case 'upload_filecc' :
        {
           ############################# READ XLS ####################
           error_reporting(E_ALL ^ E_NOTICE);
           require_once '../ex_report/excel_reader2.php';
   
           $allowedExts = "xls";
           $extension = end(explode(".", $_FILES["file_uploadcc"]["name"]));
           if ($extension==$allowedExts) {

                //validasi signature excel by ibad
                $tmpFilePath = $_FILES['file_upload']['tmp_name'];
                $fileContent = file_get_contents($tmpFilePath);
                $header = substr($fileContent, 0, 8);
                $expectedHeader = "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1";

                $is_valid = 'tidak valid';
                if ($header === $expectedHeader) {
                    if (strpos($fileContent, "Workbook") !== false || strpos($fileContent, "Sheet") !== false) {
                        $isValid = 'valid';
                    }
                }

                if($isValid == 'valid'){
                    $cell   = new Spreadsheet_Excel_Reader($_FILES['file_uploadcc']['tmp_name']);
                    $jumlah_row = $cell->rowcount($sheet_index=0);
                    $jumlah_col = $cell->colcount($sheet_index=0);
                    $kode_file  = $cell->val( 1,2 );
                    
                    for ($i = 5; $i < $jumlah_row; $i++) {
                        for ($j = 1; $j <= 6; $j++) {                    
                            $ke = $i-5;
                            $data[$ke][$j]= $cell->val( $i,$j );
                        }
                    }
                    
                    $messData = updateDataHolidaycc($conn, $data, $soldto);
        
                    if ($messData){
                        $show_ket = "Data berhasil di Simpan <br>";
                        $keterangan = array('success'=>$show_ket);
                    } else {
                        $show_ket = "Data Gagal di Simpan <br>";
                        $keterangan = array('errorMsg'=>$show_ket);
                    }
                } else{
                    $show_ket = "Invalid file...2!! <br>";
                    $keterangan = array('errorMsg'=>$show_ket);
                }
           } else {
               $show_ket = "Invalid file...!! <br>";
               $keterangan = array('errorMsg'=>$show_ket);
           }
           echo json_encode($keterangan);
        }
        break;
     case 'detail' :
        {
            $distr_id = $_SESSION['distr_id'];

            if ($distr_id == '') {
                $sold_tox .= " AND SOLD_TO = :sold_to";
            } else {
                $sold_tox .= " AND SOLD_TO = :distr_id";
            }

            $sql_order = "ORDER BY
                            A.TARGET_DATE ASC";

            $sql = $sql_select.", A.ID, A.ID_TIPE_SEMEN, A.TARGET, A.TARGET_DATE ".$sql_from." ".$sold_tox." AND TIPE_SEMEN = :tipe_semen AND DISTRIK = :distrik AND PERIODE = :periode ".$sql_join." ".$sql_order;

            $query= oci_parse($conn, $sql);
            oci_bind_by_name($query, ':tipe_semen', $tipe_semen);
            oci_bind_by_name($query, ':distrik', $distrik);
            oci_bind_by_name($query, ':periode', $periode);
            if ($distr_id == '') {
                oci_bind_by_name($query, ':sold_to', $sold_to);
            } else {
                oci_bind_by_name($query, ':distr_id', $distr_id);
            }

            oci_execute($query);
            $i=0;
            while($row=oci_fetch_array($query)){
                array_push($result, $row);
            }
            echo json_encode($result);
        }
        break;
    }
}
?>
