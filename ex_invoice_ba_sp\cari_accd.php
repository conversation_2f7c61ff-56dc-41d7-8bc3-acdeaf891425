<?
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
//$hakakses=array("admin");
//$fungsi->keamanan($hakakses);
$currentPage="cari_accd.php";
$isian= $_REQUEST['isian'];
$user_id= $_SESSION['user_id'];
$no_urut= $_REQUEST['nourut'];
$pajak= $_REQUEST['pajak'];
$orgcari= $_REQUEST['org'];
$komen="";
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>
<script>
<!--
function setForm() {
	var btn = document.getElementById("cekdata"); 
	if(btn.value != 0){
	var urut=<?=$no_urut?>;
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var pajak='pajak'+kenya;
	var komponen_pajak=document.getElementById(pajak); 
	var gl='gl'+kenya;
	var komponen_gl=document.getElementById(gl); 
	opener.document.getElementById("komponen_biaya_d"+urut).value = komponen_acc_id.value;
	opener.document.getElementById("nama_komponen_d"+urut).value = komponen_acc_no.value;
	opener.document.getElementById("pajak_d"+urut).value = komponen_pajak.value;
	opener.document.getElementById("no_gl_d"+urut).value = komponen_gl.value;
    self.close();
	}else
		{
			alert('Pilih Data Komponen Biaya Dahulu')
			return false;
		}
}
//-->
</script>
<script> 
function checkForother(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
} 
</script> 

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Master Charge Account</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar Data Master Account </th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		<td width="173" class="puso">Filter Data </td>
		<td width="26" class="puso">:</td>
		<td width="585"><input name="isian" type="text" class="" value="<? echo $isian; ?>" size="40"/>
		&nbsp;&nbsp;</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="Show" />
		<input type="hidden" id="user_id" name="user_id" value="<?=$user_id?>" />

		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?
if(isset($_POST['isian']) || isset($_GET['hal']) ||  isset($_POST['hal'])){
	if($isian!=""){
		$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE (KODE_KOMPONEN like '$isian' or NAMA_KOMPONEN like '$isian' or NO_GL like '$isian' or NAMA_GL like '$isian' ) and org='$orgcari' and delete_mark=0 ORDER BY KODE_KOMPONEN ASC";
		//AND ( TAX_CODE IS NULL OR TAX_CODE = '$pajak' ) 
	}else if($isian==""){
		$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA where delete_mark=0 and org='$orgcari' ORDER BY KODE_KOMPONEN ASC";
		//AND ( TAX_CODE IS NULL OR TAX_CODE = '$pajak' )
	}
	$query= oci_parse($conn, $mialo);
	oci_execute($query);
	
	while($row=oci_fetch_array($query)){
		$id_komponen_vw[]=$row[ID];
		$kode_komponen_vw[]=$row[KODE_KOMPONEN];
		$nama_komponen_vw[]=$row[NAMA_KOMPONEN]; 
		$no_gl_vw[]=$row[NO_GL];
		$nama_gl_vw[]=$row[NAMA_GL];
		$kode_tax_vw[]=$row[TAX_CODE];
		$nama_tax_vw[]=$row[NAMA_TAX];
	}
	$total=count($id_komponen_vw);
	include ('../include/ulang.php');
	if($total>0){
?>
<p></p>
<div align="center">
<table width="800" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Master Charge Account </span> </th>
</tr>
</table>
</div> 
<div align="center">
<form  name="formKaryawan">
<table width="800" align="center" class="adminlist">
  <tr class="quote">
    <td><div align="center"><strong>&nbsp;&nbsp; Cek.</strong></div></td>
    <td align="center"><strong>Kode Kom. </strong></td>
	<td align="center"><strong>Nama Komponen </strong></td>
    <td align="center"><strong>No GL</strong></td>
    <td align="center"><strong>Nama GL</strong></td>
    <td align="center"><strong>Kode Pajak</strong></td>
    <td align="center"><strong>Nama Pajak</strong></td>
  </tr>
  <?  for($i=0; $i<$total;$i++) {
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else{	
		echo "<tr class='row1'>";
			}	
	$b=$i+1;
	$acc_id="acc_id".$b;
	$acc_no="acc_no".$b;
	$pajak="pajak".$b;
	$gl="gl".$b;
	?>
	<tr><td align="center"><input name="radiokaryawan" type="radio" value="<?=$b;?>" onChange="checkForother(this)" id="<?=$b?>"/>
	<input id="<?=$gl;?>" name="<?=$gl;?>" type="hidden" value="<?=$no_gl_vw[$i]?>" />
	<input id="<?=$pajak;?>" name="<?=$pajak;?>" type="hidden" value="<?=$kode_tax_vw[$i]?>" />
	<input id="<?=$acc_id;?>" name="<?=$acc_id;?>" type="hidden" value="<?=$kode_komponen_vw[$i]?>" />
	<input id="<?=$acc_no;?>" name="<?=$acc_no;?>" type="hidden" value="<?=$nama_komponen_vw[$i]?>" />	</td>    
    <td align="center"><? echo $kode_komponen_vw[$i]; ?></td>
	<td align="center"><? echo $nama_komponen_vw[$i]; ?></td>
    <td align="center"><?=$no_gl_vw[$i]?></td>
    <td align="center"><?=$nama_gl_vw[$i]?></td>
    <td align="center"><?=$kode_tax_vw[$i]?></td>
    <td align="center"><?=$nama_tax_vw[$i]?></td>
	</tr>
  <? } ?>
</table>
<?
}
?>
<br />
<br />
			<input type="button" value="Oke" name="kartu" class="button" onClick="setForm()">
          <input type="button" name="Submit2" value="Cancel" onClick="window.close();" class="button" />
		  <input id="cekdata" name="cekdata" type="hidden" value="0" />
</form>
</div>

<div align="center">
<?
echo $komen;
}
?></div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
