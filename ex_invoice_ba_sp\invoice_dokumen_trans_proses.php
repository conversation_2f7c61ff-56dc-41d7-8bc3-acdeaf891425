<?php 
session_start();
include('../include/ex_fungsi.php');
include('../include/validasi.php'); 
require_once('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : '';
$user_name = isset($_SESSION['user_name']) ? $_SESSION['user_name'] : '';
$no_inv = isset($_GET['no_invoice']) ? $_GET['no_invoice'] : '';

// Validation
if (empty($no_inv)) {
    die('<center><h3>No Invoice tidak valid atau kosong</h3></center>');
}

if (empty($user_id)) {
    die('<center><h3>User session tidak valid</h3></center>');
}

/**
 * Enhanced function to generate dokumen trans with better error handling
 */
function generateDokumenTrans($conn, $no_invoice, $user_id) {
    $debug_mode = false; // Set to false in production
    
    try {
        // 1. Get data from EX_TRANS_HDR
        $sql_trans = "SELECT KELOMPOK_TRANSAKSI, TIPE_TRANSAKSI 
                      FROM EX_TRANS_HDR 
                      WHERE NO_INVOICE = :no_invoice 
                      AND (DELETE_MARK = 0 OR DELETE_MARK IS NULL)
                      GROUP BY KELOMPOK_TRANSAKSI, TIPE_TRANSAKSI";
        
        $query_trans = oci_parse($conn, $sql_trans);
        oci_bind_by_name($query_trans, ":no_invoice", $no_invoice);
        
        if (!oci_execute($query_trans)) {
            $error = oci_error($query_trans);
            if ($debug_mode) echo "Error getting transaction data: " . $error['message'] . "<br>";
            return false;
        }
        
        $row_trans = oci_fetch_assoc($query_trans);
        if (!$row_trans) {
            if ($debug_mode) echo "No transaction data found for invoice: $no_invoice<br>";
            return false;
        }
        
        $kelompok_transaksi = $row_trans['KELOMPOK_TRANSAKSI'];
        $tipe_transaksi = $row_trans['TIPE_TRANSAKSI'];
        
        if ($debug_mode) {
            echo "DEBUG: Found transaction - Kelompok: $kelompok_transaksi, Tipe: $tipe_transaksi<br>";
        }
        
        // 2. Get dokumen group
        $sql_group = "SELECT DOKUMEN_ID 
                      FROM EX_INVOICE_DOKUMEN_GROUP 
                      WHERE KELOMPOK_TRANSAKSI = :kelompok 
                      AND TIPE_TRANSAKSI = :tipe 
                      AND (DELETE_MARK = 0 OR DELETE_MARK IS NULL)";
        
        $query_group = oci_parse($conn, $sql_group);
        oci_bind_by_name($query_group, ":kelompok", $kelompok_transaksi);
        oci_bind_by_name($query_group, ":tipe", $tipe_transaksi);
        
        if (!oci_execute($query_group)) {
            $error = oci_error($query_group);
            if ($debug_mode) echo "Error getting document group: " . $error['message'] . "<br>";
            return false;
        }
        
        $row_group = oci_fetch_assoc($query_group);
        if (!$row_group || empty($row_group['DOKUMEN_ID'])) {
            if ($debug_mode) {
                echo "No document group found for Kelompok: $kelompok_transaksi, Tipe: $tipe_transaksi<br>";
                // Show available groups for debugging
                $debug_sql = "SELECT KELOMPOK_TRANSAKSI, TIPE_TRANSAKSI, DOKUMEN_ID 
                             FROM EX_INVOICE_DOKUMEN_GROUP 
                             WHERE (DELETE_MARK = 0 OR DELETE_MARK IS NULL)
                             ORDER BY KELOMPOK_TRANSAKSI, TIPE_TRANSAKSI";
                $debug_query = oci_parse($conn, $debug_sql);
                oci_execute($debug_query);
                echo "<strong>Available document groups:</strong><br>";
                while ($debug_row = oci_fetch_assoc($debug_query)) {
                    echo "- Kelompok: <strong>{$debug_row['KELOMPOK_TRANSAKSI']}</strong>, Tipe: <strong>{$debug_row['TIPE_TRANSAKSI']}</strong>, Dokumen: {$debug_row['DOKUMEN_ID']}<br>";
                }
            }
            return false;
        }
        
        $dokumen_ids = $row_group['DOKUMEN_ID'];
        if ($debug_mode) echo "DEBUG: Found dokumen IDs: $dokumen_ids<br>";
        
        // 3. Get detail dokumen
        $sql_dokumen = "SELECT ID, NAMA_DOKUMEN 
                        FROM EX_INVOICE_DOKUMEN_INV 
                        WHERE ID IN ($dokumen_ids) 
                        AND (DELETE_MARK = 0 OR DELETE_MARK IS NULL)
                        ORDER BY ID";
        
        $query_dokumen = oci_parse($conn, $sql_dokumen);
        if (!oci_execute($query_dokumen)) {
            $error = oci_error($query_dokumen);
            if ($debug_mode) echo "Error getting documents: " . $error['message'] . "<br>";
            return false;
        }
        
        $generated_count = 0;
        
        // 4. Insert data ke EX_INVOICE_DOKUMEN_TRANS
        while ($row_dokumen = oci_fetch_assoc($query_dokumen)) {
            $dokumen_id = $row_dokumen['ID'];
            $nama_dokumen = $row_dokumen['NAMA_DOKUMEN'];
            
            // Check if record already exists
            $sql_check = "SELECT COUNT(*) as COUNT_DATA 
                         FROM EX_INVOICE_DOKUMEN_TRANS 
                         WHERE NO_INVOICE = :no_invoice 
                         AND ID_INVOICE_DOKUMEN = :dokumen_id";
            
            $query_check = oci_parse($conn, $sql_check);
            oci_bind_by_name($query_check, ":no_invoice", $no_invoice);
            oci_bind_by_name($query_check, ":dokumen_id", $dokumen_id);
            oci_execute($query_check);
            $row_check = oci_fetch_assoc($query_check);
            
            if ($row_check['COUNT_DATA'] == 0) {
                $sql_insert = "INSERT INTO EX_INVOICE_DOKUMEN_TRANS 
                              (NO_INVOICE, ID_INVOICE_DOKUMEN, STATUS, CREATED_BY, CREATED_DATE) 
                              VALUES (:no_invoice, :dokumen_id, 0, :user_id, SYSDATE)";
                
                $query_insert = oci_parse($conn, $sql_insert);
                oci_bind_by_name($query_insert, ":no_invoice", $no_invoice);
                oci_bind_by_name($query_insert, ":dokumen_id", $dokumen_id);
                oci_bind_by_name($query_insert, ":user_id", $user_id);
                
                if (oci_execute($query_insert)) {
                    if ($debug_mode) echo "✓ Generated dokumen trans for: <strong>$nama_dokumen</strong><br>";
                    $generated_count++;
                } else {
                    $error = oci_error($query_insert);
                    if ($debug_mode) echo "✗ Error inserting dokumen trans for $nama_dokumen: " . $error['message'] . "<br>";
                }
            } else {
                if ($debug_mode) echo "→ Document already exists: $nama_dokumen<br>";
            }
        }
        
        if ($generated_count > 0) {
            oci_commit($conn);
            if ($debug_mode) echo "<br><strong style='color: green;'>✓ Successfully generated $generated_count dokumen trans records</strong><br>";
            return true;
        } else {
            if ($debug_mode) echo "<br><strong style='color: orange;'>→ No new dokumen trans records generated</strong><br>";
            return false;
        }
        
    } catch (Exception $e) {
        if ($debug_mode) echo "<strong style='color: red;'>Exception in generateDokumenTrans: " . $e->getMessage() . "</strong><br>";
        return false;
    }
}

// Handle form submission
if (isset($_POST['save'])) {
    $arrcek = isset($_POST['DOKTRANS']) ? $_POST['DOKTRANS'] : array();
    $idinv = isset($_POST['id_inv']) ? $_POST['id_inv'] : '';
    $totaldok = (int)(isset($_POST['totaldok']) ? $_POST['totaldok'] : 0);
    $totalapprove = 0;
    
    // Validation
    if (empty($idinv)) {
        die('<center><h3>ID Invoice tidak valid</h3></center>');
    }
    
    // Reset all status to 0
    $sqlupdate = "UPDATE EX_INVOICE_DOKUMEN_TRANS 
                  SET STATUS = 0 
                  WHERE NO_INVOICE = :idinv";
    $qupdate = oci_parse($conn, $sqlupdate);
    oci_bind_by_name($qupdate, ":idinv", $idinv);
    oci_execute($qupdate);
    
    // Update selected documents
    if (count($arrcek) > 0) {
        foreach ($arrcek as $key => $value) {
            if ($value) {
                $totalapprove++;
            }
            
            $sqlupdate = "UPDATE EX_INVOICE_DOKUMEN_TRANS 
                         SET STATUS = :value 
                         WHERE ID = :key";
            $qupdate = oci_parse($conn, $sqlupdate);
            $status_value = (int)$value;
            oci_bind_by_name($qupdate, ":value", $status_value);
            oci_bind_by_name($qupdate, ":key", $key);
            oci_execute($qupdate);
        }
    }
    
    // Check if all documents are approved
    if ($totaldok == $totalapprove) {
        $sqlupdateinv = "UPDATE EX_INVOICE 
                        SET STATUS_DOKUMEN = 1 
                        WHERE NO_INVOICE = :id_inv";
        $qupdateinv = oci_parse($conn, $sqlupdateinv);
        oci_bind_by_name($qupdateinv, ":id_inv", $idinv);
        $inserted = @oci_execute($qupdateinv);

        if ($inserted) {
            // Additional processing for approved documents...
            echo '<script>window.location = "create_invoice_all.php?no_invoice='.$idinv.'";</script>';
            exit;
        }
    } else {
        // Handle incomplete documents...
        $sqldokkurang = "SELECT TB2.NAMA_DOKUMEN 
                        FROM EX_INVOICE_DOKUMEN_TRANS TB1
                        JOIN EX_INVOICE_DOKUMEN_INV TB2 ON TB1.ID_INVOICE_DOKUMEN = TB2.ID
                        WHERE NO_INVOICE = :idinv AND TB1.STATUS = 0";

        $querydokkurang = oci_parse($conn, $sqldokkurang);
        oci_bind_by_name($querydokkurang, ":idinv", $idinv);
        oci_execute($querydokkurang);
        
        $datadokkurang = array();
        while ($row = oci_fetch_assoc($querydokkurang)) {
            $datadokkurang[] = $row['NAMA_DOKUMEN'];
        }
        $datadokkurang_str = implode(",", $datadokkurang);
        
        // Log incomplete documents...
    }
    oci_commit($conn);
} else {
    // Check if documents are already complete
    $sqlstatus = "SELECT STATUS_DOKUMEN 
                  FROM EX_INVOICE 
                  WHERE NO_INVOICE = :no_inv";
    $qstatus = oci_parse($conn, $sqlstatus);
    oci_bind_by_name($qstatus, ":no_inv", $no_inv);
    
    if (!oci_execute($qstatus)) {
        $error = oci_error($qstatus);
        echo "Error checking status dokumen: " . $error['message'] . "<br>";
        die();
    }
    
    $datastatus = oci_fetch_assoc($qstatus);
    
    if (isset($datastatus['STATUS_DOKUMEN']) && $datastatus['STATUS_DOKUMEN']) {
        echo '<center><h3>Dokumen sudah lengkap</h3></center>';
        die();
    }
    
    // Get existing document transactions
    $sqlchek = "SELECT TB1.NO_INVOICE, TB1.STATUS, TB2.NAMA_DOKUMEN, TB1.ID 
               FROM EX_INVOICE_DOKUMEN_TRANS TB1
               JOIN EX_INVOICE_DOKUMEN_INV TB2 ON TB1.ID_INVOICE_DOKUMEN = TB2.ID
               WHERE NO_INVOICE = :no_inv
               ORDER BY TB2.NAMA_DOKUMEN";
    
    $querycek = oci_parse($conn, $sqlchek);
    oci_bind_by_name($querycek, ":no_inv", $no_inv);
    
    if (!oci_execute($querycek)) {
        $error = oci_error($querycek);
        echo "Error executing main query: " . $error['message'] . "<br>";
        die();
    }
    
    // Count results
    $temp_data = array();
    while ($temp_row = oci_fetch_assoc($querycek)) {
        $temp_data[] = $temp_row;
    }
    $row_count = count($temp_data);
    
    // If no data found, try to generate
    if ($row_count == 0) {
        echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px; border-radius: 5px;'>";
        echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ No dokumen trans data found</h4>";
        echo "<p style='color: #856404;'>Attempting to generate dokumen trans data for invoice: <strong>$no_inv</strong>...</p>";
        
        if (generateDokumenTrans($conn, $no_inv, $user_id)) {
            echo "<p style='color: green;'><strong>✓ Data generated successfully!</strong> Reloading page...</p>";
            echo '</div>';
            echo '<script>setTimeout(function(){ window.location.reload(); }, 3000);</script>';
            exit;
        } else {
            echo "<p style='color: red;'><strong>✗ Failed to generate dokumen trans data.</strong></p>";
            echo "<p style='color: #856404;'>Please contact administrator to check:</p>";
            echo "<ul style='color: #856404;'>";
            echo "<li>Invoice exists in EX_TRANS_HDR table</li>";
            echo "<li>KELOMPOK_TRANSAKSI and TIPE_TRANSAKSI mapping exists in EX_INVOICE_DOKUMEN_GROUP</li>";
            echo "<li>Document IDs exist in EX_INVOICE_DOKUMEN_INV</li>";
            echo "</ul>";
            echo "</div>";
            die();
        }
    }
    
    // Re-execute query for display
    $querycek = oci_parse($conn, $sqlchek);
    oci_bind_by_name($querycek, ":no_inv", $no_inv);
    oci_execute($querycek);
    
    // Get BA invoice data
    $sql_invoice_ba = "SELECT EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 
                      FROM EX_BA_INVOICE 
                      WHERE NO_INVOICE = :no_inv AND DIPAKAI = 1";
    $query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
    oci_bind_by_name($query_invoice_ba, ":no_inv", $no_inv);
    oci_execute($query_invoice_ba);
    
    $data_invoice_ba = array();
    while($row = oci_fetch_array($query_invoice_ba)) {
        $data_invoice_ba = $row;
    }
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Aplikasi SGG Online: Input Cost Claim :)</title>
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
    <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../include/jquery.min.js"></script>
    <script type="text/javascript">
        function checkstatus() {
            var tdok = $("#tdok").val();
            var datacek = 0;
            var tanya;
            
            $.each($('.cek:checked'), function(index, val) {
                datacek++;
            });
            
            if (datacek == tdok) {
                tanya = confirm('Dokumen Telah lengkap, apakah anda yakin untuk melanjutkan proses PPL ?');
            } else {
                tanya = confirm('Dokumen Tidak lengkap, apakah anda yakin untuk melanjutkan proses Pengembalian Ke vendor ?');
            }
            
            return tanya;
        }
        
        function popUp(url) {
            window.open(url, 'popup', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }
    </script>
</head>

<body>
    <div align="center">
        <?php if (isset($_POST['save'])): ?>
            <center>Data berhasil disimpan <br><input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" /></center>
        <?php else: ?>
            <br /><br /> 
            <table class="adminlist" cellspacing="0" cellpadding="0" border="0" width="60%" align="center">
                <thead>
                    <tr class="quote">
                        <td align="center" style="width: 40px;"><strong>NO</strong></td>
                        <td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
                        <td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
                        <td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
                        <td align="center" style="width: 200px;"><strong>Submit By</strong></td> 
                    </tr>
                </thead>
                <tbody id="isiLampiran">
                    <?php
                    $lampiran = (!empty($data_invoice_ba['LAMPIRAN'])) ? json_decode($data_invoice_ba['LAMPIRAN'], true) : array();
                    $bulan = array(null, "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");
                    
                    foreach($lampiran as $i => $l) {
                        if (is_array($l) && count($l) >= 5) {
                            $tgl = explode("-", $l[1]);
                            $fileUrl = "./lampiran/".htmlspecialchars($l[3]);

                            if (strtolower($l[0]) == "ba rekapitulasi") {
                                $fileUrl = "../ex_ba_sp/upload/".htmlspecialchars($l[3]);
                            }

                            echo "<tr class='row-file'>
                                    <td align='center' class='nomor-file'>".($i+1)."</td>
                                    <td align='center'>".htmlspecialchars($l[0])."</td>
                                    <td align='center'>".$tgl[2]."-".$bulan[intval($tgl[1])]."-".$tgl[0]."</td>
                                    <td align='center'><a href='javascript:popUp(\"" . $fileUrl . "\")'>".htmlspecialchars($l[2])."</a></td>
                                    <td align='center'>".htmlspecialchars($l[4])."</td> 
                                  </tr>";
                        }
                    }
                    ?>
                </tbody>
            </table> 
            <br /><br />

            <form id="data_claim" name="data_claim" method="post" action="invoice_dokumen_trans_proses_fixed.php" >
                <div align="center">
                    <table width="95%" align="center" class="adminlist">
                        <tr>
                            <th align="left" colspan="4"><span class="style5">&nbsp;Dokumen Invoice <?php echo htmlspecialchars($no_inv); ?> </span></th>
                        </tr>
                    </table>
                </div> 
                
                <div align="center">
                    <table width="95%" align="center" class="adminlist">
                        <thead>
                            <tr class="quote">
                                <td><strong>&nbsp;&nbsp;No.</strong></td>
                                <td align="center"><strong>Dokumen</strong></td>
                                <td align="center"><strong>Cheklist</strong></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $b = 0;  
                            while ($datafunc = oci_fetch_assoc($querycek)) {
                                $b++;
                            ?>     
                            <tr>
                                <td align="center"><?php echo $b; ?></td>
                                <td align="center"><?php echo htmlspecialchars($datafunc['NAMA_DOKUMEN']); ?></td>	
                                <td align="center">
                                    <input type="checkbox" class="cek" value="1" 
                                           name="DOKTRANS[<?php echo $datafunc['ID']; ?>]" 
                                           <?php echo (($datafunc['STATUS']) ? 'checked' : ''); ?> />
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                        
                        <input type="hidden" name="id_inv" value="<?php echo htmlspecialchars($no_inv); ?>" />
                        <input type="hidden" name="totaldok" id="tdok" value="<?php echo $b; ?>">
                        
                        <tr class="quote">
                            <td colspan="3" align="center">
                                <?php if ($b > 0): ?>
                                    <input name="save" type="submit" class="button" value="Simpan" onClick="return checkstatus();" />	
                                <?php else: ?>
                                    <span style="color: red; font-weight: bold;">Tidak ada dokumen yang tersedia untuk invoice ini</span><br><br>
                                <?php endif; ?>	 
                                <input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" />		 
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
        <?php endif; ?>
    </div>
    
    <?php include('../include/ekor.php'); ?>
</body>
</html> 