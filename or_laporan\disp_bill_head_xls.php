<? 
session_start();
include ('../include/or_fungsi.php');
require_once ('../MainPHPExcel/MainPHPExcel.php'); 
require_once('../include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=151;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$soldto=$fungsi->sapcode($distr_id);

if ($fungsi->keamanan0($user_id)==0) {
?>
    <SCRIPT LANGUAGE="JavaScript">
        <!--
        alert("You are not authorized to access this page.... \n Login Please...");
        //-->
    </SCRIPT>

    <a href="../index.php">Login....</a>
<?

exit();
}

if($_SESSION['channel'] == '50'){
    $nama = "Customer";
} else{
    $nama = "Sold To Party";
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
	$X_KUNNR =  $_POST['sold_to'];//$soldto;
	$X_VBELN_FR = $_POST['vbeln1'];
	$X_VBELN_TO = $_POST['vbeln2'];				
        $X_TGL_1 = $_POST['tgl1'];	
        $X_TGL_2 = $_POST['tgl2'];
        $X_FKART = $_POST['bill_type'];

	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALINV_HEAD2");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri		
		$fce->X_VBELN_FR	= $X_VBELN_FR; 
		$fce->X_VBELN_TO	= $X_VBELN_TO;		
		$fce->X_TGL_1		= $X_TGL_1; 
		$fce->X_TGL_2		= $X_TGL_2;	
        $fce->X_VKORG = $user_org;			
		$fce->X_KUNNR = $X_KUNNR;	       
        $fce->X_FKART = $X_FKART;


		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {	
			$fce->ZHEAD->Reset();
			$s=1;
			while ( $fce->ZHEAD->Next() ){
			$NO_ACC_SAP[$s]= $fce->ZHEAD->row["NO_ACC_SAP"];
			$NO_BILL_SAP[$s]= $fce->ZHEAD->row["NO_BILL_SAP"];
			$TGL_BILL[$s]= $fce->ZHEAD->row["TGL_BILL"];
			$TOT_AMOUNT[$s]= $fce->ZHEAD->row["TOT_AMOUNT"];
			$CURR[$s]= $fce->ZHEAD->row["CURR"];
                        $ZTERM[$s]= $fce->ZHEAD->row["ZTERM"];
                        $TGL_JTHTEMP[$s]= $fce->ZHEAD->row["TGL_JTHTEMP"];
			$KUNRG[$s]= $fce->ZHEAD->row["KUNRG"];
			$KUNAG[$s]= $fce->ZHEAD->row["KUNAG"];
			if($user_org=='6000'){
                            $NAME1[$s]= $fce->ZHEAD->row["SOLDTO_NAME"];
                        }else{
                            $NAME1[$s]= $fce->ZHEAD->row["NAME1"];                            
                        }
			$NAME2[$s]= $fce->ZHEAD->row["NAME2"];
			$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($NO_ACC_SAP);	

if($total>0){
       
        $namafile="billing.xls";
        send($namafile);
        $WritePHPExcel = new PHPExcel();
        $WritePHPExcel->setActiveSheetIndex(0);
        $colomSt='A';
        $WritePHPExcel->getActiveSheet()->setTitle('Billing List');//title sheet
        $Worksheet1 = $WritePHPExcel->getActiveSheet();
        
        //head excel
        $Worksheet1->setCellValueByColumnAndRow(0, 1, $translatebhsxx->__xls('No.'));
        $Worksheet1->setCellValueByColumnAndRow(1, 1, $translatebhsxx->__xls('No. Invoice (Acc)'));
        $Worksheet1->setCellValueByColumnAndRow(2, 1, $translatebhsxx->__xls('No. Billing'));
        $Worksheet1->setCellValueByColumnAndRow(3, 1, $translatebhsxx->__xls('Date'));
        $Worksheet1->setCellValueByColumnAndRow(4, 1, $translatebhsxx->__xls('Amount'));
        $Worksheet1->setCellValueByColumnAndRow(5, 1, $translatebhsxx->__xls('Currency'));
        $Worksheet1->setCellValueByColumnAndRow(6, 1, $translatebhsxx->__xls('TOP'));
        $Worksheet1->setCellValueByColumnAndRow(7, 1, $translatebhsxx->__xls('Due Date'));
        $Worksheet1->setCellValueByColumnAndRow(8, 1, $translatebhsxx->__xls('Payer'));
        $Worksheet1->setCellValueByColumnAndRow(9, 1, $translatebhsxx->__xls($name));
        $Worksheet1->setCellValueByColumnAndRow(10, 1, $translatebhsxx->__xls('Payer Name'));       
        $Worksheet1->setCellValueByColumnAndRow(11, 1, $translatebhsxx->__xls($name.' Name'));  
        $Worksheet1->setCellValueByColumnAndRow(12, 1, $translatebhsxx->__xls('No. Ship'));   
        $Worksheet1->setCellValueByColumnAndRow(13, 1, $translatebhsxx->__xls('No. DO'));   
        $Worksheet1->setCellValueByColumnAndRow(14, 1, $translatebhsxx->__xls('Date Good Issue'));   
        $Worksheet1->setCellValueByColumnAndRow(15, 1, $translatebhsxx->__xls('No. SO'));
        if ($user_org=='6000') {
            $Worksheet1->setCellValueByColumnAndRow(16, 1, $translatebhsxx->__xls('NO. Delivery Note'));
            $Worksheet1->setCellValueByColumnAndRow(17, 1, $translatebhsxx->__xls('Line SO')); 
            $Worksheet1->setCellValueByColumnAndRow(18, 1, $translatebhsxx->__xls('No. Ship To Party'));
            $Worksheet1->setCellValueByColumnAndRow(19, 1, $translatebhsxx->__xls('Ship To Party'));
            $Worksheet1->setCellValueByColumnAndRow(20, 1, $translatebhsxx->__xls('Address'));
            $Worksheet1->setCellValueByColumnAndRow(21, 1, $translatebhsxx->__xls('Material'));
            $Worksheet1->setCellValueByColumnAndRow(22, 1, $translatebhsxx->__xls('Material Description'));
            $Worksheet1->setCellValueByColumnAndRow(23, 1, $translatebhsxx->__xls('Cummulative Order Quantity'));
            $Worksheet1->setCellValueByColumnAndRow(24, 1, $translatebhsxx->__xls('Measure'));
            $Worksheet1->setCellValueByColumnAndRow(25, 1, $translatebhsxx->__xls('Price IDR'));
            $Worksheet1->setCellValueByColumnAndRow(26, 1, $translatebhsxx->__xls('Price USD'));
            $Worksheet1->setCellValueByColumnAndRow(27, 1, $translatebhsxx->__xls('Total Amount (IDR)'));
            $Worksheet1->setCellValueByColumnAndRow(28, 1, $translatebhsxx->__xls('License Plate'));
            $Worksheet1->setCellValueByColumnAndRow(29, 1, $translatebhsxx->__xls('Driver Name'));
            $Worksheet1->setCellValueByColumnAndRow(30, 1, $translatebhsxx->__xls('Drive License'));
        } else {  
            $Worksheet1->setCellValueByColumnAndRow(16, 1, $translatebhsxx->__xls('NO. SPJ'));
            $Worksheet1->setCellValueByColumnAndRow(17, 1, $translatebhsxx->__xls('No. Ship To Party'));
            $Worksheet1->setCellValueByColumnAndRow(18, 1, $translatebhsxx->__xls('Ship To Party'));
            $Worksheet1->setCellValueByColumnAndRow(19, 1, $translatebhsxx->__xls('Address'));
            $Worksheet1->setCellValueByColumnAndRow(20, 1, $translatebhsxx->__xls('Material'));
            $Worksheet1->setCellValueByColumnAndRow(21, 1, $translatebhsxx->__xls('Material Description'));
            $Worksheet1->setCellValueByColumnAndRow(22, 1, $translatebhsxx->__xls('Cummulative Order Quantity'));
            $Worksheet1->setCellValueByColumnAndRow(23, 1, $translatebhsxx->__xls('Measure'));
            $Worksheet1->setCellValueByColumnAndRow(24, 1, $translatebhsxx->__xls('Price IDR'));
            $Worksheet1->setCellValueByColumnAndRow(25, 1, $translatebhsxx->__xls('Price USD'));
            $Worksheet1->setCellValueByColumnAndRow(26, 1, $translatebhsxx->__xls('Total Amount (IDR)'));
        }

        
        
        $colomAk = $Worksheet1->getHighestColumn();
        if($colomSt!='' && $colomAk!=''){
            $WritePHPExcel->getActiveSheet()->getStyle($colomSt."1:".$colomAk."1")->getFont()->setBold(true);//bold header
        }
        $Worksheet1->getStyle($colomSt.'1:'.$colomAk.'1')->applyFromArray($styleHead);//style head         
        // Version 4 fixed
        for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true);//auto size
        }
	
	for ($i=1; $i <= $total; $i++) {                
                //setROW
        $j=$i+1;
        if($colomSt!='' && $colomAk!='' ){
             $Worksheet1->getStyle($colomSt.$j.':'.$colomAk.$j)->applyFromArray($styleBorder);//style border record
        }
        $Worksheet1->setCellValueByColumnAndRow(0, $j, $i);
        $Worksheet1->setCellValueByColumnAndRow(1, $j, $NO_ACC_SAP[$i]);
		$Worksheet1->setCellValueByColumnAndRow(2, $j, $NO_BILL_SAP[$i]);
        $tgl_bill_raw = $TGL_BILL[$i]; // Misalnya format aslinya YYYYMMDD
        $tgl_bill_formatted = '';

        if (!empty($tgl_bill_raw) && strlen($tgl_bill_raw) == 8) {
            $tgl_bill_formatted = date('d-m-Y', strtotime(substr($tgl_bill_raw, 0, 4) . '-' . substr($tgl_bill_raw, 4, 2) . '-' . substr($tgl_bill_raw, 6, 2)));
        }
        $Worksheet1->setCellValueByColumnAndRow(3, $j, $tgl_bill_formatted);
        $Worksheet1->setCellValueByColumnAndRow(4, $j, $TOT_AMOUNT[$i]);
        $Worksheet1->setCellValueByColumnAndRow(5, $j, $CURR[$i]);
        $Worksheet1->setCellValueByColumnAndRow(6, $j, $ZTERM[$i]);
        $Worksheet1->setCellValueByColumnAndRow(7, $j, $TGL_JTHTEMP[$i]);
        $Worksheet1->setCellValueByColumnAndRow(8, $j, $KUNRG[$i]);
        $Worksheet1->setCellValueByColumnAndRow(9, $j, $KUNAG[$i]);
        $Worksheet1->setCellValueByColumnAndRow(10, $j, $NAME1[$i]);
        $Worksheet1->setCellValueByColumnAndRow(11, $j, $NAME2[$i]);

        $sap2 = new SAPConnection();
	    $sap2->Connect("../include/sapclasses/logon_data.conf");
		if ($sap2->GetStatus() == SAPRFC_OK ) $sap2->Open ();
		if ($sap2->GetStatus() != SAPRFC_OK ) {
            echo $sap2->PrintStatus();
            exit;
		}

		$fce2 = $sap2->NewFunction ("Z_ZAPPSD_RPT_REALINV_DET2");
		if ($fce2 == false ) {
            $sap2->PrintStatus();
            exit;
		}

        $fce2->X_VBELN_FR = $NO_BILL_SAP[$i];
        $fce2->X_KUNNR = $KUNRG[$i];

        $fce2->Call();

        if ($fce2->GetStatus() == SAPRFC_OK ) {	
            $fce2->ZDATA->Reset();
			$no=1;
            while ( $fce2->ZDATA->Next() ){
                $Worksheet1->setCellValueByColumnAndRow(12, $j, $fce2->ZDATA->row['NO_SHIPMENT_SAP']);   
                $Worksheet1->setCellValueByColumnAndRow(13, $j, $fce2->ZDATA->row['NO_DO_SAP']);   
                $tgl_gi_raw = $fce2->ZDATA->row['TGL_GI']; // asumsi format: YYYYMMDD
                $tgl_gi_formatted = '';
                if (!empty($tgl_gi_raw) && strlen($tgl_gi_raw) == 8) {
                    $tgl_gi_formatted = date('d-m-Y', strtotime(substr($tgl_gi_raw, 0, 4) . '-' . substr($tgl_gi_raw, 4, 2) . '-' . substr($tgl_gi_raw, 6, 2)));
                }
                $Worksheet1->setCellValueByColumnAndRow(14, $j, $tgl_gi_formatted);
                $Worksheet1->setCellValueByColumnAndRow(15, $j, $fce2->ZDATA->row["NO_SO_SAP"]);
                    $Worksheet1->setCellValueByColumnAndRow(16, $j, $fce2->ZDATA->row["NO_SPJ"]);
                if ($user_org=='6000') {
                    $Worksheet1->setCellValueByColumnAndRow(17, $j, $fce2->ZDATA->row["POSNR"]); 
                    $Worksheet1->setCellValueByColumnAndRow(18, $j, $fce2->ZDATA->row["SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(19, $j, $fce2->ZDATA->row["NAMA_SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(20, $j, $fce2->ZDATA->row["ALM_SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(21, $j, $fce2->ZDATA->row["PRODUK"]);
                    $Worksheet1->setCellValueByColumnAndRow(22, $j, $fce2->ZDATA->row["DESC1"]);
                    $Worksheet1->setCellValueByColumnAndRow(23, $j, number_format($fce2->ZDATA->row["QTY"],0));
                    $Worksheet1->setCellValueByColumnAndRow(24, $j, $fce2->ZDATA->row["UOM"]);
                    $Worksheet1->setCellValueByColumnAndRow(25, $j, number_format($fce2->ZDATA->row["PRICE_IDR"],"."));
                    $Worksheet1->setCellValueByColumnAndRow(26, $j, $fce2->ZDATA->row["PRICE_US"]);
                    $Worksheet1->setCellValueByColumnAndRow(27, $j, number_format($fce2->ZDATA->row["TOT_AMOUNT"],"."));
                    $Worksheet1->setCellValueByColumnAndRow(28, $j, $fce2->ZDATA->row["NO_POLISI"]);
                    $Worksheet1->setCellValueByColumnAndRow(29, $j, $fce2->ZDATA->row["NAMA_SUPIR"]);
                    $Worksheet1->setCellValueByColumnAndRow(30, $j, $fce2->ZDATA->row["NO_SIM"]);
                } else {   
                    $Worksheet1->setCellValueByColumnAndRow(17, $j, $fce2->ZDATA->row["SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(18, $j, $fce2->ZDATA->row["NAMA_SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(19, $j, $fce2->ZDATA->row["ALM_SHIP_TO"]);
                    $Worksheet1->setCellValueByColumnAndRow(20, $j, $fce2->ZDATA->row["PRODUK"]);
                    $Worksheet1->setCellValueByColumnAndRow(21, $j, $fce2->ZDATA->row["DESC1"]);
                    $Worksheet1->setCellValueByColumnAndRow(22, $j, number_format($fce2->ZDATA->row["QTY"],3));
                    $Worksheet1->setCellValueByColumnAndRow(23, $j, $fce2->ZDATA->row["UOM"]);
                    $Worksheet1->setCellValueByColumnAndRow(24, $j, number_format($fce2->ZDATA->row["PRICE_IDR"],"."));
                    $Worksheet1->setCellValueByColumnAndRow(25, $j, $fce2->ZDATA->row["PRICE_US"]);
                    $Worksheet1->setCellValueByColumnAndRow(26, $j, number_format($fce2->ZDATA->row["TOT_AMOUNT"],"."));
                }
                $no++;
            }
        } else {
            $fce2->PrintStatus();
        }

        $fce2->Close();
        $sap2->Close();
	}
        
        $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
        //$objWriter->save($namafile);
        $objWriter->save("php://output");
}
?>
