<?
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$id_user=$_SESSION['id_user'];
//$hakakses=array("adm","admin");
//$fungsi->keamanan($hakakses);
$currentPage="print_draft_ppl.php";
$no_invoice= $_REQUEST['no_invoice'];
$tanggal_cetak= date('d-m-Y');

require_once ('../auth_validation.php');
validation_(basename($_SERVER['SCRIPT_NAME']), $_SESSION['user_id']);

###### alpeen inv: 0000202440  req 05-06-2014 by rofi'i no tiket 73257
$sqlU = "select last_update_date, last_updated_by, kelompok_transaksi, tipe_transaksi from ex_trans_hdr where no_invoice=:no_invoice and delete_mark='0' and rownum=1";
$query_U= oci_parse($conn, $sqlU);
oci_bind_by_name($query_U, ":no_invoice", $no_invoice);
oci_execute($query_U);
$row_update=oci_fetch_array($query_U);
$last_update_date=$row_update[LAST_UPDATE_DATE];  
$last_updated_by=$row_update[LAST_UPDATED_BY];
$tipe_transx = $row_update[TIPE_TRANSAKSI];
$kel_trans=strtoupper(trim($row_update[KELOMPOK_TRANSAKSI]));

//liyantanto
$p1mna=0;
$sql_pjaknew="
            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE=:no_invoice
                group by NO_INVOICE)
                order by TGL_PAJAK_EX desc
            ) where rownum =1
    ";
$querycek= oci_parse($conn, $sql_pjaknew);
oci_bind_by_name($querycek, ":no_invoice", $no_invoice);
oci_execute($querycek);
$row_datap=oci_fetch_assoc($querycek);unset($tglfakturpajak);
$tglfakturpajak=$row_datap[TGL_PAJAK_EXF];
$VENDORpj=$row_datap[NO_VENDOR];
if($VENDORpj!='**********'){
if($tglfakturpajak!='' && $tglfakturpajak>='********'){
    $p1mna=1;
}
}

if($tipe_transx != 'BAG' && $kel_trans!='LAUT'){
   $sql_text = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
   $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC"; 
   
   $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice GROUP BY ".$sql_group .""; //AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********'
   }else{
###### end alpeen 05-06-2014
        if($kel_trans=='LAUT'){
            $sql_text = "select * from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }
        else {    
            $sql_text = "select * from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }

        $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice GROUP BY ".$sql_group .") where KLAIM_SEMEN>=KLAIM_LEBIH";
   }
	// asli $sql= "SELECT ACCOUNTING_DOC,NO_GL_SHP,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' GROUP BY NO_GL_SHP,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
//        echo "dos : ".$sql;
	$query= oci_parse($conn, $sql);
	oci_bind_by_name($query, ":no_invoice", $no_invoice);
	oci_execute($query);
	$total_invoice =0;
	$oa_semen_v=0;
        $arr_soldto_cek = array();
	while($row=oci_fetch_array($query)){
                $no_gl_shp_v=$row[NO_GL_SHP];
		$no_invoice_v=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$no_inv_sap_v=$row[NO_INV_SAP];
		$no_acc_doc_v=$row[ACCOUNTING_DOC];
		$warna_plat_v=$row[WARNA_PLAT]; 
		$vendor_v=$row[VENDOR]; 

		$sold_to_v[]=$row[SOLD_TO];
                $arr_soldto_cek[$row[SOLD_TO]] = $row[SOLD_TO];
                
		$klaim_ktg_v[]=$row[KLAIM_KTG];  
		$klaim_rezak_v[]=$row[KLAIM_REZAK];  
		$klaim_semen_v[]=$row[KLAIM_SEMEN];  
		$klaim_pdpks_v[]=$row[KLAIM_PDPKS];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		

		$distributor = $row[SOLD_TO];
		$sql_bn= "SELECT NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice AND SOLD_TO = :distributor ORDER BY ID DESC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
	$query_bn= oci_parse($conn, $sql_bn);
	oci_bind_by_name($query_bn, ":no_invoice", $no_invoice);
	oci_bind_by_name($query_bn, ":distributor", $distributor);
	oci_execute($query_bn);
	$row_bn=oci_fetch_array($query_bn);

		$nama_vendor_v=$row_bn[NAMA_VENDOR]; 
		$nama_sold_to_v[]=$row_bn[NAMA_SOLD_TO];
		$no_rek_dis_v[]=$row_bn[NO_REK_DIS];
		$nama_bank_dis_v[]=$row_bn[NAMA_BANK_DIS];
		$cabang_bank_dis_v[]=$row_bn[BANK_CABANG_DIS];  

	}
	$total=count($sold_to_v);

		$sql_inv= "SELECT * FROM EX_INVOICE WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice ";
		$query_inv= oci_parse($conn, $sql_inv);
		oci_bind_by_name($query_inv, ":no_invoice", $no_invoice);
		oci_execute($query_inv);
		$row_inv=oci_fetch_array($query_inv);
		$unit_v=$row_inv[UNIT];  
		$lokasi_v=$row_inv[LOKASI];  
		$kepala_v=$row_inv[KEPALA];  
		$atasan_v=$row_inv[ATASAN];  
		$verifikasi_v=$row_inv[VERIFIKASI];  
		$cabang_ex_v=$row_inv[BANK_CABANG];  
		$no_rek_ex_v=$row_inv[NO_REKENING];  
		$bank_ex_v=$row_inv[BANK];  
		$no_pajak_ex=$row_inv[NO_PAJAK_EX];  		
		$komentar_v=$row_inv[KETERANGAN]; 
                $orginrr=$row_inv[ORG]; 
                $npwpvendor=$row_inv[NPWP_VENDOR];
				$nitkuvendor=$row_inv[NITKU];
				var_dump($npwpvendor);
				var_dump($nitkuvendor);
                $tahntermin=$row_inv[TGL_TERMIN]; 
                $approvekasi=$row_inv[APPROVE_KASI]; 
                $approvekabiro=$row_inv[APPROVE_KABIRO]; 

		$sql_kom= "SELECT * FROM EX_KOMPONEN_INV WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice order by KODE_KOMPONEN asc";
		$query_kom= oci_parse($conn, $sql_kom);
		oci_bind_by_name($query_kom, ":no_invoice", $no_invoice);
//                echo $sql_kom;
		oci_execute($query_kom);
                $kode_komponen_lama='';
		while($row_kom=oci_fetch_array($query_kom)){
                    if($row_kom[KODE_KOMPONEN]!=$kode_komponen_lama || $row_kom[KODE_KOMPONEN]=='SG-K3-2017'){
                        $kode_komponen_lama = $row_kom[KODE_KOMPONEN];  
                        $kode_komponen_v[]=$row_kom[KODE_KOMPONEN];  
                        $nama_komponen_v[]=$row_kom[NAMA_KOMPONEN];  
                        $no_gl_v[]=$row_kom[NO_GL];  
                        $cost_center_v[]=$row_kom[COST_CENTER];  
                        $profit_v[]=$row_kom[PROFIT_CENTER];  
                        $kode_pajak_v[]=$row_kom[KODE_PAJAK];  
                        $nilai_pajak_v[]=$row_kom[PAJAK];  

                        $total_komponen_v[]=$row_kom[TOTAL];  
                        $sub_total_v[]=$row_kom[SUB_TOTAL];  
                        $keterangan_komponen_v[]=$row_kom[KETERANGAN];  
                        $total_invoice = $total_invoice + $row_kom[TOTAL];
                    }
		}
                $n_komponen = count($kode_komponen_v);
                if($n_komponen>1) $ada_klaim = false; else $ada_klaim = true;
                
    $sqmklm="select to_char(TANGGAL_KIRIM,'YYYY') as TAHUNKIRIM FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice";            
    $querysqmklm= oci_parse($conn, $sqmklm);
	oci_bind_by_name($querysqmklm, ":no_invoice", $no_invoice);
    oci_execute($querysqmklm);
    $rowyy=oci_fetch_array($querysqmklm);
    $tahnterminmo=$rowyy[TAHUNKIRIM];  
    $nmkali="KSO SEMEN GRESIK - SEMEN INDONESIA ";
    if(($tahnterminmo!='2017' && $orginrr=='7000')||($tahnterminmo!='2017' && $orginrr=='7900')){            
        $nmkali="PT SEMEN INDONESIA (PERSERO) Tbk ";
        
    }
    else if($orginrr == '5000'){
         $nmkali="PT SEMEN GRESIK";
    }
    else if($orginrr == '4000'){
         $nmkali="PT SEMEN TONASA";
    }
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>print PPL</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link href="../Templates/css/print_draf_ppl.css" rel="stylesheet" type="text/css" media="print" />
</head>

<body>
<div style="page-break-after:always">
<table width="98%" align="center" border="0">
<tr>
  <td colspan="7"><strong><?=$nmkali;?></strong></td>
  <td align="right" colspan="3"><?=$last_update_date.' '.$last_updated_by; ?></td>
</tr>
<tr>
        <?if($tahnterminmo=='2017' && $orginrr=='7000'){?>
        <td colspan="10">
          <div align="center">PERMINTAAN PEMBAYARAN</div>
        </td>
        <?} else if($tahnterminmo!='2017' && $orginrr=='7000'){?>
        <td colspan="10">
          <div align="center">PERMINTAAN PEMBAYARAN</div>
        </td>
        <?} else if($orginrr=='7900'){?>
        <td colspan="10">
          <div align="center">PERMINTAAN PEMBAYARAN</div>
        </td>
        <?} else if($orginrr == '5000'){?>
        <td colspan="10">
          <div align="center">LEMBAR VERIFIKASI</div>
        </td>
        <?} else if($orginrr == '4000'){?>
        <td colspan="10">
          <div align="center">LEMBAR PEMBAYARAN</div>
        </td>
        <?}?>    
</tr>
<tr >
  <td colspan="10" ><div align="center" >No. <?=$no_inv_sap_v?> / No Acc : <? echo $no_acc_doc_v; ?></div></td>
</tr>
<tr >
  <td colspan="10" style="border-bottom:2px #000000 solid;">
  <table width="100%">		
        <tr >
            <td > Lokasi </td>
            <td >:</td>
            <td > <? echo $lokasi_v;?> </td>
            <td >  </td>
            <?if($tahnterminmo=='2017' && $orginrr=='7000'){?>
            <td > Unit Kerja Peminta </td>
            <td >:</td>
            <td > <? echo $unit_v;?>  </td>
            <?} else if($tahnterminmo!='2017' && $orginrr=='7000'){?>
            <td > Unit Kerja Peminta </td>
            <td >:</td>
            <td > <? echo $unit_v;?>  </td>
            <?} else if( $orginrr=='7900'){?>
            <td > Unit Kerja Peminta </td>
            <td >:</td>
            <td > <? echo $unit_v;?>  </td>
             <?} else if($orginrr == '5000'){?>
            <td > </td>
            <td ></td>
            <td >  </td>
              <?} else if($orginrr == '4000'){?>
            <td > </td>
            <td ></td>
            <td >  </td>
             <?}?>
        </tr>
	<tr >
	<td style=" border-bottom:2px #000000 solid;" colspan="7">&nbsp; </td>
	</tr>

  	<tr >
  	  <td>Jenis Permintaan </td>
  	  <td>:</td>
  	  <td>OA<?=substr($kel_trans,0,1);?></td>
  	  <td></td>
  	  <td>No Permintaan UM </td>
  	  <td>:</td>
  	  <td>&nbsp;</td>
  	  </tr>
  	<tr >
  	  <td>Langsung</td>
  	  <td>:</td>
  	  <td>X</td>
  	  <td></td>
  	  <td> <? 
	  if ($no_pajak_ex != "" )
	  echo "No Pajak Expeditur"; ?></td>
  	  <td><? if ($no_pajak_ex != "" )
	  echo ":"; ?></td>
  	  <td>
		<? if ($no_pajak_ex != "" )
	  echo $no_pajak_ex; ?>	  </td>
  	  </tr>
  	<tr>
  	  <td style=" border-bottom:2px #000000 solid;">Pertanggung Jawaban </td>
  	  <td style=" border-bottom:2px #000000 solid;">:</td>
  	  <td style=" border-bottom:2px #000000 solid;">&nbsp;</td>
  	  <td style=" border-bottom:2px #000000 solid;"></td>
  	  <td style=" border-bottom:2px #000000 solid;">Batch No </td>
  	  <td style=" border-bottom:2px #000000 solid;">:</td>
  	  <td style=" border-bottom:2px #000000 solid;">&nbsp;</td>
	  </tr>
  	<tr >
  	  <td colspan="7" style=" border-bottom:2px #000000 solid;">DATA ANGGARAN </td>
  	  </tr>
  	<tr >
  	  <td colspan="7" style=" border-bottom:2px #000000 solid;">Koordinator Anggaran [KA] : 3026 </td>
  	  </tr>
  	<tr >
  	  <td>Kode Vendor </td>
  	  <td>:</td>
  	  <td><?=$vendor_v?></td>
  	  <td></td>
  	  <td>Dibayarkan Kpd </td>
  	  <td>:</td>
  	  <td><?=$vendor_v?></td>
	  </tr>
  	<tr >
  	  <td>Nama Vendor </td>
  	  <td>:</td>
  	  <td><?=$nama_vendor_v?></td>
  	  <td></td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  <td><?=$nama_vendor_v?></td>
	  </tr>
  	<tr >
  	  <td>No Kontrak </td>
  	  <td>:</td>
  	  <td>&nbsp;</td>
  	  <td></td>
  	  <td>No Rekening </td>
  	  <td>:</td>
  	  <td><?=$bank_ex_v?> / <?=$no_rek_ex_v?></td>
	  </tr>
  	<tr >
  	  <td>No Invoice </td>
  	  <td>:</td>
  	  <td><? echo $no_invoice." / ". $no_invoice_ex_v;?></td>
  	  <td></td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  <td><?=$cabang_ex_v?></td>
	  </tr>
  	<tr >
		<td><? if($npwpvendor!=''){echo "NPWP";}?></td>
		<td>:</td>
		<td><?=$npwpvendor;?></td>
		<td>  </td>
		<td>Alat Pembayaran </td>
		<td>:</td>
		<td>TRANSFER BANK  </td>
		</tr>
		<tr >
    <td><? if($nitkuvendor!=''){echo "NITKU";}?></td>
    <td>:</td>
    <td><?=$nitkuvendor;?></td>
    <td>  </td>
    </tr>
  </table>  </td>
</tr  >
<tr>
  <td colspan="10" style=" border-bottom:2px #000000 solid;">&nbsp;</td>
  </tr>
<tr>
	<td style=" border-bottom:2px #000000 solid;"> NO</td>
	<td style=" border-bottom:2px #000000 solid;">Kode GL</td>
	<td style=" border-bottom:2px #000000 solid;">Kode  </td>
	<td style=" border-bottom:2px #000000 solid;">Nama Komponen </td>
	<td style=" border-bottom:2px #000000 solid;">Cost Center</td>
	<td style=" border-bottom:2px #000000 solid;">Profit Center</td>
	<td style=" border-bottom:2px #000000 solid;">Pajak </td>
	<td style=" border-bottom:2px #000000 solid;">Keterangan</td>
	<td style=" border-bottom:2px #000000 solid;"> Jumlah</td>
	<td style=" border-bottom:2px #000000 solid;">Mata Uang</td>
</tr>

<? $x = count($kode_komponen_v);

	for ($i=0; $i < count($kode_komponen_v); $i++){ 
		$b++;
		if ($kode_komponen_v[$i] == "SG0001"){
			if ($kode_pajak_v[$i] == "VN" || $kode_pajak_v[$i] == "VX") {
				$pajak_pass = "T";
				$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}
			// else if ($kode_pajak_v[$i] == "YN" ) {
			// $pajak_pass = "TSS";
			// $nilai_pajak_oa = round($nilai_pajak_v[$i]);
      		// }	
			else if ($kode_pajak_v[$i] == "EN" ) { // PPN 12%
        		$pajak_pass = "T12";
				$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}	else {
				$pajak_pass = "F";
				$nilai_pajak_oa = 0;
			}

			if($no_gl_v[$i] == "0068140006"){
				$no_gl_v[$i] = "0021290001";
			} else {
				$no_gl_v[$i];
			}
?>		
	<tr>
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_gl_v[$i]; ?></td>
		<td align="center"><? echo $kode_komponen_v[$i]; ?></td>
		<td align="left"><? echo $nama_komponen_v[$i]; ?></td>
		<td align="center"><? echo $cost_center_v[$i]; ?></td>
		<td align="center"><? echo $profit_v[$i]; ?></td>
		<td align="center"><? echo $kd_p = $kode_pajak_v[$i]; ?></td>
		<td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
		<td align="right"><? echo number_format($tot_i = $sub_total_v[$i],0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<?              $oa_nilai += $sub_total_v[$i]; //Perhitungan TOTAL
 //               echo "hehe".$sub_total_v;
                //echo '<pre>'; print_r($sub_total_v); echo '</pre>';
//WAPU zone
if($kd_p=='WN' || $kd_p=='WX' || $kd_p=='RN' || $kd_p=='RX' ||$kd_p=='DX'||$kd_p=='DN'||$kd_p=='YQ'||$kd_p=='EQ'||$kd_p=='YY'||$kode_p='YN'){
    //$b++;
    number_format($tot_x = $nilai_pajak_v[$i],0,",",".");
	if ($kd_p=='YQ' || $kd_p=='EQ'){
		$tot_pjk_wn2 = $tot_x;
		$oa_nilai += $tot_pjk_wn2;
	}else {
	$b++;
	$tot_pjk_wn1 = $tot_x*-1;
    $tot_pjk_wn2 = $tot_x;  //*0.10
	if ($kd_p!='YN') {
        $oa_nilai += $tot_pjk_wn1;
	}
    $oa_nilai += $tot_pjk_wn2;
	}
    //$tot_pjk_wn1 = $tot_x*-1;
    //$tot_pjk_wn2 = $tot_x;  //*0.10
    //$oa_nilai += $tot_pjk_wn1;
    //$oa_nilai += $tot_pjk_wn2;
    if($kd_p!='YN') {
        if($kd_p=='WX' || $kd_p=='RX' || $kd_p=='DX'){
            $kode_claim_rezak = 'SG00033';
		}elseif($kd_p=='YY'){
			if ($tglfakturpajak>=20250101) {
				$kode_claim_rezak = 'SG-YY-2024';
			} else {
				$kode_claim_rezak = 'SG-YY-2024';
			}
		}
		elseif($kd_p=='DN'){
			$kode_claim_rezak = 'SG00036';
        }
		else{
            $kode_claim_rezak = 'SG00027';
		}
    }
    $mialo = "SELECT * 
          FROM EX_KOMPONEN_BIAYA 
          WHERE KODE_KOMPONEN LIKE :kode_claim_rezak 
          AND TAX_CODE = :kd_p 
          AND ORG = :orginrr 
          AND DELETE_MARK = '0'";

    $query= oci_parse($conn, $mialo);
	$bind_kode_claim_rezak = "%$kode_claim_rezak%";
	oci_bind_by_name($query, ":kode_claim_rezak", $bind_kode_claim_rezak);
	oci_bind_by_name($query, ":kd_p", $kd_p);
	oci_bind_by_name($query, ":orginrr", $orginrr);

	oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PROFIT_CENTER'];
	
	if($kd_p=='YQ' || $kd_p=='EQ' || $kd_p=='YN'){
	}else 
	{
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn1,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
	}
    $b++;
     if($kd_p=='WX' || $kd_p=='RX' || $kd_p=='DX' || $kd_p=='YQ' || $kd_p=='EQ'){
        $kode_claim_rezak = 'SG00034';
    }elseif($kd_p=='YY'){
		if ($tglfakturpajak>=20250101) {
			$kode_claim_rezak = 'SG-YY-2024';
		} else {
			$kode_claim_rezak = 'SG-YY-2024';
		}
    }
	elseif($kd_p=='DN'){
		$kode_claim_rezak = 'SG00037';
    }
	elseif($kd_p=='YN'){
		$kode_claim_rezak = 'SG00032';
    }else{
        $kode_claim_rezak = 'SG00028';
    }
    $mialo = "SELECT * 
          FROM EX_KOMPONEN_BIAYA 
          WHERE KODE_KOMPONEN LIKE :kode_claim_rezak 
          AND TAX_CODE = :kd_p 
          AND ORG = :orginrr 
          AND DELETE_MARK = '0'";

    $query= oci_parse($conn, $mialo);
	$bind_kode_claim_rezak = "%$kode_claim_rezak%";
	oci_bind_by_name($query, ":kode_claim_rezak", $bind_kode_claim_rezak);
	oci_bind_by_name($query, ":kd_p", $kd_p);
	oci_bind_by_name($query, ":orginrr", $orginrr);
	oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PROFIT_CENTER'];
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn2,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
}

            }else {
                
		if ($kode_komponen_v[$i] == "SG0002" or $kode_komponen_v[$i] == "SG0003" or $kode_komponen_v[$i] == "SG0004" or $kode_komponen_v[$i] == "SG0005"){
			$cetak1 = "T";
			for($o=0; $o<$total; $o++){
                            if($sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********'){
                                $total_kompi=0;
				if ($kode_komponen_v[$i] == "SG0002" )$total_kompi = (-1)*$klaim_ktg_v[$o];
				if ($kode_komponen_v[$i] == "SG0003" )$total_kompi = (-1)*$klaim_semen_v[$o];
				if ($kode_komponen_v[$i] == "SG0004" )$total_kompi = (-1)*$klaim_rezak_v[$o];
				if ($kode_komponen_v[$i] == "SG0005" )$total_kompi = (-1)*$klaim_pdpks_v[$o];

				if ($total_kompi < 0){
                                    if ($cetak1 == "T" ){
                                        ?>
                                        <tr>
                                        <td align="center"><? echo $b; ?></td>
                                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                                        <td align="center"><? echo $profit_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                                        <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr>
                                        <?
                                        $oa_nilai+=$total_kompi; //Perhitungan TOTAL
                                        $cetak1 = "F";
                                    }else {

                                        ?>
                                        <tr>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr>
                                        <?
                                        $oa_nilai+=$total_kompi; //Perhitungan TOTAL
                                    }					
				}
                            }
			}
                }else{
                        ?>
                        <tr>
                        <td align="center"><? echo $b; ?></td>
                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                        <td align="center"><? echo $profit_v[$i]; ?></td>
                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                        <td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                        <td align="center"><? echo "IDR"; ?></td>
                        </tr>
                        <?	
                            $oa_nilai+=$total_komponen_v[$i]; //Perhitungan TOTAL
                }
	}
	} ?>
<? if(($pajak_pass == "T" && $p1mna==1) ||($pajak_pass == "TSS" && $p1mna==1)){ ?>
                <tr>
		<td align="center"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? echo " PPN (1%) "; ?></td>
		<td align="right"><? echo number_format($nilai_pajak_oa,0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<? }else if ($pajak_pass == "T") { ?>
		<tr>
		<td align="center"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? echo " PPN (10%) "; ?></td>
		<td align="right"><? echo number_format($nilai_pajak_oa,0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<? } else if ($pajak_pass == "TSS") { ?>
		<tr>
		<td align="center"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="center"><?  ?></td>
		<td align="left"><? echo " PPN (11%) "; ?></td>
		<td align="right"><? echo number_format($nilai_pajak_oa,0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<? } else if ($pajak_pass == "T12") { ?>
  <tr>
    <td align="center"><? ?></td>
    <td align="center"><?  ?></td>
    <td align="center"><?  ?></td>
    <td align="left"><? ?></td>
    <td align="center"><?  ?></td>
    <td align="center"><?  ?></td>
    <td align="center"><?  ?></td>
    <td align="left"><? echo " PPN (12%) "; ?></td>
    <td align="right"><? echo number_format($nilai_pajak_oa,0,",","."); ?></td>
    <td align="center"><? echo "IDR"; ?></td>
  </tr>
<? } $oa_nilai+=$nilai_pajak_oa; //Perhitungan TOTAL ?>
		<tr>
		<td align="center" colspan="8" style=" border-top:2px #000000 solid;">TOTAL</td>

		<td align="right" style=" border-top:2px #000000 solid;"><? echo number_format($oa_nilai,0,",","."); ?></td> <? //$total_invoice ?>
		<td align="center" style=" border-top:2px #000000 solid;"><? echo "IDR"; ?></td>
		</tr>
		<tr>
		<td colspan="10" height="20" style=" border-top:2px #000000 solid;"> </td>
		</tr>
		<tr>
		<td colspan="10"  style=" border-top:2px #000000 solid;"> Keterangan : <? echo $komentar_v;?> </td>
		</tr>
		<tr>
		<td colspan="10" height="40" style=" border-top:2px #000000 solid;"> </td>
		</tr>
		
		<td colspan="3"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
			<td height="70"></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		<td colspan="4"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
      
			<td height="70"><h3></h3></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		<td colspan="3"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
      
			<td height="70"><h3></h3></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		</tr>
</table>
<p>
<table width="900" align="center" >
<tr>
</tr>
<tr>
  <td width="900">
    <!--<input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  /> changed by dimas-->
    <!--<input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  /> changed by dimas -->
</td>
</tr>
</table>
</div>

<?
if($ada_klaim) exit; //jika tidak ada klaim, tidak perlu ditampilkan
?>

<div style="page-break-after:always">
<table width="100%" align="center" border="0">
<?
$total_rezakAll = array_sum($klaim_rezak_v);
$total_semenALL = array_sum($klaim_semen_v);
if($total_rezakAll>0 or $total_semenALL>0){?>    
<tr>
<td colspan="14" ><strong> <?php echo $nmkali?></strong></td>
</tr>
<tr>
<td colspan="14">
<div align="right"><? echo $last_update_date." " . $last_updated_by;  ?></div></td>
</tr>
<tr>
<td colspan="14" ><div align="center"><?php echo $nmkali?></div></td>
</tr>
<tr >
<td colspan="14" ><div align="center">RINCIAN KLAIM ONGKOS ANGKUT </div></td>
</tr>
<?}?>
<?
// unutk print ppl turunan 
$totke=count($sold_to_v); //$arr_soldto_cek);$sold_to_v
$total_rezak_cet = 0;
$total_semen_cet = 0;

$status_print_header = true;
for ($i=0;$i<$totke;$i++){
        
        $total_rezak_cet = $klaim_rezak_v[$i];
        $total_semen_cet = $klaim_semen_v[$i];
        $total_kantong_cet = $klaim_ktg_v[$i];
        $total_glondong_cet = $total_glondong_v[$i];

        if ($total_rezak_cet > 0 or $total_semen_cet > 0){
                        $dis_fb = $sold_to_v[$i];
						$sql_fb = "SELECT SOLD_TO, NAMA_SOLD_TO, VENDOR, NAMA_VENDOR, NO_DOC_HDR, NO_REK, BANK, BANK_CABANG 
								FROM EX_FB60 
								WHERE DELETE_MARK = '0' 
								AND NO_INVOICE = :no_invoice 
								AND SOLD_TO = :dis_fb";

                        $query_fb= oci_parse($conn, $sql_fb);
						oci_bind_by_name($query_fb, ":no_invoice", $no_invoice);
						oci_bind_by_name($query_fb, ":dis_fb", $dis_fb);
						oci_execute($query_fb);

                        $row_fb=oci_fetch_array($query_fb);
                        $no_acc_fb_v=$row_fb[NO_DOC_HDR];  
                        $sold_to_fb_v=$row_fb[SOLD_TO];  
                        $nama_sold_to_fb_v=$row_fb[NAMA_SOLD_TO];  
                        $pengelola_fb_v=$row_fb[VENDOR];  
                        $nama_pengelola_fb_v=$row_fb[NAMA_VENDOR];  
                        $no_rek_fb_v=$row_fb[NO_REK];  
                        $bank_fb_v=$row_fb[BANK];  
                        $cabang_fb_v=$row_fb[BANK_CABANG];  

        }

        //echo "<br>rezak ".$total_rezak_cet;
        //echo "<br>semen ".$total_semen_cet;
        //echo "<br>kantong ".$total_kantong_cet;
        //echo "<br>glondong ".$total_glondong_cet;

if ($total_rezak_cet > 0 or $total_semen_cet > 0 or $total_kantong_cet > 0 or $total_glondong_cet != 0){

        #echo $no_invoice." - ";
        $sold_nya = $sold_to_v[$i];
        $sql= "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice AND SOLD_TO = :sold_nya AND ( TOTAL_KLAIM_ALL > 0 )ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
        // STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
//        echo "<br>doss : ".$sql;
	$query= oci_parse($conn, $sql);
		oci_bind_by_name($query, ":no_invoice", $no_invoice);
		oci_bind_by_name($query, ":sold_nya", $sold_nya);
	oci_execute($query);

		$total_klaim_kgt_dtl=0;  
		$total_klaim_rezak_dtl=0;  
		$total_klaim_semen_dtl=0;  
		$total_klaim_pdpks_dtl=0;  

		$no_invoice_dtl= array();
		$no_invoice_ex_dtl= array();
		$spj_dtl= array();
		$tgl_kirim_dtl= array();
		$nama_produk_dtl= array();
		$shp_trn_dtl= array();
		$nama_sal_dis_dtl= array();
		$qty_kantong_rusak_dtl= array();
		$qty_semen_rusak_dtl= array();
		$no_pol_dtl= array();  
		$total_klaim_all_dtl= array();
		$qty_dtl =array();
		$satuan_shp_dtl = array();
                
                $tot_rezak=array();
                $tot_kantong=array();
                $tot_semen=array();
                $tot_pdpks=array();    
	while($row=oci_fetch_array($query)){
            
                #reset nilai
                $total_klaim_kgt_dtl=0;
                $total_klaim_pdpks_dtl=0;
                $total_klaim_semen_dtl=0;
                $total_klaim_rezak_dtl=0; 
            
                $no_invoice_dtl[]=$row[NO_INVOICE];
		$no_invoice_ex_dtl[]=$row[NO_INV_VENDOR];
		$spj_dtl[]=$row[NO_SHP_TRN];
		$tgl_kirim_dtl[]=$row[TANGGAL_KIRIM];
		$nama_produk_dtl[]=$row[NAMA_PRODUK];
		$shp_trn_dtl[]=$row[NO_SHP_TRN];
		$nama_sal_dis_dtl[]=$row[NAMA_SAL_DIS]; 
		$sold_to_dtl=$row[SOLD_TO];
                                
		$nama_sold_to_dtl=$row[NAMA_SOLD_TO];
		$ship_to_dtl[]=$row[SHIP_TO];
		$qty_dtl[]=$row[QTY_SHP];
		$satuan_shp_dtl[]=$row[SATUAN_SHP];
		$qty_kantong_rusak_dtl[]=$row[QTY_KTG_RUSAK];
		if ($row[QTY_LEBIH] > 0)
		$qty_semen_rusak_dtl[]=(-1)*$row[QTY_LEBIH];
		else
		$qty_semen_rusak_dtl[]=$row[QTY_SEMEN_RUSAK];

		$no_pol_dtl[]=$row[NO_POL];  


		if ($row[QTY_LEBIH] > 0)
			$total_klaim_all_dtl[]=(-1)*$row[KLAIM_ALL_LEBIH];  
		else
			$total_klaim_all_dtl[]=$row[TOTAL_KLAIM_ALL];  

		$total_klaim_kgt_dtl +=$row[TOTAL_KTG_RUSAK];  
                $tot_kantong[]=$row[TOTAL_KTG_RUSAK];
		
		$total_klaim_rezak_dtl +=$row[TOTAL_KTG_REZAK];  
                $tot_rezak[]=$row[TOTAL_KTG_REZAK]; 

		if($sold_to_dtl == '**********' or $sold_to_dtl == '**********' or $sold_to_dtl == '**********' or $sold_to_dtl == '**********'){
		$total_klaim_pdpks_dtl = $total_klaim_pdpks_dtl + $row[TOTAL_SEMEN_RUSAK];
		$total_klaim_pdpks_dtl = $total_klaim_pdpks_dtl - $row[KLAIM_LEBIH]; 		
		}else {
		$total_klaim_semen_dtl += $row[TOTAL_SEMEN_RUSAK]; 
		$total_klaim_semen_dtl = $total_klaim_semen_dtl - $row[KLAIM_LEBIH];  
                $tot_semen[]=$total_klaim_semen_dtl;
		}
		
		$total_klaim_pdpks_dtl +=$row[PDPKS];  
		$total_klaim_pdpks_dtl = $total_klaim_pdpks_dtl - $row[PDPKS_LEBIH];
                $tot_pdpks[]=$total_klaim_pdpks_dtl;
                
                #$totx_klaim[]=$row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]+$total_klaim_semen_dtl+$total_klaim_pdpks_dtl;
	
	}

#menghilangkan distributor dobel.        
if($sold_to_dtl_lama != $sold_to_dtl){
        ?>
        
<tr >
  <td colspan="14" style="border-bottom:2px #000000 solid;">
  <table width="930">		
  	<? if($status_print_header){ ?>
        <tr>
		<td> NO TAGIHAN </td>
		<td> : </td>
		<td> <? echo $no_invoice." / ". $no_invoice_ex_v;?> </td>
		<td>  </td>
		<td>  </td>
  	</tr>
        <? $status_print_header=false; } ?>
        <tr >
		<td width="116" >DISTRIBUTOR</td>
		<td width="8"> : </td>
		<td width="515"> <? echo $sold_to_dtl."  ".$nama_sold_to_dtl ;?> </td>
		<td width="59">  </td>
		<td width="62" colspan="5">  </td>
  	</tr>        
  </table>  </td>
</tr  >
<tr>
	<td style=" border-bottom:2px #000000 solid;" align="center"> NO</td>
	<td style=" border-bottom:2px #000000 solid;" align="center"> TGL SPJ</td>
	<td style=" border-bottom:2px #000000 solid;" width="18">AREA LT</td>
	<td style=" border-bottom:2px #000000 solid;" align="center">NO SPJ</td>
	<!-- <td style=" border-bottom:2px #000000 solid;">NO. POL</td>-->
	<td style=" border-bottom:2px #000000 solid;">PRODUK</td>
	<td style=" border-bottom:2px #000000 solid;" align="center">K.KTG</td>
	<td style=" border-bottom:2px #000000 solid;" align="center">K.SMN</td>
	<td style=" border-bottom:2px #000000 solid;" align="center">QTY</td>
        <td style=" border-bottom:2px #000000 solid;" align="center">REZAK</td>
        <td style=" border-bottom:2px #000000 solid;" align="center">KANTONG</td>
        <td style=" border-bottom:2px #000000 solid;" align="center">SEMEN</td>
        <td style=" border-bottom:2px #000000 solid;" align="center">PDPKS</td>
	<td style=" border-bottom:2px #000000 solid;" align="center">JUMLAH</td>
</tr>
<?
$b=0;
$tot_ktg =0;
$tot_smn =0;
$tot_qty =0;
$tot_jumlah =0;
$tot_konke=count($no_invoice_dtl);
        
$rezakx_tot=0;
$kantongx_tot=0;
$semenx_tot=0;
$pdpksx_tot=0;

	for($d=0; $d<$tot_konke;$d++) { 
		$tot_ktg +=$qty_kantong_rusak_dtl[$d];
		$tot_smn +=$qty_semen_rusak_dtl[$d];
		$tot_qty +=$qty_dtl[$d];
		$tot_jumlah +=$total_klaim_all_dtl[$d];

		$b=$d+1;
                
                $semenx = 0;
                
                $rezakx = $tot_rezak[$d];       $rezakx_tot+=$rezakx;
                $kantongx = $tot_kantong[$d];   $kantongx_tot+=$kantongx;
                $semenx = $tot_semen[$d];       $semenx_tot+=$semenx;
                $pdpksx = $tot_pdpks[$d];       $pdpksx_tot+=$pdpksx;
?>
		<tr>		
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $tgl_kirim_dtl[$d]; ?></td>
		<td align="left"><? echo $nama_sal_dis_dtl[$d]; ?></td>
		<td align="center"><? echo $spj_dtl[$d]; ?></td>
		<!--<td align="center"><? echo $no_pol_dtl[$d]; ?></td>-->
		<td align="left"><? echo $nama_produk_dtl[$d]; ?></td>
		<td align="center"><div align="right"><? echo number_format($qty_kantong_rusak_dtl[$d],0,",","."); ?></div></td>
		<? if ($satuan_shp_dtl[$d] == "ZAK"){?>
		<td align="center"><div align="right"><? echo number_format($qty_semen_rusak_dtl[$d],0,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($qty_dtl[$d],0,",","."); ?></div></td>
		<? }else{?>
		<td align="center"><div align="right"><? echo number_format($qty_semen_rusak_dtl[$d],2,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($qty_dtl[$d],2,",","."); ?></div></td>
		<? }?>
                <td align="center"><div align="right"><? echo number_format($rezakx,2,",","."); ?></div></td>
                <td align="center"><div align="right"><? echo number_format($kantongx,2,",","."); ?></div></td>
                <td align="center"><div align="right"><? echo number_format($semenx,2,",","."); ?></div></td>
                <td align="center"><div align="right"><? echo number_format($pdpksx,2,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($total_klaim_all_dtl[$d],2,",","."); ?></div></td>
		</tr>
	  <? } ?>
<tr>
        <td colspan="5"> <div align="right" style=" border-top:2px #000000 solid;">TOTAL :</div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right">
        <?=number_format($tot_ktg,0,",",".")?>
        </div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right">
        <? 
        if ($satuan_shp_dtl[0] == "ZAK"){
        echo number_format($tot_smn,0,",",".");
        }else{
        echo number_format($tot_smn,2,",",".");
        }
        ?>
        </div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right">

        <? 
        if ($satuan_shp_dtl[0] == "ZAK"){
        echo number_format($tot_qty,0,",",".");
        }else{
        echo number_format($tot_qty,2,",",".");
        }
        ?>
        </div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right"><?=number_format($rezakx_tot,2,",",".")?></div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right"><?=number_format($kantongx_tot,2,",",".")?></div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right"><?=number_format($semenx_tot,2,",",".")?></div></td>
        <td style=" border-top:2px #000000 solid;"> <div align="right"><?=number_format($pdpksx_tot,2,",",".")?></div></td>
        
        <td style=" border-top:2px #000000 solid;"> <div align="right"><?=number_format($tot_jumlah,2,",",".")?></div></td>
</tr>
<?
        $total_klaim_show += $tot_jumlah;
        $total_klaim_smn += $tot_smn;
        $total_klaim_ktg += $tot_ktg;
        $sold_to_dtl_lama = $sold_to_dtl;
        }
}

} //end $sold_to_dtl_lama != $sold_to_dtl
 if ($total_klaim_smn > 0 or $total_klaim_ktg > 0){
?>
<tr>
     <td colspan="13"><br></td>
</tr>
<tr>
    <td colspan="5" height="20" align="right">Grand Total :</td>
	<td height="20" align="right"><?=number_format($total_klaim_ktg)?></td>
	<td height="20" align="right">
        <? 
            if ($satuan_shp_dtl[0] == "ZAK"){
            echo number_format($total_klaim_smn,0,",",".");
            }else{
            echo number_format($total_klaim_smn,2,",",".");
            }
        ?>
    </td>
    <td colspan="6" height="20" align="right">Total Klaim : <?=number_format($total_klaim_show,2,",",".")?></td>
</tr>
<tr>
     <td colspan="14"><br></td>
</tr>
<tr>
    <td colspan='12'>        
        <table align="right" border="0">
        <tr>
        <td>
        </td>
        </tr>
        <tr>

      <?php 
      //if ($approvekasi == '0') {
        //$pesan = 'Belum disetujui';
      //}else if($approvekasi == '1'){
        //$pesan = 'Disetujui dengan <br> Tanda Tangan Elektronik';
      //}else{
        //$pesan = 'Rejected';
      //}
      ?>
        <td height="70"><h3><?php //$pesan; ?></h3></td>
        </tr>
        <tr>
        <td>
        <?//=$kepala_v?></td>
        </tr>
        </table>
    </td>
    <td colspan="2"></td>
</tr>
<tr><td colspan='12' align='left'></td></tr>
<tr>
    <td colspan='12' align='left'>
     <!--  <input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  /> changed by dimas -->
     <!--  <input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  /> <!-- changed by dimas -->
    </td>
</tr>
<?}?>
</table>    
</div>

<?
exit; #stop laporan #simplifikasi PPL by iljas

// unutk print ppl turunan 
$totke=count($sold_to_v);
$total_rezak_cet = 0;
$total_semen_cet = 0;
for ($i=0;$i<$totke;$i++){

//		$no_invoice_v=$row[NO_INVOICE];
//		$nama_vendor_v=$row[NAMA_VENDOR]; 
//		$vendor_v=$row[VENDOR]; 
//		$sold_to_v[]=$row[SOLD_TO];
//		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
//		$no_rek_dis_v[]=$row[NO_REK_DIS];
//		$nama_bank_dis_v[]=$row[NAMA_BANK_DIS];
//		$cabang_bank_dis_v[]=$row[BANK_CABANG_DIS];  
//		$klaim_ktg_v[]=$row[KLAIM_KTG];  
//		$klaim_rezak_v[]=$row[KLAIM_REZAK];  
//		$klaim_semen_v[]=$row[KLAIM_SEMEN];  
//		$klaim_pdpks_v[]=$row[KLAIM_PDPKS];  
//		$oa_semen_v+= $row[OA_SEMEN];  
$total_rezak_cet = $klaim_rezak_v[$i];
$total_semen_cet = $klaim_semen_v[$i];
if ($total_rezak_cet > 0 or $total_semen_cet > 0){
?>
<div style="page-break-after:always">

<table width="100%" align="center">
<tr>
  <td colspan="10" ><strong> <?php echo $nmkali?></strong></td>
</tr>
<tr>
<td colspan="10" ><div align="center" >PERMINTAAN PEMBAYARAN </div></td>
</tr>
<tr >
  <td colspan="10" ><div align="center">No. <?=$no_inv_sap_v?> / No Acc : <? echo $no_acc_doc_v; ?></div></td>
</tr>
<tr >
  <td colspan="10" style="border-bottom:2px #000000 solid;">
  <table width="100%">		
  	<tr >
		<td > Lokasi </td>
		<td >:</td>
		<td > <? echo $lokasi_v;?> </td>
		<td >  </td>
		<td > Unit Kerja Peminta </td>
		<td >:</td>
		<td > <? echo $unit_v;?>  </td>
		</tr>
	<tr >
	<td style=" border-bottom:2px #000000 solid;" colspan="7">&nbsp; </td>
	</tr>
  	<tr >
  	  <td>Jenis Permintaan </td>
  	  <td>:</td>
  	  <td>OAD</td>
  	  <td></td>
  	  <td>No Permintaan UM </td>
  	  <td>:</td>
  	  <td>&nbsp;</td>
  	  </tr>
  	<tr >
  	  <td>Langsung</td>
  	  <td>:</td>
  	  <td>X</td>
  	  <td></td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  </tr>
  	<tr >
  	  <td style=" border-bottom:2px #000000 solid;">Pertanggung Jawaban </td>
  	  <td style=" border-bottom:2px #000000 solid;">:</td>
  	  <td style=" border-bottom:2px #000000 solid;">&nbsp;</td>
  	  <td style=" border-bottom:2px #000000 solid;"></td>
  	  <td style=" border-bottom:2px #000000 solid;">Batch No </td>
  	  <td style=" border-bottom:2px #000000 solid;">:</td>
  	  <td style=" border-bottom:2px #000000 solid;">&nbsp;</td>
	  </tr>
  	<tr  >
  	  <td colspan="7" style=" border-bottom:2px #000000 solid;">DATA ANGGARAN </td>
  	  </tr>
  	<tr >
  	  <td colspan="7" style=" border-bottom:2px #000000 solid;">Koordinator Anggaran [KA] : 3026 </td>
  	  </tr>
  	<tr >
  	  <td>Kode Vendor </td>
  	  <td>:</td>
  	  <td><?=$sold_to_v[$i]?></td>
  	  <td></td>
  	  <td>Dibayarkan Kpd </td>
  	  <td>:</td>
  	  <td><?=$sold_to_v[$i]?></td>
	  </tr>
  	<tr >
  	  <td>Nama Vendor </td>
  	  <td>:</td>
  	  <td><?=$nama_sold_to_v[$i]?></td>
  	  <td></td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  <td><?=$nama_sold_to_v[$i]?></td>
	  </tr>
  	<tr >
  	  <td>No Kontrak </td>
  	  <td>:</td>
  	  <td>&nbsp;</td>
  	  <td></td>
  	  <td>No Rekening </td>
  	  <td>:</td>
  	  <td><?=$nama_bank_dis_v[$i]?> / <?=$no_rek_dis_v[$i]?></td>
	  </tr>
  	<tr >
  	  <td>No Invoice </td>
  	  <td>:</td>
  	  <td><? echo $no_invoice." / ". $no_invoice_ex_v;?></td>
  	  <td></td>
  	  <td>&nbsp;</td>
  	  <td>&nbsp;</td>
  	  <td><?=$cabang_bank_dis_v[$i]?></td>
	  </tr>
	  <tr >
    <td><? if($nitkuvendor!=''){echo "NITKU";}?></td>
    <td>:</td>
    <td><?=$nitkuvendor;?></td>
    <td>  </td>
    </tr>
  	<tr >
		<td>&nbsp;</td>
		<td> : </td>
		<td>&nbsp;</td>
		<td>  </td>
		<td>Alat Pembayaran </td>
		<td>:</td>
		<td>CHECK  </td>
		</tr>
  </table>  </td>
</tr  >
<tr>
  <td colspan="10" style=" border-bottom:2px #000000 solid;">&nbsp;</td>
  </tr>
<tr>
	<td style=" border-bottom:2px #000000 solid;">NO</td>
	<td style=" border-bottom:2px #000000 solid;"> Kode GL 2</td>
	<td style=" border-bottom:2px #000000 solid;">Kode  </td>
	<td style=" border-bottom:2px #000000 solid;">Nama Komponen</td>
	<td style=" border-bottom:2px #000000 solid;">Cost Center </td>
	<td style=" border-bottom:2px #000000 solid;">Profit Center </td>
	<td style=" border-bottom:2px #000000 solid;">Pajak </td>
	<td style=" border-bottom:2px #000000 solid;">Keterangan</td>
	<td style=" border-bottom:2px #000000 solid;"> Jumlah</td>
	<td style=" border-bottom:2px #000000 solid;">Mata Uang </td>
</tr>
<?if ($klaim_rezak_v[$i] > 1){//untuk klaim rezak
			$kode_claim_rezak = 'SG0004';
			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like :kode_claim_rezak AND ORG=:orginrr AND DELETE_MARK = '0' ";
			$query= oci_parse($conn, $mialo);
			oci_bind_by_name($query, ":kode_claim_rezak", $kode_claim_rezak);
			oci_bind_by_name($query, ":orginrr", $orginrr);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim_rezak=$row['NO_GL']; 
			$nama_claim_rezak=$row['NAMA_KOMPONEN']; 
			$keterangan_claim_rezak=$row['KETERANGAN']; 
			$pajak_claim=$row['TAX_CODE']; 
$total_rezak_cet = $klaim_rezak_v[$i];
$total_semen_cet = $klaim_semen_v[$i];

?>
		<tr>
		<td align="center"><? $b=1; echo $b; ?></td>
		<td align="center"><? echo $no_gl_claim_rezak; ?></td>
		<td align="center"><? echo $kode_claim_rezak; ?></td>
		<td align="left"><? echo $nama_claim_rezak; ?></td>
		<td align="center"><? echo ""; ?></td>
		<td align="center"><? echo $profit_v[0]; ?></td>
		<td align="center"><? echo $pajak_claim; ?></td>
		<td align="left"><? echo $keterangan_claim_rezak; ?></td>
		<td align="right"><? echo number_format($klaim_rezak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<? } 
   if ($klaim_semen_v[$i] > 1){//untuk klaim semen
			$kode_claim_semen = 'SG0003';
			$mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like :kode_claim_rezak AND ORG=:orginrr AND DELETE_MARK = '0'";
//			echo "<br>dos: ".$mialo;
            $query= oci_parse($conn, $mialo);
			oci_bind_by_name($query, ":kode_claim_rezak", $kode_claim_rezak);
			oci_bind_by_name($query, ":orginrr", $orginrr);
			oci_execute($query);
			$row=oci_fetch_array($query);
			$no_gl_claim_semen=$row['NO_GL']; 
			$nama_claim_semen=$row['NAMA_KOMPONEN']; 
			$keterangan_claim_semen=$row['KETERANGAN']; 
			$pajak_claim=$row['TAX_CODE']; 
?>		
		<tr>
		<td align="center"><? $b=1; echo $b; ?></td>
		<td align="center"><? echo $no_gl_claim_semen; ?></td>
		<td align="center"><? echo $kode_claim_semen; ?></td>
		<td align="left"><? echo $nama_claim_semen; ?></td>
		<td align="center"><? echo ""; ?></td>
		<td align="center"><? echo $profit_v[0]; ?></td>
		<td align="center"><? echo $pajak_claim; ?></td>
		<td align="left"><? echo $keterangan_claim_semen; ?></td>
		<td align="right"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>

<?		
	}
?> 

		<tr>
		<td align="center" colspan="8" style=" border-top:2px #000000 solid;">TOTAL</td>

		<td align="right" style=" border-top:2px #000000 solid;"><? echo number_format($klaim_semen_v[$i] + $klaim_rezak_v[$i],0,",","."); ?></td>
		<td align="center" style=" border-top:2px #000000 solid;"><? echo "IDR"; ?></td>
		</tr>
		
		<tr>
		<td colspan="10" height="50"> </td>
		</tr>
		<tr>
		<td colspan="4"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
			<td height="70"></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		<td colspan="3"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
			<td height="70"></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		<td colspan="3"> 
			<table align="center">
			<tr>
			<td>
			</td>
			</tr>
			<tr>
			<td height="70"></td>
			</tr>
			
			<tr>
			<td>
			</td>
			</tr>
			</table>		</td>
		</tr>
</table>
<p>
<table width="900" align="center" >
<tr>
  <td width="900">
    <!--  <input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  />  changed by dimas -->
    <!-- <input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  />  changed by dimas -->
</td>
</tr>
</table>
</div>

<?
$sold_nya = $sold_to_v[$i];
$sql= "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = :no_invoice AND SOLD_TO = :sold_nya AND TOTAL_KLAIM_ALL > 0 ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

		$query= oci_parse($conn, $sql);
		oci_bind_by_name($query, ":no_invoice", $no_invoice);
		oci_bind_by_name($query, ":sold_nya", $sold_nya);
		oci_execute($query);

		$total_klaim_kgt_dtl=0;  
		$total_klaim_rezak_dtl=0;  
		$total_klaim_semen_dtl=0;  
		$total_klaim_pdpks_dtl=0;  

		$no_invoice_dtl= array();
		$no_invoice_ex_dtl= array();
		$spj_dtl= array();
		$tgl_kirim_dtl= array();
		$nama_produk_dtl= array();
		$shp_trn_dtl= array();
		$nama_sal_dis_dtl= array();
		$qty_kantong_rusak_dtl= array();
		$qty_semen_rusak_dtl= array();
		$no_pol_dtl= array();  
		$total_klaim_all_dtl= array();

	while($row=oci_fetch_array($query)){
		$no_invoice_dtl[]=$row[NO_INVOICE];
		$no_invoice_ex_dtl[]=$row[NO_INV_VENDOR];
		$spj_dtl[]=$row[NO_SHP_TRN];
		$tgl_kirim_dtl[]=$row[TANGGAL_KIRIM];
		$nama_produk_dtl[]=$row[NAMA_PRODUK];
		$shp_trn_dtl[]=$row[NO_SHP_TRN];
		$nama_sal_dis_dtl[]=$row[NAMA_SAL_DIS]; 
		$sold_to_dtl=$row[SOLD_TO];
		$nama_sold_to_dtl=$row[NAMA_SOLD_TO];
		$ship_to_dtl[]=$row[SHIP_TO];
		$qty_dtl[]=$row[QTY_SHP];
		$qty_kantong_rusak_dtl[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_dtl[]=$row[QTY_SEMEN_RUSAK];
		$no_pol_dtl[]=$row[NO_POL];  
		$total_klaim_all_dtl[]=$row[TOTAL_KLAIM_ALL];  

		$total_klaim_kgt_dtl+=$row[TOTAL_KTG_RUSAK];  
		$total_klaim_rezak_dtl+=$row[TOTAL_KTG_REZAK];  
		$total_klaim_semen_dtl+=$row[TOTAL_SEMEN_RUSAK];  
		$total_klaim_pdpks_dtl+=$row[PDPKS];  
	
	}
?>
<div style="page-break-after:always">

<table width="900" align="center" >
<tr>
  <td colspan="10" ><strong> <?php echo $nmkali?></strong></td>
</tr>
<tr>
<td colspan="10" ><div align="center"><?php echo $nmkali?></div></td>
</tr>
<tr >
  <td colspan="10" ><div align="center">RINCIAN KLAIM ONGKOS ANGKUT</div></td>
</tr>
<tr >
  <td colspan="10" style="border-bottom:2px #000000 solid;">
  <table width="890">		
  	<tr >
		<td width="116" >DISTRIBUTOR</td>
		<td width="8"> : </td>
		<td width="515"> <? echo $sold_to_dtl."  ".$nama_sold_to_dtl ;?> </td>
		<td width="59">  </td>
		<td width="62">  </td>
  	</tr>

  	<tr>
		<td> NO TAGIHAN </td>
		<td> : </td>
		<td> <? echo $no_invoice." / ". $no_invoice_ex_v;?> </td>
		<td>  </td>
		<td>  </td>
  	</tr>
  </table>  </td>
</tr  >
<tr>
	<td style=" border-bottom:2px #000000 solid;"> NO</td>
	<td style=" border-bottom:2px #000000 solid;"> TGL SPJ</td>
	<td style=" border-bottom:2px #000000 solid;">AREA LT</td>
	<td style=" border-bottom:2px #000000 solid;">NO SPJ</td>
	<td style=" border-bottom:2px #000000 solid;">NO. POL</td>
	<td style=" border-bottom:2px #000000 solid;">PRODUK</td>
	<td style=" border-bottom:2px #000000 solid;">K.KTG</td>
	<td style=" border-bottom:2px #000000 solid;"> K.SMN</td>
	<td style=" border-bottom:2px #000000 solid;"> KWANTUM</td>
	<td style=" border-bottom:2px #000000 solid;">JUMLAH</td>
</tr>
<?
$b=0;
$tot_ktg =0;
$tot_smn =0;
$tot_qty =0;
$tot_jumlah =0;
$tot_konke=count($no_invoice_dtl);

	for($d=0; $d<$tot_konke;$d++) { 
		$tot_ktg +=$qty_kantong_rusak_dtl[$d];
		$tot_smn +=$qty_semen_rusak_dtl[$d];
		$tot_qty +=$qty_dtl[$d];
		$tot_jumlah +=$total_klaim_all_dtl[$d];

		$b=$d+1;
?>
		<tr>		
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $tgl_kirim_dtl[$d]; ?></td>
		<td align="left"><? echo $nama_sal_dis_dtl[$d]; ?></td>
		<td align="center"><? echo $spj_dtl[$d]; ?></td>
		<td align="center"><? echo $no_pol_dtl[$d]; ?></td>
		<td align="left"><? echo $nama_produk_dtl[$d]; ?></td>
		<td align="center"><div align="right"><? echo number_format($qty_kantong_rusak_dtl[$d],0,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($qty_semen_rusak_dtl[$d],3,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($qty_dtl[$d],3,",","."); ?></div></td>
		<td align="center"><div align="right"><? echo number_format($total_klaim_all_dtl[$d],2,",","."); ?></div></td>
		</tr>
	  <? } ?>
<tr>
	<td colspan="6"> <div align="right" style=" border-top:2px #000000 solid;">TOTAL :</div></td>
	<td style=" border-top:2px #000000 solid;"> <div align="right">
	  <?=number_format($tot_ktg,0,",",".")?>
    </div></td>
	<td style=" border-top:2px #000000 solid;"> <div align="right">
	  <?=number_format($tot_smn,3,",",".")?>
    </div></td>
	<td style=" border-top:2px #000000 solid;"> <div align="right">
	  <?=number_format($tot_qty,3,",",".")?>
    </div></td>
	<td style=" border-top:2px #000000 solid;"> <div align="right">
	  <?=number_format($tot_jumlah,2,",",".")?>
    </div></td>
</tr>
<tr><td colspan="10" height="50"> </td></tr>
<tr><td colspan="4"> 
<table align="left">
<tr>
<td> Rezak</td>
<td> :</td>
<td > <?=number_format($total_klaim_rezak_dtl,2,",",".")?></td>
</tr>
<tr>
<td> Kantong</td>
<td> :</td>
<td > <?=number_format($total_klaim_kgt_dtl,2,",",".")?></td>
</tr>
<tr>
<td> Semen</td>
<td> :</td>
<td > <?=number_format($total_klaim_semen_dtl,2,",",".")?></td>
</tr>
<tr>
<td> PDPKS</td>
<td> :</td>
<td > <?=number_format($total_klaim_pdpks_dtl,2,",",".")?></td>
</tr>
<tr>
<td> Total</td>
<td> :</td>
<td > <?=number_format($total_klaim_pdpks_dtl+$total_klaim_rezak_dtl+$total_klaim_kgt_dtl+$total_klaim_semen_dtl,2,",",".")?></td>
</tr>
</table>

</td>
<td colspan="6">
<table align="right">
<tr>
<td>
Kasi Administrasi Distribusi & Transportasi</td>
</tr>
<tr>
<td height="70"></td>
</tr>

<tr>
<td>
<?=$kepala_v?></td>
</tr>
</table></td>
</tr>
</table>
<p>
<table width="900" align="center" >
<tr>
  <td width="900">
     <!-- <input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  /> changed by dimas -->
     <!-- <input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  /> changed by dimas -->
</td>
</tr>
</table>
</div>

<?
	}
}

?>
<br/>
</body>
</html>
