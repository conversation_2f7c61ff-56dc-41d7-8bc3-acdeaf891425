<?
session_start();
include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

// $importtargetVolume='upload_customer_replenishment_report.php?act=update';
$cancelUrl='mapping_leadtime_so_act.php?';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$tgl_fr = date("m/d/Y");
$tgl_to = date("m/d/Y");

$dirr = $_SERVER['PHP_SELF'];
//$halaman_id = $fungsi->get_halam_id($dirr);
$halaman_id = $fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
    ?>
                    <!-- <SCRIPT LANGUAGE="JavaScript">
                        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
                    </SCRIPT>
    
         <a href="../index.php">Login....</a> -->
    <?
    
//    exit();
    }

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Mapping Lead time</title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>

    
    <div id="dlg" style="width:auto;height:auto;padding:2% 20%"
         closed="true" buttons="#dlg-buttons">
        <div class="ftitle">PUSH SO</div>
        <form id="fm" method="post" novalidate>
        <div class="fitem">
        <div class="fitem">
        <label>ORG :</label>
        <br>
                <select required="true" id="org" class="easyui-combobox" name="org" style="width:200px;">
                    <?php
                    $sql = oci_parse($conn, "SELECT DISTINCT XPARAM FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                    $result=oci_execute($sql);
                    while ($row = oci_fetch_array($sql)){
                        echo "<option value=".$row['XPARAM'].">" .$row['XPARAM']."</option>";
                    }
                  ?>
                </select>  
                </br>
                <br>
                </br>
            </div>
                <label>Plant :</label>
                <br>
                <select id="plant" class="easyui-combobox" name="plant" style="width:200px;">
                    <?php
                    $sql = oci_parse($conn, "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                    $result=oci_execute($sql);
                    while ($row = oci_fetch_array($sql)){
                        echo "<option value=".$row['WERKS'].">" .$row['WERKS']." (". $row['NAME1'] . ")</option>";
                    }
                  ?>
                </select>  
                </br>
                <br>
                </br>
            </div>
            <div class="fitem">
                <label>Delivery Date :</label>
                <br>
                <label><i><u>(mm/dd/yyyy)</u></i></label>
                </br>
                <br>
                <input name="tgl1" id="tgl1" class="easyui-datebox" value="<?=$tgl_fr?>" label="Start Date:" labelPosition="top" style="width:195px;"> &nbsp; to &nbsp;
                <input name="tgl2" id="tgl2" class="easyui-datebox" value="<?=$tgl_to?>" label="End Date:" labelPosition="top" style="width:195px;">
                <!-- <input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl_fr?>" onClick="return showCalendar('tgl1');"/>&nbsp; to &nbsp;
	            <input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl_to?>" onClick="return showCalendar('tgl2');"/>&nbsp;	 -->
                </br>
                <br>
                </br>
                <!-- : <input name="periodefrom" id="periodefrom" class="easyui-datetimespinner" required="true" data-options="label:'yyyy-mm',labelPosition:'top',formatter:formatter2,parser:parser2,selections:[[0,4],[5,7]]" style="width:195px;"> -->
            </div>
            <!-- <div class="fitem">
                <label>Date To </label>
                <input id="dd" type="text" class="easyui-datebox" required="required"> -->
                <!-- : <input name="periodeto" id="periodeto" class="easyui-datetimespinner" required="true" data-options="label:'yyyy-mm',labelPosition:'top',formatter:formatter2,parser:parser2,selections:[[0,4],[5,7]]" style="width:195px;"> -->
            <!-- </div> -->
            <div class="fitem">
            <label>SO Number :</label>
            <br>
                <textarea name="input_SO" id="input_SO"  rows="3" style="width:97%; height:100%;" cols="10" placeholder="*format penulisan SO 10 digit, jika SO lebih dari satu gunakan pemisah koma (,) dan tanpa spasi"></textarea>
                
                </br>
                <br>
                </br>
            </div>
        </form>
    </div>
    <div align="center">   
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Push</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-print" onclick="displayAct(this)" style="width:150px" id="print_return">Display Return</a>
    <br>    <br>    <br>
    <span id="loadingd" name="loadingd" style="display: none"><img src="../images/loading.gif" alt="loding" />Loading...!!</span>
    </br>
    </div>
</div>

<div id="dlg2" style="width:auto;height:auto;padding:30px 300px">
<body>
    <!-- <textarea name="myText" id="myText"  rows="3" style="width:700px; height:170px;" cols="10"></textarea> -->
    <span id="myText"></span>
</body>
</div>


<script type="text/javascript">
    function lodingact(obj){    
    //alert(obj);
    if(obj==1){
        document.getElementById("loadingd").style.display = "none";
    }else{
        document.getElementById("loadingd").style.display = "block";
    }
}
function saveAct(){

	var org = $('#org').datebox('getValue');
    var plant = $('#plant').datebox('getValue');
    var date_from = $('#tgl1').datebox('getValue');
    var date_to = $('#tgl2').datebox('getValue');
    var so = document.getElementById('input_SO').value;

    dateto = date_to;
    datefrom = date_from;


    var strURL="push_so_act.php?xvkorg="+org+"&xwerks="+plant+"&audat_from="+datefrom+"&audat_to="+dateto+"&vbeln="+so+"&action=push_data_so";
    alert("push_so_act.php?xvkorg="+org+"&xwerks="+plant+"&audat_from="+datefrom+"&audat_to="+dateto+"&vbeln="+so+"&action=push_data_so");
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            if (req.status == 200) { 
                                    var pushdata=req.responseText;
                                    // console.log(pushdata);
                                    const pushdatareturn = pushdata.split("/");
                                    var pushdatamsg = pushdatareturn[0];
                                    var pushdataoutput = pushdatareturn[1];
                                    lodingact(1); 
                                    // alert(pushdatamsg);
                                    alert(pushdata);
                                    // var jsonStr = JSON.stringify(pushdata);
                                    document.getElementById("myText").innerHTML = pushdataoutput;
                                    let element = document.getElementById("myText");
                                    let hidden = element.getAttribute("hidden");
                                    element.setAttribute("hidden", "hidden");
                                        // button.innerText = "Show Span";
                                    
                            } else {
                                lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
    }
    //////////////////
    // // var form = new FormData();
    // // form.append("XVKORG", "7000");
    // // form.append("XWERKS", "7403");
    // // form.append("audat_from", "20222-10-18");
    // // form.append("audat_to", "2022-10-18");
    // // form.append("VBELN", "0011010929");

    // var settings = {
    // "url": "http://**********/bi/skedul/silog/send_socc.php?XVKORG="+org+"&XWERKS="+plant+"&audat_from="+datefrom+"&audat_to="+dateto+"&VBELN="+so,
    // "method": "POST",
    // "timeout": 0,
    // "processData": false,
    // "mimeType": "multipart/form-data",
    // "contentType": false,
    // // "data": form
    // };
    // $.ajax(settings).done(function (response) {
    // console.log(response);
    // alert(response);
    // });
    ///////////////////////
}
function displayAct(){
    // pushdata='ini display return';
    // document.getElementById("myText").innerHTML='tesssssss';    
    // let toggle = button => {
     let element = document.getElementById("myText");
     let hidden = element.getAttribute("hidden");
    
     if (hidden) {
        element.removeAttribute("hidden");
        // button.innerText = "Hide Span";
     } else {
        element.setAttribute("hidden", "hidden");
        // button.innerText = "Show Span";
     }
//   }

}
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
#fm2{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
