<?php

/**
 * Centralized CURL SSL Configuration
 * This file provides standardized SSL settings for all API calls
 * to prevent SSL/TLS compatibility issues.
 * 
 * Usage:
 * include_once('curl_ssl_config.php');
 * applySslConfig($ch); // where $ch is your curl handle
 */

/**
 * Apply standardized SSL configuration to a cURL handle
 * 
 * @param resource $ch cURL handle
 * @return void
 */
function applySslConfig($ch) {
    // Force TLS 1.2 for security and compatibility
    curl_setopt($ch, CURLOPT_SSLVERSION, 6); // CURL_SSLVERSION_TLSv1_2
    
    // Disable SSL verification for development (should be enabled in production)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    
    // Set timeouts to prevent hanging
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    // Enable following redirects
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
}

/**
 * Get production-ready SSL configuration
 * Use this in production environment
 * 
 * @param resource $ch cURL handle
 * @return void
 */
function applyProductionSslConfig($ch) {
    // Force TLS 1.2 for security and compatibility
    curl_setopt($ch, CURLOPT_SSLVERSION, 6); // CURL_SSLVERSION_TLSv1_2
    
    // Enable SSL verification for production security
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    
    // Set reasonable timeouts
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    
    // Enable following redirects
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
}

/**
 * Log SSL/TLS information for debugging
 * 
 * @param resource $ch cURL handle
 * @return array SSL information
 */
function getSslInfo($ch) {
    $info = curl_getinfo($ch);
    $curl_version = curl_version();
    return array(
        'ssl_verify_result' => $info['ssl_verify_result'],
        'certinfo' => isset($info['certinfo']) ? $info['certinfo'] : array(),
        'ssl_engines' => isset($curl_version['ssl_version']) ? $curl_version['ssl_version'] : 'Unknown'
    );
}

?> 