<?php

session_start();
include ('../include/or_fungsi.php');
require_once('../MainPHPExcel/MainPHPExcel.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$dist=sprintf("%010s",$_SESSION['distr_id']);

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
$ID_REQ    = htmlspecialchars($_REQUEST['id']);

function importDataExcel($conn, $data_trgt){
    $insert=0;
    $update=0;
    $statusimport=0;
    $error_get_deskripsi=0;
    $data_expired = 0;
    $h = 0;
    $user_name=$_SESSION['user_name'];

    foreach ($data_trgt as $key => $value) { 
        $kd_soldto = '';
        $nama_soldto = '';
        $kd_soldto = trim($value[2]);
        $nama_soldto = trim($value[3]);
        unset($val);
        
        // Jika hasil dari RFC adalah false (tidak ditemukan), skip data atau continue
        $get_deskripsi = getNameFromRFC($kd_soldto);
        if (is_null($get_deskripsi)) {
            $error_get_deskripsi++;
            continue;
        }

        $val['KD_SOLDTO'] = str_pad($kd_soldto, 10, '0', STR_PAD_LEFT);
        $val['NAMA_SOLDTO'] = $nama_soldto;
        $val['CREATED_BY'] = $user_name;
        $val['CREATED_AT'] = date('Y-m-d H:i:s');
        $ins=false; $upd=false;
             
        $data_cek = CekSelectData($conn,$val);
        if ($data_cek['JUMLAH'] > 0){
            $upd= UpdateData($conn,$val);
            $update++;
            $statusimport=1;
        } else {
            $ins= InsertData($conn,$val);
            $insert++;
            $statusimport=1;
        }
    }
    $msg="";
    if ($insert>0) {
        $msg .= "- ".$insert." data baru berhasil di-insert <br> ";
    }
    if ($update>0) {
        $msg .= "- ".$update." data berhasil di-update <br>  ";
    }
    if ($error_get_deskripsi>0) {
        $msg .= "- ".$error_get_deskripsi." data tidak ditemukan di SAP <br>  ";
    }
    if ($data_expired > 0) {
        $msg .= "- ".$data_expired." data gagal di-process <br>  ";
    }
    $result['status'] = $statusimport;
    $result['message'] = $msg;
    return $result;
}

function CekSelectData($conn,$data){
    $sql_select =  "SELECT
                        COUNT(KD_SOLDTO) AS JUMLAH
                    FROM
                       MAPPING_SOLDTO_FIOS
                    WHERE
                        KD_SOLDTO = '".$data['KD_SOLDTO']."' AND DEL = '0' ";

    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function CekSelectDataUpd($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_SOLDTO_FIOS
                    WHERE
                        KD_SOLDTO = '".$data['KD_SOLDTO']."' AND NAMA_SOLDTO = '".$data['NAMA_SOLDTO']."' AND DEL = '0' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];
    return $arData;
}

function CekSelectDataIns($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_SOLDTO_FIOS
                    WHERE
                        KD_SOLDTO= '".$data['KD_SOLDTO']."' AND DEL = '0' ";
   $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];
    return $arData;
}

function InsertData($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    unset($val); 

    $kd_soldto = str_pad($data_insert['KD_SOLDTO'], 10, '0', STR_PAD_LEFT);
    $val['KD_SOLDTO'] = $kd_soldto;
    $nama_from_rfc = getNameFromRFC($kd_soldto); // get Deskripsi soldto dari RFC_Z_ZCSD_DIST
    $val['NAMA_SOLDTO'] = (!empty($nama_from_rfc)) ? str_replace("'", "''", $nama_from_rfc) : '';

    $data_cek2 = CekSelectDataIns($conn,$val);
    if ($data_cek2['JUMLAH'] > 0) {
        $show_ket = "Data Already up to date<br>";
        $ins = array('errorMsg'=>$show_ket);
        return false;
    }else{
        $sql2 = "INSERT INTO MAPPING_SOLDTO_FIOS (KD_SOLDTO, NAMA_SOLDTO, CREATED_AT, CREATED_BY,DEL)
                        VALUES ( '".$val['KD_SOLDTO']."', '".$val['NAMA_SOLDTO']."', SYSDATE, '".$nama_user."','0')";
        $query2 = oci_parse($conn, $sql2);
        $ins = oci_execute($query2);
        return $ins;
    }
}

function UpdateData($conn, $data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d H:i:s');    
    $kd_soldto = str_pad($data_update['KD_SOLDTO'], 10, '0', STR_PAD_LEFT);
    $nama_from_rfc = getNameFromRFC($kd_soldto); // get Deskripsi soldto dari RFC_Z_ZCSD_DIST
    $nama_soldto = (!empty($nama_from_rfc)) ? $nama_from_rfc : '';

    $sql1 = "UPDATE MAPPING_SOLDTO_FIOS
             SET KD_SOLDTO = '$kd_soldto',
                 NAMA_SOLDTO = '$nama_soldto',
                 UPDATED_AT = SYSDATE,
                 UPDATED_BY = '$nama_user'
             WHERE
                 KD_SOLDTO = '$kd_soldto'"; 

    $query1 = oci_parse($conn, $sql1);
    $upd = oci_execute($query1);
      
    return $upd;
}

function getNameFromRFC($kd_soldto) {
    // $kd_soldto = str_pad($kd_soldto, 10, '0', STR_PAD_LEFT);
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK) {
        $sap->Open();
    } else {
        echo $sap->PrintStatus();
        return null;
    }
    $fce = $sap->NewFunction("Z_ZCSD_DIST");
    if ($fce == false) {
        echo $sap->PrintStatus();
        return null;
    }
    $fce->KUNNR = $kd_soldto;
    // $fce->ZKTOKD = 'ZSG1';

    $fce->T_KTOKD->row["SIGN"] = 'I';
    $fce->T_KTOKD->row["OPTION"] = 'EQ';
    $fce->T_KTOKD->row["LOW"] = 'ZSG1';
    $fce->T_KTOKD->row["HIGH"] = '';
    $fce->T_KTOKD->Append($fce->T_KTOKD->row);
    
    $fce->T_KTOKD->row["SIGN"] = 'I';
    $fce->T_KTOKD->row["OPTION"] = 'EQ';
    $fce->T_KTOKD->row["LOW"] = 'ZSP1';
    $fce->T_KTOKD->row["HIGH"] = '';
    $fce->T_KTOKD->Append($fce->T_KTOKD->row);
    
    $fce->T_KTOKD->row["SIGN"] = 'I';
    $fce->T_KTOKD->row["OPTION"] = 'EQ';
    $fce->T_KTOKD->row["LOW"] = 'ZST1';
    $fce->T_KTOKD->row["HIGH"] = '';
    $fce->T_KTOKD->Append($fce->T_KTOKD->row);
    
    $fce->T_KTOKD->row["SIGN"] = 'I';
    $fce->T_KTOKD->row["OPTION"] = 'EQ';
    $fce->T_KTOKD->row["LOW"] = 'ZTRA';
    $fce->T_KTOKD->row["HIGH"] = '';
    $fce->T_KTOKD->Append($fce->T_KTOKD->row);

    $fce->Call();
    $result_name = null;
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->RETURN_DATA->Reset();
        while ($fce->RETURN_DATA->Next()) {
            if ($fce->RETURN_DATA->row["KUNNR"] == $kd_soldto) {
                $result_name = $fce->RETURN_DATA->row["NAME1"];
                break;
            }
        }
    } else {
        echo $fce->PrintStatus();
    }
    $fce->Close();
    $sap->Close();

    return $result_name;
}

if(isset($aksi)){
switch($aksi) { 
  case 'show' :
    {
        $sql = "SELECT
            ma.ID,
            ma.KD_SOLDTO,
            ma.NAMA_SOLDTO,
            ma.CREATED_AT,
            ma.CREATED_BY,
            ma.UPDATED_AT,
            ma.UPDATED_BY
        FROM
            MAPPING_SOLDTO_FIOS ma
        WHERE
            DEL != '1' $wheres
        GROUP BY
            ma.ID,
            ma.KD_SOLDTO,
            ma.NAMA_SOLDTO,
            ma.CREATED_AT,
            ma.CREATED_BY,
            ma.UPDATED_AT,
            ma.UPDATED_BY
        ORDER BY
            ma.CREATED_AT DESC
        ";
        
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $i=0;
        while($row=oci_fetch_array($query)){
            array_push($result, $row);
        }
        echo json_encode($result);
     }
     break;
     case 'add' :
     {
        if ($user_org != '') {
            $kd_soldto = htmlspecialchars($_REQUEST['kd_soldto']);
            $kd_soldto = str_pad($kd_soldto, 10, '0', STR_PAD_LEFT);
            $nama_soldto = getNameFromRFC($kd_soldto);
            // Jika hasil dari RFC adalah false (tidak ditemukan), maka tidak lanjut
            if (is_null($nama_soldto)) {
                $msg = "Kode SoldTo $kd_soldto tidak ditemukan di SAP";
                echo json_encode(array('errorMsg'=>$msg));
                exit;
            }

            unset($val); 
            $val['KD_SOLDTO'] = $kd_soldto;
            $val['NAMA_SOLDTO'] = (!empty($nama_soldto)) ? $nama_soldto : '';
            $val['CREATED_BY'] = $user_name;
            $val['CREATED_AT'] = date('Y-m-d H:i:s');
           
            $ins=false; $upd=false;  
                $data_cek = CekSelectData($conn,$val);
                if ($data_cek['JUMLAH'] > 0){
                    $upd= UpdateData($conn,$val);
                    $update= true;
                } else {
                    $ins= InsertData($conn,$val);
                    $insert = true;
                }

                if($insert){
                    echo json_encode(array('success'=>true));
                }elseif($update) {
                    echo json_encode(array('success'=>true));
                }else{
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
        }else{
            echo json_encode(array('errorMsg'=>'Org tidak boleh kosong!!!'));
        }
     }
     break;
     case 'del' :
     {
        error_reporting(0);
        $value = ($_POST['data']);
        $date = date('Y-m-d H:i:s');
        $list = array();
        $gagal = 0;
        $sukses= 0;
        $i=0; 
        while($i < count($value)){
            $idDlt = $value[$i]['ID'];       
            $sql = "UPDATE MAPPING_SOLDTO_FIOS SET DEL='1' WHERE ID = '$idDlt' ";
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);

            if($result){ 
                $sukses=$sukses+1; 
            }else{ 
                $gagal=$gagal+1; 
            }

            array_push($list, $ID);
          $i++;
         }  

        if ($result){
            $keterangan = array('success'=>"Data Berhasil Di Delete = ".$sukses.", gagal = ".$gagal." ! ");
        } else {
            $keterangan = array('errorMsg'=>"Data Gagal Di Delete = gagal = ".$gagal." ! ");
        }
        echo json_encode($keterangan);

     }
     break;
     case 'updateApp' :
        {
            $kd_soldto = htmlspecialchars($_REQUEST['kd_soldto']);
            $nama_soldto = getNameFromRFC($kd_soldto); // get Deskripsi soldto dari RFC_Z_ZCSD_DIST
            // Jika hasil dari RFC adalah false (tidak ditemukan), maka tidak lanjut
            if (is_null($nama_soldto)) {
                $msg = "Kode SoldTo $kd_soldto tidak ditemukan di SAP";
                echo json_encode(array('errorMsg'=>$msg));
                exit;
            }

            $ID   = htmlspecialchars($_REQUEST['ID']);
            unset($val); 
            $kd_soldto = str_pad($kd_soldto, 10, '0', STR_PAD_LEFT);
            $val['NAMA_SOLDTO'] = (!empty($nama_soldto)) ? $nama_soldto : '';
            $val['ID'] = $ID;
                
            $data_cek2 = CekSelectDataUpd($conn,$val);
            if ($data_cek2['JUMLAH'] > 0) {
                $show_ket = "Data Already up to date<br>";
                $keterangan = array('errorMsg'=>$show_ket);
                echo json_encode($keterangan);
            }else{
                $user = $_SESSION['user_name'];            
                $sql = "UPDATE MAPPING_SOLDTO_FIOS SET KD_SOLDTO = '$kd_soldto', NAMA_SOLDTO = '$nama_soldto', UPDATED_AT = SYSDATE , UPDATED_BY = '$user' WHERE ID = '$ID'";
                // var_dump($sql);exit;
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
            if ($result){
                $show_ket = "Data berhasil di update <br>";
                $keterangan = array('success'=>$show_ket);
            } else {
                $show_ket = "Data Gagal di update <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
            echo json_encode($keterangan);
            }          
        }
        break;
     case 'upload_file' :
     {
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        $allowedExts = "xls";
        $extension = end(explode(".", $_FILES["file_upload"]["name"]));
        if ($extension==$allowedExts) {
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
            $jumlah_row = $cell->rowcount($sheet_index=0);
            $jumlah_col = $cell->colcount($sheet_index=0);
            $kode_file  = $cell->val( 1,2 );
            for ($i = 5; $i <= $jumlah_row; $i++) {
                for ($j = 1; $j <= 3; $j++) {                    
                    $ke = $i-5;
                    $data[$ke][$j]= $cell->val( $i,$j );
                }
            }
            $messData = importDataExcel($conn, $data);

            if (isset($messData['status'])){
                if ($messData['status'] == 1 ) {
                    $show_ket = "Data Berhasil di Simpan <br>".$messData['message'];
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di Simpan <br>".$messData['message'];
                    $keterangan = array('errorMsg'=>$show_ket);
                }
            } else {
                $show_ket = "Data Gagal di Simpan <br>".$messData['message'];
                $keterangan = array('errorMsg'=>$show_ket);
            }
        } else {
            $show_ket = "Invalid file...!! <br>";
            $keterangan = array('errorMsg'=>$show_ket);
        }
        echo json_encode($keterangan);
     }
     break;
        case 'exportMappingSoldtoFios':
            $sql = "SELECT * FROM MAPPING_SOLDTO_FIOS WHERE DEL = 0 ORDER BY ID ASC";
            $parse = oci_parse($conn, $sql);
            oci_execute($parse);
            $datain = array();
            while ($row = oci_fetch_array($parse, OCI_ASSOC + OCI_RETURN_NULLS)) {
                $datain[] = $row;
            }

            // $datain = $_POST['data'];
            $total = count($datain);
            $namafile = "mapping_soldto_fios.xls";
            send($namafile);
            $WritePHPExcel = new PHPExcel();
            $WritePHPExcel->setActiveSheetIndex(0);
            $colomSt = 'A';
            $WritePHPExcel->getActiveSheet()->setTitle('Mapping Soldto FIOS'); //title sheet
            $Worksheet1 = $WritePHPExcel->getActiveSheet();

            //head excel
            $Worksheet1->setCellValueByColumnAndRow(0, 1, 'No.');
            $Worksheet1->setCellValueByColumnAndRow(1, 1, 'KODE SOLDTO');
            $Worksheet1->setCellValueByColumnAndRow(2, 1, 'DESKRIPSI SOLDTO');
            $Worksheet1->setCellValueByColumnAndRow(3, 1, 'CREATED AT');
            $Worksheet1->setCellValueByColumnAndRow(4, 1, 'CREATED BY');
            $Worksheet1->setCellValueByColumnAndRow(5, 1, 'UPDATED AT');
            $Worksheet1->setCellValueByColumnAndRow(6, 1, 'UPDATED BY');

            $colomAk = $Worksheet1->getHighestColumn();
            if ($colomSt != '' && $colomAk != '') {
                $WritePHPExcel->getActiveSheet()->getStyle($colomSt . "1:" . $colomAk . "1")->getFont()->setBold(true); //bold header
            }
            $Worksheet1->getStyle($colomSt . '1:' . $colomAk . '1')->applyFromArray($styleHead); //style head         
            // Version 4 fixed
            for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true); //auto size
            }
            $i = 0;
            $j = 2;
            foreach ($datain as $key => $valuef) {
                //setROW
                if ($colomSt != '' && $colomAk != '') {
                    $Worksheet1->getStyle($colomSt . $j . ':' . $colomAk . $j)->applyFromArray($styleBorder); //style border record
                }

                $Worksheet1->setCellValueByColumnAndRow(0, $j, $i + 1);
                $Worksheet1->setCellValueExplicitByColumnAndRow(1, $j, $valuef[KD_SOLDTO], PHPExcel_Cell_DataType::TYPE_STRING);
                $Worksheet1->setCellValueByColumnAndRow(2, $j, $valuef[NAMA_SOLDTO]);
                $Worksheet1->setCellValueByColumnAndRow(3, $j, $valuef[CREATED_AT]);
                $Worksheet1->setCellValueByColumnAndRow(4, $j, $valuef[CREATED_BY]);
                $Worksheet1->setCellValueByColumnAndRow(5, $j, $valuef[UPDATED_AT]);
                $Worksheet1->setCellValueByColumnAndRow(6, $j, $valuef[UPDATED_BY]);
                $i++;
                $j = $j + 1;
            }
            $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
            $objWriter->save("php://output");
            break;
    }
}
?>
