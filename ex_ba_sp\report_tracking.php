<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../auth_validation.php');

require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$halaman_id=4871; // PROD
// $halaman_id=3197; DEV
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
// print_r($_SESSION);
// validation($user_id, $_SERVER['PHP_SELF']);


$mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
//    $orgIn= $user_org;
// }




$page="report_tracking.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

// $tanggal_mulai = $_POST['tanggal_mulai'];
// $tanggal_selesai = $_POST['tanggal_selesai'];
$no_ba = $_POST['no_ba'];
$tgl_ba_start = $_POST['tgl_ba_start'];
$tgl_ba_end = $_POST['tgl_ba_end'];
// $status_value = $_POST['status'];
// echo $status_value;
// $no_invoice_expeditur = $_POST['no_invoice_expeditur'];

$currentPage="report_tracking.php";
$komen="";
if(isset($_POST['cari'])){
	
	if($vendor=="" and $no_ba == "" and $tgl_ba_start == "" and $tgl_ba_end == ""){
		// $sql= "SELECT DISTINCT EX_BA.*, to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1, TB_USER_BOOKING.NAMA_LENGKAP, EX_TRANS_HDR.WARNA_PLAT  FROM EX_BA LEFT JOIN EX_TRANS_HDR ON EX_TRANS_HDR.NO_BA = EX_BA.NO_BA LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL WHERE EX_BA.DELETE_MARK ='0' AND EX_BA.ORG in ($user_org) AND EX_BA.NO_BA IS NOT NULL AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ORDER BY EX_BA.ID DESC";

		$sql= "SELECT
					EX_BA.ID,
					EX_BA.NO_BA,
					EX_BA.NO_VENDOR,
					EX_BA.TOTAL_INV,
					EX_BA.PAJAK_INV,
					EX_BA.NAMA_VENDOR,
					EX_BA.KLAIM_KTG,
					EX_BA.KLAIM_SEMEN,
					EX_BA.PDPKS,
					EX_BA.PDPKK,
					EX_BA.DELETE_MARK,
					EX_BA.ORG,
					EX_BA.TOTAL_INVOICE,
					EX_BA.TGL_BA,
					EX_BA.STATUS_BA,
					EX_BA.FILENAME,
					EX_BA.ALASAN_REJECT,
					EX_BA.ID_USER_APPROVAL,
					EX_BA.TIPE_ALASAN,
					EX_TRANS_HDR.WARNA_PLAT,
					TB_USER_BOOKING.NAMA_LENGKAP,
					SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
					SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
					SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
				  SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
					to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
				FROM
					EX_BA
					JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
					LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
				WHERE EX_BA.DELETE_MARK = '0' 
					AND EX_BA.ORG IN ($user_org) 
					AND EX_BA.NO_BA IS NOT NULL 
					AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') 
				GROUP BY EX_BA.ID,
					EX_BA.NO_BA,
					EX_BA.NO_VENDOR,
					EX_BA.TOTAL_INV,
					EX_BA.PAJAK_INV,
					EX_BA.NAMA_VENDOR,
					EX_BA.KLAIM_KTG,
					EX_BA.KLAIM_SEMEN,
					EX_BA.PDPKS,
					EX_BA.PDPKK,
					EX_BA.DELETE_MARK,
					EX_BA.ORG,
					EX_BA.TOTAL_INVOICE,
					EX_BA.TGL_BA,
					EX_BA.STATUS_BA,
					EX_BA.FILENAME,
					EX_BA.ALASAN_REJECT,
					EX_BA.TIPE_ALASAN,
					EX_TRANS_HDR.WARNA_PLAT,
					TB_USER_BOOKING.NAMA_LENGKAP,
					EX_BA.ID_USER_APPROVAL
				ORDER BY
					EX_BA.ID DESC";
	}else {
	// START non-prepared statement
		$pakeor=0;
		$sql= "SELECT
					EX_BA.ID,
					EX_BA.NO_BA,
					EX_BA.NO_VENDOR,
					EX_BA.TOTAL_INV,
					EX_BA.PAJAK_INV,
					EX_BA.NAMA_VENDOR,
					EX_BA.KLAIM_KTG,
					EX_BA.KLAIM_SEMEN,
					EX_BA.PDPKS,
					EX_BA.PDPKK,
					EX_BA.DELETE_MARK,
					EX_BA.ORG,
					EX_BA.TOTAL_INVOICE,
					EX_BA.TGL_BA,
					EX_BA.STATUS_BA,
					EX_BA.FILENAME,
					EX_BA.ALASAN_REJECT,
					EX_BA.ID_USER_APPROVAL,
					EX_BA.TIPE_ALASAN,
					EX_TRANS_HDR.WARNA_PLAT,
					TB_USER_BOOKING.NAMA_LENGKAP,
					SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
					SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
					SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
				  SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
					to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
				FROM
					EX_BA
					JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
					LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
				WHERE ";
		if($vendor!=""){
		$sql.=" EX_BA.NO_VENDOR LIKE '$vendor'";
		$pakeor=1;
		}
		if($no_ba!=""){
			if($pakeor==1){
			$sql.=" AND EX_BA.NO_BA LIKE '$no_ba' ";
			}else{
			$sql.=" EX_BA.NO_BA LIKE '$no_ba' ";
			$pakeor=1;
			}
		}

		if ($tgl_ba_start != "" && $tgl_ba_end != "") {
			if($pakeor==1){
				$sql.=" AND EX_BA.TGL_BA BETWEEN TO_DATE('$tgl_ba_start','YYYY-MM-DD') AND TO_DATE('$tgl_ba_end','YYYY-MM-DD') ";
			}else{
				$sql.=" EX_BA.TGL_BA BETWEEN TO_DATE('$tgl_ba_start','YYYY-MM-DD') AND TO_DATE('$tgl_ba_end','YYYY-MM-DD') ";
				$pakeor=1;
			}
		} else if ($tgl_ba_start != "" && $tgl_ba_end == "") {
			if($pakeor==1){
				$sql.=" AND EX_BA.TGL_BA >= TO_DATE('$tgl_ba_start','DD-MM-YYYY') ";
			}else{
				$sql.=" EX_BA.TGL_BA >= TO_DATE('$tgl_ba_start','DD-MM-YYYY') ";
				$pakeor=1;
			}
		} else if ($tgl_ba_start == "" && $tgl_ba_end != "") {
			if($pakeor==1){
				$sql.=" AND EX_BA.TGL_BA <= TO_DATE('$tgl_ba_end','DD-MM-YYYY') ";
			}else{
				$sql.=" EX_BA.TGL_BA <= TO_DATE('$tgl_ba_end','DD-MM-YYYY') ";
				$pakeor=1;
			}
		}

			$sql.=" AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ";
			$sql.=" AND EX_BA.DELETE_MARK = '0' 
				AND EX_BA.ORG IN ($user_org) 
				AND EX_BA.NO_BA IS NOT NULL 
			GROUP BY EX_BA.ID,
				EX_BA.NO_BA,
				EX_BA.NO_VENDOR,
				EX_BA.TOTAL_INV,
				EX_BA.PAJAK_INV,
				EX_BA.NAMA_VENDOR,
				EX_BA.KLAIM_KTG,
				EX_BA.KLAIM_SEMEN,
				EX_BA.PDPKS,
				EX_BA.PDPKK,
				EX_BA.DELETE_MARK,
				EX_BA.ORG,
				EX_BA.TOTAL_INVOICE,
				EX_BA.TGL_BA,
				EX_BA.STATUS_BA,
				EX_BA.FILENAME,
				EX_BA.ALASAN_REJECT,
				EX_BA.TIPE_ALASAN,
				EX_TRANS_HDR.WARNA_PLAT,
				TB_USER_BOOKING.NAMA_LENGKAP,
				EX_BA.ID_USER_APPROVAL
			ORDER BY
				EX_BA.ID DESC";
		$query= oci_parse($conn, $sql);
	// END non-prepared statement

	// START prepared statement
	$params = array();
	$pakeor = 0;

	$sql = "SELECT
		EX_BA.ID,
		EX_BA.NO_BA,
		EX_BA.NO_VENDOR,
		EX_BA.TOTAL_INV,
		EX_BA.PAJAK_INV,
		EX_BA.NAMA_VENDOR,
		EX_BA.KLAIM_KTG,
		EX_BA.KLAIM_SEMEN,
		EX_BA.PDPKS,
		EX_BA.PDPKK,
		EX_BA.DELETE_MARK,
		EX_BA.ORG,
		EX_BA.TOTAL_INVOICE,
		EX_BA.TGL_BA,
		EX_BA.STATUS_BA,
		EX_BA.FILENAME,
		EX_BA.ALASAN_REJECT,
		EX_BA.ID_USER_APPROVAL,
		EX_BA.TIPE_ALASAN,
		EX_TRANS_HDR.WARNA_PLAT,
		TB_USER_BOOKING.NAMA_LENGKAP,
		SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
		SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
		SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
		SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
		SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
		SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
		SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
		SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
		SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
		TO_CHAR(EX_BA.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1
	FROM EX_BA
	JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
	LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
	WHERE 1=1";

	if ($user_org) {
        $sql .= " AND EX_BA.ORG = :org";
        $params[':org'] = $user_org;
    } else {
        // paksa query tidak hasilkan data jika org kosong
        $sql .= " AND 1=0";
    }

	// Filter VENDOR
	if ($vendor != "") {
		$sql .= " AND EX_BA.NO_VENDOR LIKE :vendor";
		$params[":vendor"] = $vendor;
	}

	// Filter NO_BA
	if ($no_ba != "") {
		$sql .= " AND EX_BA.NO_BA LIKE :no_ba";
		$params[":no_ba"] = $no_ba;
	}

	// Filter TGL_BA
	if ($tgl_ba_start != "" && $tgl_ba_end != "") {
		$sql .= " AND EX_BA.TGL_BA BETWEEN TO_DATE(:tgl_start, 'YYYY-MM-DD') AND TO_DATE(:tgl_end, 'YYYY-MM-DD')";
		$params[":tgl_start"] = $tgl_ba_start;
		$params[":tgl_end"]   = $tgl_ba_end;
	} else if ($tgl_ba_start != "" && $tgl_ba_end == "") {
		$sql .= " AND EX_BA.TGL_BA >= TO_DATE(:tgl_start_only, 'DD-MM-YYYY')";
		$params[":tgl_start_only"] = $tgl_ba_start;
	} else if ($tgl_ba_start == "" && $tgl_ba_end != "") {
		$sql .= " AND EX_BA.TGL_BA <= TO_DATE(:tgl_end_only, 'DD-MM-YYYY')";
		$params[":tgl_end_only"] = $tgl_ba_end;
	}

	// Fixed conditions
	$sql .= "
		AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50')
		AND EX_BA.DELETE_MARK = '0'
		AND EX_BA.NO_BA IS NOT NULL
	GROUP BY 
		EX_BA.ID,
		EX_BA.NO_BA,
		EX_BA.NO_VENDOR,
		EX_BA.TOTAL_INV,
		EX_BA.PAJAK_INV,
		EX_BA.NAMA_VENDOR,
		EX_BA.KLAIM_KTG,
		EX_BA.KLAIM_SEMEN,
		EX_BA.PDPKS,
		EX_BA.PDPKK,
		EX_BA.DELETE_MARK,
		EX_BA.ORG,
		EX_BA.TOTAL_INVOICE,
		EX_BA.TGL_BA,
		EX_BA.STATUS_BA,
		EX_BA.FILENAME,
		EX_BA.ALASAN_REJECT,
		EX_BA.TIPE_ALASAN,
		EX_TRANS_HDR.WARNA_PLAT,
		TB_USER_BOOKING.NAMA_LENGKAP,
		EX_BA.ID_USER_APPROVAL
	ORDER BY EX_BA.ID DESC";

	$query = oci_parse($conn, $sql);
	foreach ($params as $key => $val) {
		oci_bind_by_name($query, $key, $params[$key]);
	}
	// END prepared statement
	}
	// echo $sql;
	oci_execute($query);
	$status = array();
	while($row=oci_fetch_array($query)){

      $com[]=$row[ORG];
			$no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
			$no_ba_v[]=$row[NO_BA];
			$filename[]=$row[FILENAME];
			$id[]=$row[ID];
			$no_invoice_v[]=$row[NO_INVOICE];
			$no_invoice_ex_v[]=$row[NO_INVOICE_EX];
			$vendor_v[]=$row[NO_VENDOR];
			$nama_vendor_v[]=$row[NAMA_VENDOR];
			$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
			$tgl_invoice_v[]=$row[TGL_INVOICE];
			$tgl_ba_v[]=$row[TGL_BA];
			$klaim_semen_v[]=$row[KLAIM_SEMEN];
			$klaim_ktg_v[]=$row[KLAIM_KTG];
			$warna_plat_v[]=$row[WARNA_PLAT]; 
			$qty_ktg_rusak[]=$row[QTY_KTG_RUSAK];
			$qty_semen_rusak[]=$row[QTY_SEMEN_RUSAK];
			$qty_shp[]=$row[QTY_SHP];
			$total_ktg_rusak[]=$row[TOTAL_KTG_RUSAK];
			$total_ktg_rezak[]=$row[TOTAL_KTG_REZAK];
			$total_semen_rusak[]=$row[TOTAL_SEMEN_RUSAK];
			$total_klaim_ktg[]=$row[TOTAL_KLAIM_KTG];
			$total_klaim_semen[]=$row[TOTAL_KLAIM_SEMEN];
			$total_oa_v[]=$row[SHP_COST];
			$pdpks_v[]=$row[PDPKS]; 
			$pend_ktg_v[]=$row[PDPKK]; 
			$pajak_v[]=$row[PAJAK_INV];
			$total_klaim_v[]=$row[TOTAL_INV];
			$nama_lengkap_v[]=$row[NAMA_LENGKAP];
      $no_baf[]=$row[NO_BAF];
      $status[]=$row[STATUS_BA];
      // $filename[]=$row[FILENAME];
      $alasan_reject[]=$row[ALASAN_REJECT];
      $tipe_alasan[]=$row[TIPE_ALASAN];

      $sqlS = "select NO_INVOICE, KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_BA = $row[NO_BA] and STATUS_BA_INVOICE = 110) ";   
			$query_s= @oci_parse($conn, $sqlS);
			@oci_execute($query_s);
			$row_s=@oci_fetch_array($query_s);  
			$no_invoice_ba_v[]=$row_s[NO_INVOICE];
			$keterangan_v[]=$row_s[KOMENTAR_REJECT];
			$status_id=$row_s[STATUS_BA_INVOICE]; 
			if($status_id==10){
				$status_name_v[]= "CREATE INVOICE";
			}else if($status_id==20){
				$status_name_v[]= "UPLOAD INVOICE";
			}else if($status_id==30){
				$status_name_v[]= "REVERSED";
			}else if($status_id==40){
				$status_name_v[]= "REJECTED";
			}else if($status_id==45){
				$status_name_v[]= "CANCEL PPL & INVOICE";
			}else if($status_id==50){
				$status_name_v[]= 'APPROVE CHECKLIST DOKUMEN';
			}else if($status_id==60){
				$status_name_v[]= 'GENERATE PPL';
			}else if($status_id==70){
				$status_name_v[]= 'SIMULATE & POSTING PPL';
			}else if($status_id==80){
				$status_name_v[]= 'REJECT BY MANAJER VERIFIKASI';
			}else if($status_id==90){
				$status_name_v[]= 'APPROVED  BY MANAJER VERIFIKASI';
			}else if($status_id==100){
				$status_name_v[]= 'REJECT BY SM VERIFIKASI';
			}else if($status_id==110){
				$status_name_v[]= 'APPROVED  BY SM VERIFIKASI';
			}else {
				$status_name_v[]= "";
			}
		// }
	}
	$total=count($no_ba_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}


?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: List BASTP :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
	<script src="../include/jquery.min.js"></script>
	<script src="../include/bootstrap/js/bootstrap.min.js"></script>
	<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar BASTP </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search BASTP </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No BASTP</td>
      <td class="puso">:</td>
      <td><input type="text" name="no_ba" value="<?=$no_ba?>"/></td>
    </tr>
	<tr width="174">
      <td class="puso">Periode BASTP</td>
      <td class="puso">:</td>
      <td>
		<input type="date" name="tgl_ba_start" value="<?=$tgl_ba_start?>" />
		<span style="margin: 0 10px;">s/d</span>
		<input type="date" name="tgl_ba_end" value="<?=$tgl_ba_end?>" />
	</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){

?>

<form name="export" method="post" action="report_tracking_xls.php">
	<input name="no_ba" type="hidden" value="<?=$no_ba?>"/>
	<input name="tgl_ba_start" type="hidden" value="<?=$tgl_ba_start?>"/>
	<input name="tgl_ba_end" type="hidden" value="<?=$tgl_ba_end?>"/>
	<!-- <input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	 -->
	&nbsp;&nbsp;

	<div style="text-align: right; padding: 10px 34px;">
		<input name="excel" type="Submit" id="excel" value="Export" /> 	
	</div>
</form>
<form id="data_claim" name="data_claim" method="post" action="komentar.php" >
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td>
                 <td align="center"><strong >Org</strong></td>   
		 <!-- <td align="center"><strong >No Invoice </strong></td> -->
                 <td align="center"><strong >BASTP </strong></td>
                 <td align="center"><strong>No Invoice </strong></td>
				<td align="center"><strong>Status Invoice </strong></td>
		 <!-- <td align="center"><strong>No Invoice EX </strong></td> -->
		 <td align="center"><strong>Expeditur </strong></td>
		 <td align="center"><strong>Nama Expeditur </strong></td>
		 <!-- <td align="center"><strong>No Pajak EX </strong></td> -->
		 <td align="center"><strong>Tgl BASTP </strong></td>
		 <td align="center"><strong>Warna Plat</strong></td>
		 <td align="center"><strong>Klaim Semen </strong></td>
		 <td align="center"><strong>Klaim Kantong</strong></td>
		 <td align="center"><strong>PDPKS</strong></td>
		 <td align="center"><strong>Pend. Ktg</strong></td>
		 <td align="center"><strong>Sub Total</strong></td>
		 <td align="center"><strong>Pajak (PPN)</strong></td>
		 <td align="center"><strong>Total</strong></td>
		 <td align="center"><strong>Detail</strong></td>
		 <td align="center"><strong>Status Terakhir BA</strong></td>
		 <!-- <td align="center"><strong>Aksi</strong></td>-->
		 <td align="center"><strong>Cetak</strong></td> 
		 <!-- <td align="center"><strong>Tipe Alasan Reject</strong></td> -->
		 <!-- <td align="center"><strong>Keterangan</strong></td> -->
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
                $orgCom="orgke".$i;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

		<td align="center"><? echo $b; ?></td>
                <td align="center"><? echo $com[$i]; ?><input name="<?=$orgCom;?>" id="<?=$orgCom;?>" type="hidden" value="<?=$com[$i];?>" /></td>
		<? 
		// $no_cek=$no_ba_v[$i];
		// $sql_print= "SELECT KODE_PRODUK FROM EX_TRANS_HDR WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0' GROUP BY KODE_PRODUK ";
		// $query_print= oci_parse($conn, $sql_print);
		// oci_execute($query_print);
	
		// $row_print=oci_fetch_array($query_print);
		// $kode_produk=$row_print[KODE_PRODUK];
		
		// $rest = substr($kode_produk, 0, -5);
		?>	
                <td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a></td>
                <td align="center">
							<? echo $no_invoice_ba_v[$i]; ?>
						</td>
						<td align="center">
							<? echo $status_name_v[$i]; ?>
						</td>
                <!-- <td align="center"><? echo $no_invoice_ex_v[$i]; ?></td> -->
		<td align="center"><? echo $vendor_v[$i]; ?></td>
		<td align="center"><? echo $nama_vendor_v[$i]; ?></td>
		<!-- <td align="center"><? echo $no_pajak_ex_v[$i]; ?></td> -->
		<td align="center"><? echo $tgl_ba_v[$i]; ?></td>
		<td align="center"><? echo $warna_plat_v[$i] ?></td>
		<!-- <td align="center"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($klaim_ktg_v[$i],0,",","."); ?></td> -->		
		<td align="center"><? echo number_format($qty_semen_rusak[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_ktg_rusak[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pdpks_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pend_ktg_v[$i],0,",","."); ?></td>
		<!-- <td align="center"><? echo number_format($total_klaim_v[$i],2,",","."); ?></td> -->
		<td align="center"><? echo number_format($total_oa_v[$i],0,",","."); ?></td>		
		<td align="center"><? echo number_format($pajak_v[$i],2,",","."); ?></td>
		<td align="center"><? echo number_format($total_oa_v[$i]+$pajak_v[$i],2,",","."); ?></td>
		<td align="center"><a href="javascript:popUp('report_tracking_detail.php?no_ba=<?=$no_ba_v[$i]?>')" class="btn btn-success btn-sm" style="font-size: 10px;">History</a></td>
		<td align="center">
							<? if ($status[$i] == '10') {
				echo "Open";
		}elseif($status[$i] == '11'){
				echo "Rejected";
				
		}elseif($status[$i] == '20'){
				
				echo "Submitted";
		}elseif($status[$i] == '21'){
				
				echo "Revisi";
		}elseif($status[$i] == '1'){
				
				echo "Reverse";
		}elseif($status[$i] == '30'){
				
				echo "Waiting Approval Pejabat Transportasi 1";
		}elseif($status[$i] == '40'){
				
				echo "Waiting Approval Pejabat Transportasi 2";
		}elseif($status[$i] == '50'){
				
				echo "Completed";
		} ?>
						</td>
		<td align="center"><? if ($status[$i] == '10') {
				?>
				<a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BASTP yang belum ditandatangi</a></td>
				<?php
		}elseif($status[$i] == '11' || $status[$i] == '1') {
				?>
				<!-- <a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BA yang belum ditandatangi</a></td> -->
			-</td>
				<?php
		}else{
			?>
			<a href="javascript:popUp('upload/<?=$filename[$i]?>')">Download</a>
			<?php
		} ?></td>
		</tr>
	  <? } ?>
		</tbody>
	  <tr class="quote">
		<td colspan="2" align="center">
		<? $excel="CetakExcel_inv_hdr.php"; ?>
	<!-- 	<a href="<?php printf("%s?&tanggal_mulai=$tanggal_mulai&tanggal_selesai=$tanggal_selesai&no_invoice=$no_invoice&no_invoice_expeditur=$no_invoice_expeditur", $excel,$vendor,$tanggal_mulai,$tanggal_selesai,$no_invoice,$no_invoice_expeditur); ?>"class="button">EXCEL</a> -->
	</td>
		<td colspan="17" align="center">
		<a href="report_tracking.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
