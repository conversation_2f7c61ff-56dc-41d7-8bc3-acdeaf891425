<?php
session_start();
include('../include/or_fungsi.php');
// include('../include/validasi.php');
include('../include/API.php');

$fungsi = new or_fungsi();
$conn = $fungsi->or_koneksi();
$bo_conn = $fungsi->bo_koneksi();



function getData($bastp, $pemilik_brand)
{
    $list_file = array();

    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }

    $fce = $sap->NewFunction("ZCFM_DISP_BAST_MSA_FILE");
    $fce->I_BUKRS = $pemilik_brand;
    $fce->I_BASTB = $bastp;
    $fce->I_DISP = '';

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_BASTBF->Reset();
        $itr = 0;
        while ($fce->T_BASTBF->Next()) {
            $fname = $fce->T_BASTBF->row['FNAME'];
            $urlFile = "https://dev-app1.sig.id/data/MD_Paperless/".$fname;
            $base64 = $fname != "" && file_exists($urlFile) ? base64_encode(file_get_contents($urlFile)) : "";

            $list_file[$itr]['file_name'] = $fce->T_BASTBF->row['FNAME'];
            $list_file[$itr]['base64'] = $base64;
            $itr++;
        }
    }

    $total = count($list_file);
    if ($total < 1) {
        echo json_encode("Tidak Ada Data Yang Ditemukan");
    } else {
        echo json_encode($list_file);
        // echo base64_encode($output);
    }
    exit;
}

function getFile($bastp, $pemilik_brand)
{
    $list_file = array();

    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }

    $fce = $sap->NewFunction("ZCFM_DISP_BAST_MSA_FILE");
    $fce->I_BUKRS = $pemilik_brand;
    $fce->I_BASTB = $bastp;
    $fce->I_DISP = '';

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_BASTBF->Reset();
        while ($fce->T_BASTBF->Next()) {
            $list_file[] = $fce->T_BASTBF->row['FNAME'];
        }
    }

    $total = count($list_file);
    if ($total < 1) {
        echo json_encode("Tidak Ada Data Yang Ditemukan");
    } else {
        return json_encode($list_file);
        // echo base64_encode($output);
    }
}



if (isset($_POST['download'])) {
    $bastp = $_POST['BASTP'];
    $pemilik_brand = $_POST['PEMILIK_BRAND'];
    getData($bastp, $pemilik_brand);
}


if (isset($_POST['email_royalty_sbi'])) {
    // $rootPath = '/opt/lampp/htdocs/dev/sd/sdonline/';
    require_once('phpmailer/phpmailer.php');
    require_once('phpmailer/class.smtp.php');

    $type = $_POST['TYPE'];
    $NO_INV = $_POST['NO_INVOICE'];
    $bastp =  $_POST['BASTP'];
    $pemilik_brand =  $_POST['PEMILIK_BRAND'];

    $jsonFiles = getFile($bastp, $pemilik_brand);
    $files = json_decode($jsonFiles, true);

    $mail_to = "<EMAIL>";
    // $mail_to = "<EMAIL>";
    $emailcc = "<EMAIL>";
    $mail = new PHPMailer();
    $mail->IsSMTP();
    $mail->SMTPDebug  = 1;
    $mail->Host       = "relay.sig.id";
    $mail->Port       = 25;
    $mail->SetFrom('<EMAIL>', 'Invoice MD Royalty'); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
    $mail->Subject   = "Invoice MSA [" . $NO_INV . "]";

    $body  = "Dengan Hormat, <br><br>";
    $body .= "Berikut kami lampirkan Invoice MSA " . $NO_INV . ". <br><br>";
    $body .= "Terima Kasih. <br><br>";
    $mail->MsgHTML($body); //masukkan isi dari email

    // Attchment
    if (is_array($files) && !empty($files)) {
        foreach ($files as $file) {
            $filePath = "https://dev-app1.sig.id/data/MD_Paperless/" . $file;
            $fileContent = @file_get_contents($filePath);
            if ($fileContent !== false) {
                // rinem
                if (strpos($file, 'E-BARR') !== false) {
                    $newFileName = str_replace('E-BARR', 'A', $file);
                } elseif (strpos($file, 'E-INV') !== false) {
                    $newFileName = str_replace('E-INV', 'I', $file);
                } else {
                    $newFileName = $file;
                }
                $mail->addStringAttachment($fileContent, $newFileName);
            } else {
                echo json_encode(array("status" => "File not found: " . $filePath));
            }
        }
    } else {
        echo json_encode(array("status" => "No files to attach or invalid file list"));
    }


    $mail->AddAddress($mail_to);
    $mail->AddCC($emailcc);

    if (!$mail->Send()) {
        $show_ket = "Mailer Error : " . $mail->ErrorInfo; // jika pesan tidak terkirim
    } else {
        $show_ket = "Mail Success to : " . $bastp;
    }

    echo json_encode(array("status" => $show_ket));
}
