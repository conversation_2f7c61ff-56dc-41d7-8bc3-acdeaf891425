<? 
@session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=85;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$user_name_in = $_SESSION['user_name'];

$dirr = $_SERVER['PHP_SELF'];   
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}
$inorg= $user_org;
$currentPage="so_transfer_stock_sync.php";
$komen=""; 
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("You are not have authorize to accessing this page... \n Login Please...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?php

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="so_transfer_stock_sync.php";


if(isset($_POST['cari'])){
	$no_so = trim($_POST['no_so']);
	$plant = $_POST['plant'];
	$material = $_POST['produk1'];
	$tgl1 = $_POST['tgl1'];
	$tgl2 = $_POST['tgl2'];


	if($no_so=="" and $plant=="" and $material == "" and $tgl1 == "" and $tgl2 == ""){
		$sql= "SELECT
					A.*,
					to_char( A.TGL_KIRIM_PP, 'DD-MM-YYYY' ) AS TGL_KIRIM_PP1,
					to_char( A.TGL_KIRIM_APPROVE, 'DD-MM-YYYY' ) AS TGL_KIRIM_APPROVE1,
					to_char( A.APPROVE_DATE, 'DD-MM-YYYY' ) AS APPROVE_DATE1,
					C.LAST_UPDATED_BY_DTL,
					C.LAST_UPDATE_DATE_DTL,
					B.SO_TYPE,
					B.ROUTE,
					B.SOLD_TO,
					B.PRICELIST,
					B.KD_REASON,
					B.TERM_PAYMENT AS TOP1,
					B.INCOTERM AS INCOTERMS
				FROM
					OR_TRANS_APP A
					LEFT JOIN OR_TRANS_HDR_V B ON B.NO_SO = A.NO_SO
					LEFT JOIN ( SELECT NO_SO, MAX( LAST_UPDATED_BY ) AS LAST_UPDATED_BY_DTL, MAX( LAST_UPDATE_DATE ) AS LAST_UPDATE_DATE_DTL FROM OR_TRANS_DTL GROUP BY NO_SO ORDER BY NO_SO ) C ON A.NO_SO = C.NO_SO 
				WHERE
					A.DELETE_MARK = '0' 
					AND B.ORG IN ( '$inorg' ) 
					AND A.KD_PROV IN ( SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID = '$user_id' AND DELETE_MARK = 0 ) 
				ORDER BY
					A.NO_SO ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT
					A.*,
					to_char( A.TGL_KIRIM_PP, 'DD-MM-YYYY' ) AS TGL_KIRIM_PP1,
					to_char( A.TGL_KIRIM_APPROVE, 'DD-MM-YYYY' ) AS TGL_KIRIM_APPROVE1,
					to_char( A.APPROVE_DATE, 'DD-MM-YYYY' ) AS APPROVE_DATE1,
					C.LAST_UPDATED_BY_DTL,
					C.LAST_UPDATE_DATE_DTL,
					B.SO_TYPE,
					B.ROUTE,
					B.SOLD_TO,
					B.PRICELIST,
					B.KD_REASON,
					B.TERM_PAYMENT AS TOP1,
					B.INCOTERM AS INCOTERMS
				FROM
					OR_TRANS_APP A
					LEFT JOIN OR_TRANS_HDR_V B ON B.NO_SO = A.NO_SO
					LEFT JOIN ( SELECT NO_SO, MAX( LAST_UPDATED_BY ) AS LAST_UPDATED_BY_DTL, MAX( LAST_UPDATE_DATE ) AS LAST_UPDATE_DATE_DTL FROM OR_TRANS_DTL GROUP BY NO_SO ORDER BY NO_SO ) C ON A.NO_SO = C.NO_SO
				WHERE 
					B.ORG IN ('$inorg') AND
                	A.KD_PROV IN (SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0) AND";
		if($no_so!=""){
			if($pakeor==1){
				$sql.=" A.NO_SO LIKE '$no_so' ";
			}else{
				$sql.=" A.NO_SO LIKE '$no_so' ";
				$pakeor=1;
			}
		}
		if($plant!=""){
			if($pakeor==1){
				$sql.=" AND A.PLANT LIKE '$plant' ";
			}else{
				$sql.=" A.PLANT LIKE '$plant' ";
				$pakeor=1;
			}
		}
		if($material!=""){
			if($pakeor==1){
				$sql.=" AND A.KODE_PRODUK LIKE '$material' ";
			}else{
				$sql.=" A.KODE_PRODUK LIKE '$material' ";
				$pakeor=1;
			}
		}
		if($tgl1!="" and $tgl2!=""){
			if ($tgl1=="")$tgl1_sql = "01-01-1990";
			else $tgl1_sql = $tgl1;
			if ($tgl2=="")$tgl2_sql = "12-12-9999";
			else $tgl2_sql = $tgl2;
			if($pakeor==1){
			$sql.=" AND A.TGL_KIRIM_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			}else{
			$sql.=" A.TGL_KIRIM_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS')";
			$pakeor=1;
			}
		}			
		$sql.=" AND A.DELETE_MARK = '0' ORDER BY A.NO_SO ASC";
	}
	// echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$id_app[]=$row['ID'];  
		$no_pp_app[]=$row['NO_PP'];
		$kdproduk_app[]=$row['KODE_PRODUK'];
		$produk_app[]=$row['NAMA_PRODUK'];
		$qty_app[]=$row['QTY_PP'];
		$approve_app[]=$row['QTY_APPROVE'];
		$tgl_kirim_app[]=$row['TGL_KIRIM_PP'];
		$tgl_kirim_approve_app[]=$row['TGL_KIRIM_APPROVE'];
		$tgl_terima_app[]=$row['TGL_TERIMA'];
		$soldto_app[]=$row['SOLD_TO'];
		$shipto_app[]=$row['SHIP_TO'];
		$nm_shipto_app[]=$row['NAMA_SHIP_TO'];
		$alamat_app[]=$row['ALAMAT_SHIP_TO'];
		$status_app[]=$row['STATUS_LINE'];
		$kddistrik_app[]=$row['KODE_TUJUAN'];
		$nmdistrik_app[]=$row['NAMA_TUJUAN'];
		$item_number_app[]=$row['ITEM_NUMBER'];
		$nosonya[]=$row['NO_SO'];  
        $flag_kapal_app[]=$row['FLAG_KAPAL']; 
        $ket_app[]=$row['KETERANGAN'];
        $nmkapal_app[]=$row['NAMA_KAPAL'];
		$appdate_app[]=$row['APPROVE_DATE']; 
		$approve_app[]=$row['APPROVE_BY'];
		$no_kontrak_app[]=$row['NO_KONTRAK'];  
		$kdprov_app[]=$row['KD_PROV'];  
		$nmprov_app[]=$row['NM_PROV'];  
		$plant_app[]=$row['PLANT'];  
		$nmplant_app[]=$row['NM_PLANT'];  
		$no_shp_app[]=$row['NO_SHP_OLD'];
		$noso_old_app[]=$row['NO_SOLD_OLD'];
		$price_date_app[]=$row['PRICE_DATE'];
        $kd_reject_app[]=$row['KD_REJECT'];
        $note_bl_app[]=$row['NOTE_BL']; 
        $exp_app[]=$row['EXPRIRED_DATE']; 
        $kd_kantong_app[]=$row['KD_KANTONG']; 
        $nm_kantong_app[]=$row['NM_KANTONG']; 
        $note_app[]=$row['NOTE']; 
		$so_type[]=$row['SO_TYPE'];
		$route_app[]=$row['ROUTE'];
		$pricelist_app[]=$row['PRICELIST'];
		$reason_app[]=$row['KD_REASON'];
		$top_app[]=$row['TOP1'];
		$incoterm[]=$row['INCOTERMS'];
		$last_update_app[]=$row['LAST_UPDATED_BY_DTL'];   
		$last_update_date_app[]=$row['LAST_UPDATE_DATE_DTL']; 
	}
	$total=count($no_pp_app);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

} elseif(isset($_POST['generate'])) {
	$gnoso = trim($_POST['gnoso']);

	$sql1= "SELECT
			A.*,
			to_char( A.TGL_KIRIM_PP, 'DD-MM-YYYY' ) AS TGL_KIRIM_PP1,
			to_char( A.TGL_KIRIM_APPROVE, 'DD-MM-YYYY' ) AS TGL_KIRIM_APPROVE1,
			to_char( A.APPROVE_DATE, 'DD-MM-YYYY' ) AS APPROVE_DATE1,
			C.LAST_UPDATED_BY_DTL,
			C.LAST_UPDATE_DATE_DTL,
			B.SO_TYPE,
			B.ROUTE,
			B.SOLD_TO,
			B.PRICELIST,
			B.KD_REASON,
			B.TERM_PAYMENT AS TOP1,
			B.INCOTERM AS INCOTERMS
		FROM
			OR_TRANS_APP A
		LEFT JOIN OR_TRANS_HDR_V B ON B.NO_SO = A.NO_SO
		LEFT JOIN 
			( SELECT 	
				NO_SO, 
				MAX( LAST_UPDATED_BY ) AS LAST_UPDATED_BY_DTL, MAX( LAST_UPDATE_DATE ) AS LAST_UPDATE_DATE_DTL 
			FROM 	
				OR_TRANS_DTL 
			GROUP BY 
				NO_SO 
			ORDER BY 
				NO_SO ) C ON A.NO_SO = C.NO_SO
		WHERE 
			B.ORG IN ('$inorg') AND
            A.KD_PROV IN (SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0) AND A.NO_SO LIKE '$gnoso'";


	// echo $sql1;
	$gplant = $_POST['gplant'];
	$qty = $_POST['qty'];
	$sales_org = trim($_POST['orgn']);

	$query1= oci_parse($conn, $sql1);
	oci_execute($query1);

	while($row1=oci_fetch_array($query1)){
		$id_app1[]=$row1['ID'];  
		$no_pp_app1[]=$row1['NO_PP'];
		$kdproduk_app1[]=$row1['KODE_PRODUK'];
		$produk_app1[]=$row1['NAMA_PRODUK'];
		$qty_app1[]=$row1['QTY_PP'];
		$approve_app1[]=$row1['QTY_APPROVE'];
		$tgl_kirim_app1[]=$row1['TGL_KIRIM_PP'];
		$tgl_kirim_approve_app1[]=$row1['TGL_KIRIM_APPROVE'];
		$tgl_terima_app1[]=$row1['TGL_TERIMA'];
		$shipto_app1[]=$row1['SHIP_TO'];
		$soldto_app1[]=$row['SOLD_TO'];
		$nm_shipto_app1[]=$row1['NAMA_SHIP_TO'];
		$alamat_app1[]=$row1['ALAMAT_SHIP_TO'];
		$status_app1[]=$row1['STATUS_LINE'];
		$kddistrik_app1[]=$row1['KODE_TUJUAN'];
		$nmdistrik_app1[]=$row1['NAMA_TUJUAN'];
		$item_number_app1[]=$row1['ITEM_NUMBER'];
		$nosonya1[]=$row1['NO_SO'];  
        $flag_kapal_app1[]=$row1['FLAG_KAPAL']; 
        $ket_app1[]=$row1['KETERANGAN'];
        $nmkapal_app1[]=$row1['NAMA_KAPAL'];
		$appdate_app1[]=$row1['APPROVE_DATE']; 
		$approve_app1[]=$row1['APPROVE_BY'];
		$no_kontrak_app1[]=$row1['NO_KONTRAK'];  
		$kdprov_app1[]=$row1['KD_PROV'];  
		$nmprov_app1[]=$row1['NM_PROV'];  
		$plant_app1[]=$row1['PLANT'];  
		$nmplant_app1[]=$row1['NM_PLANT'];  
		$no_shp_app1[]=$row1['NO_SHP_OLD'];
		$noso_old_app1[]=$row1['NO_SOLD_OLD'];
		$price_date_app1[]=$row1['PRICE_DATE'];
        $kd_reject_app1[]=$row1['KD_REJECT'];
        $note_bl_app1[]=$row1['NOTE_BL']; 
        $exp_app1[]=$row1['EXPRIRED_DATE']; 
        $kd_kantong_app1[]=$row1['KD_KANTONG']; 
        $nm_kantong_app1[]=$row1['NM_KANTONG']; 
        $note_app1[]=$row1['NOTE']; 
		$so_type1[]=$row1['SO_TYPE'];
		$route_app1[]=$row1['ROUTE'];
		$reason_app1[]=$row1['KD_REASON'];
		$pricelist_app1[]=$row1['PRICELIST'];
		$top_app1[]=$row1['TOP1'];
		$incoterm1[]=$row1['INCOTERMS'];
		$last_update_app1[]=$row1['LAST_UPDATED_BY_DTL'];   
		$last_update_date_app1[]=$row1['LAST_UPDATE_DATE_DTL']; 
	}

	if ($no_pp_app1[0]!=''or $no_pp_app1[0]!=null) {
		$sqlpo = "SELECT
					REGEXP_SUBSTR( PESAN_DETAIL, 'PO Int\.company Sales created under the number (\d+)', 1, 1, NULL, 1 ) AS NO_PO 
				FROM
					ZMD_LOG_SBI 
				WHERE
					NO_PP = '$no_pp_app1[0]' 
				AND TGL = ( SELECT MAX( TGL ) FROM ZMD_LOG_SBI WHERE NO_PP = '$no_pp_app1[0]' )";

		$querypo = oci_parse($conn, $sqlpo);
		oci_execute($querypo);

		while($rowpo=oci_fetch_array($querypo)){
			$nopo2[]=$rowpo['NO_PO'];  
		}
	}

	$sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }


	// ORDER ITEMS IN
	// echo "<br><br>INI ITM_NUMBER : " . $item_number_app1[0] * 10 . "<br>"; //000010
	// echo "INI MATERIAL   : " . $kdproduk_app1[0] . "<br>";
	// echo "INI PLANT	  	 : " . $gplant . "<br>";
	// echo "INI SALES DIST : " . $kddistrik_app1[0]. "<br>";
	// echo "INI SALES DIST : " . $kddistrik_app1[0]. "<br>";
	// echo "INI SHIP_POINT : " . $gplant . "<br>";
	// echo "INI ROUTE 	 : " . $route_app1[0] . "<br>";

	// // ORDER SCHEDULES IN
	// $req_date = date('Ymd', strtotime($tgl_kirim_app1[0]));
	// echo "<br><br>INI ITM_NUMBER : " . $item_number_app1[0] * 10 . "<br>"; //000010
	// echo "INI REQ_DATE   : ".$req_date."<br>"; //YYYYMMDD
	// echo "INI REQ_DATE2   : ".$tgl_kirim_app1[0]."<br>"; //YYYYMMDD
	// echo "INI REQ_QTY    : ".$qty."<br>";

    $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
    if ($fce == false) {
        $sap->PrintStatus();
        exit;
    }
                
	// ORDER ITEMS IN
    $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] =  $item_number_app1[0] * 10; //'000010';
    $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $kdproduk_app1[0];
    $fce->ORDER_ITEMS_IN->row["PLANT"] = $gplant;
    $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $kddistrik_app1[0];
	$fce->ORDER_ITEMS_IN->row["SHIP_POINT"] = $gplant;
	$fce->ORDER_ITEMS_IN->row["ROUTE"] = $route_app1[0]; 
    $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);

	// ORDER TIMES INX
	// $fce->ORDER_ITEMS_INX->row["REF_1_S"] = 'X';
	// $fce->ORDER_ITEMS_INX->row["UPDATEFLAG"] = 'U';
	// $fce->ORDER_ITEMS_INX->row["ITM_NUMBER"] = $item_number_app1[0] * 10;
	// $fce->ORDER_ITEMS_INX->Append($fce->ORDER_ITEMS_INX->row);

	// ORDER SCHEDULES IN
    $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_number_app1[0] * 10; //'000010';
    $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = date('Ymd', strtotime($tgl_kirim_app1[0])); //YYYYMMDD
    $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $qty; //QTY
    $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);

	// ORDER SCHEDULER INX
    $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_number_app1[0] * 10; //'000010';
    $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
    $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
    $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
    $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);

	// ORDER PARTNERS
	$fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000'; //'000010';
    $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
    $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto_app1[0];
	$fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
    
	// ORDER HEADER IN
	$fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type1[0]; //"ZOR";
    $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"4000";
    $fce->ORDER_HEADER_IN["DISTR_CHAN"] = '10'; //"10";
    $fce->ORDER_HEADER_IN["DIVISION"] = '00'; //"00";
    $fce->ORDER_HEADER_IN["SALES_OFF"] = $kdprov_app1[0]; //"00";
	$fce->ORDER_HEADER_IN["REQ_DATE_H"] = date('Ymd', strtotime($tgl_kirim_app1[0])); //YYYYMMDD
	$fce->ORDER_HEADER_IN["PURCH_DATE"] = date('Ymd', strtotime($tgl_kirim_app1[0])); //YYYYMMDD 
	$fce->ORDER_HEADER_IN["SALES_DIST"] = $kddistrik_app1[0]; //251001 
	$fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist_app1[0];
	$fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1[0]; //"Z1";
    $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm1[0];
	$fce->ORDER_HEADER_IN["PMNTTRMS"] = $top_app1[0]; //"Z1";
	$fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp_app1[0]; //"Z1";
	$fce->ORDER_HEADER_IN["CURRENCY"] = 'IDR';
    // $fce->ORDER_HEADER_IN["SALES_GRP"] = '101'; //"00";
	// $fce->ORDER_HEADER_IN["REF_1_S"] = $nosonya1[0];
	// $fce->ORDER_HEADER_IN["SHIP_COND"] = '01';

	// COVNERT
	$fce->CONVERT = 'X';

	// ORDER HEADER INX
	// $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
    // $fce->ORDER_HEADER_INX["PURCH_NO_S"]='X';
    // $fce->ORDER_HEADER_INX["REF_1_S"]='X'; 
    
	$fce->SALESDOCUMENT = $nosonya1[0];
    // echo "mantap";
    // echo '<pre>';
    // print_r($fce);
    // echo '</pre>';
    $fce->Call();

    if ($fce->GetStatus() == SAPRFC_OK) {
        $nomorso = $fce->SALESDOCUMENT;
        $fce->RETURN->Reset();
        while ($fce->RETURN->Next()) {
            $tipe = $fce->RETURN->row["TYPE"];
            $msg = $fce->RETURN->row["MESSAGE"];
            $id = $fce->RETURN->row["ID"];
            $number = $fce->RETURN->row["NUMBER"];
			if ($tipe != 'S') {
                $show_ket .= $msg;
                $show_ket .= '<br>';
                $show_ket .= 'SO long text';
                $show_ket .= '<br>';
                $movetestrun='X';
                $nomorso = '';
            }
        }
    }

    
	if($nomorso!=''or $nomorso!=null){
        //Commit Transaction
        $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
        $fce->Call();

		$fce = $sap->NewFunction("Z_ZCSD_UPDATE_REF_SO");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
		
		$fce->I_ZVBELN = $nomorso;
        $fce->I_VBELN = $nosonya1[0];
		$fce->I_PO = $nopo2[0];
		$fce->I_LINE_ITEM = $item_number_app1[0] * 10; //000010
        $fce->Call();

		if ($fce->GetStatus() == SAPRFC_OK) {
            $tipe2 = $fce->ZMESSAGE["TYPE"];
			$msg2 = $fce->ZMESSAGE["MESSAGE"];
            // if ($tipe2 != 'S') {
			// 	echo "Tipe: " . $tipe2 . "<br>";
			// 	echo "Message: " . $msg2 . "<br>";
            // } else {
			// 	echo "Tipe: " . $tipe2 . "<br>";
			// 	echo "Message: " . $msg2 . "<br>";
			// }
		}

		// $show_ket .= "Sales Order has been made with a number : " . $nomorso;
		// $show_ket .= "Sales Standard " . $nomorso . " has been saved.<br>";
	}

    $fce->Close();
    $sap->Close();


	// UPDATE REFERENCE KE SALES ORDER BARU
	if ($nomorso != '' or $nomorso != null or $nomorso != '' or $nomorso != null) {
		$sapC = new SAPConnection();
		$sapC->Connect("../include/sapclasses/logon_data.conf");
		if ($sapC->GetStatus() == SAPRFC_OK)
			$sapC->Open();
		if ($sapC->GetStatus() != SAPRFC_OK) {
			$sapC->PrintStatus();
			exit;
		}
		
		$fceC = $sapC->NewFunction("Z_ZCSD_UPDATE_REF_SO");
		if ($fceC == false) {
			$sapC->PrintStatus();
			exit;
		}
		
		// echo "<br> $nomorso = ".  $nomorso;
		// echo "<br> $nosonya1[0] = ".  $nosonya1[0];
		$fceC->I_ZVBELN = $nosonya1[0];
		$fceC->I_VBELN = $nomorso;
		$fceC->I_PO = $nopo2[0];
		$fceC->I_LINE_ITEM = $item_number_app1[0] * 10; 

		$fceC->Call();

		if ($fceC->GetStatus() == SAPRFC_OK) {
			$tipe = $fceC->ZMESSAGE["TYPE"];
			$msg = $fceC->ZMESSAGE["MESSAGE"];
			if ($tipe != 'S') {
				$type = $tipe;
				$show_ket .= $msg;
			} else {
				$type = $tipe;
				$show_ket .= "Sales Standard " . $nomorso . " has been saved.<br>";
			}	
		}

		$fceC->Close();
		$sapC->Close();
	}
	
}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

	function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
	function findplant() {	
			var comorg = document.getElementById('org');
			var strURL="cari_plant.php?org="+comorg.value;
			popUp(strURL);
	}
	function ketik_plant(obj) {
		var com=document.getElementById('org');
		var nilai_tujuan =obj.value;
		var cplan=document.getElementById('nama_plant');						
		cplan.value = "";
		var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('plantdiv').innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
	function findshipto() {	
			var com_sold = document.getElementById('sold_to');
			var strURL="cari_shipto.php?&sold_to="+com_sold.value;
			popUp(strURL);
	}

	function ketik_shipto(obj) {
		var com_sold = document.getElementById('sold_to');
		var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById("shiptodiv").innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}

	function finddistr(org) {
			var com_org = document.getElementById('org');		
			var strURL="cari_distr.php?org="+com_org.value;
			popUp(strURL);
			} 

	function ketik_distr(obj) {
		var com_org = document.getElementById('org');		
		var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() { 
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {	
						document.getElementById("distrdiv").innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}

	function ketik_produk(obj,ke) {
		var com_plant = document.getElementById("plant");
		var comorg = document.getElementById('org');
		var strURL="ketik_list_material_stock_sync.php?produk="+obj.value+"&plant=" + com_plant.value + "&nourut=" + ke + "&org=" + comorg.value;	
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					if (req.status == 200) {						
						document.getElementById("produkdiv"+ke).innerHTML=req.responseText;	
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}

	function findproduk(ke) {
		var comorg = document.getElementById('org');
		var com_plant = document.getElementById("plant");
		var strURL = "cari_list_material_stock_sync.php?plant=" + com_plant.value + "&nourut=" + ke + "&org=" + comorg.value;
		popUp(strURL);
	}

	window.onload = function () {
		// Menangani event klik tombol
		document.querySelectorAll('.toggleButton').forEach(function (buttonElement) {
			buttonElement.addEventListener('click', function () {
			var noSo = this.getAttribute('data-no-so');

			document.getElementById("popupModal").style.display = "block";
			document.getElementById("gnoso").value = noSo; // ← isi input form
			});
		});

		// Menutup modal jika tombol close ditekan
		const closeBtn = document.querySelector(".close");
		if (closeBtn) {
			closeBtn.onclick = function () {
			document.getElementById("popupModal").style.display = "none";
			};
		}

		// Menutup modal jika klik di luar popup
		window.onclick = function (event) {
			var modal = document.getElementById("popupModal");
			if (event.target == modal) {
			modal.style.display = "none";
			}
		};

		// Tangani submit form jika perlu (optional)
		// const form = document.getElementById("tambah"); // ← form ID adalah "tambah"
		// if (form) {
		// 	form.addEventListener("submit", function (event) {
		// 	// event.preventDefault(); // uncomment jika tidak ingin submit langsung
		// 	alert("Data disubmit untuk NO SO: " + document.getElementById("gno_so").value);
		// 	});
		// }
		};
</script>
<style>
	.modal {
		display: none;
		position: fixed;
		z-index: 999;
		left: 0; top: 0;
		width: 100%; height: 100%;
		overflow: auto;
		background-color: rgba(0,0,0,0.4);
	}

		/* Modal Content */
	.modal-content {
		background-color: #fefefe;
		margin: 10% auto;
		padding: 10px;
		width: 300px;
		border-radius: 8px;
	}

	/* Close Button */
	.close {
		color: #aaa;
		float: right;
		font-size: 20px;
		font-weight: bold;
	}
	.close:hover,
	.close:focus {
		color: black;
		text-decoration: none;
		cursor: pointer;
	}
</style>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>SGG Online Application: See the Purchase Request Data</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="../include/script.js"></script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">SO Transfer And Stock Sync</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;SO Transfer And Stock Sync</th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No SO</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_so" name="no_so" value="<?=$no_so?>"/>
	  <input type="hidden" id="org" name="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr>
		<td  class="puso">Plant </td>
		<td  class="puso">:</td>
		<td >
			<div id="plantdiv">
				<input name="plant" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>&nbsp;&nbsp;&nbsp;&nbsp;
				<input name="nama_plant" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
				<input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
				<input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
			</div>
		</td>
    </tr>
	<tr>
        <td class="puso">List Material</strong></td>
        <td class="puso">:</strong></td>
        <td colspan="2">
                <div>
                <!-- <td align="left"> -->
                    <div id="produkdiv1">
                        <input name="produk1" type="text" class="inputlabel" id="produk1" value="<?=$produk?>"  onChange="ketik_produk(this,'1'); clearData();"  maxlength="20" size="12"/>
                        <input name="nama_produk1" type="text" class="inputlabel" id="nama_produk1" value="<?=$nama_produk?>" readonly="true"  size="20"/>
                        <input name="uom1" type="text" class="inputlabel" id="uom1" value="<?=$uom?>" readonly="true"  size="4"/>
                        <input name="qto1" type="hidden" class="inputlabel" id="qto1" value="<?=$kg?>" readonly="true"  size="4"/>
                        <input name="btn_produk1" type="button" class="button" id="btn_produk1" value="..." onClick="findproduk('1')"/>
                        <input name="val_error_produk1" type="hidden" id="val_error_produk1" value="0" />
                    </div>
                <!-- </td>  -->
                </div>  
        </td>
    </tr>
    <tr>
      <td  class="puso">Tanggal SO</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl1?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl2?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<?php if (!empty($show_ket)) { if($type == 'S') { 	?>
    <div style="margin-top: 10px; text-align: center; color: green;">
        <?= $show_ket ?>
    </div>
<?php } else { ?>
	<div style="margin-top: 10px; text-align: center; color: red;">
		<?= $show_ket ?>
	</div>
<?php } } ?>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
		<form name="export" method="post" action="so_transfer_stock_sync_xls.php">
			<input name="plant" id="plant" type="hidden" value="<?=$plant?>"/>
			<input name="no_so" id="no_so" type="hidden" size="10" value="<?=$no_so;?>"/>
			<input name="produk1" id="produk1" type="hidden" size="10" value="<?=$material;?>"/>
			<input name="tgl1" id="tgl1" type="hidden" size=12 value="<?=$tgl1?>" />
			<input name="tgl2" id="tgl2" type="hidden" size=12 value="<?=$tgl2?>" />
			<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	
			&nbsp;&nbsp;
			<input name="excel" type="Submit" id="excel" value="Export" /> 	
		</form>
		<table width="1200" align="center">
			<tr>
				<th align="right" colspan="4">
					<span></span>
				</th>
			</tr>
		</table>
	</div> 
	<div align="center">
		<table width="1200" align="center" class="adminlist">
			<tr>
				<th align="left" colspan="17">
					<span class="style5">&nbsp;SO Transfer And Stock Sync Table </span>
				</th>
			</tr>
		</table>
	</div> 
	<div align="center">
		<table width="1200" align="center" class="adminlist">
			<tr class="quote">
				<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
				<td align="center"><strong>NO SO</strong></td>	
				<td align="center"><strong>NO PP</strong></td>
				<td align="center"><strong>Kode Produk</strong></td>
				<td align="center"><strong>Nama Produk</strong></td>
				<td align="center"><strong>So Type</strong></td>
				<td align="center"><strong>Qty PP</strong></td>
				<td align="center"><strong>Qty Approve</strong></td>
				<td align="center"><strong>Tgl Kirim PP</strong></td>
				<td align="center"><strong>Tgl Kirim Approve</strong></td>
				<td align="center"><strong>Tgl Terima</strong></td>
				<td align="center"><strong>Kode Shipto</strong></td>
				<td align="center"><strong>Nama Shipto</strong></td>
				<td align="center"><strong>Alamat Shipto</strong></td>
				<td align="center"><strong>Status Line</strong></td>
				<td align="center"><strong>Kode Tujuan</strong></td>
				<td align="center"><strong>Nama Tujuan</strong></td>
				<td align="center"><strong>Kode Prov</strong></td>
				<td align="center"><strong>Nama Prov</strong></td>
				<td align="center"><strong>Plant </strong></td>
				<td align="center"><strong>Nama Plant</strong></td>
				<td align="center"><strong>Action</strong></td>
			</tr >

			<?  for($i=0; $i<$total;$i++) {
				$b=$i+1;
				if(($i % 2) == 0)	{	 
					echo "<tr class='row0'>";
				} else {	
					echo "<tr class='row1'>";
				}	
				if($no_pp_app[$i] != $no_pp_app[$i-1]){
			?>   
				<td align="center"><? echo $b; ?></td>
				<td align="center"><? echo $nosonya[$i]; ?></td>
				<td align="center"><? echo $no_pp_app[$i]; ?></td>
				<td align="center"><? echo $kdproduk_app[$i]; ?></td>
				<td align="center"><? echo $produk_app[$i]; ?></td>	
				<td align="center"><? echo $so_type[$i]; ?></td>	
				<td align="center"><? echo $qty_app[$i]; ?></td>	
				<td align="center"><? echo $approve_app[$i]; ?></td>	
				<td align="center"><? echo $tgl_kirim_app[$i]; ?></td>	
				<td align="center"><? echo $tgl_kirim_approve_app[$i]; ?></td>	
				<td align="center"><? echo $tgl_terima_app[$i]; ?></td>	
				<td align="center"><? echo $shipto_app[$i]; ?></td>	
				<td align="center"><? echo $nm_shipto_app[$i]; ?></td>
				<td align="center"><? echo $alamat_app[$i]; ?></td>
				<td align="center"><? echo $status_app[$i]; ?></td>
				<td align="center"><? echo $kddistrik_app[$i]; ?></td>
				<td align="center"><? echo $nmdistrik_app[$i]; ?></td>
				<td align="center"><? echo $kdprov_app[$i]; ?></td>
				<td align="center"><? echo $nmprov_app[$i]; ?></td>
				<td align="center"><? echo $plant_app[$i]; ?></td>
				<td align="center"><? echo $nmplant_app[$i]; ?></td>
				<td align="center">
					<button class="toggleButton" data-no-so="<?= $nosonya[$i]; ?>" style="padding: 4px 8px; font-size: 12px;">Generate</button>
				</td>
			</tr>
			<? } }?>
			<tr class="quote">
				<td colspan="17" align="center"></td>
			</tr>
		</table>
	</div>
	
	<? } ?>

	<!-- Modal Popup -->
	<div id="popupModal" class="modal">
		<div class="modal-content">
			<span class="close">&times;</span>
			<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
				<table width="300" align="center" class="adminform">
					<tr width="200">
					<td class="puso">&nbsp;</td>
					<td class="puso">&nbsp;</td>
					<td>&nbsp;</td>
					<tr width="200">
					<td class="puso">ORG</td>
					<td class="puso">:</td>
					<td><input type="text" id="orgn" name="orgn" value="<?=$sales_org?>"/>
					</tr>
					<tr width="200">
					<td class="puso">Plant OPCO</td>
					<td class="puso">:</td>
					<td><input type="text" id="gplant" name="gplant" value="<?=$gplant?>"/>
					<td><input type="hidden" id="gnoso" name="gnoso" value="<?=$gnoso?>"/>
					</tr> 
					<tr width="200">
					<td class="puso">QTY</td>
					<td class="puso">:</td>
					<td><input type="text" id="qty" name="qty" value="<?=$qty?>"/>
					</tr> 
					<tr>
					<td class="ThemeOfficeMenu">&nbsp;</td>
					<td class="ThemeOfficeMenu">&nbsp;</td>
					<td rowspan="2"><input name="generate" type="submit" class="button" id="generate" value="Generate" />   
					</tr>
					<tr>
					<td class="ThemeOfficeMenu">&nbsp;</td>
					<td class="ThemeOfficeMenu">&nbsp;</td>
					</tr>
				</table>
				</form>
				</form>
		</div>
	</div>

	<div align="center">
		<? echo $komen; ?>
	</div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
