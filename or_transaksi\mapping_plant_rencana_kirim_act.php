<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$org = htmlspecialchars($_REQUEST['ORG']);
$plant_pengirim = htmlspecialchars($_REQUEST['PLANT_PENGIRIM']);
$plant_tujuan = htmlspecialchars($_REQUEST['PLANT_TUJUAN']);
$delete = htmlspecialchars($_REQUEST['delete']);
$status= htmlspecialchars($_REQUEST['DELETE_MARK']);
$id = htmlspecialchars($_REQUEST['ID']);
$CREATED_BY = htmlspecialchars($user_name);
$UPDATED_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 5;
                    for ($row = 2; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[2]) && empty($row[3]) && empty($row[4])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[2]) || empty($row[3]) || empty($row[4])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkUploadData($conn, $row[2], $row[3], $row[4])) {
                        $messageRows['database'][] = $rowNumber;
                        continue;
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (upload($conn, $row[2], $row[3], $row[4], $CREATED_BY)) {
                        $messageRows['success'][] = $rowNumber;
                    } else {
                        $messageRows['system'][] = $rowNumber;
                    }
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;
        case 'show' :
        {
            displayData($conn,$sort,$order);
        }
        break;
        case 'add':
        {
            // Cek apakah data duplikat
            if (checkDuplicateData($conn, $org, $plant_pengirim, $plant_tujuan)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn, $org, $plant_pengirim, $plant_tujuan, $CREATED_BY)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            $sqlcek= "UPDATE MAPPING_PLANT_RENCANA_KIRIM set ORG = '$org', PLANT_ASAL = '$plant_pengirim', PLANT_TUJUAN = '$plant_tujuan', UPDATED_AT = SYSDATE, DELETE_MARK = '$status',UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Data berhasil diupdate!"));
            } else {
                echo json_encode(array('errorMsg'=>'Gagal menambahkan data. Silakan coba lagi!'));
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "DELETE MAPPING_PLANT_RENCANA_KIRIM WHERE ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Data berhasil dihapus!"));
            } else {
                echo json_encode(array('errorMsg'=>'Gagal menambahkan data. Silakan coba lagi!'));
            }
        }
        break;
    }
}

function displayData($conn,$sort,$order){
    $org = $_SESSION['user_org'];
    if($conn){
        $sql1 = "SELECT * FROM MAPPING_PLANT_RENCANA_KIRIM ORDER BY DELETE_MARK ASC, GREATEST( CREATED_AT, UPDATED_AT ) DESC";
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID']    = $row['ID'];
            $result[$i]['ORG']   = $row['ORG'];
            $result[$i]['PLANT_PENGIRIM'] = $row['PLANT_ASAL'];
            $result[$i]['PLANT_TUJUAN']   = $row['PLANT_TUJUAN'];
            $result[$i]['DELETE_MARK']  = $row['DELETE_MARK'];
            $result[$i]['CREATED_AT'] = $row['CREATED_AT'] == null ? 'tidak ada keterangan' : $row['CREATED_AT'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? 'tidak ada keterangan' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT'] == null ? 'tidak ada keterangan' : $row['UPDATED_AT'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? 'tidak ada keterangan' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn, $ORG, $PLANT_PENGIRIM, $PLANT_TUJUAN, $CREATED_BY){
    $sqlcek= "INSERT INTO MAPPING_PLANT_RENCANA_KIRIM (ORG, PLANT_ASAL, PLANT_TUJUAN, CREATED_AT, CREATED_BY, DELETE_MARK) values ('".$ORG."', '".$PLANT_PENGIRIM."', '".$PLANT_TUJUAN."', SYSDATE, '".$CREATED_BY."', '0')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn, $ORG, $PLANT_PENGIRIM, $PLANT_TUJUAN) {
    // Query untuk menghitung duplikasi
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_PLANT_RENCANA_KIRIM 
                WHERE ORG = :ORG 
                    AND PLANT_ASAL = :PLANT_PENGIRIM 
                    AND PLANT_TUJUAN = :PLANT_TUJUAN
                    AND DELETE_MARK = 0";

    $query_count = oci_parse($conn, $sql_count);
    oci_bind_by_name($query_count, ':ORG', $ORG);
    oci_bind_by_name($query_count, ':PLANT_PENGIRIM', $PLANT_PENGIRIM);
    oci_bind_by_name($query_count, ':PLANT_TUJUAN', $PLANT_TUJUAN);

    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    if ($result) {
        // Jika ada duplikasi, kembalikan true
        return true;
    }
    // Jika tidak ada duplikasi, kembalikan false
    return false;
}


function checkUploadData($conn, $ORG, $PLANT_PENGIRIM, $PLANT_TUJUAN) {
    // Query untuk menghitung duplikasi
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_PLANT_RENCANA_KIRIM 
                WHERE ORG = :ORG 
                    AND PLANT_ASAL = :PLANT_PENGIRIM 
                    AND PLANT_TUJUAN = :PLANT_TUJUAN
                    AND DELETE_MARK = 0";

    $query_count = oci_parse($conn, $sql_count);
    oci_bind_by_name($query_count, ':ORG', $ORG);
    oci_bind_by_name($query_count, ':PLANT_PENGIRIM', $PLANT_PENGIRIM);
    oci_bind_by_name($query_count, ':PLANT_TUJUAN', $PLANT_TUJUAN);

    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    if ($result) {
        // Jika ada duplikasi, kembalikan true
        return true;
    }
    // Jika tidak ada duplikasi, kembalikan false
    return false;
}

function upload($conn, $ORG, $PLANT_PENGIRIM, $PLANT_TUJUAN, $CREATED_BY) {
    $sqlcek = "INSERT INTO MAPPING_PLANT_RENCANA_KIRIM 
    (ORG, PLANT_ASAL, PLANT_TUJUAN, CREATED_AT, CREATED_BY, DELETE_MARK) 
    VALUES (
        '".$ORG."', 
        '".$PLANT_PENGIRIM."', 
        '".$PLANT_TUJUAN."', 
        SYSDATE, 
        '".$CREATED_BY."', 
        '0'
    )";

    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result) {
        return true;
    } else {
        // Tangkap error dan tampilkan jika terjadi masalah
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function adjustRowNumber($num) {
    return $num - 1;
}


?>
