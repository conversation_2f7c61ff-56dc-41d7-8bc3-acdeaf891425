<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$distributor = sprintf("%010s", htmlspecialchars($_REQUEST['soldto']));

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 1;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[1])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, sprintf("%010s", $row[1]))) {
                        $messageRows['database'][] = $rowNumber;
                        continue;
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (insert($conn, sprintf("%010s", $row[1]), $created_by)) {
                        $messageRows['success'][] = $rowNumber;
                    } else {
                        $messageRows['system'][] = $rowNumber;
                    }
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;        
        case 'show' :
        {
            displayData($conn);
        }
        break;
        case 'add':
        {
            if (checkDuplicateData($conn,$distributor)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn,$distributor, $created_by)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            if (checkDuplicateData($conn,$distributor)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            }else {
                $sqlcek= "UPDATE MAPPING_SOLDTO_PLAFON set SOLD_TO = '$distributor', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
                $querycek= oci_parse($conn, $sqlcek);
                $return=oci_execute($querycek);
                if ($return){
                    echo json_encode(array('success'=>true,'info'=>"Edit data success"));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "UPDATE MAPPING_SOLDTO_PLAFON set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'multipleDel' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID'];          
                $sql = "UPDATE MAPPING_SOLDTO_PLAFON set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
    }
}

function displayData($conn){
    $org = $_SESSION['user_org'];
    if($conn){
        $soldtoName = array();
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
        }

        $fce = $sap->NewFunction ("Z_ZCSD_DIST");
        if ($fce == false ) {
            $sap->PrintStatus();
            exit;
        }

        $fce->T_KTOKD->row["SIGN"] = 'I';
        $fce->T_KTOKD->row["OPTION"] = 'EQ';
        $fce->T_KTOKD->row["LOW"] = 'ZSG1';
        $fce->T_KTOKD->row["HIGH"] = '';
        $fce->T_KTOKD->Append($fce->T_KTOKD->row);
        
        $fce->T_KTOKD->row["SIGN"] = 'I';
        $fce->T_KTOKD->row["OPTION"] = 'EQ';
        $fce->T_KTOKD->row["LOW"] = 'ZSP1';
        $fce->T_KTOKD->row["HIGH"] = '';
        $fce->T_KTOKD->Append($fce->T_KTOKD->row);
        
        $fce->T_KTOKD->row["SIGN"] = 'I';
        $fce->T_KTOKD->row["OPTION"] = 'EQ';
        $fce->T_KTOKD->row["LOW"] = 'ZST1';
        $fce->T_KTOKD->row["HIGH"] = '';
        $fce->T_KTOKD->Append($fce->T_KTOKD->row);
        
        $fce->T_KTOKD->row["SIGN"] = 'I';
        $fce->T_KTOKD->row["OPTION"] = 'EQ';
        $fce->T_KTOKD->row["LOW"] = 'ZTRA';
        $fce->T_KTOKD->row["HIGH"] = '';
        $fce->T_KTOKD->Append($fce->T_KTOKD->row);

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {		
            $fce->RETURN_DATA->Reset();
            while ( $fce->RETURN_DATA->Next() ){
                $soldtoName[$fce->RETURN_DATA->row["KUNNR"]]= $fce->RETURN_DATA->row["NAME1"];
            }
        } else
        $fce->PrintStatus();
        
        $fce->Close();	
        $sap->Close();	
        
        $sql1 = "SELECT
                    msp.*
                FROM
                    MAPPING_SOLDTO_PLAFON msp
                WHERE
                    msp.DEL_MARK = '0'
                ORDER BY
                    msp.SOLD_TO";
            // echo $sql1;
            
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['SOLD_TO'] = $row['SOLD_TO'];
            $result[$i]['SOLD_TO_NAME'] = isset($soldtoName[$row['SOLD_TO']]) ? $soldtoName[$row['SOLD_TO']] : "";
            $result[$i]['CREATED_AT'] = $row['CREATED_AT'] == null ? '-' : $row['CREATED_AT'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT'] == null ? '-' : $row['UPDATED_AT'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn,$distributor, $created_by){
    $sqlcek= "INSERT INTO MAPPING_SOLDTO_PLAFON (SOLD_TO, created_at, created_by, DEL_MARK) values ('".$distributor."',  SYSDATE, '".$created_by."', '0')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn,$distributor) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_SOLDTO_PLAFON
                WHERE 
                    SOLD_TO = '$distributor'
                    AND DEL_MARK = '0'
                ";
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function adjustRowNumber($num) {
    return $num - 2;
}



?>
