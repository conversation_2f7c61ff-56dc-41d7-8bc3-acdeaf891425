<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

//$ids = htmlspecialchars($_REQUEST['id']);
$ID_NOTAX = htmlspecialchars($_REQUEST['ID']);
$KD_SOLD_TO = htmlspecialchars($_REQUEST['SOLDTO']);
$KD_SHIPTO = htmlspecialchars($_REQUEST['SHIPTO']);
$DEL = htmlspecialchars($_REQUEST['DELETE_MARK']);
/*echo "order : ".$order.'<br>';
echo "sort : ".$sort.'<br>';*/

if(isset ($aksi)){
//    if($aksi == 'show'){
//        displayData($conn,$sort,$order);
//        
//    }
    switch($aksi) { 
        case 'show' :
            {
            $sql = "
            SELECT
            *
            FROM
            TAX_OPCO_MD
			ORDER BY ID DESC";
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $result=array();
            $i=0;
            while($row=oci_fetch_array($query)){
                $result[$i]['ID'] = $row['ID'];
                $result[$i]['SOLDTO'] = $row['SOLDTO'];
                $result[$i]['SHIPTO'] = $row['SHIPTO'];
                $result[$i]['DELETE_MARK'] = $row['DELETE_MARK'];
                $i++;
            }
                echo json_encode($result);
            }
        break;
        case 'add' :
            {
                $sql = "INSERT INTO TAX_OPCO_MD (SOLDTO, SHIPTO, DELETE_MARK)
                        VALUES (
                            '".$KD_SOLD_TO."',
                            '".$KD_SHIPTO."',
                            '".$DEL."'
                        )";

                $query = oci_parse($conn, $sql);
                $result = oci_execute($query);
                
				//echo $sql;
				 
                if ($result){
                    echo json_encode(array('success'=>true));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }                
            }
        break;
        case 'delete' :
        {
           
               $sql = "DELETE FROM TAX_OPCO_MD where
                        ID   = '$ID_NOTAX'
                        ";

               $query = oci_parse($conn, $sql);
               $result = oci_execute($query);
                echo $sql;
               if ($result){
                    echo json_encode(array('success'=>true));
               } else {
                    echo json_encode(array('errorMsg'=>$sql));
               }
           
           
        }
        break;
        case 'update' :
        {
            $sql= "
            UPDATE TAX_OPCO_MD  set
            SOLDTO   = '$KD_SOLD_TO',
            SHIPTO   = '$KD_SHIPTO',
            DELETE_MARK   = '$DEL'
                       
            where
            ID   = '$ID_NOTAX'";
            
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);
            
            //echo $sql;
            
            if ($result){
                    echo json_encode(array('success'=>true));
            } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
    }
    

    
    
}


?>
