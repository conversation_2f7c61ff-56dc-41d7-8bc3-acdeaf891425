<?
session_start();
function Terbilang($x)
{
  $abil = array("", "satu", "dua", "tiga", "empat", "lima", "enam", "tujuh", "delapan", "sembilan", "sepuluh", "sebelas");
  if ($x < 12)
    return " " . $abil[$x];
  elseif ($x < 20)
    return Terbilang($x - 10) . " belas";
  elseif ($x < 100)
    return Terbilang($x / 10) . " puluh" . Terbilang($x % 10);
  elseif ($x < 200)
    return " seratus" . Terbilang($x - 100);
  elseif ($x < 1000)
    return Terbilang($x / 100) . " ratus" . Terbilang($x % 100);
  elseif ($x < 2000)
    return " seribu" . Terbilang($x - 1000);
  elseif ($x < 1000000)
    return Terbilang($x / 1000) . " ribu" . Terbilang($x % 1000);
  elseif ($x < 1000000000)
    return Terbilang($x / 1000000) . " juta" . Terbilang($x % 1000000);
  elseif ($x < 1000000000000)
    return Terbilang($x / 1000000000) . " Milyar" . Terbilang(fmod($x ,1000000000));
}

include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=@$fungsi->ex_koneksi();
$user_id=$_SESSION['user_id'];
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$halaman_id=1253;//prod
//$halaman_id=1332;//dev
/*if (@$fungsi->keamanan($halaman_id,$user_id)==0) {
?>
    <SCRIPT LANGUAGE="JavaScript">
    
            alert("Anda tidak berhak meng akses! \n Login Dahulu...");
    //
    </SCRIPT>
    <a href="../index.php">Login....</a>
<?
exit();
}*/
$currentPage="print_invoice.php";
$no_invoice= $_REQUEST['no_invoice'];
$tanggal_cetak= date('d-m-Y');

$sql_test= "SELECT NO_TAGIHAN FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' and VEHICLE_TYPE!='205' GROUP BY NO_TAGIHAN ";
$query_test= oci_parse($conn, $sql_test);
oci_execute($query_test);
$row_test=oci_fetch_array($query_test);
$no_tagihan_test=$row_test[NO_TAGIHAN];
$adanotagihan = false;

if ($no_tagihan_test != ""){
	$sql_test= "SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_TAGIHAN = '$no_tagihan_test' GROUP BY NO_INVOICE ORDER BY NO_INVOICE ASC ";
	$query_test= oci_parse($conn, $sql_test);
	oci_execute($query_test);
	while($row_test=oci_fetch_array($query_test)){
            $no_invoice_ke[]=$row_test[NO_INVOICE];
	}
	$no_inv_ke1 = $no_invoice_ke[0];
	$no_inv_ke2 = $no_invoice_ke[1];
        if(count($no_invoice_ke)>1){
            $adanotagihan= true;
        }
}else{
	$no_inv_ke1 = $no_invoice;
}


//liyantanto
$p1mna=0;
$sql_pjaknew="
select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
    select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
    (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_inv_ke1'
    group by NO_INVOICE)
    order by TGL_PAJAK_EX desc
) where rownum =1
";
$querycek= oci_parse($conn, $sql_pjaknew);
oci_execute($querycek);
$row_datap=oci_fetch_assoc($querycek);unset($tglfakturpajak);
$tglfakturpajak=$row_datap[TGL_PAJAK_EXF];
$VENDORpj=$row_datap[NO_VENDOR];
if($VENDORpj!='0000410082'){
    if($tglfakturpajak!='' && $tglfakturpajak>='20140901'){
        $p1mna=1;
    }
}
                
$sql= "SELECT A.*, B.CREATE_BY, B.PIC_GUDANG FROM (SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF 
FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_inv_ke1'
ORDER BY PLANT,SAL_DISTRIK, KODE_KECAMATAN, KODE_PRODUK ,TANGGAL_KIRIM ASC ) A LEFT JOIN
(SELECT m1.NO_SPJ, m1.CREATE_BY, m1.PIC_GUDANG
FROM EX_INPUTCLAIM_SEMEN m1 LEFT JOIN EX_INPUTCLAIM_SEMEN m2
 ON (m1.NO_SPJ = m2.NO_SPJ AND m1.id < m2.id)
WHERE m2.id IS NULL and m1.DELETE_MARK = '0' AND m1.STATUS = 'ELOG') B ON (A.NO_SHP_TRN = B.NO_SPJ)";//ORDER BY PLANT,SAL_DISTRIK, SOLD_TO, KODE_PRODUK,TANGGAL_KIRIM ASC
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query= oci_parse($conn, $sql);
	oci_execute($query);
echo $sql;
	while($row=oci_fetch_array($query)){
		$no_invoice_v[]=$row[NO_INVOICE];
                $tgl_invoice_v[]=$row[TANGGAL_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v[]=$row[PLANT]; 
		$nama_plant_v[]=$row[NAMA_PLANT]; 
		$warna_plat_v=$row[WARNA_PLAT];
                $tipe_trukd=$row[VEHICLE_TYPE];                
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR];
                $TANGGAL_KIRIMFgg=trim($row[TANGGAL_KIRIMF]);
                $nmmkoqq=$row[ORG];
                $tahunKIRIMFgg=substr(trim($row[TANGGAL_KIRIMF]),0,4);
                $tgl_kirim_sort[]=$row[TANGGAL_KIRIM1];
                //perubahan untuk costum kereta dijadikan plat hitam
                if($tipe_trukd=='205' && $vendor_v=='0000410095' ){
                    $warna_plat_v='HITAM';
                }
		
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$um_rez=$row[UM_REZ];
		if($um_rez > 1)
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ]/1000;
		else
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  		
		$kel=$row[KELOMPOK_TRANSAKSI];  		
		$inco=$row[INCO];  		
		$nama_kapal=$row[NAMA_KAPAL];  		
		$kode_kecamatan[]=$row[KODE_KECAMATAN];
                $tipe_truk[]=$row[VEHICLE_TYPE];
                if($row[PIC_GUDANG] != ''){
                    $createby[]=$row[PIC_GUDANG];
                }else{
                    if($row[CREATE_BY] != ''){
                        $createby[]=$row[CREATE_BY];
                    } else{
                        $createby[]=$vendor;
                    }
                }
                
                //Penyederhanaan Lap.OA @t 6 Feb 2012
                $item_no = trim($row[KODE_PRODUK]);                
                #Klaim Kantong
                $arr_klaim_kantong[$item_no]+=($row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]);  
                #Klaim Semen
		$arr_klaim_semen[$item_no]+=($row[TOTAL_SEMEN_RUSAK]+$row[PDPKS]);                
                $arr_nama_material[$item_no] = $row[NAMA_PRODUK];
                
                #Qty.
                $arr_qty_klaim_kantong[$item_no]+=$row[QTY_KTG_RUSAK];
                $arr_qty_klaim_semen[$item_no]+=$row[QTY_SEMEN_RUSAK];                
	}
	$total=count($shp_trn_v);

//if($kel == "LAUT" and $inco !="FOB" and $no_tagihan_test != ""){
if ($no_tagihan_test != ""){
    $sql25= "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
    to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,
    to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' 
    AND NO_TAGIHAN = '$no_tagihan_test' AND ( NO_INVOICE = '$no_inv_ke2' OR NO_INVOICE IS NULL ) 
    ORDER BY PLANT,SAL_DISTRIK, KODE_KECAMATAN, KODE_PRODUK ,TANGGAL_KIRIM ASC"; 
//ORDER BY PLANT,SAL_DISTRIK, SOLD_TO, KODE_PRODUK,TANGGAL_KIRIM ASC
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query25= oci_parse($conn, $sql25);
	oci_execute($query25);

	while($row25=oci_fetch_array($query25)){
		$no_invoice_v25[]=$row25[NO_INVOICE];
		$no_invoice_ex_v25=$row25[NO_INV_VENDOR];
		$spj_v25[]=$row25[NO_SHP_TRN];
		$tgl_kirim_v25[]=$row25[TANGGAL_KIRIM];
		$tgl_datang_v25[]=$row25[TANGGAL_DATANG1];
		$tgl_bongkar_v25[]=$row25[TANGGAL_BONGKAR1];
		$produk_v25[]=$row25[KODE_PRODUK];
		$nama_produk_v25[]=$row25[NAMA_PRODUK];
		$shp_trn_v25[]=$row25[NO_SHP_TRN];
		$plant_v25[]=$row25[PLANT]; 
		$nama_plant_v25[]=$row25[NAMA_PLANT]; 
		$warna_plat_v25=$row25[WARNA_PLAT]; 
		$nama_vendor_v25=$row25[NAMA_VENDOR]; 
		$vendor_v25=$row25[VENDOR]; 
                $tipe_trukd25=$row25[VEHICLE_TYPE];  
                $TANGGAL_KIRIMFgg25=trim($row25[TANGGAL_KIRIMF]);
                 if($tipe_trukd25=='205' && $vendor_v25=='0000410095'){
                    $warna_plat_v25='HITAM';
                }
		$sal_dis_v25[]=$row25[SAL_DISTRIK]; 
		$nama_sal_dis_v25[]=$row25[NAMA_SAL_DIS]; 
		$sold_to_v25[]=$row25[SOLD_TO];
		$nama_sold_to_v25[]=$row25[NAMA_SOLD_TO];
		$ship_to_v25[]=$row25[SHIP_TO];
		$qty_v25[]=$row25[QTY_SHP];
		$um_rez25=$row25[UM_REZ];
		if($um_rez > 1)
		$qty_v25_ton[]=$row25[QTY_SHP]*$row25[UM_REZ]/1000;
		else
		$qty_v25_ton[]=$row25[QTY_SHP]*$row25[UM_REZ];

		$qty_kantong_rusak_v25[]=$row25[QTY_KTG_RUSAK];
                $qty_semen_rusak_v25[]=$row25[QTY_SEMEN_RUSAK];
		$id_v25[]=$row25[ID];  
		$no_pol_v25[]=$row25[NO_POL];  
		$shp_cost_v25[]=$row25[SHP_COST];  
		$total_klaim_all_v25[]=$row25[TOTAL_KLAIM_ALL];  
		$no_pajak_ex25=$row25[NO_PAJAK_EX];  		
		$kel25=$row25[KELOMPOK_TRANSAKSI];  		
		$inco25=$row25[INCO];  		
		$kode_kecamatan25[]=$row25[KODE_KECAMATAN];  
	}
	$total25=count($shp_trn_v25);
}
		$nmkali="KERJA SAMA OPERASI SEMEN GRESIK - SEMEN INDONESIA ";
        if(($tahunKIRIMFgg!='2017' && $nmmkoqq=='7000')||($tahunKIRIMFgg!='2017' && $nmmkoqq=='7900')){            
            $nmkali="PT SEMEN INDONESIA (PERSERO) Tbk ";
        } else if ($nmmkoqq=='5000'){
            $nmkali="PT. SEMEN GRESIK ";
        }else if ($nmmkoqq=='4000'){
            $nmkali="PT. SEMEN TONASA ";
        }
//        if($_SESSION['user_name'] != 'ACR7496111'){
//            $nama_vendor_v = $_SESSION['nama_lengkap'];
//        }
		$ygdiprint = false;



		//DENDA K3 
		$sqlK3 = "SELECT SUM(KREDIT) as TOTAL_DENDA FROM EX_DENDAK3_SALDO WHERE NO_INVOICE LIKE '$no_invoice' AND DELETE_AT IS NULL";
		$queryK3= oci_parse($conn, $sqlK3);
		oci_execute($queryK3);

		$rowK3 = oci_fetch_array($queryK3);
		$total_denda = floatval($rowK3['TOTAL_DENDA']);
                
                //Start POEX
                $sqlPOEX = "SELECT SUM(NILAI_TRANSAKSI) AS JUMLAH FROM M_POTONGAN_OA_TRANS WHERE NO_INVOICE LIKE '$no_invoice' AND IS_DELETE ='0'";
                $queryPOEX= oci_parse($conn, $sqlPOEX);
                oci_execute($queryPOEX);
                $rowPOEX = oci_fetch_array($queryPOEX);
		$total_nilaiPoex = floatval($rowPOEX['JUMLAH']);
                //End POEX

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>print invoice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
<!--
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}
.style2 {font-family: Arial, Helvetica, sans-serif; font-size: 12; }
.style3 {font-size: 12}
-->
</style>
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
</head>

<body>
<?php 
	$sqlnorek = "SELECT NO_REKENING,BANK,NO_KWITANSI,TGL_PAJAK_EX,to_char(TGL_PAJAK_EX,'DD-MM-YYYY') as TGL_PAJAK_EX1, to_char(TGL_INVOICE,'YYYYMMDD') as TGL_INVOICE FROM EX_INVOICE WHERE NO_INVOICE = '".$no_invoice."'";
	$qnorek = oci_parse($conn, $sqlnorek);
	oci_execute($qnorek);
	$datarek = oci_fetch_assoc($qnorek);

	$sqldirut = "select NAMA_DIRUT FROM EX_INVOICE_DIRUT_VENDOR WHERE KODE_VENDOR='".$vendor."'";
	$qdirut = oci_parse($conn, $sqldirut);
	oci_execute($qdirut);
	$datadirut = oci_fetch_assoc($qdirut);
	$tgl_invoice=$datarek[TGL_INVOICE];
	
	//echo $kel;
	//echo $vendor_v;
	?>
<span class="style1"></span>
<div align="center"></div>
<table width="900">
	<tr>
		<td colspan="3" align="center"><h3>KWITANSI<br><? echo $nama_vendor_v ;?></h3><hr></td>
	</tr>
	<tr>
		<td>Nomor Kwitansi </td>
		<td>:</td>
		<td><?php echo $datarek['NO_KWITANSI']; ?></td>
	</tr>
	<tr>
		<td>Telah Terima Dari </td>
		<td>:</td>
		<td><?=$nmkali;?></td>
	</tr>
	<tr><td colspan="3" height="15"></td></tr>
	<tr>
		<td>Sejumlah Uang </td>
		<td>:</td>
		<td><span id="terbilang"></span></td>
	</tr>
	<tr>
		<td> </td>
		<td></td>
		<td>
			<table style="margin-top:25px;" width="500">
				<?php 
				
					if ($kel == 'LAUT') {
                                            if($vendor_v == "0000410046" || $vendor_v == "0000410003" || $vendor_v == "0000410000" ){
                                                $persenppn = '1,1%';
                                            } else {
                                                $persenppn = '1%';
                                            }
						
						if ($adanotagihan) {
							if ($no_invoice == $no_inv_ke1) {
								$persen = '75%';
							}else if($no_invoice == @$no_inv_ke2){
								$persen = '25% Dari Invoice '.$no_inv_ke1;
								$ygdiprint = true;
							}else{
								$persen = '100%';
							}
						}else{
							$persen = '100%';
						}
					}else{
						$persen = '';
                                                if($tgl_invoice < 20220401){
                                                    $persenppn = '10%';
                                                } elseif($tgl_invoice>=20241101){
                                                    $persenppn = '12%';
                                                } else  {
                                                    $persenppn = '11%';
                                                }
						
					}
					
				?>
				<tr>
					<td width="100" colspan="2">Ongkos Angkut <?php echo $persen; ?> <!-- <span id="namaproduk"></span> --></td>
					<td align="right"><span id="grandtotal"></span></td>
				</tr>
				<?	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ ?>
				<tr>
				<?php if ($no_invoice == $no_inv_ke1){ ?>
					<td colspan="2">PPN <?php echo $persenppn; ?> <?php echo $no_pajak_ex;  ?></td>
					<?php }else if($no_invoice == $no_inv_ke2){ ?>
					<td colspan="2">PPN <?php echo $persenppn; ?> <?php echo $no_pajak_ex25;  ?></td>
					<?php } ?>
					<td align="right"><span id="grandpajak"></span></td>
				</tr>
				<? } ?>
				<tr><td colspan="3"><hr></td></tr>
				<tr>
					<td colspan="3" align="right"><span id="grandtotalgrand"></span></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td>Jumlah</td>
		<td>:</td>
		<td><span style="font-weight: bold;font-size: 22px;" id="grandtotalgrand2"></span></td>
	</tr>
</table>
<br><br><br><br>
<table width="900">
	<tr>
		<td></td>

		<? if(strtotime($tgl_invoice_v[0])<=strtotime('13-JAN-17')){?>
			<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$tgl_invoice_v[0];?><br><? echo $nama_vendor_v ;?></td>
	    <? } else if(strtotime($tgl_invoice_v[0])<=strtotime('27-MAR-17') && strtotime($tgl_invoice_v[0])>strtotime('13-JAN-17') && $kel == 'DARAT'){ ?>
	    	<?php
		            rsort($tgl_kirim_sort);
		            $date=date_create($tgl_kirim_sort[0]);
		        ?>
		        	<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=strtoupper(date_format($date,"d-M-Y"));?><br><? echo $nama_vendor_v ;?></td>
	    <? } else{ ?>
			<?php //if ($kel == 'LAUT'): ?>
				<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$datarek['TGL_PAJAK_EX'];?><br><? echo $nama_vendor_v ;?></td>
			<?php //else: ?>
				<?php
		            // rsort($tgl_kirim_sort);
		            // $date=date_create($tgl_kirim_sort[0]);
		        ?>
		        	<!-- <td align="center" class="style2"> <? //$fungsi->ex_cari_vendor($vendor_v)?>, <? //strtoupper(date_format($date,"d-M-Y"));?><br><? //echo $nama_vendor_v ;?></td> -->
			<?php //endif ?>
		<?php } ?>
	</tr>
	<tr><td colspan="2" height="150"></td></tr>
	<tr>
		<td>TRANSFER<br><?php echo $datarek['BANK']; ?>&nbsp;<?php echo $datarek['NO_REKENING']; ?><br>A/N&nbsp;<? echo $nama_vendor_v ;?></td>
		<td align="center"><?php echo @$datadirut['NAMA_DIRUT']; ?></td>
	</tr>
</table>
<br>
<hr>
<br>
<table width="900" align="center" >
<tr>
<td colspan="12" ><div align="center" class="style2"><?=$nmkali;?></div></td>
</tr>
<tr >
  <td colspan="12" ><div align="center" class="style2">TAGIHAN ONGKOS ANGKUT SEMEN  </div></td>
</tr>
<tr >
  <td colspan="14" style="border-bottom:1px #000000 solid;">
  <table width="890">		
  	<tr class="style2">
		<td> Expeditur</td>
		<td><span class="style2"> : </span></td>
		<td> <span class="style2"><? echo $vendor_v."  ".$nama_vendor_v ;?></span> </td>
		<td><span class="style2"></span> </td>
		<? if ($kel!="LAUT" or ($inco == "FOB")){?>
		<td> <span class="style2"><? echo "PLAT ".$warna_plat_v;?></span> </td>
		<? }else{?>
		<td><span class="style2"></span> </td>
		<? }?>
		<td><span class="style2"></span> </td>
		<td><span class="style2"></span> </td>
  	</tr>

  	<tr class="style2">
		<td> No Tagihan</td>
		<td><span class="style2"> : </span></td>
		<td> <span class="style2">
		  <? echo $no_invoice ." / ".$no_invoice_ex_v//cek?>
		</span> </td>
		<td><span class="style2"></span> </td>
		<td>  
	      </td>
		<td> <span class="style2">
		  
		</span> </td>
		<td><span class="style2"></span> </td>
  	</tr>
  </table>  </td>
</tr  >
<tr>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TGL SPJ</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> AREA LT</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO SPJ</span></td>
        <? if(strtotime($tgl_invoice_v[0])>=strtotime('01-MAR-18')){ ?>
        <td style=" border-bottom:1px #000000 solid;"><span class="style2"> APPROVED BY</span></td>
        <? } ?>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO. POL</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> PRODUK</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> DISTRIBUTOR</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> K.KTG</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> K.SMN</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> QTY </span></td>
        <td style=" border-bottom:1px #000000 solid;"><span class="style2" widht="4">TRUK</<span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TARIF</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> JUMLAH</span></td>        
</tr>
<?
$b=0;
$tot_ktg =0;
$tot_smn =0;
$tot_qty =0;
$tot_qty_ton =0;

$sub_ktg_plant =0;
$sub_smn_plant =0;
$sub_qty_plant =0;
$sub_qty_plant_ton =0;

$sub_ktg_sold =0;
$sub_smn_sold =0;
$sub_qty_sold =0;
$sub_qty_sold_ton =0;

$sub_tot_jumlah =0;
$grand_tot_jumlah =0;


for($i=0; $i<$total;$i++) { 
	$b +=1;
	if ($i==0){
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "PENGIRIMAN DARI       ". $plant_v[$i] . "         ".$nama_plant_v[$i];?></strong></span></td>
  </tr>
	<? 
	} 
	if ($i >0 and $plant_v[$i] != $plant_v[$i-1]){
	$sub_ktg =0;
	$sub_smn =0;
	$sub_qty =0;
	$sub_qty_ton =0;
	$sub_tot_jumlah=0;

	$sub_ktg_plant =0;
	$sub_smn_plant =0;
	$sub_qty_plant =0;
	$sub_qty_plant_ton =0;
	$sub_tot_jumlah_plant=0;

	$tot_ktg_sold =0;
	$tot_smn_sold =0;
	$tot_qty_sold =0;
	$tot_qty_sold_ton =0;
	$sub_tot_jumlah_sold=0;

	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "PENGIRIMAN DARI       ". $plant_v[$i] . "         ".$nama_plant_v[$i];?></strong></span></td>
	</tr>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v[$i] . "         ".$nama_sal_dis_v[$i];?></strong></span></td>
	</tr>
<!--	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "DISTRIBUTOR       ". $sold_to_v[$i] . "         ".$nama_sold_to_v[$i];?></strong></span></td>
	</tr> -->

	<?
	}
	if ($i==0){
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v[$i] . "         ".$nama_sal_dis_v[$i];?></strong></span></td>
  <!--</tr>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "DISTRIBUTOR       ". $sold_to_v[$i] . "         ".$nama_sold_to_v[$i];?></strong></span></td>
  </tr>-->
	<? 
	} 
	if ($i >0 and $sal_dis_v[$i] != $sal_dis_v[$i-1] and $plant_v[$i] == $plant_v[$i-1]){
	$sub_ktg =0;
	$sub_smn =0;
	$sub_qty =0;
	$sub_qty_ton =0;
	$sub_tot_jumlah=0;

	$tot_ktg_sold =0;
	$tot_smn_sold =0;
	$tot_qty_sold =0;
	$tot_qty_sold_ton =0;
	$sub_tot_jumlah_sold=0;
	
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v[$i] . "         ".$nama_sal_dis_v[$i];?></strong></span></td>
	</tr>
<!--<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "DISTRIBUTOR       ". $sold_to_v[$i] . "         ".$nama_sold_to_v[$i];?></strong></span></td>
  </tr>-->

	<?
	}
	if ($i >0 and $sold_to_v[$i] != $sold_to_v[$i-1] and $sal_dis_v[$i] == $sal_dis_v[$i-1]){
	$tot_ktg_sold =0;
	$tot_smn_sold =0;
	$tot_qty_sold =0;
	$tot_qty_sold_ton =0;
	$sub_tot_jumlah_sold=0;
	?>
<!--<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "DISTRIBUTOR       ". $sold_to_v[$i] . "         ".$nama_sold_to_v[$i];?></strong></span></td>
  </tr>-->
	<?
	}


$sub_ktg +=$qty_kantong_rusak_v[$i];
$sub_smn +=$qty_semen_rusak_v[$i];
$sub_qty +=$qty_v[$i];
$sub_qty_ton +=$qty_ton_v[$i];

$tot_ktg_sold +=$qty_kantong_rusak_v[$i];
$tot_smn_sold +=$qty_semen_rusak_v[$i];
$tot_qty_sold +=$qty_v[$i];
$tot_qty_sold_ton +=$qty_ton_v[$i];

$sub_tot_jumlah_sold +=$shp_cost_v[$i];
$sub_tot_jumlah +=$shp_cost_v[$i];
$grand_tot_jumlah +=$shp_cost_v[$i];

$sub_ktg_plant +=$qty_kantong_rusak_v[$i];
$sub_smn_plant +=$qty_semen_rusak_v[$i];
$sub_qty_plant +=$qty_v[$i];
$sub_qty_plant_ton +=$qty_ton_v[$i];
$sub_tot_jumlah_plant +=$shp_cost_v[$i];

$tot_ktg += $qty_kantong_rusak_v[$i];
$tot_smn += $qty_semen_rusak_v[$i];
$tot_qty += $qty_v[$i];
$tot_qty_ton +=	$qty_ton_v[$i];


?>
<tr>
	<td> <span class="style2">
	  <?=$b?>
	</span></td>
	<td> <span class="style2">
	  <?=$tgl_kirim_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$kode_kecamatan[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$spj_v[$i]?>
	</span></td>
        <? if(strtotime($tgl_invoice_v[0])>=strtotime('01-MAR-18')){ ?>
        <td align="center"> <span class="style2">
	  <?=$createby[$i]?>
	</span></td>
        <? } ?>
	<td> <span class="style2">
	  <?=$no_pol_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <? echo trim($nama_produk_v[$i],"SEMEN");?>
	</span></td>
	<td> <span class="style2">
	  <? echo substr($nama_sold_to_v[$i],0,20);?>
	</span></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_kantong_rusak_v[$i],0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_semen_rusak_v[$i],0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_v[$i],0,",",".")?>
    </div></td>
        <td> <div align="center" class="style2">
	  <?=$tipe_truk[$i]?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v[$i]/$qty_v[$i],2,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v[$i],2,",",".")?>
    </div></td>
        
</tr>
<script type="text/javascript">
	var gid = document.getElementById('namaproduk');
	gid.textContent = '<?=$nama_produk_v[$i]?>';
</script>

<?
//and $sal_dis_v[$i] == $sal_dis_v[$i+1]
	if ($total >0 and $sold_to_v[$i] != $sold_to_v[$i+1] and $i+1 != $total and $sal_dis_v[$i] == $sal_dis_v[$i+1] ){
       /*
        <tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Distributor </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_ktg_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_smn_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_qty_sold,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($tot_qty_sold_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah_sold,2,",",".")?>
	    </div></td>
	</tr>
        */
?>
	


<? 
	}

	if ($total >0 and $sal_dis_v[$i] != $sal_dis_v[$i+1] and $i+1 != $total and $plant_v[$i] == $plant_v[$i+1]){

        /*
        <tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Distributor </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_ktg_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_smn_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_qty_sold,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($tot_qty_sold_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah_sold,2,",",".")?>
	    </div></td>
	</tr>
        */
?>

	

	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Area </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty,0,",",".")?>
	    </div></td>
                <td style="border-top:1px #000000 solid;"></td>
		<td><span class="style3">
		  <?=number_format($sub_qty_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah,2,",",".")?>
	    </div></td>
	</tr>
	
	
<? 
	}

	if ($total >0 and $plant_v[$i] != $plant_v[$i+1]){
/*
 <tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Distributor </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_ktg_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_smn_sold,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($tot_qty_sold,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($tot_qty_sold_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah_sold,2,",",".")?>
	    </div></td>
	</tr>
*/
        
?>
	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Area </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty,0,",",".")?>
	    </div></td>
                <td style="border-top:1px #000000 solid;"></td>
		<td><span class="style3">
		  <?=number_format($sub_qty_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah,2,",",".")?>
	    </div></td>
	</tr>

	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Plant </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg_plant,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn_plant,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty_plant,0,",",".")?>
	    </div></td>
                <td style="border-top:1px #000000 solid;"></td>
		<td><span class="style3">
		  <?=number_format($sub_qty_plant_ton,2,",",".")?>  &nbsp;TON			
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah_plant,2,",",".")?>
	    </div></td>
	</tr>


<? 
	}

} 
?>
<tr>
	<td colspan="7"><div align="right" class="style2">Grand Total</div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_ktg,0,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_smn,0,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_qty,0,",",".")?>
    </div></td>
        <td style="border-top:1px #000000 solid;"></td>
	<td><span class="style3">
	  <?=number_format($tot_qty_ton,2,",",".")?> &nbsp;TON			
	</span> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($grand_tot_jumlah,2,",",".")?>
    </div></td>
</tr>
<script type="text/javascript">
	var gid = document.getElementById('grandtotal');
	gid.textContent = '<?=number_format($grand_tot_jumlah,2,",",".")?>';
</script>
<?
$nilai_oa_temp = $grand_tot_jumlah; //tampung nilai OA, belum dikurangi klaim
?>
<?	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ 
?>

<tr>
	<td colspan="4"><span class="style2"> 
		<? 
	  if ($no_invoice == $no_inv_ke1){
		
		echo " No Seri Pajak :"; 
	  	echo $no_pajak_ex; 
		}
		?>
	</span></td>
	<td colspan="2"><span class="style2"> 
	
	  <?
	  if ($no_invoice == $no_inv_ke1){
	   echo " Tanggal : ";
		$sql_tgl= "SELECT to_char(TGL_PAJAK_EX,'DD-MM-YYYY') as TGL_PAJAK_EX1, to_char(TGL_INVOICE,'YYYYMMDD') as TGL_INVOICE FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
		$query_tgl= oci_parse($conn, $sql_tgl);
		oci_execute($query_tgl);
		
		$row_tgl=oci_fetch_array($query_tgl);
		echo $tgl_pajak=$row_tgl[TGL_PAJAK_EX1];
		$tgl_invoice=$row_tgl[TGL_INVOICE];
	  }
	  ?> 
    </span></td>
	<td> <div align="right" class="style2">Jumlah Pajak :</div></td>
	<td colspan="3"> </td>
	<td ><span class="style3"></span> </td> <? 
	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){
            if($p1mna==1){
                if($tgl_invoice < 20220401){
                    $pajak1 = 0.1 * $grand_tot_jumlah;
//                    $pajak = 0.1 * $pajak1;
                    $pajak = $pajak1;
                } elseif($tgl_invoice >= 20241101){
                    if($vendor_v == "0000410046" || $vendor_v == "0000410003" || $vendor_v == "0000410000" ){
                        $pajak1 = 0.012 * $grand_tot_jumlah;
//                        $pajak = 0.011 * $pajak1;
                        $pajak = $pajak1;
                    } else {
                        $pajak1 = 0.12 * $grand_tot_jumlah;
//                        $pajak = 0.11 * $pajak1;
                        $pajak = $pajak1;
                    }
                } else  {
                    if($vendor_v == "0000410046" || $vendor_v == "0000410003" || $vendor_v == "0000410000" ){
                        $pajak1 = 0.011 * $grand_tot_jumlah;
//                        $pajak = 0.011 * $pajak1;
                        $pajak = $pajak1;
                    } else {
                        $pajak1 = 0.11 * $grand_tot_jumlah;
//                        $pajak = 0.11 * $pajak1;
                        $pajak = $pajak1;
                    }
                    
                }
                
            }else{
//                $pajak = 0.1 * $grand_tot_jumlah;
                if($tgl_invoice < 20220401){
                    $pajak = 0.1 * $grand_tot_jumlah;
                } else {
                    $pajak = 0.11 * $grand_tot_jumlah;
                }
            }
	}else $pajak = 0;
	?>
	<td > <div align="right" class="style2">
	  <?=number_format(floor($pajak),2,",",".")?>
    </div></td>
</tr>

<script type="text/javascript">
	var gid = document.getElementById('grandpajak');
	gid.textContent = '<?=number_format(floor($pajak),2,",",".")?>';
</script>
<tr>
	<td colspan="7"> <div align="right" class="style2">Total :</div></td>
	<? $total_all = $grand_tot_jumlah + $pajak;?>
	<td colspan="4"> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format(floor($total_all),2,",",".")?>
    </div></td>
</tr>
<?php 
	$pembilang =number_format($total_all,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			//$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
<script type="text/javascript">
	var gid = document.getElementById('grandtotalgrand');
	gid.textContent = '<?=number_format(floor($total_all),2,",",".")?>';
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format(floor($total_all),2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
</script>
<? }else{ ?>
<?php 
	$pembilang =number_format($grand_tot_jumlah,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			//$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
<script type="text/javascript">
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format(floor($grand_tot_jumlah),2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
</script>
<?	} ?>
<tr><td colspan="12" height="100"> </td>
</tr>
</table>

<p>
<? if ($total25>0){?>
<div align="center">
  <p><strong>
    
    LAMPIRAN TAGIHAN 2</strong></p>
</div>
<table  align="center" width="890"  >
<tr>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TGL SPJ</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> AREA LT</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO SPJ</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO. POL</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> PRODUK</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> DISTRIBUTOR</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> K.KTG</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> K.SMN</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> QTY </span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TARIF</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> JUMLAH</span></td>
</tr>
<?
$b=0;
$tot_ktg25 =0;
$tot_smn25 =0;
$tot_qty25 =0;
$tot_qty25_ton =0;

$sub_ktg25 =0;
$sub_smn25 =0;
$sub_qty25 =0;
$sub_qty25_ton =0;
$sub_tot_jumlah25 =0;
$grand_tot_jumlah25 =0;


for($i=0; $i<$total25;$i++) { 
	$b +=1;
	if ($i==0){
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "PENGIRIMAN DARI       ". $plant_v25[$i] . "         ".$nama_plant_v25[$i];?></strong></span></td>
  </tr>
	<? 
	} 
	if ($i >0 and $plant_v25[$i] != $plant_v25[$i-1]){
	$sub_ktg_plant25 =0;
	$sub_smn_plant25 =0;
	$sub_qty_plant25 =0;
	$sub_qty_plant25_ton =0;
	$sub_tot_jumlah_plant25=0;

	$sub_ktg25 =0;
	$sub_smn25 =0;
	$sub_qty25 =0;
	$sub_qty25_ton =0;
	$sub_tot_jumlah25=0;
	
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "PENGIRIMAN DARI       ". $plant_v25[$i] . "         ".$nama_plant_v25[$i];?></strong></span></td>
	</tr>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v25[$i] . "         ".$nama_sal_dis_v25[$i];?></strong></span></td>
  </tr>

	<?
	}
	if ($i==0){
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v25[$i] . "         ".$nama_sal_dis_v25[$i];?></strong></span></td>
  </tr>
	<? 
	} 
	if ($i >0 and $sal_dis_v25[$i] != $sal_dis_v25[$i-1] and $plant_v25[$i] == $plant_v25[$i+1]){
	$sub_ktg25 =0;
	$sub_smn25 =0;
	$sub_qty25 =0;
	$sub_qty25_ton =0;
	$sub_tot_jumlah25=0;
	
	?>
	<tr>
	  <td colspan="12" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v25[$i] . "         ".$nama_sal_dis_v25[$i];?></strong></span></td>
	</tr>
	<?
	}
$sub_ktg25 +=$qty_kantong_rusak_v25[$i];
$sub_smn25 +=$qty_semen_rusak_v25[$i];
$sub_qty25 +=$qty_v25[$i];
$sub_qty25_ton +=$qty_v25_ton[$i];

$tot_ktg25 +=$qty_kantong_rusak_v25[$i];
$tot_smn25 +=$qty_semen_rusak_v25[$i];
$tot_qty25 +=$qty_v25[$i];
$tot_qty25_ton +=$qty_v25_ton[$i];


$sub_tot_jumlah25 +=$shp_cost_v25[$i];
$grand_tot_jumlah25 +=$shp_cost_v25[$i];

$sub_ktg_plant25 +=$qty_kantong_rusak_v25[$i];
$sub_smn_plant25 +=$qty_semen_rusak_v25[$i];
$sub_qty_plant25 +=$qty_v25[$i];
$sub_qty_plant25_ton +=$qty_v25_ton[$i];
$sub_tot_jumlah_plant25 +=$shp_cost_v25[$i];
echo 'tarif '.$sub_tot_jumlah_plant25;

?>
<tr>
	<td> <span class="style2">
	  <?=$b?>
	</span></td>
	<td> <span class="style2">
	  <?=$tgl_kirim_v25[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$kode_kecamatan25[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$spj_v25[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$no_pol_v25[$i]?>
	</span></td>
	<td> <span class="style2">
	  <? echo trim($nama_produk_v25[$i],"SEMEN");?>
	</span></td>
	<td> <span class="style2">
	  <? echo substr($nama_sold_to_v25[$i],0,20);?>
	</span></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_kantong_rusak_v25[$i],0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_semen_rusak_v25[$i],0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($qty_v25[$i],0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v25[$i]/$qty_v25[$i],2,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v25[$i],2,",",".")?>
    </div></td>
</tr>
<?
	if ($total25 >0 and $sal_dis_v25[$i] != $sal_dis_v25[$i+1] and $i+1 != $total25 and $plant_v25[$i] == $plant_v25[$i+1]){
?>
	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Area </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty25,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($sub_qty25_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah25,2,",",".")?>
	    </div></td>
	</tr>
<? 
	}

	if ($total25 >0 and $plant_v25[$i] != $plant_v25[$i+1]){
?>
	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Area </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty25,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($sub_qty25_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah25,2,",",".")?>
	    </div></td>
	</tr>
	<tr>
		<td colspan="7"><div align="right" class="style2">Sub Total Per Plant </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_ktg_plant25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_smn_plant25,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_qty_plant25,0,",",".")?>
	    </div></td>
		<td><span class="style3">
		  <?=number_format($sub_qty_plant25_ton,2,",",".")?> &nbsp;TON		
		</span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah_plant25,2,",",".")?>
	    </div></td>
	</tr>
<? 
	}

} 
?>
<tr>
	<td colspan="7"><div align="right" class="style2">Grand Total </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_ktg25,0,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_smn25,0,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_qty25,0,",",".")?>
    </div></td>
	<td><span class="style3">
	  <?=number_format($tot_qty25_ton,2,",",".")?> &nbsp;TON		
	</span> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($grand_tot_jumlah25,2,",",".")?>
    </div></td>    
</tr>
<?php if ($ygdiprint): ?>
	<script type="text/javascript">
		var gid = document.getElementById('grandtotal');
		gid.textContent = '<?=number_format($grand_tot_jumlah25,2,",",".")?>';
	</script>
<?php endif ?>
<?	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ 
?>

<tr>
	<td colspan="4"><span class="style2"> 
		<? 
	  if ($no_invoice == $no_inv_ke2){
		
		echo " No Seri Pajak :"; 
	  	echo $no_pajak_ex25; 
		}
		?>
	</span></td>
	<td colspan="2"><span class="style2"> 
	
	  <?
	  if ($no_invoice == $no_inv_ke2){
	   echo " Tanggal : ";
		$sql_tgl= "SELECT to_char(TGL_PAJAK_EX,'DD-MM-YYYY') as TGL_PAJAK_EX1, to_char(TGL_INVOICE,'YYYYMMDD') as TGL_INVOICE FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
		$query_tgl= oci_parse($conn, $sql_tgl);
		oci_execute($query_tgl);
		
		$row_tgl=oci_fetch_array($query_tgl);
		echo $tgl_pajak=$row_tgl[TGL_PAJAK_EX1];
		$tgl_invoice=$row_tgl[TGL_INVOICE];
	  }
	  ?> 
    </span></td>
	<td> <div align="right" class="style2">Jumlah Pajak :</div></td>
	<td colspan="3"> </td>
	<td ><span class="style3"></span> </td> <? 
	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){
            if($p1mna==1){
                if($tgl_invoice < 20220401){
                    $pajak125 = 0.1 * $grand_tot_jumlah25;
//                    $pajak25 = 0.1 * $pajak125;
                    $pajak25 = $pajak125;
                } elseif($tgl_invoice >= 20241101){
                    if($vendor_v == "0000410003"){
                        $pajak125 = 0.012 * $grand_tot_jumlah25;
//                        $pajak25 = 0.011 * $pajak125;
                        $pajak25 = $pajak125;
                    } else {
                        $pajak125 = 0.12 * $grand_tot_jumlah25;
//                        $pajak25 = 0.11 * $pajak125;
                        $pajak25 = 0.12 * $pajak125;
                    }
                } else {
                    if($vendor_v == "0000410003"){
                        $pajak125 = 0.011 * $grand_tot_jumlah25;
//                        $pajak25 = 0.011 * $pajak125;
                        $pajak25 = $pajak125;
                    } else {
                        $pajak125 = 0.11 * $grand_tot_jumlah25;
//                        $pajak25 = 0.11 * $pajak125;
                        $pajak25 = 0.11 * $pajak125;
                    }
                    
                }
                
            }else{
//                $pajak25 = 0.1 * $grand_tot_jumlah25;
                if($tgl_invoice < 20220401){
                    $pajak25 = 0.1 * $grand_tot_jumlah25;
                } elseif($tgl_invoice < 20241101){
                    $pajak25 = 0.12 * $grand_tot_jumlah25;
                } else  {
                    $pajak25 = 0.11 * $grand_tot_jumlah25;
                }
            }
	}else $pajak25 = 0;
	?>
	<td > <div align="right" class="style2">
	  <?=number_format(floor($pajak25),2,",",".")?>
    </div></td>
</tr>
<?php if ($ygdiprint): ?>
<script type="text/javascript">
	var gid = document.getElementById('grandpajak');
	gid.textContent = '<?=number_format(floor($pajak25),2,",",".")?>';
</script>
<?php endif ?>
<tr>
	<td colspan="7"> <div align="right" class="style2">Total :</div></td>
	<? $total_all25 = $grand_tot_jumlah25 + $pajak25;?>
	<td colspan="4"> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format(floor($total_all25),2,",",".")?>
    </div></td>
</tr>
<?php if ($ygdiprint): ?>
<?php 
	$pembilang =number_format($total_all25,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			//$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
<script type="text/javascript">
	var gid = document.getElementById('grandtotalgrand');
	gid.textContent = '<?=number_format(floor($total_all25),2,",",".")?>';
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format(floor($total_all25),2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
</script>
<?php endif ?>
<? }else{ ?>
<?php if ($ygdiprint): ?>
<?php 
	$pembilang =number_format($grand_tot_jumlah25,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			//$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
<script type="text/javascript">
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format(floor($grand_tot_jumlah25),2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
</script>
<?php endif ?>
<?	} ?>

<? } ?>

</table>
<table align="center" width="890">
<tr>
	<td colspan="7"> <div align="left" class="style2">&nbsp;Total Qty:&nbsp;&nbsp;
	  <?=number_format($tot_qty25 + $tot_qty,0,",",".")?> &nbsp;ZAK&nbsp;
	  <?=number_format($tot_qty25_ton + $tot_qty_ton,0,",",".")?> &nbsp;TON
	  </div>
	</td>
	<td colspan="4">	 </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
    </div></td>
</tr>
</table>
<table align="center" width="890">
    <tr>
        <td>
<table align="left">
<?
$total_oa_selisih_klaim = 0;
$o=1;
if(is_array($arr_klaim_kantong)){
        foreach($arr_klaim_kantong as $item_no => $nilai_klaim){
            $judul_='';
            if($o==1) $judul_ = "Klaim Kantong";

            if($nilai_klaim>0){
                ?>
                <tr>
                    <td><div align="left" class="style2"><?=$judul_?></div></td>
                    <td><div align="left" class="style2"><?=$arr_nama_material[$item_no]?></div></td>
                    <td><div align="left" class="style2">&nbsp;&nbsp;<?=$arr_qty_klaim_kantong[$item_no]?></div></td>
                    <td><div align="right" class="style2"><?=number_format($nilai_klaim,2,",",".")?></div></td>
                </tr>
                <? 
                $total_oa_selisih_klaim+=$nilai_klaim;
                $o++;
            }
        }
}
$o=1;
if(is_array($arr_klaim_semen)){
        foreach($arr_klaim_semen as $item_no => $nilai_klaim){
            $judul_='';
            if($o==1) $judul_ = "Klaim Semen";

            if($nilai_klaim>0){
                ?>
                <tr>
                    <td><div align="left" class="style2"><?=$judul_?></div></td>
                    <td><div align="left" class="style2"><?=$arr_nama_material[$item_no]?></div></td>
                    <td><div align="left" class="style2">&nbsp;&nbsp;<?=$arr_qty_klaim_semen[$item_no]?></div></td>
                    <td><div align="right" class="style2"><?=number_format($nilai_klaim,2,",",".")?></div></td>
                </tr>
                <? 
                $total_oa_selisih_klaim+=$nilai_klaim;
                $o++;
            }
        }
}
if($total_denda){
	$total_oa_selisih_klaim += $total_denda;
	?>
	 			<tr>
                    <td><div align="left" class="style2">Denda K3</div></td>
                    <td><div align="left" class="style2">Denda Pelanggaran K3</div></td>
                    <td><div align="left" class="style2">&nbsp;&nbsp;</div></td>
                    <td><div align="right" class="style2"><?=number_format($total_denda,2,",",".")?></div></td>
                </tr>
    <?
}
//Start POEX
if($total_nilaiPoex){
	$total_oa_selisih_klaim += $total_nilaiPoex;
	?>
	 			<tr>
                    <td><div align="left" class="style2">Potongan OA Ekspeditur</div></td>
                    <td><div align="left" class="style2">Potongan OA Ekspeditur</div></td>
                    <td><div align="left" class="style2">&nbsp;&nbsp;</div></td>
                    <td><div align="right" class="style2"><?=number_format($total_nilaiPoex,2,",",".")?></div></td>
                </tr>
    <?
}
 //End POEX
$oa_kurang_klaim = $nilai_oa_temp-$total_oa_selisih_klaim;

if($total_oa_selisih_klaim>0 || $total_denda){ #jika ada klaim, maka tampilkan
?>
<tr>
    <td><div align="left" class="style2">Total potongan</div></td>
    <td><div align="left" class="style2">:</div></td>
    <td><div align="left" class="style2"></div></td>
    <td><div align="right" class="style2"><?=number_format($total_oa_selisih_klaim,2,",",".")?></div></td>
</tr>
<tr>
    <td><div align="left" class="style2">Total OA setelah potongan</div></td>
    <td><div align="left" class="style2">:</div></td>
    <td><div align="left" class="style2"></div></td>
    <td><div align="right" class="style2"><?=number_format($oa_kurang_klaim,2,",",".")?></div></td>
</tr>
<?
}
?>
</table>
        </td>
    </tr>
</table>
<br />
<br />
<table align="center" width="890">
<tr>
<td>
<table align="right">
<tr>
	<td colspan="4"><span class="style2"> 
	</span></td>
	<td colspan="2"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
	<? if(strtotime($tgl_invoice_v[0])<=strtotime('13-JAN-17')){?>
		<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$tgl_invoice_v[0];//date("d-m-Y")?></td>
    <? } else if(strtotime($tgl_invoice_v[0])<=strtotime('27-MAR-17') && strtotime($tgl_invoice_v[0])>strtotime('13-JAN-17') && $kel == 'DARAT'){ ?>
    	<?php
	            rsort($tgl_kirim_sort);
	            $date=date_create($tgl_kirim_sort[0]);
	        ?>
	        	<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=strtoupper(date_format($date,"d-M-Y"));?></td>
    <? } else{ ?>
		<?php //if ($kel == 'LAUT'): ?>
			<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$datarek['TGL_PAJAK_EX'];//date("d-m-Y")?></td>
		<?php //else: ?>
			<?php
	            //rsort($tgl_kirim_sort);
	            //$date=date_create($tgl_kirim_sort[0]);
	        ?>
	        	<!-- <td colspan="3" align="center" class="style2"> <? //$fungsi->ex_cari_vendor($vendor_v)?>, <? //strtoupper(date_format($date,"d-M-Y"));?></td> -->
		<?php //endif ?>
	<?php } ?>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>
<tr>
	<td colspan="4"><span class="style2"> 
	</span></td>
	<td colspan="2"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
	<td colspan="3" align="center" class="style2"> <?=$nama_vendor_v?></td>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>
<tr><td colspan="12" height="30"> </td>
</tr>
<tr>
	<td colspan="4"><span class="style2"> 
	</span></td>
	<td colspan="2"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
	<td colspan="3" align="center" class="style2"> <?php echo @$datadirut['NAMA_DIRUT']; ?> </td>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>
</table>
</td>
</tr>
<tr>
  <td width="890">
    <input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  /> 
    <input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  />
</div>
</td>
</tr>
</table>

<br/>
</body>
</html>
