<?php
set_time_limit(600); // 300 detik
ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// include_once('ex_fungsi.php');

// session_start();
include_once('ex_fungsi.php');
include_once('curl_ssl_config.php');
// $nama = isset($_SESSION['nama']) ? $_SESSION['nama'] : 'DefaultNama';
class ApiEMeterai
{

    // dev synxchro
    const URL_LOGIN                     = 'https://dev-integrasi-api.sig.id/peruri/ematerai/login';
    //const URL_UPLOAD_DOC                = 'http://skedull.sig.id/bi/skedul/e_invoice/peruri_emeterei.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
     const URL_UPLOAD_DOC                = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
    const URL_GENERATE_SERIAL_NUMBER    = 'https://dev-integrasi-api.sig.id/peruri/ematerai/generate_serial_number';
    const URL_STAMPING                  = 'https://dev-integrasi-api.sig.id/peruri/ematerai/stamping';
    const URL_DOWNLOAD_DOC_PREFIX       = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

    // dev
    // const URL_LOGIN                     = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservicestg.e-meterai.co.id/api/users/login';
    // //const URL_UPLOAD_DOC                = 'http://skedull.sig.id/bi/skedul/e_invoice/peruri_emeterei.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
    //  const URL_UPLOAD_DOC                = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
    // const URL_GENERATE_SERIAL_NUMBER    = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampv2stg.e-meterai.co.id/chanel/stampv2';
    // const URL_STAMPING                  = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampservicestg.e-meterai.co.id/keystamp/adapter/docSigningZ';
    // const URL_DOWNLOAD_DOC_PREFIX       = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

    // prod
    // const URL_LOGIN                     = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservice.e-meterai.co.id/api/users/login';
    // const URL_UPLOAD_DOC                = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileupload.e-meterai.co.id/uploaddoc2';
    // const URL_GENERATE_SERIAL_NUMBER    = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampv2.e-meterai.co.id/chanel/stampv2';
    // const URL_STAMPING                  = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampservice.e-meterai.co.id/keystamp/adapter/docSigningZ';
    // const URL_DOWNLOAD_DOC_PREFIX       = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

    private $jwt = null;
// penambahan fungsi untuk write file json untuk log
    public function cek($nama, $proses,$status,$response){
               $createdAt = new DateTime('now');
            $createdAt = $createdAt->format('Y-m-d H:i:s');
      $fungsi = new ex_fungsi();
        $this->connection = $fungsi->ex_koneksi();
        // $cek=implode('=', $response);
        //       var_dump($response);  
        // die;
      
       if($response != 'false'){
           if(isset($response->statusCode) && $response->statusCode == '00' || isset($response->status) && $response->status == 'True'){
               
              $status="BERHASIL";

           }elseif(isset($response->status) && $response->status=='False'){
               $status= $response->errorMessage;
           }
           else{
               $status ="GAGAL DARI E-MATERAI";
           }
       } 

      $response = json_encode($response);
      
      if($response == 'false'){
      
          $response ="Error: Couldn t resolve host";
      }

        $qu="INSERT INTO ZREPORT_LOG_SERVICE (ID, GROUP_LOG,REQUEST, RESPON, BY_LOG,LOG_DATE, TOKEN, DELETE_MARK)
VALUES ('', '1', '$proses','$response','$nama', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), '$status', '1')
";
       $sql = oci_parse($this->connection, $qu);
            oci_execute($sql);
    }
    public function setJwt($jwt)
    {
        $this->jwt = $jwt;
    }

    public function login($email, $password)
    {
        $headers = array(
            'Content-Type: application/json',
        );

        $data = array(
            'user' => $email,
            'password' => $password,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_LOGIN);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        // Disable SSL verification for local development
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6); // Force TLS 1.2

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            $this->cek("E-MATERAI","GET TOKEN","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $response = json_decode($response);
         $this->cek("E-MATERAI","GET TOKEN","BERHASIL",$response);
        return $response;
    }

    public function uploadDocument($file)
    {
        echo "1m";
        $headers = array(
            'Content-Type: multipart/form-data',
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'file' => $file,
            'token' => $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_UPLOAD_DOC);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6); // Force TLS 1.2

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            $this->cek("E-MATERAI","UPLOAD DOCUMENT","GAGAL", curl_exec($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $response = json_decode($response);
       $this->cek("E-MATERAI","UPLOAD DOCUMENT","BERHASIL",$response);


        return $response;
    }

    public function generateSerialNumber($data)
    {

        echo "2m";
        $headers = array(   
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        getSslInfo($ch);
        curl_setopt($ch, CURLOPT_URL, self::URL_GENERATE_SERIAL_NUMBER);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        // Disable SSL verification for local development
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6); // Force TLS 1.2

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
          $this->cek("E-MATERAI","GENERATE SERIAL NUMBER","GAGAL",curl_errno($ch));
          throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $response = json_decode($response);
        $this->cek("E-MATERAI","GENERATE SERIAL NUMBER","BERHASIL",$response);
        return $response;
    }

    public function stamping($data)
    {
        echo "3m";
        $headers = array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_STAMPING);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6); // Force TLS 1.2

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
             $this->cek("E-MATERAI","STAMPING","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $response = json_decode($response);
         $this->cek("E-MATERAI","STAMPING","BERHASIL",$response);
        return $response;
    }
    
    public function downloadDocument($url)
    {   
        echo "4m";
        $headers = array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_DOWNLOAD_DOC_PREFIX . $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6); // Force TLS 1.2

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
             $this->cek("E-MATERAI","DOWNLOAD DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);
         $this->cek("E-MATERAI","DOWNLOAD DOCUMENT","BERHASIL",$response);
        return $response;
    }
}

class EMeterai
{
    public function __construct()
    {
        $fungsi = new ex_fungsi();
        $this->connection = $fungsi->ex_koneksi();

        $this->jwt = $this->getLatestJwt()->TOKEN;

        $this->api = new ApiEMeterai();
        $this->api->setJwt($this->jwt);
    }

    private function getActiveCredentials() {
        $query = "SELECT * FROM EX_BA_USER_EMATERAI WHERE STATUS_AKTIF = 1";
        $sql = oci_parse($this->connection, $query);
        oci_execute($sql);
        $user = oci_fetch_object($sql);

        if (!$user) {
            throw new Exception('User EMeterai tidak ditemukan');
        }

        return $user;
    }

    private function getLatestJwt()
    {
        $query = "SELECT * FROM (SELECT ID, TOKEN, TO_CHAR(EXPIRED_AT, 'yyyy-mm-dd hh24:mi:ss') AS EXPIRED_AT, CREATED_AT FROM EX_PERURI_METERAI_JWT ORDER BY EXPIRED_AT DESC) where ROWNUM = 1";
        $sql = oci_parse($this->connection, $query);
        oci_execute($sql);
        $data = oci_fetch_object($sql);

        if ($data) {
            return $data;
        }

        return null;
    }

    private function hasActiveJwt()
    {
        $data = $this->getLatestJwt();

        // Check if token is not expired
        $expiredAt = new DateTime($data->EXPIRED_AT);
        $currentTime = new DateTime('now');

        $currentDateTimeGreater = $currentTime > $expiredAt;
        $isSameDate = $expiredAt->format('Y-m-d') == $currentTime->format('Y-m-d');
        $isDiff2Hours = intval($expiredAt->format('H')) - intval($currentTime->format('H')) <= 2;

        if ($currentDateTimeGreater || ($isSameDate && $isDiff2Hours)) {
            return false;
        }

        return true;
    }

    private function refreshJwt()
    {
        $user = $this->getActiveCredentials();

        $response = $this->api->login($user->EMAIL, $user->PASSWORD);

        $token = $response->token;
        if(empty($token)){
            return false;
        }else{
            $createdAt = new DateTime('now');
            $createdAt = $createdAt->format('Y-m-d H:i:s');
            $expiredAt = new DateTime('now');
            $expiredAt->modify('+12 hours');
            $expiredAt = $expiredAt->format('Y-m-d H:i:s');

            $query = "INSERT INTO EX_PERURI_METERAI_JWT (TOKEN, CREATED_AT, EXPIRED_AT) VALUES ('$token', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), TO_DATE('$expiredAt', 'YYYY-MM-DD HH24:MI:SS'))";
            $sql = oci_parse($this->connection, $query);
            oci_execute($sql);

            $this->api->setJwt($token);
        }
    }

    /**
     * Merefresh token jwt.
     *
     * @return bool true jika sukses refresh, false jika token masih aktif.
     */
    public function refreshJwtIfExpired()
    {
        if (!$this->hasActiveJwt()) {
            $this->refreshJwt();
            return true;
        }

        return false;
    }

    public function uploadDocument($file)
    {
        $response = $this->api->uploadDocument($file);

        if ($response->statusCode == '00') {
            return $response;
        }

        throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
    }

    public function generateSerialNumber($data)
    {
        $sendData = array(
            /* [M] Unik ID dokumen yang didapatkan dari API Upload Doc */
            'idfile' => $data['idfile'],

            /* [M] Isi dengan true */
            'isUpload' => true,

            /* [M] Kode jenis dokumen sesuai dengan pilihan yang disediakan dari hasil API Jenis Dokumen */
            'namadoc' => $data['namadoc'],

            /* [M] Nomor dokumen (Jika tidak ada dapat diisi 0) */
            'nodoc' => isset($data['nodoc']) ? $data['nodoc'] : '0',

            /* [M] Tanggal dokumen dengan format [YYYY-MM-DD] (Mandatory khusus akun Pemungut) */
            'tgldoc' => $data['tgldoc'],

            /* [O] Nilai transaksi meterai (Mandatory khusus akun Pemungut) */
            // 'nilaidoc' => $data['nilaidoc'],

            /* [O] Jenis identitas pihak terutang (KTP/NPWP) (Mandatory khusus akun Pemungut) (Mandatory apabila noidentitas dan/atau namedipungut diisi) */
            // 'namejidentitas' => $data['namejidentitas'],

            /* [O] Nomor identitas pihak terutang (NIK KTP/No. NPWP) (Mandatory khusus akun Pemungut) (Mandatory apabila namejidentitas dan/atau namedipungut diisi) */
            // 'noidentitas' => $data['noidentitas'],

            /* [O] Nama pihak terutang (Mandatory khusus akun Pemungut) (Mandatory apabila noidentitas dan/atau namejidentitas diisi) */
            // 'namedipungut' => $data['namedipungut'],

            /* [M] Nama dokumen asli */
            'namafile' => $data['namafile'],

            /* [M] Isi dengan false */
            'snOnly' => false,
        );

        $response = $this->api->generateSerialNumber($sendData);

        if (isset($response->statusCode) && $response->statusCode == '00') {
            return $response;
        }

        // throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
        if (is_object($response)) {
          $msg = (!empty($response->message) ? $response->message : ( !empty($response->errorMessage) ? $response->errorMessage : 'Unknown error'));
        } else {
            // fallback jika bukan object
            $msg = 'Invalid API response: ' . var_export($response, true);
        }
        throw new Exception('Error: ' . $msg);
    }

    public function stamping($data)
    {
        $sendData = array(
            /* [M] Isi dengan false, untuk memunculkan link download */
            'onPrem' => false,

            /* [M] Unik ID dokumen yang didapatkan dari API Upload Doc */
            'docId' => $data['docId'],

            /* [M] Keterangan proses pembubuhan */
            'certificatelevel' => $data['certificatelevel'],

            /* [M] Path folder yang berisi dokumen yang nantinya sudah selesai dibubuhkan e-meterai
               Format: "/sharefolder/final_(parameter id yang didapat dari API uploaddoc).pdf" */
            'dest' => $data['dest'],

            /* [M] Password dokumen (jika ada) */
            'docpass' => $data['docpass'],

            /* [M] Default field digital stamp yang perlu diisi dengan informasi lokasi transaksi, sebagai referensi */
            'location' => $data['location'],

            /* [M] Nama stempel */
            'profileName' => $data['profileName'],

            /* [O] Default field digital stamp yang diisi (opsional) sesuai dengan kebutuhan, sebagai referensi */
            'reason' => $data['reason'],

            /* [M] Path folder yang berisi file QR Image yang akan dibubuhkan pada dokumen
               Format: "/sharefolder/qr_(nama file qr image yang didapat dari API generate SN).png" */
            'spesimenPath' => $data['spesimenPath'],

            /* Path folder yang berisi dokumen yang akan dibubuhkan e-meterai
               Format: "/sharefolder/doc_(parameter id yang didapat dari API uploaddoc).pdf" */
            'src' => $data['src'],

            /* Koordinat specimen QR pada file PDF yang akan dibubuhkan */
            'visLLX' => $data['visLLX'],
            'visLLY' => $data['visLLY'],
            'visURX' => $data['visURX'],
            'visURY' => $data['visURY'],

            /* Informasi halaman file PDF tempat specimen QR dibubuhkan. */
            'visSignaturePage' => $data['visSignaturePage'],

            /* Token JWT yang didapat dari login */
            'jwToken' => $this->jwt,

            /* Serial number yang didapatkan dari API generate SN */
            'refToken' => $data['refToken'],
        );

        $response = $this->api->stamping($sendData);

        if ($response->errorCode == '00') {
            return $response;
        }

        throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
    }

    public function downloadDocument($url)
    {
        $response = $this->api->downloadDocument($url);

        return $response;
    }
}
