<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

//$halaman_id=86;
$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);
$user_id=$_SESSION['user_id'];
$distr_id=$_SESSION['distr_id'];
$user_org=$_SESSION['user_org'];
$distr_nm=$_SESSION['distr_nm'];
// echo "ini distr_id" . $distr_id;
// $distr_id=$fungsi->sapcode($distr_id)
// $sold_to=$distr_id;

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
<SCRIPT LANGUAGE="JavaScript">
<!--
alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
//
-->
</SCRIPT>

<a href="../index.php">Login....</a>
<?

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="sales_value.php";

$currentPage="sales_value.php";

$komen="";	
	

if(isset($_POST['cari'])){
	$noso = $_POST['noso'];
	$tglm = $_POST['tgl1'];
	list($day,$month,$year)=explode("-",$tglm);
	$tglm=$year.$month.$day;
	$tgls = $_POST['tgl2'];
	list($day1,$month1,$year1)=explode("-",$tgls);
	$tgls=$year1.$month1.$day1;

    // echo "ini noso : ".$noso;
    // echo "<br> ini distr_id : ".$distr_id;
    // echo "<br> ini user_org : ".$user_org;
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
		$sap->PrintStatus();
		exit;
	}

	$fce = $sap->NewFunction ("ZCSD_RVKRED88");
	if ($fce == false ) {
	$sap->PrintStatus();
	exit;
	}

	$fce->T_KNKLI->row["SIGN"] = 'I';
	$fce->T_KNKLI->row["OPTION"] = 'EQ';
	$fce->T_KNKLI->row["LOW"] = $distr_id;
	$fce->T_KNKLI->row["HIGH"] = '';
	$fce->T_KNKLI->Append($fce->T_KNKLI->row);

	$fce->T_KKBER->row["SIGN"] = 'I';
	$fce->T_KKBER->row["OPTION"] = 'EQ';
	$fce->T_KKBER->row["LOW"] = '2000';
	$fce->T_KKBER->row["HIGH"] = '';
	$fce->T_KKBER->Append($fce->T_KKBER->row);

	// $fce->T_DD_VBELN->row["SIGN"] = 'I';
	// $fce->T_DD_VBELN->row["OPTION"] = 'EQ';
	// $fce->T_DD_VBELN->row["LOW"] = $noso;
	// $fce->T_DD_VBELN->row["HIGH"] = '';
	// $fce->T_DD_VBELN->Append($fce->T_DD_VBELN->row);

	$fce->T_DD_ERDAT->row["SIGN"] = 'I';
	$fce->T_DD_ERDAT->row["OPTION"] = 'BT';
	$fce->T_DD_ERDAT->row["LOW"] = $tglm;
	$fce->T_DD_ERDAT->row["HIGH"] = $tgls;
	$fce->T_DD_ERDAT->Append($fce->T_DD_ERDAT->row);

	$fce->Call();

	if ($fce->GetStatus() == SAPRFC_OK ) {
		$fce->T_RETURN->Reset();
		$s=0;
		while ( $fce->T_RETURN->Next() ){
			$orgs[$s]= $fce->T_RETURN->row["KKBER"];
			$distr[$s]= $fce->T_RETURN->row["KNKLI"];
			$currcy[$s]= $fce->T_RETURN->row["CMWAE"];
			$sal_doc[$s]= $fce->T_RETURN->row["VBELN"];
			$doc_code[$s]= $fce->T_RETURN->row["VBTYP"];
			$opn_ord[$s]= $fce->T_RETURN->row["OEIKW"];
			$opn_del[$s]= $fce->T_RETURN->row["OLIKW"];
			$opn_bil[$s]= $fce->T_RETURN->row["OFAKW"];
			$months[$s]= $fce->T_RETURN->row["SPMON"];
			$s++;
		}
	} else
		$fce->PrintStatus();

	$fce->Close();
	// $distributor=$distr;
	$total=count($sal_doc);            
	$sap->Close();	

}

?>

<script language=javascript>
// <!-- Edit the message as your wish -->
var message = "You dont have permission to right click";

function clickIE() {
    if (document.all) {
        (message);
        return false;
    }
}

function clickNS(e) {
    if (document.layers || (document.getElementById && !document.all)) {
        if (e.which == 2 || e.which == 3) {
            (message);
            return false;
        }
    }
}

if (document.layers) {
    document.captureEvents(Event.MOUSEDOWN);
    document.onmousedown = clickNS;
} else {
    document.onmouseup = clickNS;
    document.oncontextmenu = clickIE;
}

document.oncontextmenu = new Function("return false")

function getXMLHTTP() {
    var xmlhttp = false;
    try {
        xmlhttp = new XMLHttpRequest();
    } catch (e) {
        try {
            xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
        } catch (e) {
            try {
                xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
            } catch (e1) {
                xmlhttp = false;
            }
        }
    }
    return xmlhttp;
}

function handleSubmit() {
    // Validasi tanggal
    var tgl1 = document.getElementById('tgl1').value;
    var tgl2 = document.getElementById('tgl2').value;

    if (!tgl1 || !tgl2) {
        alert("Tanggal SO harus diisi.");
        return false;
    }

    // Tampilkan loading
    document.getElementById('loading').style.display = 'block';

    // Biarkan form tetap submit
    return true;
}
</script>
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Aplikasi SGG Online: Lihat Data Sales Value :)</title>
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <!-- import the calendar script -->
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <!-- import the language module -->
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
</head>

<body>
    <div align="center">
        <table width="600" align="center" class="adminheading" border="0">
            <tr>
                <th class="kb2">Daftar Sales Value </th>
            </tr>
        </table>
    </div>
    <?
	if($total<1){
?>

    <div align="center">
        <table width="600" align="center" class="adminlist">
            <tr>
                <th align="left" colspan="4"> &nbsp;Form Search Sales Value </th>
            </tr>
        </table>
    </div>

    <!--<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>"  onSubmit="return document.hasil" >-->
    <form id="tambah" name="tambah" method="post" action="<? $currentPage;?>" onsubmit="return handleSubmit();">

        <table width="600" align="center" class="adminform">
            <tr width="174">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td class="puso">Tanggal SO</td>
                <td class="puso">:</td>
                <td>
                    <input name="tgl1" type="text" id="tgl1" size=12 value="" onClick="return showCalendar('tgl1');"
                        required />&nbsp; s.d &nbsp;
                    <input name="tgl2" type="text" id="tgl2" size=12 value="" onClick="return showCalendar('tgl2');"
                        required />&nbsp;
                </td>
            </tr>
            <tr>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find"
                        onclick="return cekTgl();" />
            </tr>
            <tr>
                <td class="ThemeOfficeMenu">&nbsp;</td>
                <td class="ThemeOfficeMenu">&nbsp;</td>
            </tr>
        </table>
    </form>

    <div id="loading" style="display:none; text-align:center; margin-top:20px;">
        <img src="./css-sorter/loading.gif" alt="Loading..." width="50" />
        <p>Mohon tunggu, sedang memproses...</p>
    </div>

    <? } ?>
    <br />
    <br />
    <?
	if($total>0){
?>
    <div align="center">
        <table width="95%" align="center">
            <tr>
                <th align="left">Subdistributor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:
                    &nbsp;&nbsp;<?= $distr_id ?>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?= $distr_nm ?>
                </th>
                <th align="right" colspan="4"><span>
                    </span></th>
            </tr>
        </table>
    </div>
    <div align="center" style="margin-top: 10px; margin-bottom: 10px;">
        <form name="export" method="post" action="sales_value_xls.php">
            <input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tglm?>" />
            <input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgls?>" />
            <input name="Print" type="button" id="Print" value="Cetak" onclick="javascript:window.print();"
                class="nonPrint" />
            &nbsp;&nbsp;
            <input name="excel" type="Submit" id="excel" value="Export" />
        </form>
    </div>
    <div align="center">
        <table width="95%" align="center" class="adminlist">
            <tr>
                <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Sales Value <?=$noso?></span></th>
            </tr>
        </table>
    </div>
    <div align="center">
        <table width="95%" align="center" class="adminlist">
            <tr class="quote">
                <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
                <td align="center"><strong>Credit Acc</strong></td>
                <td align="center"><strong>Curr</strong></td>
                <td align="center"><strong>Sales Doc</strong></td>
                <td align="center"><strong>Doc</strong></td>
                <td align="center"><strong>Month</strong></td>
                <td align="center"><strong>Open Order</strong></td>
                <td align="center"><strong>Open Delivery</strong></td>
                <td align="center"><strong>Open Bill</strong></td>
            </tr>
            <?  	$totalharga=0;
  		for($i=0; $i<$total;$i++) {
		
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>
            <td align="center">
                <? echo $b; ?>
            </td>
            <td align="center">
                <? echo $distr[$i]; ?>
            </td>
            <td align="center">
                <? echo $currcy[$i]; ?>
            </td>
            <td align="center">
                <? echo $sal_doc[$i]; ?>
            </td>
            <td align="center">
                <? echo $doc_code[$i]; ?>
            </td>
            <td align="center">
                <? 	if($months[$i] != 0 ) {
									$thn1=substr($months[$i],0,4);
									$bln1=substr($months[$i],4,2);
									$tglmonths=$bln1.'.'.$thn1;
									echo $tglmonths;
								} else {
									echo "0";
								} ?>
            </td>
            <td align="center">
                <? echo $opn_ord[$i]; ?>
            </td>
            <td align="center">
                <? echo $opn_del[$i]; ?>
            </td>
            <td align="center">
                <? echo $opn_bil[$i]; ?>
            </td>

            </tr>
            <? } ?>
        </table>
    </div>
    <?}?>
    <div align="center">
        <?
echo $komen;

?>
    </div>
    <p>&nbsp;</p>
    </p>
    <? include ('../include/ekor.php'); ?>

</body>

</html>
