<?php
/*
 * upload master supir expeditur
 * @yopi satria
 */

session_start();
include ('../include/ex_fungsi.php');
include ('../include/email.php');
require_once 'helper.php';
require_once ('../security_helper.php');
sanitize_global_input();

$email = new Email();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
$halaman_id=3110;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$dirr = $_SERVER['PHP_SELF']; 
// $halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$no_ba = $_GET['no_ba'];
$importtargetVolume='reject_ba.php';
$waktu=date("d-m-Y");



function formatTGL($tgl){
    $a = explode("/", $tgl);
    $tgal = $a[3].$a[2].$a[1];
    return $tgal;
}


if(isset ($_POST['Import'])){
                    $query = "SELECT * FROM EX_BA WHERE NO_BA = :no_ba";
                    $sql = oci_parse($conn, $query);
                    oci_bind_by_name($sql,":no_ba", $no_ba);
                    oci_execute($sql);
                    $data = oci_fetch_array($sql);
                    $status_ba = $data['STATUS_BA'];
                    if($status_ba != 10){
                        echo "<script>alert('Gagal Reject, BASTP sedang tidak dalam status perlu persetujuan pejabat ekspeditur');</script>";
                        echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =lihat_ba_hdr_ex.php'>"; 
                        exit;
                    }
                
                    // print_r($data);exit;
                    $no_ba_v = $data['NO_BA'];
                    $org_v = $data['ORG'];
                    $no_vendor_v = $data['NO_VENDOR'];
                    $nama_vendor_v = $data['NAMA_VENDOR'];
                    $total_semen_v = $data['KLAIM_SEMEN'];
                    $total_ppdks_v = $data['PPDKS'];
                    $total_inv_v = $data['TOTAL_INV'];

                    $user_name = $_SESSION['user_name'];
                    $keterangan = $_POST['keterangan'];
                    $alasan_ba = 3;
                    $field_names = array('ALASAN_REJECT', 'STATUS_BA','LAST_UPDATE_DATE','LAST_UPDATED_BY','TIPE_ALASAN');
                    $field_data = array("$keterangan", "11", "SYSDATE", "$user_name","$alasan_ba");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT','KOMENTAR_REJECT');
                    $field_data = array("$no_ba","11","REJECTED","$user_id","SYSDATE","$keterangan");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

                    //INSERT LOG HISTORY BA
                    $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
                    <div align=\"center\">
                    <thead>
                    <tr class=\"quote\">
                    <td ><strong>&nbsp;&nbsp;No.</strong></td>
                    <td align=\"center\"><strong>ORG</strong></td>
                    <td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
                    <td align=\"center\"><strong>EKSPEDITUR</strong></td>
                    <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
                    <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
                    <td align=\"center\"><strong>PDPKS</strong></td>
                    <td align=\"center\"><strong>TOTAL</strong></td>
                    <td align=\"center\"><strong>STATUS</strong></td>
                    </tr>
                    </thead>
                    <tbody>";

                    $email_content_table .= " 
                    <td align=\"center\">1</td>
                    <td align=\"center\">".$org_v."</td>       
                    <td align=\"center\">".$no_ba_v."</td>
                    <td align=\"center\">".$no_vendor_v."</td>
                    <td align=\"center\">".$nama_vendor_v."</td>
                    <td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
                    <td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
                    <td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
                    <td align=\"center\">Rejected</td>
                    </tr>";
                    
                    //sendEmail
                    $mailCc = "";
                    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA = :no_ba and STATUS_BA = 10";
                    $query = oci_parse($conn, $sql);
                    oci_bind_by_name($query,":no_ba", $no_ba);
                    oci_execute($query);
                    $row = oci_fetch_assoc($query);
                    $mailTo = $row[ALAMAT_EMAIL];
                    $email_content_table = '';
                    if(!empty($mailTo)){
                        sendMail($mailTo, $mailCc, 'Notifikasi Reject BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
                    }
                    //end sendEmail

                    echo "<script>alert('Berhasil Reject BA');</script>";
                    // if($user_id == '1243'){
                    //     echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =kabiro_ba_trans.php'>"; 
                    // }else{
                        echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =lihat_ba_hdr_ex.php'>"; 
                    // }
                    // echo "<script>setTimeout(function() { win.close();}, 3000)</script>";
              
    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Reject BA</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Reject BA</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="reject_ba.php?no_ba=<?=$no_ba?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
<!--         <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih Alasan</td>
            <td class="puso">:</td>
            <td> <select name="alasan_ba" required>
                <option value="">Pilih Alasan Reject</option>
                <option value="1">SPJ Tidak Sesuai</option>
                <option value="2">Upload SPJ</option>
                <option value="3">Other</option>
            </select></td>
        </tr> -->
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Keterangan</td>
            <td class="puso">:</td>
            <td> <textarea rows="5" name="keterangan" required></textarea></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Submit"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
       <!--  <tr>
            <td class="puso" colspan="3">&nbsp;&nbsp;&nbsp;Download template supir <a href="templete_xls/template_supir.xls">disini</a></td>
           
        </tr> -->
          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>