<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input(); 
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=438;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
 

// $action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="posting_ppl.php";
$no_invoice= $_REQUEST['no_invoice'];
$nod= '';
$sql= "SELECT NO_BA,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,NO_SHP_TRN,KODE_PRODUK,NAMA_PRODUK,PLANT,NAMA_PLANT,WARNA_PLAT,VEHICLE_TYPE,NAMA_VENDOR,VENDOR,SAL_DISTRIK,NAMA_SAL_DIS,SOLD_TO,NAMA_SOLD_TO,SHIP_TO,QTY_SHP,QTY_KTG_RUSAK,QTY_SEMEN_RUSAK,ID,NO_POL,SHP_COST,TOTAL_KLAIM_ALL,NO_PAJAK_EX,KOSTL,PRCTR,KELOMPOK_TRANSAKSI,INCO,ORG, to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1,to_char( TANGGAL_INVOICE, 'YYYY' ) AS TAHUN_INVOICE1, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND ORG = '$user_org' AND NO_INVOICE  IS NOT NULL AND KELOMPOK_TRANSAKSI = 'DARAT' AND STATUS = 'INVOICED'
	AND STATUS2 = 'OPEN' AND NO_BA IS NOT NULL ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){ 
			$orginrr=$row[ORG]; 
		$no_ba_v=$row[NO_BA];
		$no_invoice_v=$row[NO_INVOICE];
		$sqlS = "select SKBP from EX_INVOICE where NO_INVOICE = $row[NO_INVOICE] ";   
		//echo $sqlS;
			$query_s= @oci_parse($conn, $sqlS);
			@oci_execute($query_s);
			$row_s=@oci_fetch_array($query_s); 
		$skbp_val = ($row_s[SKBP]=="" ? "NO" : $row_s[SKBP]);
		
		
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM1];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v=$row[PLANT]; 
		$nama_plant_v=$row[NAMA_PLANT]; 
                $warna_plat_v=$row[WARNA_PLAT]; 
                $type_plat_ingv=trim($row[VEHICLE_TYPE]);                
                if($type_plat_ingv=='205'){
                    $warna_plat_v='HITAM';
                }		
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR]; 
		
		$tanggal_invoice_v=$row[TANGGAL_INVOICE1];
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  
		$cost_centers_v=$row[KOSTL];  
		$profit_center_v=$row[PRCTR];  
		$prctr_v[]=$row[PRCTR];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		
		$inco=$row[INCO];  	
        $orgin_v=$row[ORG];

				
		$noe_invoice =$row[NO_INV_SAP];
		$tahunee =$row[TAHUN_INVOICE1];
		
	}
	$no_pajak_in = str_replace("-", "", $no_pajak_ex);
    $no_pajak_in = str_replace(".", "", $no_pajak_in);
    $kode_faktur_wapu = substr($no_pajak_in, 0, 3);
    $kode_faktur_wapu2 = substr($no_pajak_in, 0, 2);
    if($warna_plat_v =='HITAM'){
		if ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03') {
			$jenis_pajak = "RN";
		}elseif($kode_faktur_wapu == '010' || $kode_faktur_wapu2 == '01'){ //($warna_plat_v=="HITAM")
			$jenis_pajak = "YN";
		}
	}else{
		$jenis_pajak = "YY";
	}
	$total=count($sold_to_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";
	 
	$sql_kom= "SELECT KODE_KOMPONEN,NAMA_KOMPONEN,NO_GL,COST_CENTER,PROFIT_CENTER,KODE_PAJAK,PAJAK,TOTAL,SUB_TOTAL,KETERANGAN FROM EX_KOMPONEN_INV WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' order by ID asc";
	$query_kom= oci_parse($conn, $sql_kom);
               //echo $sql_kom;
		oci_execute($query_kom);
                $kode_komponen_lama=''; 
		while($row_kom=oci_fetch_array($query_kom)){ 
                    // if($row_kom[KODE_KOMPONEN]!=$kode_komponen_lama || $row_kom[KODE_KOMPONEN]=='SG-K3-2017'){ 
                        $kode_komponen_lama = $row_kom[KODE_KOMPONEN];  
                        $kode_komponen_v[]=$row_kom[KODE_KOMPONEN];  
                        $nama_komponen_v[]=$row_kom[NAMA_KOMPONEN];  
                        $no_gl_v[]=$row_kom[NO_GL];  
                        $cost_center_v[]=($row_kom[COST_CENTER] == null ? "" : $row_kom[COST_CENTER]);  
                        $profit_v[]=$row_kom[PROFIT_CENTER];  
                        $kode_pajak_v[]=$row_kom[KODE_PAJAK];  
                        $nilai_pajak_v[]=$row_kom[PAJAK];  

                        $total_komponen_v[]=$row_kom[TOTAL];  
                        $sub_total_v[]=$row_kom[SUB_TOTAL];  
                        $keterangan_komponen_v[]=$row_kom[KETERANGAN];  
                        $total_invoice = $total_invoice + $row_kom[TOTAL];
                    // }
		}
                $n_komponen = count($kode_komponen_v);
                if($n_komponen>1) $ada_klaim = false; else $ada_klaim = true;
	
	
###### alpeen inv: 0000202440  req 05-06-2014 by rofi'i no tiket 73257
$sqlU = "select last_update_date, last_updated_by, kelompok_transaksi, tipe_transaksi from ex_trans_hdr where no_invoice='$no_invoice_v' and delete_mark='0' and rownum=1";
$query_U= oci_parse($conn, $sqlU);
oci_execute($query_U);
$row_update=oci_fetch_array($query_U);
$last_update_date=$row_update[LAST_UPDATE_DATE];  
$last_updated_by=$row_update[LAST_UPDATED_BY];
$tipe_transx = $row_update[TIPE_TRANSAKSI];
$kel_trans=strtoupper(trim($row_update[KELOMPOK_TRANSAKSI]));

//liyantanto
$p1mna=0;
$sql_pjaknew="
            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_v'
                group by NO_INVOICE)
                order by TGL_PAJAK_EX desc
            ) where rownum =1
    ";
$querycek= oci_parse($conn, $sql_pjaknew);
oci_execute($querycek);
$row_datap=oci_fetch_assoc($querycek);unset($tglfakturpajak);
$tglfakturpajak=$row_datap[TGL_PAJAK_EXF];
$VENDORpj=$row_datap[NO_VENDOR];
if($VENDORpj!='**********'){
if($tglfakturpajak!='' && $tglfakturpajak>='********'){
    $p1mna=1;
}
}

if($tipe_transx != 'BAG' && $kel_trans!='LAUT'){
   $sql_text = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
   $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC"; 
   
   $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' GROUP BY ".$sql_group .""; //AND SOLD_TO != '*********3' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********'
   }else{
###### end alpeen 05-06-2014
        if($kel_trans=='LAUT'){
            $sql_text = "select ACCOUNTING_DOC,
        NO_INV_SAP,
        NO_INVOICE,
        NO_INV_VENDOR,
        VENDOR,
        SOLD_TO,
        KLAIM_KTG,
        KLAIM_REZAK,
        KLAIM_SEMEN,
        KLAIM_LEBIH,
        KLAIM_PDPKS,
        OA_SEMEN,
        WARNA_PLAT,
        KELOMPOK_TRANSAKSI from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }
        else {    
            $sql_text = "select ACCOUNTING_DOC,
        NO_INV_SAP,
        NO_INVOICE,
        NO_INV_VENDOR,
        VENDOR,
        SOLD_TO,
        KLAIM_KTG,
        KLAIM_REZAK,
        KLAIM_SEMEN,
        KLAIM_LEBIH,
        KLAIM_PDPKS,
        OA_SEMEN,
        WARNA_PLAT,
        KELOMPOK_TRANSAKSI from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }

        $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' GROUP BY ".$sql_group .") where KLAIM_SEMEN>=KLAIM_LEBIH";
   } 
	$query= oci_parse($conn, $sql);
	oci_execute($query);
	$total_invoice =0;
	$oa_semen_v=0;
        $arr_soldto_cek = array();
	while($row=oci_fetch_array($query)){
                $no_gl_shp_v=$row[NO_GL_SHP];
		$no_invoice_v=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$no_inv_sap_v=$row[NO_INV_SAP];
		$no_acc_doc_v=$row[ACCOUNTING_DOC];
		$warna_plat_v=$row[WARNA_PLAT]; 
		$vendor_v=$row[VENDOR]; 

		$sold_to_v[]=$row[SOLD_TO];
                $arr_soldto_cek[$row[SOLD_TO]] = $row[SOLD_TO];
                
		$klaim_ktg_v[]=$row[KLAIM_KTG];  
		$klaim_rezak_v[]=$row[KLAIM_REZAK];  
		$klaim_semen_v[]=$row[KLAIM_SEMEN];  
		$klaim_pdpks_v[]=$row[KLAIM_PDPKS];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		

		$distributor = $row[SOLD_TO];
	// 	$sql_bn= "SELECT NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' AND SOLD_TO = '$distributor' ORDER BY ID DESC"; 
	// $query_bn= oci_parse($conn, $sql_bn);
	// oci_execute($query_bn);
	// $row_bn=oci_fetch_array($query_bn);

	// 	$nama_vendor_v=$row_bn[NAMA_VENDOR]; 
	// 	$nama_sold_to_v[]=$row_bn[NAMA_SOLD_TO];
	// 	$no_rek_dis_v[]=$row_bn[NO_REK_DIS];
	// 	$nama_bank_dis_v[]=$row_bn[NAMA_BANK_DIS];
	// 	$cabang_bank_dis_v[]=$row_bn[BANK_CABANG_DIS];  

	}
	
	  
	// $link_koneksi_sap = "../include/sapclasses/logon_data.conf"; 
	  
	 // $sap = new SAPConnection();
	// $sap->Connect($link_koneksi_sap);
	// if ($sap->GetStatus() == SAPRFC_OK) 
		// $sap->Open();
	// if ($sap->GetStatus() != SAPRFC_OK) {
		 // $sap->PrintStatus();
		// exit;
	// }

	// $fce = $sap->NewFunction("BAPI_INCOMINGINVOICE_GETDETAIL");
	// if ($fce == false) {
		 // $sap->PrintStatus();
		// exit;
	// } 
	// $fce->INVOICEDOCNUMBER = $noe_invoice;
	// $fce->FISCALYEAR = $tahunee; 
	// $fce->Call();  
	// if ($fce->GetStatus() == SAPRFC_OK) { 
		// $fce->GLACCOUNTDATA->Reset();
		// while ($fce->GLACCOUNTDATA->Next()) { 
			// $arrayu[]=$fce->GLACCOUNTDATA->row;
			// $GL_ACCOUNT_v[]=$fce->GLACCOUNTDATA->row['GL_ACCOUNT'];  
			// $ITEM_AMOUNT_v[]=$fce->GLACCOUNTDATA->row['ITEM_AMOUNT'];  
			// $ITEM_TEXT_v[]=$fce->GLACCOUNTDATA->row['ITEM_TEXT'];  
			// $jenis_pajak_v[]=$fce->GLACCOUNTDATA->row['TAX_CODE'];  
		// } 
		// $totalee = 0;
		// $fce->ITEMDATA->Reset();
		// while ($fce->ITEMDATA->Next()) {  
			// $totalee+=$fce->ITEMDATA->row['ITEM_AMOUNT'];   
		// }
		
		 
 
		
	// }else{  
			 // $fce->PrintStatus();
			// $fce->Close();
			// $sap->Close();
	// }
	// $total_sap=count($GL_ACCOUNT_v); 
	
	// $sap = new SAPConnection();
	// 	$sap->Connect($link_koneksi_sap);
	// 	if ($sap->GetStatus() == SAPRFC_OK) 
	// 		$sap->Open();
	// 	if ($sap->GetStatus() != SAPRFC_OK) {
	// 		 $sap->PrintStatus();
	// 		exit;
	// 	}

	// 	$fce = $sap->NewFunction("BAPI_INCOMINGINVOICE_GETDETAIL");
	// 	if ($fce == false) {
	// 		 $sap->PrintStatus();
	// 		exit;
	// 	} 
	// 	$fce->INVOICEDOCNUMBER = $noe_invoice;
	// 	$fce->FISCALYEAR = $tahunee; 
	// 	$fce->Call();  
	// 	if ($fce->GetStatus() == SAPRFC_OK) { 
		
	// 		$fce1 = $sap->NewFunction("Z_ZCMM_INVOICE_CREATE");
	// 		if ($fce1 == false) {
	// 			 $sap->PrintStatus();
	// 			exit;
	// 		} 
	// 		$fce1->FI_SIMULATE  = "X";
	// 		$fce1->FI_HEADER_DATA['INVOICE_IND'] = $fce->HEADERDATA['INVOICE_IND'];
	// 		$fce1->FI_HEADER_DATA['DOC_TYPE'] = $fce->HEADERDATA['DOC_TYPE']; 
	// 		$fce1->FI_HEADER_DATA['DOC_DATE'] = $fce->HEADERDATA['DOC_DATE'];
	// 		$fce1->FI_HEADER_DATA['PSTNG_DATE'] =$fce->HEADERDATA['PSTNG_DATE'];
	// 		$fce1->FI_HEADER_DATA['COMP_CODE'] = $fce->HEADERDATA['COMP_CODE'];
	// 		$fce1->FI_HEADER_DATA['CURRENCY'] = $fce->HEADERDATA['CURRENCY'];
	// 		$fce1->FI_HEADER_DATA['PMNTTRMS'] = $fce->HEADERDATA['PMNTTRMS'];
	// 		$fce1->FI_HEADER_DATA['HEADER_TXT'] = $fce->HEADERDATA['HEADER_TXT'];
	// 		$fce1->FI_HEADER_DATA['PMNT_BLOCK'] = $fce->HEADERDATA['PMNT_BLOCK'];
	// 		$fce1->FI_HEADER_DATA['PYMT_METH'] = $fce->HEADERDATA['PYMT_METH'];
	// 		$fce1->FI_HEADER_DATA['PARTNER_BK'] = $fce->HEADERDATA['PARTNER_BK'];
	// 		$fce1->FI_HEADER_DATA['ITEM_TEXT'] = $fce->HEADERDATA['ITEM_TEXT'];
			
	// 		$fce->ITEMDATA->Reset();
	// 		while ($fce->ITEMDATA->Next()) { 
	// 			$no_spjee = $fce->ITEMDATA->row['REF_DOC_NO'];
	// 			$sqlS = "select  NO_ENTRY_SHEET  from EX_TRANS_HDR where NO_SHP_TRN = $no_spjee ";   
	// 			$query_s= @oci_parse($conn, $sqlS);
	// 			@oci_execute($query_s);
	// 			$row_s=@oci_fetch_array($query_s); 
	// 			$NO_ENTRY_SHEET=$row_s[NO_ENTRY_SHEET]; 
				
	// 			$fce1->FT_ITEMDATA->row["INVOICE_DOC_ITEM"] = $fce->ITEMDATA->row['INVOICE_DOC_ITEM']; 
	// 			$fce1->FT_ITEMDATA->row["PO_NUMBER"] = $fce->ITEMDATA->row['PO_NUMBER']; 
	// 			$fce1->FT_ITEMDATA->row["PO_ITEM"] = $fce->ITEMDATA->row['PO_ITEM']; 
	// 			$fce1->FT_ITEMDATA->row["REF_DOC_IT"] = $fce->ITEMDATA->row['REF_DOC_IT']; 
	// 			$fce1->FT_ITEMDATA->row["TAX_CODE"] = $fce->ITEMDATA->row['TAX_CODE']; 
	// 			$fce1->FT_ITEMDATA->row["ITEM_AMOUNT"] = $fce->ITEMDATA->row['ITEM_AMOUNT']; 
	// 			$fce1->FT_ITEMDATA->row["QUANTITY"] = $fce->ITEMDATA->row['QUANTITY']; 
	// 			$fce1->FT_ITEMDATA->row["PO_UNIT"] = $fce->ITEMDATA->row['PO_UNIT']; 
	// 			$fce1->FT_ITEMDATA->row["PO_PR_QNT"] = $fce->ITEMDATA->row['PO_PR_QNT']; 
	// 			$fce1->FT_ITEMDATA->row["COND_ST_NO"] = $fce->ITEMDATA->row['COND_ST_NO']; 
	// 			$fce1->FT_ITEMDATA->row["COND_COUNT"] = $fce->ITEMDATA->row['COND_COUNT']; 
	// 			$fce1->FT_ITEMDATA->row["SHEET_NO"] = $NO_ENTRY_SHEET; 
	// 			$fce1->FT_ITEMDATA->row["ITEM_TEXT"] = $fce->ITEMDATA->row['ITEM_TEXT']; 
	// 			$fce1->FT_ITEMDATA->row["SHEET_ITEM"] =  "10" ;  
	// 			$fce1->FT_ITEMDATA->Append($fce1->FT_ITEMDATA->row);
	// 			$total_amounte += $fce->ITEMDATA->row['ITEM_AMOUNT']; 
	// 		} 
			
			
	// 		$fce1->FI_HEADER_DATA['GROSS_AMOUNT'] = $total_amounte;
			 
			 
	// 		$fce1->Call();  
			
	// 		if ($fce1->GetStatus() == SAPRFC_OK) {  
	// 			 $fce1-> FT_ACCIT->Reset();
	// 			while ($fce1->FT_ACCIT->Next()) {
	// 				$status_SIM = $fce1->FT_ACCIT->row["SHKZG"];
	// 				if($status_SIM=="H"){
	// 					$kredite += $fce1->FT_ACCIT->row["PSWBT"]*100;
	// 				}else{
	// 					$debit += $fce1->FT_ACCIT->row["PSWBT"]*100;
	// 				} 
	// 			}
				
	// 			 $fce1->FT_RETURN->Reset();
	// 			while ($fce1->FT_RETURN->Next()) {
	// 				$status_sape = $fce1->FT_RETURN->row["TYPE"];
	// 				$show_kete .= "<br> " . $fce1->FT_RETURN->row["TYPE"];
	// 				$show_kete .= " " . $fce1->FT_RETURN->row["NUMBER"];
	// 				$show_kete .= " " . $fce1->FT_RETURN->row["MESSAGE"];
	// 			}
				 
				
	// 		}else{   
	// 				$fce1->PrintStatus();
	// 				$fce1->Close();
	// 				$sap->Close();
	// 		}
	// 	}else{   
	// 			$fce->PrintStatus();
	// 			$fce->Close();
	// 			$sap->Close();
	// 	}
	
	
        
        $sql1= "SELECT NO_REKENING, BANK, BANK_CABANG, BVTYP FROM EX_INVOICE WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice'";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query1 = oci_parse($conn, $sql1);
	oci_execute($query1);

	while($row1=oci_fetch_array($query1)){
            if($row1[NO_REKENING]!=''){
                $no_rek = $row1[NO_REKENING];
                $nama_bank = $row1[BANK];
                $cabang_bank = $row1[BANK_CABANG];
                $bvtyp = $row1[BVTYP];	
            }
	}

	///DENDA K3
	// $sql_ = "SELECT * FROM EX_DENDAK3_SALDO WHERE NO_INVOICE LIKE '$no_invoice'";
	$sql_ = "SELECT * FROM EX_DENDAK3_SALDO WHERE NO_INVOICE = '$no_invoice'";

	$query_= oci_parse($conn, $sql_);
	oci_execute($query_);
	$dendak3 = array();
	while($row=oci_fetch_assoc($query_)){
	$dendak3[] = $row;
	}
        
        //start POEX
        $sqlPOEX = "SELECT MPOAT.BA_NUMBER,MPOAT.NUM,MPOAT.NILAI_TRANSAKSI,MPOA.JUMLAH FROM M_POTONGAN_OA MPOA
                    JOIN M_POTONGAN_OA_TRANS MPOAT ON MPOAT.NUM = MPOA.NUM
                    WHERE MPOAT.NO_INVOICE = '$no_invoice' AND MPOAT.IS_DELETE = '0'";
	$queryPOEX= oci_parse($conn, $sqlPOEX);
	oci_execute($queryPOEX);
	$poex = array();
	while($rowpoex=oci_fetch_assoc($queryPOEX)){
            $poex[] = $rowpoex;
	}
        
        //end POEX
        
        $today = date('Ymd');
        
        $sqlskbd = "SELECT NO_SKBP FROM EX_SKBP WHERE ORG = '$orgin_v' AND VENDOR = '$vendor_v' AND STATUS = 'APPROVE'
        AND TO_CHAR(DATE_FROM, 'YYYYMMDD') <= $today AND TO_CHAR(DATE_TO, 'YYYYMMDD') >= $today AND DELETE_MARK = '0'";
//        echo $sqlskbd;
        $queryskbd = oci_parse($conn, $sqlskbd);
	oci_execute($queryskbd);
        while($row=oci_fetch_assoc($queryskbd)){
            $skbd[] = $row[NO_SKBP];
	}

//	include ('../denda_k3/dendak3.php');
//	$fung = new dendak3();
//	$fung->test();
	// $dendak3 = $dendak3[0];
	// print_r($dendak3);
	
	
	if((isset($_POST["simpan"]) || $_POST["simpan"] == 'POSTING') ) {
		$hasil_simul= $_POST["hasil_simulate"];
		//echo "masuk ke sini";
		//die;
		if($hasil_simul!=0){
			$url_home = "http://".$_SERVER['SERVER_NAME'].dirname($_SERVER["REQUEST_URI"].'?').'/list_verifikasi_invoice.php'; 
				  
			echo "<script>
					alert('Hasil Simulate harus BALANCE');
					window.location.href='$url_home';
					</script>";
		} 
		
		$no_ba = $_POST["no_ba"]; 
		$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_BA = '$no_ba' AND DIPAKAI = 1";
		$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
		oci_execute($query_invoice_ba);

		$data_invoice_ba = array();
		while($row = oci_fetch_array($query_invoice_ba)) {
			$data_invoice_ba = $row;
		}
		
		if(isset($data_invoice_ba["ID"])) {
			if(isset($_POST["simpan"])){ 
				$status_ba = 70;
				$keter = " Posting PPL ";
				 
			}
		
			
		// fce->I_BELNR = $no_invoice_sap; 
        // while ($row_acc = oci_fetch_array($query_acc)) {
            // $fce->T_BELNR->row["BUKRS"] = $row_acc[ORG];
            // $fce->T_BELNR->row["BELNR"] = $no_accdoc;
            // $fce->T_BELNR->row["GJAHR"] = date("Y");
            // $fce->T_BELNR->Append($fce->T_BELNR->row);
            // $fce->I_BUKRS = $row_acc[ORG];
        // }
			 
			$action = "posting_ppl";
			include ('formula_prod.php'); 
		}
	}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

<script> 

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }


</script>
<script language="javascript">
<!--
var j=1;
    function start_addd() {
        j++;
	var cek=j-1;
	if(validasi('komponen_biaya_d'+cek+'','','R','nama_komponen_d'+cek+'','','R','nilaid'+cek+'','','RisNum','keterangan_d'+cek+'','','R')){
		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
				var nilai_acc_nod = obj_acc_nod.value;	
	
				var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
				var nilai_acc_nod_cek = obj_acc_nod_cek.value;	

				if (nilai_acc_nod == nilai_acc_nod_cek ){
					alert('Data Komponen Biaya Debet Telah Diinputkan \n Silahkan Input Ulang...');
					j--;
					return false;	
				}
			} 
		}
		var body1 = document.getElementById("cobad");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
		newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left"><div id="voucherd'+j+'"><input name="komponen_biaya_d'+j+'" type="text" class="inputlabel" id="komponen_biaya_d'+j+'" value="" onChange="ketik_acc_nod(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_d'+j+'" type="text" class="inputlabel" id="nama_komponen_d'+j+'" value="" readonly="true"  size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="pajak_d'+j+'" type="text" class="inputlabel" id="pajak_d'+j+'" value="" readonly="true"  size="5"/><input name="no_gl_d'+j+'" type="hidden" id="no_gl_d'+j+'" value=""/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnd" type="button" class="button" id="cari_btnd" value="..." onClick="find_acc()"/></div></td><td align="left"><input type="text" value="" id="nilaid'+j+'" name="nilaid'+j+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="left"><input name="keterangan_d'+j+'" type="text" id="no_fakturd'+j+'" size="50" value=""/>&nbsp;&nbsp;</td><td width="100"></td></tr></table>';
		body1.appendChild(newdiv);
		
		document.getElementById("jumlahd").value=j;
		}else{
		j--;
		}
    }

var k=1;
    function start_addk() {
        k++;
	var cek=k-1;
	if(validasi('komponen_biaya_k'+cek+'','','R','nama_komponen_k'+cek+'','','R','nilaik'+cek+'','','RisNum','keterangan_k'+cek+'','','R')){

		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_acc_nok = document.getElementById('komponen_biaya_k'+i+'');
				var nilai_acc_nok = obj_acc_nok.value;	
	
				var obj_acc_nok_cek = document.getElementById('komponen_biaya_k'+cek+'');
				var nilai_acc_nok_cek = obj_acc_nok_cek.value;	

				if (nilai_acc_nok == nilai_acc_nok_cek ){
					alert('Data Komponen Biaya Kredit Telah Diinputkan \n Silahkan Input Ulang...');
					k--;
					return false;	
				}
			} 
		}

		var body1 = document.getElementById("cobak");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "kk"+k); 
		newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left"><div id="voucherk'+k+'"><input name="komponen_biaya_k'+k+'" type="text" class="inputlabel" id="komponen_biaya_k'+k+'" value="" onChange="ketik_acc_nok(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_k'+k+'" type="text" class="inputlabel" id="nama_komponen_k'+k+'" value="" readonly="true"  size="20"/><input name="pajak_k'+k+'" type="text" class="inputlabel" id="pajak_k'+k+'" value="" readonly="true"  size="5"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="no_gl_k'+k+'" type="hidden" id="no_gl_k'+k+'" value=""/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnk" type="button" class="button" id="cari_btnk" value="..." onClick="find_acck()"/></div></td><td align="left"><input type="text" value="" id="nilaik'+k+'" name="nilaik'+k+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="left"><input name="keterangan_k'+k+'" type="text" id="keterangan_k'+k+'" size="50" value=""/>&nbsp;&nbsp;</td><td width="100"></td></tr></table>';
		body1.appendChild(newdiv);
		
		document.getElementById("jumlahk").value=k;
		}else{
		k--;
		}
    }

    
	function cek_lastd() {
		var obj = document.getElementById("jumlahd");
		var cek = obj.value;	
		if (document.hasil == true){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
					var nilai_acc_nod = obj_acc_nod.value;	
		
					var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
					var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
	
					if (nilai_acc_nod == nilai_acc_nod_cek ){
						alert('Data Komponen Debet Telah Diinputkan \n Silahkan Input Ulang...');
						j--;
						return false;	
						document.hasil = false;
					}
				} 
			}
			return true;
		}else{
			return false;	
			document.hasil = false;
		}	
    }

	function cek_lastk() {
		var obj = document.getElementById("jumlahk");
		var cek = obj.value;	
		if (document.hasil == true){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_acc_nod = document.getElementById('komponen_biaya_k'+i+'');
					var nilai_acc_nod = obj_acc_nod.value;	
		
					var obj_acc_nod_cek = document.getElementById('komponen_biaya_k'+cek+'');
					var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
	
					if (nilai_acc_nod == nilai_acc_nod_cek ){
						alert('Data Komponen Kredit Telah Diinputkan \n Silahkan Input Ulang...');
						k--;
						return false;	
						document.hasil = false;
					}
				} 
			}
			return true;	
		}else{
			return false;	
			document.hasil = false;
		}	
    }

	function stop_addd()
	{
	if (j==1){alert('Maaf Minimal 1 Detail Komponen Biaya..');return false;}
	k=j;
	k=k.toString();
    var body1 = document.getElementById("cobad");
	var buang = document.getElementById("dd"+k);
    body1.removeChild(buang);
	j=j-1;
	document.getElementById("jumlahd").value=j;
	}
	function stop_addk()
	{
	if (k==1){alert('Maaf Minimal 1 Detail Komponen Biaya..');return false;}
	l=k;
	l=l.toString();
    var body1 = document.getElementById("cobak");
	var buang = document.getElementById("kk"+l);
	alert(l);
    body1.removeChild(buang);
	k=k-1;
	document.getElementById("jumlahk").value=k;
	}
	
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }

function ketik_acc_nod(obj) {
        var comorg = document.getElementById('org');
	var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&org="+comorg.value+"&nourut="+j;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("voucherd"+j).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function ketik_acc_nok(obj) {
        var comorg = document.getElementById('org');
	var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&org="+comorg.value+"&nourut="+k;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("voucherk"+k).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function find_acc() {
            var comorg = document.getElementById('org');
	    var jenis = document.getElementById("jenis_pajaknya");
		var strURL="cari_accd.php?pajak="+jenis.value+"&org="+comorg.value+"&nourut="+j;
		popUp(strURL);
}
function find_acck() {	
            var comorg = document.getElementById('org');
	    var jenis = document.getElementById("jenis_pajaknya");
		var strURL="cari_acck.php?pajak="+jenis.value+"&org="+comorg.value+"&nourut="+k;
		popUp(strURL);
}
function find_rek() {	
	    var no_vendor = document.getElementById("no_vendor");
		var strURL="cari_rek.php?no_vendor="+no_vendor.value;
		popUp(strURL);
}

function testlink(a){
	var noinvc = '0000000000'+a;
	var panjang = noinvc.length;
	if (panjang>10){
		ekor = panjang -10;
		noinvc = noinvc.substring(ekor, panjang);
	}
	k3_nilai=[];
	// alert(noinvc);
	// alert(k3_temp);
	newWindow = window.open("../denda_k3/preview_denda.php?invoice="+noinvc+"&nod="+k3_temp, null, "height=650,width=700,status=yes,toolbar=no,menubar=no,location=no");
	// newWindow = window.open("../denda_k3/preview_denda.php?invoice="noinvc"&noc="k3_temp"", null, "height=1000,width=700,status=yes,toolbar=no,menubar=no,location=no");
}
</script>
</head>

<body>


<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Posting PPL </th>
</tr></table></div>
<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Data Invoice </th>
</tr>
</table>
</div>

<form id="data_claim" name="data_claim" method="post" action="" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No BA</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba_v?>" readonly="true"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td>
            <input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice_v?>" readonly="true"/>
            <input type="hidden" id="org" name="org" value="<?=$orgin_v?>" readonly="true"/>
      </td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_ex_v?>" readonly="true"/></td>
    </tr>
    <tr>
      <td  class="puso">Vendor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="no_vendor" name="no_vendor"  value="<?=$vendor_v?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_vendor" name="nama_vendor"  value="<?=$nama_vendor_v?>" readonly="true"/></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal  Invoice</td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_invoice" type="text" id="tanggal_invoice" readonly="true" value="<?=$tanggal_invoice_v?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Cost Center </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="cost_center" name="cost_center"  value="<?=$cost_centers_v?>" readonly="true" size="50"/>
        &nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Profit Center </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="profit_center" name="profit_center"  value="<?=$profit_center_v?>" readonly="true"/>
        &nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Warna Plat </td>
      <td  class="puso">:</td>
      <td ><input name="warna_plat" type="text" id="warna_plat" value="<?=$warna_plat_v?>" readonly="true" /></td>
    </tr>
    <tr>
      <td  class="puso">No Rekening </td>
      <td  class="puso">:</td>
      <td > <input type="text" id="bvtyp" name="bvtyp"  value="<?=$bvtyp?>" readonly="true" size="8"/> &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_bank" name="nama_bank"  value="<?=$nama_bank?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="no_rek" name="no_rek"  value="<?=$no_rek?>" readonly="true"/>
      <input name="cari_rek" type="button" class="button" id="cari_rek" value="..." onClick="find_rek()"/></td>
    </tr>
    <tr>
      <td  class="puso">&nbsp;</td>
      <td  class="puso">&nbsp;</td>
      <td >
      <input type="text" id="cabang_bank" name="cabang_bank"  value="<?=$cabang_bank?>" readonly="true" size="50"/>
	   <input type="hidden" name="jenis_pajaknya" id="jenis_pajaknya" value="<?=$jenis_pajak?>"/>
	  </td>
    </tr>
    <tr>
      <td  class="puso">Keterangan</td>
      <td  class="puso">:</td>
      <td> <input type="text" id="keterangan_miro" name="keterangan_miro"  value="" size="60"/> 
      </td>
    </tr>
    <tr>
      <td  class="puso">SKBP</td>
      <td  class="puso">:</td>
	   <td  class="puso"><input type="text" id="skbp" name="skbp"  value="<?=$skbp_val?>" readonly="true" size="3"/>
          Surat Keterangan Bebas Pemotongan Dana/Pemungutan PPh23 </td>
      </td>
    </tr>  
  </table>
<br />
<br />
<?
	if($total>0){

?>

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Invoice </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;NO.</strong></td>
		<td align="center"><strong >TGL SPJ </strong></td>
		 <td align="center"><strong>AREA LT </strong></td>
		 <td align="center"><strong>PRCTR </strong></td>
		 <td align="center"><strong>NO SPJ </strong></td>
		 <td align="center"><strong>NO POL </strong></td>
		 <td align="center"><strong>PRODUK </strong></td>
		 <td align="center"><strong>DISTRIBUTOR</strong></td>
		 <td align="center"><strong>K.KTG</strong></td>
		 <td align="center"><strong>K.SMN</strong></td>
		 <td align="center"><strong>KWANTUM</strong></td>
		 <td align="center"><strong>JUMLAH</strong></td>
		 <td align="center"><strong>KLAIM</strong></td>
      </tr >
	  </thead>
	  <tbody>
<?
		$total_qty_kantong_rusak_v=0;
		$total_qty_semen_rusak_v=0;
		$total_qty_v=0;
		$total_shp_cost_v=0;
		$total_total_klaim_all_v=0;

?>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

<?
		$total_qty_kantong_rusak_v+=$qty_kantong_rusak_v[$i];
		$total_qty_semen_rusak_v+=$qty_semen_rusak_v[$i];
		$total_qty_v+=$qty_v[$i];
		$total_shp_cost_v+=$shp_cost_v[$i];
		$total_total_klaim_all_v+=$total_klaim_all_v[$i];

?>
	  <td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $sal_dis_v[$i]; ?></td>
		<td align="center"><? echo $prctr_v[$i]; ?></td>
		<td align="center"><? echo $spj_v[$i]; ?></td>
		<td align="center"><? echo $no_pol_v[$i]; ?></td>
		<td align="center"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? echo $nama_sold_to_v[$i]; ?></td>
		<td align="center"><? echo number_format($qty_kantong_rusak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_semen_rusak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($shp_cost_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_klaim_all_v[$i],0,",","."); ?></td>
		</tr>
	  <? } ?>
		</tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="8" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_qty_kantong_rusak_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_qty_semen_rusak_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_qty_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_shp_cost_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_total_klaim_all_v,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>

<br />
<br />

<? 
if (count($dendak3)>0) {
?>

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Pelanggaran K3 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
		  <tr class="quote">
			<td ><strong>&nbsp;&nbsp;NO.</strong></td>
			<td align="center"><strong >NO DOKUMEN</strong></td>
			<td align="center"><strong>DIPOTONGKAN</strong></td>
			<td align="center"><strong>JUMLAH DENDA</strong></td>
	      </tr >
		  </thead>
		  <tbody>
		<?
			$total_potongan=0;
			$total_denda=0;
			$total_sisa=0;

		?>
	  	<?  $i=0;
  	// for($i=0; $i<$total;$i++) {
  		foreach ($dendak3 as $key => $value) {

  			$total_potongan += floatval($value['KREDIT']);
  			$total_denda += floatval($value['SALDO']);

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     
		  	<td align="center"><? echo $b; ?></td>
			<td align="center"><?= $value['NO_DOC_DENDA']  ?></td>
			<td align="center"><?= number_format($value['KREDIT'],0,",",".") ?></td>
			<td align="center"><?= number_format($value['SALDO'],0,",",".") ?></td>
		</tr>
		<? 
		  $i++;
		} 
		?>
		</tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="2" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_potongan,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_denda,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>

<br />
<br />
<? }?>
<!-- //Start POEX-->
<?php if (count($poex)>0) {?>
    	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Potongan OA</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
		  <tr class="quote">
			<td ><strong>&nbsp;&nbsp;NO.</strong></td>
			<td align="center"><strong >NO DOKUMEN</strong></td>
                        <td align="center"><strong >NUM</strong></td>
			<td align="center"><strong>DIPOTONGKAN</strong></td>
			<td align="center"><strong>NILAI MASTER POTONGAN</strong></td>
                 </tr >
		</thead>
		<tbody>
		<?
			$total_potonganpoex=0;
			$total_dendapoex=0;
			$total_sisa=0;

		?>
	  	<?  $i=0;
  		foreach ($poex as $key => $value) {

  			$total_potonganpoex += floatval($value['NILAI_TRANSAKSI']);
  			$total_dendapoex += floatval($value['JUMLAH']);

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     
		  	<td align="center"><? echo $b; ?></td>
			<td align="center"><?= $value['BA_NUMBER']  ?></td>
                        <td align="center"><?= $value['NUM']  ?></td>
			<td align="center"><?= number_format($value['NILAI_TRANSAKSI'],0,",",".") ?></td>
			<td align="center"><?= number_format($value['JUMLAH'],0,",",".") ?></td>
		</tr>
		<? 
		  $i++;
		} 
		?>
                </tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="3" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_potonganpoex,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_dendapoex,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>
    <br />
    <br />
<?php } ?> 
    <!-- //End POEX-->
	<!-- <a onclick="testlink(<?= $no_invoice;?>)" class="button">Find Dokumen Denda k3</a> -->
	<!-- <a href="javascript:popUp('../denda_k3/preview_denda.php?invoice=<?=$no_invoice;?>&noc=k3_temp')" onclick="k3_nilai=[]" class="button">Find Dokumen Denda k3</a> -->
	<br /><br />
<div align="center">
	<table width="95%" align="center" class="adminlist" >
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Simulate Jurnal </span></th>
	</tr>
	</table>
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable" >
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;NO.</strong></td>
		<td align="center"><strong >Kode GL </strong></td>
		 <td align="center"><strong>Kode </strong></td>
		 <td align="center"><strong>Nama Komponen </strong></td>
		 <td align="center"><strong>Cost Center </strong></td>
		 <td align="center"><strong>Profit Center </strong></td>
		 <td align="center"><strong>Pajak </strong></td>
		 <td align="center"><strong>Keterangan</strong></td>
		 <td align="center"><strong>Jumlah</strong></td> 
		 <td align="center"><strong>Mata Uang</strong></td> 
      </tr >
	  </thead>
	  <tbody> 
   
	 
<? $x = count($kode_komponen_v);
 $b = 0;
	for ($i=0; $i < count($kode_komponen_v); $i++){ 
		$b++;
		if ($kode_komponen_v[$i] == "SG0001"){
			if ($kode_pajak_v[$i] == "VN" || $kode_pajak_v[$i] == "VX") {
			$pajak_pass = "T";
			$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}
		else if ($kode_pajak_v[$i] == "YN" ) {
			$pajak_pass = "TSS";
			$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}	
			else {
			$pajak_pass = "F";
			$nilai_pajak_oa = 0;
			} 
?>		
	<tr>
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_gl_v[$i]; ?></td>
		<td align="center"><? echo $kode_komponen_v[$i]; ?></td>
		<td align="left"><? echo $nama_komponen_v[$i]; ?></td>
		<td align="center"><? echo $cost_center_v[$i]; ?></td>
		<td align="center"><? echo $profit_v[$i]; ?></td>
		<td align="center"><? echo $kd_p = $kode_pajak_v[$i]; ?></td>
		<td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
		<td align="right"><? echo number_format($tot_i = $sub_total_v[$i],0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<?              $oa_nilai += $sub_total_v[$i]; //Perhitungan TOTAL
 //               echo "hehe".$sub_total_v;
                //echo '<pre>'; print_r($sub_total_v); echo '</pre>';
//WAPU zone 
if($kd_p=='WN' || $kd_p=='WX'  || $kd_p=='RX' || $kd_p=='YQ'){
    //$b++;
    number_format($tot_x = $nilai_pajak_v[$i],0,",",".");
	if ($kd_p=='YQ'){
		$tot_pjk_wn2 = $tot_x;
		$oa_nilai += $tot_pjk_wn2;
	}else {
	$b++;
	$tot_pjk_wn1 = $tot_x*-1;
    $tot_pjk_wn2 = $tot_x;  //*0.10
    $oa_nilai += $tot_pjk_wn1;
    $oa_nilai += $tot_pjk_wn2;
	}
	
    //$tot_pjk_wn1 = $tot_x*-1;
    //$tot_pjk_wn2 = $tot_x;  //*0.10
    //$oa_nilai += $tot_pjk_wn1;
    //$oa_nilai += $tot_pjk_wn2;
    
    if($kd_p=='WX' || $kd_p=='RX'){
        $kode_claim_rezak = 'SG00033';
    }else{
        $kode_claim_rezak = 'SG00027';
    }
    $mialo= "SELECT NAMA_KOMPONEN,NO_GL,KETERANGAN,TAX_CODE,KODE_KOMPONEN,PRCTR FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND TAX_CODE='$kd_p' AND ORG='$orginrr' AND DELETE_MARK = '0' ";
//    echo "<br>crot: ".$mialo;
    $query= oci_parse($conn, $mialo);
    oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PRCTR'];
 
	if($kd_p=='YQ'){
	}else 
	{
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn1,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
	}
    $b++;
     if($kd_p=='WX' || $kd_p=='RX' || $kd_p=='YQ'){
        $kode_claim_rezak = 'SG00034';
    }else{
        $kode_claim_rezak = 'SG00028';
    }
    $mialo= "SELECT NO_GL,NAMA_KOMPONEN,KETERANGAN,TAX_CODE,KODE_KOMPONEN,PRCTR FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND TAX_CODE='$kd_p' AND ORG='$orginrr' AND DELETE_MARK = '0' ";
//    echo "<br>dor: ".$mialo;
    $query= oci_parse($conn, $mialo);
    oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PRCTR'];
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn2,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
}

            }else {
		if ($kode_komponen_v[$i] == "SG0002" or $kode_komponen_v[$i] == "SG0003" or $kode_komponen_v[$i] == "SG0004" or $kode_komponen_v[$i] == "SG0005"){ 
			// $cetak1 = "T";
			// for($o=0; $o<$total; $o++){
            //                 if($sold_to_v[$o] != '*********3' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********'){
								
            //                     $total_kompi=0;
			// 	if ($kode_komponen_v[$i] == "SG0002" )$total_kompi = (-1)*$klaim_ktg_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0003" )$total_kompi = (-1)*$klaim_semen_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0004" )$total_kompi = (-1)*$klaim_rezak_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0005" )$total_kompi = (-1)*$klaim_pdpks_v[$o];

			// 	if ($total_kompi < 0){
					
            //                         if ($cetak1 == "T" ){
                                        ?>
                                        <tr>
                                        <td align="center"><? echo $b;   ?></td>
                                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                                        <td align="center"><? echo $profit_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                                        <!-- <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td> -->
                                        <td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr>
                                        <?
                                        // $oa_nilai+=$total_kompi; //Perhitungan TOTAL
                                        $oa_nilai+=$total_komponen_v[$i];
                                        // $cetak1 = "F";
                                    // }else {

                                        ?>
                                        <!-- <tr>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td> 
										<td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr> -->
                                        <?
                                        // $oa_nilai+=$total_kompi; //Perhitungan TOTAL
										// $oa_nilai+=$total_komponen_v[$i];
                //                     }					
				// }
                //             }
			// }
                }else{
                        ?>
                        <tr>
                        <td align="center"><? echo $b; ?></td>
                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                        <td align="center"><? echo $profit_v[$i]; ?></td>
                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                        <td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                        <td align="center"><? echo "IDR"; ?></td>
                        </tr>
                        <?	
                            $oa_nilai+=$total_komponen_v[$i]; //Perhitungan TOTAL
                }
                ?>
                
                <?php
              
	}
	} ?>
				<tfoot>
					<tr class="quote">
					<td colspan="6" align="center"><strong>Total</strong></td>
					<td align="center"></td>
					<td align="center"></td>
					<td align="right"><strong><? echo number_format($oa_nilai,0,",","."); ?></strong></td>
					<td align="center"></td>
					</tr>
				</tfoot>
	</table>
	<?
		if($ada_klaim) exit; //jika tidak ada klaim, tidak perlu ditampilkan
	?>
	</div> 
	<br /><br />
	<table width="50%" align="center" class="adminlist" id="myScrollTable" style="display: none;">
	<thead>
	  <tr class="quote"> 
		 <td align="center"><strong>Debet/Kredit</strong></td> 
		 <td align="center"><strong>Nilai</strong></td> 
		 <td align="center"><strong>Keterangan</strong></td> 
      </tr >
	  </thead>
	  <tbody>  	
		<tr>  
		<td align="center">Debet</td> 
		<td align="center"><? echo number_format($debit,0,",","."); ?></td> 
		<?php
			if($status_sape!="S"){
				echo $show_kete;
			} 
			$hasil_kurang = $debit - $kredite  ;
			//echo $hasil_kurang;
			if($hasil_kurang == 0 ){
				echo '<td rowspan="2"><center style="font-weight : bold;color:#3db440;font-size: 15px;">BALANCE</center></td>';
			}else{ 
				echo '<td rowspan="2"><center style="font-weight : bold;color:#f81724;font-size: 15px;">NOT BALANCE</center></td>';
			}	
		?>
		
		</tr> 
		<tr>  
		<td align="center">Kredit</td> 
		<td align="center"><? echo "- ".number_format($kredite,0,",","."); ?></td> 
		</tr> 
		
	  </tbody> 
	</table> 
	
<table>
<tr>
<td>
	<input type="hidden" value="<?php echo $hasil_kurang;?>" name="hasil_simulate"  />
	<input type="submit" value="POSTING" name="simpan" class="button"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<a href="list_verifikasi_invoice.php" class="button"><? echo "CANCEL"; ?></a>
</td>
</tr>
</table>
</form>
	</div>
	
	
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
