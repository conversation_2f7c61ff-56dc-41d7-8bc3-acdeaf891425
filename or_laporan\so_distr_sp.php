<? 
	session_start();
	include ('../include/or_fungsi.php');
	include ('../include/validasi.php'); 
	$fungsi=new or_fungsi();
	$conn=$fungsi->or_koneksi();

	$halaman_id=1793;
	$user_id=$_SESSION['user_id'];
	$user_org=$_SESSION['user_org'];
	$distr_id=$_SESSION['distr_id'];
	$sold_to=$fungsi->sapcode($distr_id);

	
	// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
	// ?>
	// 	<SCRIPT LANGUAGE="JavaScript">
	// 		<!--
	// 		alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
	// 		//-->
	// 	</SCRIPT>

	// 	<a href="../index.php">Login....</a>
	// <?

	// exit();
	// }
	
	 $tgl1 = date('d-m-Y');
	  $tgl2 = date('d-m-Y');
	//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
	$page="so_distr_sp.php";
	$distrik = $_POST['kode_distrik'];
	$no_so = $_POST['no_so'];
	$no_so = $fungsi->sapcode($no_so);
	$so_type = $_POST['so_type'];
	$incoterm = $_POST['incoterm'];
	$status = $_POST['status'];

			$tglm = $_POST['tgl1'];
			list($day,$month,$year)=split("-",$tglm);
			$tglm=$year.$month.$day;
			$tgls = $_POST['tgl2'];
			list($day1,$month1,$year1)=split("-",$tgls);
			$tgls=$year1.$month1.$day1;

	$currentPage="so_distr_sp.php";
	$komen="";
	if(isset($_POST['cari'])){
			$sap = new SAPConnection();
			$sap->Connect("../include/sapclasses/logon_data.conf");
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK ) {
			   echo $sap->PrintStatus();
			   exit;
			}
			
			if ($status!='A') {
				$fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
			} else {
				$fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL2");
			}
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			
			//header entri
			
			$fce->XVKORG = $user_org; //org
			$fce->XKUNNR = $sold_to; // sold to
			$fce->XBZIRK = $distrik; 
			$fce->XVBELN = $no_so; // sold to
			$fce->XFLAG = $status;
			$fce->XAUART = $so_type;
			$fce->XINCO1 = $incoterm;
			$fce->XFLAG = $status;
			
			if ($tglm!="" and $tgls!=""){
			$fce->LR_EDATU->row["SIGN"] = 'I';
			$fce->LR_EDATU->row["OPTION"] = 'BT';
			$fce->LR_EDATU->row["LOW"] = $tglm;
			$fce->LR_EDATU->row["HIGH"] = $tgls;
			$fce->LR_EDATU->Append($fce->LR_EDATU->row);
			}	
			// filter reason for rejection
			$fce->LR_ABGRU->row["SIGN"] = 'I';
			$fce->LR_ABGRU->row["OPTION"] = 'EQ';
			$fce->LR_ABGRU->row["LOW"] = '';
			$fce->LR_ABGRU->Append($fce->LR_ABGRU->row);

			$fce->LR_AUGRU->row["SIGN"] = 'I';
			$fce->LR_AUGRU->row["OPTION"] = 'NE';
			$fce->LR_AUGRU->row["LOW"] = 'Z02';
			$fce->LR_AUGRU->Append($fce->LR_AUGRU->row);
		
			$fce->Call();
			
			if ($fce->GetStatus() == SAPRFC_OK ) {
				$fce->RETURN_DATA->Reset();
				$s=0;
				while ( $fce->RETURN_DATA->Next() ){
			$kddistr[$s]= $fce->RETURN_DATA->row["KUNNR"];
			$distr[$s]= $fce->RETURN_DATA->row["NAME1"];
			$so_num[$s]= $fce->RETURN_DATA->row["VBELN"];
			$pp_num[$s]= $fce->RETURN_DATA->row["BSTKD"];
			$co_num[$s]= $fce->RETURN_DATA->row["VGBEL"];
			$tglso[$s]= $fce->RETURN_DATA->row["AUDAT"];
			$kdincoterm[$s]= $fce->RETURN_DATA->row["INCO1"];
			$kddistrik[$s]= $fce->RETURN_DATA->row["BZIRK"];
			$nmdistrik[$s]= $fce->RETURN_DATA->row["BZTXT"];
			$produk[$s]= $fce->RETURN_DATA->row["MAKTX"];
			$kdproduk[$s]= $fce->RETURN_DATA->row["MATNR"];
			$plant1[$s]= $fce->RETURN_DATA->row["WERKS"];
			$sotype[$s]= $fce->RETURN_DATA->row["AUART"];
			$kdshipto[$s]= $fce->RETURN_DATA->row["KUNNR2"];
			$shipto[$s]= $fce->RETURN_DATA->row["NAME2"];
			$qty_so[$s]= $fce->RETURN_DATA->row["KWMENG"];
			$qty_do[$s]= $fce->RETURN_DATA->row["RFMNG"];
			$uom[$s]= $fce->RETURN_DATA->row["MEINS"];
			$kapal[$s]= $fce->RETURN_DATA->row["BNAME"];
			$harga[$s]= $fce->RETURN_DATA->row["NETWR"];
			$pajak[$s]= $fce->RETURN_DATA->row["MWSBP"];
			$pricegrp[$s]= $fce->RETURN_DATA->row["PLTYP"];
			$material[$s]= $fce->RETURN_DATA->row["MATNR"];
			$zpro[$s]= $fce->RETURN_DATA->row["ACCESSNO"];
			$tglkirim[$s]= $fce->RETURN_DATA->row["EDATU"];
			$route[$s]= $fce->RETURN_DATA->row["BEZEI_ROUTE"];
			$tgl_leadtime[$s]= $fce->RETURN_DATA->row["VDATU"];
            $top[$s]= $fce->RETURN_DATA->row["ZTERM"];
			
			$BSTKD_E_PO2[$s]= $fce->RETURN_DATA->row["BSTKD_E"];
			$IHREZ_E_SO3[$s]= $fce->RETURN_DATA->row["IHREZ_E"];	
				$s++;
				}
			}else
					$fce->PrintStatus();

			$fce->Close();	
			$sap->Close();	
			$total=count($so_num);	

	}

	?>
	<script language=javascript>
	<!-- Edit the message as your wish -->

	var message="You dont have permission to right click";

	function clickIE()
	 
	{if (document.all)
	{(message);return false;}}
	 
	function clickNS(e) {
	if
	(document.layers||(document.getElementById&&!document.all))
	{
	if (e.which==2||e.which==3) {(message);return false;}}}
	if (document.layers)
	{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
	else
	{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
	 
	document.oncontextmenu=new Function("return false")
	function getXMLHTTP() { 
			var xmlhttp=false;	
			try{
				xmlhttp=new XMLHttpRequest();
			}
			catch(e)	{		
				try{			
					xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
				}
				catch(e){
					try{
					xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
					}
					catch(e1){
						xmlhttp=false;
					}
				}
			}
				
			return xmlhttp;
		}
	function finddistr(org) {
			var com_org = document.getElementById('org');		
			var strURL="cari_distr.php?org="+com_org.value;
			popUp(strURL);
			} 
			  
	function ketik_distr(obj) {
		var com_org = document.getElementById('org');		
		var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() { 
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {	
						document.getElementById("distrdiv").innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
	function findshipto() {	
			var com_sold = document.getElementById('sold_to');
			var strURL="cari_shiptoadm.php?nourut="+j+"&sold_to="+com_sold.value;
			popUp(strURL);
	}

	function ketik_shipto(obj) {
		var com_sold = document.getElementById('sold_to');
		var strURL="ketik_shiptoadm.php?shipto="+obj.value+"&nourut="+j+"&sold_to="+com_sold.value;
		var req = getXMLHTTP();
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById("shiptodiv"+j).innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
	function finddistrik() {	
			var com_sold = document.getElementById('sold_to');
			var strURL="cari_distrik.php?sold_to="+com_sold.value;
			popUp(strURL);
	}
	</script>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
	<title>Aplikasi SGG Online: Lihat Data Sales Order :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
	<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
	<!-- import the calendar script -->
	<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
	<!-- import the language module -->
	<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
	<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
	<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
	<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
	<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
	<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
	</head>

	<body>
	<div align="center">
	<table width="600" align="center" class="adminheading" border="0">
	<tr>
	<th class="kb2">Daftar Sales Order </th>
	</tr></table></div>
	<?
		if($total<1){
	?>	

	<div align="center">
	<table width="600" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"> &nbsp;Form Search Sales Order </th>
	</tr>
	</table>
	</div>

	<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('so_type','','R');return document.hasil">
	  <table width="600" align="center" class="adminform">
		<tr width="174">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		</tr>
		 <tr>
		   <td  class="puso">No SO </td>
		   <td  class="puso">:</td>
		   <td ><input name="no_so" type="text" class="" value="<? echo $no_so; ?>" size="40" maxlength="10"/></td>
		 </tr>
		 <tr>
		  <td  class="puso">Distrik </td>
		  <td  class="puso">:</td>
		  <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
		  <input name="sold_to" id="sold_to" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$sold_to;?>"/>
			<div id="shiptodiv">
		  <input type="text" value="" class="inputlabel" id="kode_distrik" name="kode_distrik" size="10">
		  <input type="text" value="" class="inputlabel" id="nama_distrik" name="nama_distrik"  size="20"  readonly="true" >
		  <input name="btn_distrik" type="button" class="button" id="btn_distrik" value="..." onClick="finddistrik()"/>
		  <input name="val_error_distrik" type="hidden" id="val_error_distrik" value="0" />
		</div></td></tr>
		 <tr>
		  <td  class="puso">Tipe SO </td>
		  <td  class="puso">:</td>
		  <td ><select name="so_type" id="so_type">
			<option value="">---Pilih Tipe Order---</option>
			<? $fungsi->or_order_type($so_type); ?>     
			</select>*	
	</td></tr>
		 <tr>
		  <td  class="puso">Syarat Penyerahan</td>
		  <td  class="puso">:</td>
		  <td ><select name="incoterm" id="incoterm" >
			<option value="">---Pilih Penyerahan---</option>
			<? $fungsi->or_jenis_kirim($incoterm); ?>     
			</select>	
	</td></tr> 
		 <tr>
		  <td class="puso">Status SO</td>
		  <td class="puso">:</td>
		  <td ><select name="status" id="status" >
			<option value="O">Open</option>
			<option value="C">Complete</option>
			<option value="A">All</option>
			</select>	
	</td></tr>
		<tr>
		  <td  class="puso">Tanggal Kirim</td>
		  <td  class="puso">:</td>
		  <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl1?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
		<input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl2?>" onClick="return showCalendar('tgl2');"/></td>
		</tr>    
		<tr>
		  <td class="ThemeOfficeMenu">&nbsp;</td>
		  <td class="ThemeOfficeMenu">&nbsp;</td>
		<td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
		<tr>
		  <td class="ThemeOfficeMenu">&nbsp;</td>
		  <td class="ThemeOfficeMenu">&nbsp;</td>
		</tr>
	  </table>
	</form>
	<? } ?>
	<br />
	<br />
	<?
		if($total>0){
	?>
		<div align="center">
		<table width="95%" align="center">
		<tr>
		<th align="right" colspan="4"><span>
		 </span></th>
		</tr>
		</table>
		</div> 
		<div align="center">
		<table width="1300" align="center" class="adminlist">
		<tr>
		<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Sales Order Distributor <?=$nama_sold_to?></span></th>
		</tr>
		</table>
		</div> 
		<div align="center">
		<table width="1300" align="center" class="adminlist">
		  <tr class="quote">
			<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >No Kontrak</strong></td>
		<td align="center"><strong >Sales Order</strong></td>
		<td align="center"><strong >Sales Order 3</strong></td>
		<td align="center"><strong >PO 2</strong></td>		
		<td align="center"><strong >Tgl SO</strong></td>
		<td align="center"><strong >Tgl Kirim</strong></td>
		<td align="center"><strong >Estimasi Tgl Terima</strong></td>			
		<td align="center"><strong >Tipe SO</strong></td>
		<td align="center"><strong >Incoterm</strong></td>
		<td align="center"><strong >Route</strong></td>
         <td align="center"><strong >TOP</strong></td>
		 <td align="center"><strong>Distributor</strong></td>
		 <td align="center"><strong>Nama Distrik</strong></td>
		 <td align="center"><strong>Ship To </strong></td>
		 <td align="center"><strong>Plant</strong></td>
		 <td align="center"><strong>Material</strong></td>
		 <td align="center"><strong>Material Desc</strong></td>
		 <td align="center"><strong>Qty SO</strong></td>
		 <td align="center"><strong>UOM</strong></td>
		 <td align="center"><strong>Qty Release</strong></td>
		 <td align="center"><strong>Qty Sisa SO</strong></td>
		  <td align="center"><strong>Klp Harga</strong></td>
		  <td align="center"><strong>Harga Setelah Pajak</strong></td>
		  <td align="center"><strong>Total Harga</strong></td>
		 <td align="center"><strong>Nama Kapal</strong></td>
		 <td align="center"><strong>Print SO</strong></td>
		  </tr >
	  <?  for($i=0; $i<$total;$i++) {

			$b=$i+1;
			if(($i % 2) == 0)	{	 
			echo "<tr class='row0'>";
				}
			else	{	
			echo "<tr class='row1'>";
				}	
			?>     
			<td align="center"><? echo $b; ?></td>
			<td align="center"><? echo $pp_num[$i]; ?></td>
			<td align="center"><? echo $co_num[$i]; ?></td>
			<td align="center"><? echo $so_num[$i]; ?></td>
			<td align="center"><? echo $IHREZ_E_SO3[$i]; ?></td>
		<td align="center"><? echo $BSTKD_E_PO2[$i]; ?></td>				
			<td align="center"><? $thn=substr($tglso[$i],0,4);
								  $bln=substr($tglso[$i],4,2);
								  $hr=substr($tglso[$i],6,2);
								  $tgl=$hr.'-'.$bln.'-'.$thn;
									echo $tgl; ?></td>
			<td align="center"><? $thn1=substr($tglkirim[$i],0,4);
								  $bln1=substr($tglkirim[$i],4,2);
								  $hr1=substr($tglkirim[$i],6,2);
								  $tglkrm=$hr1.'-'.$bln1.'-'.$thn1;
									echo $tglkrm; ?></td>
		<td align="center"><? $thn1=substr($tgl_leadtime[$i],0,4);
							  $bln1=substr($tgl_leadtime[$i],4,2);
							  $hr1=substr($tgl_leadtime[$i],6,2);
							  $tglleadtime=$hr1.'-'.$bln1.'-'.$thn1;
								echo $tglleadtime; ?></td>																	
			<td align="center"><? echo $sotype[$i]; ?></td>
			<td align="center"><? echo $kdincoterm[$i]; ?></td>
			<td align="center"><? echo $route[$i]; ?></td>
			<td align="center"><? echo $top[$i]; ?></td>
			<td align="left"><? echo $distr[$i]; ?></td>
			<td align="left"><? echo $nmdistrik[$i]; ?></td>
			<td align="left"><? echo $shipto[$i]; ?></td>
			<td align="center"><? echo $plant1[$i]; ?></td>
			<td align="center"><? echo $kdproduk[$i]; ?></td>
			<td align="left"><? echo $produk[$i]; ?></td>
			<td align="right"><? echo number_format($qty_so[$i],0,",","."); ?></td>
			<td align="center"><? echo $uom[$i]; ?></td>
			<td align="right"><? echo number_format($qty_do[$i],0,",","."); ?></td>
			<td align="center"><? $sisa=$qty_so[$i]-$qty_do[$i]; echo number_format($sisa,0,",","."); ?></td>
			<td align="center"><? echo $pricegrp[$i]; ?></td>
			<td align="right"><?  
                 $harga_total= (($harga[$i]*100)+($pajak[$i]*100)) ;
		    $hargasatuan = (($harga[$i]*100)+($pajak[$i]*100)) / $qty_so[$i];
		  	echo number_format($hargasatuan,2,",",".");
			//   $harga_total= (($harga[$i]*100)+($pajak[$i]*100)) ;
			//   $hargasatuan = (($harga[$i]*100)+($pajak[$i]*100)) / $qty_so[$i];
			// 	echo number_format($hargasatuan,2,",","."); 
                /*   
                $hargasatuan = ($harga[$i]*100) / $qty_so[$i];
                
                if($plant1[$i] == "3404")
                    $prsn = 1.0025;
                else
                    $prsn = 1.1025;
			$hargapajak = $hargasatuan * $prsn;
                        echo number_format($hargapajak,2,",","."); 
                */                                               
                ?></td>
		<td align="right"><? 
                 	echo number_format($harga_total,2,",","."); 
                        /*$hargatotal = ($hargapajak * $qty_so[$i]);
								echo number_format($hargatotal,2,",","."); */ ?></td> 
									
			<td align="left"><? echo $kapal[$i]; ?></td>
			<td align="center">
				<a href="javascript:popUp('detailso.php?presales_ord_no=<?=$so_num[$i]?>&soldto=<?=$kddistr[$i] ?>&qty=<?=$qty_do[$i]?>&satuan=<?=$uom[$i] ?>&plant=<?=$plant1[$i] ?>&incoterm=<?= $kdincoterm[$i]?>&materialdesc=<?=$kdproduk[$i] ?>&material=<?=$produk[$i] ?>&kapal=<?=$kapal[$i] ?>&nopo=<?=$pp_num[$i] ?>&totalharga=<?=number_format($hargasatuan,2,",",".")?>')"> 
				<img src="../images/search.png" border="0" height="20" width="20"/>
				</a>   
			</td>
			</tr>
		  <? } ?>
		</table>
	<p>&nbsp;</p>
	<div class="nonPrint" >
	<form name="export"  method="post" action="so_distr_xls.php">
	<input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
	<input name="so_type" type="hidden" id="so_type" value="<?=$so_type?>"/>
	<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
	<input name="incoterm" type="hidden" id="incoterm" size=12 value="<?=$incoterm?>"/>
	<input type="hidden" value="<?=$kode_distrik;?>" class="inputlabel" id="hidden" name="kode_distrik" size="10">
	<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tglm?>"/>
	<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgls?>"/>

	<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="button" /> 	
	&nbsp;&nbsp;
	<input name="excel" type="Submit" id="excel" value="Export" class="button" /> 	
	&nbsp;&nbsp;
	<a href="so_distr_sp.php" target="isi" class="button">Back</a>
	</form>
	</div>
		</div>
		<?
		}?>
	<div align="center">
	<?
	echo $komen;

	?></div>

	<p>&nbsp;</p>
	</p>
	<? include ('../include/ekor.php'); ?>

	</body>
	</html>
