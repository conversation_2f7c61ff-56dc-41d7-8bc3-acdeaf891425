<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

//Translate
require_once('../include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

$halaman_id=2393;
$user_id=$_SESSION['user_id'];
$distr_id=$_SESSION['distr_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($distr_id);
$distr_nm=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","DISTRIBUTOR_ID",$distr_id,"NAMA_DISTRIBUTOR");
$cara_bayar='CASH';

function getPlantfromdist($orgin,$kunnrin){
        
        if ($orgin=='2000' or $orgin=='5000' or $orgin=='7000') { $org1='ZSG1';}
        elseif ($orgin=='3000') { $org1='ZSP1';}
        elseif ($orgin=='6000') { $org1='ZTL1';}
        else {$org1='ZST1';}
        
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
           echo $sap->PrintStatus();
           exit;
        }

        $fce = $sap->NewFunction ("Z_ZCSD_DIST");
	if ($fce == false ) {
	   $sap->PrintStatus();
	   exit;
	}
	
	//header entri	
	$fce->ZKTOKD = $org1;
	$fce->ZKUNNR = sprintf('%010s',$kunnrin);
        $fce->ZNMORG = $orgin;
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {		
            $fce->RETURN_DATA->Reset();
            while ( $fce->RETURN_DATA->Next() ){
                $werks= $fce->RETURN_DATA->row["VWERK"];
            }
        }else
                $fce->PrintStatus();

        $fce->Close();	
        $sap->Close();
        
        return $werks;
}

$branch_plantold=getPlantfromdist($user_org,$distr_id);
if($branch_plantold!=''){
    $branch_plant=$branch_plantold;
}
/*if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("You are not authorized to access this page.... \n Login Please...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}*/
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);

$sold_to=$distr_id;
$nama_sold_to=$distr_nm;
$halaman_aksi = "komentar.php";
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }


</script>
<script language="javascript">
<!--
var j=1;
    function start_add() {
	//if (j==11){alert('ma\'af maksimal 10');return false;}
        j++;
	var cek=j-1;
                if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','qty'+cek+'','','RisNum','tgl_kirim'+cek+'','','R')){

//		if(cek>1){
//			for (var i = 1; i < cek; i++){
//				var obj_tokoi = document.getElementById('shipto'+i+'');
//				var nilai_tokoi = obj_tokoi.value;	
//	
//				var obj_tokocek = document.getElementById('shipto'+cek+'');
//				var nilai_tokocek = obj_tokocek.value;	
//
//				var obj_produki = document.getElementById('produk'+i+'');
//				var nilai_produki = obj_produki.value;	
//	
//				var obj_produkcek = document.getElementById('produk'+cek+'');
//				var nilai_produkcek = obj_produkcek.value;	
//
//				var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
//				var nilai_tgl_kirimi = obj_tgl_kirimi.value;	
//
//				var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
//				var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;	
//	
//				if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek){
//						alert("Data Stores, Product and Date Inquiry has been entered \n Please entry again...");
//						document.hasil = false;
//						return false;
//				}
//			} 
//		} 

		
		var body1 = document.getElementById("coba");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
		newdiv.innerHTML='<table width = "2045" class="adminlist">\n\
                        <tr>\n\
                                <td align="left"><span id="nodiv'+j+'"><strong>'+j+'</strong></span></td>\n\
                                <td align="left"><div id="shiptodiv'+j+'">\n\
                                <input type="text" class="inputlabel" value="" size="10" id="shipto'+j+'" name="shipto'+j+'" onchange="ketik_shipto(this,'+j+')"/>\n\
                                <input type="text" value="" size="17" class="inputlabel" id="nama_shipto'+j+'" readonly="true" name="nama_shipto'+j+'">\n\
                                <input type="text" value="" id="alamat'+j+'" size="17" name="alamat'+j+'" readonly="true" >\n\
                                <input type="hidden" value="" id="kode_distrik'+j+'" name="kode_distrik'+j+'" >\n\
                                <input type="text" value="" id="nama_distrik'+j+'" size="10" name="nama_distrik'+j+'" readonly="true" >\n\
                                <input type="hidden" value="" id="kode_prov'+j+'" name="kode_prov'+j+'" >\n\
                                <input type="hidden" value="" id="nama_prov'+j+'" name="nama_prov'+j+'" >\n\
                                <input name="btn_shipto'+j+'" type="button" class="button" id="btn_shipto'+j+'" value="..." onClick="findshipto('+j+')">\n\
                                </div></td>\n\
                                <td align="left"><div id="produkdiv'+j+'">\n\
                                <input type="text" value="" size="12" class="inputlabel" id="produk'+j+'" name="produk'+j+'" onchange="ketik_produk(this,'+j+');"/>\n\
                                <input type="text" value="" class="inputlabel" readonly="true" id="nama_produk'+j+'" name="nama_produk'+j+'" size="17"/>\n\
                                <input name="disuom'+j+'" type="text" class="inputlabel" id="disuom'+j+'" value="" readonly="true"  size="4"/>\n\
                                <input type="hidden" value="" class="inputlabel" readonly="true" id="uom'+j+'" name="uom'+j+'" size="4"/>\n\
                                <input name="btn_produk'+j+'" type="button" class="button" id="btn_produk'+j+'" value="..." onClick="findproduk('+j+');">\n\
                                </div>\n\
                                </td>\n\
                                <td align="left">\n\
                                    <div id="kantongdiv'+j+'">\n\
                                          <input name="ktproduk'+j+'" type="text" class="inputlabel" id="ktproduk'+j+'" value="<?=$produkkt?>" onChange="ketik_produkkt(this,'+j+')" maxlength="20" size="12"/>\n\
                                          <input name="ktnama_produk'+j+'" type="text" class="inputlabel" id="ktnama_produk'+j+'" value="<?=$nama_produkkt?>" readonly="true"  size="17"/>\n\
                                          <input name="ktuom'+j+'" type="text" class="inputlabel" id="ktuom'+j+'" value="<?=$uomkt?>" readonly="true"  size="4"/>\n\
                                          <input name="ktbtn_produk'+j+'" type="button" class="button" id="ktbtn_produk'+j+'" value="..." onClick="findprodukkt('+j+')"/>\n\
                                          <input name="ktval_error_produk'+j+'" type="hidden" id="ktval_error_produk'+j+'" value="0"/>\n\
                                    </div>\n\
                                </td>\n\
                                <td align="left">\n\
                                <input type="text" value="" id="nopol'+j+'" name="nopol'+j+'" onChange="getcaripol(this,'+j+');" size="14")/>\n\
                                <input type="text" value="" id="tipenopol'+j+'" name="tipenopol'+j+'" size="4" readonly="readonly" />\n\
                                <input type="text" value="" id="drivernamenopol'+j+'" name="drivernamenopol'+j+'" size="17" />\n\
                                <input type="text" value="" id="simdrivernamenopol'+j+'" name="simdrivernamenopol'+j+'" size="17"/>\n\
                                <input name="btn_truck'+j+'" type="button" class="button" id="btn_truck'+j+'" value="..." onClick="findtruk('+j+')"/>\n\
                                <span id="covertruk'+j+'"></span>\n\
                                </td>\n\
                                <td align="left">\n\
                                <input type="text" value="" id="qty'+j+'" name="qty'+j+'" size="6" maxlength="6" onBlur="javascript:IsNumeric(this)"/>\n\
                                </td>\n\
                                <td align="left">\n\<input name="tgl_kirim'+j+'" size="12" type="text" id="tgl_kirim'+j+'" value="<?=date("d-m-Y");?>" onClick="return showCalendar(\'tgl_kirim'+j+'\');" readonly="readonly" />\n\
                                </td>\n\
                                <td align="left"><input name="val_addnote'+j+'" type="text" id="val_addnote'+j+'" size="20"  readonly="readonly" />\n\
                                <input type="button" value=".." name="addnote'+j+'" id="addnote'+j+'" onClick="addnote(\''+j+'\',\'val_addnote'+j+'\');" />\n\
                                </td>\n\
                                <td width="50">\n\
                                    <input type="button" onclick="return stop_add('+j+');" id="kurang'+j+'" name="kurang'+j+'" value="  -  ">\n\
                                </td>\n\
                                </tr></table>';
		body1.appendChild(newdiv);
		document.getElementById('jumlah').value=j;
		}else{
		j--;
		}
    }
    function cek_last(id_cek) {
		var obj = document.getElementById(id_cek);
		var cek = obj.value;
		
		if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','nopol'+cek+'','','R','tipetruk'+cek+'','','R','qty'+cek+'','','RisNum','tgl_kirim'+cek+'','','R')){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_tokoi = document.getElementById('shipto'+i+'');
					var nilai_tokoi = obj_tokoi.value;	
		
					var obj_tokocek = document.getElementById('shipto'+cek+'');
					var nilai_tokocek = obj_tokocek.value;	
	
					var obj_produki = document.getElementById('produk'+i+'');
					var nilai_produki = obj_produki.value;	
		
					var obj_produkcek = document.getElementById('produk'+cek+'');
					var nilai_produkcek = obj_produkcek.value;	

					var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
					var nilai_tgl_kirimi = obj_tgl_kirimi.value;	

					var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
					var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;	
	
					if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek){
						alert("<?php $translatebhsxx->__1('Data Stores, Product and Date Inquiry has been entered'); ?> \n <?php $translatebhsxx->__1('Please entry again'); ?>...");
						document.hasil = false;
						return false;
					}
				} 
			}
			return true;	
		}else{
		document.hasil = false;
		return false;
		}		
    }
        function tukar(idtk){
            var objrec = document.getElementById('jumlah');
            var cekrec = objrec.value;
            var ty = 0;
            for (var a = 2; a < cekrec + 1; a++){  
                ty=a-1;
                if(idtk!=a && a>=idtk){            
                    if(ty<=cekrec){
                        //alert (a+" ke "+ty);
                        document.getElementById("dd"+a).setAttribute("id", "dd"+ty);          
                        document.getElementById("nodiv"+a).innerHTML='<strong>'+ty+'</strong>';
                        document.getElementById("nodiv"+a).setAttribute("id", "nodiv"+ty);
                        document.getElementById("shiptodiv"+a).setAttribute("id", "shiptodiv"+ty);
                        document.getElementById("shipto"+a).setAttribute("onChange", "ketik_shipto(this,'"+ty+"');");
                        document.getElementById("shipto"+a).setAttribute("name", "shipto"+ty);
                        document.getElementById("shipto"+a).setAttribute("id", "shipto"+ty);
                        document.getElementById("nama_shipto"+a).setAttribute("name", "nama_shipto"+ty);
                        document.getElementById("nama_shipto"+a).setAttribute("id", "nama_shipto"+ty);
                        document.getElementById("alamat"+a).setAttribute("name", "alamat"+ty);
                        document.getElementById("alamat"+a).setAttribute("id", "alamat"+ty);
                        document.getElementById("kode_distrik"+a).setAttribute("name", "kode_distrik"+ty);
                        document.getElementById("kode_distrik"+a).setAttribute("id", "kode_distrik"+ty);
                        document.getElementById("nama_distrik"+a).setAttribute("name", "nama_distrik"+ty);
                        document.getElementById("nama_distrik"+a).setAttribute("id", "nama_distrik"+ty);
                        document.getElementById("kode_prov"+a).setAttribute("name", "kode_prov"+ty);
                        document.getElementById("kode_prov"+a).setAttribute("id", "kode_prov"+ty);
                        document.getElementById("nama_prov"+a).setAttribute("name", "nama_prov"+ty);
                        document.getElementById("nama_prov"+a).setAttribute("id", "nama_prov"+ty);
                        document.getElementById("btn_shipto"+a).setAttribute("onClick", "findshipto('"+ty+"');");
                        document.getElementById("btn_shipto"+a).setAttribute("name", "btn_shipto"+ty);
                        document.getElementById("btn_shipto"+a).setAttribute("id", "btn_shipto"+ty);                    
                        document.getElementById("produkdiv"+a).setAttribute("id", "produkdiv"+ty);
                        document.getElementById("produk"+a).setAttribute("onchange", "ketik_produk(this,'"+ty+"');");
                        document.getElementById("produk"+a).setAttribute("name", "produk"+ty);
                        document.getElementById("produk"+a).setAttribute("id", "produk"+ty);
                        document.getElementById("nama_produk"+a).setAttribute("name", "nama_produk"+ty);
                        document.getElementById("nama_produk"+a).setAttribute("id", "nama_produk"+ty);
                        document.getElementById("disuom"+a).setAttribute("name", "disuom"+ty);
                        document.getElementById("disuom"+a).setAttribute("id", "disuom"+ty);
                        document.getElementById("uom"+a).setAttribute("name", "uom"+ty);
                        document.getElementById("uom"+a).setAttribute("id", "uom"+ty);                        
                        document.getElementById("btn_produk"+a).setAttribute("onClick", "findproduk('"+ty+"');");
                        document.getElementById("btn_produk"+a).setAttribute("name", "btn_produk"+ty);
                        document.getElementById("btn_produk"+a).setAttribute("id", "btn_produk"+ty);
                        document.getElementById("nopol"+a).setAttribute("onchange", "getcaripol(this,'"+ty+"');");
                        document.getElementById("nopol"+a).setAttribute("name", "nopol"+ty);
                        document.getElementById("nopol"+a).setAttribute("id", "nopol"+ty);
                        document.getElementById("tipenopol"+a).setAttribute("name", "tipenopol"+ty);
                        document.getElementById("tipenopol"+a).setAttribute("id", "tipenopol"+ty);
                        document.getElementById("drivernamenopol"+a).setAttribute("name", "drivernamenopol"+ty);
                        document.getElementById("drivernamenopol"+a).setAttribute("id", "drivernamenopol"+ty);
                        document.getElementById("simdrivernamenopol"+a).setAttribute("name", "simdrivernamenopol"+ty);
                        document.getElementById("simdrivernamenopol"+a).setAttribute("id", "simdrivernamenopol"+ty);
                        document.getElementById("btn_truck"+a).setAttribute("onClick", "findtruk('"+ty+"');");
                        document.getElementById("btn_truck"+a).setAttribute("name", "btn_truck"+ty);
                        document.getElementById("btn_truck"+a).setAttribute("id", "btn_truck"+ty);
                        document.getElementById("covertruk"+a).setAttribute("id", "covertruk"+ty);
                        document.getElementById("tipetruk"+a).setAttribute("name", "tipetruk"+ty);
                        document.getElementById("tipetruk"+a).setAttribute("id", "tipetruk"+ty);                        
                        document.getElementById("qty"+a).setAttribute("onBlur", "javascript:IsNumeric(this);");
                        document.getElementById("qty"+a).setAttribute("name", "qty"+ty);
                        document.getElementById("qty"+a).setAttribute("id", "qty"+ty);
                        document.getElementById("tgl_kirim"+a).setAttribute("onClick", "return showCalendar('tgl_kirim"+ty+"');");
                        document.getElementById("tgl_kirim"+a).setAttribute("name", "tgl_kirim"+ty);
                        document.getElementById("tgl_kirim"+a).setAttribute("id", "tgl_kirim"+ty);                        
                        document.getElementById("kantongdiv"+a).setAttribute("id", "kantongdiv"+ty);
                        document.getElementById("ktproduk"+a).setAttribute("onchange", "ketik_produkkt(this,'"+ty+"');");
                        document.getElementById("ktproduk"+a).setAttribute("name", "ktproduk"+ty);
                        document.getElementById("ktproduk"+a).setAttribute("id", "ktproduk"+ty);
                        document.getElementById("ktnama_produk"+a).setAttribute("name", "ktnama_produk"+ty);
                        document.getElementById("ktnama_produk"+a).setAttribute("id", "ktnama_produk"+ty);
                        document.getElementById("ktuom"+a).setAttribute("name", "ktuom"+ty);
                        document.getElementById("ktuom"+a).setAttribute("id", "ktuom"+ty);                    
                        document.getElementById("ktbtn_produk"+a).setAttribute("onClick", "findprodukkt('"+ty+"');");
                        document.getElementById("ktbtn_produk"+a).setAttribute("name", "ktbtn_produk"+ty);
                        document.getElementById("ktbtn_produk"+a).setAttribute("id", "ktbtn_produk"+ty);                        
                        document.getElementById("val_addnote"+a).setAttribute("name", "val_addnote"+ty);
                        document.getElementById("val_addnote"+a).setAttribute("id", "val_addnote"+ty);
                        document.getElementById("addnote"+a).setAttribute("onClick", "addnote('"+ty+"','val_addnote"+ty+"');");
                        document.getElementById("addnote"+a).setAttribute("name", "addnote"+ty);
                        document.getElementById("addnote"+a).setAttribute("id", "addnote"+ty);                        
                        document.getElementById("kurang"+a).setAttribute("onclick", "return stop_add("+ty+");");
                        document.getElementById("kurang"+a).setAttribute("name", "kurang"+ty);
                        document.getElementById("kurang"+a).setAttribute("id", "kurang"+ty);
                    }                    
                }
            }                        
        }
        
	function stop_add(obj)
	{
	if (j==1){alert("<?php $translatebhsxx->__1('Sorry Minimum 1 Item Request'); ?>..");return false;}
	//k=j;
        //k=k.obj.value;
        k=obj;
            var body1 = document.getElementById("coba");
            var buang = document.getElementById("dd"+k);
        body1.removeChild(buang);
	j=j-1;
	document.tambah.jumlah.value=j;
        if(j>1){
            tukar(obj);
        }
	}
	
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("<?php $translatebhsxx->__1('Required Numbers'); ?>..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("<?php $translatebhsxx->__1('Only Enter Numbers'); ?>...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("<?php $translatebhsxx->__1('Entering Numbers More Than 0'); ?>..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }

function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
 
function getroute(muat) {		
		var strURL="cariroute.php?muat="+muat;
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('routediv').innerHTML=req.responseText;						
					} else {
						alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
   
function ketik_shipto(obj,ke) {
	var strURL="ketik_shipto.php?shipto="+obj.value+"&nourut="+ke;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv"+ke).innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto(ke) {	
		var strURL="cari_shipto.php?nourut="+ke;
		popUp(strURL);
}
function getType(obj,ke){
    var com_produk = document.getElementById("produk"+ke);
    var com_tipetruk = document.getElementById("covertruk"+ke);
    var com_shippingtype = document.getElementById("jenis_kirim");
    var com = document.getElementById("org");
    var strURL="carimaterialtruck.php?org="+com.value+"&materialtruck="+com_produk.value+"&jenis="+com_shippingtype.value+"&urutan="+ke;
    var req = getXMLHTTP();
    if(trim(com_shippingtype.value)!=''){        	
         while(com_tipetruk.hasChildNodes()){
                com_tipetruk.removeChild(com_tipetruk.lastChild);
         }
         //var newdiv=document.createElement("span");
         //newdiv.innerHTML='<select name="tipetruk'+ke+'" id="tipetruk'+ke+'"><option value="303">---303---</option><option value="304">---304---</option></select>';
	 //com_tipetruk.appendChild(newdiv);
                 var jml =com_shippingtype.length;  
                 for (var i=0; i<jml; i++) {
                        if (com_shippingtype.options[i].value == com_shippingtype.value) {
                            com_shippingtype.options[i].setAttribute('selected','selected');
                       }else{
                           com_shippingtype.options[i].setAttribute('disabled','disabled');
                       }
                 } 
         
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('covertruk'+ke).innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
    }else{
        document.getElementById('produk'+ke).value='';
        document.getElementById('nama_produk'+ke).value='';
        document.getElementById('uom'+ke).value='';
        alert("<?php $translatebhsxx->__1('Shipping Type is not null'); ?>");
    }
}
function ketik_produk(obj,ke) {
	var strURL="ketik_produk.php?produk="+obj.value+"&nourut="+ke;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv"+ke).innerHTML=req.responseText;	
                                        getType(this,ke);    
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk(ke) {	
      var com_org = document.getElementById("org");
      var com_shippingtype = document.getElementById("jenis_kirim");
      if(trim(com_shippingtype.value)!=''){     
        if ((com_org.value!="2000")) {
	    var com_plant = document.getElementById("branch_plant");
            if((com_org.value=="6000")){
               var strURL="cari_produk2.php?plant="+com_plant.value+"&nourut="+ke;
            }else{                
                var strURL="cari_produk2.php?nourut="+ke; 
            }
	}else{
            var strURL="cari_produk2.php?nourut="+ke; 
        }
	popUp(strURL);
        getType(this,ke);
    }else{
        document.getElementById('produk'+ke).value='';
        document.getElementById('nama_produk'+ke).value='';
        document.getElementById('uom'+ke).value='';
        alert("<?php $translatebhsxx->__1('Shipping Type is not null');?>");
    }       
}


function ketik_produkkt(obj,ke) {
        var com_plant = document.getElementById("branch_plant");
        if(trim(com_plant.value)!=''){
        var strURL="ketik_kantong.php?produk="+obj.value+"&nourut="+ke+"&plantcode="+com_plant.value; 
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("kantongdiv"+ke).innerHTML=req.responseText;
                                        
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
        }else{
            document.getElementById('ktproduk'+ke).value='';
            document.getElementById('ktnama_produk'+ke).value='';
            document.getElementById('ktuom'+ke).value='';
            alert("<?php $translatebhsxx->__1('Plant is not null'); ?>");
        }    
}
function findprodukkt(ke) {	
    var com_org = document.getElementById("org");
    var com_plant = document.getElementById("branch_plant");
    if(trim(com_plant.value)!=''){ 
        var strURLKT="cari_kantong.php?nourut="+ke+"&plantcode="+com_plant.value; 
        popUp(strURLKT); 
    }else{
            document.getElementById('ktproduk'+ke).value='';
            document.getElementById('ktnama_produk'+ke).value='';
            document.getElementById('ktuom'+ke).value='';
            alert("<?php $translatebhsxx->__1('Plant is not null'); ?>");
    }    
}

function findkapal() {	
    var com = document.getElementById("org");
	var strURL="cari_kapal.php?org="+com.value;
	popUp(strURL);
}
function ketik_kapal(obj) {
        var com = document.getElementById("org");
	var strURL="ketik_kapal.php?org="+com.value+"&kapal="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('kapaldiv').innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function addnote(obj,ke) {	
    //alert(obj+' '+ke);
    var my_string = prompt("<?php $translatebhsxx->__1('Please enter note'); ?>");
    if(my_string.length<=100 ){
        document.getElementById(ke).value=my_string;
    }else{
        document.getElementById(ke).value="";
        alert("<?php $translatebhsxx->__1('Should not exceed 100 characters'); ?>");
    }
}

function findtruk(ke) {	
     var com_org = document.getElementById("org");
     var com_shippingtype = document.getElementById("jenis_kirim");
     if(trim(com_shippingtype.value)!=''){    
        var strURL="cari_truck.php?jenis="+com_shippingtype.value+"&nourut="+ke; 
        popUp(strURL);
     }else{
        document.getElementById('nopol'+ke).value='';
        document.getElementById('tipenopol'+ke).value='';
        document.getElementById('drivernamenopol'+ke).value='';
        document.getElementById('simdrivernamenopol'+ke).value='';
        alert("<?php $translatebhsxx->__1('Shipping Type is not null'); ?>");
    }  
}

function getcaripol(obj,ke) {
        var com = document.getElementById("org");
        var com_shippingtype = document.getElementById("jenis_kirim");
        if(trim(com_shippingtype.value)!=''){   
        var strURL="ceknopol.php?org="+com.value+"&nopolcek="+obj.value+"&jenis="+com_shippingtype.value+"&nourut="+ke;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {		
                                        var mystr = req.responseText;
                                        var myarr = mystr.split("+=");
                                        if(myarr[0]!=''){
                                            document.getElementById("nopol"+ke).value= myarr[0];
                                            document.getElementById("tipenopol"+ke).value= myarr[1];
                                            document.getElementById('drivernamenopol'+ke).value=myarr[2];
                                            document.getElementById('simdrivernamenopol'+ke).value=myarr[3];
                                        }else{
                                            alert("<?php $translatebhsxx->__1('License Plate is not registered'); ?> "+ke);
                                            document.getElementById("nopol"+ke).value='';
                                            document.getElementById("tipenopol"+ke).value= '';
                                            document.getElementById('drivernamenopol'+ke).value='';
                                            document.getElementById('simdrivernamenopol'+ke).value='';
                                        }
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
        }else{
            document.getElementById('nopol'+ke).value='';
            document.getElementById('tipenopol'+ke).value='';
            document.getElementById('drivernamenopol'+ke).value='';
            document.getElementById('simdrivernamenopol'+ke).value='';
            alert("<?php $translatebhsxx->__1('Shipping Type is not null'); ?>");
        }
}

function cektanggal(obj) {
var com_tgl = document.getElementById(obj);
var com_kn = document.getElementById('tglnya');
var tgl = com_tgl.value;
var kn = com_kn.value;
var tgl1 = parseInt(tgl.substr(0,2));
var bln1 = parseInt(tgl.substr(3,2));
var th1 = parseInt(tgl.substr(6,4));
var tglo = bln1+"/"+tgl1+"/"+th1;
var tglx = new Date(tglo);
var tgl2 = parseInt(kn.substr(0,2));
var bln2 = parseInt(kn.substr(3,2));
var th2 = parseInt(kn.substr(6,4));
var tgln = bln2+"/"+tgl2+"/"+th2;
var knx = new Date(tgln);
	if( (tglx >= knx) )
	{
	 com_tgl.value=tgl;
	} else { com_tgl.value=kn; }
}
function trim(str){
	    return str.replace(/^\s+|\s+$/g,'');
}   

function cek_data() {
	var obj = document.getElementById('jumlah');
        var com = document.getElementById("org");
	var cek = obj.value;
        for (var i = 1; i <= cek; i++){	
		if (validasi('Branch Plant','','R','Pembayaran','','R','jenis_kirim','','R','shipto'+i+'','','R','produk'+i+'','','R','qty'+i+'','','RisNum','tgl_kirim'+i+'','','R')) {
                    var nopolf = document.getElementById('nopol'+i+'');
                    if(nopolf.value!=''){
                    
                    var typepol1 = document.getElementById('tipenopol'+i+'');
                    var nilai_typepol1 = typepol1.value;	
		
                    var typepol2 = document.getElementById('tipetruk'+i+'');
                    var nilai_typepol2 = typepol2.value;	
                    var myarrtipe = nilai_typepol2.split("-");
                    if(nilai_typepol1==nilai_typepol2){ 
                                                           
                    }else{
                        if(nilai_typepol1>=myarrtipe[0] && nilai_typepol1<=myarrtipe[1]){
                            
                        }else{
                            alert("<?php $translatebhsxx->__1('Please check type truck');?> "+i);    
                            document.hasil = false;
                            return false;
                        }
                    }
                    }
                }else{
		document.hasil = false;
		return false;
		}		
	}	
 }
function getCreditlimit(){
    var com_sold_to = document.getElementById("sold_to");
    var com_org = document.getElementById('org');	
    var strURL="caricreditlimit.php?org="+com_org.value+"&distr="+com_sold_to.value;
    var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('creditlimit').innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP'); ?>:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}

}   
//-->
</script>

<title>Application SGG Online: <?php $translatebhsxx->__1('Input Order Reservation'); ?> :)</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>

<body onload="getCreditlimit();">
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="ba1"><?php $translatebhsxx->__1('Input Order Reservation'); ?></th>
</tr></table></div>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"><?php $translatebhsxx->__1('Form Input Order Reservation'); ?></th>
</tr>
</table></div>
<div  align="center">
<form  action="komentar.php" method="post" name="tambah" id="tambah" onSubmit="cek_data();return document.hasil">
<table width="600" border="0" class="adminform" align="center">
 <tr>
    <td width="175"><strong><?php $translatebhsxx->__1('Date'); ?></strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2"><?=gmdate("d-m-Y",time()+60*60*7);?><input type="hidden" id="org" name="org" value="<?=$user_org;?>" />
	<input type="hidden" id="tglnya" name="tglnya" value="<?=gmdate("d-m-Y",time()+60*60*7);?>" />
	</td>
 </tr>
<!-- <tr>
    <td width="175"><strong><?php $translatebhsxx->__1('Bank'); ?></strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2"><?
    $databangk=$fungsi->or_infobank($distr_id,$user_org);
    echo $databangk[BANKL]."".$databangk[BANKA]." ".$databangk[BANKN];
    ?></td>
 </tr>-->
<!-- <tr>
    <td width="175"><strong>Credit Limit</strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2"><?=number_format($fungsi->or_creditlimit($distr_id,$user_org),2,'.',',');?>
	</td>
 </tr>  -->
 <tr>
      <td colspan="4"><div id="creditlimit" ></div></td>
 </tr>   
 <tr>
   <td ><strong><?php $translatebhsxx->__1('Order Type'); ?></strong></td>
   <td><strong>:</strong></td>
   <td colspan="2"><select name="so_type" id="so_type" onChange="document.tambah.nama_so_type.value=this.options[this.selectedIndex].title">
		<option value="">---<?php $translatebhsxx->__1('Select Order Type'); ?>---</option>
		<? $fungsi->or_order_type2('ZOR'); ?>     
		</select>	
		<input type="hidden" value="Sales Standart" id="nama_so_type" name="nama_so_type" /></td>
 </tr>
 <tr>
    <td width="175"><strong><?php $translatebhsxx->__1('Plant'); ?> </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2">
<select name="branch_plant" id="branch_plant" onChange="document.tambah.nama_plant.value=this.options[this.selectedIndex].title">
		<option value="">---<?php $translatebhsxx->__1('Select'); ?>---</option>
		<? $fungsi->or_jns_plant2($branch_plant); ?>     
		</select>	
		<input type="hidden" value="" id="nama_plant" name="nama_plant" />
	</td>
	</tr>	
	<tr>
	  <td><strong><?php $translatebhsxx->__1('Payment'); ?></strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
	  <select name="cara_bayar" id="Pembayaran">
		<option value="">---<?php $translatebhsxx->__1('Select Payment'); ?>---</option>
		<? $fungsi->or_bayar($cara_bayar); ?>     
		</select>	
	  </td>
	</tr>
	<tr>
	  <td><strong><?php $translatebhsxx->__1('Shipping Type'); ?></strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2"><select name="jenis_kirim" id="jenis_kirim" onChange="document.tambah.nama_kirim.value=this.options[this.selectedIndex].title;getroute(this.value)">
		<option value="">---<?php $translatebhsxx->__1('Select Shipping'); ?>---</option>
		<? $fungsi->or_jenis_kirim2($jenis_kirim); ?>     
		</select>	
		<input type="hidden" value="" id="nama_kirim" name="nama_kirim" />
	  </td>
	</tr>
	<tr>
	  <td><strong><?php $translatebhsxx->__1('Delivery Route'); ?></strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
              <div id="routediv">
                  <select name="route" id="route">
                    <option value="">---<?php $translatebhsxx->__1('Route'); ?>---</option>
                  </select>
             </div>
	  </td>
	</tr>
    <tr>
	  <td><strong><?php $translatebhsxx->__1('Method of Payment'); ?></strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2"><select name="top" id="top" onChange="document.fsimpan.nama_top.value=this.options[this.selectedIndex].title" disabled required>
		<option value="">---<?php $translatebhsxx->__1('Select TOP'); ?>---</option>
		<option value="0001" title="Cash Payment" selected>0001 - Cash Payment</option>
		<!-- <? $fungsi->or_cara_bayar(''); ?>      -->

		</select>
		<input name="nama_top" type="hidden" id="nama_top" value="" <?=$hanyabaca?> ></td>
	</tr>
    <tr>
	  <td><strong><?php $translatebhsxx->__1('Price List'); ?> </strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2"><select name="pricelist" id="pricelist" onChange="document.fsimpan.nama_pricelist.value=this.options[this.selectedIndex].title;" required>
		<option value="" required>---<?php $translatebhsxx->__1('Select Price List'); ?>---</option>
		<?
            $fungsi->or_pricelist2('');
        ?>
		</select>
		<input name="nama_pricelist" type="hidden" id="nama_pricelist" value="" <?=$hanyabaca?> >
        </td>
	</tr>
<!--	<tr>
	  <td><strong><?php $translatebhsxx->__1('Note'); ?></strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">-->
            <input name="keterangan" type="hidden" class="inputlabel" id="Keterangan" value="<?=$keterangan?>" <?=$hanyabaca?> size="70" maxlength="250"/>  
<!--          </td>
	</tr>-->
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
</table> 
<br/><br/>
<table width="2045" align="center" class="adminlist">
  <tr>
	<th align="left" colspan="5"><?php $translatebhsxx->__1('Item Order Reservation'); ?></th>
  </tr>
</table>
<table width="2045" align="center" class="adminlist">
<tr class="quote">
        <td align="left">&nbsp;</td>
	<td align="left"> <?php $translatebhsxx->__1('Ship To Code/Ship To Name'); ?> </td>
	<td align="left"> <?php $translatebhsxx->__1('Product Code/Product Name'); ?> </td>
        <td align="left"> <?php $translatebhsxx->__1('Bag Code/Bag Name'); ?> </td>
	<td > <?php $translatebhsxx->__1('License Plate/Truck Type/Driver Name/License Driver'); ?></td>
        <td > <?php $translatebhsxx->__1('QTY'); ?> </td>
	<td> <?php $translatebhsxx->__1('Delivery Date'); ?> </td>        
	<td> <?php $translatebhsxx->__1('Note'); ?> </td>
        <td> </td>
</tr>
<tr>
<td align="left"><span id="nodiv1"><strong>1</strong></span></td>    
<td align="left">
    <div id="shiptodiv1">
       <input name="shipto1" type="text" class="inputlabel" id="shipto1" value="<?=$shipto?>" onChange="ketik_shipto(this,'1')" maxlength="12" size="10"/>
       <input name="nama_shipto1" type="text" class="inputlabel" id="nama_shipto1" value="<?=$nama_shipto?>" readonly="true"  size="17"/>
 	  <input type="text" value="" id="alamat1" name="alamat1" size="17" readonly="true" >
	  <input type="hidden" value="" id="kode_distrik1" name="kode_distrik1" >
	  <input type="text" value="" id="nama_distrik1" name="nama_distrik1" size="10" readonly="true" >
	  <input type="hidden" value="" id="kode_prov1" name="kode_prov1" >
	  <input type="hidden" value="" id="nama_prov1" name="nama_prov1" >
      <input name="btn_shipto1" type="button" class="button" id="btn_shipto1" value="..." onClick="findshipto('1')"/>
      <input name="val_error_shipto1" type="hidden" id="val_error_shipto1" value="0" />
    </div>

</td>
<td align="left">
    <div id="produkdiv1">
	<input name="produk1" type="text" class="inputlabel" id="produk1" value="<?=$produk?>" onChange="ketik_produk(this,'1');" maxlength="20" size="12"/>
        <input name="nama_produk1" type="text" class="inputlabel" id="nama_produk1" value="<?=$nama_produk?>" readonly="true"  size="17"/>
        <input name="disuom1" type="text" class="inputlabel" id="disuom1" value="<?=$disuom?>" readonly="true"  size="4"/>
        <input name="uom1" type="hidden" class="inputlabel" id="uom1" value="<?=$uom?>" readonly="true"  size="4"/>
       <input name="btn_produk1" type="button" class="button" id="btn_produk1" value="..." onClick="findproduk('1');"/>
      <input name="val_error_produk1" type="hidden" id="val_error_produk1" value="0" />
    </div>
</td> 
<td align="left">
    <div id="kantongdiv1">
	  <input name="ktproduk1" type="text" class="inputlabel" id="ktproduk1" value="<?=$produkkt?>" onChange="ketik_produkkt(this,'1')" maxlength="20" size="12"/>
          <input name="ktnama_produk1" type="text" class="inputlabel" id="ktnama_produk1" value="<?=$nama_produkkt?>" readonly="true"  size="17"/>
	  <input name="ktuom1" type="text" class="inputlabel" id="ktuom1" value="<?=$uomkt?>" readonly="true"  size="4"/>
          <input name="ktbtn_produk1" type="button" class="button" id="ktbtn_produk1" value="..." onClick="findprodukkt('1')"/>
          <input name="ktval_error_produk1" type="hidden" id="ktval_error_produk1" value="0" />
    </div>
</td>
<td align="left">
    <input type="text" value="" id="nopol1" name="nopol1" size="14" onChange="getcaripol(this,'1');" />
    <input type="text" value="" id="tipenopol1" name="tipenopol1" size="4" readonly="readonly" />
    <input type="text" value="" id="drivernamenopol1" name="drivernamenopol1" size="17" />
    <input type="text" value="" id="simdrivernamenopol1" name="simdrivernamenopol1" size="17"/>
    <input name="btn_truck1" type="button" class="button" id="btn_truck1" value="..." onClick="findtruk('1')"/>
    <span id="covertruk1"></span>
</td>    
<td align="left">
	<input type="text" value="" id="qty1" name="qty1" size="6" maxlength="6" onBlur="javascript:IsNumeric(this)"/>
</td>
<td align="left">
	<input name="tgl_kirim1" type="text" id="tgl_kirim1" size=12 value="<?=date("d-m-Y");?>" onClick="return showCalendar('tgl_kirim1');" readonly="readonly" /></td>
<td align="left">
        <input name="val_addnote1" type="text" id="val_addnote1" value="" size="20"  readonly="readonly" />
	<input type="button" value=".." name="addnote1" id="addnote1" onClick="addnote('1','val_addnote1');" /></td>        
<td width="50">
	<input type="button" value=" + " name="tambah" onClick="return start_add();" />
<!--	<input type="button" value="  -  " name="kurang" onClick="return stop_add();" />-->
</td>
</tr>
</table>
<div id="coba"></div>
</div>
<br>
<br>

<div align="center">
  <table width="600" border="0" align="center" class="adminlist">
	<tr>
      <td colspan="5"><div align="center">
        <input name="save" type="submit" class="button" id="save" value="<?php $translatebhsxx->__1('Save'); ?>" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 		 <input name="action" type="hidden" value="tambah" />
                 <input name="lasthalaman" type="hidden" value="tambahbaru" />
      </div></td>
    </tr>
  </table>
</div>

<input type="hidden" value="1" name="jumlah" id="jumlah" />
<input type="hidden" value="<?=$sold_to?>" name="sold_to" id="sold_to" size="12"/>
<input type="hidden" value="<?=$nama_sold_to?>" name="nama_sold_to" id="nama_sold_to" size="12"/>
</form>


<br/><br/>
<? //include ('ekor.php'); ?>

</body>
</html>
