<?php
//by @liyantanto 2014

include("../include/lib/nusoap.php");
$server = new soap_server();
$server->configureWSDL('wsdlQuery', 'urn:wsdlQuery');

require_once ("autorisasi.php");
$fautoris= new autorisasi();

$server->wsdl->addComplextype(
	'wsdlAntriRequest',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'token' => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'token', 'type' => 'xsd:string'),
		'ORG'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'ORG', 'type' => 'xsd:string'),
		'PLANT'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'PLANT', 'type' => 'xsd:string'),
		'USER'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'USER', 'type' => 'xsd:string'),
		'NOPOL'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NOPOL', 'type' => 'xsd:string'),
		'NO_BOOKING'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NO_BOOKING', 'type' => 'xsd:string'),
	)       
);

$server->wsdl->addComplextype(
	'wsdlDeleteRequest',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'token' => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'token', 'type' => 'xsd:string'),
		'ORG'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'ORG', 'type' => 'xsd:string'),
		'PLANT'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'PLANT', 'type' => 'xsd:string'),
		'USER'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'USER', 'type' => 'xsd:string'),
		'NO_ANTRI'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NO_ANTRI', 'type' => 'xsd:string'),
		'NO_BOOKING'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NO_BOOKING', 'type' => 'xsd:string'),
		'NOPOL'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NOPOL', 'type' => 'xsd:string'),
		'TGL_PREMATCH'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'TGL_PREMATCH', 'type' => 'xsd:string'),
		'STATUS'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'STATUS', 'type' => 'xsd:string'),
		'ALASAN'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'ALASAN', 'type' => 'xsd:string')
	)       
);

$server->wsdl->addComplextype(
	'wsdlIntransitRequest',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'token' => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'token', 'type' => 'xsd:string'),
		'RFiD'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'RFiD', 'type' => 'xsd:string'),
		'USER'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'USER', 'type' => 'xsd:string'),
		'PLANT'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'PLANT', 'type' => 'xsd:string'),
		'ORG'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'ORG', 'type' => 'xsd:string'),
		'statusacttrans'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'statusacttrans', 'type' => 'xsd:string'),
		'statusactn'   => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'statusactn', 'type' => 'xsd:string')
	)       
);

$server->wsdl->addComplextype(
	'wsdlAntriResponse',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'TYPE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TYPE', 'type' => 'xsd:string'),
		'MSG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MSG', 'type' => 'xsd:string'),
		'NOANTRI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NOANTRI', 'type' => 'xsd:string'),
		'NOPOLISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NOPOLISI', 'type' => 'xsd:string'),
		'NMPLAN' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NMPLAN', 'type' => 'xsd:string'),
		'NO_EXPEDITUR' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_EXPEDITUR', 'type' => 'xsd:string'),
		'NAMA_EXPEDITUR' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NAMA_EXPEDITUR', 'type' => 'xsd:string'),
		'CREATE_DATE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'CREATE_DATE', 'type' => 'xsd:string'),
		'CREATE_TIME' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'CREATE_TIME', 'type' => 'xsd:string'),
		'KAPASITAS' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'KAPASITAS', 'type' => 'xsd:string'),
		'TIPE_TRUK' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TIPE_TRUK', 'type' => 'xsd:string'),
		'NO_PO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_PO', 'type' => 'xsd:string'),
		'LINE_PO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LINE_PO', 'type' => 'xsd:string'),
		'ITEM_NO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ITEM_NO', 'type' => 'xsd:string'),
		'ITEM_DESC' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ITEM_DESC', 'type' => 'xsd:string')
		// 'detailData' => array('minOccurs' => '0', 'maxOccurs' => 'unbounded','name' => 'detailData', 'type' => 'tns:DetailData')
	)
);

$server->wsdl->addComplextype(
	'wsdlDeleteResponse',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'TYPE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TYPE', 'type' => 'xsd:string'),
		'MSG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MSG', 'type' => 'xsd:string')
	)
);

$server->wsdl->addComplextype(
	'wsdlIntransitResponse',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'TYPE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TYPE', 'type' => 'xsd:string'),
		'MSG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MSG', 'type' => 'xsd:string'),
		'NOANTRI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NOANTRI', 'type' => 'xsd:string'),
		'NOPOLISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NOPOLISI', 'type' => 'xsd:string'),
		'NMPLAN' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NMPLAN', 'type' => 'xsd:string'),
		'NO_EXPEDITUR' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_EXPEDITUR', 'type' => 'xsd:string'),
		'NAMA_EXPEDITUR' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NAMA_EXPEDITUR', 'type' => 'xsd:string'),
		'CREATE_DATE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'CREATE_DATE', 'type' => 'xsd:string'),
		'CREATE_TIME' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'CREATE_TIME', 'type' => 'xsd:string'),
		'KAPASITAS' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'KAPASITAS', 'type' => 'xsd:string'),
		'TIPE_TRUK' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TIPE_TRUK', 'type' => 'xsd:string')
	)
);

$server->wsdl->addComplextype(
	'wsdlAntriRequest',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'RFiD'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'RFiD', 'type' => 'xsd:string'),
		'USER'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'USER', 'type' => 'xsd:string'),
		'ORG'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'ORG', 'type' => 'xsd:string'),
		'PLANT'  => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'PLANT', 'type' => 'xsd:string'),
		'NOPOL'  => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'NOPOL', 'type' => 'xsd:string')
	)       
);

/*$server->wsdl->addComplexType(
	'DetailData',
	'complexType',
	'struct',
	'sequence',
	'',        
	array(

				  //OUTPUT
		'NoAntri' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NoAntri', 'type' => 'xsd:string'),
		'NoDO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NoDO', 'type' => 'xsd:string')

	)
); */

$server->register('antriGlondong',                 // method name
	array('wsdlAntriRequest' => 'tns:wsdlAntriRequest'),        // input parameters
	array('wsdlAntriResponse' => 'tns:wsdlAntriResponse'),  // output parameters
	'urn:wsdlQuery',                        // namespace
	'urn:wsdlQuery#antriGlondong',                 // soapaction
	'rpc',                                  // style
	'literal',                              // use
	'wsdlQuery Request...'      // documentation
);

$server->register('deleteTrans',                 // method name
	array('wsdlDeleteRequest' => 'tns:wsdlDeleteRequest'),        // input parameters
	array('wsdlDeleteResponse' => 'tns:wsdlDeleteResponse'),  // output parameters
	'urn:wsdlQuery',                        // namespace
	'urn:wsdlQuery#deleteTrans',                 // soapaction
	'rpc',                                  // style
	'literal',                              // use
	'wsdlQuery Request...'      // documentation
);

$server->register('intransitGlondong',                 // method name
	array('wsdlIntransitRequest' => 'tns:wsdlIntransitRequest'),        // input parameters
	array('wsdlIntransitResponse' => 'tns:wsdlIntransitResponse'),  // output parameters
	'urn:wsdlQuery',                        // namespace
	'urn:wsdlQuery#intransitGlondong',                 // soapaction
	'rpc',                                  // style
	'literal',                              // use
	'wsdlQuery Request...'      // documentation
);


// Define the method as a PHP function
// Baru
function getTruck($inqueryRequest){
	$data=array();
	$tampung = array();
	$NOPOL = trim($inqueryRequest['NOPOL']);
	$ORG = trim($inqueryRequest['ORG']);

	$dirr = $_SERVER['PHP_SELF'];  
	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$TYPE = '01';
		$MSG = 'Gagal koneksi ke SAP';
		$responseRequest = array (
			'TYPE' => $TYPE,
			'MSG' => $MSG,
			'detailData' => $dataDetail
		);
	}else{
		$sap->Open ();
		$fce2 = $sap->NewFunction ("Z_ZAPPSD_SELECT_TRUK2");
		
		if ($fce2==false){ ##--- Ketika RFC Tidak ada ---##
			$TYPE = '02';
			$MSG = 'RFC Tidak Ditemukan';
			$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
				'detailData' => $dataDetail
			);
		}else{

			$fce2->XPARAM["NOPOLISI"] =$NOPOL;
			$fce2->XPARAM["STATUS"] = '0';
			$fce2->XDATA_APP["NMORG"] = $ORG;

			$fce2->Call();
			//print_r($fce2);
			if ($fce2->GetStatus() == SAPRFC_OK) {
				$fce2->RETURN_DATA->Reset();
								//Display Tables
								//$i=0;
				
				if ($fce2->RETURN_DATA->Next()) {
					$data["TIPE_TRUK"]=$fce2->RETURN_DATA->row["VEHICLE_TYPE"];
					$data["TIPE_NAME"]=$fce2->RETURN_DATA->row["MODE_OFTRANSPORT"];
					$data["NO_STNK"]=$fce2->RETURN_DATA->row["NOSTNK"];
					$data["NO_RFID"]=$fce2->RETURN_DATA->row["NO_RFID"];
					$data["SYSTEM_BONGKAR"]=$fce2->RETURN_DATA->row["SYSTEM_BONGKAR"];
					$data["NO_EXPEDITUR"]=$fce2->RETURN_DATA->row["NO_EXPEDITUR"];
					$data["NAMA_EXPEDITUR"]=$fce2->RETURN_DATA->row["NAMA_EXPEDITUR"];
					$data["SYSTEM_BONGKAR"]=$fce2->RETURN_DATA->row["SYSTEM_BONGKAR"];
					$data["KAPASITAS"]=$fce2->RETURN_DATA->row["KAPASITAS"]*1;
					$data["WARNA_PLAT"]=$fce2->RETURN_DATA->row["WARNA_PLAT"];
					
				} else{
					$data["TIPE_TRUK"]= "";
					$data["TIPE_NAME"]= "";
					$data["NO_STNK"]= "";
					$data["NO_RFID"]= "";
					$data["SYSTEM_BONGKAR"]= "";
					$data["NO_EXPEDITUR"]= "";
					$data["NAMA_EXPEDITUR"]= "";
					$data["SYSTEM_BONGKAR"]= "";
					$data["KAPASITAS"]= "";
					$data["WARNA_PLAT"]= "";
					
					$data["TYPE"] =  $fce2->RETURN["TYPE"];
					$data["MSG"] = "Truck : ".$fce2->RETURN["MESSAGE"];
					
				}
				$tampung = $data;


			}

			$fce2->Close();  
			// $sap->Close();  
			
		}

	}
	//echo json_encode($tampung); exit();
	return $tampung;
}

function getdata($org,$plant,$no_trans){

	$dirr = $_SERVER['PHP_SELF'];  
	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$TYPE = '01';
		$MSG = 'Gagal koneksi ke SAP';
		$responseRequest = array (
			'TYPE' => $TYPE,
			'MSG' => $MSG,
			'detailData' => $dataDetail
		);
	}else{
		$sap->Open ();
		$fce2 = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
		
		if ($fce2==false){ ##--- Ketika RFC Tidak ada ---##
			$TYPE = '02';
			$MSG = 'RFC Tidak Ditemukan';
			$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
				'detailData' => $dataDetail
			);
		}else{

			// echo $plant." - ".$org." - ".sprintf('%010s',$no_trans);

			$fce2->X_TGL1 = date("Ymd");
			$fce2->X_TGL2 = date("Ymd");
			$fce2->X_WERKS = $plant;
			$fce2->X_VKORG = $org;
			$fce2->X_STATUS = "40";

			$fce2->LR_NO_TRANSAKSI->row['SIGN'] = "I";
			$fce2->LR_NO_TRANSAKSI->row['OPTION'] = "EQ";
			$fce2->LR_NO_TRANSAKSI->row['LOW'] = sprintf('%010s',$no_trans);
			$fce2->LR_NO_TRANSAKSI->row['HIGH'] = "";
			$fce2->LR_NO_TRANSAKSI->Append($fce2->LR_NO_TRANSAKSI->row);

			$fce2->Call();
			//print_r($fce2);
			if ($fce2->GetStatus() == SAPRFC_OK) {
				$fce2->ZDATA->Reset();
				while ($fce2->ZDATA->Next()) {


					$data["NO_EXPEDITUR"] = $fce2->ZDATA->row["NO_EXPEDITUR"];
					$data["NAMA_EXPEDITUR"] = $fce2->ZDATA->row["NAMA_EXPEDITUR"];
					$data["TGL_ANTRI"] = $fce2->ZDATA->row["TGL_ANTRI"];
					$data["JAM_ANTRI"] = $fce2->ZDATA->row["JAM_ANTRI"];
					$data["KAPASITAS"] = (int)($fce2->ZDATA->row["KAPASITAS"]);
					$data["TIPE_TRUK"] = $fce2->ZDATA->row["TIPE_TRUK"];
					$data["NO_PO"]= trim($fce2->ZDATA->row["NO_PO"]);
					$data["LINE_PO"]= trim($fce2->ZDATA->row["LINE_SO"]);
					$data["ITEM_NO"]= trim($fce2->ZDATA->row["ITEM_NO"]);
					$data["ITEM_DESC"]= trim($fce2->ZDATA->row["PRODUK"]);
					$data["NAMA_SOPIR"]= trim($fce2->ZDATA->row["NAMA_SOPIR"]);
					$data["NO_KONVEYOR"]= trim($fce2->ZDATA->row["LSTEL"]);
				}
			   
				$tampung = $data;


			}

			$fce2->Close();  
			// $sap->Close();  
			
		}

	}
	//echo json_encode($tampung); exit();
	return $tampung;
}

function antri($inqueryRequest){
	$data=array();
	$tampung = array();
	$dataget = array();
	$NOPOL = trim($inqueryRequest['NOPOL']);
	$USER = trim($inqueryRequest['USER']);
	$PLANT = trim($inqueryRequest['PLANT']);
	$ORG = trim($inqueryRequest['ORG']);
	$NO_BOOKING = trim($inqueryRequest['NO_BOOKING']);
	$dataget = $inqueryRequest;

	if ($NOPOL == '' || $USER == '' || $PLANT == '' || $ORG == '') {
		$TYPE = '03';
		$MSG = 'Parameter tidak lengkap';
		$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
		);
		return $responseRequest;
	}

	$data_truk = getTruck($inqueryRequest);
	if ($data_truk['NO_RFID']=='') {
		$TYPE = '03';
		$MSG = 'NOPOL belum terdaftar / Tidak ada RFID / ada Transaksi Outstanding';
		$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
		);
		return $responseRequest;
	}

	$RFiD = $data_truk['NO_RFID'];
	
	$dirr = $_SERVER['PHP_SELF'];  
	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$TYPE = '01';
		$MSG = 'Gagal koneksi ke SAP';
	   $responseRequest = array (
		   'TYPE' => $TYPE,
		   'MSG' => $MSG
	   );
	   return $responseRequest;
	} else {
		$sap->Open ();

		if($PLANT=='2403' or $PLANT=='7403'){
			$fce = $sap->NewFunction ("Z_ZAPPSD_GLONDONG");
		
			if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
				$TYPE = '02';
				$MSG = 'RFC Tidak Ditemukan';
				$responseRequest = array (
				   'TYPE' => $TYPE,
				   'MSG' => $MSG
			   );
			   return $responseRequest;
			}else{

				$fce->I_NMORG = strtoupper($ORG);
				$fce->I_PLANT = strtoupper($PLANT);
				$fce->I_PTGS_ANTRIAN = strtoupper($USER);
				$fce->I_RFID = strtoupper($RFiD);
				$fce->I_ORGDESC = trim(getNmOrganisasi(strtoupper($ORG)));
				$fce->XFLAG = 'X'; // block 2000
				$fce->I_NO_BOOKING = $NO_BOOKING;
				// $fce->I_MAX_LOOP1 = "30";
				// $fce->I_DELAY1 = "0.3";
				// $fce->I_MAX_LOOP2 = "20";
				// $fce->I_DELAY2 = "0.1";
				$fce->Call();                
				
				//print_r($fce2);
				if ($fce->GetStatus() == SAPRFC_OK) {

					//Display Tables
					$dataget["TYPE"] = 'S';
					$dataget["MSG"] = 'Antri success..!';

					$no_trans = $dataget["NOANTRI"] = $fce->T_OUTPUT["NO_TRANSAKSI"];
					$dataget["NOPOLISI"] = $fce->T_OUTPUT["NO_POLISI"];
					$plant = $dataget["NMPLAN"] = $fce->T_OUTPUT["NMPLAN"];

					$org = trim($fce->T_OUTPUT["NMORG"]);

					$data_dtl = getdata($org,$plant,$no_trans);
					// echo "<pre>";
					// print_r($data_dtl);

					$dataget["NO_EXPEDITUR"] = $data_dtl["NO_EXPEDITUR"];
					$dataget["NAMA_EXPEDITUR"] = $data_dtl["NAMA_EXPEDITUR"];
					$dataget["CREATE_DATE"] = $data_dtl["TGL_ANTRI"];
					$dataget["CREATE_TIME"] = $data_dtl["JAM_ANTRI"];
					$dataget["KAPASITAS"] = (int)($data_dtl["KAPASITAS"]);
					$dataget["TIPE_TRUK"] = $data_dtl["TIPE_TRUK"];
					$dataget["NO_PO"]= trim($data_dtl["NO_PO"]);
					$dataget["LINE_PO"]= trim($data_dtl["LINE_PO"]);
					$dataget["ITEM_NO"]= trim($data_dtl["ITEM_NO"]);
					$dataget["ITEM_DESC"]= trim($data_dtl["ITEM_DESC"]);
					$dataget["NAMA_SOPIR"]= trim($data_dtl["NAMA_SOPIR"]);
					$dataget["NO_KONVEYOR"]= trim($data_dtl["NO_KONVEYOR"]);

					// $dataget["NO_EXPEDITUR"] = $fce->T_OUTPUT["NO_EXPEDITUR"];
					// $dataget["NAMA_EXPEDITUR"] = $fce->T_OUTPUT["NAMA_EXPEDITUR"];
					// $dataget["CREATE_DATE"] = $fce->T_OUTPUT["TGL_ANTRI"];
					// $dataget["CREATE_TIME"] = $fce->T_OUTPUT["JAM_ANTRI"];
					// $dataget["KAPASITAS"] = (int)($fce->T_OUTPUT["KAPASITAS"]);
					// $dataget["TIPE_TRUK"] = $fce->T_OUTPUT["TIPE_TRUK"];
					// $dataget["NO_PO"]= trim($fce->T_OUTPUT_DTL["NO_PO"]);
					// $dataget["LINE_PO"]= trim($fce->T_OUTPUT_DTL["LINE_PO"]);
					// $dataget["ITEM_NO"]= trim($fce->T_OUTPUT_DTL["ITEM_NO"]);
					// $dataget["ITEM_DESC"]= trim($fce->T_OUTPUT_DTL["ITEM_DESC"]);

					// echo "<pre>";
					// print_r($fce->T_OUTPUT_DTL);
					// exit();

					//display error
					if (trim($fce->RETURN["TYPE"]) != 'S') {
						$dataget["TYPE"] = trim($fce->RETURN["TYPE"]);
						$dataget["MSG"] = $fce->RETURN["MESSAGE"];
					} else {
						$pre = Prematch($dataget);
					}
				   
				}
			}
		} else if($PLANT=='2405' or $PLANT=='7405'){

			$fce = $sap->NewFunction ("Z_ZAPPSD_TRUK_ANTRI_RFID2");
		
			if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
				$TYPE = '02';
				$MSG = 'RFC Tidak Ditemukan';
			}else{
				$fce->I_NMORG = strtoupper($ORG);
				$fce->I_NMPLAN = strtoupper($PLANT);
				$fce->I_PTGS_ANTRIAN = strtoupper($USER);
				$fce->I_RFID = strtoupper($RFiD);
				$fce->XFLAG = 'X'; // block 2000
				$fce->XFLAG2 = 'X'; // update status = 25
				$fce->Call();
				//baca return value
				if ($fce->GetStatus() == SAPRFC_OK) {
					//Display Tables
					$dataget["TYPE"] = 'S';
					$dataget["MSG"] = $fce->RETURN["MESSAGE"];//'Antri success..!';

					$no_trans = $dataget["NOANTRI"] = $fce->T_OUTPUT["NO_TRANSAKSI"];
					$dataget["NOPOLISI"] = $fce->T_OUTPUT["NO_POLISI"];
					$plant = $dataget["NMPLAN"] = $fce->T_OUTPUT["NMPLAN"];

					$org = trim($fce->T_OUTPUT["NMORG"]);

					$data_dtl = getdata($org,$plant,$no_trans);
					// echo "<pre>";
					// print_r($data_dtl);

					$dataget["NO_EXPEDITUR"] = $data_dtl["NO_EXPEDITUR"];
					$dataget["NAMA_EXPEDITUR"] = $data_dtl["NAMA_EXPEDITUR"];
					$dataget["CREATE_DATE"] = $data_dtl["TGL_ANTRI"];
					$dataget["CREATE_TIME"] = $data_dtl["JAM_ANTRI"];
					$dataget["KAPASITAS"] = (int)($data_dtl["KAPASITAS"]);
					$dataget["TIPE_TRUK"] = $data_dtl["TIPE_TRUK"];
					$dataget["NO_PO"]= trim($data_dtl["NO_PO"]);
					$dataget["LINE_PO"]= trim($data_dtl["LINE_PO"]);
					$dataget["ITEM_NO"]= trim($data_dtl["ITEM_NO"]);
					$dataget["ITEM_DESC"]= trim($data_dtl["ITEM_DESC"]);
					$dataget["NAMA_SOPIR"]= trim($data_dtl["NAMA_SOPIR"]);
					$dataget["NO_KONVEYOR"]= trim($data_dtl["NO_KONVEYOR"]);


					// $dataget["NOANTRI"] = $fce->DATA_RETURN["NOANTRI"];
					// $dataget["NOPOLISI"] = $fce->DATA_RETURN["NOPOLISI"];
					// $dataget["NMPLAN"] = $fce->DATA_RETURN["NMPLAN"];
					// $dataget["NO_EXPEDITUR"] = $fce->DATA_RETURN["NO_EXPEDITUR"];
					// $dataget["NAMA_EXPEDITUR"] = $fce->DATA_RETURN["NAMA_EXPEDITUR"];
					// $dataget["CREATE_DATE"] = $fce->DATA_RETURN["CREATE_DATE"];
					// $dataget["CREATE_TIME"] = $fce->DATA_RETURN["CREATE_TIME"];
					// $dataget["KAPASITAS"] = (int)($fce->DATA_RETURN["KAPASITAS"]);
					// $dataget["TIPE_TRUK"] = $fce->DATA_RETURN["TIPE_TRUK"];
					// $dataget["NO_PO"]= trim($fce->T_OUTPUT_DTL["NO_PO"]);
					// $dataget["LINE_PO"]= trim($fce->T_OUTPUT_DTL["LINE_PO"]);
					// $dataget["ITEM_NO"]= trim($fce->T_OUTPUT_DTL["ITEM_NO"]);
					// $dataget["ITEM_DESC"]= trim($fce->T_OUTPUT_DTL["ITEM_DESC"]);

					//display error
					if (trim($fce->RETURN["TYPE"]) != 'S') {
						$dataget["TYPE"] = trim($fce->RETURN["TYPE"]);
						$dataget["MSG"] = $fce->RETURN["MESSAGE"];
					}  else {
						// $pre = Prematch($dataget);
					}
				}
			}
		} else {
			$fce = $sap->NewFunction ("Z_ZAPPSD_TRUK_ANTRI_RFID2");
		
			if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
				$TYPE = '02';
				$MSG = 'RFC Tidak Ditemukan';
			}else{
				$fce->I_NMORG = strtoupper($ORG);
				$fce->I_NMPLAN = strtoupper($PLANT);
				$fce->I_PTGS_ANTRIAN = strtoupper($USER);
				$fce->I_RFID = strtoupper($RFiD);
				$fce->XFLAG = 'X'; // block 2000
				$fce->Call();
				//baca return value
				if ($fce->GetStatus() == SAPRFC_OK) {
					//Display Tables
					$dataget["TYPE"] = 'S';
					$dataget["MSG"] = $fce->RETURN["MESSAGE"];//'Antri success..!';


					$no_trans = $dataget["NOANTRI"] = $fce->T_OUTPUT["NO_TRANSAKSI"];
					$dataget["NOPOLISI"] = $fce->T_OUTPUT["NO_POLISI"];
					$plant = $dataget["NMPLAN"] = $fce->T_OUTPUT["NMPLAN"];

					$org = trim($fce->T_OUTPUT["NMORG"]);

					$data_dtl = getdata($org,$plant,$no_trans);
					// echo "<pre>";
					// print_r($data_dtl);

					$dataget["NO_EXPEDITUR"] = $data_dtl["NO_EXPEDITUR"];
					$dataget["NAMA_EXPEDITUR"] = $data_dtl["NAMA_EXPEDITUR"];
					$dataget["CREATE_DATE"] = $data_dtl["TGL_ANTRI"];
					$dataget["CREATE_TIME"] = $data_dtl["JAM_ANTRI"];
					$dataget["KAPASITAS"] = (int)($data_dtl["KAPASITAS"]);
					$dataget["TIPE_TRUK"] = $data_dtl["TIPE_TRUK"];
					$dataget["NO_PO"]= trim($data_dtl["NO_PO"]);
					$dataget["LINE_PO"]= trim($data_dtl["LINE_PO"]);
					$dataget["ITEM_NO"]= trim($data_dtl["ITEM_NO"]);
					$dataget["ITEM_DESC"]= trim($data_dtl["ITEM_DESC"]);
					$dataget["NAMA_SOPIR"]= trim($data_dtl["NAMA_SOPIR"]);
					$dataget["NO_KONVEYOR"]= trim($data_dtl["NO_KONVEYOR"]);

					// $dataget["NOANTRI"] = $fce->DATA_RETURN["NOANTRI"];
					// $dataget["NOPOLISI"] = $fce->DATA_RETURN["NOPOLISI"];
					// $dataget["NMPLAN"] = $fce->DATA_RETURN["NMPLAN"];
					// $dataget["NO_EXPEDITUR"] = $fce->DATA_RETURN["NO_EXPEDITUR"];
					// $dataget["NAMA_EXPEDITUR"] = $fce->DATA_RETURN["NAMA_EXPEDITUR"];
					// $dataget["CREATE_DATE"] = $fce->DATA_RETURN["CREATE_DATE"];
					// $dataget["CREATE_TIME"] = $fce->DATA_RETURN["CREATE_TIME"];
					// $dataget["KAPASITAS"] = (int)($fce->DATA_RETURN["KAPASITAS"]);
					// $dataget["TIPE_TRUK"] = $fce->DATA_RETURN["TIPE_TRUK"];
					// $dataget["NO_PO"]= trim($fce->T_OUTPUT_DTL["NO_PO"]);
					// $dataget["LINE_PO"]= trim($fce->T_OUTPUT_DTL["LINE_PO"]);
					// $dataget["ITEM_NO"]= trim($fce->T_OUTPUT_DTL["ITEM_NO"]);
					// $dataget["ITEM_DESC"]= trim($fce->T_OUTPUT_DTL["ITEM_DESC"]);

					//display error
					if (trim($fce->RETURN["TYPE"]) != 'S') {
						$dataget["TYPE"] = trim($fce->RETURN["TYPE"]);
						$dataget["MSG"] = $fce->RETURN["MESSAGE"];
					}  else {
						// $pre = Prematch($dataget);
					}
				}   
			}
		}

		$fce->Close();
		// $sap->Close();
		
	}

	$dataget["MSG"] .= "  -  Prematch ".$pre['response'];
	$tampung = $dataget;	

	return $tampung;
}

function getEpoool($inqueryRequest)
{
		$ORG = trim($inqueryRequest['ORG']);
		$PLANT = trim($inqueryRequest['PLANT']);
		$NO_BOOKING = trim($inqueryRequest['NO_BOOKING']);
		$TGL_PREMATCH = trim($inqueryRequest['TGL_PREMATCH']);
		$NOPOL = trim($inqueryRequest['NOPOL']);
		$USER = trim($inqueryRequest['USER']);

	require_once ("../include/sapclasses/sap.php");

		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) {
		    $sap->Open ();
		}
		if ($sap->GetStatus() != SAPRFC_OK ) {
		    echo $sap->PrintStatus();
		    exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_SOVSTRUK");
		if ($fce == false ) { 
		    $sap->PrintStatus(); 
		    exit; 
		}else{

		    $fce->I_NMORG = $ORG;
		    $fce->I_PLANT = $PLANT;
		    $fce->I_VIEW = "X";
		    $fce->I_NO_BOOKING = $NO_BOOKING;
		    $fce->I_NOPOL = $NOPOL;
		    $fce->LR_DATE->row["SIGN"] = "I"; 
            $fce->LR_DATE->row["OPTION"] = "BT";
            $fce->LR_DATE->row["LOW"] = date("Ymd",strtotime($TGL_PREMATCH));
            $fce->LR_DATE->row["HIGH"] = date("Ymd",strtotime($TGL_PREMATCH));
            $fce->LR_DATE->Append($fce->LR_DATE->row);

		    $fce->Call();
		    $data = array();
		    if ($fce->GetStatus() == SAPRFC_OK ) {		
		        $fce->T_DATA->Reset();
                //Display Tables
                while($fce->T_DATA->Next()) {
                    $data["ORG"]  = $fce->T_DATA->row["ORG"];
                    $data["PLANT"]  = $fce->T_DATA->row["PLANT"];
                    $data["EDATU"]  = $fce->T_DATA->row["EDATU"];
                    $data["NO_PO"] = $data["NO_SO"]  = $fce->T_DATA->row["NO_SO"];
                    $data["LINE_PO"] = $data["POSNR"]  = $fce->T_DATA->row["POSNR"];
                    $data["NO_EXPEDITUR"] = $data["EXPEDITUR"]  = $fce->T_DATA->row["EXPEDITUR"];
                    $data["NOPOL"] = $data["NOPOLISI"]  = $fce->T_DATA->row["NOPOLISI"];
                    $data["IDDATETIME"]  = $fce->T_DATA->row["IDDATETIME"];
                    $data["QTY_SO"]  = $fce->T_DATA->row["QTY_SO"];
                    $data["SALES_UNIT"] = $fce->T_DATA->row["SALES_UNIT"];
                    $data["VEHICLE_TYPE"]  = $fce->T_DATA->row["VEHICLE_TYPE"];
                    $data["DELETE_MARK"]  = $fce->T_DATA->row["DELETE_MARK"];
                    $data["UPDATE_BY"]  = $fce->T_DATA->row["UPDATE_BY"];
                    $data["CREATE_DATE"] = $data["UPDATE_DATE"]  = $fce->T_DATA->row["UPDATE_DATE"];
                    $data["CREATE_TIME"] = $data["UPDATE_TIME"]  = $fce->T_DATA->row["UPDATE_TIME"];
                    $data["STATUS"]  = $fce->T_DATA->row["STATUS"];
                    $data["KETERANGAN"]  = $fce->T_DATA->row["KETERANGAN"];
                    $data['NAMA_SOPIR'] = $data["NAMA_SUPIR"]  = $fce->T_DATA->row["NAMA_SUPIR"];
                    $data['NOANTRI'] = $data["NO_TRANSAKSI"]  = $fce->T_DATA->row["NO_TRANSAKSI"];
                    $data["NO_BOOKING"]  = $fce->T_DATA->row["NO_BOOKING"];
                    $data["NO_KONVEYOR"]  = $fce->T_DATA->row["NO_KONVEYOR"];
                    $data["JAM_ANTRI"]  = $fce->T_DATA->row["JAM_ANTRI"];
                    $data["GROUP_ANTRI"]  = $fce->T_DATA->row["GROUP_ANTRI"];
                    $data["JAM_ANTRI_END"]  = $fce->T_DATA->row["JAM_ANTRI_END"];
                    
                    //
                    $data["NMWERKS"]  = $fce->T_DATA->row["NMWERKS"];
                    $data["EXP_NAME"]  = $fce->T_DATA->row["EXP_NAME"];
                    $data["DISTRIK"]  = $fce->T_DATA->row["DISTRIK"];
                    $data["KOTA_NAME"]  = $fce->T_DATA->row["KOTA_NAME"];
                    $data["SOLD_TO_CODE"]  = $fce->T_DATA->row["SOLD_TO_CODE"];
                    $data["SOLD_TO_PARTY"]  = $fce->T_DATA->row["SOLD_TO_PARTY"];
                    $data["SHIP_TO_CODE"]  = $fce->T_DATA->row["SHIP_TO_CODE"];
                    $data["SHIP_TO_PARTY"]  = $fce->T_DATA->row["SHIP_TO_PARTY"];
                    $data["ALAMAT"]  = $fce->T_DATA->row["ALAMAT"];
                    $data["VKBUR"]  = $fce->T_DATA->row["VKBUR"];
                    $data["ITEM_NO"]  = $fce->T_DATA->row["ITEM_NO"];
                    $data["ITEM_DESC"]  = $fce->T_DATA->row["ITEM_DESC"];
                    $data["TIPE_SO"]  = $fce->T_DATA->row["SO_TYPE"];
                    $data["INCOTERM"]  = $fce->T_DATA->row["INCOTERM"];
                    $data["ROUTE"]  = $fce->T_DATA->row["ROUTE"];
                    $data["PALLET"]  = $fce->T_DATA->row["PALLET"];
                    $data["PLTYP"]  = $fce->T_DATA->row["PLTYP"];
                    $data["STATUS_TRUCK"]  = $fce->T_DATA->row["STATUS_TRUCK"];
                    $data["TIPE_TRUK"]  = $fce->T_DATA->row["TIPE_TRUK"];
                    $data["EDATUSO"]  = $fce->T_DATA->row["EDATUSO"];
                    $data["QTY_SO_UOM"]  = $fce->T_DATA->row["QTY_SO_UOM"];
                    $data["QTY_SO_KG"]  = $fce->T_DATA->row["QTY_SO_KG"];
                    $data["PROD_HIERARCHY"]  = $fce->T_DATA->row["PROD_HIERARCHY"];
                    $data["NO_SIM"]  = $fce->T_DATA->row["NO_SIM"];
                    $data["NO_RFID"]  = $fce->T_DATA->row["NO_RFID"];
                    $data["NO_STNK"]  = $fce->T_DATA->row["NO_STNK"];      
                    $data["NO_CONVEYOR"]  = $fce->T_DATA->row["NO_KONVEYOR"];
                    $data["JAM_ANTRI"]  = $fce->T_DATA->row["JAM_ANTRI"];
                    $data["JAM_ANTRI_END"]  = $fce->T_DATA->row["JAM_ANTRI_END"];
                    $data["TYPE"] = "S";
                }

                $data["USER"] = $USER;

		    }else{
		        $fce->PrintStatus();
		    }
		    
		}
		return $data;
}

function Prematch($dataget, $delete = "0", $inqueryRequest = array())
{

	// echo "<pre>";
	// print_r($dataget);
	// exit();
	if ($dataget["TYPE"]=="S") {	

		require_once ("../include/sapclasses/sap.php");

		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) {
		    $sap->Open ();
		}
		if ($sap->GetStatus() != SAPRFC_OK ) {
		    echo $sap->PrintStatus();
		    exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_SOVSTRUK");
		if ($fce == false ) { 
		    $sap->PrintStatus(); 
		    exit; 
		}else{

			if (count($inqueryRequest)>0) {
				$ORG = trim($inqueryRequest['ORG']);
				$PLANT = trim($inqueryRequest['PLANT']);
				$NO_BOOKING = trim($inqueryRequest['NO_BOOKING']);
				$TGL_PREMATCH = trim($inqueryRequest['TGL_PREMATCH']);
				$NOPOL = trim($inqueryRequest['NOPOL']);
				$USER = trim($inqueryRequest['USER']);
				
				$fce->I_NMORG = $ORG;
			    $fce->I_PLANT = $PLANT;
			    $fce->I_VIEW = "X";
			    $fce->I_NO_BOOKING = $NO_BOOKING;
			    $fce->I_NOPOL = $NOPOL;
			    $fce->LR_DATE->row["SIGN"] = "I"; 
	            $fce->LR_DATE->row["OPTION"] = "BT";
	            $fce->LR_DATE->row["LOW"] = date("Ymd",strtotime($TGL_PREMATCH));
	            $fce->LR_DATE->row["HIGH"] = date("Ymd",strtotime($TGL_PREMATCH));
	            $fce->LR_DATE->Append($fce->LR_DATE->row);

			}

		    $fce->I_NMORG = $dataget['ORG'];
		    $fce->I_SO_EPOOL = "X";
		    $fce->I_NO_TRAYEK = "X";
		    $fce->I_SKIP_SO = "X";
		    $fce->I_SKIP_SISA_SO = "X";
		    $fce->T_INSERT->row['ORG'] = $dataget['ORG'];
		    $fce->T_INSERT->row['PLANT'] = $dataget['PLANT'];
		    $fce->T_INSERT->row['EDATU'] = $dataget["CREATE_DATE"];
		    $fce->T_INSERT->row['IDDATETIME'] = $dataget["CREATE_TIME"];
		    $fce->T_INSERT->row['NO_SO'] = $dataget["NO_PO"];
		    $fce->T_INSERT->row['POSNR'] = $dataget["LINE_PO"];
		    $fce->T_INSERT->row['EXPEDITUR'] = $dataget["NO_EXPEDITUR"];
		    $fce->T_INSERT->row['NOPOLISI'] = $dataget["NOPOL"];
		    $fce->T_INSERT->row['QTY_SO'] = $dataget["KAPASITAS"];
		    $fce->T_INSERT->row['SALES_UNIT'] = "TO";
		    $fce->T_INSERT->row['VEHICLE_TYPE'] = $dataget['TIPE_TRUK'];
		    $fce->T_INSERT->row['DELETE_MARK'] = $delete;
		    $fce->T_INSERT->row['UPDATE_BY'] = $dataget['USER'];
		    $fce->T_INSERT->row['UPDATE_DATE'] = $dataget["CREATE_DATE"];
		    $fce->T_INSERT->row['UPDATE_TIME'] = $dataget["CREATE_TIME"];
		    $fce->T_INSERT->row['NAMA_SUPIR'] = $dataget['NAMA_SOPIR'];
		    $fce->T_INSERT->row['NO_TRANSAKSI'] = $dataget['NOANTRI'];		    
		    $fce->T_INSERT->row['NO_BOOKING'] = $dataget['NO_BOOKING'];
		    $fce->T_INSERT->row['NO_KONVEYOR'] = $dataget['NO_KONVEYOR'];
		    $fce->T_INSERT->row['JAM_ANTRI'] = $dataget["CREATE_DATE"];
		    $fce->T_INSERT->row['GROUP_ANTRI'] = "";
		    $fce->T_INSERT->row['JAM_ANTRI_END'] = $dataget["CREATE_TIME"];
		    $fce->T_INSERT->row['STATUS'] = "1";

		    if ($delete=="1") {
		    	$ALASAN = trim($dataget['ALASAN']);
	        	$fce->T_INSERT->row['KETERANGAN'] = $ALASAN;
		    }
		    
		    
		    $fce->T_INSERT->Append($fce->T_INSERT->row);

		    $fce->Call();
		    $data = array();
		    if ($fce->GetStatus() == SAPRFC_OK ) {		
		        $fce->RETURN->Reset();
		        $i=0;
		        while ($fce->RETURN->Next()) {
		            $data['return']=$fce->RETURN->row;
		            // $data[$i]['t_insert']=$fce->T_INSERT->row;
		            $data['response']=$fce->RETURN->row['MESSAGE'];
		            $i++;
		        }
		    }else{
		        $fce->PrintStatus();
		    }
		    
		}
		return $data;
	}
}

function intransit($inqueryRequest){
	$data=array();
	$tampung = array();
	$RFiD = trim($inqueryRequest['RFiD']);
	$USER = trim($inqueryRequest['USER']);
	$PLANT = trim($inqueryRequest['PLANT']);
	$ORG = trim($inqueryRequest['ORG']);
	$statusacttrans = trim($inqueryRequest['statusacttrans']);
	$statusactn = trim($inqueryRequest['statusactn']);
	
	$dirr = $_SERVER['PHP_SELF'];  
	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$TYPE = '01';
		$MSG = 'Gagal koneksi ke SAP';
//        $responseRequest = array (
//            'TYPE' => $TYPE,
//            'MSG' => $MSG,
//            'detailData' => $dataDetail
//        );
	} else {
		$sap->Open ();
		$fce = $sap->NewFunction ("Z_ZAPPSD_CPINTRANS_UPD_RFID");
		if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
			$TYPE = '02';
			$MSG = 'RFC Tidak Ditemukan';
		}else{
			$fce->I_NMORG = strtoupper($ORG);
			$fce->I_PLANT = strtoupper($PLANT);                
			$fce->I_PETUGAS_CEK = strtoupper($USER);
			$fce->I_RFID = strtoupper($RFiD);//sprintf('009999', "%06s");
			$fce->I_STATUS_TRANS_FROM = strtoupper($statusacttrans);
			$fce->I_STATUS_ACTION = strtoupper($statusactn);
			$fce->I_TANGGAL_CEK = date("Ymd");
			$fce->I_JAM_CEK = date("His");
			$fce->Call();
			//baca return value
			if ($fce->GetStatus() == SAPRFC_OK) {
				 $fce->T_DATA->Reset();
				//Display Tables
				//display error
				$dataget["TYPE"]= trim($fce->MESSAGE["TYPE"]);
				if ($dataget["TYPE"] == 'E') {
					$dataget = array();
					$dataget["MESSAGE"] = 'Intransit Failed!';//$fce->MESSAGE["MESSAGE"];
				}else{
					while ($fce->T_DATA->Next()) {  
						$dataget["MESSAGE"] = 'Intransit Success!';
						$dataget["NOANTRI"] = $fce->T_DATA->row["NO_TRANSAKSI"];
						$dataget["NOPOLISI"] = $fce->T_DATA->row["NO_POLISI"];
						$dataget["NMPLAN"] = $fce->T_DATA->row["NMPLAN"];
						$dataget["NO_EXPEDITUR"] = $fce->T_DATA->row["NO_EXPEDITUR"];
						$dataget["NAMA_EXPEDITUR"] = $fce->T_DATA->row["NAMA_EXPEDITUR"];
						$dataget["CREATE_DATE"] = date("Ymd");//$fce->T_DATA["CREATE_DATE"];
						$dataget["CREATE_TIME"] = date("His");//$fce->T_DATA["CREATE_TIME"];
						$dataget["KAPASITAS"] = (int)($fce->T_DATA->row["KAPASITAS"]);
						$dataget["TIPE_TRUK"] = $fce->T_DATA->row["TIPE_TRUK"];                
					}
				}
			}
			$fce->Close();
			$dsap->Close();  
		}
	}
	
	$tampung= $dataget;

	return $tampung;
}


function deleteAntri($inqueryRequest) {
	$data=array();
	$tampung = array();

	$ORG = trim($inqueryRequest['ORG']);
	$PLANT = trim($inqueryRequest['PLANT']);
	$NO_ANTRI = trim($inqueryRequest['NO_ANTRI']);
	$USER = trim($inqueryRequest['USER']);
	$STATUS = trim($inqueryRequest['STATUS']);
	$ALASAN = trim($inqueryRequest['ALASAN']);
	$NO_BOOKING = trim($inqueryRequest['NO_BOOKING']);
	$TGL_PREMATCH = trim($inqueryRequest['TGL_PREMATCH']);
	$NOPOL = trim($inqueryRequest['NOPOL']);

	if ($ALASAN=="") {
		$data["TYPE"] = "02";
		$data["MSG"] = "Harus isi alasan!";
		return $data;
	}

	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$ResponseCode = '01';
		$ResponseMessage = 'Gagal koneksi ke SAP';
	}else{
		$sap->Open ();
		$fce = $sap->NewFunction("Z_ZAPPSD_DEL_TRANS");
		
		
		if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
			$ResponseCode = '02';
			$ResponseMessage = 'RFC Tidak Ditemukan';
		}else{
			$fce->X_NMORG = $ORG;
			$fce->X_NMPLAN = $PLANT;
			$fce->X_NOTRANS = $NO_ANTRI;
			$fce->X_STATUS = $STATUS;

			$fce->X_REASON_CHG = $ALASAN;

			$fce->X_LAST_UPDATE_DATE = date('Ymd');
			$fce->X_LAST_UPDATE_TIME = date('His');
			$fce->X_LAST_UPDATED_BY = $USER;

			$fce->Call();
			
			if ($fce->GetStatus() == SAPRFC_OK) {
				if ($fce->RETURN["TYPE"] == "E") {
					$data["TYPE"] = "E";
					$data["MSG"] = $fce->RETURN["MESSAGE"];
				} else {
					$data["TYPE"] = $fce->RETURN["TYPE"];
					$data["MSG"] = "Delete transaksi sukses";

					$data_epoool = getEpoool($inqueryRequest);
					$data_epoool['ALASAN'] = $ALASAN;
					$upd_epoool = Prematch($data_epoool, "1");
					$data["MSG"] .= " - Epoool : ".$upd_epoool['response'];					
				}
			}
			
			$fce->Close();
			$sap->Close();
		}
		
	}
		
	return $tampung = $data;
}

function antriGlondong($inqueryRequest) {
	global $fautoris;
	unset($dataHead);
	$token_in = trim($inqueryRequest['token']);
	$role=$fautoris->login($token_in);
	$jmlData=count($role['dataUserAuto']);
	if($role['status']==true && $jmlData>0){
		$aksesser=$fautoris->aksesservice($token_in);//pembatasan akses ke service
		if($aksesser['status']==true){
			//role 
		   $user_id = trim($role['dataUserAuto']['USER_ID']);
		   $dirr = $_SERVER['PHP_SELF'];    
		   $rolenn=$fautoris->keamananser($dirr,$user_id);//pembatasan akses ke service
		   //if($rolenn==true){
				$responseRequest = antri($inqueryRequest);

		//    } else{
		// 	   $TYPE = '04';
		// 	   $MSG = 'Tidak ada akses terhadap service ini';
		// 	   $responseRequest = array (
		// 			   'TYPE' => $TYPE,
		// 			   'MSG' => $MSG,
		// 	   );
		//    }
		} else{
			$TYPE = '02';
			$MSG = $aksesser['keterangan'];
			$responseRequest = array (
					'TYPE' => $TYPE,
					'MSG' => $MSG,
			);
		}        
	}else{
		$TYPE = '01';
		$MSG = $role['keterangan'];
		$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
				'headData' => $dataHead
		);
	}
	return $responseRequest;
}
function getstatustr($inqueryRequest){
	$data=array();
	$tampung = array();

	$ORG = trim($inqueryRequest['ORG']);
	$PLANT = trim($inqueryRequest['PLANT']);
	$NO_ANTRI = trim($inqueryRequest['NO_ANTRI']);
	$ALASAN = trim($inqueryRequest['ALASAN']);
	

	if ($ALASAN=="") {
		$data["TYPE"] = "02";
		$data["MSG"] = "Harus isi alasan!";
		return $data;
	}

	require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
		//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
		//$sap->Connect($link_koneksi_sap);
	
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##

		$ResponseCode = '01';
		$ResponseMessage = 'Gagal koneksi ke SAP';
	}else{
		$sap->Open ();
		$fce = $sap->NewFunction("Z_ZAPPSD_SEL_TRNS_HDR2");
		
		
		if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
			$ResponseCode = '02';
			$ResponseMessage = 'RFC Tidak Ditemukan';
		}else{
			$fce->XDATA_APP["NMORG"] = $ORG;
			$fce->XDATA_APP["NMPLAN"] = $PLANT;
			$fce->XPARAM["NO_TRANSAKSI"] = $NO_ANTRI;

			$fce->Call();
			
			if ($fce->GetStatus() == SAPRFC_OK) {
				if ($fce->RETURN["TYPE"] == "E") {
					$data["TYPE"] = "E";
					$data["MSG"] = $fce->RETURN["MESSAGE"];
				} else {
					$fce->RETURN_DATA->Reset();
					if ($fce->RETURN_DATA->Next()) {
						$data['status']=$fce->RETURN_DATA->row['STATUS_TRANS'];
						
					}
									
				}
			}
			
			$fce->Close();
			$sap->Close();
		}
		
	}
		
	return $tampung = $data;
}

function deleteTrans($inqueryRequest) {
	global $fautoris;
	unset($dataHead);
	$token_in = trim($inqueryRequest['token']);
	$role=$fautoris->login($token_in);
	$jmlData=count($role['dataUserAuto']);
	if($role['status']==true && $jmlData>0){
		$aksesser=$fautoris->aksesservice($token_in);//pembatasan akses ke service
		if($aksesser['status']==true){
			//role 
		   $user_id = trim($role['dataUserAuto']['USER_ID']);
		   $dirr = $_SERVER['PHP_SELF'];    
		   $rolenn=$fautoris->keamananser($dirr,$user_id);//pembatasan akses ke service
		   if($rolenn==true){
				$st=getstatustr($inqueryRequest);
			if($st['status']==70){
				$responseRequest = array (
					'TYPE' => '02',
					'MSG' =>"Transaksi Tidak bisa di Hapus Karena Sudah SPJ",
					);
			}elseif($st['status']==50){
				$responseRequest = array (
					'TYPE' => '02',
					'MSG' =>"Transaksi Tidak bisa di Hapus Karena Sudah Timbang Masuk",
					);
			}else{
				
				$responseRequest = deleteAntri($inqueryRequest);
			}
		

		   } else{
			   $TYPE = '04';
			   $MSG = 'Tidak ada akses terhadap service ini';
			   $responseRequest = array (
					   'TYPE' => $TYPE,
					   'MSG' => $MSG,
			   );
		   }
		} else{
			$TYPE = '02';
			$MSG = $aksesser['keterangan'];
			$responseRequest = array (
					'TYPE' => $TYPE,
					'MSG' => $MSG,
			);
		}        
	}else{
		$TYPE = '01';
		$MSG = $role['keterangan'];
		$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG
		);
	}
	return $responseRequest;
}

function intransitGlondong($inqueryRequest) {
	global $fautoris;
	unset($dataHead);
	$token_in = trim($inqueryRequest['token']);
	$role=$fautoris->login($token_in);
	$jmlData=count($role['dataUserAuto']);
	if($role['status']==true && $jmlData>0){
		$aksesser=$fautoris->aksesservice($token_in);//pembatasan akses ke service
		if($aksesser['status']==true){
			//role 
		   $user_id = trim($role['dataUserAuto']['USER_ID']);
		   $dirr = $_SERVER['PHP_SELF'];    
		   $rolenn=$fautoris->keamananser($dirr,$user_id);//pembatasan akses ke service
		   if($rolenn==true){
				$responseRequest = intransit($inqueryRequest);

		   } else{
			   $TYPE = '01';
			   $MSG = 'Tidak ada akses terhadap service ini';
			   $responseRequest = array (
					   'TYPE' => $TYPE,
					   'MSG' => $MSG,
			   );
		   }
		} else{
			$TYPE = '01';
			$MSG = $aksesser['keterangan'];
			$responseRequest = array (
					'TYPE' => $TYPE,
					'MSG' => $MSG,
			);
		}        
	}else{
		$TYPE = '01';
		$MSG = $role['keterangan'];
		$responseRequest = array (
				'TYPE' => $TYPE,
				'MSG' => $MSG,
				'headData' => $dataHead
		);
	}

	//log service
	$byLog='SV_GLONDONG';
	$log_servie=$fautoris->log_service($inqueryRequest,$responseRequest,$byLog,$token_in);

	return $responseRequest;
}
	
function getNmOrganisasi($orgn_code){
	$toOrg=array(
		'2000'=>'PT. SEMEN INDONESIA',
		'3000'=>'PT.SEMEN PADANG',
		'4000'=>'PT.SEMEN TONASA',
		'5000'=>'PT.SEMEN GRESIK',
		'6000'=>'TLCC',
		'7000'=>'PT SEMEN INDONESIA',
		'7900'=>'MD SEMEN INDONESIA'
		);
	return $toOrg[$orgn_code];
}

function TglToDisplay($tgl){
        $y = substr($tgl,6,4);
        $m = substr($tgl,2,2);
        $d = substr($tgl,0,2);

        return $d . "" . $m . "" . $y;
}

// public function JamToDisplay($jam){
//         $h = substr($jam,0,2);
//         $m = substr($jam,2,2);
//         //$s = substr($jam,4,2);

//         return $h . ":" . $m ;
// }

			if ( !isset( $HTTP_RAW_POST_DATA ) ) $HTTP_RAW_POST_DATA =file_get_contents( 'php://input' );
			$server->service($HTTP_RAW_POST_DATA);


			?>  
