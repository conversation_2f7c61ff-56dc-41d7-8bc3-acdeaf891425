<?php
require_once ('../security_helper.php');
sanitize_global_input();

$decode_params = base64_decode($_GET['kode']);
$params = explode("||",$decode_params);
$type = $params[0];
$data = json_decode($params[1]);

switch ($type) {
    case "approve_ba_ex":
        include_once('via_email/approve_ba_ex.php');
        break;
    case "approve_ba_trans":
        include_once('via_email/approve_ba_trans.php');
        break;
    case "approve_ba_kasie":
        include_once('via_email/approve_ba_kasie.php');
        break;
    case "approve_ba_kabiro":
        include_once('via_email/approve_ba_kabiro.php');
        break;
    default:
        $show_ket = "kode tidak valid";
}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Approve Dokumen BASTP</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
</head>
<style>
</style>

<body>
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
            <a href="<?= get_base_home() ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

</body>

</html>
