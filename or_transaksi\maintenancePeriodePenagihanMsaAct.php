<?php
/*
 * @liyantanto
 */
session_start();
include ('../include/or_fungsi.php');
require_once('../MainPHPExcel/MainPHPExcel.php');

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$dist=sprintf("%010s",$_SESSION['distr_id']);

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$soldto2=$fungsi->sapcode($distr_id);

$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
//$com = htmlspecialchars($_REQUEST['ORG']);
$soldto = $dist;//htmlspecialchars($_REQUEST['SOLD_TO']);
$tipesemen = htmlspecialchars($_REQUEST['TIPE_SEMEN']);
if($tipesemen == 'ZAK'){
    $tipesemen = '121-301';
} else if($tipesemen == 'TO'){
    $tipesemen = '121-302';
}
$distrik_add = htmlspecialchars($_REQUEST['DISTRIK']);
$tgl_target = htmlspecialchars($_REQUEST['periode']);
$bulan = substr($tgl_target,5,2); // 2018-10-21
$tahun = substr($tgl_target,0,4);
// $tahun = htmlspecialchars($_REQUEST['TAHUN']);
$target = htmlspecialchars($_REQUEST['TARGET']);


$sold_to    = htmlspecialchars($_REQUEST['sold_to']);
$ID_REQ    = htmlspecialchars($_REQUEST['id']);
$statusH    = htmlspecialchars($_REQUEST['statusHoliday']);
$tipe_semen = htmlspecialchars($_REQUEST['tipe_semen']);
$distrik    = htmlspecialchars($_REQUEST['distrik']);
$periode    = htmlspecialchars($_REQUEST['periode']);

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');
}else{
   $orgIn= $user_org;
}

/*if($bulan=='JANUARI'){
    $bulan = '01';
}elseif($bulan=='FEBRUARI'){
    $bulan = '02';
}elseif($bulan=='MARET'){
    $bulan = '03';
}elseif($bulan=='APRIL'){
    $bulan = '04';
}elseif($bulan=='MEI'){
    $bulan = '05';
}elseif($bulan=='JUNI'){
    $bulan = '06';
}elseif($bulan=='JULI'){
    $bulan = '07';
}elseif($bulan=='AGUSTUS'){
    $bulan = '08';
}elseif($bulan=='SEPTEMBER'){
    $bulan = '09';
}elseif($bulan=='OKTOBER'){
    $bulan = '10';
}elseif($bulan=='NOVEMBER'){
    $bulan = '11';
}elseif($bulan=='DESEMBER'){
    $bulan = '12';
}*/
// $bulan = 10;
$datetemp = ($bulan."/01/".$tahun);
$now = (date("m")."/01/".date("Y"));
$diff = floor(strtotime($datetemp) - strtotime($now)) / 2592000;

 function callAPI($method, $url, $param)
    {
        $content = ($method==='POST') ? json_encode($param): '';
        $options = array(
                'http' => array(
                        'header'  => "Content-type: application/json\r\n",
                        'method'  => $method,
                        'content' => $content,
                )
        );
        $context    = stream_context_create($options);
        $result     = @file_get_contents( $url, false, $context );
        $response   = json_decode($result);
        return json_encode($response);
    }


function updateDataHoliday($conn, $data_trgt){
    $insert=0;
    $update=0;
    $data_expired = 0;
    $msgRow = "";
    $h = 0;
    $user_name=$_SESSION['user_name'];

    foreach ($data_trgt as $key => $value) { 
        // $kode_material    = '';
            $compOrg = '';
            $compOrg            = trim($value[2]);
            $plant = '';
            $plant            = trim($value[3]);
            $compGroup = '';
            $compGroup            = trim($value[4]);
            
            unset($val); 
            // $val['KODE_MATERIAL']     = $kode_material;
            $val['ORG']  = strtoupper($compOrg);
            $val['PLANT']  = strtoupper($plant);
            $val['GRP_PLANT']  = strtoupper($compGroup);
           
            $ins=false; $upd=false;
             
            if ($compOrg != '' && $plant != '' && $compGroup != '') {
                $data_cek = CekSelectData($conn,$val);

                if ($data_cek['JUMLAH'] > 0){
                    $upd = UpdateData($conn, $val);
                    $msgRow .= ($key + 1).". Success update data <br>";
                    $update++;
                } else {
                    $ins= InsertData($conn,$val);
                    $msgRow .= ($key + 1).". Success insert data <br>";
                    $insert++;
                }
            } else {
                $msgRow .= ($key + 1).". Gagal proses data <br>";
                $data_expired++;    
            }
            // } else {
            //     $data_expired = 1;
            // }
        // } //end if kosong
        
    }  //end foreach
    //============================================================================
    $msg_row = "";
    if ($insert > 0) {
        $msg_row .= $insert . " Insert Data, ";
    }
    if ($update > 0) {
        $msg_row .= $update . " Update Data Run Cost,  ";
    }
    if ($data_expired > 0) {
        $msg_row .= $data_expired . " Gagal  ";
    }
    // $msg["msg"]="";
    $msg["status"]=200;
    $msg["msg"] = $msg_row;
    // if ($insert>0) {
    //     $msg["status"]=200;
    // }else{
    //     if ($data_expired > 0) {
    //         $msg["status"]=200;
    //         $msg["msg"] .= $msgRow;
    //     }
    // }
    
    return $msg;
}

function CekSelectData($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       Z_MAINTENANCE_PLANT_SO
                    WHERE
                        PLANT = '".$data['PLANT']."' AND DELETE_MARK != '1' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function CekSelectDataUpd($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       Z_MAINTENANCE_PLANT_SO
                    WHERE
                        PLANT = '".$data['PLANT']."' AND DELETE_MARK != '1' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];

    return $arData;
}

function generateId($conn){
    $sql_select =  "SELECT 
                    MAX(ID) + 1 AS NEW_ID
                    FROM
                    Z_MAINTENANCE_PLANT_SO";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    $row = oci_fetch_array($query);
    $arData['NEW_ID']= $row['NEW_ID'];

    return $arData['NEW_ID'];
}

function InsertData($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d');
   
        # code...
    if ($data_insert['PLANT'] != '') {
        # code...

        $generateId = generateId($conn);
        $sql2 = "INSERT INTO Z_MAINTENANCE_PLANT_SO (ID, PLANT, DELETE_MARK, ORG, GRP_PLANT)
                 VALUES ('".$generateId."','".$data_insert['PLANT']."','0','".$data_insert['ORG']."','".$data_insert['GRP_PLANT']."')";
                //  var_dump($sql2);exit;
    
        $query2 = oci_parse($conn, $sql2);
        $ins = oci_execute($query2);
    }    

    return $ins;
}

function UpdateData($conn,$data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d');
   
        # code...
    if ($data_update['PLANT'] != '') {
        # code...

        $generateId = generateId($conn);
        $sql2 = "UPDATE Z_MAINTENANCE_PLANT_SO SET ORG = '".$data_update['ORG']."', GRP_PLANT = '".$data_update['GRP_PLANT']."' WHERE PLANT = '".$data_update['PLANT']."'";
    
        $query2 = oci_parse($conn, $sql2);
        $upd = oci_execute($query2);

                
    }    

    return $upd;
}

if(isset($aksi)){
switch($aksi) { 

  case 'getPeriode' :
    {
        $result = array(
            array(
                "PERIODE"  => "1",
                "DESCRIPTION"  => "Januari"
            ),
            array(
                "PERIODE"  => "2",
                "DESCRIPTION"  => "Februari"
            ),
            array(
                "PERIODE"  => "3",
                "DESCRIPTION"  => "Maret"
            ),
            array(
                "PERIODE"  => "4",
                "DESCRIPTION"  => "April"
            ),
            array(
                "PERIODE"  => "5",
                "DESCRIPTION"  => "Mei"
            ),
            array(
                "PERIODE"  => "6",
                "DESCRIPTION"  => "Juni"
            ),
            array(
                "PERIODE"  => "7",
                "DESCRIPTION"  => "Juli"
            ),
            array(
                "PERIODE"  => "8",
                "DESCRIPTION"  => "Agustus"
            ),
            array(
                "PERIODE"  => "9",
                "DESCRIPTION"  => "September"
            ),
            array(
                "PERIODE"  => "10",
                "DESCRIPTION"  => "Oktober"
            ),
            array(
                "PERIODE"  => "11",
                "DESCRIPTION"  => "November"
            ),
            array(
                "PERIODE"  => "12",
                "DESCRIPTION"  => "Desember"
            )
        );

        echo json_encode($result);
     }
     break;

  case 'show' :
    {
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
        }
        
        $fce = $sap->NewFunction ("ZCFM_GHP_MDR");
        if ($fce == false ) {
            $sap->PrintStatus();
            exit;
        }
        
        //header entri
        $result = array();
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "3000";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "4000";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "5000";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "7000";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "7900";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "1000";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }

        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "PTSC";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }
        
        $fce->I_TRANSACTION_TYPE = "R";
        $fce->X_PARAM['BUKRS'] = "ID50";
        $fce->Call();
        if($fce->GetStatus() == SAPRFC_OK){
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
              $row    = $fce->T_DATA->row;
              $result[]  = $row;
            }
        }

        $fce->Close();	
        $sap->Close();	

        echo json_encode($result);
     }
     break;

     case 'updateApp' :
        {
            // $kode_material   = htmlspecialchars($_REQUEST['kode_material']);
            $compOrg     = trim(htmlspecialchars($_REQUEST['org']));
            $brand     = trim(htmlspecialchars($_REQUEST['brand']));
            $kontrak     = trim(htmlspecialchars($_REQUEST['periode']));
            $year     = trim(htmlspecialchars($_REQUEST['year']));
              
            unset($val); 
            // $val['KODE_MATERIAL']     = $kode_material;
            $val['BUKRS']             = strtoupper($compOrg);
            $val['BRAND']             = strtoupper($brand);
            $val['KONTRAK']             = strtoupper($kontrak);
            $val['YEAR']             = strtoupper($year);
         
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
                echo $sap->PrintStatus();
                exit;
            }
            
            $fce = $sap->NewFunction ("ZCFM_GHP_MDR");
            if ($fce == false ) {
                $sap->PrintStatus();
                exit;
            }
            
            //header entri
            
            $fce->X_PARAM['BUKRS'] = $val['BUKRS'];
            $fce->X_PARAM['BRAND'] = $val['BRAND'];
            $fce->X_PARAM['KONTRAK'] = $val['KONTRAK'];
            $fce->X_PARAM['YEAR'] = $val['YEAR'];

            $fce->I_TRANSACTION_TYPE = "U";

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK ) {		
                $fce->T_DATA->Reset();
                $return = $fce->RETURN;
                $status = $return['TYPE'];
            }else{
                $fce->PrintStatus();
            }

            $fce->Close();	
            $sap->Close();	
                
            if (isset($status)){
                if ($status == "S") {
                    $show_ket = "Data berhasil di update <br>";
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di update <br>";
                    $keterangan = array('errorMsg'=>$show_ket);
                }
            } else {
                $show_ket = "Data Gagal di update <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
            echo json_encode($keterangan);

          
           
        // }else{
        //     echo json_encode(array('errorMsg'=>'Duplicate Data.'));
        // }
          
        }
        break;

     case 'upload_file' :
     {
        ############################# READ XLS ####################
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        $allowedExts = "xls";
        $extension = end(explode(".", $_FILES["file_upload"]["name"]));
        if ($extension==$allowedExts) {
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
            $jumlah_row = $cell->rowcount($sheet_index=0);
            $jumlah_col = $cell->colcount($sheet_index=0);
            $kode_file  = $cell->val( 1,2 );
            $ke = 0;
            for ($i = 5; $i <= $jumlah_row; $i++) {
                for ($j = 1; $j <= 4; $j++) {                    
                    $data[$ke][$j]= $cell->val( $i,$j );
                }
                $ke++;
            }
            
            $messData = updateDataHoliday($conn, $data);

            if ($messData){
                // $show_ket = "Data berhasil di Simpan <br>";
                $keterangan = array('success'=>$messData["msg"]);
            } else {
                $show_ket = "Data Gagal di Simpan <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
        } else {
            $show_ket = "Invalid file...!! <br>";
            $keterangan = array('errorMsg'=>$show_ket);
        }
        echo json_encode($keterangan);
     }
     break;
     
     case 'export_data' :
     {
        $datain = $_POST['data'];
            $total = count($datain);
            // foreach ($datain as $valuef) {
            //     var_dump ($valuef['BRAND']);
            // }exit;
            // var_dump ($datain);exit;

            $namafile = "maintenance_plant_so.xls";
            send($namafile);
            $WritePHPExcel = new PHPExcel();
            $WritePHPExcel->setActiveSheetIndex(0);
            $colomSt = 'A';
            $WritePHPExcel->getActiveSheet()->setTitle('Maintenance Plant SO'); //title sheet
            $Worksheet1 = $WritePHPExcel->getActiveSheet();

            //head excel
            $Worksheet1->setCellValueByColumnAndRow(0, 1, 'No.');
            $Worksheet1->setCellValueByColumnAndRow(1, 1, 'ORG');
            $Worksheet1->setCellValueByColumnAndRow(2, 1, 'Plant');
            $Worksheet1->setCellValueByColumnAndRow(3, 1, 'Description');
            $Worksheet1->setCellValueByColumnAndRow(4, 1, 'Group');



            $colomAk = $Worksheet1->getHighestColumn();
            if ($colomSt != '' && $colomAk != '') {
                $WritePHPExcel->getActiveSheet()->getStyle($colomSt . "1:" . $colomAk . "1")->getFont()->setBold(true); //bold header
            }
            $Worksheet1->getStyle($colomSt . '1:' . $colomAk . '1')->applyFromArray($styleHead); //style head         
            // Version 4 fixed
            for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true); //auto size
            }
            $i = 0;
            $j = 2;
            // if (!empty($datain)) {
            // for ($dti=0; $dti < $total; $dti++) {
            foreach ($datain as $valuef) {
                $Worksheet1->setCellValueByColumnAndRow(0, $j, $i + 1);
                $Worksheet1->setCellValueByColumnAndRow(1, $j, $valuef['ORG']);
                $Worksheet1->setCellValueByColumnAndRow(2, $j, $valuef['PLANT']);
                $Worksheet1->setCellValueByColumnAndRow(3, $j, $valuef['NAME']);
                $Worksheet1->setCellValueByColumnAndRow(4, $j, $valuef['GROUP_DESCR']);
                $i++;
                $j = $j + 1;
            }
            // }
            $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
            //$objWriter->save($namafile);
            $objWriter->save("php://output");
     }
     break;
    }
}
?>
