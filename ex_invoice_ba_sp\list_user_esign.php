<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
include_once('../include/e_sign.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=171;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}
   
if((isset($_POST["add_user"]) && $_POST["add_user"] == 'add_user')) {
	$email = $_POST["emaile"];
	$id_user = $_POST["id_user"];
	$hari_ini = date("Y-m-d");
	$eSign = new ESign();
	$eSign->refreshJwtIfExpired();

	// $email = '<EMAIL>';
	//$email = '<EMAIL>';

	try {
		$eSign->checkCeritificate($email);
		$field_namesisertsing = array('ID_USER', 'STATUS_AKTIF', 'CREATED_BY', 'CREATED_AT');
		$field_datainsertsing = array("$id_user", "1", "$user_id", "SYSDATE");
		$tablenameinsertsing = "EX_BA_USER_APPROVAL";
		$fungsi->insert($conn, $field_namesisertsing, $field_datainsertsing, $tablenameinsertsing);
		$pesan = "Berhasil Create User Approval";
		echo '<script language="javascript">';
		echo 'alert("'.$pesan.'");'; 
		echo '</script>';
		//echo "Email terverifikasi";
	} catch (Exception $e) {
		$pesan = "Email tidak valid, alasan: " . $e->getMessage();
		echo '<script language="javascript">';
		echo 'alert("'.$pesan.'");'; 
		echo '</script>';
	}
 
  
	// $action = "cancel_invoice_bag_darat";
	// include ('formula.php'); 
	
}

#$action_page=$fungsi->security($conn,$user_id,$halaman_id);

$page="list_user_esign.php";
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$username = $_POST['username']; 
 
$currentPage="list_user_esign.php";
$komen="";
if(isset($_POST['cari'])){
	if($username==""  and $vendor==""){
		$sql= "SELECT * from TB_USER_BOOKING TUB WHERE TUB.DELETE_MARK = 0
		";
		//AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED')
	}else { 
		$sql= "SELECT TUB.*, EUP.STATUS_AKTIF from TB_USER_BOOKING TUB 
		LEFT JOIN EX_BA_USER_APPROVAL EUP ON EUP.ID_USER = TUB.ID
		WHERE TUB.DELETE_MARK = 0 AND ORG = '3000'";
		
		if($username!=""){ 
			$sql.=" AND ( TUB.NAMA LIKE '$username' OR TUB.NAMA_LENGKAP LIKE '$username' ) ";
		}
		if($vendor!=""){ 
			$sql.=" AND ( TUB.VENDOR_NAME LIKE '$vendor' OR TUB.VENDOR LIKE '$vendor' ) ";
		}
		  
		$sql.="  ORDER BY TUB.NAMA ASC";
		
	}
	 //echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
	$total_tagihan=0; 
	while($row=oci_fetch_array($query)){   
	
		$id_user_v[] 	=$row[ID];
		$company_v[] 	=$row[NAMA_ORG];
		$username_v[] 	=$row[NAMA];
		$nama_v[] 		=$row[NAMA_LENGKAP];
		$email_v[] 		=$row[ALAMAT_EMAIL]; 
		$status_id_v[] 	=$row[STATUS_AKTIF]; 
		$status_v[] 	=($row[STATUS_AKTIF]== 1 ? 'Active' : 'Inactive'); 
	
		
		// $sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";   
		// $query_s= @oci_parse($conn, $sqlS);
		// @oci_execute($query_s);
		// $row_s=@oci_fetch_array($query_s); 
		// $keterangan_v[]=$row_s[KOMENTAR_REJECT];
		// $status_v[]=$row_s[STATUS_BA_INVOICE];
		// $status_id =$row_s[STATUS_BA_INVOICE];
				
				 
	 
 

	} 
	$total=count($username_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Add User Approval </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search</th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">Username</td>
      <td class="puso">:</td>
      <td><input type="text" id="username" name="username" value="<?=$username?>"/></td>
    </tr>
     
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){		
		

?>
<!--<form id="data_claim" name="data_claim" method="post" action="komentar.php" >-->

	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="100%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data User </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="100%" align="center" class="adminlist">
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td> 
		<td align="center"><strong >Name </strong></td> 
		<td align="center"><strong >Username </strong></td> 
		<td align="center"><strong >Company</strong></td>
		<td align="center"><strong >Email </strong></td> 
		<td align="center"><strong>Aksi</strong></td> 
                 
      </tr >
	  
  <? $total_tagihan = 0;    
  for($i=0; $i<$total;$i++) {
		
		//if($no_ba_v[$i]!=''){ 
			$b=$i+1;
				if(($i % 2) == 0)	{	 
				echo "<tr class='row0' id='$rowke' >";
					}
				else	{	
				echo "<tr class='row1'  id='$rowke' >";
					}	
						$orgCom="orgke".$i;        
				?>     
				
				<td align="center"><? echo $b; ?></td> 
				<td align="center"><? echo $nama_v[$i]; ?></td> 
				<td align="center"><? echo $username_v[$i]; ?></td>
				<td align="center"><? echo $company_v[$i]; ?></td>
				<td align="center"><? echo $email_v[$i]; ?></td>  
				<td> 
					<? if($status_id_v[$i] == '' ){ ?> 
					<center><form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $id_user_v[$i]; ?>" name="id_user"><input type="hidden" value="<? echo $email_v[$i]; ?>" name="emaile"><button type="submit" name="add_user" value="add_user" style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #ff7800; color: #fff; border: 1px solid #000; border-radius: 4px;">Add User</button> </form></center>
					<? }?>
					<? if($status_id_v[$i] == '1' || $status_id_v[$i] == '0' ){ ?> 
					 <center><button  style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #666666; color: #fff; border: 1px solid #000; border-radius: 4px;">Add User</button></center>  
					<? }?>
				</td> 
						
				</tr> 
		  <? }?>  
		 

	  <tr class="quote">
		<td colspan="2" align="center">
		<? $excel="CetakExcel_bag_darat.php"; ?>
		<a style='display:none' href="<?php printf("%s?no_shipment=$no_shipment&distributor=$distributor&tipe_transaksi=$tipe_transaksi&tanggal_mulai=$tanggal_mulai&tanggal_selesai=$tanggal_selesai&warna_plat=$warna_plat&nopol=$nopol&no_invoice=$no_invoice&no_invoice_expeditur=$no_invoice_expeditur", $excel,$no_shipment,$distributor,$tipe_transaksi,$tanggal_mulai,$tanggal_selesai,$warna_plat,$nopol,$no_invoice,$no_invoice_expeditur,$vendor); ?>"class="button">EXCEL</a></td>

		<td colspan="21" align="center">
		<a href="list_user_esign.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	 <tr style='display:none'>
		<td colspan="17"><BR>
			<TABLE align="left"  class="adminlist" >
			<TR class="quote">
				<TD><B>Keterangan</B> :<BR>
					&nbsp;&nbsp;&nbsp;Free for payment<BR>
					*	Skip account<BR>
					#	Payment Proposal<BR>
					1	Hold<BR>
					2	Accepted<BR>
					3	Approved<BR>
					A	Locked for payment<BR>
					B	Blocked for payment		<BR>	
					N	Postprocess inc.pmnt<BR>
					P	Payment request<BR>
					R	Invoice verification<BR>
					V	Payment clearing<BR>
				</TD>
			</TR>
			</TABLE>
		</td>
	 </tr>
	</table>
	
	
	
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		<!--</form>-->

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
