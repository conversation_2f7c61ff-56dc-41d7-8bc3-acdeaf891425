<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();


$targetVolume='maintenanceGlAccountMsaAct.php';
$titlepage='Maintenance GL Account <PERSON>agihan MSA';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$distr_id=$_SESSION['distr_id'];
$mp_coics=$fungsi->getComin($conn,$user_org);
$nama = $fungsi->arrayorg();

$soldto=$fungsi->sapcode($distr_id);
// $soldto = str_replace(PHP_EOL,"\<br />", $sld) . " \\"; 
// $soldto=sprintf("%010s",$_SESSION['distr_id']);

//$user_id='mady';
$com=$user_org;
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,3);
	else return '0';
}
function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}
$waktu=date("d-m-Y");
$year = date("Y");
$yeara = date("Y", strtotime('+1year', strtotime($year)));
//$halaman_id=1896;//dev
//$halaman_id=3753;//prod
$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);
$url = basename($_SERVER['SCRIPT_NAME']);

if ($url != 'login.php') {
    // Check if the user is logged in
    if (empty($_SESSION['user_id'])) {
		// var_dump ($_SESSION);
		// die;
        echo '<script type="text/javascript">';
        echo 'window.location.href = "https://csms.sig.id/sdonline/login.php";';
        echo '</script>';
        exit;
    }
}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<style type="text/css">
      #outtable{
        padding:1px;
        border:1px solid #e3e3e3;
        width:600px;
        border-radius: 5px;
      }
 
      .short{
        width: 50px;
      }
 
      .normal{
        width: 150px;
      }
      .tabel_1{
        border-collapse: collapse;
        font-family: arial;
        color:#5E5B5C;
      }

      .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .btn:not([disabled]) {
        color: white;
    }

    .icon-upload {
     background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
     background: transparent url("icon/excel.png") no-repeat scroll center center;
    }

    .icon-mail {
     background: transparent url("icon/send-mail.png") no-repeat scroll center center;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }
 
      thead th{
        text-align: left;
        padding: 7px;
      }
 
      tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
      }

      
</style>
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>
<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px">
        <thead>
            <tr>
                <th field="ck" checkbox="true"></th>     
                <th field="OPCO" width="120">ORG OPCO</th>
                <th field="OPCO_PARTNER" width="120">ORG OPCO PARTNER</th>
                <th field="GL_ACCOUNT_AP" width="200">GL ACCOUNT AP</th>
                <th field="GL_ACCOUNT_AR" width="200">GL ACCOUNT AR</th>
                <th field="COST_CENTER" width="150">COST CENTER</th>
                <th field="LIFNR" width="150">LIFNR</th>
                <th field="CUST" width="150">CUST</th>
                <th field="TAX_TYPE_AP" width="150">TAX TYPE AP</th>
                <th field="TAX_TYPE_AR" width="150">TAX TYPE AR</th>
            </tr>
        </thead>
    </table>
    <div id="toolbar">
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">Add</a> -->
           
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" onclick="newAct()" plain="true">Add</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="updateAppAct()">Edit</a> 
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="cancelAct()">Update</a> -->
            
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" plain="true" onclick="cancelAct()">delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-upload" href="template_xls/template_maintenance_gl_account_msa.xls" >Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-excel" onclick="uploadAct()">Upload Excel</a>
            <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
            <!-- <select id="filter_status" class="easyui-combobox" iconCls="icon-search" name="filter_status" style="width:150px;">
                    <option value="Waiting Approve">Waiting Approve</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="ALL">ALL</option>
                </select> -->
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-mail" onclick="maintainCC()" plain="true">Maintain CC Email</a> -->

    </div>

    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle"><?=$titlepage;?></div>
            <form id="fm" method="post" novalidate>

                <!-- <div class="fitem" id="hideshipto">
                    <label>Code Shipto:</label>
                    <input type="text" class="easyui-combogrid" id="shipto" name="shipto" style="width:200px;" required="true"> 
                </div> -->
                <input type="hidden"  id="ID" name="ID">
                <div class="fitem">
                    <label>ORG OPCO</label>
                    <input class="easyui-textbox" id="opco" name="opco" style="width:200px;" required="true"> 
                </div>
                <div class="fitem">
                    <label>ORG OPCO PARTNER</label>
                    <input class="easyui-textbox" id="opco_partner" name="opco_partner" style="width:200px;" required="true"> 
                </div>
                <div class="fitem">
                    <label>GL ACCOUNT AP</label>
                    <input class="easyui-textbox" id="gl_account_ap" name="gl_account_ap" style="width:200px;" required="true"> 
                </div>
                <div class="fitem">
                    <label>GL ACCOUNT AR</label>
                    <input class="easyui-textbox" id="gl_account_ar" name="gl_account_ar" style="width:200px;" required="true"> 
                </div>
                <div class="fitem">
                    <label>COST CENTER</label>
                    <input class="easyui-textbox" id="costcenter" name="costcenter" style="width:200px;" required="true"> 
                </div>
                <div class="fitem">
                    <label>LIFNR</label>
                    <input class="easyui-textbox" id="lifnr" name="lifnr" style="width:200px;"> 
                </div>
                <div class="fitem">
                    <label>CUST</label>
                    <input class="easyui-textbox" id="cust" name="cust" style="width:200px;"> 
                </div>
                <div class="fitem">
                    <label>TAX TYPE AP</label>
                    <select class="easyui-combobox" id="tax_type_ap" name="tax_type_ap" style="width:200px;" required="true">
                        <option value="WAPU">WAPU</option>
                        <option value="NON WAPU">NON WAPU</option>
                    </select>
                </div>
                <div class="fitem">
                    <label>TAX TYPE AR</label>
                    <select class="easyui-combobox" id="tax_type_ar" name="tax_type_ar" style="width:200px;" required="true">
                        <option value="WAPU">WAPU</option>
                        <option value="NON WAPU">NON WAPU</option>
                    </select>
                </div>
            </form>
        </div>

        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px" id="close">Cancel</a>
        </div>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload" name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px" id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>

    <!-- maintain email cc -->
    

<script type="text/javascript">

 $(function(){
    $("#dg").datagrid({
            url:'maintenanceGlAccountMsaAct.php?act=show',
            // singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');    
 });

    $("#btnExport").click(function() {        
        var myData = $('#dg').datagrid('getData');        
        var mapForm = document.createElement("form");
        mapForm.id = "formexport";
        mapForm.target = "dialogSave";
        mapForm.method = "POST";
        mapForm.action = "maintenanceGlAccountMsaAct.php?act=export_data";        
        $.each(myData.rows, function(k,v){
            $.each(v, function(k2, v2){
                var hiddenField = document.createElement("input");              
                hiddenField.type = "hidden";
                hiddenField.name = "data[" + k + "][" + k2 + "]";
                hiddenField.value = v2;
                mapForm.appendChild(hiddenField);
            });
        });            
        document.body.appendChild(mapForm);
        mapForm.submit();
        document.body.removeChild(mapForm);
        
    });



var url;

function newAct(value){
    $('#dlg').dialog('open').dialog('setTitle','Create');
    //$('#SOLD_TO').textbox('setValue', $(this).val());
    $('#fm').form('clear');
    $('#opco').textbox('textbox').prop('disabled', false)
            $('#opco_partner').textbox('textbox').prop('disabled', false)

    url = 'maintenanceGlAccountMsaAct.php?act=add';
}


function saveAct(){
    $.messager.confirm('Confirm','are you sure to save this transaction?',function(r){
        $('#fm').form('submit',{
            url: url,
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    });    
}


function cancelAct(){
    var row = $('#dg').datagrid('getSelections');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('maintenanceGlAccountMsaAct.php?act=del&',{data:row},function(result){
            if (result.success){
                $('#dg').datagrid('reload'); // reload the user data
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.success
                });
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function deleteAct(){
    var row = $('#dgcc').datagrid('getSelected');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('maintenanceGlAccountMsaAct.php?act=del&',{id:row.ID},function(result){
            if (result.success){
                $('#dgcc').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

function updateAppAct(){
    var row = $('#dg').datagrid('getSelected');
    
    if(row) {
            $("#dlg").dialog('open').dialog('setTitle', 'Edit');
            // $("#fm").form('clear');
            $("#fm").form("load", row);
            // $('#brand').textbox({editable: false, value: row.BRAND});
            // $("#kode_material").textbox('setValue', row.KODE_MATERIAL);
            $("#opco").textbox('setValue', row.OPCO);
            $("#opco_partner").textbox('setValue', row.OPCO_PARTNER);
            $("#gl_account_ap").textbox('setValue', row.GL_ACCOUNT_AP);
            $("#gl_account_ar").textbox('setValue', row.GL_ACCOUNT_AR);
            $("#costcenter").textbox('setValue', row.COST_CENTER);
            $("#lifnr").textbox('setValue', row.LIFNR);
            $("#cust").textbox('setValue', row.CUST);
            $("#tax_type_ap").combobox('setValue', row.TAX_TYPE_AP);
            $("#tax_type_ar").combobox('setValue', row.TAX_TYPE_AR);
            
            $('#opco').textbox('textbox').prop('disabled', true)
            $('#opco_partner').textbox('textbox').prop('disabled', true)
            
            url = 'maintenanceGlAccountMsaAct.php?act=updateApp';
            
        }
        else {
            $.messager.alert('Error', 'Select one of the data to be edited', 'error');
        }
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle','Upload Maintenance GL Account Penagihan MSA');
    $('#uploadForm').form('clear');
    // url = 'cMbrand.php?act=upload_file';
}

function saveUploadAct() {
        $('#uploadForm').form('submit',{
            url: 'maintenanceGlAccountMsaAct.php?act=upload_file',
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
  
}

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
