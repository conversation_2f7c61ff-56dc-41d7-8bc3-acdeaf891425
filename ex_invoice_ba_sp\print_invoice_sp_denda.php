<?php
ob_start();
session_start();
function Terbilang($x)
{
    $abil = array("", "satu", "dua", "tiga", "empat", "lima", "enam", "tujuh", "delapan", "sembilan", "sepuluh", "sebelas");
    if ($x < 12)
        return " " . $abil[$x];
    elseif ($x < 20)
        return Terbilang($x - 10) . " belas";
    elseif ($x < 100)
        return Terbilang($x / 10) . " puluh" . Terbilang($x % 10);
    elseif ($x < 200)
        return " seratus" . Terbilang($x - 100);
    elseif ($x < 1000)
        return Terbilang($x / 100) . " ratus" . Terbilang($x % 100);
    elseif ($x < 2000)
        return " seribu" . Terbilang($x - 1000);
    elseif ($x < 1000000)
        return Terbilang($x / 1000) . " ribu" . Terbilang($x % 1000);
    elseif ($x < 1000000000)
        return Terbilang($x / 1000000) . " juta" . Terbilang($x % 1000000);
    elseif ($x < 1000000000000)
        return Terbilang($x / 1000000000) . " Milyar" . Terbilang(fmod($x, 1000000000));
}

include('../include/ex_fungsi.php');
include('../include/validasi.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = @$fungsi->ex_koneksi();
$user_id = $_SESSION['user_id'];

$vendor = $fungsi->ex_find_vendor($conn, $user_id);
$halaman_id = 1253; //prod
//$halaman_id=1332;//dev


$no_ba = $_REQUEST['no_ba'];

$currentPage = "print_invoice_ba.php";
$tanggal_cetak = date('d-m-Y');

// $sql = "
//     SELECT
//         EX_BA_INVOICE.*,
//         EX_BA.TGL_BA,
//         TB_USER_BOOKING.NAMA_LENGKAP,
//         TB_USER_BOOKING.VENDOR_NAME,
//         to_char(EX_BA_INVOICE.TGL_SP_DENDA,'DD-MM-YYYY') as TGL_SP_DENDA,
//         to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_BA
//     FROM EX_BA_INVOICE
//     LEFT JOIN EX_BA ON EX_BA_INVOICE.NO_BA = EX_BA.NO_BA
//     INNER JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA_INVOICE.ID_USER_APPROVAL
//     WHERE EX_BA_INVOICE.NO_BA = '$no_ba' AND EX_BA_INVOICE.DIPAKAI = 1
// ";
$sql1 = "
    SELECT
        EX_BA_INVOICE.*,
        EX_BA.TGL_BA,
        TB_USER_BOOKING.NAMA_LENGKAP,
        TB_USER_BOOKING.VENDOR_NAME,
        to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_BA
    FROM EX_BA_INVOICE
    LEFT JOIN EX_BA ON EX_BA_INVOICE.NO_BA = EX_BA.NO_BA
    INNER JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA_INVOICE.ID_USER_APPROVAL
    WHERE EX_BA_INVOICE.NO_BA = '$no_ba' AND EX_BA_INVOICE.DIPAKAI = 1
";
$sql = oci_parse($conn, $sql1);
oci_execute($sql);

$data_invoice_ba = oci_fetch_array($sql);


$no_invoice = $data_invoice_ba['NO_INVOICE'];
$is_date_sama = $data_invoice_ba['TGL_BA'] == $data_invoice_ba['TGL_SP_DENDA'];

$sql2 = "
    SELECT
        EX_INVOICE.*,
        to_char(EX_INVOICE.TGL_INVOICE,'YYYYMMDD') as TGL_INVOICE
    FROM EX_INVOICE
    WHERE EX_INVOICE.NO_INVOICE = '$no_invoice'
";
$sql = oci_parse($conn, $sql2);
oci_execute($sql);

$data_invoice = oci_fetch_array($sql);

$sql3 = "
    SELECT
        *
    FROM
        EX_TRANS_HDR
    WHERE
        DELETE_MARK = '0' AND NO_BA = '$no_ba'
";
$sql = oci_parse($conn, $sql3);
oci_execute($sql);
$data_trans_hdr = oci_fetch_array($sql);
$data_trans_hdr['ORG'];

$orginnn = $data_trans_hdr['ORG'];
//var_dump($orginnn);

    if( $orginnn=='7000'){            
        $nmkali="PT. Semen Indonesia (PERSERO) Tbk ";
    } else if ($orginnn=='5000'){
        $nmkali="PT. Semen Gresik ";
    }

$sql4 = "select NAMA_LENGKAP as NAMA_DIRUT FROM EX_BA A LEFT JOIN TB_USER_BOOKING B ON A.ID_USER_APPROVAL = B.ID WHERE NO_BA='".$no_ba."'";
$sql = oci_parse($conn, $sql4);
oci_execute($sql);
$data_invoice_dirut_vendor = oci_fetch_array($sql);

$p1mna = 0;
$sql_pjaknew = "
select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
    select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
    (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice'
    group by NO_INVOICE)
    order by TGL_PAJAK_EX desc
) where rownum =1
";
$querycek = oci_parse($conn, $sql_pjaknew);
oci_execute($querycek);
$row_datap = oci_fetch_assoc($querycek);
unset($tglfakturpajak);
$tglfakturpajak = $row_datap['TGL_PAJAK_EXF'];
$VENDORpj = $row_datap['NO_VENDOR'];
if ($VENDORpj != '0000410082') {
    if ($tglfakturpajak != '' && $tglfakturpajak >= '20140901') {
        $p1mna = 1;
    }
}

$warna_plat = $data_trans_hdr['VEHICLE_TYPE'] == '205' && $data_trans_hdr['VENDOR'] == '0000410095' ? 'HITAM' : $data_trans_hdr['WARNA_PLAT'];
$kelompok = $data_trans_hdr['KELOMPOK_TRANSAKSI'];
$inco = $data_trans_hdr['INCO'];

$tanggal_sp_denda = $data_invoice_ba['TGL_SP_DENDA'];
$tanggal_faktur_pajak_expect = date('d-m-Y', strtotime($data_invoice_ba['TGL_BA']));
$tanggal_faktur_pajak_actual = date('d-m-Y', strtotime($data_invoice_ba['TGL_BA']));
$no_faktur_pajak = $data_invoice_ba['NO_FAKTUR_PAJAK'];

$user_name = $data_invoice_ba['NAMA_LENGKAP'];
$user_position = 'Pimpinan ' . $data_invoice_ba['VENDOR_NAME'];

$sign_location = $fungsi->ex_cari_vendor($data_trans_hdr['VENDOR']);
$sign_date = date('d F Y');

$sql5 = "SELECT A.*, B.CREATE_BY, B.PIC_GUDANG, C.NO_INVOICE NO_INVOICE_BA FROM (SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF 
FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_BA = '$no_invoice'
ORDER BY PLANT,SAL_DISTRIK, KODE_KECAMATAN, KODE_PRODUK ,TANGGAL_KIRIM ASC ) A LEFT JOIN
(SELECT m1.NO_SPJ, m1.CREATE_BY, m1.PIC_GUDANG
FROM EX_INPUTCLAIM_SEMEN m1 LEFT JOIN EX_INPUTCLAIM_SEMEN m2
 ON (m1.NO_SPJ = m2.NO_SPJ AND m1.id < m2.id)
WHERE m2.id IS NULL and m1.DELETE_MARK = '0' AND m1.STATUS = 'ELOG') B ON (A.NO_SHP_TRN = B.NO_SPJ)
LEFT JOIN (SELECT m3.NO_BA, m3.NO_INVOICE, m3.NO_FAKTUR_PAJAK, m3.TGL_FAKTUR_PAJAK
FROM EX_BA_INVOICE m3  where DIPAKAI = 1 ) C ON (C.NO_BA = A.NO_BA)
";
//ORDER BY PLANT,SAL_DISTRIK, SOLD_TO, KODE_PRODUK,TANGGAL_KIRIM ASC
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
// \echo $sql;
$query = oci_parse($conn, $sql5);
oci_execute($query);

$total_shp_cost = 0;

while ($row = oci_fetch_array($query)) {
    $total_shp_cost += $row['SHP_COST'];
}

$nominal_pajak = 0;

if ($warna_plat != "KUNING" || ($kelompok == "LAUT" && $inco != "FOB")) {
    if ($p1mna == 1) {
        if ($data_invoice['TGL_INVOICE'] < 20220401) {
            $nominal_pajak = 0.1 * $total_shp_cost;
        } else {
            if ($data_trans_hdr['VENDOR'] == "0000410046" || $data_trans_hdr['VENDOR'] == "0000410003" || $data_trans_hdr['VENDOR'] == "0000410000") {
                $nominal_pajak = 0.011 * $total_shp_cost;
            } else {
                $nominal_pajak = 0.11 * $total_shp_cost;
            }
        }
    } else {
        if ($data_invoice['TGL_INVOICE'] < 20220401) {
            $nominal_pajak = 0.11 * $total_shp_cost;
        } else {
            $nominal_pajak = 0.11 * $total_shp_cost;
        }
    }
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>Print Denda Faktur Pajak</title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
    <style>
        body {
            padding-left: 2.3rem !important;
            padding-right: 2.3rem !important;
        }

        hr {
            border-top: .1px solid #000;
        }

        p {
            margin-bottom: 10px;
            line-height: 1.25rem;
            text-align: justify;
        }

        .text-center {
            text-align: center;
        }

        table {
            width: 100%;
            border-spacing: 0 !important;
        }

        table td {
            padding-bottom: 10px;
        }

        .sign {
            width: 300px;
        }

        .sign p {
            text-align: center;
        }
    </style>
</head>

<body>
    <h3 class="text-center"><u>SURAT PERNYATAAN</u></h3>

    <p>Kepada Yth:</p>

    <p>Unit Perpajakan <?=$nmkali;?></p>

    <p>CC: Seksi Verifikasi</p>

    <br>

    <p>Yang bertanda tangan di bawah ini:</p>

    <table>
        <tr>
            <td style="width: 100px;">Nama</td>
            <td style="width: 10px;">:</td>
            <td>
                <?= $user_name ?>
            </td>
        </tr>
        <tr>
            <td>Jabatan</td>
            <td>:</td>
            <td>
                <?= $user_position ?>
            </td>
        </tr>
    </table>

    <?php if ($is_date_sama) : ?>
        <p>
            Sehubungan dengan keterlambatan tagihan kami berikut ini:
        </p>
    <?php else : ?>
        <p>
            Sehubungan dengan perbedaan tanggal penerbitan Faktur Pajak dengan tanggal Berita Acara Rekapitulasi Ongkos Angkut atau tanggal Faktur Pajak tidak sesuai Perjanjian Kontrak ( Tata Cara Penagihan dan Pembayaran Tagihan ) atas tagihan berikut ini :
        </p>
    <?php endif ?>

    <table>
        <tr>
            <td style="<?= $is_date_sama ? 'width: 150px;' : 'width: 310px;' ?>">No Invoice</td>
            <td style="width: 10px;">:</td>
            <td>
                <?= $no_invoice ?>
            </td>
        </tr>
        <?php if (!$is_date_sama) : ?>
            <tr>
                <td>Tgl Faktur Pajak (seharusnya diterbitkan)</td>
                <td>:</td>
                <td>
                    <?= $tanggal_faktur_pajak_expect ?>
                </td>
            </tr>
        <?php endif ?>
        <?php if (!$is_date_sama) : ?>
            <tr>
                <td>Tanggal Faktur Pajak (diterbitkan)</td>
                <td>:</td>
                <td>
                    <?= $tanggal_faktur_pajak_actual ?>
                </td>
            </tr>
        <?php endif ?>
        <tr>
            <td>No Faktur Pajak</td>
            <td>:</td>
            <td>
                <?= $no_faktur_pajak ?>
            </td>
        </tr>
        <tr>
            <td>Nilai PPN</td>
            <td>:</td>
            <td>
                <?= $nominal_pajak == 0 ? '0' : number_format($nominal_pajak, 2, ",", ".") ?>
            </td>
        </tr>
        <?php if ($is_date_sama) : ?>
            <tr>
                <td style="vertical-align: top;">Pekerjaan</td>
                <td style="vertical-align: top;">:</td>
                <td>
                    Untuk Pembayaran Ongkos Angkut, sesuai dengan Berita Acara Nomor: <?= $no_ba ?>
                </td>
            </tr>
        <?php endif ?>
        <?php if (!$is_date_sama) : ?>
            <tr>
                <td>Tgl Berita Acara Rekapitulasi Ongkos Angkut </td>
                <td>:</td>
                <td>
                    <?= $tanggal_sp_denda ?>
                </td>
            </tr>
        <?php endif ?>
    </table>

    <?php if ($is_date_sama) : ?>
        <p>
            Dengan ini kami menyatakan bersedia dan sanggup menanggung POKOK PAJAK dan/atau DENDA KETERLAMBATAN penyerahan INVOICE dan FAKTUR PAJAK sesuai ketentuan yang berlaku dan dipotongkan langsung atas pembayaran tagihan kami.
        </p>
    <?php else : ?>
        <p>
            Dengan ini menyatakan bersedia menanggung DENDA PPN jika timbul masalah dikemudian hari.
        </p>
    <?php endif ?>

    <p>
        Demikian surat pernyataan ini dibuat dengan sebenar-benar dan untuk dipergunakan sebagaimana mestinya.
    </p>

    <br><br>

    <table>
        <tr>
            <td></td>
            <td class="sign">
                <p><?= $sign_location ?>, <?= $sign_date ?></p>

                <br><br><br><br><br>

                <p>
                    <span><u><?= $user_name ?></u></span>
                    <br>
                    <span><?= $user_position ?></span>
                </p>
            </td>
        </tr>
    </table>
</body>

</html>