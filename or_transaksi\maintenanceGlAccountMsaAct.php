<?php
/*
 * @liyantanto
 */
session_start();
include ('../include/or_fungsi.php');
require_once('../MainPHPExcel/MainPHPExcel.php');

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$dist=sprintf("%010s",$_SESSION['distr_id']);

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$soldto2=$fungsi->sapcode($distr_id);

$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
//$com = htmlspecialchars($_REQUEST['ORG']);
$soldto = $dist;//htmlspecialchars($_REQUEST['SOLD_TO']);
$tipesemen = htmlspecialchars($_REQUEST['TIPE_SEMEN']);
if($tipesemen == 'ZAK'){
    $tipesemen = '121-301';
} else if($tipesemen == 'TO'){
    $tipesemen = '121-302';
}
$distrik_add = htmlspecialchars($_REQUEST['DISTRIK']);
$tgl_target = htmlspecialchars($_REQUEST['periode']);
$bulan = substr($tgl_target,5,2); // 2018-10-21
$tahun = substr($tgl_target,0,4);
// $tahun = htmlspecialchars($_REQUEST['TAHUN']);
$target = htmlspecialchars($_REQUEST['TARGET']);


$sold_to    = htmlspecialchars($_REQUEST['sold_to']);
$ID_REQ    = htmlspecialchars($_REQUEST['id']);
$statusH    = htmlspecialchars($_REQUEST['statusHoliday']);
$tipe_semen = htmlspecialchars($_REQUEST['tipe_semen']);
$distrik    = htmlspecialchars($_REQUEST['distrik']);
$periode    = htmlspecialchars($_REQUEST['periode']);

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');
}else{
   $orgIn= $user_org;
}

/*if($bulan=='JANUARI'){
    $bulan = '01';
}elseif($bulan=='FEBRUARI'){
    $bulan = '02';
}elseif($bulan=='MARET'){
    $bulan = '03';
}elseif($bulan=='APRIL'){
    $bulan = '04';
}elseif($bulan=='MEI'){
    $bulan = '05';
}elseif($bulan=='JUNI'){
    $bulan = '06';
}elseif($bulan=='JULI'){
    $bulan = '07';
}elseif($bulan=='AGUSTUS'){
    $bulan = '08';
}elseif($bulan=='SEPTEMBER'){
    $bulan = '09';
}elseif($bulan=='OKTOBER'){
    $bulan = '10';
}elseif($bulan=='NOVEMBER'){
    $bulan = '11';
}elseif($bulan=='DESEMBER'){
    $bulan = '12';
}*/
// $bulan = 10;
$datetemp = ($bulan."/01/".$tahun);
$now = (date("m")."/01/".date("Y"));
$diff = floor(strtotime($datetemp) - strtotime($now)) / 2592000;

 function callAPI($method, $url, $param)
    {
        $content = ($method==='POST') ? json_encode($param): '';
        $options = array(
                'http' => array(
                        'header'  => "Content-type: application/json\r\n",
                        'method'  => $method,
                        'content' => $content,
                )
        );
        $context    = stream_context_create($options);
        $result     = @file_get_contents( $url, false, $context );
        $response   = json_decode($result);
        return json_encode($response);
    }


function updateDataHoliday($conn, $data_trgt){
    $insert=0;
    $update=0;
    $data_expired = 0;
    $msgRow = "";
    $h = 0;
    $user_name=$_SESSION['user_name'];

    foreach ($data_trgt as $key => $value) { 
        // $kode_material    = '';
            $opco            = trim($value[2]);
            $opco_partner            = trim($value[3]);
            $gl_account_ap            = trim($value[4]);
            $gl_account_ar            = trim($value[5]);
            $costcenter            = trim($value[6]);
            $lifnr            = trim($value[7]);
            $cust            = trim($value[8]);
            $tax_type_ap            = trim($value[9]);
            $tax_type_ar            = trim($value[10]);
            
            unset($val); 
            // $val['KODE_MATERIAL']     = $kode_material;
            $val['OPCO']  = strtoupper($opco);
            $val['OPCO_PARTNER']  = strtoupper($opco_partner);
            $val['GL_ACCOUNT_AP']  = strtoupper($gl_account_ap);
            $val['GL_ACCOUNT_AR']  = strtoupper($gl_account_ar);
            $val['COST_CENTER']  = strtoupper($costcenter);
            $val['LIFNR']  = strtoupper($lifnr);
            $val['CUST']  = strtoupper($cust);
            $val['TAX_TYPE_AP']  = strtoupper($tax_type_ap);
            $val['TAX_TYPE_AR']  = strtoupper($tax_type_ar);
           
            $ins=false; $upd=false;
             
            if ($opco != '' && $opco_partner != '' && $gl_account_ap != '' && $gl_account_ar != '' && $costcenter != '' && $tax_type_ap != '' && $gl_account_ar != '') {
                $data_cek = CekSelectData($conn,$val);

                if ($data_cek['JUMLAH'] > 0){
                    $upd = UpdateData($conn, $val);
                    $msgRow .= ($key + 1).". Success update data <br>";
                    $update++;
                } else {
                    $ins= InsertData($conn,$val);
                    $msgRow .= ($key + 1).". Success insert data <br>";
                    $insert++;
                }
            } else {
                $msgRow .= ($key + 1).". Gagal proses data <br>";
                $data_expired++;    
            }
            // } else {
            //     $data_expired = 1;
            // }
        // } //end if kosong
        
    }  //end foreach
    //============================================================================
    $msg_row = "";
    if ($insert > 0) {
        $msg_row .= $insert . " Insert Data, ";
    }
    if ($update > 0) {
        $msg_row .= $update . " Update Data,  ";
    }
    if ($data_expired > 0) {
        $msg_row .= $data_expired . " Gagal  ";
    }
    // $msg["msg"]="";
    $msg["status"]=200;
    $msg["msg"] = $msg_row;
    // if ($insert>0) {
    //     $msg["status"]=200;
    // }else{
    //     if ($data_expired > 0) {
    //         $msg["status"]=200;
    //         $msg["msg"] .= $msgRow;
    //     }
    // }
    
    return $msg;
}

function CekSelectData($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       CONFIG_GL_ACCOUNT_MSA
                    WHERE
                        OPCO = '".$data['OPCO']."' AND OPCO_PARTNER = '".$data['OPCO_PARTNER']."' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function CekSelectDataUpd($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       CONFIG_GL_ACCOUNT_MSA
                    WHERE
                        OPCO = '".$data['OPCO']."' AND OPCO_PARTNER = '".$data['OPCO_PARTNER']."' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];

    return $arData;
}

function InsertData($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d');

    $sql2 = "INSERT INTO CONFIG_GL_ACCOUNT_MSA (OPCO, OPCO_PARTNER, GL_ACCOUNT_AP, GL_ACCOUNT_AR, COST_CENTER, LIFNR, CUST, TAX_TYPE_AP, TAX_TYPE_AR)
                                VALUES ('".$data_insert['OPCO']."','".$data_insert['OPCO_PARTNER']."','".$data_insert['GL_ACCOUNT_AP']."','".$data_insert['GL_ACCOUNT_AR']."','".$data_insert['COST_CENTER']."','".$data_insert['LIFNR']."','".$data_insert['CUST']."','".$data_insert['TAX_TYPE_AP']."','".$data_insert['TAX_TYPE_AR']."')";
            //  var_dump($sql2);exit;

    $query2 = oci_parse($conn, $sql2);
    $ins = oci_execute($query2);

    return $ins;
}

function UpdateData($conn,$data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d');

    $sql2 = "UPDATE CONFIG_GL_ACCOUNT_MSA SET GL_ACCOUNT_AP = '".$data_update['GL_ACCOUNT_AP']."', GL_ACCOUNT_AR = '".$data_update['GL_ACCOUNT_AR']."', COST_CENTER = '".$data_update['COST_CENTER']."', LIFNR = '".$data_update['LIFNR']."', CUST = '".$data_update['CUST']."', TAX_TYPE_AP = '".$data_update['TAX_TYPE_AP']."', TAX_TYPE_AR = '".$data_update['TAX_TYPE_AR']."' WHERE OPCO = '".$data_update['OPCO']."'  AND OPCO_PARTNER = '".$data_update['OPCO_PARTNER']."'";

    $query2 = oci_parse($conn, $sql2);
    $upd = oci_execute($query2);

    return $upd;
}

if(isset($aksi)){
switch($aksi) { 
  case 'show' :
    {
      
        $sql = "SELECT
                    *
                FROM
                    CONFIG_GL_ACCOUNT_MSA
                ";
        // var_dump($sql);
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $i=0;
        while($row=oci_fetch_array($query)){
            array_push($result, $row);
        }
        echo json_encode($result);
     }
     break;

     case 'add' :
        {
           if ($user_org != '') {
   
   
                   // get parameter 
                   $opco     = strtoupper(trim(htmlspecialchars($_REQUEST['opco'])));
                   $opco_partner     = strtoupper(trim(htmlspecialchars($_REQUEST['opco_partner'])));
                   $gl_account_ap     = strtoupper(trim(htmlspecialchars($_REQUEST['gl_account_ap'])));
                   $gl_account_ar     = strtoupper(trim(htmlspecialchars($_REQUEST['gl_account_ar'])));
                   $costcenter     = strtoupper(trim(htmlspecialchars($_REQUEST['costcenter'])));
                   $lifnr     = strtoupper(trim(htmlspecialchars($_REQUEST['lifnr'])));
                   $cust     = strtoupper(trim(htmlspecialchars($_REQUEST['cust'])));
                   $tax_type_ap     = strtoupper(trim(htmlspecialchars($_REQUEST['tax_type_ap'])));
                   $tax_type_ar     = strtoupper(trim(htmlspecialchars($_REQUEST['tax_type_ar'])));
                   $date     = $newDate = date("d-m-Y");
                   
   
                               //cek apakah data sudah ada 
                               $cekdata = "SELECT ID FROM CONFIG_GL_ACCOUNT_MSA WHERE OPCO = '$opco' AND OPCO_PARTNER = '$opco_partner'";
                               $cek = oci_parse($conn, $cekdata);
                               oci_execute($cek);
                               $hasilCek = oci_fetch_array($cek);
   
   
                               $user = $_SESSION['user_name'];
                               if (count($hasilCek[0]) < 1) {
                                   $sql2 = "INSERT INTO CONFIG_GL_ACCOUNT_MSA (OPCO, OPCO_PARTNER, GL_ACCOUNT_AP, GL_ACCOUNT_AR, COST_CENTER, LIFNR, CUST, TAX_TYPE_AP, TAX_TYPE_AR)
                                   VALUES ('".$opco."','".$opco_partner."','".$gl_account_ap."','".$gl_account_ar."','".$costcenter."','".$lifnr."','".$cust."','".$tax_type_ap."','".$tax_type_ar."')";
                                   $query2 = oci_parse($conn, $sql2);
                                   $ins = oci_execute($query2);
   
                                   if ($ins){
                                       echo json_encode(array('success'=>'Sukses insert data'));
                                   } else {
                                       echo json_encode(array('errorMsg'=>'Some errors occured.'));
                                   }
                               
                               }else{
                                   echo json_encode(array('errorMsg'=>'Duplicate Data.'));
                               }
                      
   
                       
              
           }else{
               echo json_encode(array('errorMsg'=>'Silahkan login terlebih dahulu!!!'));
           }
        }
        break;

     case 'del' :
     {

        $value = ($_POST['data']);
        $list = array();
        $gagal = 0;
        $sukses= 0;
        $availMapping = 0;
        $i=0; 

        while($i < count($value)){
            $idDlt = $value[$i]['ID'];       
            $sql = "DELETE FROM CONFIG_GL_ACCOUNT_MSA WHERE ID = $idDlt ";
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);

            if($result){ 
                $sukses=$sukses+1; 
            }else{ 
                $gagal=$gagal+1; 
            }

            $i++;
        }  
         
        if ($result){
            $keterangan = array('success'=>"Data Berhasil Di Delete = ".$sukses.", gagal = ".$gagal." ! ");
        } else {
            $keterangan = array('errorMsg'=>"Data Gagal Di Delete = gagal = ".$gagal." ! ");
        }
        // }
        echo json_encode($keterangan);

     }
     break;

     case 'updateApp' :
        {
            // $kode_material   = htmlspecialchars($_REQUEST['kode_material']);
            $opco     = strtoupper(trim(htmlspecialchars($_REQUEST['opco'])));
            $opco_partner     = strtoupper(trim(htmlspecialchars($_REQUEST['opco_partner'])));
            $gl_account_ap     = strtoupper(trim(htmlspecialchars($_REQUEST['gl_account_ap'])));
            $gl_account_ar     = strtoupper(trim(htmlspecialchars($_REQUEST['gl_account_ar'])));
            $costcenter     = strtoupper(trim(htmlspecialchars($_REQUEST['costcenter'])));
            $lifnr     = strtoupper(trim(htmlspecialchars($_REQUEST['lifnr'])));
            $cust     = strtoupper(trim(htmlspecialchars($_REQUEST['cust'])));
            $tax_type_ap     = strtoupper(trim(htmlspecialchars($_REQUEST['tax_type_ap'])));
            $tax_type_ar     = strtoupper(trim(htmlspecialchars($_REQUEST['tax_type_ar'])));
            $ID   = htmlspecialchars($_REQUEST['ID']);
              
            unset($val); 
            
            $val['OPCO']             = strtoupper($opco);
            $val['OPCO_PARTNER']             = strtoupper($opco_partner);
            $val['GL_ACCOUNT_AP']             = strtoupper($gl_account_ap);
            $val['GL_ACCOUNT_AR']             = strtoupper($gl_account_ar);
            $val['COST_CENTER']             = strtoupper($costcenter);
            $val['LIFNR']             = strtoupper($lifnr);
            $val['CUST']             = strtoupper($cust);
            $val['TAX_TYPE_AP']             = strtoupper($tax_type_ap);
            $val['TAX_TYPE_AR']             = strtoupper($tax_type_ar);
            $val['ID']                = $ID;
         
            $data_cek2 = CekSelectDataUpd($conn,$val);
            if ($data_cek2['JUMLAH'] > 0) {
                $user = $_SESSION['user_name'];
                
                $sql = "UPDATE CONFIG_GL_ACCOUNT_MSA SET GL_ACCOUNT_AP = '".$val['GL_ACCOUNT_AP']."', GL_ACCOUNT_AR = '".$val['GL_ACCOUNT_AR']."', COST_CENTER = '".$val['COST_CENTER']."', LIFNR = '".$val['LIFNR']."', CUST = '".$val['CUST']."', TAX_TYPE_AP = '".$val['TAX_TYPE_AP']."', TAX_TYPE_AR = '".$val['TAX_TYPE_AR']."' WHERE ID = '$ID'";
                
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
                
                if ($result){
                    $show_ket = "Data berhasil di update <br>";
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di update <br>";
                    $keterangan = array('errorMsg'=>$show_ket);
                }
                echo json_encode($keterangan);
            }else{
                $show_ket = "Data Tidak Ditemukan <br>";
                $keterangan = array('errorMsg'=>$show_ket);
                echo json_encode($keterangan);
            }

          
           
        // }else{
        //     echo json_encode(array('errorMsg'=>'Duplicate Data.'));
        // }
          
        }
        break;

     case 'upload_file' :
     {
        ############################# READ XLS ####################
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        $allowedExts = "xls";
        $extension = end(explode(".", $_FILES["file_upload"]["name"]));
        if ($extension==$allowedExts) {
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
            $jumlah_row = $cell->rowcount($sheet_index=0);
            $jumlah_col = $cell->colcount($sheet_index=0);
            $kode_file  = $cell->val( 1,2 );
            $ke = 0;

            for ($i = 5; $i <= $jumlah_row; $i++) {
                $row = array();
                for ($j = 1; $j <= 10; $j++) {
                    $row[$j] = $cell->val($i, $j);
                }
                if (!empty($row[2]) && !empty($row[3])) {
                    $data[] = $row;
                }
            }
            
            $messData = updateDataHoliday($conn, $data);

            if ($messData){
                // $show_ket = "Data berhasil di Simpan <br>";
                $keterangan = array('success'=>$messData["msg"]);
            } else {
                $show_ket = "Data Gagal di Simpan <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
        } else {
            $show_ket = "Invalid file...!! <br>";
            $keterangan = array('errorMsg'=>$show_ket);
        }
        echo json_encode($keterangan);
     }
     break;
     
     case 'export_data' :
     {
        $datain = $_POST['data'];
            $total = count($datain);
            // foreach ($datain as $valuef) {
            //     var_dump ($valuef['BRAND']);
            // }exit;
            // var_dump ($datain);exit;

            $namafile = "maintenance_gl_account_msa.xls";
            send($namafile);
            $WritePHPExcel = new PHPExcel();
            $WritePHPExcel->setActiveSheetIndex(0);
            $colomSt = 'A';
            $WritePHPExcel->getActiveSheet()->setTitle('Maintenance GL Account MSA'); //title sheet
            $Worksheet1 = $WritePHPExcel->getActiveSheet();

            //head excel
            $Worksheet1->setCellValueByColumnAndRow(0, 1, 'No.');
            $Worksheet1->setCellValueByColumnAndRow(1, 1, 'OPCO');
            $Worksheet1->setCellValueByColumnAndRow(2, 1, 'OPCO PARTNER');
            $Worksheet1->setCellValueByColumnAndRow(3, 1, 'GL ACCOUNT AP');
            $Worksheet1->setCellValueByColumnAndRow(4, 1, 'GL ACCOUNT AR');
            $Worksheet1->setCellValueByColumnAndRow(5, 1, 'COST CENTER');
            $Worksheet1->setCellValueByColumnAndRow(6, 1, 'LIFNR');
            $Worksheet1->setCellValueByColumnAndRow(7, 1, 'CUST');
            $Worksheet1->setCellValueByColumnAndRow(8, 1, 'TAX TYPE AP');
            $Worksheet1->setCellValueByColumnAndRow(9, 1, 'TAX TYPE AR');



            $colomAk = $Worksheet1->getHighestColumn();
            if ($colomSt != '' && $colomAk != '') {
                $WritePHPExcel->getActiveSheet()->getStyle($colomSt . "1:" . $colomAk . "1")->getFont()->setBold(true); //bold header
            }
            $Worksheet1->getStyle($colomSt . '1:' . $colomAk . '1')->applyFromArray($styleHead); //style head         
            // Version 4 fixed
            for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true); //auto size
            }
            $i = 0;
            $j = 2;
            // if (!empty($datain)) {
            // for ($dti=0; $dti < $total; $dti++) {
            foreach ($datain as $valuef) {
                $Worksheet1->setCellValueByColumnAndRow(0, $j, $i + 1);
                $Worksheet1->setCellValueByColumnAndRow(1, $j, $valuef['OPCO']);
                $Worksheet1->setCellValueByColumnAndRow(2, $j, $valuef['OPCO_PARTNER']);
                $Worksheet1->setCellValueByColumnAndRow(3, $j, $valuef['GL_ACCOUNT_AP']);
                $Worksheet1->setCellValueByColumnAndRow(4, $j, $valuef['GL_ACCOUNT_AR']);
                $Worksheet1->setCellValueByColumnAndRow(5, $j, $valuef['COST_CENTER']);
                $Worksheet1->setCellValueByColumnAndRow(6, $j, $valuef['LIFNR']);
                $Worksheet1->setCellValueByColumnAndRow(7, $j, $valuef['CUST']);
                $Worksheet1->setCellValueByColumnAndRow(8, $j, $valuef['TAX_TYPE_AP']);
                $Worksheet1->setCellValueByColumnAndRow(9, $j, $valuef['TAX_TYPE_AR']);
                $i++;
                $j = $j + 1;
            }
            // }
            $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
            //$objWriter->save($namafile);
            $objWriter->save("php://output");
     }
     break;
    }
}
?>
