<?php

include_once('ex_fungsi.php');

class ApiESign
{
    // dev
    const API_KEY = '0706e266-7901-4fe4-ac1e-3cc357afbf79';//'8b6a21b7-ec21-41d0-a7c3-e5c32c3dd494';
    // prod
    // const API_KEY = '1e911ed0-a9a8-4bb3-b0e2-00739828ba92';
    const SYSTEM_ID = 'SIG';//'PERURI-DEVELOPMENT'; 

    // dev
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/jwtSandbox/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v1';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';
    
    // prod
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/jwt/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/signing/v2';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/checkDocumentStatus/v1';

// dev 2024
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v2';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';    

    // dev synxchro
    // const URL_GENERATE_JWT      = 'https://dev-integrasi-api.sig.id/peruri/esign/jwt';
    // const URL_UPLOAD_DOCUMENT   = 'https://dev-integrasi-api.sig.id/peruri/esign/senddocument';
    // const URL_GET_OTP           = 'https://dev-integrasi-api.sig.id/peruri/esign/getotp';
    // const URL_SIGNING           = 'https://dev-integrasi-api.sig.id/peruri/esign/signing';
    // const URL_DOWNLOAD_DOCUMENT = 'https://dev-integrasi-api.sig.id/peruri/esign/downloaddocument';
    // const URL_CHECK_CERTIFICATE = 'https://dev-integrasi-api.sig.id/peruri/esign/checkcertificate';
    // const URL_CHECK_DOC         = 'https://dev-integrasi-api.sig.id/peruri/esign/checkdocumentstatus';    

    //dev terbaru setelah case CPNS
     const URL_GENERATE_JWT      = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/jwtSandbox/1.0/getJsonWebToken/v1';
//const URL_GENERATE_JWT      = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php';
    const URL_UPLOAD_DOCUMENT   = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    const URL_GET_OTP           = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    const URL_SIGNING           = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v1';
    const URL_DOWNLOAD_DOCUMENT = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    const URL_CHECK_CERTIFICATE = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    const URL_CHECK_DOC         = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';    



    private $jwt = null;

    // penambahan fungsi untuk write file json untuk log
    public function cek($nama, $proses,$status,$response){
        
               $createdAt = new DateTime('now');
            $createdAt = $createdAt->format('Y-m-d H:i:s');
      $fungsi = new ex_fungsi();
        $this->connection = $fungsi->ex_koneksi();
        // $cek=implode('=', $response);
        //       var_dump($response);  
        // die;
           if($response != 'false'){
           if($response->resultCode == '0'){
               if(isset($response->data) && isset($response->data->base64Document) ){
                   //potong base64 jika ada menjadi 50 
                  $response=$response->resultDesc;
               }
           $status="BERHASIL";
           }elseif($response->resultCode == 'BP-002'){
               $status ="Dokumen Masih Menunggu Ditandatangani";
              
            } elseif($response->resultCode == 'BP-003'){
                   $status = 'Dokumen sudah lebih dari 3 hari tidak ditandatangan (expired)';
               }else{
               $status ="GAGAL DARI E-SIGN";
           }
       } 

      $response = json_encode($response);
      
      if($response == 'false'){
      
          $response ="Error: Couldn t resolve host";
      }

        $qu="INSERT INTO ZREPORT_LOG_SERVICE (ID, GROUP_LOG,REQUEST, RESPON, BY_LOG,LOG_DATE, TOKEN, DELETE_MARK)
VALUES ('', '1', '$proses','$response','$nama', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), '$status', '1')
";
       $sql = oci_parse($this->connection, $qu);
            oci_execute($sql);
    }


    public function setJwt($jwt)
    {
        $this->jwt = $jwt;
    }

    public function generateJwt()
    {
        // echo "1";
        
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
        );

        $data = array(
            'param' => array(
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_GENERATE_JWT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);
        // echo 'respon JWT '.$response;
        //die;

        if (curl_errno($ch)) {
            $this->cek("E-SIGN","GENERATE JWT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
             $this->cek("E-SIGN","GENERATE JWT","BERHASIL",$resp);
        return $response->data;
    }

    public function uploadDocument($data)
    {
        // echo "2 ";
       
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_UPLOAD_DOCUMENT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);


        if (curl_errno($ch)) {
         $this->cek("E-SIGN","UPLOAD DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
           $this->cek("E-SIGN","UPLOAD DOCUMENT","BERHASIL", $resp);
        return $response;
    }

    public function getOtp($orderId)
    {
        // echo "3";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_GET_OTP);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","GET OTP","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
         $this->cek("E-SIGN","GET OTP","BERHASIL",$resp);
        return $response;
    }

    public function signing($data)
    {

        // echo "4";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_SIGNING);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","SIGNING ","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
          $this->cek("E-SIGN","SIGNING","BERHASIL",$resp);
        return $response;
    }

    public function downloadDocument($orderId)
    {
        // echo "5";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_DOWNLOAD_DOCUMENT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","DOWNLOAD DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
        $this->cek("E-SIGN","DOWNLOAD DOCUMENT","BERHASIL",$resp);
        return $response;
    }

    public function checkCertificate($email)
    {
        // echo "6";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'email' => $email,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_CHECK_CERTIFICATE);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","CHECK SERTIFICATE","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
          $this->cek("E-SIGN","CHECK SERTIFICATE","BERHASIL",$resp);
        return $response;
    }

    public function checkDoc($orderId)
    {
        // echo "7";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );
        // echo json_encode($data);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_CHECK_DOC);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","CHECK DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);
        $resp=json_decode($response);
        $response = json_decode($response);
        
        $this->cek("E-SIGN","CHECK DOCUMENT","BERHASIL",$resp);
      
        return $response;
    }
}

class ESign
{
    public function __construct()
    {
        $fungsi = new ex_fungsi();

        $this->connection = $fungsi->ex_koneksi();
        $this->api = new ApiESign();

        $data = $this->getLatestJwt();
        $this->api->setJwt($data->TOKEN);
    }

    private function getLatestJwt()
    {
        $query = "SELECT * FROM (SELECT ID, TOKEN, TO_CHAR(EXPIRED_AT, 'yyyy-mm-dd hh24:mi:ss') AS EXPIRED_AT, CREATED_AT FROM EX_PERURI_SIGN_JWT ORDER BY EXPIRED_AT DESC) where ROWNUM = 1";
        $sql = oci_parse($this->connection, $query);
        oci_execute($sql);
        $data = oci_fetch_object($sql);

        if ($data) {
            return $data;
        }

        return null;
    }

    private function hasActiveJwt()
    {
        $data = $this->getLatestJwt();

        // Check if token is not expired
        $expiredAt = new DateTime($data->EXPIRED_AT);
        $currentTime = new DateTime('now');

        $currentDateTimeGreater = $currentTime > $expiredAt;
        $isSameDate = $expiredAt->format('Y-m-d') == $currentTime->format('Y-m-d');
        $isDiff2Hours = intval($expiredAt->format('H')) - intval($currentTime->format('H')) <= 2;

        if ($currentDateTimeGreater || ($isSameDate && $isDiff2Hours)) {
            return false;
        }

        return true;
    }

    private function refreshJwt()
    {
        $response = $this->api->generateJwt();

        $token = $response->jwt;
        if(empty($token)){
            return false;
        }else{
            $createdAt = new DateTime('now');
            $createdAt = $createdAt->format('Y-m-d H:i:s');
            $expiredAt = new DateTime('now');
            $expiredAt->modify('+12 hours');
            $expiredAt = $expiredAt->format('Y-m-d H:i:s');

            $query = "INSERT INTO EX_PERURI_SIGN_JWT (TOKEN, CREATED_AT, EXPIRED_AT) VALUES ('$token', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), TO_DATE('$expiredAt', 'YYYY-MM-DD HH24:MI:SS'))";
            $sql = oci_parse($this->connection, $query);
            oci_execute($sql);

            $this->api->setJwt($token);
        }
    }

    /**
     * Merefresh token jwt.
     *
     * @return bool true jika sukses refresh, false jika token masih aktif.
     */
    public function refreshJwtIfExpired()
    {
        if (!$this->hasActiveJwt()) {
            $this->refreshJwt();
            return true;
        }

        return false;
    }

    public function uploadDocument($data)
    {
        $sendData = array(
            'param' => array(
                'systemId' => ApiESign::SYSTEM_ID,
                'email' => $data['email'],
                'payload' => array(
                    'fileName' => $data['fileName'],
                    'base64Document' => $data['base64Document'],
                    'signer' => array(
                        array(
                            'isVisualSign' => 'YES',
                            'lowerLeftX' => $data['lowerLeftX'],
                            'lowerLeftY' => $data['lowerLeftY'],
                            'upperRightX' => $data['upperRightX'],
                            'upperRightY' => $data['upperRightY'],
                            'page' => $data['page'],
                            'certificateLevel' => 'NOT_CERTIFIED',
                            'varLocation' => 'GRESIK',
                            'varReason' => $data['varReason'],
                        )
                    )
                )
            )
        );

        $response = $this->api->uploadDocument($sendData);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        // If success
        if ($resultCode == '0') {
            return $response->data->orderId;
        }

        throw new Exception("Error: $resultCode - $resultDesc");
    }

    public function getOtp($orderId)
    {
        $response = $this->api->getOtp($orderId);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        // If success
        if ($resultCode == '0') {
            return $response->data;
        }

        throw new Exception("Error: $resultCode - $resultDesc");
    }

    public function signing($data)
    {
        $sendData = array(
            'requestSigning' => array(
                'orderId' => $data['orderId'],
                'otpCode' => $data['otpCode'],
                'token' => $data['token'],
            )
        );

        $response = $this->api->signing($sendData);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        if ($resultCode == '0') {
            return true;
        }

        throw new Exception("Error: $resultCode - $resultDesc");
    }

    public function downloadDocument($orderId)
    {
        $response = $this->api->downloadDocument($orderId);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        if ($resultCode == '0') {
            return $response->data->base64Document;
        }

        throw new Exception("Error: $resultCode - $resultDesc");
    }

    public function checkCeritificate($email)
    {
        $response = $this->api->checkCertificate($email);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        if ($resultCode == '0') {
            return true;
        }

        throw new Exception($resultDesc);
    }

    public function checkDoc($orderId)
    {
        $response = $this->api->checkDoc($orderId);

        $resultCode = $response->resultCode;
        $resultDesc = $response->resultDesc;

        if ($resultCode == '0') {
            return true;
        }

        throw new Exception($resultDesc);
    }
}
