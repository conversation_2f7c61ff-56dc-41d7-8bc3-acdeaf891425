<?
session_start();
//  $url = 'https://dev-integrasi-api.sig.id/APIMD/CreateSalesOrder'; // Prod Synxchrox
//  //$url = 'https://integrasi-api.sig.id/apimd/createsalesorder/dev'; // Prod Synxchrox
//                         // echo "run SBI </br>";
//                         $data = array('Token' => 'aSsMx7GV0HFGzlufM4DH',
//                             'SystemID' => 'QASSO', //'QASSO',
//                             'Data' => array(
// 							'SalesDocumentType' => "ZOR",
// 							'SalesOrganization' => "1000",
// 							'DistributionChannel' => "DB",
// 							'Division' => "CM",
// 							'SalesDocumentDate' => "2025-05-28",
// 							'RequestDeliveryHeader' => "2025-06-01",
// 							'ShippingCondition' => "01",
// 							'ShippingType' => "G4",
// 							'SoldToCode' => "CUST001",
// 							'ShipToCode' => "SHIP001",
// 							'MDReferenceSONumber' => "SOREFF001",
// 							'MDPurchaseOrderNumber' => "PO001",
// 							'MDPurchaseOrderItem' => "00010",
// 							'PurchaseOrderNumber' => "PO001",
// 							'PurchaseOrderDate' => "2025-05-28",
// 							'PaymentTerms' => "0001",
// 							'Items' => array(
// 								array(
// 									'MaterialNumber' => "MAT001",
// 									'OrderQuantity' => "100",
// 									'UOM' => "TO"
// 								)
// 							)
// 						)
//                         );

//                         // echo "<pre> <br>";
//                         // echo "Data Lempar Untuk Create SO SBI : <br>";
//                         // print_r($data);
//                         $options = array(
//                             'http' => array(
//                                 'header' => "Content-type: application/json\r\n",
//                                 'method' => 'POST',
//                                 'content' => json_encode($data),
//                             )
//                         );

// //                        print_r($option);


//                         $context = stream_context_create($options);
//                         $result = file_get_contents($url, false, $context); 
//                         // print_r($context);   
//                         // print_r($result);
//                         $response = json_decode($result);  
// 						var_dump($response); 
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
include_once('../include/sapclasses/sap.php');

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

 
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_CHK_DO");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$fce->I_NMORG = "2000";
		$fce->I_WERKS = "10";
		$fce->I_ERDAT_FR = "00";
		$fce->I_ERDAT_TO = "";
		$fce->I_FLAG_EMPTY = "9999";
		$fce->I_FLAG_DEL = "20101212";

		$fce->Call();
			
		if ($fce->GetStatus() == SAPRFC_OK ) {
			$fce->T_RETURNDATA->Reset();
			$s=0;
			while ( $fce->T_RETURNDATA->Next() ){
			$nodo[$s]= $fce->RETURN_DATA->row["VBELN"];
			$posnr[$s]= $fce->RETURN_DATA->row["POSNR"];
			$matnr[$s]= $fce->RETURN_DATA->row["MATNR"];
			$qty[$s]= $fce->RETURN_DATA->row["LFIMG"];
			$uom[$s]= $fce->RETURN_DATA->row["MEINS"];
			$so_num[$s]= $fce->RETURN_DATA->row["NO_SO"];
			$shp_num[$s]= $fce->RETURN_DATA->row["NO_TRANSAKSI"];
			$status[$s]= $fce->RETURN_DATA->row["STATUS_TRANS"];
			}
			}else
        		$fce->PrintStatus();

			$fce->Close();
			$sap->Close();
?>
