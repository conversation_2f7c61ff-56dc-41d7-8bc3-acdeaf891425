<?php
session_start();

include_once ('../helper.php');
include ('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

switch ($action) {
    case 'get':
        $sql = "SELECT eua.ID, tub.NAMA, tub.NAMA_LENGKAP
                FROM EX_USER_APPROVER eua 
                LEFT JOIN TB_USER_BOOKING tub 
                ON eua.USER_ID = tub.ID
                ORDER BY NAMA_LENGKAP";

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        
        $data = array();

        while($row=oci_fetch_array($query)){
            array_push($data, $row);
        }

        echo json_encode($data);

        break;
  
    case 'get_users':
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';
        $keyword = strtoupper($keyword);

        
        $sql = "
            SELECT ID, NAMA, NAMA_LENGKAP 
            FROM (
                SELECT ID, NAMA, NAMA_LENGKAP 
                FROM TB_USER_BOOKING 
                WHERE 
                    UPPER(NAMA) LIKE '%' || :keyword || '%' 
                    OR UPPER(NAMA_LENGKAP) LIKE '%' || :keyword || '%'
                ORDER BY NAMA_LENGKAP
            ) 
            WHERE ROWNUM <= 10
        ";

        $stid = oci_parse($conn, $sql);
        oci_bind_by_name($stid, ":keyword", $keyword);
        oci_execute($stid);

        $data = array();
        while ($row = oci_fetch_assoc($stid)) {
            $data[] = array(
                "id" => $row['ID'],
                "text" => $row['NAMA'].'-'.$row['NAMA_LENGKAP']
            );
        }

        oci_free_statement($stid);
        oci_close($conn);

        header('Content-Type: application/json');
        echo json_encode($data);
        break;
    case 'get_configs':
        $sql = "SELECT ID,NAMA FROM EX_MASTER_CONFIG WHERE ORG = '3000'";

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        
        $data = array();

        while($row=oci_fetch_array($query)){
            array_push($data, $row);
        }

        echo json_encode($data);

        break;
    case 'insert':
        $user_id  = $_POST['user_id'];
        
        try {
            $sql = "SELECT 1 FROM EX_USER_APPROVER WHERE USER_ID = :user_id";
            $query = oci_parse($conn, $sql);
            oci_bind_by_name($query, ':user_id', $user_id);
            oci_execute($query);

            if (oci_fetch($query)) {
                $result = array(
                    'status' => 'success'
                );
                echo json_encode($result);
                
                exit;
            }

            $field_names = array('USER_ID', 'ORG');
            $field_data = array("$user_id", "3000");
            $tablename = "EX_USER_APPROVER";
            $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
            $result = array(
                'status' => 'success'
            );
            echo json_encode($result);
        } catch (Exception $e) {
            $result = array(
                'status' => 'error',
                'message' => $e->getMessage()
            );
            echo json_encode($result);
        }

        break;

    case 'delete':
        $id = $_POST['user_id'];

        $tablename = "EX_USER_APPROVER";
        $field_id = array('ID');
        $value_id = array("$id");
        
        try {
            $fungsi->delete($conn, $tablename, $field_id, $value_id);
            $result = array(
                'status' => 'success'
            );
            echo json_encode($result);
        } catch (Exception $e) {
            $result = array(
                'status' => 'error',
                'message' => $e->getMessage()
            );
            echo json_encode($result);
        }

    break;
  
    default:
      // ⚠️ Unknown action
      $result = array(
        'status' => 'error',
        'message' => 'Invalid action'
        );
      echo json_encode($result);
      break;
  }
?>