<?php
//by @liyantanto 2014

include("../include/lib/nusoap.php");
$server = new soap_server();
$server->configureWSDL('wsdlQuery', 'urn:wsdlQuery');

require_once ("autorisasi.php");
$fautoris= new autorisasi();

$server->wsdl->addComplextype(
    'wsdlQueryRequest',
    'complexType',
    'struct',
    'sequence',
    '',
    array(
              
                'STATUS_TRANS' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'STATUS_TRANS', 'type' => 'xsd:string'),
                'BERAT_ISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'BERAT_ISI', 'type' => 'xsd:string'),
                'BERAT_KOSONG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'BERAT_KOSONG', 'type' => 'xsd:string'),
                'BERAT_BERSIH' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'BERAT_BERSIH', 'type' => 'xsd:string'),
                'BERAT_TOTAL_DO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'BERAT_TOTAL_DO', 'type' => 'xsd:int'),
                'BERAT_DELTA' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'BERAT_DELTA', 'type' => 'xsd:string'),
                'TOTAL_QTY' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TOTAL_QTY', 'type' => 'xsd:int'),
                //'NO_SEGEL' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_SEGEL', 'type' => 'xsd:string'),
                'PTGS_ISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'PTGS_ISI', 'type' => 'xsd:string'),
                'TGL_ISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TGL_ISI', 'type' => 'xsd:string'),
                'JAM_ISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'JAM_ISI', 'type' => 'xsd:string'),
                'LAST_UPDATE_DATE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LAST_UPDATE_DATE', 'type' => 'xsd:string'),
                'LAST_UPDATE_TIME' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LAST_UPDATE_TIME', 'type' => 'xsd:string'),
                'LAST_UPDATE_BY' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LAST_UPDATE_BY', 'type' => 'xsd:string'),
                //'ID_CARD' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ID_CARD', 'type' => 'xsd:string'),
                'NOPOLISI' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NOPOLISI', 'type' => 'xsd:string'),
                //'SHIFT' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'SHIFT', 'type' => 'xsd:string'),
                'NMORG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NMORG', 'type' => 'xsd:string'),
                'NMPLAN' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NMPLAN', 'type' => 'xsd:string'),
                'XDATA_KEY' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'XDATA_KEY', 'type' => 'xsd:string'),
                'XDATA_MANUAL' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'XDATA_MANUAL', 'type' => 'xsd:string'),
                'SHIPT' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'SHIPT', 'type' => 'xsd:string'),
                //SBI
                'VKORG' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'VKORG', 'type' => 'xsd:string'),
                'NO_DO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_DO', 'type' => 'xsd:string'),
                'WERKS' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'WERKS', 'type' => 'xsd:string'),
                'NO_SHIPMENT' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_SHIPMENT', 'type' => 'xsd:string'),
                'NO_SO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_SO', 'type' => 'xsd:string'),
                'NO_DO_REFF' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_DO_REFF', 'type' => 'xsd:string'),
                'WADAT_IST' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'WADAT_IST', 'type' => 'xsd:string')

            )       
);

$server->wsdl->addComplextype(
    'wsdlQueryResponse',
    'complexType',
    'struct',
    'sequence',
    '',
    array(
        'ResponseCode' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ResponseCode', 'type' => 'xsd:string'),
        'ResponseMessage' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ResponseMessage', 'type' => 'xsd:string'),
        'detailData' => array('minOccurs' => '0', 'maxOccurs' => 'unbounded','name' => 'detailData', 'type' => 'tns:DetailData')
    )
);

$server->wsdl->addComplexType(
    'DetailData',
    'complexType',
    'struct',
    'sequence',
    '',        
    array(

                  //OUTPUT
                  //IDOC_RET
                'NO_IDOC' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_IDOC', 'type' => 'xsd:string'),
                'NO_SHP' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NO_SHP', 'type' => 'xsd:string'),
                'DESC' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'DESC', 'type' => 'xsd:string'),
                
                'TYPE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'TYPE', 'type' => 'xsd:string'),
                'ID' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ID', 'type' => 'xsd:string'),
                'NUMBER' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'NUMBER', 'type' => 'xsd:string'),
                'MESSAGE' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MESSAGE', 'type' => 'xsd:string'),
                'LOG_NO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LOG_NO', 'type' => 'xsd:string'),
                'LOG_MSG_NO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'LOG_MSG_NO', 'type' => 'xsd:string'),
                'MESSAGE_V1' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MESSAGE_V1', 'type' => 'xsd:string'),
                'MESSAGE_V2' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MESSAGE_V2', 'type' => 'xsd:string'),
                'MESSAGE_V3' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MESSAGE_V3', 'type' => 'xsd:string'),
                'MESSAGE_V4' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'MESSAGE_V4', 'type' => 'xsd:string'),
                'PARAMETER' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'PARAMETER', 'type' => 'xsd:string'),
                'ROW' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ROW', 'type' => 'xsd:string'),
                'SYSTEM' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'SYSTEM', 'type' => 'xsd:string')
            
    )
);

$server->register('getListRelease',                 // method name
    array('wsdlQueryRequest' => 'tns:wsdlQueryRequest'),        // input parameters
    array('wsdlQueryResponse' => 'tns:wsdlQueryResponse'),  // output parameters
    'urn:wsdlQuery',                        // namespace
    'urn:wsdlQuery#getListRelease',                 // soapaction
    'rpc',                                  // style
    'literal',                              // use
    'wsdlQuery Request...'      // documentation
);

// Define the method as a PHP function
function getListRelease($inqueryRequest){

        unset($dataDetail);
       //pembatasan akses ke service 
        $STATUS_TRANS = trim($inqueryRequest['STATUS_TRANS']);
        $STATUS_HOLD = trim($inqueryRequest['STATUS_HOLD']);
        $APPROVE_HOLD_BY = trim($inqueryRequest['APPROVE_HOLD_BY']);
        $NAMA_CHECKPOINT = trim($inqueryRequest['NAMA_CHECKPOINT']);
        $APPROVE_REASON = trim($inqueryRequest['NAPPROVE_REASON']);
        $BERAT_ISI = trim($inqueryRequest['BERAT_ISI']);
        $BERAT_BERSIH = trim($inqueryRequest['BERAT_BERSIH']);
        $BERAT_TOTAL_DO = trim($inqueryRequest['BERAT_TOTAL_DO']);
        $BERAT_DELTA = trim($inqueryRequest['BERAT_DELTA']);
        $TOTAL_QTY = trim($inqueryRequest['TOTAL_QTY']);
        $NO_SEGEL = trim($inqueryRequest['NO_SEGEL']);
        $PTGS_ISI = trim($inqueryRequest['PTGS_ISI']);
        $TGL_ISI = trim($inqueryRequest['TGL_ISI']);
        $JAM_ISI = trim($inqueryRequest['JAM_ISI']);
        $LAST_UPDATE_DATE= trim($inqueryRequest['LAST_UPDATE_DATE']);
        $LAST_UPDATE_TIME= trim($inqueryRequest['LAST_UPDATE_TIME']);
        $LAST_UPDATE_BY = trim($inqueryRequest['LAST_UPDATE_BY']);
        $NOPOLISI= trim($inqueryRequest['NOPOLISI']);
        $SHIFT= trim($inqueryRequest['SHIFT']);
        $NOSPJ= trim($inqueryRequest['NOSPJ']);
        $KODE_KECAMATAN= trim($inqueryRequest['KODE_KECAMATAN']);
        $NAMA_KECAMATAN= trim($inqueryRequest['NAMA_KECAMATAN']);
        $STATUS_GR= trim($inqueryRequest['STATUS_GR']);
        $NO_ORA= trim($inqueryRequest['NO_ORA']);
        $JML_JUMBO = trim($inqueryRequest['JML_JUMBO']);
        $TIPE_JUMBO= trim($inqueryRequest['TIPE_JUMBO']);
        $NMORG= trim($inqueryRequest['NMORG']);
        $NMPLAN = trim($inqueryRequest['NMPLAN']);
        $ID_CARD = trim($inqueryRequest['ID_CARD']);
        $BERAT_KOSONG = trim($inqueryRequest['BERAT_KOSONG']);
        $XDATA_KEY= trim($inqueryRequest['XDATA_KEY']);
        $XDATA_MANUAL = trim($inqueryRequest['XDATA_MANUAL']);
        $SHIPT = trim($inqueryRequest['SHIPT']);
        //SBI
        $VKORG= trim($inqueryRequest['VKORG']);
        $NO_DO = trim($inqueryRequest['NO_DO']);
        $WERKS = trim($inqueryRequest['WERKS']);
        $NO_SHIPMENT = trim($inqueryRequest['NO_SHIPMENT']);
        $NO_SO = trim($inqueryRequest['NO_SO']);
        $NO_DO_REFF = trim($inqueryRequest['NO_DO_REFF']);
        $WADAT_IST = trim($inqueryRequest['WADAT_IST']);
       
    //role 
   // $dirr = $_SERVER['PHP_SELF'];    
    require_once ("../include/sapclasses/sap.php");
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
        //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
        //$sap->Connect($link_koneksi_sap);
    unset($dataDetail);
    if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##
            
        $ResponseCode = '01';
        $ResponseMessage = 'Gagal koneksi ke SAP';
        $responseRequest = array (
            'ResponseCode' => $ResponseCode,
            'ResponseMessage' => $ResponseMessage,
            'detailData' => $dataDetail
        );
    }else{
        $sap->Open ();
        $fce = $sap->NewFunction ("Z_ZAPPSD_GP_UPD_TIMB_ISI");
        if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
            $ResponseCode = '02';
            $ResponseMessage = 'RFC Tidak Ditemukan';
            $responseRequest = array (
                'ResponseCode' => $ResponseCode,
                'ResponseMessage' => $ResponseMessage,
                'detailData' => $dataDetail
            );
        }else{
            
//             $jml_do = 1;//$_SESSION['gpjual']['jumlahdo']; //$this->jml->Text;
//            //$data_do = $_SESSION['gpjual']['todo'];
//            $berat_netto = 0;
//            for ($i = 0; $i < $jml_do; $i++) {
//                $berat_netto += 0;
//            
//             }
        $timvalnetto = $TOTAL_QTY * 1000;     
        $fce->XDATA_UPD["STATUS_TRANS"] = $STATUS_TRANS;
        $fce->XDATA_UPD["BERAT_ISI"] = $BERAT_ISI;
        $fce->XDATA_UPD["BERAT_BERSIH"] = $BERAT_ISI - $BERAT_KOSONG;
        $fce->XDATA_UPD["BERAT_TOTAL_DO"] = $timvalnetto;;    
        $fce->XDATA_UPD["BERAT_DELTA"] = ($BERAT_ISI - $BERAT_KOSONG) - $timvalnetto;    
        $fce->XDATA_UPD["TOTAL_QTY"] = $TOTAL_QTY;    
        //$fce->XDATA_UPD["NO_SEGEL"] = $NO_SEGEL;    
        $fce->XDATA_UPD["PTGS_ISI"] = $PTGS_ISI;    
        $fce->XDATA_UPD["TGL_ISI"] = $TGL_ISI;    
        $fce->XDATA_UPD["JAM_ISI"] = $JAM_ISI;    
        $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');    
        $fce->XDATA_UPD["LAST_UPDATE_TIME"] = date('His');    
        $fce->XDATA_UPD["LAST_UPDATE_BY"] = $LAST_UPDATE_BY;     
        $fce->XDATA_UPD["NOPOLISI"] = $NOPOLISI; 
       // $fce->XDATA_UPD["ID_CARD"] = $ID_CARD;  
        //$fce->XDATA_UPD["SHIFT"] = $SHIFT;      
        $fce->XDATA_APP["NMORG"] = $NMORG;    
        $fce->XDATA_APP["NMPLAN"] = $NMPLAN;
        
        $fce->XDATA_KEY = $XDATA_KEY;    
        $fce->XDATA_MANUAL = $XDATA_MANUAL; 
        $fce->SHIPT= $SHIPT;
        //DATASBI
        if($NMORG == "7900"){
        $fce->DATA_SBI["VKORG"] = '$VKORG';    
        $fce->DATA_SBI["NO_DO"] = '$NO_DO';     
        $fce->DATA_SBI["WERKS"] = '$WERKS';    
        $fce->DATA_SBI["NO_SHIPMENT"] = $NO_SHIPMENT;    
        $fce->DATA_SBI["NO_SO"] = $NO_SO;    
        $fce->DATA_SBI["NO_DO_REFF"] = sprintf('%010d',$$NO_DO_REFF);    
        $fce->DATA_SBI["WADAT_IST"] = $WADAT_IST;  
        }
        
                            
        if (strlen(trim($fce->SHIPT)) > 0){
                $fce->XDATA_MANUAL = '';
            }
            else{
                $fce->XDATA_MANUAL = 'X';
            }
                            $fce->Call();
                            
                            //print_r($fce);
                             if ($fce->GetStatus() == SAPRFC_OK ) {   
                               $fce->IDOC_RET->Reset();
                               $i=0;
                              while ($fce->IDOC_RET->Next()) {
                                $dataDetail[$i]["NO_IDOC"] = $fce->IDOC_RET->row["NO_IDOC"];
                                $dataDetail[$i]["NO_SHP"] = $fce->IDOC_RET->row["NO_SHP"];
                                $dataDetail[$i]["DESC"] = $fce->IDOC_RET->row["DESC"];
                                $laporan .= 'IDoc Number: ' .  $fce->IDOC_RET->row["NO_IDOC"];
                                $i++;
                               }
                               
                            if ($fce->ZPESAN['TYPE'] == 'E') {
                                $dataDetail['TYPE']  = 'E';
                                $laporan          .= $fce->ZPESAN["MESSAGE"];
                              
                            }else{
                                $dataDetail['TYPE']  = 'S';
                                $i = 0;
                                $fce->ZIDOC_MSG->Reset(); 
                                 while ($fce->ZIDOC_MSG->Next()) {
                                 $dataDetail[$i]["TYPE"] = $fce->ZIDOC_MSG->row["TYPE"];
                                 $dataDetail[$i]["NUMBER"] = $fce->ZIDOC_MSG->row["NUMBER"];
                                 $dataDetail[$i]["MESSAGE"] = $fce->ZIDOC_MSG->row["MESSAGE"];
                                if ($dataDetail[$i]["TYPE"] == 'E') {
                                    $laporan .=  $fce->ZIDOC_MSG->row["NUMBER"] . ': ' . $fce->ZIDOC_MSG->row["MESSAGE"];
                                }
                                $i++;
                                }

                                if ($dataDetail[$i]["TYPE"] == 'E') {
                                    $laporan .= $fce->ZIDOC_MSG->row["TYPE"] . ': ' .  $fce->ZIDOC_MSG->row["NUMBER"];
                                }
                                $i++;
                                                         
                                $ResponseMessage .= $laporan;
                            }
                            }else{
                                $dataDetail['STATUS']       = 'E';
                                }
                               $dataDetail['DESC']   =   $laporan;
                                $fce->Close();                        
                                $sap->Close();  
                        if(count($dataDetail)>0){
                            $ResponseCode = '00';
                            $ResponseMessage = 'Sukses';
                            $responseRequest = array (
                                    'ResponseCode' => $ResponseCode,
                                    'ResponseMessage' => $ResponseMessage,
                                    'detailData' => $dataDetail
                            );
                        }else{
                            $ResponseCode = '01';
                            $ResponseMessage = $pesan;
                            $responseRequest = array (
                                    'ResponseCode' => $ResponseCode,
                                    'ResponseMessage' => $ResponseMessage,
                                    'detailData' => $dataDetail
                            );
                        }
                        
        }
    }
        
       
    //log service
  // $byLog='getListRelease';
   //$log_servie->log_service($inqueryRequest,$responseRequest,$byLog);
    
    return $responseRequest;
}

if ( !isset( $HTTP_RAW_POST_DATA ) ) $HTTP_RAW_POST_DATA =file_get_contents( 'php://input' );
$server->service($HTTP_RAW_POST_DATA);


?>  