<?
session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();


// $importtargetVolume='upload_customer_replenishment_report.php?act=update';
$cancelUrl='mapuserkoordinatoract.php?';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
//$halaman_id = $fungsi->get_halam_id($dirr);
$halaman_id = $fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
    ?>
                    <SCRIPT LANGUAGE="JavaScript">
                        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
                    </SCRIPT>
    
         <a href="../index.php">Login....</a>
    <?
    
   exit();
    }

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>MAPPING KOORDINATOR AREA</title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>   
<body>

<div align="center">   
    <table id="dg" title="mapping Koordinator Area" class="easyui-datagrid" style="width:100%;height:350px">
        <thead>
            <tr>
                <!-- <th field="ck" checkbox="true"></th>   -->
                <th field="ID" width="5%" hidden="true">ID</th>                   
                <th field="KOORDINATOR_KODE" width="300">KOORDINATOR AREA</th>
                <th field="USERNAME" width="300">USERNAME</th>
                <th field="DEL" width="300">AKTIF</th>
                <th field="INSERT_BY" width="200">CREATED BY</th>
                <th field="INSERT_DATE" width="100">CREATED DATE</th>
                <th field="UPDATE_BY" width="200">UPDATED BY</th>
                <th field="UPDATE_DATE" width="100">UPDATED DATE</th>
            </tr>
        </thead>
    </table>
    <div id="toolbar">
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="deleteAct()">Change Status</a>
        <a class="easyui-linkbutton" plain="true" iconCls="icon-excel"
            href="template_xls/template_mapping_koordiantor_area_user.xls">Download Template</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
            onclick="uploadAct()">Upload Excel</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
            onclick="exportToExcel()">Export Excel</a>
        <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="historyAct()">History</a> -->
    </div>
    
    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px"
         closed="true" buttons="#dlg-buttons">
        <div class="ftitle">MAPPING USER KOORDINATOR</div>
        <form id="fm" method="post" novalidate>
        <div class="fitem">
                <label>Koordonator Area</label>
                : <input name="KOORDINATOR_KODE" id="KOORDINATOR_KODE" class="easyui-textbox" required="true" style="width:200px;">
            </div>
            <div class="fitem">
                <label>Username</label>
                : <input name="USERNAME" id="USERNAME" class="easyui-textbox" required="true" style="width:200px;">
            </div>
        </form>
    </div>

    <!-- AWAL UPLOAD FILE XLS -->
    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true"
        buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload"
                    name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px"
                id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>
    <!-- AKHIR UPLOAD FILE XLS -->
    
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
</div>

<script type="text/javascript">



  function changeHandler(val)
  {
    if (Number(val.value) > 100)
    {
      val.value = 100
    }else if(val.value<=0){
        val.value = 0
    }
  }


 $(function(){
    $("#dg").datagrid({
            url:'mapuserkoordinatoract.php?act=show',
            singleSelect:true,
            pagination:true, 
            pageList:[5,10,20,30,40,50,100,200,300],
            pageSize:10,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
     $('#dg').datagrid('enableFilter');
    
 }); 

//  window.onload = function(e){ 
//     $('#proporsi_material').numberbox('options').max = 100;
//     $('#proporsi_material').numberbox('options').min = 0;
//     $('#proporsi_plant').numberbox('options').max = 100;
//     $('#proporsi_plant').numberbox('options').min = 0; 
// }

 function newAct(){
    url = 'mapuserkoordinatoract.php?act=add';
    $('#dlg').dialog('open').dialog('setTitle','Tabel Mapping Koordinator Area');
    // $('#org').combo('readonly', false);
    $('#fm').form('clear');

    // $('#org').combobox('setValue', '7000');
    // $('#proporsi_material').numberbox('options').max = 100;
    // $('#proporsi_material').numberbox('options').min = 0;
    // $('#proporsi_plant').numberbox('options').max = 100;
    // $('#proporsi_plant').numberbox('options').min = 0;
     //set value tanggal
//    $('#TARGET_DATEF').datebox('setValue', myformatter(g_now1)); 
     
//       $('#org').combobox({      
//          onChange:  function(newValue,oldValue) {
//            loadshiptolist();
//            if(newValue == 'ZAK'){
//                $('#qtysementara').val("").show();
//            } else{
//                $('#qtysementara').val("").hide();
//            }
//          }
//       });
}

function editAct(){
var row = $('#dg').datagrid('getSelected');
if (row){
    $('#dlg').dialog('open').dialog('setTitle','Edit Mapping Koordinator Area');
    var idnh = row.ID;
    $('#KOORDINATOR_KODE').textbox('setValue', row.KOORDINATOR_KODE);
    $('#DESKRIPSI').textbox('setValue', row.DESKRIPSI);
    $('#USERNAME').textbox('setValue', row.USERNAME);

    // url = 'mapuserkoordinatoract.php?act=add';
    // $('#dlg').dialog('open').dialog('setTitle','Tabel Mapping Koordinator Area');
    // // $('#org').combo('readonly', false);
    // $('#fm').form('clear');

    url = "mapuserkoordinatoract.php?act=update&id="+row.ID+"&DEL="+row.DEL+"&KOORDINATOR_KODE="+row.KOORDINATOR_KODE+"&USERNAME="+row.USERNAME;           
}else{
        alert("Pilih baris data yang ingin di edit terlebih dahulu !");
    }
}

// function editAct(){
    
//     var row = $('#dg').datagrid('getSelected');       
//     if(row){        
// //        if(row.KET != 'Dibatalkan'){            
//             $('#dlg').dialog('open').dialog('setTitle','Edit Mapping Koordinator Area');
//             var idnh = row.ID;
//             $('#KOORDINATOR_KODE').textbox('setValue', row.KOORDINATOR_KODE);
//             $('#DESKRIPSI').textbox('setValue', row.DESKRIPSI);
//             $('#USERNAME').textbox('setValue', row.USERNAME);
//             $.messager.confirm('Confirm','Apakah Anda Yakin Ingin Mengupdate Data Mapping?',function(r){
//                 if(r){                    
//                     var strURL="mapuserkoordinatoract.php?act=update&id="+row.ID+"&KOORDINATOR_KODE="+row.KOORDINATOR_KODE+"&DESKRIPSI="+row.DESKRIPSI+"&USERNAME="+row.USERNAME;
//                     alert(strURL);
//                     var req = getXMLHTTP();                    
//                     if (req) {                        
// //                        alert(company+" "+noeks+" "+nofaktur);                        
//                         req.onreadystatechange = function() {
//                             //            lodingact(0);
//                             $.messager.progress({
//                                 title:'Please waiting',
//                                 msg:'Loading data...'
//                             });
//                             if (req.readyState == 4) {
//                                 // only if "OK"
//                                 if (req.status == 200) {
// //                                    alert("SUKSES");
//                                     //                                    alert(req.responseText);
//                                     //                                    body1.appendChild(newdiv);
//                                 } else {
//                                     alert("There was a problem while using XMLHTTP:\n" + req.statusText);
//                                 }
//                                 //                lodingact(1);
//                                 $.messager.progress('close');
//                                 $('#dg').datagrid('reload');
//                             }				
//                         }			
//                         req.open("GET", strURL, true);
//                         req.send(null);
//                     }                    
//                 }
//             })
// //       } else {
// //            alert('Dokumen yang sudah dibatalkan, tidak dapat dibatalkan kembali!!');
// //       }                
//     } else {
//         alert('Pilih Baris yang Akan Dibatalkan Terlebih Dahulu');
//     }    
// }

function saveAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
        } else {
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function saveUploadAct() {
    $('#uploadForm').form('submit', {
        url: 'mapuserkoordinatoract.php?act=upload_file',
        onSubmit: function() {
            return $(this).form('validate');
        },
        success: function(result) {
            var result = eval('(' + result + ')');
            console.log(result);
            if (result.errorMsg) {
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg,
                    showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                });
                $('#dlg_upload').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: result.data,
                    showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                });
                $('#dlg_upload').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}


function deleteAct(){
    
    var row = $('#dg').datagrid('getSelected');       
    if(row){        
//        if(row.KET != 'Dibatalkan'){            
            $.messager.confirm('Confirm','Apakah Anda Yakin Ingin Mengupdate Statusnya?',function(r){
                if(r){                    
                    var strURL="mapuserkoordinatoract.php?act=delete&id="+row.ID+"&DEL="+row.DEL+"&KOORDINATOR_KODE="+row.KOORDINATOR_KODE+"&USERNAME="+row.USERNAME;           
                    var req = getXMLHTTP();                    
                    if (req) {                        
//                        alert(company+" "+noeks+" "+nofaktur);                        
                        req.onreadystatechange = function() {
                            //            lodingact(0);
                            $.messager.progress({
                                title:'Please waiting',
                                msg:'Loading data...'
                            });
                            if (req.readyState == 4) {
                                // only if "OK"
                                if (req.status == 200) {
//                                    alert("SUKSES");
                                    //                                    alert(req.responseText);
                                    //                                    body1.appendChild(newdiv);
                                } else {
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                //                lodingact(1);
                                $.messager.progress('close');
                                $('#dg').datagrid('reload');
                            }				
                        }			
                        req.open("GET", strURL, true);
                        req.send(null);
                    }                    
                }
            })
//       } else {
//            alert('Dokumen yang sudah dibatalkan, tidak dapat dibatalkan kembali!!');
//       }                
    } else {
        alert('Pilih Baris yang Akan Dibatalkan Terlebih Dahulu');
    }    
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle', 'Upload Excel Data');
    $('#uploadForm').form('clear');
}

function exportToExcel() {
        // var rows = data.rows;
        var rows = $('#dg').datagrid('getRows');

        // Filter the rows to include only the displayed columns and adjust FLAGING values
        var filteredRows = rows.map(function(row) {
            return {
                KOORDINATOR_KODE: row.KOORDINATOR_KODE,
                USERNAME: row.USERNAME,
                AKTIF: row.DEL,
                CREATED_BY: row.INSERT_BY,
                CREATED_DATE: row.INSERT_DATE,
                UPDATED_BY: row.UPDATE_BY,
                UPDATED_DATE: row.UPDATE_DATE
            };
        });

        // Convert the filtered data to a worksheet
        var worksheet = XLSX.utils.json_to_sheet(filteredRows);
        var workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Save the workbook as an Excel file
        XLSX.writeFile(workbook, "MAPPING_USER_KOORDINATOR.xls");
    }

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
#fm2{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
.icon-upload {
    background: transparent url("icon/upload.png") no-repeat scroll center center;
}

.icon-excel {
    background: transparent url("icon/excel.png") no-repeat scroll center center;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
