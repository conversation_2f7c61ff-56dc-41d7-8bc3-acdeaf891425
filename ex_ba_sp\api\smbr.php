<?php

class ApiSmbr{
    private $url_get_token = 'http://sapdev.semenbaturaja.co.id:8011/zabap_einv/x_token/fetch?sap-client=120';
    // private $url_get_shipment_cost = 'http://sapdev.semenbaturaja.co.id:8011/zabap_einv/shp/recal?sap-client=120';
    private $url_get_shipment_cost = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/get_shipment_cost.php';
    private $auth_username = 'smbr-jaya';
    private $auth_password = 'i-love-smbr';

    public function getToken(&$cookieFile)
    {
        $response = array(
            'success' => false,
            'msg' => '',
            'data' => array()
        );

        $cookieFile = tempnam(sys_get_temp_dir(), 'sap_cookie');
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_get_token);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile); // simpan cookie
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "x-csrf-token: Fetch",
            "Authorization: Basic " . $auth
        ));

        $result = curl_exec($ch);
        $error = curl_error($ch);

        if ($error) {
            curl_close($ch);
            $response['msg'] = 'Error: ' . $error;
            return $response;
        }

        // Pisahkan header dan body
        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $header = substr($result, 0, $header_size);
        $body = substr($result, $header_size);

        // Ambil token dari header
        $csrfToken = null;
        foreach (explode("\r\n", $header) as $line) {
            if (stripos($line, 'x-csrf-token:') !== false) {
                $csrfToken = trim(str_ireplace('x-csrf-token:', '', $line));
                break;
            }
        }

        curl_close($ch);

        $response['success'] = true;
        $response['msg'] = 'Success Get Token';
        $response['token'] = $csrfToken;

        return $response;
    }


    public function getShipmentCost($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        $token = '';
        $cookieFile = '';

        // get token & cookie
        // $result = $this->getToken($cookieFile);
        // if(!$result['success']){
        //     $response['msg'] = 'failed get token: '. $result['msg'];
        //     return $response;
        // }
        // $token = $result['token'];

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        // if(!$token){
        //     $response['msg'] = 'csrf-token is required';
        //     return $response;
        // }

        $no_shipment = array();

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_get_shipment_cost);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function generatePPL($params)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        $token = '';
        $cookieFile = '';

        // get token & cookie
        // $result = $this->getToken($cookieFile);
        // if(!$result['success']){
        //     $response['msg'] = 'failed get token: '. $result['msg'];
        //     return $response;
        // }
        // $token = $result['token'];

        // if(!$token){
        //     $response['msg'] = 'csrf-token is required';
        //     return $response;
        // }

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_get_shipment_cost);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data;
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }
}

?>