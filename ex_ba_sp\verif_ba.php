<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=3110;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}


$page="verif_ba.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$no_ba = $_GET['no_ba'];
// $no_ba = $_GET['no_ba'];
// $no_invoice_expeditur = $_POST['no_invoice_expeditur'];

$currentPage="verif_ba.php";
$komen="";
// if(isset($_POST['cari'])){
    // if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_ba == ""){
        $sql= "SELECT
    A.ID,
    A.NO_BA,
    A.NO_VENDOR,
    A.TOTAL_INV,
    A.PAJAK_INV,
    A.NAMA_VENDOR,
    A.KLAIM_KTG,
    A.KLAIM_SEMEN,
    A.PDPKS,
    A.PDPKK,
    A.DELETE_MARK,
    A.ORG,
    A.TOTAL_INVOICE,
    A.TGL_BA,
    A.STATUS_BA,
    A.FILENAME,
    A.ALASAN_REJECT,
    A.ID_USER_APPROVAL,
    A.SIGN_STATUS_2,
    B.NO_SHP_TRN,
    B.NO_SHP_TRN2,
    B.SOLD_TO,
    B.NAMA_SOLD_TO,
    B.NO_PAJAK_EX,
    A.TGL_INVOICE,
    B.TANGGAL_DATANG,
    B.TANGGAL_BONGKAR,
    B.SHIP_TO,
    B.NAMA_SHIP_TO,
    B.ALAMAT_SHIP_TO,
    B.SAL_DISTRIK,
    B.NAMA_SAL_DIS,
    B.NO_POL,
    B.WARNA_PLAT,
    B.KODE_PRODUK,
    B.NAMA_PRODUK,
    B.KODE_KANTONG,
    B.NAMA_KANTONG,
    B.QTY_KTG_RUSAK,
    B.QTY_SEMEN_RUSAK,
    B.QTY_SHP,
    B.TOTAL_KTG_RUSAK,
    B.TOTAL_KTG_REZAK,
    B.TOTAL_SEMEN_RUSAK,
    B.TOTAL_KLAIM_KTG,
    B.TOTAL_KLAIM_SEMEN,
    B.NO_INVOICE ,
    B.EVIDENCE_POD1,
    B.EVIDENCE_POD2,
    B.FLAG_POD,
    B.STATUS2,
    B.GEOFENCE_POD,
    B.KETERANGAN_POD,
    B.KODE_KECAMATAN,
    B.NAMA_KECAMATAN
FROM
    EX_BA A
    JOIN EX_TRANS_HDR B ON A.NO_BA = B.NO_BA WHERE B.NO_BA = :no_ba AND B.ORG in ($user_org) ORDER BY B.NO_SHP_TRN asc";
    // echo $sql;
    $query= oci_parse($conn, $sql);
    oci_bind_by_name($query, ":no_ba", $no_ba);
    oci_execute($query);

    while($row=oci_fetch_array($query)){

            $com[]=$row[ORG];
            $no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
            $no_shipment[]=$row[NO_SHP_TRN];
            $spjmd[]=$row[NO_SHP_TRN2];
            $no_ba_v[]=$row[NO_BA];
            $no_ba = $row[NO_BA];
            $no_ba_to_get=$row[NO_BA];
            $filename[]=$row[FILENAME];
            $id_v[]=$row[ID];
            $no_invoice_v[]=$row[NO_INVOICE];
            $no_invoice_ex_v[]=$row[NO_INVOICE_EX];
            $produk_v[]=$row[NAMA_PRODUK];
            $vendor_v[]=$row[NO_VENDOR];
            $nama_vendor_v[]=$row[NAMA_VENDOR];
            $no_pol_v[]=$row[NO_POL];
            $sal_distrik_v[]=$row[SAL_DISTRIK];
            $sold_to_v[]=$row[SOLD_TO];
            $nama_sold_to_v[]=$row[NAMA_SOLD_TO];
            $no_pajak_ex_v[]=$row[NO_PAJAK_EX];
            $tgl_invoice_v[]=$row[TGL_INVOICE];
            $tgl_ba_v[]=$row[TGL_BA];
            $tgl_datang_v[]=$row[TANGGAL_DATANG];
            $tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
            // $klaim_semen_v[]=$row[KLAIM_SEMEN];
            // $klaim_ktg_v[]=$row[KLAIM_KTG];            
            $klaim_semen_v[]=$row[QTY_SEMEN_RUSAK];
            $klaim_ktg_v[]=$row[QTY_KTG_RUSAK];
            $total_ktg_rusak[]=$row[TOTAL_KTG_RUSAK];
            $total_ktg_rezak[]=$row[TOTAL_KTG_REZAK];
            $total_semen_rusak[]=$row[TOTAL_SEMEN_RUSAK];
            $total_klaim_ktg[]=$row[TOTAL_KLAIM_KTG];
            $total_klaim_semen[]=$row[TOTAL_KLAIM_SEMEN];
            $pdpks_v[]=$row[PDPKS]; 
            $pend_ktg_v[]=$row[PDPKK]; 
            $pajak_v[]=$row[PAJAK_INV];
            $total_klaim_v[]=$row[TOTAL_INV];
            $no_baf[]=$row[NO_BAF];
            $status=$row[STATUS_BA];
            $statusSign2=$row[SIGN_STATUS_2];
            $lampiran[]=$row[EVIDENCE_POD1];
            $lampiran2[]=$row[EVIDENCE_POD2];
            $flag_POD[]=$row[FLAG_POD];
            $status2[]=$row[STATUS2];
            $GEOFENCE_POD[]=$row[GEOFENCE_POD];
            $KETERANGAN_POD[] = $row[KETERANGAN_POD];
            $nama_distrik[] = $row[NAMA_SAL_DIS];
            $kode_kecamatan[] = $row[KODE_KECAMATAN];
            $nama_kecamatan[] = $row[NAMA_KECAMATAN];
            $kode_toko[] = $row[SHIP_TO];
            $nama_toko[] = $row[NAMA_SHIP_TO];
            $alamat_toko[] = $row[ALAMAT_SHIP_TO];
            // $status_BA[]=$row[STATUS_BA];
        // }
        // }

    }
    $total=count($no_ba_v);
    if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

// }



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Verifikasi BASTP :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css" integrity="sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2" crossorigin="anonymous">
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<script src="../include/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">
<style>
    .hidden {
        display: none !important;
    }
</style>
</head>

<body>
<script type="text/javascript" language="JavaScript">
    //ini ni yang buat div tapi kita hidden... ocre....
    document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
    
    </script>
    <script type="text/javascript">
        checked=false;
function checkedAll (frm1) {
  
  var aa= document.getElementById('fsimpan');
 
  // console.log(lampiran_ttd)
   if (checked == false){
        
                checked = true
                 markAllRows('fsimpan');
   }else
          {
             // alert('Kondisi else 2')
          checked = false
      unMarkAllRows('fsimpan')
          }
/*  for (var i =0; i < aa.elements.length; i++) 
  {
   aa.elements[i].checked = checked;

  }
*/ }

  function markAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
      if (checkbox.checked != true){
        checkbox.checked = true;
        rows[i].className += ' selected';
      }
        }
    }

    return true;
}

function unMarkAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
      if (checkbox.checked != false){
      checkbox.checked = false;
            rows[i].className = rows[i].className.replace(' selected', '');
      }
        }
    }

    return true;
}
    </script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Review BASTP </th>
</tr></table></div>

<form id="fsimpan" name="verif_ba" method="post"  enctype="multipart/form-data" onSubmit="countChecked()">

    <!-- <div align="center">
    <table width="95%" align="center" class="table table-responsive">
    <tr>
    <th align="left" colspan="4"><span class="style5">&nbsp;Review BA Rekapitulasi </span></th>
    </tr>
    </table>
    </div> --> 
    <div align="center" class="container-fluid">
    <table width="90%" align="center" class="table table-responsive table-bordered" >
    <thead>
      <tr class="quote">
       <td ><strong><input type="button" id="check-all-btn" class="btn btn-success btn-sm" onClick="checkedAll('fsimpan');" class="form-control" style="font-size: 10px;" value="Check All"></strong></td>  
         <!-- <td align="center"><strong >No Invoice </strong></td> -->
         <td align="center"><strong >ORG </strong></td>
         <td align="center"><strong >BASTP REKAPITULASI </strong></td>
         <!-- <td align="center"><strong>No Invoice EX </strong></td> -->
         <td align="center"><strong >KODE DISTRIK </strong></td>
         <td align="center"><strong >NAMA DISTRIK </strong></td>
         <td align="center"><strong>NO SPJ </strong></td>
		 <td align="center"><strong>SPJ MD</strong></td>
         <td align="center"><strong>NO. POL </strong></td>
         <td align="center"><strong>PRODUK</strong></td>
         <td align="center"><strong>DISTRIBUTOR</strong></td>
         <td align="center"><strong>EXPEDITUR </strong></td>
         <td align="center"><strong>NAMA EXPEDITUR </strong></td>
         <td align="center"><strong>KODE KECAMATAN </strong></td>
         <td align="center"><strong>NAMA KECAMATAN </strong></td>
         <td align="center"><strong>KODE TOKO </strong></td>
         <td align="center"><strong>NAMA TOKO </strong></td>
         <td align="center"><strong>ALAMAT TOKO </strong></td>
         <!-- <td align="center"><strong>No Pajak EX </strong></td> -->
         <td align="center"><strong>TANGGAL BASTP </strong></td>
         <td align="center"><strong>TANGGAL DATANG </strong></td>
         <td align="center"><strong>Tanggal Bongkar </strong></td>
         <td align="center"><strong>QTY KLAIM SEMEN </strong></td>
         <td align="center"><strong>QTY KLAIM KANTONG</strong></td>

         <td align="center"><strong>TOTAL KTG RUSAK</strong></td>
         <td align="center"><strong>TOTAL KTG REZAK</strong></td>
         <td align="center"><strong>TOTAL SEMEN RUSAK</strong></td>
         <td align="center"><strong>TOTAL KLAIM KTG</strong></td>
         <td align="center"><strong>TOTAL KLAIM SEMEN</strong></td>

         <td align="center"><strong>PDPKS</strong></td>
         <td align="center"><strong>PEND. KTG</strong></td>
         <td align="center"><strong>Total OA</strong></td>
         <!-- <td align="center"><strong>PAJAK (PPN)</strong></td>
         <td align="center"><strong>TOTAL</strong></td> -->
         <td align="center"><strong>POD</strong></td>
         <td align="center"><strong>GEOFENCE POD</strong></td>
         <td align="center"><strong>USER</strong></td>
        <!-- <td align="center"><strong>AKSI</strong></td> -->
         <td align="center"><strong>LAMPIRAN 1</strong></td>
         <td align="center"><strong>LAMPIRAN 2</strong></td>
         <td align="center"><strong>LAMPIRAN BASTP</strong></td>
         <!-- <td align="center"><strong>Status</strong></td>
         <td align="center"><strong>Verifikasi</strong></td> -->
         <!-- <td align="center"><strong>Upload Sign BA</strong></td> -->
         <!-- <td align="center"><strong>Aksi</strong></td> -->
      </tr >
      </thead>
      <tbody>
  <?  for($i=0; $i<$total;$i++) {
        $b=$i+1;
        $orgCom="orgke".$i;
        $idke="idke".$i;
        $appke=$id_v[$i];
        $urutke="urutke".$i;
        if(($i % 2) == 0)   {    
        echo "<tr class='row0' id='$rowke' >";
            }
        else    {   
        echo "<tr class='row1'  id='$rowke' >";
            }   

        ?>     
        <?php if($status == 50){
            ?>
            <td align="center"><input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" checked disabled/> <? echo $b; ?></td>
            <?php
        }else{
            ?>
             <td align="center"><input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" class="ba-checkbox" value="<?=$appke;?>" /> <? echo $b; ?></td>
            <?php
        } ?>
       
                <td align="center"><? echo $com[$i]; ?><input name="<?=$orgCom;?>" id="<?=$orgCom;?>" type="hidden" value="<?=$com[$i];?>" /></td>
        <? 
        // $no_cek=$no_ba_v[$i];
        // $sql_print= "SELECT KODE_PRODUK FROM EX_TRANS_HDR WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0' GROUP BY KODE_PRODUK ";
        // $query_print= oci_parse($conn, $sql_print);
        // oci_execute($query_print);
    
        // $row_print=oci_fetch_array($query_print);
        // $kode_produk=$row_print[KODE_PRODUK];
        
        // $rest = substr($kode_produk, 0, -5);
        ?>  
        <td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a><input name="no_ba" id="no_ba" type="hidden" value="<?=$no_ba_v[$i];?>" /><input name="id_ba" id="id_ba" type="hidden" value="<?=$id[$i];?>" /></td>
        <!-- <td align="center"><? echo $no_invoice_ex_v[$i]; ?></td> -->
        <td align="center"><? echo $sal_distrik_v[$i]; ?></td>
        <td align="center"><? echo $nama_distrik[$i]; ?></td>
        <td align="center"><? echo $no_shipment[$i]; ?> <input name="no_spj<?php echo $i ?>" id="no_spj" type="hidden" value="<?=$no_shipment[$i];?>" /></td>
        <td align="center"><? echo $spjmd[$i]; ?></td>
        <td align="center"><? echo $no_pol_v[$i]; ?></td>
        <td align="center"><? echo $produk_v[$i]; ?></td>
        <td align="center"><? echo $sold_to_v[$i]." / ".$nama_sold_to_v[$i]; ?></td>
        <td align="center"><? echo $vendor_v[$i]; ?></td>
        <td align="center"><? echo $nama_vendor_v[$i]; ?></td>
        <td align="center"><? echo $kode_kecamatan[$i]; ?></td>
        <td align="center"><? echo $nama_kecamatan[$i]; ?></td>
        <td align="center"><? echo $kode_toko[$i]; ?></td>
        <td align="center"><? echo $nama_toko[$i]; ?></td>
        <td align="center"><? echo $alamat_toko[$i]; ?></td>
        <!-- <td align="center"><? echo $no_pajak_ex_v[$i]; ?></td> -->
        <td align="center"><? echo $tgl_ba_v[$i]; ?></td>
        <td align="center"><? echo $tgl_datang_v[$i]; ?></td>
        <td align="center"><? echo $tgl_bongkar_v[$i]; ?></td>
        <td align="center"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($klaim_ktg_v[$i],0,",","."); ?></td>

        <td align="center"><? echo number_format($total_ktg_rusak[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($total_ktg_rezak[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($total_semen_rusak[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($total_klaim_ktg[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($total_klaim_semen[$i],0,",","."); ?></td>

        <td align="center"><? echo number_format($pdpks_v[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($pend_ktg_v[$i],0,",","."); ?></td>
        <td align="center"><? echo number_format($total_klaim_v[$i],2,",","."); ?></td>
        <!-- <td align="center"><? echo number_format($pajak_v[$i],2,",","."); ?></td>
        <td align="center"><? echo number_format($total_klaim_v[$i]+$pajak_v[$i],2,",","."); ?></td> -->
        <td align="center"><? echo $flag_POD[$i]; ?></td>
        <td align="center"><a href="javascript:popUp('https://www.google.com/search?q=<?php echo $GEOFENCE_POD[$i] ?>')"><?php echo $GEOFENCE_POD[$i] ?></a></td>
        <td align="center"><? echo $KETERANGAN_POD[$i]; ?></td>
        <td align="center"><? if ($lampiran[$i] == '') {
        echo "File SPJ Belum Diupload";
        }else{
            ?>
        <a href="javascript:popUp('<?php echo $lampiran[$i] ?>')">Lampiran SPJ</a>
        <?php
        } ?></td>
        <td align="center"><? if ($lampiran2[$i] == '') {
        echo "File TTD SPJ Belum Diupload";
        }else{
            ?>
        <a href="javascript:popUp('<?php echo $lampiran2[$i] ?>')">Lampiran TTD SPJ</a>
        <?php
        } ?></td>
        <?php if($filename[$i] != ''){
            ?>
            <td align="center"><a href="javascript:popUp('upload/<?=$filename[$i]?>')">Lampiran BASTP</a></td>
            <?php
        }else{
             ?>
            <td align="center">Lampiran BASTP kosong</td>
            <?php
        } ?>
        
        
        <input name="total" id="total" type="hidden" value="<?=$total;?>" />
        </tr>
        <? } ?>
        </tbody>
      <tr class="quote">
        <td colspan="37" align="center">
      <!--   <?php if($status == '40'){
            ?>
            File Sign Kabiro : <input type="file" name="upload_sign_kabiro" accept=".pdf">
            <?php
        } ?>
    </td> -->
    <tr>
        <tr class="quote">
        <td colspan="37" align="left">
            Alasan Cancel : <textarea class="form-control" rows="5" name="ket_cancel" id="ket_cancel"></textarea>
        </td>
        </tr>
        <tr class="quote">
        <td colspan="37" align="center">
        <? if($status == '20' || $status == '21'){
            ?>
            
            <input name="simpan" type="submit" class="btn btn-success btn-sm" style="font-size: 10px" id="simpan" value="Submit" onclick="countChecked()" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <a href="daftar_ba_trans.php" target="isi" class="button">Back</a>       
            <input name="action" type="hidden" value="verif_ba" />
            <input name="totalCheked" id="totalCheked" type="hidden" value="" />
            <?php
        }elseif($status == '30'){
            ?>
                <input name="simpan" type="submit" class="btn btn-success btn-sm" style="font-size: 10px" id="simpan" value="Submit" onclick="countChecked()"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <!-- <input name="simpan" type="submit" class="btn btn-success btn-sm" id="simpan" value="Approve" style="font-size: 10px"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a href="reject_ba.php?no_ba=<?=$no_ba_to_get?>" class="btn btn-danger btn-sm" style="font-size: 10px">Reject</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; -->
                <!-- <input name="simpan" type="submit" class="button" id="simpan" value="Reject"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; -->
                <a href="kasie_ba_trans.php" target="isi" class="btn btn-secondary btn-sm" style="font-size: 10px;color: white;">Back</a>       
                <input name="action" type="hidden" value="approve_kasie" />
                <input name="totalCheked" id="totalCheked" type="hidden" value="" />
                <?php
        }elseif($status == '40'){
            ?>
                 <input name="simpan" type="submit" class="btn btn-success btn-sm" style="font-size: 10px; display: none;" id="simpan" value="Submit" onclick="countChecked()"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                 <button type="button" class="btn btn-success btn-sm" style="font-size: 10px" id="submit-sign" onclick="submitSignKeyla()">
                    Submit ESign (Keyla)
                </button>
                <button type="button" class="btn btn-primary btn-sm" style="font-size: 10px" id="submit-sign" onclick="submitSignOtp()">
                    Submit ESign (OTP)
                </button>
                <button onclick="window.location.href='verif_ba_sign_qr.php?no_ba=<?= $no_ba ?>'" id="approve_qr" type="button" class="btn btn-primary btn-sm" style="font-size: 10px">
                    Submit ESign (QR System)
                </button>
                <button id="reject_qr" type="button" class="btn btn-primary btn-sm" style="font-size: 10px" id="submit-sign" onclick="rejectSignQR()">
                    Submit ESign (QR System)
                </button>
                <input type="hidden" id="sign_type" name="sign_type" value="">

                <!-- <input name="simpan" type="submit" class="btn btn-success btn-sm" style="font-size: 10px" id="simpan" value="Approve"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a href="reject_ba.php?no_ba=<?=$no_ba_to_get?>" class="btn btn-danger btn-sm" style="font-size: 10px">Reject</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; -->
                <!-- <input name="simpan" type="submit" class="button" id="simpan" value="Reject"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; -->
                <a href="kabiro_ba_trans.php" target="isi" class="btn btn-secondary btn-sm" style="font-size: 10px;color: white;">Back</a>       
                <input name="action" type="hidden" value="approve_kabiro" />
                <input name="totalCheked" id="totalCheked" type="hidden" value="" />
                <?php
        } ?>
        
        </td>
        </tr>
        
    </table>
    </div>
   
<div align="center">
<?
echo $komen;

?></div>
        </form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
    <script language=javascript>
    //We write the table and the div to hide the content out, so older browsers won't see it
        obj=document.getElementById("tunggu_ya");
        obj.style.display = "none";
        obj_tampil=document.getElementById("halaman_tampil");
        obj_tampil.style.display = "inline";

        let formAction = '';

        function submitSignKeyla() {
            formAction = 'submit_sign';

            document.getElementById('sign_type').value = 'keyla';
            document.getElementById('simpan').click();
        }

        function submitSignOtp() {
            formAction = 'submit_sign';

            document.getElementById('sign_type').value = 'otp';
            document.getElementById('simpan').click();
        }

        function rejectSignQR() {
            formAction = 'submit_sign';

            document.getElementById('sign_type').value = 'qr';
            document.getElementById('simpan').click();
        }

        function approveSignQR() {
            
        }

        function countChecked(type = ''){
            let action = 'komentar.php';
            
            // return false;
            var total= document.getElementById('total').value;
            var ket_cancel= document.getElementById('ket_cancel').value;
            // alert('total 1'+total);
        var countChecked = document.querySelectorAll('input[type="checkbox"]:checked').length;
        if(countChecked < total){
            if(ket_cancel == ''){
                alert('WAJIB ISI KETERANGAN CANCEL JIKA ADA SPJ YANG TIDAK DI CHECKLIST');
                return false;
            }else{
                document.getElementById("fsimpan").action = action;
                return true;
            }
        }else{
            if (formAction === 'submit_sign') {
                action = 'verif_ba_sign.php';
            }

            // alert("Total "+countChecked);
        document.getElementById("totalCheked").value = countChecked;
        document.getElementById("fsimpan").action = action;
        return true;
        }
        
    }
    </script>

    <?php if ($status == '40' && ((bool) $statusSign2)): ?>
        <script>
            // $('body').hide();
            $('#check-all-btn').prop('disabled', true);
            $('textarea[name="ket_cancel"').prop('disabled', true);
            $('.ba-checkbox').prop('checked', true).prop('disabled', true);
            // $('#submit-sign').click();
        </script>
    <?php endif ?>

    <script>
    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById("approve_qr").classList.add("hidden");
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');

        function handleCheckboxChange() {
            const anyUnchecked = Array.from(checkboxes).some(cb => !cb.checked);
      
            if (anyUnchecked) {
                document.getElementById("reject_qr").classList.remove("hidden");
                document.getElementById("approve_qr").classList.add("hidden");
            } else {
                document.getElementById("reject_qr").classList.add("hidden");
                document.getElementById("approve_qr").classList.remove("hidden");
            }
        }

        checkboxes.forEach(function (checkbox) {
        checkbox.addEventListener("change", handleCheckboxChange);
        });
    });
    </script>
</body>
</html>
