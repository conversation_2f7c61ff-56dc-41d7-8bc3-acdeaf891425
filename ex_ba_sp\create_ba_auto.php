<?php
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

//Koneksi DEVSD
require_once '../include/oracleDev.php'; 
$fungsi2=new conntoracleDEVSD();
$conn2=$fungsi2->DEVSDdb();

require_once 'helper.php';

// query select and grouping by SHIP_TO, SHIPMENT_COST, ORG
$sql= "WITH Ranked AS (
    SELECT 
        DENSE_RANK() OVER (ORDER BY SHIP_TO, SHP_COST, WARNA_PLAT) AS ROW_NUM,
        t.*
    FROM EX_PAJAK_HDR_V4 t
    WHERE DELETE_MARK = '0' 
    AND STATUS = 'OPEN' 
    AND STATUS2 = 'OPEN' 
    AND VEHICLE_TYPE <> '205' 
    AND KELOMPOK_TRANSAKSI = 'DARAT' 
    AND TIPE_TRANSAKSI = 'BAG'
    AND STATUS_PAJAK = 'OK' 
    AND TO_CHAR(TANGGAL_KIRIM, 'YYYY-MM') = TO_CHAR(SYSDATE, 'YYYY-MM') 
    AND ORG IN ('3000','1000')
    AND (REJECT_STATUS <> '1' OR REJECT_STATUS IS NULL)
)
SELECT 
	(SELECT MAX(ROW_NUM) FROM Ranked) AS ROW_COUNT,
    r.*
FROM Ranked r
ORDER BY r.ROW_NUM";

$query= oci_parse($conn, $sql);
oci_execute($query);
$msg = "";
$user_approval = array();

while($row=oci_fetch_array($query)){
    $org = $row[ORG];
    $flag_POD = $row[FLAG_POD];
    $lampiran = $row[EVIDENCE_POD1];
    $lampiran2 = $row[EVIDENCE_POD2];
    $warna_plat_in = strtoupper($row[WARNA_PLAT]);
    $no_shipment_v = $row[NO_SHP_TRN];
    $vendor = $row[VENDOR];
    $row_num= $row[ROW_NUM];
    $id = $row[ID];
    $spj_group_classified_count = $row[ROW_COUNT];
    $ship_to_v = $row[SHIP_TO];
    $geo_pod = $row[GEOFENCE_POD];

    $api = new Customer();
    $response = $api->getList($ship_to_v, $org);

    if(!$response['success']){
        $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
        $field_data = array("1", "SYSDATE", "SYSTEM", "gagal mendapatkan data geofence customer: ". $response['msg']);
        $tablename = "EX_TRANS_HDR";
        $field_id = array('ID');
        $value_id = array("$id");
        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        
        $msg .= "(X) => " . $no_shipment_v . ": gagal mendapatkan data geofence customer: ". $response['msg'] . "<br>"; 
        continue;
    }else{
        if(!$response['data']['XLONG'] || !$response['data']['LAT']){
            $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
            $field_data = array("1", "SYSDATE", "SYSTEM", "data geofence customer tidak ditemukan");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$id");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            
            $msg .= "(X) => " . $no_shipment_v . ": data geofence customer tidak ditemukan <br>"; 
            continue;
        }

        $geo_pod_parts = explode(", ", $geo_pod);

        $lat_pod =  str_replace(',', '.', trim($geo_pod_parts[1]));
        $long_pod = str_replace(',', '.', trim($geo_pod_parts[0]));
        $lat_cust = str_replace(',', '.', $response['data']['LAT']);
        $long_cust = str_replace(',', '.', $response['data']['XLONG']);
        $distance = getDistanceMeter($lat_pod, $long_pod, $lat_cust, $long_cust);
        $distance_limit = get_batas_jarak_geofence($conn);

        // batas(dalam meter)
        if($distance > $distance_limit){
            $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
            $field_data = array("1", "SYSDATE", "SYSTEM", "jarak antara geofence pod dan customer melebihi batas(".$distance_limit."meter)");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$id");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            
            $msg .= "(X) => " . $no_shipment_v . ": jarak antara geofence pod dan customer melebihi batas(".$distance_limit."meter) <br>"; 
            continue;
        }
    }

    $tanggal_kirim = $row['TANGGAL_KIRIM'];
    // Pastikan tanggal valid
    $timestamp = strtotime($tanggal_kirim);
    if ($timestamp !== false) {
        $day = (int)date('d', $timestamp);
        
        if ($day > 25) {
            $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
            $field_data = array("1", "SYSDATE", "SYSTEM", "tanggal kirim lewat dari tanggal 25 bulan berjalan");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$id");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $msg .= "(X) => " . $no_shipment_v . ": tanggal kirim lewat dari tanggal 25 bulan berjalan <br>"; 
            continue;
        }
    }

    if (!in_array($flag_POD, array('POD-FIOS'))){
        
        $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
        $field_data = array("1", "SYSDATE", "SYSTEM", "jenis pod bukan POD-FIOS");
        $tablename = "EX_TRANS_HDR";
        $field_id = array('ID');
        $value_id = array("$id");
        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        
        $msg .= "(X) => " . $no_shipment_v . ": jenis pod bukan POD-FIOS <br>"; 
        continue;
    } else if($warna_plat_in != "KUNING" && $warna_plat_in != "HITAM"){
        
        $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
        $field_data = array("1", "SYSDATE", "SYSTEM", "Data No Pajak Tidak Valid");
        $tablename = "EX_TRANS_HDR";
        $field_id = array('ID');
        $value_id = array("$id");
        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        
        $msg .= "(X) => " . $no_shipment_v . ": Data No Pajak Tidak Valid cek no pajak anda pada transaksi sebelumnya.. <br>"; 
        continue;
    } else if($lampiran == '' || $lampiran2 == ''){
        $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
        $field_data = array("1", "SYSDATE", "SYSTEM", "lampiran evidence pod tidak lengkap");
        $tablename = "EX_TRANS_HDR";
        $field_id = array('ID');
        $value_id = array("$id");
        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        
        $msg .= "(X) => " . $no_shipment_v . ": lampiran evidence pod tidak lengkap <br>"; 
        continue;
    }

    // if (!isset($shiptoblack[$org])) {
    //     $sqlcek = "SELECT SHIPTO FROM OR_MAP_SHIPTO_3PL WHERE ORG = :org AND DELETE_MARK = '0'";
    //     $querycek = oci_parse($conn, $sqlcek);
    //     oci_bind_by_name($querycek, ':org', $org);
    //     oci_execute($querycek);

    //     $shiptoblack[$org] = array();
        
    //     while ($datacek = oci_fetch_assoc($querycek)) {
    //         array_push($shiptoblack[$org], $datacek['SHIPTO']);
    //     }
    // }

    // if((in_array($row[SHIP_TO], $shiptoblack[$org])) && ($status_asal=="NON-ELOG")){

    //     $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
    //     $field_data = array("1", "SYSDATE", "SYSTEM", "shipto non-elog bermasalah");
    //     $tablename = "EX_TRANS_HDR";
    //     $field_id = array('ID');
    //     $value_id = array("$id");
    //     $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    //     $msg .= "(X) => " . $no_shipment_v . ": shipto non-elog bermasalah <br>"; 
    //     continue;
    // } else if((!in_array($row[SHIP_TO], $shiptoblack[$org])) && ($status_asal=="E-LOG")){

    //     $field_names = array('REJECT_STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NOTE');
    //     $field_data = array("1", "SYSDATE", "SYSTEM", "shipto e-log bermasalah");
    //     $tablename = "EX_TRANS_HDR";
    //     $field_id = array('ID');
    //     $value_id = array("$id");
    //     $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    //     $msg .= "(X) => " . $no_shipment_v . ": shipto e-log bermasalah <br>"; 
    //     continue;
    // }

    $no_pajak_vendor_in = "";

    // get user approval vendor
    if(!isset($user_approval[$vendor])){
        $user = new User_SP();
        $pejabat_eks_auto = $user->get_pejabat_eks_auto();
        $user_approval[$vendor] = $pejabat_eks_auto[0];
    }

    // max number of spj per invoice = 100, jika lebih makan masuk ke next invoice
    $n = 0;
    $i = 0;
    while($n >= 100){
        if(!isset($spj_group_classified[$row_num][$i]));
        $spj_group_classified[$row_num][$i] = array();

        $n = count($spj_group_classified[$row_num][$i]);

        $i++;
    }
    $spj_group_classified[$row_num][$i][] = $row;
};

for ($i = 1; $i <= $spj_group_classified_count; $i++) {
    if (!isset($spj_group_classified[$i]))
        continue;

    foreach($spj_group_classified[$i] as $spj_group){
        $NO_BA_in = $fungsi->new_invoice_ba($conn);
        $NO_BA_vendor_in = $NO_BA_in;
        $inv_old = $NO_BA_in;
        $no_vendor_in = $spj_group[0][VENDOR];
        $nama_vendor_in = $spj_group[0][NAMA_VENDOR];
        $id_user_approval = $user_approval[$no_vendor_in][ID];
        
        $id_trans = array();
        foreach ($spj_group as $spj) {
            $id = $spj[ID];
            $no_spj = $spj[NO_SHP_TRN];

            $field_names = array('NO_BA', 'STATUS', 'TANGGAL_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2', 'NO_TAGIHAN', 'REJECT_STATUS');
            $field_data = array("$NO_BA_in", "OPEN BA", "SYSDATE", "SYSDATE", "SYSTEM", "OPEN BA", "$inv_old", '0');
            $tablename = "EX_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$id");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        
            $id_trans[] = $no_spj;
        }

        $sqlcek = "SELECT ORG, QTY_KTG_RUSAK, QTY_SEMEN_RUSAK, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_BA = '$NO_BA_in' GROUP BY ORG,QTY_KTG_RUSAK, QTY_SEMEN_RUSAK ";
        $querycek = oci_parse($conn, $sqlcek);
        oci_execute($querycek);
        $row_data = oci_fetch_assoc($querycek);
        $qty_semen_rusak = $row_data[QTY_SEMEN_RUSAK];
        $qty_ktg_rusak = $row_data[QTY_KTG_RUSAK];
        $total_klaim_in = $row_data[TOTAL_KLAIM];
        $total_shp_in = $row_data[SHP_COST];
        $total_ktg_in = $row_data[TOTAL_KTG];
        $total_semen_in = $row_data[TOTAL_SEMEN];
        $total_pdpks_in = $row_data[TOTAL_PDPKS];
        $total_pdpkk_in = $row_data[TOTAL_PDPKK];
        $org_in = $row_data[ORG];
        //tambahan pengujian pajak 11 persen 01-04-2022
        if ($warna_plat_in == "HITAM")
            if (date("Ymd") >= 20220401 && date("Ymd") < 20241101){
                $pajak_in = round($total_shp_in * 0.11, 0);
            }elseif (date("Ymd") >= 20241101){
                $pajak_in = round($total_shp_in * 0.12, 0);
            }else{
                $pajak_in = round($total_shp_in * 0.1, 0);   
            }
        //----------------------------------
        else
            $pajak_in = 0;
        $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
        $field_names = array('NO_BA_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_BA', 'TGL_BA', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'TGL_TERMIN', 'TERMIN', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI','NO_BAF','COUNT_SPJ','STATUS_BA', 'ID_USER_APPROVAL');
        $field_data = array("$NO_BA_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$qty_ktg_rusak", "$qty_semen_rusak", "$total_pdpks_in", "$NO_BA_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "SYSTEM", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$tgl_tremin", "$termin", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi","$noBaF","$sampai","10", $id_user_approval);
        $tablename = "EX_BA";
        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

        //INSERT LOG HISTORY BA
        $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
        $field_data = array("$NO_BA_in","10","OPEN","0","SYSDATE"); // CREATED_BY = 0 => CREATED BY SYSTEM
        $tablename = "EX_BA_TRACK";
        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

        //send email to kepala ditran
        $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = $id_user_approval";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $row = oci_fetch_assoc($query);
        $data_email = $row['ALAMAT_EMAIL'];
        $emailTO = $data_email;
        $emailTOCC = '';

        $data = array(
            "no_ba" => $NO_BA_in
        );
        $data = json_encode($data);
        $approve_param = "approve_ba_ex||$data";
        $approve_param_encode = base64_encode($approve_param);
        $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";
        sendMailApprove($NO_BA_in,$org_in,$no_vendor_in,$nama_vendor_in,$total_semen_in,$total_pdpks_in,$total_shp_in,$emailTO,$emailTOCC,$approve_link);
    
        if(count($id_trans) > 0){
            $msg .= "(S) => NO. SPJ(" . implode(", ", $id_trans) . ") : BASTP Berhasil dibuat dengan No. BASTP $NO_BA_in <br>"; 
        }    
    }
    
}

if($msg){
    echo $msg;
}else{
    echo "Tidak ada data yang dapat diproses";
}
?>
