<?php
/**
 * description : class untuk scheduller post data create SO SAP
 * author : PT.Artavel
 * tambah field NAMA_KAPAL DI Tabel OR_TRANS_HDR, field NO_KONTRAK_POSNR Di tabel OR_TRANS_DTL
 * edit file or_transaksi/formula.php edit case tambah new untuk insert variabel $nama_kapal ke field NAMA_KAPAL
 */
set_time_limit(0);
class createSoSAP {

    private $fungsi;
    private $conn;
    private $logfile;
    public $msg;
    public $dataFuncRFC;
    private $status;

    function __construct() {
        $this->status = 'SUKSES';
        $this->fungsi = new or_fungsi();
        $this->conn = $this->fungsi->or_koneksi();
        // $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
    }

    function saveLog() {
        $this->msg = substr($this->msg, 0, 900);
        $sqllog = "INSERT INTO RFC_LOG VALUES ('CREATE SO',SYSDATE,'" . $this->msg . "')";
        $querylog = oci_parse($this->conn, $sqllog);
        if ($querylog) {
            $execlog = @oci_execute($querylog);
        }
        //set running down
        $sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
        $query_run = oci_parse($this->conn, $sqlset_run);
        oci_execute($query_run);
        //end set
    }

    function run() {
        // fwrite($this->logfile, "Start ".get_class($this)."pada tanggal jam ".date('d-m-Y H:i:s')."\n");
        $this->msg = "Start " . get_class($this) . "pada tanggal jam " . date('d-m-Y H:i:s') . "\n";
        $datapp = array();
        // proyek SOCC V.2
        $sqlhdr = "SELECT * FROM CREATESOSAP7000ZAK_V WHERE DELETE_MARK='0' AND (NOTE != 'ics_pp' OR NOTE IS NULL) ";
    //     $sqlhdr = "SELECT
    //     or_trans_hdr.*
    // FROM
    //     OR_TRANS_HDR LEFT JOIN OR_TRANS_DTL ON or_trans_hdr.NO_PP=or_trans_dtl.NO_PP 
    // WHERE
    //     (or_trans_hdr.STATUS = 'OPEN' OR or_trans_hdr.STATUS = 'PROCESS')
    //     AND or_trans_hdr.SO_TYPE IN ('ZOR', 'ZFC')
    //     AND (or_trans_hdr.BPLANT <> 'OTHER'
    //         OR or_trans_hdr.BPLANT IS NULL)
    //     AND or_trans_hdr.NO_SHP_OLD IS NULL
    //     AND TO_CHAR(or_trans_hdr.CREATE_DATE, 'YYYY-MM-DD') >= '" . date('Y-m-d') . "'
    //     AND or_trans_hdr.DELETE_MARK = 0
    //     AND or_trans_hdr.TIPEPP = 'NEW PP'
    //     AND (or_trans_hdr.ORG = '7000')
    //     AND or_trans_hdr.TERM_PAYMENT IS NOT NULL
    //     AND or_trans_hdr.NAMA_TOP IS NOT NULL
    //     AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'";
        //
    //    $sqlhdr = "SELECT * FROM OR_TRANS_HDR WHERE (STATUS = 'OPEN' OR STATUS = 'PROCESS') AND SO_TYPE in ('ZOR', 'ZFC') AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND NO_SHP_OLD IS NULL AND TO_CHAR(CREATE_DATE,'YYYY-MM-DD') = '" . date('Y-m-d') . "' AND DELETE_MARK = 0 AND TIPEPP = 'NEW PP' AND (ORG = '7000') AND TERM_PAYMENT IS NOT NULL AND NAMA_TOP IS NOT NULL AND NOTE IS NULL";
        //$sqlhdr = "SELECT * FROM OR_TRANS_HDR WHERE (STATUS = 'OPEN' OR STATUS = 'PROCESS') AND SO_TYPE in ('ZOR', 'ZFC') AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND NO_SHP_OLD IS NULL AND TO_CHAR(CREATE_DATE,'YYYY-MM-DD') >= '2020-12-14' AND DELETE_MARK = 0 AND TIPEPP = 'NEW PP' AND (ORG = '5000' OR ORG = '7000' OR ORG = '7900') AND TERM_PAYMENT IS NOT NULL AND NAMA_TOP IS NOT NULL AND NOTE IS NULL";       
       //$sqlhdr = "SELECT * FROM OR_TRANS_HDR WHERE (STATUS = 'OPEN' OR STATUS = 'PROCESS') AND SO_TYPE in ('ZOR', 'ZFC') AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND NO_SHP_OLD IS NULL AND TO_CHAR(CREATE_DATE,'YYYY-MM-DD') = '" . date('Y-m-d') . "' AND DELETE_MARK = 0 AND TIPEPP = 'NEW PP' AND (ORG = '5000' OR ORG = '7000' OR ORG = '7900') AND TERM_PAYMENT IS NOT NULL AND NAMA_TOP IS NOT NULL AND NOTE IS NULL";
       $queryhdr = oci_parse($this->conn, $sqlhdr);
        oci_execute($queryhdr);
        $i = 0;
        $data = array();
        while ($datafunc = oci_fetch_assoc($queryhdr)) {
            $datapp[$i] = $datafunc;
            $datapp[$i]['TRANS_DTL'] = array();
            ///////////////////////////////////////////////// proyek SOCC V.2
            // $sqldtl = "SELECT * FROM OR_TRANS_DTL WHERE NO_PP = '" . $datafunc['NO_PP'] . "'";
            $sqldtl = "SELECT CREATESOSAP7000ZAK_V.TANGGAL_PRELEADTIME, or_trans_dtl.* FROM OR_TRANS_DTL LEFT JOIN CREATESOSAP7000ZAK_V ON or_trans_dtl.NO_PP = CREATESOSAP7000ZAK_V.NO_PP WHERE or_trans_dtl.NO_PP = '" . $datafunc['NO_PP'] . "' ";
            ///////////////////////////////////////////////
            $querydtl = oci_parse($this->conn, $sqldtl);
            oci_execute($querydtl);
            while ($datadtl = oci_fetch_assoc($querydtl)) {
                array_push($datapp[$i]['TRANS_DTL'], $datadtl);
            }
            $i++;
        }

        if (count($datapp) > 0) {
            for ($i = 0; $i < count($datapp); $i++) {
                // $this->postSoSAP_MD($datapp[$i]);
                $resultPostSoSAP = $this->postSoSAP($datapp[$i]);
                if ($resultPostSoSAP) {
                    $data[] = $resultPostSoSAP;
                }
            }
        } else {
            echo 'Tidak ada data';
        }

        // sv order API Epoool Push SO
        if (!empty($data)) {
        $url = 'https://dev-integrasi-api.sig.id/epoool/api/sales_order';
        $username = 'login_epoool';
        $password = 'S1G-3p0OoL3#';
        $token = 'Bearer hc6ULG54uzXugcjYBhHg';
        $options = array(
            'http' => array(
                'header'  => "Content-type: application/json\r\n" .
                            "Authorization: Basic " . base64_encode("$username:$password") . "\r\n" .
                            "token: $token\r\n",
                'method'  => 'POST',
                'content' => json_encode($data),
            )
        );
        $context  = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        }
        
        $this->saveLog();
        // fclose($this->logfile);
    }

    function postSoSAP($datapp) {
        $show_ket = 'proses pp no ' . @$datapp['NO_PP'] . "\n";
        $user_name = 'SCHEDULER';
        $so_type = $datapp['SO_TYPE'];
        $nama_so_type = $datapp['NAMA_SO_TYPE'];
        $sales_org = $datapp['ORG']; //$_POST['org'];
        if ($so_type == 'ZEX') {
            $distr_chan = "30";
        } elseif ($so_type == 'ZPR') {
            if ($sales_org == '2000' || $sales_org == '7000' || $sales_org == '5000')
                $distr_chan = "10";
            else if ($sales_org == '6000')
                $distr_chan = "10";
            else
                $distr_chan = "50";
        }
        else {
            $distr_chan = "10";
        }
        $division = "00";
        if ($so_type == 'ZFC') {
            $dlv_block = "Z1";
        }else{
            $dlv_block = "";
        }
        $soldto = $datapp['SOLD_TO'];
        $soldto = $this->fungsi->sapcode($soldto);
        // $distr_chancondi = trim($this->fungsi->findOneByOne($this->conn, "TB_USER_BOOKING", "DISTRIBUTOR_ID", $soldto, "CHANNEL"));
        // if ($distr_chancondi != '') {
        //     $distr_chan = $distr_chancondi;
        // }
        $nama_sold_to = $datapp['NAMA_SOLD_TO'];
        $tgl_pp = date("d-m-Y");
        list($day, $month, $year) = split("-", $tgl_pp);
        $tgl_pp = $year . $month . $day;
        //$tanggal=gmdate("Ymd",time()+60*60*7);
        $top = $datapp['TERM_PAYMENT']; //field term payment
        $nama_top = $datapp['NAMA_TOP']; //field nama top
        $reason = $datapp['KD_REASON'];
        $nama_reason = $datapp['NM_REASON'];

        $plant = $datapp['PLANT_ASAL']; //field plant_asal
        $nama_plant = trim($datapp['NAMA_PLANT']); //field nama_plant
        $incoterm1 = $datapp['INCOTERM']; //field INCOTERM
        $incoterm2 = $datapp['NAMA_INCOTERM']; //field NAMA_INCOTERM
        $route = $datapp['ROUTE']; //field ROUTE
        $nama_kapal = $datapp['NAMA_KAPAL']; //field trans_dtl NAMA_KAPAL
        $pricelist = $datapp['PRICELIST']; //field PRICELIST
        $nama_pricelist = $datapp['NAMA_PRICELIST']; //field NAMA_PRICELIST
        $ship_cond = $this->fungsi->findOneByOne($this->conn, "TB_ROUTE", "ROUTE", $route, "SHIP_COND");
        $no_pp = $datapp['NO_PP']; //FIELD NO_PP
        $lcnum = $datapp['NO_KONTRAK_LC']; //field NO_KONTRAK_LC
        $sampai1 = count($datapp['TRANS_DTL']); //jumlah produk count TRANS_DTL
        for ($j = 0; $j < $sampai1; $j++) {
            $kontrakh = $datapp['TRANS_DTL'][$j]['NO_KONTRAK']; //field no kontrak TRANS_DTL
        }
        $com_royalty = '-';

        //call bapi sales order sap
        $sap = new SAPConnection();
        $sap->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            echo $sap->PrintStatus();
            $this->msg .= $sap->PrintStatus();
            $this->status = 'GAGAL';
            $this->saveLog();
            return false;
        }

        // SOCC v2
        $looping_end=1;
        $movetestrun='Z';
        for ($looping = 0; $looping <= $looping_end; $looping++) {
        if($movetestrun!='X'){
        ////////////
        // $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
        $fce = $sap->NewFunction("ZBAPI_SALESORDER_CREATEFROMDAT");
        if ($fce == false) {
            echo $sap->PrintStatus();
            $this->msg .= $sap->PrintStatus();
            $this->status = 'GAGAL';
            $this->saveLog();
            return false;
        }

        //header entri
        $fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type; //"ZOR";
        $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"3000";
        $fce->ORDER_HEADER_IN["DISTR_CHAN"] = $distr_chan; //"10";
        $fce->ORDER_HEADER_IN["DIVISION"] = $division; //"00";
        $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $dlv_block; //"Z1";
        $fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp; //"Z1";
        $fce->ORDER_HEADER_IN["PURCH_DATE"] = $tgl_pp; //"Z1";
        $fce->ORDER_HEADER_IN["PMNTTRMS"] = $top; //"Z1";
        $fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1; //"Z1";
        $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm2;
        $fce->ORDER_HEADER_IN["SHIP_COND"] = $ship_cond;
        $fce->ORDER_HEADER_IN["NAME"] = $nm_kapal;
        $fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist;
        $fce->ORDER_HEADER_IN["ORD_REASON"] = $reason;
        $fce->ORDER_HEADER_IN["REF_1"] = $oldso;

        if ($lcnum != '') {
            $fce->ORDER_HEADER_IN["PMTGAR_PRO"] = 'Z00001';
            $fce->ORDER_HEADER_IN["DOC_NUM_FI"] = $lcnum;
            // $fce->ORDER_HEADER_IN["REC_POINT"] = 2;
        }
        if ($kontrakh != '') {
            $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
            $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrakh;
        }

        //detail entri item'
        for ($j = 1; $j <= $sampai1; $j++) {
            $item_num = $j;
            $urutan = $j - 1;
            $shipto = $datapp['TRANS_DTL'][$urutan]["SHIP_TO"];
            $kode_distrik = $datapp['TRANS_DTL'][$urutan]["KODE_TUJUAN"];
            $produk = $datapp['TRANS_DTL'][$urutan]["KODE_PRODUK"];
            $qty = $datapp['TRANS_DTL'][$urutan]["QTY_PP"];
            // $tgl_kirim = date('Ymd', strtotime($datapp['TRANS_DTL'][$urutan]['TGL_KIRIM_PP']));
            $kontrak = $datapp['TRANS_DTL'][$urutan]["NO_KONTRAK"];
            $posnr = $datapp['TRANS_DTL'][$urutan]["NO_KONTRAK_POSNR"];
                                    
            //////////////////////////////////////////// proyek SOCC v.2
            // $plant_socc = $datapp['PLANT_ASAL'];
            // $kode_distrik_socc = $datapp['TRANS_DTL'][$urutan]["KODE_TUJUAN"];
            // $material_for_socc = substr($datapp['TRANS_DTL'][$urutan]["KODE_PRODUK"],0,7);
            // $tgl_terima_leadtime = $this->fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
            // $tgl_terima_leadtime=$tgl_terima_leadtime;
            // list($day, $month, $year) = split("-", $tgl_terima_leadtime);
            // $tgl_terima_leadtime = $year . $month . $day;
            if ($datapp['TRANS_DTL'][$urutan]['TGL_LEADTIME']!="" || $datapp['TRANS_DTL'][$urutan]['TGL_LEADTIME']!=NULL) {
                $tgl_terima_leadtime = date('Ymd', strtotime($datapp['TRANS_DTL'][$urutan]['TGL_LEADTIME']));
                // if(date('H:i')>='15:00'){
                //     $tgl_terima_leadtime =  date('Ymd', strtotime($tgl_terima_leadtime. ' + 1 days'));
                // }else{
                    $tgl_terima_leadtime = $tgl_terima_leadtime;    
                // }
            } else $tgl_terima_leadtime = date("Ymd");

            if ($datapp['TRANS_DTL'][$urutan]['TANGGAL_PRELEADTIME']!="" || $datapp['TRANS_DTL'][$urutan]['TANGGAL_PRELEADTIME']!="") {
                $tgl_fdate_socc = date('Ymd', strtotime($datapp['TRANS_DTL'][$urutan]['TANGGAL_PRELEADTIME']));
            } else $tgl_fdate_socc = date("Ymd");

            /////////////////////////////////// penambahan kondisi tgl fdate dan rdd sama untuk FOT 
                if($incoterm1=='FOT'){
                    $tgl_fdate_socc = $tgl_terima_leadtime;
                }
            ///////////////////////////////////////////

            $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
            $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $produk;
                    // SOCC V2
                    $fce->ORDER_HEADER_IN["TP_DATE"] = 'D';
                    $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tgl_terima_leadtime;  
                    /////
            $fce->ORDER_ITEMS_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
            $fce->ORDER_ITEMS_INX->row["UPDATEFLAG"] = 'I'; //'000010';
            $fce->ORDER_ITEMS_INX->row["MATERIAL"] = 'X';
            if ($so_type == 'ZFC') {
                $fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'X'; //$item_cat;  
                if ($sales_org == '6000') {
                    $ITEM_CATEGvali = 'ZKNN';
                } else if ($sales_org == '4000' || $sales_org == '3000' || $sales_org == '7000' || $sales_org == '5000' ) {
                    $ITEM_CATEGvali = 'ZKLN';
                } else {
                    $ITEM_CATEGvali = 'ZKNN';
                }
                $fce->ORDER_ITEMS_IN->row["ITEM_CATEG"] = $ITEM_CATEGvali; //$item_cat;                   
            }
            $fce->ORDER_ITEMS_IN->row["PLANT"] = $plant;
            $fce->ORDER_ITEMS_IN->row["ROUTE"] = $route;

            $fce->ORDER_ITEMS_INX->row["PLANT"] = 'X';
            $fce->ORDER_ITEMS_INX->row["ROUTE"] = 'X';
            // $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $kode_distrik;
            if ($kontrak != '') {
                $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = $posnr;
                $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';

                $fce->ORDER_ITEMS_INX->row["REF_DOC"] = 'X';
                $fce->ORDER_ITEMS_INX->row["REF_DOC_IT"] = 'X';
                $fce->ORDER_ITEMS_INX->row["REF_DOC_CA"] = 'X';
            }

            $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);
            $fce->ORDER_ITEMS_INX->Append($fce->ORDER_ITEMS_INX->row);

            //detail entri schedule n qty'
            $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
            $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $qty;
            //////////////////////////////////////////////////////////// proyek SOCC V.2
            // $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_kirim;
            $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_fdate_socc;
            // $fce->ORDER_SCHEDULES_IN->row["TP_DATE"] = 'D';                                        
            // $fce->ORDER_SCHEDULES_IN->row["DLV_DATE"] = $tgl_terima_leadtime;  
            ///////////////////////////////////////////////////////////
            $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);
            $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
            $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
            $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
            $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
            $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);


            if ($j == 1)
                $item_num = '000000';
            else
                $item_num = $item_num;
            $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = $item_num * 10; //'000010';
            $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
            $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
            $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
        }

        $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
        $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'AG';
        $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $soldto;
        $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
            // SOCC V.2
            if($looping=='0' or $looping==0){
                $fce->TESTRUN = "X";
            }
            //
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $nomorso = $fce->SALESDOCUMENT;
            $fce->RETURN->Reset();
            while ($fce->RETURN->Next()) {
                $tipe = $fce->RETURN->row["TYPE"];
                $msg = $fce->RETURN->row["MESSAGE"];
                if ($tipe != 'S') {
                    $show_ket .= $msg;
                    $show_ket .= '<br>';
                // if($tipe=='W' && $id=='V1' && $number=='555'){

                    //SOCC v2
                    $show_ket .= 'SO long text (cek kembali mappingan/data PP)';
                    $show_ket .= '<br>';
                    $movetestrun='X';
                    $nomorso = '';
                    ///
                // }
                }
                //tambah pengujian rfc test run SOCC
            }
        }
        //SOCC v2
        }
        // $looping +=1;
        }
        ////////////////////////////////////////////////////////////// batas socc testrun
        if($nomorso!='' or $nomorso!=null){
            //Commit Transaction
            $fcecom = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
            $fcecom->Call();
            $fcecom->Close();

            //Update SO
            if ($nomorso != '' && $j > 1) {
                sleep(5); //seconds to wait..   
                $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                $fce->SALESDOCUMENT = $nomorso; //"ZOR";
                $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';
                $fce->Call();

                //Commit Transaction
                $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                $fce->Call();
            }

            if ($sales_org == '6000') {
                sleep(5); //seconds to wait..
                //Teks
                unset($tgl_kirimapp);
                for ($j = 1; $j <= $sampai1; $j++) {
                    $item_num1 = $j * 10;
                    $urutan = $j - 1;

                    if ($datapp['TRANS_DTL'][$urutan]['NO_POLISI']) {
                        $item_num = $this->fungsi->linenum($item_num1);
                        $com_nopolisiv = trim($datapp['TRANS_DTL'][$urutan]['NO_POLISI']);
                        $com_drivernamenopolv = trim($datapp['TRANS_DTL'][$urutan]['DRIVERN']);
                        $com_simdrivernamenopolv = trim($datapp['TRANS_DTL'][$urutan]['SIMDRIVER']);
                        $com_typetruckv = trim($datapp['TRANS_DTL'][$urutan]['TYPE_TRUCT']);
                        $com_catatanv = "";
                        $com_kodekantong = $datapp['TRANS_DTL'][$urutan]['KODE_BAG'];
                        $tgl_kirimapp = $tgl_kirim . "!";


                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                        $fce->I_VBELN = $nomorso; //'000010';
                        $fce->I_POSNR = sprintf("%06d", $item_num); //'000010';
                        $fce->I_TEXT1 = $com_nopolisiv;
                        $fce->I_TEXT2 = $com_typetruckv;
                        $fce->I_TEXT3 = $com_catatanv;
                        $fce->I_TEXT4 = $com_kodekantong;
                        $fce->I_TEXT6 = $com_drivernamenopolv;
                        $fce->I_TEXT7 = $com_simdrivernamenopolv;
                        $fce->Call();
                    }
                }

                if ($sales_org == '6000' && $nomorso != '') {
                    $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorso&tgleq=$tgl_kirimapp";
                }
            }

            //@liyantanto penambahan no ref pp
            if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                sleep(5); //s
                for ($j = 1; $j < $sampai1; $j++) {
                    $item_num1 = $j * 10;
                    $urutan = $j - 1;
                    $item_num = $this->fungsi->linenum($item_num1);
                    $com_nopppref = trim($datapp['TRANS_DTL'][$j]['NO_PPREF']);

                    if ($com_nopppref != '' && $nomorso != '') {
                        $pputam = substr($com_nopppref, 0, 10);
                        $itempputam = @(substr($com_nopppref, 10, 6) / 10);
                        $sql_ppref = "
	                            select ID,NO_SO,ITEM_NUMBER,NO_PPREF from OR_TRANS_DTL where DELETE_MARK=0 
	                            and NO_PP='$pputam' 
	                            and ITEM_NUMBER='$itempputam'
	                            ";
                        $query_ppref = oci_parse($this->conn, $sql_ppref);
                        oci_execute($query_ppref);
                        while ($row_ref = oci_fetch_array($query_ppref)) {
                            $idPPREF = $row_ref['ID'];
                            $no_soref = $row_ref['NO_SO'];
                            $item_soref = $row_ref['ITEM_NUMBER'];
                        }
                        $com_noppputam = $no_pp . sprintf("%06d", $item_num * 10);
                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                        $fce->I_VBELN = $no_soref; //'000010';
                        $fce->I_POSNR = sprintf("%06d", $item_soref * 10); //'000010';
                        $fce->I_TEXT5 = $nomorso . sprintf("%06d", $item_num * 10); //pp ref
                        $fce->Call();

                        //update so pp ref
                        $field_namesppru = array('NO_PPREF');
                        $field_datapput = array("$com_noppputam");
                        $tablenamepput = "OR_TRANS_DTL";
                        $field_idpput = array('ID');
                        $value_idpput = array("$idPPREF");
                        $this->fungsi->update($this->conn, $field_namesppru, $field_datapput, $tablenamepput, $field_idpput, $value_idpput);

                        $nosoref = $no_soref . sprintf("%06d", $item_soref * 10);
                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                        $fce->I_VBELN = $nomorso; //'000010';
                        $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                        $fce->I_TEXT5 = $nosoref; //pp ref
                        $fce->Call();
                    }
                }
            }

                $queryItem="SELECT  otd.KODE_TUJUAN, otd.PLANT, otd.ID,otd.NO_SO,otd.ITEM_NUMBER,otd.NO_PPREF,CASE WHEN min(mbp.IS_ROYALTY)='Y' THEN 'ROYALTY' ELSE '' END AS ROYALTY
                FROM OR_TRANS_HDR oth 
                LEFT JOIN OR_TRANS_DTL otd ON otd.NO_PP=oth.NO_PP
                LEFT JOIN ZSD_TARGET_HEADER_SCM zths ON zths.DISTRIK=otd.KODE_TUJUAN AND to_char(otd.TGL_KIRIM_PP,'MM-YYYY')=zths.PERIODE AND zths.MATERIAL=otd.KODE_PRODUK AND zths.FLAG_DEL='X'
                LEFT JOIN MAPPING_BRAND_PLANT mbp ON mbp.BRAND=zths.BRAND AND mbp.PLANT_MD='".$datapp['PLANT_ASAL']."' AND mbp.FLAG_DEL='X'
                WHERE oth.id='".$datapp['ID']."'
                GROUP BY otd.ID,otd.NO_SO,otd.ITEM_NUMBER,otd.NO_PPREF,otd.KODE_TUJUAN,otd.PLANT";

                $query_ppref = oci_parse($this->conn, $queryItem);
                oci_execute($query_ppref);
                while ($row_ref = oci_fetch_array($query_ppref)) {
                    $idPPREF = $row_ref[ID];
                    $no_soref = $row_ref[NO_SO];
                    $item_soref = $row_ref[ITEM_NUMBER];
                    $flagroyalty = $row_ref[ROYALTY]; 
                    $kodetujuanmsa= $row_ref[KODE_TUJUAN];
                    $plantmsa = $row_ref[PLANT];
                }

                $mysqlmsa="SELECT 
                                COUNT(*) AS COUNT_DATA
                                FROM(
                                SELECT
                                    kp.KOORDINATOR_AREA,
                                    mp.COM_OPCO,
                                    CASE
                                        WHEN kp.KOORDINATOR_AREA = mp.COM_OPCO THEN 'FALSE'
                                        ELSE NULL
                                    END AS MSA_STATUS
                                FROM
                                    ZMD_KOORDINATOR_PENJUALAN kp
                                JOIN ZMD_MAPPING_PLANT mp ON
                                    kp.KOORDINATOR_AREA = mp.COM_OPCO
                                WHERE
                                    kp.DEL = '0'
                                    AND mp.DEL = '0'
                                    AND mp.PLANT_MD = '$plantmsa'
                                    AND mp.COM_OPCO IN (
                                    SELECT
                                        KOORDINATOR_AREA
                                    FROM
                                        ZMD_KOORDINATOR_PENJUALAN
                                    WHERE
                                        DISTRICT = '$kodetujuanmsa'
                                        AND DEL = '0' )) a";
                                $mysql_msa=oci_parse($conn,$mysqlmsa);
                                oci_execute($mysql_msa);
                                $row_ref = oci_fetch_assoc($mysql_msa);
                                $msa=$row_ref[COUNT_DATA];
                                $isMsa = $msa == 0 ? 'MSA' : '';                                                                    

                if(($flagroyalty == 'ROYALTY') && ($isMsa=='MSA')){
                    $com_royalty = 'ROYALTY_MSA';
                }else if (($flagroyalty=='ROYALTY') && ($isMsa!='MSA')){
                    $com_royalty ='ROYALTY';
                }else if (($flagroyalty!='ROYALTY') && ($isMsa=='MSA')){
                    $com_royalty ='MSA';
                }else{
                    $com_royalty='';
                }
                
                $nosoref = $no_soref . sprintf("%06d", 1 * 10);
                $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                $fce->I_VBELN = $nomorso; //'000010';
                $fce->I_POSNR = sprintf("%06d", 1 * 10); //'000010';
                $fce->I_TEXT10 = $com_royalty;
                $fce->Call();

                $nosoref = $no_soref . sprintf("%06d", 1 * 10);
                $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                $fce->I_VBELN = $no_soref; //'000010';
                $fce->I_POSNR = sprintf("%06d", 1 * 10); //'000010';
                $fce->I_TEXT10 = $com_royalty;
                $fce->Call();
        }
        $fce->Close();
        $sap->Close();


        if ($nomorso != "") {
            $status = "APPROVE";
            $show_ket .= "Sales Order has been made with a number : " . $nomorso . "\n";
        } else {
            $status = "PROCESS";
            $show_ket .= "Order Reservation Failed ! \n";
        }
        $idh = $datapp['ID'];

        //update data header 
        $field_names = array('PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'STATUS', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ROUTE', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'KD_REASON', 'NM_REASON', 'ORG');
        $field_data = array("$plant", "$nama_plant", "$top", "$status", "$nama_top", "$so_type", "$nama_so_type", "$incoterm1", "$incoterm2", "SYSDATE", "$user_name", "$route", "$pricelist", "$nama_pricelist", "$lcnum", "$reason", "$nama_reason", "$sales_org");
        $tablename = "OR_TRANS_HDR";
        $field_id = array('ID');
        $value_id = array("$idh");
        $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        // update data detail
        for ($k = 0; $k < $sampai1; $k++) {
            $id_dtl1 = $datapp['TRANS_DTL'][$k]['ID'];
            $qty1 = $datapp['TRANS_DTL'][$k]['QTY_PP'];
            $tgl_kirim1 = date('d-m-Y', strtotime($datapp['TRANS_DTL'][$k]['TGL_KIRIM_PP']));
            $kode_distrik = $datapp['TRANS_DTL'][$k]["KODE_TUJUAN"];
            $tgl_terima = $this->fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
            $kontrak = $datapp['TRANS_DTL'][$k]["NO_KONTRAK"];
            $posnr = $datapp['TRANS_DTL'][$k]["NO_KONTRAK_POSNR"];

            /////////////////////////////////////////// proyek SOCC V.2
            $tgl_kirim1 = date('d-m-Y', strtotime($datapp['TRANS_DTL'][$k]['TGL_KIRIM_PP']));
            $tgl_terima = $this->fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
            //old
            //-----------------------------------------------------------------------//
            //new
            // $plant_socc = $datapp['PLANT_ASAL'];
            // $kode_distrik_socc = $datapp['TRANS_DTL'][$k]["KODE_TUJUAN"];
            // $material_for_socc = substr($datapp['TRANS_DTL'][$k]["KODE_PRODUK"],0,7);
            // $tgl_terima_leadtime = $this->fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
            ///////////////////////////////////////////

            if ($nomorso != "") {
                $status = "APPROVE";
                $show_ket .= "Sales Order has been made with a number : " . $nomorso . "\n";
                $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT');
                $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "$kontrak", "$plant", "$nama_plant");
            } else {
                $status = "PROCESS";
                $show_ket .= "Order Reservation Failed ! \n";
                $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT');
                $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "$kontrak", "$plant", "$nama_plant");
            }
                    //pengujian jika approve tidak update ke process
                    $sql_sql_getdataapprove = "select STATUS_LINE from OR_TRANS_DTL 
                    where DELETE_MARK=0 
                    and ID='$id_dtl1'";
            
                    $mysql_getdataapprove=oci_parse($this->conn,$sql_sql_getdataapprove);
                    oci_execute($mysql_getdataapprove);
                    $row_datagetapprove=oci_fetch_assoc($mysql_getdataapprove);
                    $statusline_pp=$row_datagetapprove[STATUS_LINE]; 
                    //////
                    if($statusline_pp!='APPROVE'){
            $tablename = "OR_TRANS_DTL";
            $field_id = array('ID');
            $value_id = array("$id_dtl1");
            $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }
            if ($nomorso != "") {
                $field_names1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'APPROVE_DATE', 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                $field_data1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', "$qty1", "instgl_$tgl_kirim1", 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', "'APPROVE'", 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', "SYSDATE", 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                $tablenamefrom = "OR_TRANS_DTL";
                $tablenameto = "OR_TRANS_APP";
                $field_id1 = array('ID');
                $value_id1 = array("$id_dtl1");
                $this->fungsi->insertinto($this->conn, $field_names1, $field_data1, $tablenameto, $tablenamefrom, $field_id1, $value_id1);

                $sqlupdatekontrak = "UPDATE RFC_Z_ZCSD_KONTRAK_SI SET RFMNG = RFMNG+" . $qty1 . " where VBELN = '" . $kontrak . "' AND POSNR = '" . $posnr . "'";
                $qupdatekontrak = oci_parse($this->conn, $sqlupdatekontrak);
                oci_execute($qupdatekontrak);
            }

            if ($nomorso != "") {
                $no_pp = $datapp['NO_PP'];
                $sqlOrTrans =  "SELECT
                            hdr.ID AS ID_HDR,
                            dtl.ID AS ID_DTL,
                            hdr.*,
                            dtl.*,
                            z.NAME_KEY,
                            r.PALLET,
                            r.KVGR1,
                            TO_CHAR(dtl.TGL_KIRIM_PP, 'YYYY-MM-DD HH24:MI:SS') AS TGL_KIRIM_PP,
                            TO_CHAR(dtl.TGL_LEADTIME, 'YYYY-MM-DD HH24:MI:SS') AS TGL_LEADTIME,
                            TO_CHAR(dtl.APPROVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS APPROVE_DATE
                        FROM
                            OR_TRANS_HDR hdr
                        JOIN
                            OR_TRANS_DTL dtl ON
                            hdr.NO_PP = dtl.NO_PP
                        LEFT JOIN
                            ZREPORT_M_PRICE z ON hdr.TERM_PAYMENT = z.KEY
                        LEFT JOIN
                            RFC_Z_ZCSD_SHIPTO r ON dtl.SHIP_TO = r.KUNN2
                        WHERE hdr.NO_PP = '$no_pp'
                        ";
                $sqlOrTrans = oci_parse($this->conn, $sqlOrTrans);
                oci_execute($sqlOrTrans);
                $datapp=oci_fetch_assoc($sqlOrTrans);
                // get ORG Name
                $arrayOrg = $this->fungsi->arrayorg();
                $orgCode = $datapp['ORG'];
                $orgName = isset($arrayOrg[$orgCode]) ? $arrayOrg[$orgCode] : '';
                // convert format tanggal
                $tglKirim = $datapp['TGL_KIRIM_PP'];
                $tglPP = $datapp['TGL_PP'];
                $tglLeadtime = $datapp['TGL_LEADTIME'];
                $tglApprove = $datapp['APPROVE_DATE'];
                $tglKirim = $this->fungsi->tanggalSvOrder($tglKirim);
                $tglPP = $this->fungsi->tanggalSvOrder($tglPP);
                $tglLeadtime = $this->fungsi->tanggalSvOrder($tglLeadtime);
                $tglApprove = $this->fungsi->tanggalSvOrder($tglApprove);
                
                 if ($datapp['SO_TYPE'] == 'ZFC') {
                    $deliveryBlock = "Z1";
                }

                $data = array(
                    "DeliveryBlock" => $deliveryBlock,
                    "OrderReason" => "",
                    "ReasonForRejection" => "",
                    "alamatShipto" => trim($datapp['ALAMAT_SHIP_TO']),
                    "com" => $datapp['ORG'],
                    "com_name" => $orgName,
                    "descTop" => $datapp['NAME_KEY'],
                    "harga" => $datapp['HARGA'],
                    "incoterm" => $datapp['INCOTERM'],
                    "kodeDistributor" => $datapp['SOLD_TO'],
                    "kodeDistrik" => $datapp['KODE_TUJUAN'],
                    "kodeProduk" => $datapp['KODE_PRODUK'],
                    "kodeShipto" => $datapp['SHIP_TO'],
                    "lineSO" => "10",
                    "namaDistributor" => trim($datapp['NAMA_SOLD_TO']),
                    "namaDistrik" => $datapp['NAMA_TUJUAN'],
                    "namaKapal" => $datapp['NAMA_KAPAL'],
                    "namaProduk" => trim($datapp['NAMA_PRODUK']),
                    "namaShipto" => trim($datapp['NAMA_SHIP_TO']),
                    "noKontrak" => $datapp['NO_KONTRAK'],
                    "noPP" => $datapp['NO_PP'],
                    "pallet" => $datapp['PALLET'],
                    "plant" => $datapp['PLANT'],
                    "price" => $datapp['PRICELIST'],
                    "qtyRelease" => "0",
                    "qtySisa" => $datapp['QTY_PP'],
                    "qtySo" => $datapp['QTY_PP'],
                    "salesOrder" => $datapp['NO_SO'],
                    "tanggalKirim" => $tglKirim,
                    "tanggalRDD" => $tglLeadtime,
                    "tanggalSO" => $tglApprove,
                    "tipeSO" => $datapp['SO_TYPE'],
                    "tipeTruk" => $datapp['KVGR1'],
                    "top" => $datapp['NAMA_TOP'],
                    "uom" => $datapp['UOM']
                );
                return $data;
            } else {
                return false;
            }

        }
        
        echo $show_ket;
        $this->msg .= $show_ket;
        // fwrite($this->logfile, $show_ket);
    }

}

?>
