<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
include ('../include/email.php');
require_once ('../security_helper.php');
sanitize_global_input();
$email = new Email();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=4886;
$dirr = $_SERVER['PHP_SELF'];
// $halaman_id = $fungsi->getmainhalam_id($conn, $dirr);
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// } 
// print_r($orgIn);



if(isset($_POST["rejectInv"]) && $_POST["rejectInv"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Apakah ada yakin ingin Reject invoice <? echo $_POST["nomer_invoice"] ; ?> ?
				<input type="hidden" value="<? echo $_POST["nomer_invoice"] ; ?>" name="nor_invoice">
			</div>
			<div class="alert alert-danger" role="alert">
				<label>Alasan Reject Invoice</label>
				<textarea class="form-control" name="reject_inv_ket" rows="5" required></textarea>
			</div>
			<button type="submit" name="rejectInv" value="rejectInv" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Iya&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


if((isset($_POST["rejectInv"]) && $_POST["rejectInv"] == 'rejectInv')) {
	$nomer_invoice = $_POST["nor_invoice"];
	$keterangan_cancel = $_POST['reject_inv_ket'];
	$sql_inv = "SELECT ID, WARNA_PLAT, NO_BA FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$nomer_invoice' ";
 
	$query_inv = oci_parse($conn, $sql_inv);
	oci_execute($query_inv);
	$row_inv = oci_fetch_assoc($query_inv);
	$id_trans = $row_inv[ID];
	$warna_plat = $row_inv[WARNA_PLAT];
	$no_ba = $row_inv[NO_BA];
	 // echo $no_ba ;exit();
	$status_ba = 40;
	
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	$action = "reject_invoice_ba_checklist";
	include ('formula_prod.php'); 
	//sendEmail
	$sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$data_invoice_ba['CREATED_BY'];
	$query = oci_parse($conn, $sql);
	oci_execute($query);
	$row = oci_fetch_assoc($query);
	$mailTo = $row[ALAMAT_EMAIL];
	$mailCc = '';
	$email_content_table = "";
    
    $email->sendMail($mailTo, $mailCc, 'Notifikasi Reject Dokumen Invoice', $no_invoice_in, 'Mohon untuk ditindaklanjuti Dokumen tsb.', $email_content_table);
	//end sendEmail
}
 
/*
$action_page=$fungsi->security($conn,$user_id,$halaman_id);
*/$page="invoice_dokumen_trans.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
if (isset($_POST['vendor']))
$vendor = $_POST['vendor'];

$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$no_invoice = $_POST['no_invoice'];
$no_invoice_expeditur = $_POST['no_invoice_expeditur'];
$no_ba = $_POST['no_ba'];

$currentPage="invoice_dokumen_trans.php";
$komen="";
if(isset($_POST['cari'])){
	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_invoice == "" and $no_invoice_expeditur == "" and $no_ba == ""){
		$sql= "SELECT DISTINCT EX_INVOICE.NO_INVOICE_SAP, EX_INVOICE.NO_INVOICE, EX_INVOICE.NO_INVOICE_EX, EX_INVOICE.NO_VENDOR,EX_INVOICE.NAMA_VENDOR,EX_INVOICE.NO_PAJAK_EX, TO_CHAR(EX_INVOICE.TGL_INVOICE, 'DD-MM-YYYY') AS TGL_INVOICE1, EBA.NO_BA ,EBA.DIPAKAI , EBA.LAMPIRAN , EBA.STATUS_BA_INVOICE, C.WARNA_PLAT FROM EX_INVOICE LEFT JOIN EX_BA_INVOICE EBA ON EBA.NO_INVOICE = EX_INVOICE.NO_INVOICE AND EBA.DIPAKAI = 1 LEFT JOIN EX_TRANS_HDR C ON EX_INVOICE.NO_INVOICE = C.NO_INVOICE
		WHERE EX_INVOICE.DELETE_MARK ='0' AND EX_INVOICE.NO_INVOICE IS NOT NULL AND STATUS_DOKUMEN = 0 AND EBA.STATUS_BA_INVOICE = 20 AND EX_INVOICE.ORG in ($orgIn) ORDER BY EX_INVOICE.NO_VENDOR, EX_INVOICE.NO_INVOICE DESC";
	}else {
		$pakeor=0;
		$sql = "SELECT DISTINCT EX_INVOICE.NO_INVOICE_SAP, EX_INVOICE.NO_INVOICE, EX_INVOICE.NO_INVOICE_EX, EX_INVOICE.NO_VENDOR,EX_INVOICE.NAMA_VENDOR,EX_INVOICE.NO_PAJAK_EX, TO_CHAR(EX_INVOICE.TGL_INVOICE, 'DD-MM-YYYY') AS TGL_INVOICE1, EBA.NO_BA ,EBA.DIPAKAI , EBA.LAMPIRAN , EBA.STATUS_BA_INVOICE, C.WARNA_PLAT FROM EX_INVOICE LEFT JOIN EX_BA_INVOICE EBA ON EBA.NO_INVOICE = EX_INVOICE.NO_INVOICE AND EBA.DIPAKAI = 1 LEFT JOIN EX_TRANS_HDR C ON EX_INVOICE.NO_INVOICE = C.NO_INVOICE WHERE STATUS_DOKUMEN = 0 AND EBA.STATUS_BA_INVOICE = 20 AND ";
		if($vendor!=""){
		$sql.=" ( EX_INVOICE.NAMA_VENDOR LIKE '$vendor' OR EX_INVOICE.NO_VENDOR LIKE '$vendor' ) ";
		$pakeor=1;
		}
		if($tanggal_mulai!="" or tanggal_selesai!=""){

			if ($tanggal_mulai=="")
			$tanggal_mulai_sql = "01-01-1990";
			else
			$tanggal_mulai_sql = $tanggal_mulai;

			if ($tanggal_selesai=="")
			$tanggal_selesai_sql = "12-12-9999";
			else
			$tanggal_selesai_sql = $tanggal_selesai ;

			if($pakeor==1){
			$sql.=" AND EX_INVOICE.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + 1";
			}else{
			$sql.=" EX_INVOICE.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + 1";
			$pakeor=1;
			}
		}
		if($no_invoice_expeditur!=""){
			if($pakeor==1){
			$sql.=" AND EX_INVOICE.NO_INVOICE_EX LIKE '$no_invoice_expeditur' ";
			}else{
			$sql.=" EX_INVOICE.NO_INVOICE_EX LIKE '$no_invoice_expeditur' ";
			$pakeor=1;
			}
		}
		if($no_invoice!=""){
			if($pakeor==1){
			$sql.=" AND EX_INVOICE.NO_INVOICE LIKE '$no_invoice' ";
			}else{
			$sql.=" EX_INVOICE.NO_INVOICE LIKE '$no_invoice' ";
			$pakeor=1;
			}
		}
		if($no_ba!=""){ 
			$sql.=" AND EBA.NO_BA LIKE '$no_ba' ";  
		}
		$sql.=" AND EX_INVOICE.DELETE_MARK ='0' AND EX_INVOICE.NO_INVOICE IS NOT NULL AND EX_INVOICE.ORG in ($orgIn)  ORDER BY EX_INVOICE.NO_VENDOR, EX_INVOICE.NO_INVOICE DESC";
	}
	echo 'query '.$sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
		$no_invoice_v[]=$row[NO_INVOICE];
		$no_ba_v[]=$row[NO_BA];
		$no_invoice_in=$row[NO_INVOICE];
		$no_invoice_ex_v[]=$row[NO_INVOICE_EX];
		$vendor_v[]=$row[NO_VENDOR];
		$nama_vendor_v[]=$row[NAMA_VENDOR];
		$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
		$tgl_invoice_v[]=$row[TGL_INVOICE1];
		
		// $sqlS = "select WARNA_PLAT from EX_TRANS_HDR where NO_BA = $row[NO_BA] ";   
		// $query_s= @oci_parse($conn, $sqlS);
		// @oci_execute($query_s);
		// $row_s=@oci_fetch_array($query_s); 
		$warna_plat_v[]=$row[WARNA_PLAT]; 

		$sqlcek="SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE  NO_INVOICE = '$no_invoice_in' AND DELETE_MARK = '0' ";
		$querycek= oci_parse($conn, $sqlcek);
		oci_execute($querycek);
		$row_data=oci_fetch_assoc($querycek);
		$total_klaim_in=$row_data[TOTAL_KLAIM];
		$total_shp_in = $row_data[SHP_COST];
		$total_ktg_in=$row_data[TOTAL_KTG];
		$total_semen_in=$row_data[TOTAL_SEMEN];
		$total_pdpks_in=$row_data[TOTAL_PDPKS];
		$total_pdpkk_in=$row_data[TOTAL_PDPKK];

		if ($row[PAJAK_INV] > 0) 
		$pajak = 0.1*$row_data[TOTAL_KLAIM];
		else 
		$pajak = 0;


		$klaim_semen_v[]=$row_data[TOTAL_SEMEN];
		$klaim_ktg_v[]=$row_data[TOTAL_KTG];
		$pdpks_v[]=$row_data[TOTAL_PDPKS]; 
		$pend_ktg_v[]=$row_data[TOTAL_PDPKK]; 
		$total_klaim_v[]=$row_data[TOTAL_KLAIM];
		$pajak_v[]=$pajak;

	}
	$total=count($no_invoice_v);
	if ($total < 1){
		$komen = "Tidak Ada Data Yang Ditemukan";
	}

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Invoice Claim </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Invoice Claim </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No BA</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_expeditur?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Vendor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="vendor" name="vendor"  value="<?=$vendor?>" <? echo $hanya_baca; ?>/></td>
    </tr>
    <tr>
      <td  class="puso">Periode Invoice </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>"  />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>"  />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){

?>
<!--<form id="data_claim" name="data_claim" method="post" action="komentar.php" > -->


	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Cost Claim </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No Invoice </strong></td>
		 <td align="center"><strong>No Invoice EX </strong></td>
		 <td align="center"><strong>No BA </strong></td>
		 <td align="center"><strong>Vendor </strong></td>
		 <td align="center"><strong>No Pajak EX </strong></td>
		 <td align="center"><strong>Tgl Invoice </strong></td>
		 <td align="center"><strong>Warna Plat </strong></td>
		 <td align="center"><strong>Klaim Semen </strong></td>
		 <td align="center"><strong>Klaim Kantong</strong></td>
		 <td align="center"><strong>PDPKS</strong></td>
		 <td align="center"><strong>Pend. Ktg</strong></td>
		 <td align="center"><strong>Sub Total</strong></td>
		 <td align="center"><strong>Pajak (PPN)</strong></td>
		 <td align="center"><strong>Total</strong></td>
		 <td align="center"><strong>Action</strong></td>
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_invoice_v[$i]; ?></a></td>	

		<td align="center"><? echo $no_invoice_ex_v[$i]; ?></td>
		<td align="center"><? echo $no_ba_v[$i]; ?></td>
		<td align="center"><? echo $vendor_v[$i]." / ".$nama_vendor_v[$i]; ?></td>
		<td align="center"><? echo $no_pajak_ex_v[$i]; ?></td>
		<td align="center"><? echo $tgl_invoice_v[$i]; ?></td>
		<td align="center"><? echo $warna_plat_v[$i]; ?></td>
		<td align="center"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($klaim_ktg_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pdpks_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pend_ktg_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_klaim_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pajak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."); ?></td>
		<td align="center"  style='white-space: nowrap'><a href="javascript:popUp('invoice_dokumen_trans_proses.php?no_invoice=<?=$no_invoice_v[$i]?>')" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Cheklist Dokumen</a>
					 <form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $no_invoice_v[$i]; ?>" name="nomer_invoice"><br><button type="submit" name="rejectInv" style="font-size: 12px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 4px; background-color: #ff7800; color: #fff; border: 1px solid #000; border-radius: 4px;">Reject</button> </form>
		</td>
		</tr>
	  <? } ?>
		</tbody>

	  <tr class="quote">
		<td colspan="16" align="center">
		<a href="invoice_dokumen_trans.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		<!-- </form> -->

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
