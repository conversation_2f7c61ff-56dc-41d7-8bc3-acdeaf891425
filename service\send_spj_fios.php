<?php
error_reporting(E_ALL ^ E_NOTICE);
$request_method = $_SERVER["REQUEST_METHOD"];

unset($dataHead);
require_once ("autorisasi.php");
$fautoris= new autorisasi();
global $fautoris;

switch ($request_method) {
    case 'GET':
            $param['X_TGL1'] = isset($_GET['X_TGL1'])?$_GET['X_TGL1']:date("Y-m-d"); 
            $param['X_TGL2'] = isset($_GET['X_TGL2'])?$_GET['X_TGL2']:date("Y-m-d");
            $param['X_WERKS'] = $_GET['X_WERKS'];
            $param['PLANT_CARCONS'] = array("7401","7403","7408","7406","7635","7609","7652","7911","79E3","7973","79C1","7960","7611","79D4","7806","7641","7633","79B1","7986","7954","79E8","79B3","79B2","79G2","7938","7981","7985","7986","79D3","79I4","79C2","79I3","7980","79L9","79I2","7602","7996","79K1","7983","79B4","79A2");
            // $param['X_WERKS_TO'] = $_GET['X_WERKS_TO'];
            $conn=$fautoris->koneksi();
            $stid = oci_parse($conn, "SELECT KD_SOLDTO FROM MAPPING_SOLDTO_FIOS WHERE DEL = 0");
            oci_execute($stid);
            $data = array();
            oci_fetch_all($stid, $data, 0, -1, OCI_FETCHSTATEMENT_BY_ROW);

            $kd_soldto = array();
            foreach ($data as $row) {
                $kd_soldto[] = $row['KD_SOLDTO'];
            }
            // echo "<pre>";
            // print_r("TOP");
            // print_r($kd_soldto);
            // echo "</pre>";
            if (empty($kd_soldto)) {
                echo json_encode("Belum Mapping Soldto!!");
                exit;
            }

            $param['X_KUNNR'] = isset($_GET['X_KUNNR']) ? $_GET['X_KUNNR'] : $kd_soldto;
            // $param['X_KUNNR'] = isset($_GET['X_KUNNR']) ? $_GET['X_KUNNR'] : array("0000000138");
            // $param['X_KUNNR'] = isset($_GET['X_KUNNR']) ? $_GET['X_KUNNR'] : array(
            //                         "0000000106", "0000000107", "0000000108", "0000000120", "0000000122", "0000000125", "0000000127", 
            //                         "0000000130", "0000000133", "0000000138", "0000000140", "0000000142", "0000000144", "0000000145", 
            //                         "0000000147", "0000000149", "0000000150", "0000000158", "0000000159", "0000000195", "0000000214", 
            //                         "0000000215", "0000000217", "0000000220", "0000000222", "0000000223", "0000000238", "0000000239", 
            //                         "0000000240", "0000000244", "0000000247", "0000000250", "0000000252", "0000000253", "0000000254", 
            //                         "0000000260", "0000000262", "0000000264", "0000000265", "0000000273", "0000000274", "0000000276", 
            //                         "0000000277", "0000000278", "0000000279", "0000000281", "0000000282", "0000000283", "0000000284", 
            //                         "0000000285", "0000000286", "0000000287", "0000000289", "0000000294", "0000000298", "0000000302", 
            //                         "0000000318", "0000000702", "0000000704", "0000000711", "0000000716", "0000000722", "0000000723", 
            //                         "0000000727", "0000000728", "0000000734", "0000000736", "0000000739", "0000000756", "0000000761", 
            //                         "0000000773", "0000000810", "0000000812", "0000000819", "0000000885", "0000000886", "0000000889", 
            //                         "0000000898", "0000000901", "0000000902", "0000000903", "0000000904", "0000000915", "0000000924", 
            //                         "0000000977", "0000000980", "0000000982", "0000000988", "0000000995", "0000000998", "0000030010", 
            //                         "0000030031", "0000030058", "0000030156", "0000030157", "0000030160", "0000030165", "0000030166", 
            //                         "0000035033", "0000035036", "0000035037", "0000037001", "0000037002", "0000037003", "0000037004", 
            //                         "0000037005", "0000037009", "0000037018", "0000037020", "0000037023", "0000037024", "0000037027", 
            //                         "0000037034", "0000037038", "0000037040", "0000037041", "0000037042", "0000037043", "0000037044", 
            //                         "0000037045", "0000037048", "0000037049", "0000037050", "0000037051", "0000037052", "0000037056", 
            //                         "0000037057", "0000037058", "0000037059", "0000037060", "0000037063", "0000037064", "0000037065", 
            //                         "0000037069", "0000037071", "0000037072", "0000037086", "0000037094", "0000037098", "0000037099", 
            //                         "0000037106", "0000037108", "0000037135", "0000037138", "0000037155", "0000037157", "0000037165", 
            //                         "0000037166", "0000037174", "0000037190", "0000037192", "0000037193", "0000037195", "0000037196", 
            //                         "0000037206", "0000037251", "0000037253", "0000037254", "0000037256", "0000037270", "0000038002", 
            //                         "0000038005", "0000038008", "0000038013", "0000038017", "0000038018", "0000038020", "0000038022", 
            //                         "0000038024", "0000038028", "0000038032", "0000038035", "0000038039", "0000038052", "0000038070", 
            //                         "0000038071", "0000038075", "0000038086", "0000040005", "0000040006", "0000040007", "0000040028", 
            //                         "0000040034", "0000040043", "0000040044", "0000040047", "0000040064", "0000040065", "0000040083", 
            //                         "0000040090", "0000040092", "0000040123", "0000040170", "0000040185", "0000040244", "0000040259", 
            //                         "0000040260", "0000040261", "0000040262", "0000040264", "0000040265", "0000040268", "0000040269", 
            //                         "0000040270", "0000040294", "0000040304", "0000040308", "0000040311", "0000040339", "0000040368", 
            //                         "0000040427", "0000040440", "0000040477", "0000040513", "0000040525", "0000040527", "0000040701", 
            //                         "0000040710", "0000040714", "0000040725", "0000040769", "0000040772", "0000040779", "0000040785", 
            //                         "0000040788", "0000040819", "0000040825", "0000040850", "0000040862", "0000040899", "0000040909", 
            //                         "0000040918", "0000040928", "0000070000", "0000070001", "0000070004", "0000070015", "0000070017", 
            //                         "0000070020", "0000070024", "0000070025", "0000070030", "0000070032", "0000070039", "0000070040", 
            //                         "0000070044", "0000070045", "0000070046", "0000070048", "0000070049", "0000070050", "0000070051", 
            //                         "0000070053", "0000070057", "0000070060", "0000800001", "0000800002", "0000800003", "0000800004", 
            //                         "0000800005", "0000037287", "0000020002", "0000020003" 

            // ); //$_GET['X_KUNNR'];
            // echo "<pre>";
            // print_r($param['X_KUNNR']);
            // echo "</pre>";
            // $param['X_VKBUR'] = $_GET['X_VKBUR'];
            // $param['X_VKBUR_TO'] = $_GET['X_VKBUR_TO'];
            $param['X_BZIRK'] = $_GET['X_BZIRK'];
            // $param['X_BZIRK_TO'] = $_GET['X_BZIRK_TO'];
            // $param['X_LIFNR'] = $_GET['X_LIFNR'];
            // $param['X_LIFNR_TO'] = $_GET['X_LIFNR_TO'];
            $param['X_STATUS'] = "70";//$_GET['X_STATUS'];
            // $param['X_STATUS_TO'] = $_GET['X_STATUS_TO'];
            $param['X_VKORG'] = $_GET['X_VKORG'];//"7900";
            // $param['X_LSTEL'] = $_GET['X_LSTEL'];
            $param['X_VBELN'] = $_GET['X_VBELN'];
            // $param['X_VBELN_TO'] = $_GET['X_VBELN_TO'];
            // $param['X_EBELN'] = $_GET['X_EBELN'];
            // $param['X_EBELN_TO'] = $_GET['X_EBELN_TO'];
            $param['X_NOPOLISI'] = $_GET['X_NOPOLISI'];
            // $param['X_NOPOLISI_TO'] = $_GET['X_NOPOLISI_TO'];
            $param['X_NOSPJ'] = $_GET['X_NOSPJ'];
            // $param['X_NOSPJ_TO'] = $_GET['X_NOSPJ_TO'];
            // $param['X_ITEM_NO'] = $_GET['X_ITEM_NO'];
            // $param['X_LFART'] = $_GET['X_LFART'];
            // $param['X_CURAHBAG'] = $_GET['X_CURAHBAG'];
            // $param['X_SO'] = $_GET['X_SO'];
            // $param['X_PO'] = $_GET['X_PO'];
            // $param['X_SHIFT'] = $_GET['X_SHIFT'];
            // $param['X_TIMEFLAG'] = $_GET['X_TIMEFLAG'];
            $param['X_INCO'] = $_GET['X_INCO'];
            $param['X_ROUTE'] = $_GET['X_ROUTE'];
            // $param['X_BRAN'] = $_GET['X_BRAN'];
            // $param['X_KONFIRMASI'] = $_GET['X_KONFIRMASI'];
            // $param['X_WO_KONFIRMASI'] = $_GET['X_WO_KONFIRMASI'];
            // $param['X_KUNNR2'] = $_GET['X_KUNNR2'];
            // $param['X_AUART'] = $_GET['X_AUART'];
            // $param['X_FLAGTOKO'] = $_GET['X_FLAGTOKO'];
            // $param['X_NOFLAG'] = $_GET['X_NOFLAG'];
            // $param['X_SHIP_TO_CODE'] = $_GET['X_SHIP_TO_CODE'];
            // $param['X_FROM_WHSE'] = $_GET['X_FROM_WHSE'];
            // $param['X_TO_WHSE'] = $_GET['X_TO_WHSE'];
            // $param['I_TGL_CMPLT_INC'] = $_GET['I_TGL_CMPLT_INC'];
            // $param['I_JAM_CMPLT_INC'] = $_GET['I_JAM_CMPLT_INC'];
            // $param['X_TGL_MATCH2_LOW'] = $_GET['X_TGL_MATCH2_LOW'];
            // $param['X_TGL_MATCH2_HIGH'] = $_GET['X_TGL_MATCH2_HIGH'];
            // $param['X_LINE_SO'] = $_GET['X_LINE_SO'];
            // $param['X_NO_KTG_ACT'] = $_GET['X_NO_KTG_ACT'];
            // $param['X_NO_KTG'] = $_GET['X_NO_KTG'];
            // $param['X_GR_PO'] = $_GET['X_GR_PO'];
            // $param['X_PALELT'] = $_GET['X_PALELT'];
            // $param['X_VKANTONG'] = $_GET['X_VKANTONG'];
            // $param['X_WITH_LOOP'] = $_GET['X_WITH_LOOP'];
            // $param['X_JUMBO'] = $_GET['X_JUMBO'];

            $get = new get_spj();
            $login = $get->login();
            // var_dump($login);exit;
            if (!$login['error']) {
                // var_dump($login['error']);exit;
                $data = $get->get_data($param);
                // var_dump($data);exit;
                if (empty($data)) {
                    echo json_encode("No Data Found");
                } else {
                    $result = $get->send_data($data,$login['data']);
                    echo json_encode($result);
                }
            } else echo json_encode("Login Silog Gagal");
        break;
}

class get_spj
{

    // private $_basePath;
    // private $_sapCon;
    private $_data;

    public function __construct()
    {
        // require_once("../../../sgg/include/sapclasses/sap.php");
        // require_once("../../../sgg/include/connect/SAPDataModule_Connection.php");
        require_once("../include/sapclasses/sap.php");
        $this->_sapCon = "../include/sapclasses/logon_data.conf";
    }

    function getConnSAP(){
        //if($this->_konsap) $this->_konsap->Close();

        $this->_konsap = new SAPConnection();
        /* NB: Ini File Koneksinya */
        // $loginFile = '../../../../sgg/include/connect/sap_sd_030.php';
        $loginFile = '../../../sgg/include/connect/sap_sd_210.php';
        $this->_konsap->Connect($this->_sapCon);
        if ($this->_konsap->GetStatus() == SAPRFC_OK ) $this->_konsap->Open ();
        if ($this->_konsap->GetStatus() != SAPRFC_OK ) {
           $this->_konsap->PrintStatus();
           exit;
        }else {
            return $this->_konsap;
        }
    }

    function cek_koneksi()
    {
        $sap = $this->getConnSAP();

        if ($sap->GetStatus() != 'SAPRFC_OK') {
            $ResponseMessage = 'Gagal koneksi ke SAP';
        } else {
            $ResponseMessage = 'Koneksi ke SAP OK';
        }
        return $ResponseMessage;
    }

    function get_data($param)
    {
        $sap = $this->getConnSAP();

        if ($sap->GetStatus() != 'SAPRFC_OK') {
            $ResponseMessage = 'Gagal koneksi ke SAP';
            $responseRequest = $param;
        } else {
            $sap->Open();

            $fce = $sap->NewFunction("Z_ZAPPSD_RPT_REAL");
            if ($fce == false) {
                $ResponseMessage = 'RFC Tidak Ditemukan RFC';
                $responseRequest = $param;
            } else {

                $data = $this->rfc($fce, $param);
                return $data;
            }
            $sap->Close();
            $fce->Close();
        }
    }

    function rfc($fce, $param)
    {
        //   $tgl1 = explode("-",$param['I_TANGGAL_KELUAR_FROM']);
        //   $tgl1 = $tgl1[2]."".$tgl1[1]."".$tgl1[0];
        $tgl1 = str_replace("-", "", $param['X_TGL1']);
        $fce->X_TGL1 = $tgl1;

        //   $tgl2 = explode("-",$param['I_TANGGAL_KELUAR_TO']);
        //   $tgl2 = $tgl2[2]."".$tgl2[1]."".$tgl2[0];
        $tgl2 = str_replace("-", "", $param['X_TGL2']);
        $fce->X_TGL2 = $tgl2;

        
        // $fce->X_WERKS_TO = $param['X_WERKS_TO'];

        if ($param['X_WERKS']=="") {
            foreach ($param['PLANT_CARCONS'] as $key => $value) {
                $fce->LR_PLANT->row['SIGN']='I';
                $fce->LR_PLANT->row['OPTION']='EQ';
                $fce->LR_PLANT->row['LOW']= $value;
                $fce->LR_PLANT->row['HIGH']= '';
                $fce->LR_PLANT->Append($fce->LR_PLANT->row);
            }
        } else $fce->X_WERKS = $param['X_WERKS'];

        
        $fce->X_VKBUR = $param['X_VKBUR'];
        $fce->X_VKBUR_TO = $param['X_VKBUR_TO'];
        $fce->X_BZIRK = $param['X_BZIRK'];
        $fce->X_BZIRK_TO = $param['X_BZIRK_TO'];
        $fce->X_LIFNR = $param['X_LIFNR'];
        $fce->X_LIFNR_TO = $param['X_LIFNR_TO'];
        $fce->X_STATUS = $param['X_STATUS'];
        $fce->X_STATUS_TO = $param['X_STATUS_TO'];
        if ($param['X_VKORG']=="") {
            $fce->X_VKORG = "7900";
        } else $fce->X_VKORG = $param['X_VKORG'];
        $fce->X_LSTEL = $param['X_LSTEL'];
        $fce->X_VBELN = $param['X_VBELN'];
        $fce->X_VBELN_TO = $param['X_VBELN_TO'];
        $fce->X_EBELN = $param['X_EBELN'];
        $fce->X_EBELN_TO = $param['X_EBELN_TO'];
        $fce->X_NOPOLISI = $param['X_NOPOLISI'];
        $fce->X_NOPOLISI_TO = $param['X_NOPOLISI_TO'];
        $fce->X_NOSPJ = $param['X_NOSPJ'];
        $fce->X_NOSPJ_TO = $param['X_NOSPJ_TO'];
        $fce->X_ITEM_NO = $param['X_ITEM_NO'];
        $fce->X_LFART = $param['X_LFART'];
        $fce->X_CURAHBAG = $param['X_CURAHBAG'];
        $fce->X_SO = $param['X_SO'];
        $fce->X_PO = $param['X_PO'];
        $fce->X_SHIFT = $param['X_SHIFT'];
        $fce->X_TIMEFLAG = $param['X_TIMEFLAG'];
        $fce->X_INCO = $param['X_INCO'];
        $fce->X_ROUTE = $param['X_ROUTE'];
        $fce->X_BRAN = $param['X_BRAN'];
        $fce->X_KONFIRMASI = $param['X_KONFIRMASI'];
        $fce->X_WO_KONFIRMASI = $param['X_WO_KONFIRMASI'];
        $fce->X_KUNNR2 = $param['X_KUNNR2'];
        $fce->X_AUART = $param['X_AUART'];
        $fce->X_FLAGTOKO = $param['X_FLAGTOKO'];
        $fce->X_NOFLAG = $param['X_NOFLAG'];
        $fce->X_SHIP_TO_CODE = $param['X_SHIP_TO_CODE'];
        $fce->X_FROM_WHSE = $param['X_FROM_WHSE'];
        $fce->X_TO_WHSE = $param['X_TO_WHSE'];
        $fce->I_TGL_CMPLT_INC = $param['I_TGL_CMPLT_INC'];
        $fce->I_JAM_CMPLT_INC = $param['I_JAM_CMPLT_INC'];
        $fce->X_TGL_MATCH2_LOW = $param['X_TGL_MATCH2_LOW'];
        $fce->X_TGL_MATCH2_HIGH = $param['X_TGL_MATCH2_HIGH'];
        $fce->X_LINE_SO = $param['X_LINE_SO'];
        $fce->X_NO_KTG_ACT = $param['X_NO_KTG_ACT'];
        $fce->X_NO_KTG = $param['X_NO_KTG'];
        $fce->X_GR_PO = $param['X_GR_PO'];
        $fce->X_PALELT = $param['X_PALELT'];
        $fce->X_VKANTONG = $param['X_VKANTONG'];
        $fce->X_WITH_LOOP = $param['X_WITH_LOOP'];
        $fce->X_JUMBO = $param['X_JUMBO'];

        if (is_array($param['X_KUNNR'])) {
            foreach ($param['X_KUNNR'] as $key => $value) {
                $fce->LR_KUNNR->row['SIGN']='I';
                $fce->LR_KUNNR->row['OPTION']='EQ';
                $fce->LR_KUNNR->row['LOW']= $value;
                $fce->LR_KUNNR->row['HIGH']= '';
                $fce->LR_KUNNR->Append($fce->LR_KUNNR->row);
            }
        } else $fce->X_KUNNR = $param['X_KUNNR'];


        if ($param['X_VKORG']=="") {
            $fce->LRI_VKORG->row['SIGN']='I';
            $fce->LRI_VKORG->row['OPTION']='EQ';
            $fce->LRI_VKORG->row['LOW']= "7900";
            $fce->LRI_VKORG->row['HIGH']= '';
            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);

            $fce->LRI_VKORG->row['SIGN']='I';
            $fce->LRI_VKORG->row['OPTION']='EQ';
            $fce->LRI_VKORG->row['LOW']= "7000";
            $fce->LRI_VKORG->row['HIGH']= '';
            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
            
        }

        //echo "<pre>";
        //print_r($fce);
        //echo "</pre>";

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->ZDATA->Reset();

            $i = 0;
            while ($fce->ZDATA->Next()) {
                // if ($fce->ZDATA->row["ATTRIBUTE2"]=="") {
                //     continue;
                // }

                $fce->ZDATA->row["TGL_SPJ"] = "" . substr($fce->ZDATA->row["TGL_SPJ"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_SPJ"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_SPJ"], 0, 4);
                $fce->ZDATA->row["TGL_DO"] = "" . substr($fce->ZDATA->row["TGL_DO"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_DO"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_DO"], 0, 4);
                $fce->ZDATA->row["TGL_MINTA"] = "" . substr($fce->ZDATA->row["TGL_MINTA"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_MINTA"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_MINTA"], 0, 4);
                $fce->ZDATA->row["TGL_MASUK"] = "" . substr($fce->ZDATA->row["TGL_MASUK"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_MASUK"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_MASUK"], 0, 4);
                $fce->ZDATA->row["TGL_ISI"] = "" . substr($fce->ZDATA->row["TGL_ISI"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_ISI"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_ISI"], 0, 4);
                $fce->ZDATA->row["TGL_CMPLT"] = "" . substr($fce->ZDATA->row["TGL_CMPLT"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_CMPLT"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_CMPLT"], 0, 4);
                $fce->ZDATA->row["TGL_RETOKO"] = "" . substr($fce->ZDATA->row["TGL_RETOKO"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_RETOKO"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_RETOKO"], 0, 4);
                $fce->ZDATA->row["TGL_ANTRI"] = "" . substr($fce->ZDATA->row["TGL_ANTRI"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_ANTRI"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_ANTRI"], 0, 4);
                $fce->ZDATA->row["TGL_MATCH2"] = "" . substr($fce->ZDATA->row["TGL_MATCH2"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_MATCH2"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_MATCH2"], 0, 4);
                $fce->ZDATA->row["TGL_GR"] = "" . substr($fce->ZDATA->row["TGL_GR"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_GR"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_GR"], 0, 4);
                $fce->ZDATA->row["TGL_APPROVEALM"] = "" . substr($fce->ZDATA->row["TGL_APPROVEALM"], 6, 2) . "-" . substr($fce->ZDATA->row["TGL_APPROVEALM"], 4, 2) . "-" . substr($fce->ZDATA->row["TGL_APPROVEALM"], 0, 4);
                $fce->ZDATA->row["LAST_UPDATE_DATE"] = "" . substr($fce->ZDATA->row["LAST_UPDATE_DATE"], 6, 2) . "-" . substr($fce->ZDATA->row["LAST_UPDATE_DATE"], 4, 2) . "-" . substr($fce->ZDATA->row["LAST_UPDATE_DATE"], 0, 4);

                $jam_masuk = str_split($fce->ZDATA->row["JAM_MASUK"], 2);
                $fce->ZDATA->row["JAM_MASUK"] = implode(":", $jam_masuk);

                $jam_isi = str_split($fce->ZDATA->row["JAM_ISI"], 2);
                $fce->ZDATA->row["JAM_ISI"] = implode(":", $jam_isi);

                $jam_cmplt = str_split($fce->ZDATA->row["JAM_CMPLT"], 2);
                $fce->ZDATA->row["JAM_CMPLT"] = implode(":", $jam_cmplt);

                $jam_retoko = str_split($fce->ZDATA->row["JAM_RETOKO"], 2);
                $fce->ZDATA->row["JAM_RETOKO"] = implode(":", $jam_retoko);

                $jam_antri = str_split($fce->ZDATA->row["JAM_ANTRI"], 2);
                $fce->ZDATA->row["JAM_ANTRI"] = implode(":", $jam_antri);

                $jam_match2 = str_split($fce->ZDATA->row["JAM_MATCH2"], 2);
                $fce->ZDATA->row["JAM_MATCH2"] = implode(":", $jam_match2);


                // $fce->T_DATA->row["INSERT_DATE"] = "" . substr($fce->T_DATA->row["INSERT_DATE"], 6, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 4, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 0, 4);
                // $fce->T_DATA->row["UPDATE_DATE"] = "" . substr($fce->T_DATA->row["UPDATE_DATE"], 6, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 4, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 0, 4);
                // $fce->T_DATA->row["DELETE_DATE"] = "" . substr($fce->T_DATA->row["DELETE_DATE"], 6, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 4, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 0, 4);
                // $fce->T_DATA->row["TANGGAL_KELUAR2"] = "" . substr($fce->T_DATA->row["TANGGAL_KELUAR2"], 6, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 4, 2) . "/" . substr($fce->T_DATA->row["INSERT_DATE"], 0, 4);

                $this->_data[] = $fce->ZDATA->row;

                $i++;
            }
        }
        return $this->_data;
    }

    function send_data($data,$token)
    {
        // var_dump($token);exit;
        foreach ($data as $value) {
            $tgl_timbang_kosong = explode('-', $value['TGL_MASUK']);
            $tgl_timbang_kosong = $tgl_timbang_kosong[2] . '-' . $tgl_timbang_kosong[1] . '-' . $tgl_timbang_kosong[0] . 'T' . '00:00:00Z';
            $jam_timbang_kosong = '0000-01-01T' . $value['JAM_MASUK'] . 'Z';

            $tgl_timbang_isi = explode('-', $value['TGL_ISI']);
            $tgl_timbang_isi = $tgl_timbang_isi[2] . '-' . $tgl_timbang_isi[1] . '-' . $tgl_timbang_isi[0] . 'T' . '00:00:00Z';
            $jam_timbang_isi = '0000-01-01T' . $value['JAM_ISI'] . 'Z';
            
            $org       = $value['NMORG'];
            $plant     = $value['PLANT'];
            $spj_reff2 = $value['NO_SPJ_REF'];
            $spj_reff3 = $value['NO_SPJ_REFF3'];
            
            if($spj_reff2 == "" && $org == "7900"){
               $spj_reff2 = $value['NO_DO_REFF'];
            }
            
            if($spj_reff2 == "" && ($plant == "7641" || $plant == "7806" ||  $plant == "7633" || $plant == "7808")){  // untuk transaksi VP No SPPS dari LDT yang di simpan referensinya
               $spj_reff2 = $value['NO_SPPS'];
            }

            $no_spps = $value['NO_SPPS'];
            $no_do = $value['NO_DO'];
            $no_do_reff = $value['NO_DO_REFF'];

            if($spj_reff2 == "" && ($plant == "79H7")){  // kondisi khusus smbr
               $spj_reff2 = $value['NO_SPPS'];
               $no_do_reff = $value['NO_SPPS'];
               $no_spps = '';
            }
            
            if($spj_reff3 == "" && $org == '7900' && $spj_reff2 != ""){
               $spj_reff3 = $value['NO_DO_REFF3']; 
            }

            $body[] = array(
                'spj_no' => $value['NO_SPJ'],
                'spj_opco' => $spj_reff2,
                'so_no' => $value['NO_SO'],
                'fios_ref_no' => $value['ATTRIBUTE2'],
                'tanggal_timbang_kosong' => $tgl_timbang_kosong,
                'jam_timbang_kosong' => $jam_timbang_kosong,
                'berat_timbang_kosong' => (float) ($value['BERAT_KOSONG']/1000),
                'tanggal_timbang_isi' => $tgl_timbang_isi,
                'jam_timbang_isi' => $jam_timbang_isi,
                'berat_timbang_isi' => (float) ($value['BERAT_ISI']/1000),
                'qty_muatan' =>  (float) ($value['TOTAL_QTY']/1000),
                'third_spj_no' => $spj_reff3,
                'no_ldt' => $no_spps, //tambahan 79B4
                'no_do_md' => $no_do, //tambahan 79B4
                'no_do_reff' => $no_do_reff //tambahan 79B4
             );
        }

        $body_string = json_encode($body);

        // $url = "https://thirdparty-api.accelog.com/api/spj";
        // $url = "http://fios-api.id-fleet.net/api/spj"; //prod
        // $url = "https://api.silog.co.id/api/spj"; //prod
        $url = "http://api-silog.id-transport.net/api/spj"; //dev
        // $headers = array(
        //     'Content-Type: application/json',
        //     'Authorization: Bearer ' . $token,
        // );
        try {
            // $ch = curl_init();
            // curl_setopt($ch, CURLOPT_URL, $url);
            // curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // curl_setopt($ch, CURLOPT_POST, true);
            // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            // curl_setopt($ch, CURLOPT_POSTFIELDS, $body_string);
            // $response = curl_exec($ch);

            // if (curl_errno($ch)) {
            //     echo curl_error($ch);
            // } else {
            //     // echo $response;
            // }

            // $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            // curl_close($ch);

            // $command = "curl $url --insecure --request POST --header 'Content-Type: application/json' --data '$body_string'";
            $command = "curl $url --insecure --request POST --header 'Content-Type: application/json' --header 'Authorization: Bearer $token' --data '$body_string'";
            exec($command, $result);

            // Combine the array elements into a single JSON string
            $result = implode("", $result);

            // Remove unnecessary whitespace
            $result = str_replace(array("\n", "\r", "\t"), '', $result);

            $result = json_decode($result, true);
            $result = array('Token'=>$token, 'result_silog' =>  $result, 'push_data' => $body);

            return $result;
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    function login() {
        // $url = "https://thirdparty-api.accelog.com/api/auth/login";
        // $url = "http://fios-api.id-fleet.net/api/auth/login"; //prod
        // $url = "https://api.silog.co.id/api/auth/login"; //prod
        $url = "http://api-silog.id-transport.net/api/auth/login"; //dev
        $headers = array(
            'Content-Type: application/json',
        );
        $body = array(
            'username'=> 'sig',
            'password'=> 'sig123'
        );
        // $body_string = json_encode($body);
        $body_string = '{"username":"sig", "password":"sig123"}';
        $command = "curl $url --insecure --request POST --header 'Content-Type: application/json' --data '$body_string'";
        exec($command, $result);
        
        // Combine the array elements into a single JSON string
        $jsonString = implode("", $result);

        // Remove unnecessary whitespace
        $jsonString = str_replace(array("\n", "\r", "\t", " "), '', $jsonString);

        // Decode the JSON string into a PHP array
        return $resultArray = json_decode($jsonString, true);





        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body_string);
            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                // echo  curl_error($ch);
            } else {
                // echo $response;
            }

            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $response = json_decode($response, true);
            return $response;
          
        } catch (Exception $e) {
            echo $e->getMessage();
        }

    }
}
