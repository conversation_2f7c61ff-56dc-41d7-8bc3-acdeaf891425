<?php
/*
 * @liyantanto
 */

session_start();
include ('../include/or_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$orgva=$fungsi->arrayorg();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$orguntil = isset($_GET['orguntil']) ? strval($_GET['orguntil']) : '';
if(count($orgva)>0){
    foreach ($orgva as $key => $value) {
        if($orguntil=='1'){
            if($key!=$user_org){
                $row['ORGINH']=$key;
                $row['NAMEORG']=$value;
                array_push($result, $row);
            }
        }else{
            $row['ORGINH']=$key;
            $row['NAMEORG']=$value;
            array_push($result, $row);
        }
    }
}    
echo json_encode($result);
?>
