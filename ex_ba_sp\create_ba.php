<?
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
require_once ('../security_helper.php');
sanitize_global_input();

include_once 'helper.php';
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

//Koneksi DEVSD
require_once '../include/oracleDev.php'; 
$fungsi2=new conntoracleDEVSD();
$conn2=$fungsi2->DEVSDdb();

$halaman_id=4869; //PROD
// $halaman_id=3095; DEV
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$show_ket='';




$page="create_ba.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);

//$vendor='0000410019';
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_shipment = $_POST['no_shipment'];
$distributor = $_POST['distributor'];
$tipe_transaksi = $_POST['tipe_transaksi'];
$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$warna_plat = $_POST['warna_plat'];
$tahun = date("Y");
if (isset($_POST['tahun']))
$tahun=$_POST['tahun'];
$bulan = $_POST['bulan'];
$plant = $_POST['plant'];
$tgl_tremin=$bulan.$tahun;
$termin = $_POST['termin'];
$status_asal = $_POST['status_asal'];
$organisasi = $user_org;
$jeniSpj = $_POST['jenis_spj'];

$currentPage="create_ba.php";
$komen="";

$mp_coics=$fungsi->getComin($conn,$user_org);
$nama = $fungsi->arrayorg();

if(isset($_POST['cari'])){
        $tahun=$_POST['tahun'];

    $bulan = $_POST['bulan'];
    // echo $bulan.'<br>';
  $tgl_tremin=$bulan.$tahun;
        $termin = $_POST['termin'];
        $orgselect = $_POST['orgCode']; 
        $mp_coics=$fungsi->getComin($conn,$user_org);
        /*if(count($mp_coics)>0){
            unset($inorg);$orgcounter=0;
            foreach ($mp_coics as $keyOrg => $valorgm){
                  $inorg .="'".$keyOrg."',";
                  $orgcounter++;
            }
            $orgIn= rtrim($inorg, ',');
        }else{
           $orgIn= $user_org;
        }*/
        
        if($orgselect !=''){ 
            $orgIn = $_POST['orgCode'];
        }

  $jumHari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
        //echo "Tahun : ".$id_tahun." : ".$jumHari;

  if ($termin==1)
       {
      $tanggal_mulai_sql='1-'.$bulan.'-'.$tahun;
      $tanggal_selesai_sql='10-'.$bulan.'-'.$tahun;
    }
  ELSEif ($termin==2)
     {
      $tanggal_mulai_sql='11-'.$bulan.'-'.$tahun;
      $tanggal_selesai_sql='20-'.$bulan.'-'.$tahun;
    }
  ELSEif ($termin==3)
     {
    $tanggal_mulai_sql='21-'.$bulan.'-'.$tahun;
    $tanggal_selesai_sql=$jumHari.'-'.$bulan.'-'.$tahun;
    }
  ELSEif ($termin==4)
     {
    $tanggal_mulai_sql='01-'.$bulan.'-'.$tahun;
    $tanggal_selesai_sql=$jumHari.'-'.$bulan.'-'.$tahun;
    }
  ELSE {

        $tanggal_mulai_sql='01-'.$bulan.'-'.$tahun;
        //$tanggal_mulai_sql='01-'.'09'.'-'.$tahun;
    $tanggal_selesai_sql=$jumHari.'-'.$bulan.'-'.$tahun;
    }
  if($plant=="" and $no_shipment=="" and $distributor=="" and $vendor=="" and $tipe_transaksi == "" and $tahun == "" and $bulan == "" and $warna_plat == ""){
    $sql= "SELECT * FROM EX_PAJAK_HDR_V4 WHERE TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') AND DELETE_MARK = '0' AND ORG in ($orgIn) AND STATUS = 'OPEN' AND STATUS2 = 'OPEN' AND VEHICLE_TYPE<>'205' AND KELOMPOK_TRANSAKSI = 'DARAT' AND STATUS_PAJAK = 'OK' ORDER BY ORG,NAMA_VENDOR,NO_SHP_TRN ASC";
    $query= oci_parse($conn, $sql);
  }else {

  // START non-prepared statement
    // $pakeor=0;
    // $sql= "SELECT DISTINCT EX_PAJAK_HDR_V4.* FROM EX_PAJAK_HDR_V4";
    // if ($status_asal=="E-LOG") {
    //   # code...
    //    $sql.=" JOIN EX_INPUTCLAIM_SEMEN ON EX_PAJAK_HDR_V4.NO_SHP_TRN = EX_INPUTCLAIM_SEMEN.NO_SPJ";
    // }
    // $sql.=" WHERE";
    // if($no_shipment!=""){
    // $sql.=" EX_PAJAK_HDR_V4.NO_SHP_TRN LIKE '$no_shipment' ";
    // $pakeor=1;
    // }
    // if($distributor!=""){
    //   if($pakeor==1){
    //   $sql.=" AND ( EX_PAJAK_HDR_V4.NAMA_SOLD_TO LIKE '$distributor' OR EX_PAJAK_HDR_V4.SOLD_TO LIKE '$distributor' ) ";
    //   }else{
    //   $sql.=" ( EX_PAJAK_HDR_V4.NAMA_SOLD_TO LIKE '$distributor' OR EX_PAJAK_HDR_V4.SOLD_TO LIKE '$distributor' ) ";
    //   $pakeor=1;
    //   }
    // }
    // if($no_shipment!=""){
    //   if($pakeor==1){
    //   $sql.=" AND EX_PAJAK_HDR_V4.NO_SHP_TRN LIKE '$no_shipment' ";
    //   }else{
    //   $sql.=" EX_PAJAK_HDR_V4.NO_SHP_TRN LIKE '$no_shipment' ";
    //   $pakeor=1;
    //   }
    // }
    // if($vendor!=""){
    //   if($pakeor==1){
    //   $sql.=" AND ( EX_PAJAK_HDR_V4.NAMA_VENDOR LIKE '$vendor' OR EX_PAJAK_HDR_V4.VENDOR = '".str_pad($vendor,10,"0",STR_PAD_LEFT)."' ) ";
    //   }else{
    //   $sql.=" ( EX_PAJAK_HDR_V4.NAMA_VENDOR LIKE '$vendor' OR EX_PAJAK_HDR_V4.VENDOR = '".str_pad($vendor,10,"0",STR_PAD_LEFT)."' ) ";
    //   $pakeor=1;
    //   }
    // }
    // if($plant!=""){
    //   if($pakeor==1){
    //   $sql.=" AND EX_PAJAK_HDR_V4.PLANT LIKE '$plant' ";
    //   }else{
    //   $sql.=" EX_PAJAK_HDR_V4.PLANT LIKE '$plant' ";
    //   $pakeor=1;
    //   }
    // }
    // if($tipe_transaksi!=""){
    //   if($pakeor==1){
    //   $sql.=" AND EX_PAJAK_HDR_V4.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
    //   }else{
    //   $sql.=" EX_PAJAK_HDR_V4.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
    //   $pakeor=1;
    //   }
    // }
    // if($bulan!="" or $tahun!=""){
    //   if ($tahun=="")$tahun = date("Y");
    //   if ($bulan=="")$bulan = date("m");

    //   if($pakeor==1){
    //   $sql.=" AND EX_PAJAK_HDR_V4.TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY')";
    //   }else{
    //   $sql.="  EX_PAJAK_HDR_V4.TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
    //   $pakeor=1;
    //   }

    // }
    // if($warna_plat!=""){
    //   if($pakeor==1){
    //     $sql.=" AND LOWER(WARNA_PLAT) like LOWER('$warna_plat%') ";
    //   }else{
    //       $sql.=" LOWER(WARNA_PLAT) like LOWER('$warna_plat%') ";
    //   $pakeor=1;
    //   }
    // }
    // if($jeniSpj !=""){
    //     if($jeniSpj =='klaim'){
    //         $sql.="
    //         AND (
    //             EX_PAJAK_HDR_V4.QTY_KTG_RUSAK > 0
    //             OR
    //             EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK > 0
    //         )
    //         ";
    //     }else if($jeniSpj =='ds'){
    //         $sql.="
    //         AND (
    //             EX_PAJAK_HDR_V4.SOLD_TO like '00000003%'
    //         )
    //         ";        
    //     }else{
    //         $sql.="
    //         AND (
    //             EX_PAJAK_HDR_V4.QTY_KTG_RUSAK <= 0
    //             AND
    //             EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK <=0
    //         )
    //         ";
    //     }
    // }
    
    // $sql.=" AND EX_PAJAK_HDR_V4.DELETE_MARK = '0' AND EX_PAJAK_HDR_V4.ORG in ($orgIn) AND EX_PAJAK_HDR_V4.STATUS = 'OPEN' AND EX_PAJAK_HDR_V4.STATUS2 = 'OPEN' AND EX_PAJAK_HDR_V4.VEHICLE_TYPE<>'205' AND EX_PAJAK_HDR_V4.KELOMPOK_TRANSAKSI = 'DARAT' AND EX_PAJAK_HDR_V4.STATUS_PAJAK = 'OK' AND EX_PAJAK_HDR_V4.FLAG_POD IN('POD-ATOM','POD-EPOOOL','POD-FIOS') AND REJECT_STATUS = '1' ORDER BY EX_PAJAK_HDR_V4.ORG,VENDOR, EX_PAJAK_HDR_V4.SAL_DISTRIK, EX_PAJAK_HDR_V4.NO_SHP_TRN ASC";
    // $query = oci_parse($conn, $sql);
  // END non-prepared statement
  
  // START prepared statement
    $params = array();

    $sql = "SELECT DISTINCT EX_PAJAK_HDR_V4.* FROM EX_PAJAK_HDR_V4";

    if ($status_asal == "E-LOG") {
        $sql .= " JOIN EX_INPUTCLAIM_SEMEN ON EX_PAJAK_HDR_V4.NO_SHP_TRN = EX_INPUTCLAIM_SEMEN.NO_SPJ";
    }

    $sql .= " WHERE 1=1";

    if ($orgIn) {
      $sql .= " AND EX_PAJAK_HDR_V4.ORG = :org_in";
      $params[':org_in'] = $orgIn;
    } else {
        // paksa query tidak hasilkan data jika org kosong
        $sql .= " AND 1=0";
    }

    if ($no_shipment != "") {
        $sql .= " AND EX_PAJAK_HDR_V4.NO_SHP_TRN LIKE :no_shipment";
        $params[':no_shipment'] = $no_shipment;
    }

    if ($distributor != "") {
        $sql .= " AND (EX_PAJAK_HDR_V4.NAMA_SOLD_TO LIKE :distributor OR EX_PAJAK_HDR_V4.SOLD_TO LIKE :distributor)";
        $params[':distributor'] = $distributor;
    }

    if ($vendor != "") {
        $sql .= " AND (EX_PAJAK_HDR_V4.NAMA_VENDOR LIKE :vendor_name OR EX_PAJAK_HDR_V4.VENDOR = :vendor_code)";
        $params[':vendor_name'] = $vendor;
        $params[':vendor_code'] = str_pad($vendor, 10, "0", STR_PAD_LEFT);
    }

    if ($plant != "") {
        $sql .= " AND EX_PAJAK_HDR_V4.PLANT LIKE :plant";
        $params[':plant'] = $plant;
    }

    if ($tipe_transaksi != "") {
        $sql .= " AND EX_PAJAK_HDR_V4.TIPE_TRANSAKSI LIKE :tipe_transaksi";
        $params[':tipe_transaksi'] = $tipe_transaksi;
    }

    if ($bulan != "" || $tahun != "") {
        if ($tahun == "") $tahun = date("Y");
        if ($bulan == "") $bulan = date("m");

        $sql .= " AND EX_PAJAK_HDR_V4.TANGGAL_KIRIM BETWEEN TO_DATE(:tanggal_mulai, 'DD-MM-YYYY') AND TO_DATE(:tanggal_selesai, 'DD-MM-YYYY')";
        $params[':tanggal_mulai'] = $tanggal_mulai_sql;
        $params[':tanggal_selesai'] = $tanggal_selesai_sql;
    }

    if ($warna_plat != "") {
        $sql .= " AND LOWER(WARNA_PLAT) LIKE LOWER(:warna_plat)";
        $params[':warna_plat'] = strtolower($warna_plat) . '%';
    }

    if ($jeniSpj != "") {
        if ($jeniSpj == 'klaim') {
            $sql .= " AND (EX_PAJAK_HDR_V4.QTY_KTG_RUSAK > 0 OR EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK > 0)";
        } elseif ($jeniSpj == 'ds') {
            $sql .= " AND EX_PAJAK_HDR_V4.SOLD_TO LIKE '00000003%'";
        } else {
            $sql .= " AND EX_PAJAK_HDR_V4.QTY_KTG_RUSAK <= 0 AND EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK <= 0";
        }
    }

    $sql .= " AND EX_PAJAK_HDR_V4.DELETE_MARK = '0'
              AND EX_PAJAK_HDR_V4.STATUS = 'OPEN'
              AND EX_PAJAK_HDR_V4.TIPE_TRANSAKSI = 'BAG'
              AND EX_PAJAK_HDR_V4.STATUS2 = 'OPEN'
              AND EX_PAJAK_HDR_V4.VEHICLE_TYPE <> '205'
              AND EX_PAJAK_HDR_V4.KELOMPOK_TRANSAKSI = 'DARAT'
              AND EX_PAJAK_HDR_V4.STATUS_PAJAK = 'OK'
              AND EX_PAJAK_HDR_V4.FLAG_POD IN ('POD-ATOM','POD-EPOOOL','POD-FIOS')
              AND REJECT_STATUS = '1'
              ORDER BY EX_PAJAK_HDR_V4.ORG, VENDOR, EX_PAJAK_HDR_V4.SAL_DISTRIK, EX_PAJAK_HDR_V4.NO_SHP_TRN ASC";

    $query = oci_parse($conn, $sql);

    // Binding satu per satu
    foreach ($params as $key => $val) {
        oci_bind_by_name($query, $key, $params[$key]);
    }
  // END prepared-statement
  }

  //  echo 'query '.$sql;
  oci_execute($query);

    
    $sqlcek = "SELECT SHIPTO FROM OR_MAP_SHIPTO_3PL WHERE DELETE_MARK = '0'";
    $params = array();
    if ($orgIn) {
      $sqlcek .= " AND ORG = :org_in";
      $params[':org_in'] = $orgIn;
    } else {
        // paksa query tidak hasilkan data jika org kosong
        $sqlcek .= " AND 1=0";
    }
    $querycek = oci_parse($conn, $sqlcek);
    foreach ($params as $key => $val) {
        oci_bind_by_name($querycek, $key, $params[$key]);
    }
    oci_execute($querycek);
    $shiptoblack = array();
    while($datacek=oci_fetch_assoc($querycek)){
        array_push($shiptoblack, $datacek[SHIPTO]);
    }
  //  echo $sql;exit;
    $qry_str = "?bulan=".$bulan."&tahun=".$tahun."";
    // $ch = curl_init();

    // Set query data here with the URL
    //DEV 
    // curl_setopt($ch, CURLOPT_URL, 'http://***********/dev/sd/sdonline/accelog_api_bulan.php' . $qry_str); 

    //prod
    // curl_setopt($ch, CURLOPT_URL, 'https://csms.sig.id/sdonline/accelog_api_bulan.php' . $qry_str); 

    // curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    // curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    // $content = trim(curl_exec($ch));
    // curl_close($ch);

  while($row=oci_fetch_array($query)){
      
//      echo in_array($row[SHIP_TO], $shiptoblack);
      if((in_array($row[SHIP_TO], $shiptoblack)) && ($status_asal=="NON-ELOG")){
          continue;
      } else if((!in_array($row[SHIP_TO], $shiptoblack)) && ($status_asal=="E-LOG")){
          continue;
      }else{
        $com[]=$row[ORG];
        $no_shipment_v[]=$row[NO_SHP_TRN];
        $spjmd[]=$row[NO_SHP_TRN2];
        $tgl_kirim_v[]=$row[TANGGAL_KIRIM];
        $tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
        $tgl_datang_v[]=$row[TANGGAL_DATANG];
        $produk_v[]=$row[NAMA_PRODUK];
        $plant_v[]=$row[PLANT];
        $no_pol_v[]=$row[NO_POL];
        $warna_plat_v[]=$row[WARNA_PLAT];
        $sal_distrik_v[]=$row[SAL_DISTRIK];
        $sold_to_v[]=$row[SOLD_TO];
        $nama_sold_to_v[]=$row[NAMA_SOLD_TO];
        $qty_v[]=$row[QTY_SHP];
        $qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
        $qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
        $total_ktg_rusak[]=$row[TOTAL_KTG_RUSAK];
        $total_ktg_rezak[]=$row[TOTAL_KTG_REZAK];
        $total_semen_rusak[]=$row[TOTAL_SEMEN_RUSAK];
        $total_klaim_ktg[]=$row[TOTAL_KLAIM_KTG];
        $total_klaim_semen[]=$row[TOTAL_KLAIM_SEMEN];
        $pdpks[]=$row[PDPKS];
        $id_v[]=$row[ID];
        $tarif_cost_v[]=$row[TARIF_COST];
        $shp_cost_v[]=$row[SHP_COST];
        $no_vendor_v=$row[VENDOR];
        $nama_vendor_v=$row[NAMA_VENDOR];
        $spt_cek=$row[SPT_PAJAK];
        $lampiran[]=$row[EVIDENCE_POD1];
        $lampiran2[]=$row[EVIDENCE_POD2];
        $flag_POD[]=$row[FLAG_POD];
        $GEOFENCE_POD[]=$row[GEOFENCE_POD];
        $READY_TO_INV[]=$row[READY_TO_INV];
        $KETERANGAN_POD[] = $row[KETERANGAN_POD];
    

	#ECHO'<BR>'.	$sqlok="select * from EX_INVOICE WHERE NO_VENDOR='".$row[VENDOR]."'";
        // $sqlok="select tgl_termin, termin from EX_INVOICE WHERE NO_VENDOR='".$row[VENDOR]."' and tgl_termin='".$tgl_tremin."' and termin is not null group by tgl_termin, termin";
        // $queryok= oci_parse($conn, $sqlok);
        // @oci_execute($queryok); $arr_termin = array();
        // while($data=@oci_fetch_array($queryok)){
        //   $novendor[]=$data['NO_VENDOR'];
        //       $tgl_tremin1=$data['TGL_TERMIN'];
        //   $termin1=$data['TERMIN'];
        //   $arr_termin[$termin1] = $tgl_tremin1;
        // }
        //echo "<br><br>-----dor";

        //print_r($arr_termin);
      }
  }

  $lanjut = false;
  if (@array_key_exists($termin, $arr_termin)) {
    $lanjut = true;
  }

//  if ($lanjut)
//  {
//    echo "<script>alert('Tremin Sudah ada');</script>";
//    exit();
//  }
  $total=count($no_shipment_v);
  if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}else if(isset($_POST['action'])){
        // echo $_POST['tanggal_pjk'];exit;
        $no_pajak_vendor_cek=trim($_POST['pjk2']).trim($_POST['pjk3']).trim($_POST['pjk4']);
        $tanggal_pjkceke=trim($_POST['tanggal_pjk']);
        $kepalapjak=trim($_POST['pjk1']);
        $cektahun=trim($_POST['tahun']);

        // if($no_pajak_vendor_cek!='' && $tanggal_pjkceke!=''){
            //$no_pajak_vendor_cek."<br>";

            //parameter tanggal
            $pecah2 = explode("-", $_POST['tanggal_pjk']);
            $date2 = $pecah2[0];
            $month2 = $pecah2[1];
            $year2 = $pecah2[2];
            $fromat2=$year2.$month2.$date2;

//             if($fromat2>='20130401'){
//                      //Koneksi SAP
//                      //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php";
//                      $sap = new SAPConnection();
//                      $sap->Connect("../include/sapclasses/logon_data.conf");
//                      //$sap->Connect($link_koneksi_sap);
//                      if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
//                         if ($sap->GetStatus() != SAPRFC_OK ) {
//                         echo $sap->PrintStatus();
//                         exit;
//                      }
//                     $fce = &$sap->NewFunction ("ZAPPSD_DISPLAYFAKTUR");
//                     if ($fce == false ) {
//                        $sap->PrintStatus();
//                        exit;
//                     }
//                     $org_fpajakac=trim($_POST['org_fpajak']);
//                     if($org_fpajakac!=''){
//                         $orgIn4st = $org_fpajakac;
//                     }else{
//                         $orgIn4st = $user_org;
//                     }

//                     //$fce->I_BUKRS=$orgIn4st;//$user_org;
//                     $fce->I_LIFNR=$vendor;
//                     $fce->I_BUDAT=$fromat2;
//                     $fce->I_XBLNR=$kepalapjak.$no_pajak_vendor_cek;

//                     $fce->Call();
//                     if ($fce->GetStatus() == SAPRFC_OK ) {
//                     $fce->T_DATA->Reset();
//                     $q = 0;
//                     unset($arrayu);
//                     $arrayu=array();
//                     $FPNUMH='';$FPNUML='';
//                     while ( $fce->T_DATA->Next() ){
//                             //filter data
//                             $arrayu[]=$fce->T_DATA->row;
//                             $FPNUML=$fce->T_DATA->row['FPNUML'];
//                             $FPNUMH=$fce->T_DATA->row['FPNUMH'];
//                     }


//                     $tipeerr=$fce->RETURN['TYPE'];
//                     $pesansap=$fce->RETURN['MESSAGE'];

//                     }else{
//                             $fce->PrintStatus();
//                             $fce->Close();
//                             $sap->Close();
//                     }

//                     if($tipeerr=='S'){
//                        // echo $show_ket =$FPNUML." | ".$FPNUMH." Ketemu";

// //                               echo "<pre>";
// //                               print_r($_POST);
// //                               echo "</pre>";

//                        unset($totjumlah);unset($persenTot);
//                        foreach ($_POST as $key => $value) {
//                            foreach ($_POST as $idkey => $idvalue) {
//                                if(substr($idkey,0,4)=='idke'){
//                                    if($key=='jumlah'.$idvalue){
//                                        $totjumlah +=$value;
//                                        $orgCom="orgp".$idvalue;
//                                        $orgIn4st = trim($_POST[$orgCom]);
//                                    }
//                                }
//                            }
//                        }
//                        //echo "Total ".$totjumlah;
//                        $persenTot=@(($totjumlah*10)/100);
//                        $totplusppn=$totjumlah+$persenTot;
//                        $toleransiP=10000000;//10000000

//                        if($totplusppn >= $toleransiP){
//                            if(($orgIn4st == '7000' && $cektahun==2017) || $orgIn4st == '5000'){ //perubahan pajak KSO tiket 26432
//                                if($kepalapjak=='010'){
//                                     //exit;
//                                     $user_name_cek_id=$_SESSION['user_name'];
//                                     if($user_name_cek_id != ""){
//                                         $action=$_POST['action'];
//                                         include ('formula.php');
//                                     }
//                                 }else{
//                                 $show_ket ="Silahkan Periksa kembali No Pajak Expeditur !!! Harus 010"."<br>";
//                            }
//                            }else{
//                                if($kepalapjak=='030'){
//                                     //exit;
//                                     $user_name_cek_id=$_SESSION['user_name'];
//                                     if($user_name_cek_id != ""){
//                                         $action=$_POST['action'];
//                                         include ('formula.php');
//                                     }
//                                }else{
//                                     $show_ket ="Silahkan Periksa kembali No Pajak Expeditur !!! Harus 030"."<br>";
//                                }
//                            }

//                        }else{
//                            if($kepalapjak=='010'){
//                                 //exit;
//                                 $user_name_cek_id=$_SESSION['user_name'];
//                                 if($user_name_cek_id != ""){
//                                     $action=$_POST['action'];
//                                     include ('formula.php');
//                                 }
//                            }else{
//                                 $show_ket ="Silahkan Periksa kembali No Pajak Expeditur !!! Harus 010"."<br>";
//                            }
//                        }

//                     }else{
//                         $show_ket ="Silahkan Periksa kembali Tanggal Pajak dan No Pajak Expeditur !!! (".$pesansap.")"."<br>";
//                     }
// //                               echo "<pre>";
// //                               print_r($arrayu);
// //                               echo "</pre>";
//             }else{
                //exit;
                $user_name_cek_id=$_SESSION['user_name'];
                if($user_name_cek_id != ""){
                     $action=$_POST['action'];
                     include ('formula.php');
                }
            // }


        // }else{
        //     $show_ket ="Silahkan Isi Tanggal Pajak dan No Pajak Expeditur !!!";
        // }

        $total=0;
}

  $user = new User_SP();
  $data_user_approval = $user->get_pejabat_eks_manual();


?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()

{if (document.all)
{(message);return false;}}

function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}

document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Create BASTP :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<script src="../include/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">
<script type="text/javascript">
checked=false;
function checkedAll (frm1) {
  var aa= document.getElementById('fsimpan');
  var lampiran = document.getElementById('lampiran').value;
  // console.log(lampiran)
  var lampiran_ttd = document.getElementById('lampiran_ttd').value;
  // console.log(lampiran_ttd)
   if (checked == false)
          {
          if(lampiran == 0 || lampiran_ttd == 0){
            alert('Harap lengkapi data SPJ')
            checked = false
          } 
          if(lampiran != 0 || lampiran_ttd != 0){
                checked = true
                 markAllRows('fsimpan');
          }
       
          }
        else
          {
             // alert('Kondisi else 2')
          checked = false
      unMarkAllRows('fsimpan')
          }
/*	for (var i =0; i < aa.elements.length; i++) 
  {
   aa.elements[i].checked = checked;

  }
*/ }

function cek_org(id_cek) {
    var obj = document.getElementById(id_cek);
    var cek = obj.value;
    var satu_data = "0";
    for (var keb = 0; keb < cek; keb++){
        var rowke = 'idke'+keb;
        var com_rowke = document.getElementById(rowke);
        if (com_rowke.checked == true)  {
            satu_data = "1";
        }
    }
    var cekdata = 0; var kec1; var kec2;
    for (var keb1 = 0; keb1 < cek; keb1++){
        kec1 = keb1 ;
        for (var keb2 = 0; keb2 < cek; keb2++){
            kec2 = keb2;
            var rowke = 'idke'+kec1;
            var com_rowke = document.getElementById(rowke);

            var rowke2 = 'idke'+kec2;
            var com_rowke2 = document.getElementById(rowke2);

            var rowke = 'orgke'+kec1;
            var com_roworg1 = document.getElementById(rowke);

            var rowke2 = 'orgke'+kec2;
            var com_roworg2 = document.getElementById(rowke2);

            if (com_rowke.checked == true && com_rowke2.checked == true)  {

                 if (com_roworg1.value != com_roworg2.value ) {
                        cekdata++;

                }
            }
        }
    }

    
    if (parseInt(cekdata) > 0) {
        alert("ORG yang dipilih harus sama ...");
        return document.hasil = false;
    }
    if (satu_data == "0") {
        alert("Minimal Pilih Satu Data...");
        return document.hasil = false;
    }
    // var lampiran = document.getElementById('lampiran');
    // var lampiran_v = lampiran.value;
    // if(lampiran_v == "0"){
    //     alert("SPJ harus diupload di sistem...");
    //     return document.hasil = false;
    // }
    return document.hasil = true;
}



function markAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
      if (checkbox.checked != true){
        checkbox.checked = true;
        rows[i].className += ' selected';
      }
        }
    }

    return true;
}

function unMarkAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
      if (checkbox.checked != false){
      checkbox.checked = false;
            rows[i].className = rows[i].className.replace(' selected', '');
      }
        }
    }

    return true;
}

</script>

<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<script type="text/javascript">

function IsNumeric(obj,panjang)
   //  check for valid numeric strings
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length != panjang){
     alert("Isi dengan Angka " + panjang + " digit..");
   obj.value="";
   return false;
  } else {
     //  test strString consists of valid characters listed above
     for (i = 0; i < strString.length; i++)
      {
      strChar = strString.charAt(i);
      if (strValidChars.indexOf(strChar) == -1)
       {
       alert("Hanya Masukkan Angka 0-9 dengan " + panjang + " digit..");
       obj.value="";
       return false;
       }
      }
   }
   }


function addLoadEvent(func) {
  var oldonload = window.onload;
  if (typeof window.onload != 'function') {
    window.onload = func;
  } else {
    window.onload = function() {
      oldonload();
      func();
    }
  }
}

function addClass(element,value) {
  if (!element.className) {
    element.className = value;
  } else {
    newClassName = element.className;
    newClassName+= " ";
    newClassName+= value;
    element.className = newClassName;
  }
}


function stripeTables() {
  var tables = document.getElementsByTagName("table");
  for (var m=0; m<tables.length; m++) {
    if (tables[m].className == "pickme") {
      var tbodies = tables[m].getElementsByTagName("tbody");
      for (var i=0; i<tbodies.length; i++) {
        var odd = true;
        var rows = tbodies[i].getElementsByTagName("tr");
        for (var j=0; j<rows.length; j++) {
          if (odd == false) {
            odd = true;
            addClass(rows[j],"odd1");
          } else {
            addClass(rows[j],"odd0");
            odd = false;
          }
        }
      }
    }
  }
}
function highlightRows() {
  if(!document.getElementsByTagName) return false;
    var tables = document.getElementsByTagName("table");
  for (var m=0; m<tables.length; m++) {
    if (tables[m].className == "pickme") {
        var tbodies = tables[m].getElementsByTagName("tbody");
        for (var j=0; j<tbodies.length; j++) {
         var rows = tbodies[j].getElementsByTagName("tr");
         for (var i=0; i<rows.length; i++) {
             rows[i].oldClassName = rows[i].className
             rows[i].onmouseover = function() {
              if( this.className.indexOf("selected") == -1)
               addClass(this," highlight");
             }
             rows[i].onmouseout = function() {
              if( this.className.indexOf("selected") == -1)
               this.className = this.oldClassName
             }
         }
        }
    }
  }
}

function selectRowCheckbox(row) {
  var checkbox = row.getElementsByTagName("input")[0];
  if (checkbox.checked == true) {
    checkbox.checked = false;
  } else
  if (checkbox.checked == false) {
    checkbox.checked = true;
  }
}

function lockRow() {
    var tables = document.getElementsByTagName("table");
  for (var m=0; m<tables.length; m++) {
    if (tables[m].className == "pickme") {
        var tbodies = tables[m].getElementsByTagName("tbody");
        for (var j=0; j<tbodies.length; j++) {
          var rows = tbodies[j].getElementsByTagName("tr");
          for (var i=0; i<rows.length; i++) {
            rows[i].oldClassName = rows[i].className;
            // rows[i].onclick = function() {
            //   if (this.className.indexOf("selected") != -1) {
            //     this.className = this.oldClassName;
            //   } else {
            //     addClass(this," selected");
            //   }
            //   selectRowCheckbox(this);
            // }
          }
        }
    }
  }
}

addLoadEvent(stripeTables);
addLoadEvent(highlightRows);
addLoadEvent(lockRow);


function lockRowUsingCheckbox() {
  var tables = document.getElementsByTagName("table");
  for (var m=0; m<tables.length; m++) {
    if (tables[m].className == "pickme") {
      var tbodies = tables[m].getElementsByTagName("tbody");
      for (var j=0; j<tbodies.length; j++) {
        var checkboxes = tbodies[j].getElementsByTagName("input");
        for (var i=0; i<checkboxes.length; i++) {
          checkboxes[i].onclick = function(evt) {
            if (this.parentNode.parentNode.className.indexOf("selected") != -1){
              this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
            } else {
              addClass(this.parentNode.parentNode," selected");
            }
            if (window.event && !window.event.cancelBubble) {
              window.event.cancelBubble = "true";
            } else {
              evt.stopPropagation();
            }
          }
        }
      }
    }
  }
}
addLoadEvent(lockRowUsingCheckbox);

function findplant(org) {
    var com_org = document.getElementById('org');
    var strURL="cari_plant.php?org="+com_org.value;
    popUp(strURL);
}
function ketik_plant(obj) {
  var com=document.getElementById('org');
  var nilai_tujuan =obj.value;
  var cplan=document.getElementById('nama_plant');
  cplan.value = "";
  var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
  var req = getXMLHTTP();
  if (req) {
    req.onreadystatechange = function() {
      if (req.readyState == 4) {
        // only if "OK"
        if (req.status == 200) {
          document.getElementById('plantdiv').innerHTML=req.responseText;
        } else {
          alert("There was a problem while using XMLHTTP:\n" + req.statusText);
        }
      }
    }
    req.open("GET", strURL, true);
    req.send(null);
  }
}
function kodepajak(val){
    if((val.value!='010')&&(val.value!='030')){
        alert("No Pajak Expeditur dengan awalan 010 atau 030");
        val.value='';
        val.select();
        val.focus();
        return false;
    }else{
  return true;
    }

}

function find_rek() {
    var no_vendor = document.getElementById("no_vendor");
    var strURL="cari_rek.php?no_vendor="+no_vendor.value;
    popUp(strURL);
}
</script>

</head>

<body>
<script type="text/javascript" language="JavaScript">
  //ini ni yang buat div tapi kita hidden... ocre....
  document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');

  </script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Create BASTP </th>
</tr></table></div>
<?
  if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search BASTP </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="create_ba.php" onSubmit="validasi('organisasi','','R','bulan','','R','tahun','','RisNum','vendor','','R','warna_plat','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Org</td>
      <td  class="puso">:</td>
      <td >
          <select name="organisasi" id="organisasi" onchange="orgOnChange()">
                <? foreach($mp_coics as $key => $value) { 
                    if($value == '2000'){
                        continue;
                    } else {?>
                    <option value="<? echo $value ?>" <? if($value==$user_org){echo("selected");} ?>><? echo $value." - ".$nama[$value] ?></option>
                <? }};?>
          </select><span style="color:red;font-size: 15px;">*</span>
          <input type="hidden" name="orgCode" id="orgCode" value="<?=$organisasi?>"></input>
      </td>
    </tr>
    <tr width="174">
      <td class="puso">No SPJ </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_shipment" name="no_shipment" value="<?=$no_shipment?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Distributor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="distributor" name="distributor"  value="<?=$distributor?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Periode Shipment </td>
      <td  class="puso">:</td>
      <td ><select name="bulan" id="bulan" required="true">>
        <option value="">---Pilih---</option>
        <? $fungsi->ex_bulan($bulan);?>
      </select> <input type="text" id="tahun" name="tahun"  value="<?=$tahun?>" maxlength="4" size="10"/>
      <span style="color:red;font-size: 15px;">*</span></td>
    </tr>

    <tr>
      <td  class="puso">Warna Plat </td>
      <td  class="puso">:</td>
      <td >
  <!--  <script type="text/javascript">
    function display_div(show){
       document.getElementById('HITAM').style.display = "none";
       document.getElementById(show).style.display = "block";
    }
    </script>
    <select name="warna_plat" id="warna_plat">
          <option value=""onClick="display_div('');">---Pilih---</option>
          <? $fungsi->ex_warna_plat($warna_plat);?>
        </select> -->

    <script type="text/javascript">
          function display_div(show){
          document.getElementById('HITAM').style.display = "none";
          document.getElementById(show).style.display = "block";
          }
        </script>

             <select name="warna_plat" id="warna_plat" required="true">>
             <option value="" selected="selected">----Pilih---</option>
             <option onClick="display_div('HITAM');">HITAM</option>
             <option onClick="display_div('HITAM');">KUNING</option>
       </select>
       <span style="color:red;font-size: 15px;">*</span></td>
    </tr>
  <tr>
  <td  class="puso">Termin Invoice </td>
    <td  class="puso">:</td>
  <td>
      <div id="HITAM" style="display:none;">
          <select name="termin" id="termin">
            <option value="">---Pilih---</option>
            <option value="1">Termin 1</option>
            <option value="2">Termin 2</option>
            <option value="3">Termin 3</option>
            <option value="4">All Termin</option>
           </select>*
      </div>
    </td>
  </TR>
  <tr>
    <td class="puso">
        Status Asal
    </td>
    <td class="puso">
      :
    </td>
    <td>
        <script type="text/javascript">
          function display_div(show){
          document.getElementById('HITAM').style.display = "none";
          document.getElementById(show).style.display = "block";
          }
        </script>

       <select name="status_asal" id="status_asal">
             <option value="" selected="selected">----Pilih---</option>
<!--             <option value="E-LOG">E-LOG</option>-->
             <option value="NON-ELOG">NON E-LOG</option>
       </select>
    </td>
  </tr>
  <tr>
      <td class="puso">
        Jenis SPJ
    </td>
    <td class="puso">
      :
    </td>
    <td>
        <select name="jenis_spj" id="jenis_spj" required="true">
             <option value="" selected="selected">----Pilih---</option>
             <option value="klaim">Klaim</option>
             <option value="nonklaim">Non Klaim</option>
             <option value="ds">Direct Selling</option>
       </select><span style="color:red;font-size: 15px;">*</span>
    </td>
  </tr>
   <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
  if($total>0){
$filhpajak='';
if ($warna_plat == "HITAM"){
//@liyantanto
$filhpajak="javascript:kodepajak(pjk1);";

?>
<!-- <form id="fsimpan" name="fsimpan" method="post" action="<?=$page;?>" onSubmit="validasi('pjk1','','R','pjk2','','R','pjk3','','R','pjk4','','R','tanggal_pjk','','R','no_rek','','R');cek_org('total');return document.hasil"> -->
    <form id="fsimpan" name="fsimpan" method="post" action="komentar.php" onSubmit="validasi('no_invoice_vendor','','R');cek_org('total');return document.hasil">               `
<?
} else {
?>
<form id="fsimpan" name="fsimpan" method="post" action="komentar.php" onSubmit="validasi('no_invoice_vendor','','R');cek_org('total');return document.hasil">

<? } ?>
  <div align="center">
  <table width="95%" align="center">
    <!-- <tr>
      <td width="13%"  class="puso">No Kwitansi Expeditur </td>
      <td width="1%"  class="puso">:</td>
      <td width="86%" ><div align="left">
        <input type="text" id="no_kwitansi_vendor" name="no_kwitansi_vendor" value="" size="50" maxlength="20"/>
      </div></td>
    </tr> -->
    <!-- <tr>
      <td width="13%"  class="puso">No Invoice Expeditur </td>
      <td width="1%"  class="puso">:</td>
      <td width="86%" ><div align="left">
        <input type="text" id="no_invoice_vendor" name="no_invoice_vendor" value="" size="50" maxlength="20"/> *boleh tidak diisi
      </div></td>
    </tr> -->
  </table>
  <? if ($warna_plat == "HITAM"){?>

  <table width="95%" align="center">
    <!-- <tr>
      <td width="13%"  class="puso">No Pajak Expeditur </td>
      <td width="1%"  class="puso">:</td>
      <td width="86%" ><div align="left">
        <input type="text" id="pjk1" name="pjk1" value="" size="10" maxlength="3" onBlur="javascript:IsNumeric(this,'3');<?=$filhpajak;?>"/>
        <strong>.</strong>
    <input type="text" id="pjk2" name="pjk2" value="" size="10" maxlength="3" onBlur="javascript:IsNumeric(this,'3')"/>
    <strong>-</strong>
    <input type="text" id="pjk3" name="pjk3" value="" size="10" maxlength="2" onBlur="javascript:IsNumeric(this,'2')"/>
    <strong>.</strong>
    <input type="text" id="pjk4" name="pjk4" value="" size="20" maxlength="8" onBlur="javascript:IsNumeric(this,'8')"/>
      </div></td>
    </tr> -->
            <? } ?>
    <table width="95%" align="center">

      <tr>
        <? if ($warna_plat == "HITAM"){?>
          <td width="13%"  class="puso">Tanggal </td>
        <? } else { ?>
      <td width="13%"  class="puso">Tanggal</td>
       <? }?>
      <td width="1%"  class="puso">:</td>
      <td width="86%" ><input name="tanggal_pjk" type="text" id="tanggal_pjk" <?=$hanyabaca?> value="<?=$tanggal_pjk?>" />
          <input name="btn_pjk" type="button" class="button" onClick="return showCalendar('tanggal_pjk');" value="..." />

    </td>
    <tr>
      <td width="13%" class="puso">User Approval</td>
      <td width="1%" class="puso">:</td>
      <td width="86%">
        <select name="id_user_approval" required>
          <option value="">--- Pilih ---</option>
          <?php foreach ($data_user_approval as $user): ?>
            <option value="<?=$user['ID']?>"><?=$user['NAMA_LENGKAP']?></option>
          <?php endforeach ?>
        </select>
        <!-- <input name="tanggal_pjk" type="text" id="tanggal_pjk" <?=$hanyabaca?> value="<?=$tanggal_pjk?>" />
          <input name="btn_pjk" type="button" class="button" onClick="return showCalendar('tanggal_pjk');" value="..." /> -->
      </td>
    </tr>

   <!--  <tr>
      <input name="no_vendor" id="no_vendor" type="hidden" value="<?=$no_vendor_v;?>" />
      <td  class="puso">No Rekening </td>
      <td  class="puso">:</td>
      <td > <input type="text" id="bvtyp" name="bvtyp"  value="" readonly="true" size="8"/> &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_bank" name="nama_bank"  value="" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="no_rek" name="no_rek"  value="" readonly="true"/>
      <input name="cari_rek" type="button" class="button" id="cari_rek" value="..." onClick="find_rek()"/></td>
    </tr> -->
    <!-- <tr>
      <td  class="puso">&nbsp;</td>
      <td  class="puso">&nbsp;</td>
    <td >
      <input type="text" id="cabang_bank" name="cabang_bank"  value="" readonly="true" size="50"/>
    </td>
    </tr> -->
  </table>

  </div>
  <div align="center">
  <table width="95%" align="center" class="adminlist">
  <tr>
  <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BASTP </span></th>
  </tr>
  </table>
  </div>
  <div align="center">
  <table width="95%" align="center"  class="table-responsive" id="myScrollTable">
  <thead >
    <tr class="quote">
    <td ><strong><input type="button" class="btn btn-success btn-sm" onClick="checkedAll('fsimpan');" class="form-control" style="font-size: 10px;" value="Check All"></strong></td>
                <td align="center"><strong >Org </strong></td>
    <td align="center"><strong >TGL. SPJ </strong></td>
    <td align="center"><strong >TGL. DATANG </strong></td>
    <td align="center"><strong >TGL. BONGKAR </strong></td>
    <td align="center"><strong >AREA. LT </strong></td>
    <td align="center"><strong >NO. SPJ </strong></td>
    <td align="center"><strong >SPJ MD</strong></td>
     <td align="center"><strong>NO. POL </strong></td>
     <td align="center"><strong>PLAT </strong></td>
     <td align="center"><strong>PRODUK</strong></td>
     <td align="center"><strong>DISTRIBUTOR</strong></td>
     <td align="center"><strong>K.KTG</strong></td>
     <td align="center"><strong>K.SMN</strong></td>

     <td align="center"><strong>TOTAL KTG RUSAK</strong></td>
     <td align="center"><strong>TOTAL KTG REZAK</strong></td>
     <td align="center"><strong>TOTAL SEMEN RUSAK</strong></td>
     <td align="center"><strong>TOTAL KLAIM KTG</strong></td>
     <td align="center"><strong>PDPKS</strong></td>
     <td align="center"><strong>TOTAL KLAIM SEMEN</strong></td>

     <td align="center"><strong>KWANTUM</strong></td>
     <td align="center"><strong>TARIF</strong></td>
     <td align="center"><strong>JUMLAH</strong></td>
     <td align="center"><strong>POD</strong></td>
     <!-- <td align="center"><strong>AKSI</strong></td> -->
     <td align="center"><strong>LAMPIRAN 1</strong></td>
     <td align="center"><strong>LAMPIRAN 2</strong></td>
     <td align="center"><strong>GEOFENCE POD</strong></td>
     <td align="center"><strong>USER</strong></td>
      </tr >
    </thead>
    <tbody >
  <?  for($i=0; $i<$total;$i++) {

    $b=$i+1;
    $rowke="rowke".$i;
    $idke="idke".$i;
    $appke=$id_v[$i];
    $urutke="urutke".$i;
    // $lampiran="lampiran".$i;
                $orgCom="orgke".$i;
                $org_fpajakkk=$com[$i];
    ?>
    <tr>
    <td align="center">
        <? if ($flag_POD[$i] == 'POD-ATOM' || $flag_POD[$i] == 'POD-EPOOOL' || $flag_POD[$i] == 'POD-FIOS') {
            if($READY_TO_INV[$i] == 1){
                 ?>
                <input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" checked/> <? echo $b; ?></td>
                 <?php
             }else{
                 ?>
                <input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" /> <? echo $b; ?></td>
                <?php
             }
           
            }else{
                if ($lampiran[$i] == '' || $lampiran2[$i] == '') {
                ?>
                <input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" disabled/> <? echo $b; ?></td>
                <?php
                }else{
                ?>
                <input name="<?=$idke;?>" id="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" /> <? echo $b; ?></td>
                <?php
                } 
            } ?>
                <td align="center"><? echo $com[$i]; ?><input name="<?=$orgCom;?>" id="<?=$orgCom;?>" type="hidden" value="<?=$com[$i];?>" /></td>
    <td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
    <td align="center"><? echo $tgl_datang_v[$i]; ?></td>
    <td align="center"><? echo $tgl_bongkar_v[$i]; ?></td>
    <td align="center"><? echo $sal_distrik_v[$i]; ?></td>
    <td align="center"><? echo $no_shipment_v[$i]; ?></td>
    <td align="center"><? echo $spjmd[$i]; ?></td>
    <td align="center"><? echo $no_pol_v[$i]; ?></td>
    <td align="center"><? echo $warna_plat_v[$i]; ?></td>
    <td align="center"><? echo $produk_v[$i]; ?></td>
    <td align="center"><? echo $sold_to_v[$i]." / ".$nama_sold_to_v[$i]; ?></td>
    <td align="center"><? echo number_format($qty_kantong_rusak_v[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($qty_semen_rusak_v[$i],0,",","."); ?></td>

    <td align="center"><? echo number_format($total_ktg_rusak[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($total_ktg_rezak[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($total_semen_rusak[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($total_klaim_ktg[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($pdpks[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($total_klaim_semen[$i],0,",","."); ?></td>

    <td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
    <td align="center"><? echo number_format($tarif_cost_v[$i],0,",","."); ?></td>
     <td align="center">
                    <? echo number_format($shp_cost_v[$i],0,",","."); ?>
                    <input name="jumlah<?=$appke;?>" type="hidden" id="jumlah<?=$appke;?>" size="10" value="<?=$shp_cost_v[$i];?>" readonly="true"/>
                    <input name="orgp<?=$appke;?>" type="hidden" id="orgp<?=$appke;?>" value="<?=$com[$i];?>" readonly="true"/>
                    <input name="orgSPJ" type="hidden" id="orgSPJ" value="<?=$com[$i];?>" readonly="true"/>
                </td>
    <td align="center"><? echo $flag_POD[$i]; ?></td>
    <td align="center"><? if ($lampiran[$i] == '') {
       ?>
       <a href="upload_spj.php?no_spj=<?=$no_shipment_v[$i]?>" class="btn btn-success btn-sm" style="font-size: 10px;" required>Upload File SPJ</a>
       <input type="hidden" id="lampiran" name="lampiran" value="0">
       <?php
    }else{
        ?>
       <a href="javascript:popUp('<?php echo $lampiran[$i] ?>')">Lampiran SPJ</a>
       <input type="hidden" id="lampiran" name="lampiran" value="1">
       <?php
    } ?></td>
     <td align="center"><? if ($lampiran2[$i] == '') {
       ?>
       <a href="upload_spj_ttd.php?no_spj=<?=$no_shipment_v[$i]?>" class="btn btn-primary btn-sm" style="font-size: 10px;" required>Upload File TTD SPJ</a>
       <input type="hidden" id="lampiran_ttd" name="lampiran_ttd" value="0">
       <?php
    }else{
        ?>
       <a href="javascript:popUp('<?php echo $lampiran2[$i] ?>')">Lampiran TTD SPJ</a>
       <input type="hidden" id="lampiran_ttd" name="lampiran_ttd" value="1">
       <?php
    } ?></td>
    <td align="center">
        <a href="javascript:popUp('https://www.google.com/search?q=<?php echo $GEOFENCE_POD[$i] ?>')"><?php echo $GEOFENCE_POD[$i] ?></a>
    </td>
    <td align="center"><? echo $KETERANGAN_POD[$i]; ?></td>
    </tr>
    <? } ?>
    </tbody>
    <tfoot>
    <tr class="quote">
    <td colspan="27" align="center">
    <input name="simpan" type="submit" class="button" id="simpan" value="Save" onclick="countChecked()"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <a href="create_ba.php" target="isi" class="button">Cancel</a>&nbsp;&nbsp;
    <input name="total" id="total" type="hidden" value="<?=$total;?>" />
    <input name="totalCheked" id="totalCheked" type="hidden" value="" />
    <input name="no_vendor" type="hidden" value="<?=$no_vendor_v;?>" />
    <input name="nama_vendor" type="hidden" value="<?=$nama_vendor_v;?>" />
    <input name="warna_plat" type="hidden" value="<?=$warna_plat;?>" />
    <input name="bulan" type="hidden" value="<?=$bulan;?>" />
    <input name="tahun" type="hidden" value="<?=$tahun;?>" />
    <input name="spt_cek" type="hidden" value="<?=$spt_cek;?>" />
    <input name="tgl_tremin" type="hidden" value="<?=$tgl_tremin;?>" />
    <input name="org_fpajak" type="hidden" value="<?=$org_fpajakkk;?>" />
    <input name="termin" type="hidden" value="<?=$termin;?>" />
		<input name="action" type="hidden" value="create_ba" />		 </td>
      </tr>
    </tfoot>
  </table>
  </div>
  <?
  }?>
<div align="center">
<?
echo $komen;

?></div>
</form>
<!--<div class="warning message">
<h3>Warning!</h3>
<p>Invoice PPL dengan tahun pengiriman SPJ <span style="color:white;">dibawah tahun 2017</span>,</br>
mohon tidak dibuat dalam satu inovice dengan pengiriman diatas <span style="color:white;">tahun 2017 !!!</span></p>
</div>-->
<p>&nbsp;</p>

<? if ($total> 10){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 600);
</script>
<? } ?>
</p>
</div>

<?if($show_ket!=''){?>
<div align="center" class="login">
<?
echo $show_ket;
?>
</div>
<?}?>

<? //include ('../include/ekor.php'); ?>
  <script language=javascript>
  //We write the table and the div to hide the content out, so older browsers won't see it
    obj=document.getElementById("tunggu_ya");
    obj.style.display = "none";
    obj_tampil=document.getElementById("halaman_tampil");
    obj_tampil.style.display = "inline";
    function orgOnChange(){
                    var selectedVal = document.getElementById("organisasi").value;
                    //alert ('Orrg selected : '+selectedVal);
                    document.getElementById("orgCode").value = selectedVal;
                    var kodeOrg = document.getElementById("orgCode").value; 
                    //alert ('Org code : '+kodeOrg);
    }
    
    function countChecked(){
        var countChecked = document.querySelectorAll('input[type="checkbox"]:checked').length;
        alert("Total "+countChecked);
        document.getElementById("totalCheked").value = countChecked;
    }
  </script>

</body>
</html>
