<?php
include ('../include/API.php');

function deleteso_smbr($datadelparam){

    // print_r("delete rollback so smbr (no pp) ".$datadelparam["csmsnumber"]);

    $curl = curl_init();
    // echo "Get Token </br>";
    curl_setopt_array($curl, array(
      //CURLOPT_URL => 'http://10.10.2.182:8080/smbr/md/api/auth/signin',
      CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/auth/signin',
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POSTFIELDS =>'{
      "username":"smbr-md",
        "password":"ciT@la-Plg"
    }
    }',
      CURLOPT_HTTPHEADER => array(
        'api-key: TissueBasaHCrlfUtf8@Spaces4Ln35Col44!!!',
        'Content-Type: application/json'
      ),
    ));
    
    $response = curl_exec($curl);
    
    curl_close($curl);
    $return_signin_smbr = json_decode($response, true);
            $token = $return_signin_smbr["token"];

    //////////////////////////////////////////////////////////

    $curl = curl_init();

    curl_setopt_array($curl, array(
    //CURLOPT_URL => 'http://10.10.2.182:8080/smbr/md/api/v1/so/cancel',
    CURLOPT_URL => 'http://10.10.101.182:8080/smbr/md/api/v1/so/cancel',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS =>'{
        "csmsNumber":"'.$datadelparam["csmsnumber"].'",
        "reasonRejectionCode":"Z3"
    }',
    CURLOPT_HTTPHEADER => array(
        'Authorization:Bearer '.$token,
        'Content-Type:application/json'
    ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    // echo $response;

    return $response;

}

function createso_smbr($dataparam){

    $curl = curl_init();
    curl_setopt_array($curl, array(
        //CURLOPT_URL => 'http://10.10.2.182:8080/smbr/md/api/v1/so/create',
        CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/v1/so/create',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "csmsNumber":"'.$dataparam["csmsnumber"].'",
        "sosigNumber":"'.$dataparam["sosignumber"].'",
        "plant":"'.$dataparam["plant"].'",
        "documentType":"'.$dataparam["documenttype"].'",
        "soldToParty":"'.ltrim(trim($dataparam["soldtoparty"]),'0').'",
        "shipToParty":"'.trim($dataparam["shiptoparty"]).'",
        "paymentMethod":"'.$dataparam["paymentmethod"].'",
        "paymentTerm":"'.$dataparam["paymentterm"].'",
        "requestDeliveryDate":"'.$dataparam["requestdeliverydate"].'",
        "shippingType":"'.$dataparam["shippingType"].'",
        "incoterm":"'.$dataparam["incoterm"].'",
        "transactionCategory":"'.$dataparam["transaction_category"].'",
        "businessPartner":"'.$dataparam["bussinies_partner"].'",
        "deliveryBlock":"'.$dataparam["deliveryBlock"].'",
        "subdistCode":"'.$dataparam["subdistCode"].'",
        "subdistName":"'.trim($dataparam["subdistName"]).'",
        "royaltyFlag": "'.$dataparam["royaltyFlag"].'",
        "items":[
            {
                "csmsNumber":"'.$dataparam["csmsnumber"].'",
                "itemNumber":"'.$dataparam["itemnumber"].'",
                "material":"'.$dataparam["material"].'",
                "salesUnit":"'.$dataparam["salesunit"].'",
                "quantity":"'.$dataparam["quantity"].'",
                "priceList":"'.$dataparam["pricelist"].'"
            }
        ]
    }',
        CURLOPT_HTTPHEADER => array(
        'Authorization:Bearer '.$dataparam["token"],
        'Content-Type:application/json'
        ),
    ));
    
    $response = curl_exec($curl);
    $info = curl_getinfo($curl);
    
    // echo "<br> INFO CURL SMBR =========> <br>";
    // echo "<pre>";
    // print_r($info);       
    // echo "</pre>";

    // echo "<br> return CURL SMBR =========> <br>";
    // echo "<pre>";
    // print_r(json_decode(curl_error($curl)));       
    // echo "</pre>";
    // curl_close($curl);

    // echo $response;  
    // echo '<pre>';    
    // print_r($response);

    $data_output = json_decode($response, true);

// print_r($data_output);
    // print_r($data_output["status"]);

    if($data_output["status"]=="200"){
        $pesan = $response;
        // $pesan[] = $datarequest;
        // print_r($data_output["data"]["items"]["salesUnit"]);
        // $returnsmbr["sonumber"]= $data_output["data"]["soNumber"];
        // $returnsmbr["salesunit"]= $data_output["data"]["salesUnit"];
        // $returnsmbr["subtotal"]= $data_output["data"]["subtotal"];
    }else{
        // print_r("data tidak ada");
        $pesan = "data tidak ada ( message : ".$data_output["message"]." errorMessage : ".$data_output["errorMessage"]." )";
    }

    // $returndata["daatarequest"]=json_decode($datarequest,true);
    // $retrundata["response"]=$data_output;
    // $returndata["token"]=$token;
    return $pesan;

}

switch ($action) {

case "create_pp":
    $user_id = $_SESSION['user_id'];
    if ($user_id == "") {
        ?>
        <SCRIPT LANGUAGE="JavaScript">
            <!--
            alert("You are not authorized to access this page.... \n Login Please...");
            //-->
        </SCRIPT>

        <a href="../index.php">Login....</a>
        <?php
    } else {
        //validasi
        foreach ($_POST as $key => $value) {
            echo "Key: $key; Value: $value <br>";
        } 
        $validasi_tgl_rdd = trim($_POST['tgl_kirim1']);
        $validasi_qty = trim($_POST['qty1']);
        $byg = trim($_POST['plantBayangan']);

        $validasi_plant = trim($_POST['plant']);

        if ($validasi_plant == '') {
            $validasi_plant = $byg;
         }
        $validasi_kode_distrik = trim($_POST['kota_tujuan']);
        $validasi_kode_material = trim($_POST['produk1']);
        $sub_validasi_kode_material=substr($validasi_kode_material,0,7);
        $validasi_incoterm = trim($_POST['nama_kirim']);
        $validasi_shipto = trim($_POST['shipto1']);        
        $validasi_org = trim($_POST['org']);        
        $validasi_soldto = trim($_POST['sold_to']); 
        $validasi_qty_front_end = trim($_POST['qty1']);                
        $validasi_tgl_front_end = trim($_POST['tgl_kirim1']);
        $tglleadtimeppqty=$validasi_tgl_front_end;        
        $validasi_route = trim($_POST['route']);                
        $validasi_kode_provinsi = trim($_POST['kode_prov1']);
        $brand = trim($_POST['brand']);
        if ($brand == '') {
            $brand = trim($_POST['jenis_kemasan']);
        }
                
        $tgl_harini_quary = date('d-m-Y');
        $getPrd = explode("-",$tgl_harini_quary);
        $getPeriode = $getPrd[1]."-".$getPrd[2];  
        // $getincomterms = "SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '$validasi_plant' AND INCOTERM_MD ='$validasi_incoterm' AND DEL = 0 AND ROWNUM <= 1";

        // $exc = oci_parse($conn, $getincomterms);
        // oci_execute($exc);
        // $incsource = oci_fetch_assoc($exc);

        // if (oci_num_rows($exc) != 0) {
        //     $validasi_incoterm = $incsource["INCOTERM_SOURCE"];
        // } else {
        //     $getincomterms2 = "SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '$validasi_plant' AND DEL = 0 AND ROWNUM <= 1";
        //     $exc1 = oci_parse($conn, $getincomterms2);
        //     oci_execute($exc1);
        //     $incsource2 = oci_fetch_assoc($exc1);
        //     if ($incsource2["INCOTERM_SOURCE"] != null ) {
        //         $validasi_incoterm = $incsource2["INCOTERM_SOURCE"];
        //     }
        // }

        // cek dulu apakah sudah di ammping quota bulanan
        // $selectData = "SELECT
        // QTY_BULANAN,QTY_HARIAN,QUARY 
        // FROM
        //     ZSD_TARGET_HEADER_SCM 
        // WHERE
        // BRAND = '$brand' AND DISTRIK = '$validasi_kode_distrik'  AND  MATERIAL = '$validasi_kode_material' AND INCOTERM = '$validasi_incoterm' AND PLANT = '$validasi_plant' AND PERIODE = '$getPeriode' AND FLAG_DEL ='X'  AND ROWNUM <= 1";
        
        // $exc=oci_parse($conn,$selectData);
        // oci_execute($exc);
        // $dataHarian=oci_fetch_assoc($exc);
        // $rowHarian=$dataHarian['QTY_HARIAN'];
        // $rowBln =$dataHarian['QTY_BULANAN'];

        // pengecekan apakah shipto yang di pilih shipto exception
        // $exCek = "SELECT
        // *
        // FROM
        //     EX_MAPPING_SHIPTO
        // WHERE
        // SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant' AND FLAG_DEL = 'X' AND ROWNUM <= 1";
        
        // $excEx=oci_parse($conn,$exCek);
        // oci_execute($excEx);
        // $dataShipto=oci_fetch_assoc($excEx);
        // $rowPlant=$dataShipto['PLANT'];
      
        // cek  plant yang di pilih apakah plant shipto exception 
        // $plantEx = 0 ;
        // if (count($rowPlant) > 0 ) {
        //     $validasi_plant = $byg;
        //     $plantEx = 1;
        //     $tmpQuary = "SELECT
        //     *
        //     FROM
        //         EX_MAPPING_SHIPTO
        //     WHERE
        //     SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant' AND FLAG_DEL = 'X' AND TEMP_KUOTA != '0' AND ROWNUM <= 1";
            
        //     $exTmp=oci_parse($conn,$tmpQuary);
        //     oci_execute($exTmp);
        //     $temp_reset=oci_fetch_assoc($exTmp);
            
            
        //     // di cek dulu apakah sudah waktunya reset kuota
        //     $tglReset = $temp_reset['TANGGAL_RESET'];
        //     $dtNow = date('d-m-Y');
            
        //     $qty_resetx = $temp_reset['TEMP_KUOTA'];
        //     if ($tglReset != '' && $tglReset <= $dtNow) {
        //         $sql= "UPDATE EX_MAPPING_SHIPTO SET TEMP_KUOTA = '$qty_resetx' WHERE SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant' AND FLAG_DEL = 'X' AND TEMP_KUOTA != '0' ";
        //         $query= oci_parse($conn, $sql);
        //         $result=oci_execute($query);
        //     }
            
            
        //     // di cek apakag kuota nya ada
        //     // $temp_qry=oci_fetch_assoc($exTmp);
        //     $temp_kuari = $temp_reset['TEMP_KUOTA'];
        //     $rw = $temp_kuari;


        // }else{
        //     if ($validasi_incoterm == 'FRC' || $validasi_incoterm == 'CIF' ) {
        //         $rw = $rowHarian;
        //     }else{
        //         $rw = $rowBln;
        //     }
        // }

        
        // if (isset($rw)) {
            // convert dulu JIKA ZAK
            $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE  MATNR = '{$validasi_kode_material}'  AND WERKS = '{$validasi_plant}' AND ROWNUM <= 1 ";
            $querymat = oci_parse($conn, $strmat);
            oci_execute($querymat);
            $rowmat = oci_fetch_array($querymat, OCI_ASSOC);
            if (count($rowmat) > 0 && $rowmat['MEINS'] == "ZAK") {
                $qty = ( intval($validasi_qty_front_end) * $rowmat['BERAT'] ) / 1000;
            } else {
                $qty =  $validasi_qty_front_end;
            }

            // if ( $validasi_incoterm == 'FRC' || $validasi_incoterm == 'CIF') {
            // get qty harian dulu
            // Pengecekan jika shipto yang di pilih adaalah shipto exception , apakah sudah di mappingkan atau belum 
        //     if (count($rowPlant) > 0) {
        //         $quary = 0;
        //         $qty_request = $qty;                
        //         $sisaQuary = $temp_kuari - $qty; 
        //         if ( $sisaQuary <= 0) {
        //         $qtyQry = 0;
        //         }else{
        //             $qtyQry = $sisaQuary;
        //         }
        //         $updateQuary = "UPDATE EX_MAPPING_SHIPTO SET TEMP_KUOTA = '$qtyQry' WHERE SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant'";
        //         $Quary= oci_parse($conn, $updateQuary);
        //         $resultQuary=oci_execute($Quary);
        //     }else{
        //             $quary = 0;
        //             $qty_request = $qty;
                    
        //             $sisaQuary = $dataHarian['QUARY'] - $qty; 

        //             if ( $sisaQuary <= 0) {
        //             $qtyQry = 0;
        //             }else{
        //                 $qtyQry = $sisaQuary;
        //             }
                    
        //             // get prioritas plant 
        //             $getPlant = array();
        //             $cekPlant = "SELECT DISTINCT
        //             scm.PLANT , 
        //             sys.NAME1,
        //             scm.PRIORITAS,
        //             scm.QUARY
                    
        //         FROM
        //             ZSD_TARGET_HEADER_SCM scm
        //             LEFT JOIN
        //             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.QUARY != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //             GROUP BY
        //             scm.PLANT,
        //             sys.NAME1,scm.PRIORITAS,scm.QUARY
        //             ORDER BY 
        //             scm.PRIORITAS
        //             ";

        //             $qplant = @oci_parse($conn, $cekPlant);
        //             @oci_execute($qplant);
        //             while ($raw = oci_fetch_assoc($qplant)) {
        //             $planton = $raw['PLANT'];
                    
        //             }
        //             $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QUARY = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = '$validasi_incoterm' AND PERIODE = '$getPeriode' AND BRAND = '$brand' AND FLAG_DEL ='X'";
        //             $Quary= oci_parse($conn, $updateQuary);
        //             $resultQuary=oci_execute($Quary);

        //             // cek jika quary sudah habis semua 
        //             $getPlant = array();
        //             $cekPlant = "SELECT DISTINCT
        //             scm.ID , 
        //             scm.QTY_HARIAN , 
        //             scm.PLANT , 
        //             sys.NAME1,
        //             scm.PRIORITAS,
        //             scm.QUARY
                    
        //         FROM
        //             ZSD_TARGET_HEADER_SCM scm
        //             LEFT JOIN
        //             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.PERIODE = '$getPeriode' AND scm.FLAG_DEL = 'X'
        //             GROUP BY
        //             scm.ID,
        //             scm.PLANT,
        //             sys.NAME1,scm.PRIORITAS,scm.QUARY, scm.QTY_HARIAN 
        //             ORDER BY 
        //             scm.PRIORITAS
        //             ";

        //             $cekNilaiQuary = @oci_parse($conn, $cekPlant);
        //             @oci_execute($cekNilaiQuary);
        //             while ($rew = oci_fetch_array($cekNilaiQuary)) {
        //             $jmlQuary[] = $rew['QUARY'];
        //             $datas[] = $rew;
        //             }
                    
        //             $totalQuary = array_sum($jmlQuary);

        //             if ($totalQuary == 0) {
        //                 foreach ($datas as $key ) {
        //                     // update quary ke qty awal 
        //                     $qtyHrn = $key['QTY_HARIAN'];
        //                     $idQuary = $key['ID'];

        //                     $sql= "UPDATE ZSD_TARGET_HEADER_SCM SET QUARY = '$qtyHrn' WHERE ID = '$idQuary' ";
        //                     $query= oci_parse($conn, $sql);
        //                     $result=oci_execute($query);
        //                 }
        //             }
        // }

        // }else{

        //     if (count($rowPlant) > 0) { // pengecekan mapping shipto exeption -> jika data nya sudah termapping 
        //         $quary = 0;
        //         $qty_request = $qty;
                
        //         $sisaQuary = $temp_kuari - $qty; 

        //         if ( $sisaQuary <= 0) {
        //         $qtyQry = 0;
        //         }else{
        //             $qtyQry = $sisaQuary;
        //         }

        //         $updateQuary = "UPDATE EX_MAPPING_SHIPTO SET TEMP_KUOTA = '$qtyQry' WHERE SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant'";
        //         $Quary= oci_parse($conn, $updateQuary);
        //         $resultQuary=oci_execute($Quary);


        //     }else{
        //                 // cek lagi apakah incoterm source nya FOT, FRC , CIF
        //                 // jika iya maka cek apakah ada mappingan di plant nya 
        //                 // kalau ada mengurangi quota nappingan quota 

        //                 $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$validasi_plant}' AND DEL=0";
        //                 $query = @oci_parse($conn, $strkdven);
        //                 @oci_execute($query);
        //                 $rowkdven = oci_fetch_array($query, OCI_ASSOC);
        //                 $incoterm_source = $rowkdven["INCOTERM_SOURCE"];
                        
                        
        //                 if ($incoterm_source == 'FRC' || $incoterm_source == 'FOT' || $incoterm_source == 'CIF') { // KONDISI UNTUK CEK 
        //                     $quary = 0;
        //                     $qty_request = $qty;
                                    
        //                             $sisaQuary = $dataHarian['QUARY'] - $qty; 
                                    
        //                             if ( $sisaQuary <= 0) {
        //                                 $qtyQry = 0;
        //                             }else{
        //                                 $qtyQry = $sisaQuary;
        //                             }
                                    
        //                             // get prioritas plant 
        //                             $getPlant = array();
        //                             $cekPlant = "SELECT DISTINCT
        //                             scm.PLANT , 
        //                             sys.NAME1,
        //                             scm.PRIORITAS,
        //                             scm.QUARY
                                    
        //                         FROM
        //                             ZSD_TARGET_HEADER_SCM scm
        //                             LEFT JOIN
        //                             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT                                                                                  
        //                             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = 'FRC' AND scm.QUARY != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //                             GROUP BY
        //                             scm.PLANT,
        //                             sys.NAME1,scm.PRIORITAS,scm.QUARY
        //                             ORDER BY 
        //                             scm.PRIORITAS
        //                             ";


        //                             $qplant = @oci_parse($conn, $cekPlant);
        //                             @oci_execute($qplant);
        //                             while ($raw = oci_fetch_assoc($qplant)) {
        //                             // cari prioritas terkecil 
        //                             // $angkaKecil = array();
        //                             $planton = $raw['PLANT'];
                                    
        //                             }
        //                             $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QUARY = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = 'FRC' AND PERIODE = '$getPeriode' AND BRAND = '$brand' AND FLAG_DEL ='X'";
        //                             $Quary= oci_parse($conn, $updateQuary);
        //                             $resultQuary=oci_execute($Quary);

        //                             // cek jika quary sudah habis semua 
        //                             $getPlant = array();
        //                             $cekPlant = "SELECT DISTINCT
        //                             scm.ID , 
        //                             scm.QTY_HARIAN , 
        //                             scm.PLANT , 
        //                             sys.NAME1,
        //                             scm.PRIORITAS,
        //                             scm.QUARY
                                    
        //                         FROM
        //                             ZSD_TARGET_HEADER_SCM scm
        //                             LEFT JOIN
        //                             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //                             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.PERIODE = '$getPeriode' AND scm.FLAG_DEL ='X'
        //                             GROUP BY
        //                             scm.ID,
        //                             scm.PLANT,
        //                             sys.NAME1,scm.PRIORITAS,scm.QUARY, scm.QTY_HARIAN 
        //                             ORDER BY 
        //                             scm.PRIORITAS
        //                             ";


        //                             $cekNilaiQuary = @oci_parse($conn, $cekPlant);
        //                             @oci_execute($cekNilaiQuary);
        //                             while ($rew = oci_fetch_array($cekNilaiQuary)) {
        //                             $jmlQuary[] = $rew['QUARY'];
        //                             $datas[] = $rew;
        //                             }
                                    
        //                             $totalQuary = array_sum($jmlQuary);

        //                             if ($totalQuary == 0) {
        //                                 foreach ($datas as $key ) {
        //                                     // update quary ke qty awal 
        //                                     $qtyHrn = $key['QTY_HARIAN'];
        //                                     $idQuary = $key['ID'];

        //                                     $sql= "UPDATE ZSD_TARGET_HEADER_SCM SET QUARY = '$qtyHrn' WHERE ID = '$idQuary' ";
        //                                     $query= oci_parse($conn, $sql);
        //                                     $result=oci_execute($query);
        //                                 }
        //                             }
        //                         }else{
        //                         // kondisi jika selain FRC dan CIF
        //                         // kondisinya ambil plant terserah jika habis maka tidak bisa terpakai kembali
        //                         // $qty = $validasi_qty_front_end;
        //                         $quary = 0;
        //                         $qty_request = $qty;
                                
        //                         $sisaQuary = $dataHarian['QTY_BULANAN'] - $qty; 

        //                         if ( $sisaQuary <= 0) {
        //                         $qtyQry = 0;
        //                         }else{
        //                             $qtyQry = $sisaQuary;
        //                         }
                                
        //                         // get prioritas plant 
        //                         $getPlant = array();
        //                         $cekPlant = "SELECT DISTINCT
        //                         scm.PLANT , 
        //                         sys.NAME1,
        //                         scm.PRIORITAS,
        //                         scm.QTY_BULANAN
                                
        //                     FROM
        //                         ZSD_TARGET_HEADER_SCM scm
        //                         LEFT JOIN
        //                         RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //                         WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.QTY_BULANAN != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //                         GROUP BY
        //                         scm.PLANT,
        //                         sys.NAME1,scm.PRIORITAS,scm.QTY_BULANAN
        //                         ORDER BY 
        //                         scm.PRIORITAS
        //                         ";

        //                         $qplant = @oci_parse($conn, $cekPlant);
        //                         @oci_execute($qplant);
        //                         // $raw = oci_fetch_assoc($qplant)
        //                         while ($raw = oci_fetch_assoc($qplant)) {
        //                         // cari prioritas terkecil 
        //                         // $angkaKecil = array();
        //                         $planton = $raw['PLANT'];
                                
        //                         }
        //                         $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QTY_BULANAN = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = '$validasi_incoterm' AND PERIODE = '$getPeriode'  FLAG_DEL ='X'";
        //                         $Quary= oci_parse($conn, $updateQuary);
        //                         $resultQuary=oci_execute($Quary);

        //                         }


        //     }
        // }

        // tambah new dist lama
        // if($sub_validasi_kode_material=='121-301'){
        //     $validasi_kode_distrik_getrdd=$validasi_kode_distrik;
        // }else{
        //     $validasi_kode_distrik_getrdd=$validasi_kode_provinsi;
        // }

        // $mysql="SELECT
        // STANDART_AREA
        // FROM
        //     (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$validasi_plant' and kota='$validasi_kode_distrik_getrdd' and kd_material='$sub_validasi_kode_material' and delete_mark='0' ORDER BY id desc)
        // WHERE
        //     rownum BETWEEN 0 AND 1";
        // $mysql_set=oci_parse($conn,$mysql);
        // oci_execute($mysql_set);
        // $row_leadtime=oci_fetch_assoc($mysql_set);
        // $leadtimesonya=$row_leadtime[STANDART_AREA];
        
        // if ($leadtimesonya != "" or $leadtimesonya != null){
        //     $leadtimeso=$leadtimesonya;
        // }else{
        //     $leadtimeso=0;
        // }
        
        /////////////////////////////////////
        // $mysqlperkapgudang="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
        // $mysql_setperkapgudang=oci_parse($conn,$mysqlperkapgudang);
        // oci_execute($mysql_setperkapgudang);
        // $row_configperkapgudang=oci_fetch_assoc($mysql_setperkapgudang);
        // $configperkapgudang=$row_configperkapgudang[CONFIG]; 
        // if($configperkapgudang!=null or $configperkapgudang!=''){
        //     $configperkapgudangfix = intval($configperkapgudang);
        // }else{
        //     $configperkapgudangfix = 100;
        // }
        
        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        //Perhitungan QTY order PP vs MDXL
                //intransit
            //     $sap = new SAPConnection();
            //     $sap->Connect("../include/sapclasses/logon_data.conf");
            //     if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            //     if ($sap->GetStatus() != SAPRFC_OK ) {
            //     echo $sap->PrintStatus();
            //     exit;
            //     }
            //     $fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL");
            //     if ($fce == false ) {
            //         $sap->PrintStatus();
            //         exit;
            //     }
            //                 $fce->XVKORG = $validasi_org;
            //                 $fce->XKUNNR2 = $validasi_shipto;
            //                 $fce->XBZIRK = $validasi_kode_distrik;
    
            //                     $validasi_hari_h = date('Ymd', strtotime($tglleadtimeppqty));
            //                     // $vdatuhigh = date('d.m.Y',strtotime());
            //                     $validasi_vdatulow = date('Ymd',strtotime($validasi_hari_h. ' -'.$leadtimeso.' day'));
                
            //                     $fce->LR_EDATU->row["SIGN"] = 'I';
            //                     $fce->LR_EDATU->row["OPTION"] = 'BT';
            //                     $fce->LR_EDATU->row["LOW"] = $validasi_vdatulow;
            //                     $fce->LR_EDATU->row["HIGH"] = $validasi_hari_h;
            //                     $fce->LR_EDATU->Append($fce->LR_EDATU->row);
    
            //             $fce->Call();
            //             if ($fce->GetStatus() == SAPRFC_OK ) {
            //                 $fce->RETURN_DATA->Reset();
            //                 $s=0;
            //                 while ($fce->RETURN_DATA->Next()){
            //                     $intransit += $fce->RETURN_DATA->row["RFMNG"];                                
            //                     $s++;
            //                 }
            //             }else {
            //                 $fce->PrintStatus();
            //             }
    
            //             $fce->Close();
            //             $sap->Close();
            // //////////////////////////////////////////////////////////////
            // $curl = curl_init();
            // curl_setopt_array($curl, array(
            // CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si='.$validasi_soldto.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
            // CURLOPT_RETURNTRANSFER => true,
            // CURLOPT_ENCODING => '',
            // CURLOPT_MAXREDIRS => 10,
            // CURLOPT_TIMEOUT => 0,
            // CURLOPT_FOLLOWLOCATION => true,
            // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            // CURLOPT_CUSTOMREQUEST => 'GET',
            // ));
            
            // $response = curl_exec($curl);
            
            // curl_close($curl);
            // $data_output_soldto = json_decode($response, true);
            // foreach ($data_output_soldto as $valuesoldto){
            //     $kode_soldto_si_mdxl_temp = $valuesoldto['kode_distributor_mdxl'];
            //     $kode_soldto_si_mdxl = sprintf("%'010s",$kode_soldto_si_mdxl_temp);
            // }
            // /////////////////////////////////////////////////////////////
            // $curl = curl_init();
            // curl_setopt_array($curl, array(
            //     //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',            
            // // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$validasi_shipto,
            // CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$validasi_shipto.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A',
            // CURLOPT_RETURNTRANSFER => true,
            // CURLOPT_ENCODING => '',
            // CURLOPT_MAXREDIRS => 10,
            // CURLOPT_TIMEOUT => 0,
            // CURLOPT_FOLLOWLOCATION => true,
            // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            // CURLOPT_CUSTOMREQUEST => 'GET',
            // ));
            
            // $response = curl_exec($curl);
            
            // curl_close($curl);
            // $data_output_shipto = json_decode($response, true);
            // $adashipto=false;
            // foreach ($data_output_shipto as $valueshipto) {
            // $kode_gudang_si_mdxl = $valueshipto['kode_gudang_mdxl'];
            // $adashipto=true;
            // }
            
            // if($adashipto){
            //     $mdxlshipto=$kode_gudang_si_mdxl;
            // }

            // if($mdxlshipto==NULL or $mdxlshipto==''){
            //     $mdxlshipto=$validasi_shipto;
            // }
            // ////////////////////////////////////////////////////////////
            // /////////////////////////////////////////////////////////////
            // $curl = curl_init();

            // curl_setopt_array($curl, array(
            // // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
            // CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp,
            // CURLOPT_RETURNTRANSFER => true,
            // CURLOPT_ENCODING => '',
            // CURLOPT_MAXREDIRS => 10,
            // CURLOPT_TIMEOUT => 0,
            // CURLOPT_FOLLOWLOCATION => true,
            // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            // CURLOPT_CUSTOMREQUEST => 'GET',
            // ));

            // $response = curl_exec($curl);

            // curl_close($curl);

            // $data_output = json_decode($response, true);
            // $adadimdxl = false;
            //                                                                 // WWW gae edit rumus intransit 5665
            //                                                                 $tglleadtimeppqty1 = date("01-m-Y", strtotime($tglleadtimeppqty));
            //                                                                 if(date("d", strtotime($tglleadtimeppqty)) < 15)
            //                                                                     $tglleadtimeppqty2 = date("t-m-Y", strtotime($tglleadtimeppqty));
            //                                                                 else
            //                                                                     $tglleadtimeppqty2 = date("t-m-Y", strtotime("$tglleadtimeppqty +1 month"));
                                                            
            //                                                                 $get_harian="SELECT DISTINCT zthn.PLANT, zls.STANDART_AREA FROM ZSD_TARGET_HARIAN_NEW zthn 
            //                                                                         JOIN ZMD_LEADTIME_SO zls ON zthn.PLANT = zls.PLANT AND zthn.DISTRIK = zls.KOTA AND zthn.TIPE = zls.KD_MATERIAL 
            //                                                                         WHERE zthn.ORG = '$validasi_org' 
            //                                                                         AND zthn.TANGGAL_TARGET BETWEEN TO_DATE('$tglleadtimeppqty1', 'DD-MM-YYYY HH24:MI:SS') AND TO_DATE('$tglleadtimeppqty2', 'DD-MM-YYYY HH24:MI:SS')
            //                                                                         AND zthn.DISTRIK = '$validasi_kode_distrik'
            //                                                                         AND zthn.TIPE = '$sub_validasi_kode_material'";
            //                                                                         // echo "$get_harian";
                                                                            
            //                                                                         $hasil_harian=oci_parse($conn,$get_harian);
            //                                                                         oci_execute($hasil_harian);
            //                                                                         $planthasil = array();
            //                                                                         $leadtimehasil = array();
            //                                                                         while($data=oci_fetch_assoc($hasil_harian)){
            //                                                                             array_push($planthasil, $data[PLANT]);
            //                                                                             array_push($leadtimehasil, $data[STANDART_AREA]);
            //                                                                         }
            //                                                                                                     $hari_h = date('Ymd');
            //                                                                                                     $sap = new SAPConnection();
            //                                                                                                     $sap->Connect("../include/sapclasses/logon_data.conf");
            //                                                                                                     if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            //                                                                                                     if ($sap->GetStatus() != SAPRFC_OK ) {
            //                                                                                                     echo $sap->PrintStatus();
            //                                                                                                     exit;
            //                                                                                                     }
            //                                                                                                     $rptrealavgin=0;
                                                            
            //                                                                                                     for ($w=0; $w < count($planthasil); $w++) {
            //                                                                                                     $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
            //                                                                                                     if ($fce == false ) {
            //                                                                                                     $sap->PrintStatus();
            //                                                                                                     exit;
            //                                                                                                     }
                                                                                                                
            //                                                                                                     //header entri
            //                                                                                                     $fce->X_VKORG = '7900';
            //                                                                                                     $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtimehasil[$w].' day'));
            //                                                                                                     $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
            //                                                                                                     $fce->X_WERKS = $planthasil[$w];
            //                                                                                                     $fce->X_KUNNR = $kode_soldto_si_mdxl_temp;
            //                                                                                                     $fce->X_STATUS = '70';
            //                                                                                                     $fce->X_BZIRK = $validasi_kode_distrik;
            //                                                                                                     ///////////////////////////
            //                                                                                                     $fce->LRI_VKORG->row["SIGN"] = 'I';
            //                                                                                                     $fce->LRI_VKORG->row["OPTION"] = 'EQ';
            //                                                                                                     $fce->LRI_VKORG->row["LOW"] = '7900';
            //                                                                                                     $fce->LRI_VKORG->row["HIGH"] = '';
            //                                                                                                     $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
            //                                                                                                     $fce->LRI_VKORG->row["SIGN"] = 'I';
            //                                                                                                     $fce->LRI_VKORG->row["OPTION"] = 'EQ';
            //                                                                                                     $fce->LRI_VKORG->row["LOW"] = '7000';
            //                                                                                                     $fce->LRI_VKORG->row["HIGH"] = '';
            //                                                                                                     $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        

            //                                                                                                     $fce->LR_KODE_DA->row["SIGN"] = 'I';
            //                                                                                                     $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
            //                                                                                                     $fce->LR_KODE_DA->row["LOW"] = $shipto;
            //                                                                                                     $fce->LR_KODE_DA->row["HIGH"] = '';
            //                                                                                                     $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);

            //                                                                                                     $fce->Call();
            //                                                                                                     $fce->ZDATA->Reset();
            //                                                                                                     if ($fce->GetStatus() == SAPRFC_OK) {
            //                                                                                                         while ( $fce->ZDATA->Next() ){
            //                                                                                                             $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
            //                                                                                                         }
            //                                                                                                     } else {
            //                                                                                                         $fce->PrintStatus();
            //                                                                                                         return;
            //                                                                                                     }
            //                                                                                                 }
                                                        
            //                                                                                                 ///////////////////////////////////////
                                                                                                         
            //                                                                                                      $fce->Close(); 
            //                                                                                                      $sap->Close();             
            //                                                                                                         $realisasi_shipment = $rptrealavgin;
                                                                                    //////////////////////////////////////////////////////////////

            // foreach ($data_output["items"] as $key => $value) {
            //     if (count($value["shipto_info"]) !== 0) {
            //         foreach ($value["shipto_info"] as $key2 => $value2) {
            //             if($value2['kode_shipto']==$mdxlshipto){                    
            //                 $mdxlvolume_stock_gudang=$value["volume_stock_gudang"];
            //                 $mdxlpenjualan_avg=$value["penjualan_avg"];
            //                 $mdxlsell_out=$value["sell_out"];
            //                 // new
            //                 $mdxlforcase_level_stok = $mdxlvolume_stock_gudang+$realisasi_shipment-($mdxlpenjualan_avg*$leadtimeso);
            //                 $mdxlkapasitas_gudang=$value["kapasitas_gudang"];
            //                 $mdxlkapasitas_gudang= ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
            //                 $mdxlorder_qty=$mdxlkapasitas_gudang-$mdxlforcase_level_stok;
            //                 $mdxlkapasitas_bongkar=$value["kapasitas_bongkar"];
            //                 $adadimdxl=true;
            //             }
            //         }
            //     }
            // }

            /////////////////////////////////////////////////
                // if($adadimdxl){
                //     $mdxlorder_qty = $mdxlorder_qty;
                //     $mdxlkapasitas_bongkar = $mdxlkapasitas_bongkar;
                // }else{
                //     //mapping gudang dan prov
                //     $parameterconfiggudang="GUDANG_".$validasi_kode_provinsi;
                //     $mysql_gudang="SELECT
                //                     CONFIG 
                //                     FROM (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                //                     WHERE
                //                     rownum BETWEEN 0 AND 1";
                //     $mysql_setgudang=oci_parse($conn,$mysql_gudang);
                //     oci_execute($mysql_setgudang);
                //     $row_setgudang=oci_fetch_assoc($mysql_setgudang);
                //     $configbongkar=$row_setgudang[CONFIG]; 
                //     if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null){
                //         $mysqlconbongkar="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";
            
                //         $mysql_configbongkar=oci_parse($conn,$mysqlconbongkar);
                //         oci_execute($mysql_configbongkar);
                //         $row_configbongkar=oci_fetch_assoc($mysql_configbongkar);
                //         $configbongkar=$row_configbongkar[CONFIG];
                //     }
             
                //     if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null){
                //         // $mdxlforcase_level_stok = 0-(0*$leadtimeso)+$intransit;
                //         $mdxlforcase_level_stok = 0;
                //         $mdxlkapasitas_gudang=$configbongkar;
                //         $mdxlorder_qty=$mdxlkapasitas_gudang-$mdxlforcase_level_stok;
                //         $mdxlkapasitas_bongkar=$configbongkar;
                //     }
                // }

                // $sqlgetpp="SELECT
                //             sum(or_trans_dtl.QTY_PP) as TOTPP
                //             FROM
                //                 OR_TRANS_hdr
                //             JOIN or_trans_dtl ON
                //                 or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
                //             WHERE
                //                 (or_trans_dtl.STATUS_LINE = 'OPEN'
                //                     OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                //                     OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                //                 AND or_trans_hdr.SOLD_TO = '$validasi_soldto'
                //                 AND or_trans_hdr.DELETE_MARK = '0'
                //                 AND or_trans_dtl.DELETE_MARK = '0'
                //                 AND or_trans_dtl.KODE_TUJUAN = '$validasi_kode_distrik'
                //                 AND or_trans_dtl.SHIP_TO = '$validasi_shipto'
                //                 AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$validasi_tgl_front_end'
                //                 AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'
                //                 AND or_trans_hdr.SO_TYPE != 'ZSE'
                                
                //                 ";

                //             $mysql_getpp=oci_parse($conn,$sqlgetpp);
                //             oci_execute($mysql_getpp);
                //             $row_getpp=oci_fetch_assoc($mysql_getpp);
                //             $getpp=$row_getpp[TOTPP];
            
                //             $final_qty = $mdxlorder_qty - $getpp;
                // if($final_qty>($mdxlkapasitas_bongkar-$getpp)){
                //     $max_order_qty_socc=$mdxlkapasitas_bongkar-$getpp;
                // }else{
                //     $max_order_qty_socc=$final_qty;
                // }
                // $validasi_qty_vinal_mdxl = floor($max_order_qty_socc);  

        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
 
        //so 3 layer multi ics FOT-FOT-FRC
        // if($validasi_incoterm=='FOT'){
        //     $mysql2 = "SELECT
        //     INCOTERM_SOURCE
        // FROM
        //     (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$validasi_plant' and INCOTERM_SOURCE='FRC' ORDER BY id_plant desc)
        // WHERE
        //     rownum BETWEEN 0 AND 1";
        // $mysql_set2=oci_parse($conn,$mysql2);
        // oci_execute($mysql_set2);
        // $row_config2=oci_fetch_assoc($mysql_set2);
        // $configso2=$row_config2[INCOTERM_SOURCE];
        //     if($configso2!='' or $configso2!=null){
        //         $validasi_incoterm=$configso2;
        //     }
        // }

        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // $validasi_lanjut=false;
        // if($sub_validasi_kode_material=='121-301' or $sub_validasi_kode_material=='121-302'){
        //     if($validasi_incoterm=='FRC' or $validasi_incoterm=='CIF'){
        //         if($leadtimeso!='' or $leadtimeso!=null){
        //             $validasi_tanggal_rddmin = date('Ymd', strtotime(' +'.$leadtimeso.' day'));

        //             /////////////////////////////////////////
        //             //rddmin+1
        //             $validasirddminplus1 = $validasi_kode_provinsi."_".$validasi_plant;
        //             $mysql_tglleadtimeplus="SELECT
        //                     CONFIG
        //             FROM
        //                 (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_$validasirddminplus1' and DELETE_MARK='0' ORDER BY ID desc)
        //             WHERE
        //                 rownum BETWEEN 0 AND 1";
        //             $mysql_settglleadtimeplus=oci_parse($conn,$mysql_tglleadtimeplus);
        //             oci_execute($mysql_settglleadtimeplus);
        //             $row_leadtimetglleadtimeplus=oci_fetch_assoc($mysql_settglleadtimeplus);
        //             $leadtimesotglleadtimeplus=$row_leadtimetglleadtimeplus[CONFIG]; 

        //             if($leadtimesotglleadtimeplus!='' or $leadtimesotglleadtimeplus!='0' or $leadtimesotglleadtimeplus!=null or $leadtimesotglleadtimeplus!=0){
        //                 $validasi_rddmin_plussatu=date('Ymd', strtotime($validasi_tanggal_rddmin. ' +'.$leadtimesotglleadtimeplus.' day'));
        //             }else{
        //                 $validasi_rddmin_plussatu = date('Ymd', strtotime(' +'.$leadtimeso.' day'));
        //             }

        //                 $validasi_tanggal_front_end = date('Ymd',strtotime($validasi_tgl_rdd));
        //                 if($validasi_incoterm=='FRC' && $validasi_route !='ZR0001'){
        //                     if(($validasi_tanggal_front_end>=$validasi_rddmin_plussatu)){
        //                         if($sub_validasi_kode_material=='121-301'){
        //                             if($validasi_qty_vinal_mdxl>=$validasi_qty_front_end){
        //                                 $validasi_lanjut = true;
        //                             }else{
        //                                 $show_ket .= "Gagal Create PP, quantum tidak sesuai validasi. mohon input PP kembali dan pastikan datanya sesuai<br>";                            
        //                             }
        //                         }else{
        //                             $validasi_lanjut = true;
        //                         }
        //                     }else{
        //                         $show_ket .= "Gagal Create PP, mohon input kembali dan pastikan datanya sesuai<br>";
        //                     }
        //                 }else{
        //                     $validasi_lanjut = true;
        //                 }
        //             }else{
        //                 $show_ket .= 'Gagal Create PP, mohon input kembali dan pastikan datanya sesuai. <br> tanggal RDD belum dimappingkan. <br>';
        //             }
        //         }else{
        //             $validasi_lanjut = true;
        //         }
        //     }else{
        //         $validasi_lanjut = true;
        //     }

                    $validasi_lanjut = true; 
                    if($validasi_lanjut){
                    $user_org_in = $_SESSION['user_org'];
                    $user_name_in = $_SESSION['user_name'];
                    $branch_plant_in = trim($_POST['branch_plant']);
                    $nama_plant_in = trim($_POST['nama_bplant']);
                    $jenis_kirim_in = trim($_POST['jenis_kirim']);
                    $nama_kirim_in = trim($_POST['nama_kirim']);
                    $sold_to_in = trim($_POST['shipto1']);
                    $sold_to_in = $fungsi->sapcode($sold_to_in);
                    $nama_sold_to_in = trim($_POST['nama_shipto1']);
                    $route_in = trim($_POST['route']);
                    $plant = $validasi_plant;
                    $cekNamaPlant = "SELECT
                            sys.WERKS,
                            sys.NAME1 
                            FROM
                            RFC_Z_ZAPP_SELECT_SYSPLAN sys 
                            WHERE
                            WERKS = '$plant'
                            GROUP BY
                            sys.WERKS,
                            sys.NAME1 ";

                            $qnamaplant = oci_parse($conn, $cekNamaPlant);
                            oci_execute($qnamaplant);
                            $nmPlant = oci_fetch_assoc($qnamaplant);
                            $nama_plant = $nmPlant['NAME1'];

                    // pengecekan apakah dia org 7900 atau bukan, di ambil dari plant
                    $cekOrg = substr("{$plant}",0,2);
                    $cekOrg2 = substr("{$plant}",0,1);
                    if ($cekOrg2 == "I") {
                        if ($cekOrg == "IL") {
                            $user_org_in = 'ID50';
                        } else {
                            $user_org_in = 'PTSC';
                        }   
                    } else if ($cekOrg2 == "1") {
                        $user_org_in = '1000';
                    } else {
                        if ($cekOrg == '79' || $cekOrg == 79) {
                            $user_org_in = '7900';
                        } else{
                            $user_org_in = '7000';
                        } 
                    }
                    // ========================= //
                    $so_type = trim($_POST['so_type']);
                    $j_kemasan = trim($_POST['jenis_kemasan']);
                    
                    if($so_type =="ZFC"){
                    $cara_bayar_in = "CASH";       
                    }else{
                    $cara_bayar_in = trim($_POST['cara_bayar']);     
                    }
                    
                    $nama_so_type = trim($_POST['nama_so_type']);
                    $ket = 'ics_pp';
                    $kd_kapal = trim($_POST['kd_kapal']);
                    $nm_kapal = trim($_POST['nama_kapal']);
                    $nama_kapal = $nm_kapal;
                    $posnr = trim($_POST['com_posnr']);
                    $com_sisa = trim($_POST['com_sisa']);
                    $nokontrakhead = trim($_POST['com_kontrak']);
                    $no_pp_in = $fungsi->or_new_pp_number($conn);
                    $lelang_isi = trim($_POST['lelangFLAG']);
                    $tipeCreatePP_isi = trim($_POST['tipeCreatePP']);
                    $top = 'ZT90';
                    $nama_top = 'Overdue 90 days';

                    $pricelist = $_POST['pricelist'];
                    $nama_pricelist = $_POST['nama_pricelist'];
                    $valperlcnum = trim($_POST['perslcnum']);
                    $lcnum = $_POST['lcnum'];
                    $lcnum = $fungsi->sapcode($lcnum);

                    $field_names = array('SOLD_TO', 'NAMA_SOLD_TO', 'NO_PP', 'TGL_PP', 'PLANT_ASAL', 'NAMA_PLANT', 'SO_TYPE', 'NAMA_SO_TYPE', 'CARA_BAYAR', 'INCOTERM', 'NAMA_INCOTERM', 'BPLANT', 'STATUS', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'ROUTE', 'ORG', 'NOTE', 'FLAG_LELANG', 'TIPEPP', 'TERM_PAYMENT', 'NAMA_TOP', 'PRICELIST', 'NAMA_PRICELIST');
                    $field_data = array("$sold_to_in", "$nama_sold_to_in", "$no_pp_in", "SYSDATE", "$plant", "$nama_plant", "$so_type", "$nama_so_type", "$cara_bayar_in", "$jenis_kirim_in", "$nama_kirim_in", "$branch_plant_in", "OPEN", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "0", "$route_in", "$user_org_in", "$ket", "$lelang_isi", "$tipeCreatePP_isi", "$top", "$nama_top", "$pricelist", "$nama_pricelist");
                    $tablename = "OR_TRANS_HDR";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);
                    $show_ket .= "Order Reservation Success Made with No. $no_pp_in <br>";

                    $sampai = $_POST['jumlah'];
                    for ($k = 1; $k <= $sampai; $k++) {
                        $shipto = "shipto" . $k;
                        $nama_shipto = "nama_shipto" . $k;
                        $alamat = "alamat" . $k;
                        $kode_distrik = "kode_distrik" . $k;
                        $nama_distrik = "nama_distrik" . $k;
                        $kode_prov = "kode_prov" . $k;
                        $nama_prov = "nama_prov" . $k;
                        $produk = "produk" . $k;
                        $nama_produk = "nama_produk" . $k;
                        $qty = "qty" . $k;
                        $uom = "uom" . $k;
                        $com_kontrak = "com_kontrak" . $k;
                        $noppref_inc = "refpp" . $k;
                        $tgl_leadtime = "tgl_kirim" . $k;

                        if (isset($_POST[$shipto])) {
                            $shipto_in = trim($_POST[$shipto]);
                            $nama_shipto_in = trim($_POST[$nama_shipto]);
                            $alamat_in = trim($_POST[$alamat]);
                            $produk_in = trim($_POST[$produk]);
                            $kode_distrik_in = trim($_POST[$kode_distrik]);
                            $nama_distrik_in = trim($_POST[$nama_distrik]);
                            $kode_prov_in = trim($_POST[$kode_prov]);
                            $nama_prov_in = trim($_POST[$nama_prov]);
                            $nama_produk_in = trim($_POST[$nama_produk]);
                            $leadtime1 = trim($_POST[$tgl_leadtime]);
                            if($leadtime1=='' or $leadtime1==null){
                                $leadtime1 = date('d-m-Y');
                            }

                            $tgl_leadtimeval=$leadtime1;
                            $qty_in = trim($_POST[$qty]);
                            if($j_kemasan == 'M3 PALLET1'){   // konversi per pallet = 1.6 M3
                            $qty_in = $qty_in * 1.6;    
                            }
                            
                            $uom_in = trim($_POST[$uom]);
                            $kontrak = $_POST[$com_kontrak];
                            $noppref_in = trim($_POST[$noppref_inc]);

                            $jenis_kirim_in_config = $jenis_kirim_in;
                            //pengecekan incoterm multi ics
                            if($jenis_kirim_in_config=='FOT'){
                                    $mysql2 = "SELECT
                                    INCOTERM_SOURCE
                                FROM
                                    (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$plant' ORDER BY id_plant desc)
                                WHERE
                                    rownum BETWEEN 0 AND 1";
                                $mysql_set2=oci_parse($conn,$mysql2);
                                oci_execute($mysql_set2);
                                $row_config2=oci_fetch_assoc($mysql_set2);
                                $configso2=$row_config2[INCOTERM_SOURCE];
                            };

                            if ($configso2 != 0 or $configso2 != "" or $configso2 != null){
                                $jenis_kirim_in_config= $configso2;
                            }else{
                                $jenis_kirim_in_config = $jenis_kirim_in;
                            }

                            $produk_in_getleadtime=substr(trim($produk_in),0,7);
                            $plant=trim($plant);
                            $kode_distrik_in=trim($kode_distrik_in);
                            $getdatanyaleadtime='0';
                            $tgl_kirim_in = trim($_POST['tgl_kirim1']);
                            $tgl_terima = trim($_POST['tgl_terima']);

                            // if($produk_in_getleadtime=='121-301' && ($jenis_kirim_in_config=='FRC' or $jenis_kirim_in_config=='CIF')){
                            //     $mysql="SELECT
                            //     STANDART_AREA
                            //     FROM
                            //         (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in_getleadtime' and delete_mark='0' ORDER BY id desc)
                            //     WHERE
                            //         rownum BETWEEN 0 AND 1";
                            //                             $mysql_set=oci_parse($conn,$mysql);
                            //                             oci_execute($mysql_set);
                            //                             $row_leadtime=oci_fetch_assoc($mysql_set);
                            //                             $getdatanyaleadtime=$row_leadtime[STANDART_AREA]; 
                            // }else if($produk_in_getleadtime=='121-302' && ($jenis_kirim_in_config=='FRC' or $jenis_kirim_in_config=='CIF')){
                            //     $mysql="SELECT
                            //     STANDART_AREA
                            //     FROM
                            //         (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_prov_in' and kd_material='$produk_in_getleadtime' and delete_mark='0' ORDER BY id desc)
                            //     WHERE
                            //         rownum BETWEEN 0 AND 1";
                            //                             $mysql_set=oci_parse($conn,$mysql);
                            //                             oci_execute($mysql_set);
                            //                             $row_leadtime=oci_fetch_assoc($mysql_set);
                            //                             $getdatanyaleadtime=$row_leadtime[STANDART_AREA]; 
                            // }else{
                            //     $getdatanyaleadtime='0';
                            // }
                            
                            // if (($getdatanyaleadtime!="" or $getdatanyaleadtime!=null) && ($jenis_kirim_in_config=='FRC' or $jenis_kirim_in_config=='CIF')){
                            //     $tgl_kirim_in = date('d-m-Y',strtotime($tgl_leadtimeval. ' -'.$getdatanyaleadtime.' day'));
                            // }else{
                            //     $tgl_kirim_in = date('d-m-Y');
                            // }

                            $field_names = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'TGL_KIRIM_PP', 'TGL_TERIMA', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NAMA_KAPAL', 'KD_PROV', 'NM_PROV', 'UOM', 'NO_PPREF', 'NO_KONTRAK', 'NO_KONTRAK_POSNR', 'PLANT', 'NM_PLANT',"TGL_LEADTIME");
                            $field_data = array("$no_pp_in", "$produk_in", "$nama_produk_in", "$qty_in", "instgl_$tgl_kirim_in", "instgl_$tgl_terima", "$shipto_in", "$nama_shipto_in", "$alamat_in", "SYSDATE", "$user_name_in", "0", "$kode_distrik_in", "$nama_distrik_in", "OPEN", "$k", "$nama_kapal", "$kode_prov_in", "$nama_prov_in", "$uom_in", "$noppref_in", "$kontrak", "$posnr", "$plant", "$nama_plant","instgl_$tgl_leadtimeval");
                            $tablename = "OR_TRANS_DTL";
                            $fungsi->insert($conn, $field_names, $field_data, $tablename);
                            $show_ket .= "Item $produk_in with Qty $qty_in Success<br>";
                        }
                    }
                }


        // }else{
        //     $show_ket .= "Plant $validasi_plant masih belum termapping quota";
        // }

        }
    $habis = $_POST['lasthalaman'];
    break;


//============================================================================================================================
    case "buatso":
        $show_ket = "";
        $pesan_telegram = "";
        //data header so
        $user_name_in = $_SESSION['user_name'];
        $idh = $_POST['idh'];
        $iddtl = $_POST['iddtl'];
        $so_type = $_POST['so_type'];
        $nama_so_type = $_POST['nama_so_type'];
        $sales_org = $_POST['org']; //$_POST['org'];
        $channelnew = $_POST['channels'];
        if ($so_type == 'ZEX') {
            $distr_chan = "30";
        } elseif ($so_type == 'ZPR') {
            if ($sales_org == '2000' || $sales_org == '7000' || $sales_org == '5000')
                $distr_chan = "10";
            else if ($sales_org == '6000')
                $distr_chan = "10";
            else
                $distr_chan = "50";
        } else {
            $distr_chan = "10";
        }
        $division = "00";
        $dlv_block = "";
        $soldto = $_POST['distr_id'];
        $soldto = $fungsi->sapcode($soldto);
        $distr_chancondi = trim($fungsi->findOneByOne($conn, "TB_USER_BOOKING", "DISTRIBUTOR_ID", $soldto, "CHANNEL"));
        if ($distr_chancondi != '') {
            $distr_chan = $distr_chancondi;
        }
        if ($channelnew != '') {
            $distr_chan = $channelnew;
        }
        $no_pp = $_POST['no_pp'];
        $tgl_pp = $_POST['tgl_pp'];
        list($day, $month, $year) = split("-", $tgl_pp);
        $tgl_pp = $year . $month . $day;
        $tgl_ppPO = $year . "-" . $month . "-" . $day . " 00:00:00";
        //$tanggal=gmdate("Ymd",time()+60*60*7);
        $top = $_POST['top'];
        $nama_top = $_POST['nama_top'];
        $reason = $_POST['reason'];
        $nama_reason = $_POST['nama_reason'];
        $plant = $_POST['plant'];
        $nama_plant = trim($_POST['nama_plant']);
        $nm_kapal = $_POST['nm_kapal'];
        $incoterm1 = $_POST['incoterm'];
        $incoterm2 = $_POST['nama_incoterm'];
        $route =  $_POST['route'];
        // echo $route;
        $pricelist = $_POST['pricelist'];
        $nama_pricelist = $_POST['nama_pricelist'];
        $lcnum = $_POST['lcnum'];
        $lcnum = $fungsi->sapcode($lcnum);
        $ship_cond = $fungsi->findOneByOne($conn, "TB_ROUTE", "ROUTE", $route, "SHIP_COND");
        $ship_condition = $_POST['ship_condition'];
        $soldtocek = $_POST['distr_id'];
        $ket = "";

        $sampai1 = $_POST['jumlah'];
        for ($j = 0; $j <= $sampai1; $j++) {
            $idke = "idke" . $j;
            $pesan_telegram .= "PP $no_pp";

            if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                $kontrakh = $_POST['com_kontrak' . $j];
            }
        }

        // KONDISI ICS
        if ($sales_org == "PTSC" || $sales_org == "ID50" || $sales_org == "1000") {
            for ($j = 0; $j <= $sampai1 - 1; $j++) {
                $tgl_terima_leadtime = $_POST['com_tgl_terima' . $j];
                $shiptotocek = $_POST['com_shipto' . $j];

                $tgl_trmPO = $_POST['com_tgl_terima' . $j];
                list($day1, $month1, $year1) = split("-", $tgl_trmPO);
                $tgl_trm_po = $year1 . $month1 . $day1;
            }

            $strorder = "SELECT * FROM ZMD_MAPPING_ORDER WHERE ORDER_TYPE_MD = '{$so_type}' AND COMPANY_OPCO = '{$sales_org}' AND DEL = 0";
            $queryorder = oci_parse($conn, $strorder);
            oci_execute($queryorder);
            $roworder = oci_fetch_array($queryorder, OCI_ASSOC);

            if ($sales_org == "PTSC") {
                $sales_organization = "SCBD";
            } else if ($sales_org == "ID50") {
                $sales_organization = "ID11";
            }

            $rddsbi = date('Y-m-d', strtotime($tgl_terima_leadtime));
            
            if(empty($ship_condition)){
                        
                $shipcond_sbi = "";
                               
                if ($incoterm1 == "FOT") {
                    $shipcond_sbi = "PK";
                } else if ($incoterm1 == "FRC") {
                    $shipcond_sbi = "D0";
                } else if ($incoterm1 == "CNF") {
                    $shipcond_sbi = "DV";
                } else {
                    $shipcond_sbi = "";
                }
            
            }else{
                $shipcond_sbi = $ship_condition;
            }

            $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}'  AND DEL = 0 AND ORG_OPCO = '{$sales_org}'";
            $queryshipto = oci_parse($conn, $strshipto);
            oci_execute($queryshipto);
            $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
            if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}'  AND DEL = 0";
                $queryshipto = oci_parse($conn, $strshipto);
                oci_execute($queryshipto);
                $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
            }
            
            $soldtosbi = $rowshipto['SOLD_TO_OPCO'];
            $shipto = $rowshipto['SHIP_TO_OPCO'];
            
            $soldtosmbr = $rowshipto['SOLD_TO_OPCO'];
            $shiptosmbr = $rowshipto['SHIP_TO_OPCO'];

            $strtop = "SELECT * FROM ZMD_MAPPING_TOP WHERE TOP_SMI = '{$top}' AND (COM_SB = '{$sales_org}' or COM_SB = 'ALL')  AND DEL = 0 order by COM_SB DESC";
            $querytop = oci_parse($conn, $strtop);
            oci_execute($querytop);
            $rowtop = oci_fetch_array($querytop, OCI_ASSOC);
            if (ISSET($rowtop['TOP_SB'])) {
                $topsbi = $rowtop['TOP_SB'];
            }else{
                $topsbi = null;
            }

            $soldtoparty = ltrim($soldtocek, '0');
            $firstDigit = substr((string)$soldtoparty, 0, 1);
            $com2 = $firstDigit . '000';

            $Tmaterial = array();
            $Tmaterial['MaterialCode'] = array();
            $Tmaterial['PlantCode'] = array();
            $Tmaterial['Qty'] = array();
            $Tmaterial['QtyUnit'] = array();
            $Tmaterial['ScheduleLine'] = array();
            $Tmaterial['RequestDeliveryDate'] = array();
            $Tmaterial['RequestDeliveryQty'] = array();
            $Dmaterial = array();
            $sampai = $_POST['jumlah'];
            for ($j = 0; $j <= $sampai - 1; $j++) {
                $material = $_POST['com_produk' . $j];
                $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE VKORG = '{$sales_org}' AND MATNR = '{$material}'  AND WERKS = '{$soldtoparty}'";
                $querymat = oci_parse($conn, $strmat);
                oci_execute($querymat);
                $rowmat = oci_fetch_array($querymat, OCI_ASSOC);

                $row_sales_unit = $rowmat['MEINS'];
                if($rowtop_seles['MEINS'] == "TO"){
                    $row_sales_unit = "TON";
                }elseif($rowtop_seles['MEINS'] == "BAG"){
                    $row_sales_unit = "BGB";
                }

                if (count($rowmat) > 0 && $rowmat['MEINS'] == "ZAK") {
                    $qty = ( intval($_POST['com_qty' . $j]) * $rowmat['BERAT'] ) / 1000;
                    $qtyx = ( intval($_POST['com_qtyx' . $j]) * $rowmat['BERAT'] ) / 1000;
                } else {
                    $qty = $_POST['com_qty' . $j];
                    $qtyx = $_POST['com_qtyx' . $j];
                }



                $shiptotocek = $_POST['com_shipto' . $j];
                $item_numline = sprintf("%04d", $j + 1);
                $tanggall = $_POST['com_tgl_kirim' . $j];
                list($day, $month, $year) = split("-", $tanggall);
                $tgl_kirim = $year . "-" . $month . "-" . $day;

                $tgl_fdate_socc = $year . $month . $day;

                $strmpoics = "SELECT * FROM MAPPING_PO_ICS WHERE COMPANY_CODE = '{$sales_org}' AND SHIPPING_POINT = '{$plant}' AND SOLD_TO_PARTY = '{$soldtoparty}' AND INCOTERM = '{$incoterm1}' AND FLAG_DEL = 'X' ";
            
                $querympoics = oci_parse($conn, $strmpoics);  
                oci_execute($querympoics); 
                $rowmpoics = oci_fetch_array($querympoics, OCI_ASSOC);    
                $plant_tujuan = $rowmpoics["SOLD_TO_PARTY"];

                $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$com2}' AND PLANT_MD = '{$plant_tujuan}' AND ORG_OPCO = '{$sales_org}' AND PLANT_OPCO = '{$plant}' AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                $querymaterial = oci_parse($conn, $strmaterial);
                oci_execute($querymaterial);
                $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);

                if ($sales_org == "PTSC" || $sales_org == "PTSC") {
                    $materialsbi = $rowmaterial["KD_MATERIAL_OPCO"];
    
                    $Tmaterial['MaterialCode'] = $materialsbi;
                    $Tmaterial['PlantCode'] = $plant;
                    $Tmaterial['Qty'] = $qty;
                    $Tmaterial['QtyUnit'] = "TO";
                    $Tmaterial['ScheduleLine'] = $item_numline;
                    $Tmaterial['RequestDeliveryDate'] = $tgl_kirim;
                    $Tmaterial['RequestDeliveryQty'] = $qtyx;
                    $Dmaterial[] = $Tmaterial;
                }else {
                    $materialsmbr = $rowmaterial["KD_MATERIAL_OPCO"];
                }

            }

            if ($sales_org == "PTSC" || $sales_org == "ID50") { // CREATE SO SBI & SBA
                
                $url = 'https://integrasi-api.sig.id/apimd/createsalesorder/dev'; // Prod Synxchrox
                // $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/CreateSalesOrder'; // Prod Synxchrox
                
                $data = array('Token' => 'aSsMx7GV0HFGzlufM4DH',
                    'SystemID' => 'QASSO', //'PASSO',
                    'Data' => array('SalesDocumentType' => $roworder["ORDER_TYPE_OPCO"],
                        'SalesOrganization' => $sales_organization,
                        'DistributionChannel' => 'DB',
                        'Division' => 'CM',
                        'SalesDocumentDate' => $tgl_pp,
                        'RequestDeliveryHeader' => $rddsbi,
                        'ShippingCondition' => $shipcond_sbi, //shipcond
                        'ShippingType' => 'G4',
                        'SoldToCode' => $soldtosbi,
                        'ShipToCode' => $shipto,
                        'MDReferenceSONumber' => $no_pp, ///// SO reference ????
                        'MDPurchaseOrderNumber' => $no_pp, ///// PO MD reference ????
                        'MDPurchaseOrderItem' => '00010',
                        'PurchaseOrderNumber' => $no_pp, ///// PO reference ????
                        'PurchaseOrderDate' => $tgl_ppPO,
                        'PaymentTerms' => $topsbi,
                        'Items' => $Dmaterial
                    )
                );
                
                $options = array(
                    'http' => array(
                        'header' => "Content-type: application/json\r\n",
                        'method' => 'POST',
                        'content' => json_encode($data),
                    )
                );
    
                $context = stream_context_create($options);
                $result = file_get_contents($url, false, $context); 
                
                $response = json_decode($result);   
                $data_response_sbi['datasbi'] = $response;  
                
                $status = $response->Status;
                $Message = $response->Message;
    
                $Message_detail = $response->MessageDetail;
                $param_send = json_encode($data);
                $param_return = json_encode($response);
    
                $field_names = array(
                    'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
                );
                $field_data = array(
                    "$param_send", "$param_return", "$user_name_in", "$no_pp", "$Message", "$Message_detail", "SYSDATE"
                );
                $tablename = "ZMD_LOG_SBI";
                $sukses = $fungsi->insert($conn, $field_names, $field_data, $tablename);
                
                $nomorso = $response->Data->SAPSalesOrderNumber;
                $tanggalSO = $response->Data->SAPCreatedDateStr;
                
                if ($status != 1) {
                    // $next = 'N';
                    $show_ket .= "SO OPCO = " . $Message_detail;
                    $show_ket .= '<br>';
                } else {
                    $show_ket .= "SO OPCO = Sales Order has been made with a number : " . $nomorso;
                    $show_ket .= '<br>';
                }
            }else { // CREATE SO SMBR

                $curl = curl_init();
                // echo "Get Token </br>";
                curl_setopt_array($curl, array(
                    //CURLOPT_URL => 'http://10.10.2.182:8080/smbr/md/api/auth/signin',
                    CURLOPT_URL => 'http://10.10.101.182:8080/smbr/md/api/auth/signin',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>'{
                    "username":"smbr-md",
                    "password":"ciT@la-Plg"
                }
                }',
                    CURLOPT_HTTPHEADER => array(
                    'api-key: TissueBasaHCrlfUtf8@Spaces4Ln35Col44!!!',
                    'Content-Type: application/json'
                    ),
                ));
                
                $response = curl_exec($curl);
                
                curl_close($curl);
                $return_signin_smbr = json_decode($response, true);
                $token = $return_signin_smbr["token"];

                if($incoterm1=="FOT"){
                    $incotermsmbr="AS";
                }else{
                    $incotermsmbr="FRC";
                }

                $dataparam= array();
                $dataparam["csmsnumber"]=$no_pp;
                $dataparam["plant"]=$plant;
                $dataparam["sosignumber"]="";
                $dataparam["documenttype"]="ZOOL";
                $dataparam["soldtoparty"]=$soldtosmbr;
                $dataparam["shiptoparty"]=$shiptosmbr;
                $dataparam["paymentmethod"]="104";
                $dataparam["paymentterm"]=$topsbi;
                $dataparam["requestdeliverydate"]=date('Y-m-d', strtotime($tgl_fdate_socc));
                $dataparam["shippingType"]="01";
                $dataparam["incoterm"]=$incotermsmbr;
                $dataparam["itemnumber"]="000010";
                $dataparam["material"]=$materialsmbr;
                // $dataparam["salesunit"]="ZAK";
                $dataparam["salesunit"]=$row_sales_unit;//$rowtop_seles['MEINS'];
                $dataparam["quantity"]=$_POST['com_qtyx0'];//$qtyx;

                $dataparam["pricelist"]="17";
                $dataparam["bussinies_partner"]='1000';
                $dataparam["transaction_category"]="ICS";

                $deliveriBrock = $_POST['bayar'];
                if ($deliveriBrock == 'CASH') {
                    $dataparam["deliveryBlock"]="Z1";
                } else {
                    $dataparam["deliveryBlock"]="";
                }

                $dataparam["subdistCode"] = $_POST['sold_to'];
                $dataparam["subdistName"] = $_POST['nama_sold_to'];
                // $dataparam["subdistCode"] = "0000000138";
                // $dataparam["subdistName"] = "SEKAWAN NIAGA JAYA, PT";

                $dataparam["token"]=$token; 
                $dataparam["royaltyFlag"]="";

                $cetak_so_smbr = createso_smbr($dataparam);

                $datareturnsmbr = json_decode(json_encode($cetak_so_smbr),true);
                $data_output = json_decode($cetak_so_smbr, true);

                // penambahan lempar parameter deleveri block smbr
                if ($deliveriBrock == 'CASH') {
                    $deliveryBlock = 'Z1';
                } else {
                    $deliveryBlock = '';
                }
                
                $curl2 = curl_init();
                // echo "Get Token </br>";
                curl_setopt_array($curl2, array(
                  //CURLOPT_URL => 'http://10.10.2.182:8080/smbr/md/api/v1/so/open-delivery-block',
                  CURLOPT_URL => 'http://10.10.101.182:8080/smbr/md/api/v1/so/open-delivery-block',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>'{
                    "soNumber":"'.$data_output["data"]["soNumber"].'",
                    "deliveryBlock":"'.$deliveryBlock.'"
                }
                }',
                  CURLOPT_HTTPHEADER => array(
                    'Authorization:Bearer '.$token,
                    'Content-Type:application/json'
                  ),
                ));
                
                $response_block = curl_exec($curl2);
                // print_r("return SIGN IN CURL SMBR".curl_error($curl));
                // print_r($response_block);
                
                curl_close($curl2);
                $return_delivery_block = json_decode($response_block, true);
                // end API delivery block
                ///////////////////////////////////
                
                if($data_output["status"]=="200"){
                    $cetak_so_smbr = $data_output["data"]["soNumber"];
        
                    foreach($data_output["data"]["items"] as $key => $value){
                        $hargasmbr = $value["itemPrice"];
                        $satuansmbr = $value["salesUnit"];
                    }
                }

                $datarequestsmbr = json_encode($dataparam);
                // $datareturnsmbr = json_decode(json_encode($cetak_so_smbr),true);
                $field_names = array('GROUP_LOG','REQUEST','RESPON','BY_LOG','LOG_DATE','TOKEN');
                $field_data = array("1","$datarequestsmbr","$datareturnsmbr","CREATE_SO_SMBR","SYSDATE","$token");
                $tablename = "ZREPORT_LOG_SERVICE";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);

                $nomorso = $cetak_so_smbr;

                if($data_output["status"]=="200"){//$cetak_so_smbr==''
                    $show_ket .= "SO OPCO SMBR = Sales Order has been made with a number : " . $nomorso;
                    $show_ket .= '<br>';
                }else{
                    $show_ket .= "SO OPCO SMBR = " . $cetak_so_smbr;
                    $show_ket .= '<br>';
                }
            }


            // $strmven = "SELECT * FROM MAPPING_PO_ICS WHERE SHIPPING_POINT = '{$plant}' AND SOLD_TO_PARTY = '{$soldtoparty}' AND INCOTERM = '{$incoterm1}' AND FLAG_DEL = 'X' ";
            $strmven = "SELECT * FROM MAPPING_PO_ICS WHERE COMPANY_CODE = '{$sales_org}' AND SHIPPING_POINT = '{$plant}' AND SOLD_TO_PARTY = '{$soldtoparty}' AND INCOTERM = '{$incoterm1}' AND FLAG_DEL = 'X' ";
            
            $querymven = oci_parse($conn, $strmven);  
            oci_execute($querymven); 
            $rowmven = oci_fetch_array($querymven, OCI_ASSOC);    
            $comMd = $rowmven["COMPANY_CODE"];
            $plant_tujuan = $rowmven["SOLD_TO_PARTY"];
            $vendorFIX = sprintf("%010d", $rowmven["VENDOR"]);
            $amount_po_ics = $rowmven["AMOUNT"];
            $price_po_ics = $rowmven["PRICE_PO"];

            if (($status == '1' && ($sales_org == "PTSC" || $sales_org == "ID50")) || ($status == "200" && $sales_org == "1000")) {               
                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK)
                    $sap->Open();
                if ($sap->GetStatus() != SAPRFC_OK) {
                    $sap->PrintStatus();
                    exit;
                }
    
                // CREATE PO
                $fce = $sap->NewFunction("BAPI_PO_CREATE1");
                
                $tgl_sekarang = date("Ymd");
    
                // $fce->POHEADER["COMP_CODE"] = "7900"; // company pertama
                $fce->POHEADER["COMP_CODE"] = $com2; // company pertama
                // $fce->POHEADER["COMP_CODE"] = $comMd; // company pertama
                $fce->POHEADER["DOC_TYPE"] = "ZIC1"; // hardcode
                $fce->POHEADER["VENDOR"] = $vendorFIX; //
                $fce->POHEADER["PMNTTRMS"] = $top;
                $fce->POHEADER["PURCH_ORG"] = $rowmven["PURCH_ORGANIZATION"];
                $fce->POHEADER["PUR_GROUP"] = $rowmven["PURCHASING_GROUP"];
                //                        $fce->POHEADER["CURRENCY"] = "IDR";
                //                        $fce->POHEADER["EXCH_RATE"] = "1";
                $fce->POHEADER["DOC_DATE"] = $tgl_sekarang;
                $fce->POHEADER["INCOTERMS1"] = $incoterm1;
                $fce->POHEADER["INCOTERMS2"] = $incoterm2;
    
                $fce->NO_PRICE_FROM_PO = 'X';
                $fce->POHEADERX["COMP_CODE"] = "x";
                $fce->POHEADERX["DOC_TYPE"] = "x";
                $fce->POHEADERX["VENDOR"] = "x";
                $fce->POHEADERX["PMNTTRMS"] = "x";
                $fce->POHEADERX["PURCH_ORG"] = "x";
                $fce->POHEADERX["PUR_GROUP"] = "x";
                //                        $fce->POHEADERX["CURRENCY"] = "x";
                //                        $fce->POHEADERX["EXCH_RATE"] = "x";
                $fce->POHEADERX["DOC_DATE"] = "x";
                $fce->POHEADERX["INCOTERMS1"] = "x";
                $fce->POHEADERX["INCOTERMS2"] = "x";
    
                $fce->POTEXTHEADER->row["TEXT_ID"] = "F01";
                $fce->POTEXTHEADER->row["TEXT_FORM"] = "/";
                $fce->POTEXTHEADER->row["TEXT_LINE"] = "";
                $fce->POTEXTHEADER->Append($fce->POTEXTHEADER->row);
    
                $fce->POTEXTHEADER->row["TEXT_ID"] = "F14";
                $fce->POTEXTHEADER->row["TEXT_FORM"] = "/";
                $fce->POTEXTHEADER->row["TEXT_LINE"] = "AUTO GR";
                $fce->POTEXTHEADER->Append($fce->POTEXTHEADER->row);
    
                $sampai = $_POST['jumlah'];
                for ($j = 0; $j <= $sampai - 1; $j++) {
                    $idke = "idke" . $j;
                    $id_dtl = $_POST['com_iddtl' . $j];

                    $qty1 = $_POST['com_qty' . $j];
                    $qtyx1 = $_POST['com_qtyx' . $j];
                    
                    $item_num = $j + 1 * 10;
                    $item_numline = sprintf("%05d", $item_num);
                    $item_numlinePO = sprintf("%06d", $item_num);
    
                    $material = $_POST['com_produk' . $j];
    
                    $qty = $_POST['com_qty' . $j];
                    $qtyx = $_POST['com_qtyx' . $j];
    
                    $kontrak = $_POST['com_kontrak' . $j];
                    $posnr = $fungsi->linenum($_POST['com_posnr' . $j]);
                    $distrik = $_POST['com_distrik' . $j];
    
                    $tgl_kirimPO = $_POST['com_tgl_kirim' . $j];
                    list($day, $month, $year) = split("-", $tgl_kirimPO);
                    $tgl_kirim = $year . $month . $day;
    
                    $mtr_group = substr($material, 0, 7);
    
    
                    if (($nomorso != "") and ( $qty == $qtyx))
                        $status1 = "APPROVE";
                    else
                        $status1 = "PROCESS";
    
                    if ($mtr_group == "121-301") {
                        $nm_material = "SEMEN ZAK";
                        $po_unit = "ZAK";
                    } else if ($mtr_group == "121-302") {
                        $nm_material = "SEMEN CURAH";
                        $po_unit = "TO";
                    } else if ($mtr_group == "121-200") {
                        $nm_material = "CLINKER";
                        $po_unit = "TO";
                    }
    
                    // ITEM DARI SO SBI
                    $datas = $response->Data->Items[$j];
                    // $hargaPO = $datas->SAPSalesOrderNetPriceUnit; // Ambil dari price mapping price PO
                    $hargaPO = $price_po_ics;
                    if ($sales_org == "1000") {
                        $perPO = "1";
                        if($satuansmbr == 'TON'){
                            $satuanPO = 'TO';
                            $satuan2PO = 'TO';
                        }elseif($satuansmbr == 'BGB'){
                            $satuanPO = 'BAG';
                            $satuan2PO = 'BAG';
                        }else{
                            $satuanPO = $satuansmbr;
                            $satuan2PO = $satuansmbr;
                        }
                        $currencyPO = "IDR";
                    }else{
                        $perPO = $datas->SAPSalesOrderPerUnit;
                        $satuanPO = $datas->SAPSalesOrderUnit;
                        $satuan2PO = $datas->SAPSalesOrderUnit;
                        $currencyPO = $datas->SAPSalesOrderCurrency;
                    }
    
                    $fce->POITEM->row["PO_ITEM"] = $item_numline;
                    // $fce->POITEM->row["SHORT_TEXT"] = $nm_material;
                    $fce->POITEM->row["MATERIAL"] = $material;
                    // $fce->POITEM->row["PLANT"] = "79B1";
                    $fce->POITEM->row["PLANT"] = $plant_tujuan;
                    // $fce->POITEM->row["PLANT"] = $plantPO;
    
                    $fce->POITEM->row["QUANTITY"] = $qtyx;
                    $fce->POITEM->row["STGE_LOC"] = $rowmven["STORAGE_LOCATION"];
                    $fce->POITEM->row["PRICE_UNIT"] = $hargaPO;
                    // $fce->POITEM->row["MATL_GROUP "] = $mtr_group;
                    // $fce->POITEM->row["GR_IND"] = "X";
                    // $fce->POITEM->row["IR_IND"] = "X";
                    // $fce->POITEM->row["GR_BASEDIV"] = "X";
                    $fce->POITEM->Append($fce->POITEM->row);
    
                    $fce->POITEMX->row["PO_ITEM"] = $item_numline;
                    // $fce->POITEMX->row["SHORT_TEXT"] = "X";
                    $fce->POITEMX->row["MATERIAL"] = "X";
                    $fce->POITEMX->row["PLANT"] = "X";
                    $fce->POITEMX->row["STGE_LOC"] = "X";
                    // $fce->POITEMX->row["MATL_GROUP "] = "X";
                    $fce->POITEMX->row["QUANTITY"] = "X";
                    $fce->POITEMX->row["PRICE_UNIT"] = "X";
                    // $fce->POITEMX->row["GR_IND"] = "X";
                    // $fce->POITEMX->row["IR_IND"] = "X";
                    // $fce->POITEMX->row["GR_BASEDIV"] = "X";
                    $fce->POITEMX->Append($fce->POITEMX->row);
    
                    $fce->POTEXTITEM->row["PO_ITEM"] = $item_numline;
                    $fce->POTEXTITEM->row["TEXT_ID"] = "F01";
                    $fce->POTEXTITEM->row["TEXT_FORM"] = "/";
                    $fce->POTEXTITEM->row["TEXT_LINE"] = "SEMEN PPC";
                    $fce->POTEXTITEM->Append($fce->POTEXTITEM->row);
    
                    $fce->POSCHEDULE->row["PO_ITEM"] = $item_numline;
                    // $fce->POSCHEDULE->row["SCHED_LINE"] = "0001";
                    $fce->POSCHEDULE->row["DELIVERY_DATE"] = $tgl_trm_po;
                    // $fce->POSCHEDULE->row["QUANTITY "] = $qtyx;
                    // $fce->POSCHEDULE->row["STAT_DATE"] = $tgl_kirim;
                    // $fce->POSCHEDULE->row["PO_DATE"] = $tgl_kirim;
                    $fce->POSCHEDULE->Append($fce->POSCHEDULE->row);
    
                    $fce->POSCHEDULEX->row["PO_ITEM"] = $item_numline;
                    // $fce->POSCHEDULEX->row["SCHED_LINE"] = "X";
                    $fce->POSCHEDULEX->row["DELIVERY_DATE"] = "X";
                    // $fce->POSCHEDULEX->row["QUANTITY "] = "X";
                    // $fce->POSCHEDULEX->row["STAT_DATE"] = "X";
                    // $fce->POSCHEDULEX->row["PO_DATE"] = "X";
                    $fce->POSCHEDULEX->Append($fce->POSCHEDULEX->row);
    
    
                    $fce->POCOND->row["ITM_NUMBER"] = $item_numline;
                    $fce->POCOND->row["COND_ST_NO"] = '001';
                    $fce->POCOND->row["COND_TYPE"] = "PBXX";
                    $fce->POCOND->row["COND_VALUE"] = $hargaPO;
                    $fce->POCOND->row["COND_P_UNT"] = $perPO;
                    $fce->POCOND->row["COND_UNIT"] = $satuanPO;
                    $fce->POCOND->row["COND_UNIT_SO"] = $satuan2PO;
                    $fce->POCOND->row["CURRENCY"] = $currencyPO;
                    $fce->POCOND->row["CURRENCY_ISO"] = $currencyPO;
                    $fce->POCOND->row["CHANGE_ID"] = "U";
                    $fce->POCOND->Append($fce->POCOND->row);
    
                    // $fce->POCONDX->row["ITM_NUMBER"] = $item_numline;
                    $fce->POCONDX->row["COND_ST_NO"] = '001';
                    $fce->POCONDX->row["COND_TYPE"] = "x";
                    $fce->POCONDX->row["COND_VALUE"] = "x";
                    $fce->POCONDX->row["COND_P_UNT"] = "x";
                    $fce->POCONDX->row["COND_UNIT"] = "x";
                    $fce->POCONDX->row["COND_UNIT_SO"] = "x";
                    $fce->POCONDX->row["CURRENCY"] = "x";
                    $fce->POCONDX->row["CURRENCY_ISO"] = "x";
                    $fce->POCONDX->row["CHANGE_ID"] = "x";
                    $fce->POCONDX->Append($fce->POCONDX->row);

                    // ===================================================================== COND_COUNT
                     // mappingan from mapping_po_ics tabel oracle
                     $fce->POCOND->row["ITM_NUMBER"] = '000010';
                    //  $fce5->POCOND->row["COND_ST_NO"] = '002';
                    // $fce5->POCOND->row["COND_COUNT"] = '01';
                     $fce->POCOND->row["COND_TYPE"] = "ZBA2";
					 $fce->POCOND->row["COND_P_UNT"] = "1";
                     $fce->POCOND->row["COND_VALUE"] = $amount_po_ics;
                     $fce->POCOND->row["CURRENCY"] = 'IDR';
                     $fce->POCOND->row["CURRENCY_ISO"] = 'IDR';
                     $fce->POCOND->row["CHANGE_ID"] = "I";
                     $fce->POCOND->Append($fce->POCOND->row);
                    

                    $fce->POCONDX->row["ITM_NUMBER"] = '000010';
                    // $fce5->POCONDX->row["COND_ST_NO"] = '002';
                    // $fce5->POCONDX->row["COND_COUNT"] = 'x';
                    $fce->POCONDX->row["COND_TYPE"] = "X";
                    $fce->POCONDX->row["COND_VALUE"] = "X";
					$fce->POCONDX->row["COND_P_UNT"] = "X";
                    $fce->POCONDX->row["CURRENCY"] = "X";
                    $fce->POCONDX->row["CURRENCY_ISO"] = "X";
                    $fce->POCONDX->row["CHANGE_ID"] = "X";
                    $fce->POCONDX->Append($fce->POCONDX->row);
                }
    
    
                $fce->Call();
    
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $nopoT = $fce->EXPPURCHASEORDER;
                    $fce->RETURN->Reset();
                    while ($fce->RETURN->Next()) {
                        $tipe = $fce->RETURN->row["TYPE"];
                        $msg = $fce->RETURN->row["MESSAGE"];
                        $show_ket .= $msg;
                        $show_ket .= '<br>';
                    }
                    
                    //Commit Transaction
                    $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                    $fce->Call();
                }

                if (($nomorso != null || $nomorso != 0 || $nomorso != '') && ($nopoT != null || $nopoT != 0 || $nopoT != '')) {
                    if ($sales_org == "PTSC" || $sales_org == "ID50") {
                        $sql = "select * from apimd_url_config where nama = 'updatesoics' and delete_mark = 0";
                        $query=@oci_parse($conn, $sql);
                        // var_dump($query);
                        @oci_execute($query);
                        $results = oci_fetch_array($query, OCI_ASSOC);
                        // echo '<br>';
                        // echo $sql;
                        // print_r($results);
    
                        $url_update_so = $results['URL'];
    
                        $param = array(
                            'token' => $results['TOKEN'],
                            'systemid' => 'QASSO', 
                            'nomorso' => $nomorso,
                            'nomorpo' => $nopoT            
                        );
    
                        // echo '<pre>';
                        // echo json_encode($param);
    
                        $ch = curl_init();
    
                        // Set cURL options
                        curl_setopt($ch, CURLOPT_URL, $url_update_so);
                        curl_setopt($ch, CURLOPT_POST, 1);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
                        // Execute the request
                        $response = curl_exec($ch);
    
                        
                        // Close cURL session
                        curl_close($ch);
                        
                        $result_api = json_decode($response,true);
                        // Check for errors
                        if (!$result_api) {
                            $show_ket .= 'Update PO Referensi Gagal, Mohon ubah secara manual pada Nomor SO : '.$nomorso;
                        }
                    }

                    if ( $qty1 == $qtyx1)
                        $status = "APPROVE";
                    else
                        $status = "PROCESS";
                    //update data header
                    $field_names = array('PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'STATUS', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ROUTE', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'KD_REASON', 'NM_REASON', 'ORG');
                    $field_data = array("$plant", "$nama_plant", "$top", "$status", "$nama_top", "$so_type", "$nama_so_type", "$incoterm1", "$incoterm2", "SYSDATE", "$user_name_in", "$route", "$pricelist", "$nama_pricelist", "$lcnum", "$reason", "$nama_reason", "$sales_org");
                    $tablename = "OR_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$idh");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    if ( $qty1 == $qtyx1){
                        $status1 = "APPROVE";
                        $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT', 'KODE_BAG');
                        $field_data = array("$qty1", "updtgl_$tgl_kirimPO", "updtgl_$tgl_terima_leadtime", "$nomorso", "$status1", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "$kontrak", "$plant", "$nama_plant", "$com_kodekantong");
                    }else{
                        $status1 = "PROCESS";
                        $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT', 'KODE_BAG');
                        $field_data = array("$qty1", "updtgl_$tgl_kirimPO", "updtgl_$tgl_terima_leadtime", "$nomorso", "$status1", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "$kontrak", "$plant", "$nama_plant", "$com_kodekantong");
                    }

                    $sql_sql_getdataapprove = "select STATUS_LINE from OR_TRANS_DTL 
                    where DELETE_MARK=0 
                    and ID='$id_dtl'";
            
                    $mysql_getdataapprove=oci_parse($conn,$sql_sql_getdataapprove);
                    oci_execute($mysql_getdataapprove);
                    $row_datagetapprove=oci_fetch_assoc($mysql_getdataapprove);
                    $statusline_pp=$row_datagetapprove[STATUS_LINE]; 
                    //////
                    if($statusline_pp!='APPROVE'){
                        $tablename = "OR_TRANS_DTL";
                        $field_id = array('ID');
                        $value_id = array("$id_dtl");
                        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }
                }else { // KONDISI GAGAL CREATE PO DELETE SO SBI
                    if ($nomorso && !$nopoT && ($sales_org == "PTSC" || $sales_org == "ID50")) {
                        $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/DeleteSalesOrder'; // Prod Synxchrox
    
                        $data = array(
                            'Token' => 'aSsMx7GV0HFGzlufM4DH',
                            'SystemID' => 'QASSO', //'PASSO',
                            'SAPSalesOrderNumber' => $nomorso,
                            'SAPCreatedDate' => $tanggalSO,
                        );
                        $options = array(
                            'http' => array(
                                'header' => "Content-type: application/json\r\n",
                                'method' => 'POST',
                                'content' => json_encode($data),
                            )
                        );
    
                        $context = stream_context_create($options);
                        $result = file_get_contents($url, false, $context);
                        $response = json_decode($result);
                        $status = $response->Status;
                        $Message = $response->Message;
                        $Message_detail = isset($response->MessageDetail) ? $response->MessageDetail : $Message;

                        $param_send = json_encode($data);
                        $param_return = json_encode($response);
                        $field_names = array(
                            'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
                        );
                        $field_data = array(
                            "$param_send", "$param_return", "$user_name_in", "$no_pp", "$Message", "$Message_detail", "SYSDATE"
                        );
                        $tablename = "ZMD_LOG_SBI";
                        $sukses = $fungsi->insert($conn, $field_names, $field_data, $tablename);
                        if ($status != 1) {
                            $show_ket .= "SO OPCO = " . $Message_detail;
                            $show_ket .= '<br>';
                        } else {
                            $show_ket .= "SO OPCO = Sales Order has been rollback";
                            $show_ket .= '<br>';
                        }
                    } else if ($nomorso && !$nopoT && ($sales_org == "1000")) {
                        $datadelparam["csmsnumber"]= (int) $no_pp;

                        $delete_so_smbr = deleteso_smbr($datadelparam);

                        $data_delete_output = json_decode($delete_so_smbr, true);

                        // print_r($data_delete_output);

                        $status = $data_delete_output["cancelSoStatus"];
                        $Message = $data_delete_output["soMessage"];
                        
                        if ($status != "200") {
                            $show_ket .= "SO OPCO = " . $Message;
                            $show_ket .= '<br>';
                        } else {
                            $show_ket .= "SO OPCO = Sales Order has been rollback";
                            $show_ket .= '<br>';
                        }
                    }
                }
            }

            if (isset($_POST['lasthalaman'])) {
                $habis = "list_approve_pp_ics.php";
            } else {
                $habis = "approve_pp_ics.php";
            }

            break;         
        } // BREAK KONDISI ICS

        // jalankan bapi create so sap  
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            $sap->PrintStatus();
            exit;
        }

        unset($ex_so);
        $ex_so = true;
        //cegatan credit limit so khusus tlcc
//                if($sales_org=='6000'){
//                    require_once 'loadcreditlimit.php';
//                }

        if ($ex_so == true) {
            // SOCC v2
            $looping_end=1;
            $movetestrun='Z';
            for ($looping = 0; $looping <= $looping_end; $looping++) {
            if($movetestrun!='X'){
            ////////////
            // $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
            $fce = $sap->NewFunction("ZBAPI_SALESORDER_CREATEFROMDAT");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }
            //detail entri item'
            //$zak=$fungsi->qtyso($totalzak*1000);
            $sampai = $_POST['jumlah'];
            $ada = 0;
            for ($j = 0; $j <= $sampai - 1; $j++) {
                $idke = "idke" . $j;
                if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                    $id_dtl = $_POST['com_iddtl' . $j];
                    $item_num1 = $_POST['com_line' . $j];
                    $item_num = $fungsi->linenum($item_num1);
                    $material = $_POST['com_produk' . $j];
                    $shipto = $_POST['com_shipto' . $j];
                    $shipto = $fungsi->sapcode($shipto);
                    $distrik = $_POST['com_distrik' . $j];
                    $qty = $_POST['com_qty' . $j];
                    $qtyx = $_POST['com_qtyx' . $j];
                    $tgl_kirim = $_POST['com_tgl_kirim' . $j];
                    $kontrak = $_POST['com_kontrak' . $j];
                    $posnr = $fungsi->linenum($_POST['com_posnr' . $j]);

                    list($day, $month, $year) = split("-", $tgl_kirim);
                    $tgl_kirim = $year . $month . $day;
                                        //////////////////////////////////////////// proyek SOCC v.2
                                        // $plant_socc = $_POST['plant'];
                                        // $kode_distrik_socc = $_POST['com_distrik' . $j];
                                        // $material_for_socc = substr($_POST['com_produk' . $j],0,7);
                                        // $tgl_terima_leadtime = $fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
                                        // $tgl_terima_leadtime=$tgl_terima_leadtime;
                                        // if((date('H:i')>='15:00')){
                                        //     $tgl_terima_leadtime = $_POST['com_tgl_terima' . $j];
                                        //     $tgl_terima_leadtime = date('d-m-Y', strtotime($tgl_terima_leadtime. ' + 1 days'));
                                        // }
                                        // else{
                                            $tgl_terima_leadtime = $_POST['com_tgl_terima' . $j];
                                        // }
                                        // list($day, $month, $year) = split("-", $tgl_terima_leadtime);
                                        // $tgl_terima_leadtime = $year . $month . $day;
                                        list($dayleadtime, $monthleadtime, $yearleadtime) = split("-", $tgl_terima_leadtime);
                                        $tgl_terima_leadtime = $yearleadtime . $monthleadtime . $dayleadtime;


                                        // $tgl_fdate_socc=Date('d-m-Y');
                                        // list($day, $month, $year) = split("-", $tgl_fdate_socc);
                                        // $tgl_fdate_socc = $year . $month . $day;
                                        $tgl_fdate_socc=$tgl_kirim;
                                        ///////////////////////////////////////////
                    if ($qtyx != $qty) {
                        $ada = $ada + 1;
                    }

                    $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                    $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $material;
                    if ($so_type == 'ZFC') {
                        //$fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'ZKNN';//$item_cat;///salah revisi dibawah ini   
                        $fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'X'; //$item_cat;  
                        if ($sales_org == '6000') {
                            $ITEM_CATEGvali = 'ZKNN';
                        } else if ($sales_org == '4000' || $sales_org == '3000' || $sales_org == '7000' || $sales_org == '5000') {
                            $ITEM_CATEGvali = 'ZKLN';
                            //sales FOC distrik
                            if ($sales_org == '7000' || $sales_org == '5000') {
                                $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                                $fce->ORDER_ITEMS_IN->row["SALES_OFF"] = '10' . substr($distrik, 0, 2);
                            }
                        } else {
                            $ITEM_CATEGvali = 'ZKNN';
                        }
                        $fce->ORDER_ITEMS_IN->row["ITEM_CATEG"] = $ITEM_CATEGvali; //$item_cat;
                    }
                    $fce->ORDER_ITEMS_IN->row["PLANT"] = $plant;
                    $fce->ORDER_ITEMS_IN->row["ROUTE"] = $route;

                    if ($sales_org == '7900') {
                        $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                        $fce->ORDER_ITEMS_IN->row["SALES_OFF"] = '10' . substr($distrik, 0, 2);
                    }

                    //$fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                    if ($kontrak != '') {
                        $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                        $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = $posnr;
                        $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';
                    }
                    $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);

                    //detail entri schedule n qty'
                    $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                    $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $qty;
                    //////////////////////////////////////////////////////////// proyek SOCC V.2
                    // $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_kirim;
                    $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_fdate_socc;
                    ///////////////////////////////////////////////////////////
                    $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);

                    $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                    $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
                    $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
                    $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
                    $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);

                    //detail entri distributor dan agen
                    if ($j == 0)
                        $item_num = '000000';
                    else
                        $item_num = $item_num;
                    $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                    $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
                    $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
                    $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
                }
            }

            // if ($sales_org == '3000') {
            //     $fce1 = $sap->NewFunction("Z_ZCSD_SHIPTO");
            //     if ($fce1 == false) {
            //         $sap->PrintStatus();
            //         exit;
            //     }

            //     //header entri

            //     $fce1->ZNMORG = 3000;
            //     $fce1->ZKUNNR = $shipto;
            //     $fce1->Call();
            //     if ($fce1->GetStatus() == SAPRFC_OK) {
            //         $fce1->RETURN_DATA->Reset();
            //         while ($fce1->RETURN_DATA->Next()) {
            //             $kode = $fce1->RETURN_DATA->row["VKGRP"];
            //         }
            //     }
            //     $fce->ORDER_HEADER_IN["SALES_GRP"] = $kode;
            // }
            if ($so_type == 'ZFC') {
            $fce->ORDER_HEADER_IN["DLV_BLOCK"] = "Z1";
            }
            
            $fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type; //"ZOR";
            $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"3000";
            // $fce->ORDER_HEADER_IN["DISTR_CHAN"] = $distr_chan; //"10";
            $fce->ORDER_HEADER_IN["DISTR_CHAN"] = '40'; //"10";
            $fce->ORDER_HEADER_IN["DIVISION"] = $division; //"00";
            $fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp; //"Z1";
            $fce->ORDER_HEADER_IN["PURCH_DATE"] = $tgl_fdate_socc;; //"Z1";
            $fce->ORDER_HEADER_IN["PMNTTRMS"] = $top; //"Z1";
            $fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1; //"Z1";
            $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm2;
            $fce->ORDER_HEADER_IN["SHIP_COND"] = $ship_cond;
            $fce->ORDER_HEADER_IN["NAME"] = $nm_kapal;
            $fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist;
            $fce->ORDER_HEADER_IN["ORD_REASON"] = $reason;
            // SOCC V2
            $fce->ORDER_HEADER_IN["TP_DATE"] = 'D';
            $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tgl_terima_leadtime;  
            /////
            if ($lcnum != '') {
                $fce->ORDER_HEADER_IN["PMTGAR_PRO"] = 'Z00001';
                $fce->ORDER_HEADER_IN["DOC_NUM_FI"] = $lcnum;
            }
            if ($kontrakh != '') {
                $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
                $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrakh;
            }

            $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
            $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'AG';
            $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $soldto;
            $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
            // SOCC V.2
            if($looping=='0' or $looping==0){
                $fce->TESTRUN = "X";
            }
            //
            // echo "mantap";
            // echo '<pre>';
            // print_r($fce);
            // echo '</pre>';
            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                $nomorso = $fce->SALESDOCUMENT;
                $fce->RETURN->Reset();
                while ($fce->RETURN->Next()) {
                    $tipe = $fce->RETURN->row["TYPE"];
                    $msg = $fce->RETURN->row["MESSAGE"];
                    $id = $fce->RETURN->row["ID"];
                    $number = $fce->RETURN->row["NUMBER"];
                    if ($tipe != 'S') {
                        $show_ket .= $msg;
                        $show_ket .= '<br>';
                    // if($tipe=='W' && $id=='V1' && $number=='555'){

                        //SOCC v2
                        $show_ket .= 'SO long text';
                        $show_ket .= '<br>';
                        $movetestrun='X';
                        $nomorso = '';
                        ///
                    // }
                    }
                    //tambah pengujian rfc test run SOCC
                }
            }
            //SOCC v2
            }
            // $looping +=1;
            }
            ////////////////////////////////////////////////////////////// batas socc testrun
            if ($nomorso != '') {
                //Commit Transaction
                $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                $fce->Call();

                //Update SO
                // if ($nomorso != '' && $j > 0) {
                //     sleep(5); //seconds to wait..    
                //     $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                //     $fce->SALESDOCUMENT = $nomorso; //"ZOR";
                //     $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';
                //     $fce->Call();

                //     //Commit Transaction
                //     $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                //     $fce->Call();
                // }

//                 if ($sales_org == '6000') {
//                     sleep(5); //seconds to wait..
//                     //Teks
//                     unset($tgl_kirimapp);
//                     for ($j = 0; $j <= $sampai - 1; $j++) {
//                         $idke = "idke" . $j;
//                         if (isset($_POST[$idke]) and $_POST[$idke] != "") {
//                             $item_num1 = $_POST['com_line' . $j];
//                             $item_num = $fungsi->linenum($item_num1);
//                             $com_nopolisiv = $_POST['com_nopolisi' . $j];
//                             $com_typetruckv = $_POST['com_typetruck' . $j];
//                             $com_catatanv = $_POST['com_catatan' . $j];
//                             $com_kodekantong = $_POST['com_viewkodebag' . $j];
//                             $tgl_kirimappT = $_POST['com_tgl_kirim' . $j];
//                             $com_viewdrivern = $_POST['com_viewdrivern' . $j];
//                             $com_viewsimv = $_POST['com_viewsim' . $j];
//                             list($day, $month, $year) = split("-", $tgl_kirimappT);
//                             $tgl_kirimapp .= $year . $month . $day . "!";

//                             $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
//                             $fce->I_VBELN = $nomorso; //'000010';
//                             $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
//                             $fce->I_TEXT1 = $com_nopolisiv;
//                             $fce->I_TEXT2 = $com_typetruckv;
//                             $fce->I_TEXT3 = $com_catatanv;
//                             $fce->I_TEXT4 = $com_kodekantong;
//                             $fce->I_TEXT6 = $com_viewdrivern;
//                             $fce->I_TEXT7 = $com_viewsimv;
//                             $fce->Call();
// //                            echo "<pre>";
// //                            //print_r($fce);
// //                            echo "</pre>";
//                         }
//                     }

//                     if ($sales_org == '6000' && $nomorso != '') {
//                         $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorso&tgleq=$tgl_kirimapp";
//                     }
//                 }

                //@liyantanto penambahan no ref pp
                if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                    sleep(5); //s
                    for ($j = 0; $j <= $sampai - 1; $j++) {
                        $idke = "idke" . $j;
                        if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                            $item_num1 = $_POST['com_line' . $j];
                            $item_num = $fungsi->linenum($item_num1);
                            $com_nopppref = trim($_POST['com_ppref' . $j]);

                            if ($com_nopppref != '' && $nomorso != '') {
                                $pputam = substr($com_nopppref, 0, 10);
                                $itempputam = @(substr($com_nopppref, 10, 6) / 10);
                                $sql_ppref = "
                                    select ID,NO_SO,ITEM_NUMBER,NO_PPREF from OR_TRANS_DTL where DELETE_MARK=0 
                                    and NO_PP='$pputam' 
                                    and ITEM_NUMBER='$itempputam'
                                    ";
                                $query_ppref = oci_parse($conn, $sql_ppref);
                                oci_execute($query_ppref);
                                while ($row_ref = oci_fetch_array($query_ppref)) {
                                    $idPPREF = $row_ref[ID];
                                    $no_soref = $row_ref[NO_SO];
                                    $item_soref = $row_ref[ITEM_NUMBER];
                                }
                                // $com_noppputam = $no_pp . sprintf("%06d", $item_num * 10);
                                // $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                // $fce->I_VBELN = $no_soref; //'000010';
                                // $fce->I_POSNR = sprintf("%06d", $item_soref * 10); //'000010';
                                // $fce->I_TEXT5 = $nomorso . sprintf("%06d", $item_num * 10); //pp ref
                                // $fce->Call();

                                //update so pp ref
                                $field_namesppru = array('NO_PPREF');
                                $field_datapput = array("$com_noppputam");
                                $tablenamepput = "OR_TRANS_DTL";
                                $field_idpput = array('ID');
                                $value_idpput = array("$idPPREF");
                                $fungsi->update($conn, $field_namesppru, $field_datapput, $tablenamepput, $field_idpput, $value_idpput);

                                // $nosoref = $no_soref . sprintf("%06d", $item_soref * 10);
                                // $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                // $fce->I_VBELN = $nomorso; //'000010';
                                // $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                                // $fce->I_TEXT5 = $nosoref; //pp ref
                                // $fce->Call();
//                                echo "<pre>";
//                                //print_r($fce);
//                                echo "</pre>";
                            }
                        }
                    }
                }
            }
            $fce->Close();
            $sap->Close();

            if (($nomorso != "") and ( $ada == 0))
                $status = "APPROVE";
            else
                $status = "PROCESS";
            //update data header
            $field_names = array('PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'STATUS', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ROUTE', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'KD_REASON', 'NM_REASON', 'ORG');
            $field_data = array("$plant", "$nama_plant", "$top", "$status", "$nama_top", "$so_type", "$nama_so_type", "$incoterm1", "$incoterm2", "SYSDATE", "$user_name_in", "$route", "$pricelist", "$nama_pricelist", "$lcnum", "$reason", "$nama_reason", "$sales_org");
            $tablename = "OR_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$idh");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            // update data detail
            $sampai1 = $_POST['jumlah'];
            for ($k = 0; $k <= $sampai1; $k++) {
                $idke = "idke" . $k;
                if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                    $id_dtl1 = $_POST['com_iddtl' . $k];
                    $qty1 = $_POST['com_qty' . $k];
                    $qtyx1 = $_POST['com_qtyx' . $k];
                    $kontrak = $_POST['com_kontrak' . $k];
                    $posnr = $_POST['com_posnr' . $k];
                    $kode_distrik = $_POST['com_distrik' . $k];
                    $tgl_kirim1 = $_POST['com_tgl_kirim' . $k];
                    $tgl_terima = $fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
                    $item_numline1 = trim($_POST['com_line' . $k]);
                    $item_numline = $fungsi->linenum($item_numline1);
                    $com_kodekantong = $_POST['com_viewkodebag' . $k];
                    /////////////////////////////////////////// proyek SOCC V.2
                    $tgl_kirim1 = $_POST['com_tgl_kirim' . $k];
                    $tgl_terima = $fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
                    //old
                    //-----------------------------------------------------------------------//
                    //new
                    $tgl_terima1 = $_POST['com_tgl_terima' . $k];
                    // $plant_socc = $_POST['plant'];
                    // $kode_distrik_socc = $_POST['com_distrik' . $k];
                    // $material_for_socc = substr($_POST['com_produk' . $k],0,7);
                    // $tgl_terima_leadtime = $fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
                    //                     if((date('H:i')>='15:00')){
                    //                         $tgl_terima_leadtime = date('d-m-Y', strtotime($tgl_terima_leadtime. ' - 1 days'));
                    //                     }
                    // $tglterimaleadtime = date('d-m-Y', strtotime($tgl_terima_leadtime));
                    
                    ///////////////////////////////////////////
                    if (($nomorso != "") and ( $qty1 == $qtyx1)){
                        $status1 = "APPROVE";
                        $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT', 'KODE_BAG');
                        $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status1", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "$kontrak", "$plant", "$nama_plant", "$com_kodekantong");
                    }else{
                        $status1 = "PROCESS";
                        $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT', 'KODE_BAG');
                        $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status1", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "$kontrak", "$plant", "$nama_plant", "$com_kodekantong");
                    }

                    //pengujian jika approve tidak update ke process
                    $sql_sql_getdataapprove = "select STATUS_LINE from OR_TRANS_DTL 
                    where DELETE_MARK=0 
                    and ID='$id_dtl1'";
            
                    $mysql_getdataapprove=oci_parse($conn,$sql_sql_getdataapprove);
                    oci_execute($mysql_getdataapprove);
                    $row_datagetapprove=oci_fetch_assoc($mysql_getdataapprove);
                    $statusline_pp=$row_datagetapprove[STATUS_LINE]; 
                    //////
                    if($statusline_pp!='APPROVE'){
                        echo "Field Names:<br>";
                        foreach ($field_names as $name) {
                            echo $name . "<br>";
                        }

                        echo "<br>Field Data:<br>";
                        foreach ($field_data as $data) {
                            echo $data . "<br>";
                        }
                        $tablename = "OR_TRANS_DTL";
                        $field_id = array('ID');
                        $value_id = array("$id_dtl1");
                        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }

                    if ($nomorso != "") {
                        $field_names1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'APPROVE_DATE', 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                        $field_data1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', "$qty1", "instgl_$tgl_kirim1", 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', "'APPROVE'", 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', "SYSDATE", 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                        $tablenamefrom = "OR_TRANS_DTL";
                        $tablenameto = "OR_TRANS_APP";
                        $field_id1 = array('ID');
                        $value_id1 = array("$id_dtl1");
                        $fungsi->insertinto($conn, $field_names1, $field_data1, $tablenameto, $tablenamefrom, $field_id1, $value_id1);

                        //@liyantanto update qty approve
                        if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                            $sql_qtya = "
                                        update OR_TRANS_DTL set QTY_APPROVE=(
                                                select sum(nvl(QTY_APPROVE,0)) as QTY from OR_TRANS_APP where NO_PP='$no_pp' and ITEM_NUMBER='$item_numline1'
                                                and DELETE_MARK=0 and STATUS_LINE='APPROVE'
                                        ) where ID='$id_dtl1'
                                    ";
                            $query_qtya = oci_parse($conn, $sql_qtya);
                            oci_execute($query_qtya);
                        }

                    }
                }
            }

            $next = '';
            $sap2 = new SAPConnection();
            $sap2->Connect("../include/sapclasses/logon_data.conf");
            if ($sap2->GetStatus() == SAPRFC_OK)
                $sap2->Open();
            if ($sap2->GetStatus() != SAPRFC_OK) {
                $sap2->PrintStatus();
                exit;
            }   

            if ($nomorso != null || $nomorso != 0 || $nomorso != '') {

      
                $fce5 = $sap2->NewFunction("BAPISDORDER_GETDETAILEDLIST");
                $fce5->I_BAPI_VIEW["SDCOND"] = "X";
                $fce5->SALES_DOCUMENTS->row["VBELN"] = $nomorso;
                $fce5->SALES_DOCUMENTS->Append($fce5->SALES_DOCUMENTS->row);

                $fce5->Call();
                if ($fce5->GetStatus() == SAPRFC_OK) {
                    $fce5->ORDER_CONDITIONS_OUT->Reset();
                    $totL = 0;
                    $builder = array();
                    $dataH = array();
                    $dataH["item_number"] = array();
                    $dataH["nilai_harga"] = array();
                    $dataH["per"] = array();
                    $dataH["satuan"] = array();
                    while ($fce5->ORDER_CONDITIONS_OUT->Next()) {
                        if ($fce5->ORDER_CONDITIONS_OUT->row["COND_TYPE"] == "ZPR0") {
                            $dataH["item_number"] = $fce5->ORDER_CONDITIONS_OUT->row["ITM_NUMBER"];
                            $dataH["nilai_harga"] = $fce5->ORDER_CONDITIONS_OUT->row["COND_VALUE"];
                            $dataH["per"] = $fce5->ORDER_CONDITIONS_OUT->row["COND_P_UNT"];
                            $dataH["satuan"] = $fce5->ORDER_CONDITIONS_OUT->row["COND_D_UNT"];
                            $dataH["satuan2"] = $fce5->ORDER_CONDITIONS_OUT->row["T_UNIT_ISO"];
                            $dataH["currency"] = $fce5->ORDER_CONDITIONS_OUT->row["CURRENCY"];

                            $builder[] = $dataH;
                            $totL += 1;
                        }
                    }
                  
                    //Commit Transaction
                    $fce5 = $sap2->NewFunction("BAPI_TRANSACTION_COMMIT");
                    $fce5->Call();

                    if ($totL != $_POST['jumlah']) {
                        $next = "N";
                        $show_ket .= "Master Harga Belum Ada...!!!";
                        $show_ket .= '<br>';
                    }
                }
            }

            if ($next != 'N') {
                # code...
            // PROSES CREATE PO
            $fce5 = $sap2->NewFunction("BAPI_PO_CREATE1");
            $tgl_sekarang = date("Ymd");
            $min_soldto = intval($soldto); 

            $firstDigit = substr((string)$min_soldto, 0, 1);
            
            $com2 = $firstDigit . '000';

            $strven = "SELECT * FROM MAPPING_PO_ICS WHERE SHIPPING_POINT = '{$plant}' AND SOLD_TO_PARTY = '{$min_soldto}' AND INCOTERM = '{$incoterm1}' AND FLAG_DEL = 'X' ";
                
            $queryven = oci_parse($conn, $strven);  
            oci_execute($queryven); 
            $rowven = oci_fetch_array($queryven, OCI_ASSOC);    
            $plant_tujuan = $rowven["SOLD_TO_PARTY"];
            $str_loc = $rowven["STORAGE_LOCATION"];
            $amount_po_ics = $rowven["AMOUNT"];
            // echo $amount_po_ics;
            $fce5->POHEADER["COMP_CODE"] = $com2; // company kedua 
            // dilihat soldto / shipto party .. jika depan 3 maka 3000 jika 4 40000
            // di ambil dari tabel mapping_po_ics = dari soldto party 
            // select table nya dari shiping point dan sold to party
            $fce5->POHEADER["DOC_TYPE"] = "ZIC1"; // hardcode
            $fce5->POHEADER["VENDOR"]    = str_pad($rowven["VENDOR"], 10, '0', STR_PAD_LEFT);
            $fce5->POHEADER["PMNTTRMS"] = $top; 
            $fce5->POHEADER["PURCH_ORG"] = $rowven["PURCH_ORGANIZATION"];
            $fce5->POHEADER["PUR_GROUP"] = $rowven["PURCHASING_GROUP"]; 
            // ini di ambil dari mappingan e mas akbar aja
            //$fce5->POHEADER["CURRENCY"] = "IDR";
            //$fce5->POHEADER["EXCH_RATE"] = "1";
            $fce5->POHEADER["DOC_DATE"] = $tgl_sekarang;
            $fce5->POHEADER["INCOTERMS1"] = $incoterm1;
            $fce5->POHEADER["INCOTERMS2"] = $incoterm2;
            $fce5->NO_PRICE_FROM_PO = 'X';
            $fce5->POHEADERX["COMP_CODE"] = "x";
            $fce5->POHEADERX["DOC_TYPE"] = "x";
            $fce5->POHEADERX["VENDOR"] = "x";
            $fce5->POHEADERX["PMNTTRMS"] = "x";
            $fce5->POHEADERX["PURCH_ORG"] = "x";
            $fce5->POHEADERX["PUR_GROUP"] = "x";
            //$fce5->POHEADERX["CURRENCY"] = "x";
            //$fce5->POHEADERX["EXCH_RATE"] = "x";
            $fce5->POHEADERX["DOC_DATE"] = "x";
            $fce5->POHEADERX["INCOTERMS1"] = "x";
            $fce5->POHEADERX["INCOTERMS2"] = "x";

            

            $fce5->POTEXTHEADER->row["TEXT_ID"] = "F01";
            $fce5->POTEXTHEADER->row["TEXT_FORM"] = "/";
            $fce5->POTEXTHEADER->row["TEXT_LINE"] = "";
            $fce5->POTEXTHEADER->Append($fce5->POTEXTHEADER->row);

            $fce5->POTEXTHEADER->row["TEXT_ID"] = "F14";
            $fce5->POTEXTHEADER->row["TEXT_FORM"] = "/";
            $fce5->POTEXTHEADER->row["TEXT_LINE"] = "AUTO GR";
            $fce5->POTEXTHEADER->Append($fce5->POTEXTHEADER->row);

            $sampai = $_POST['jumlah'];
            for ($j = 0; $j <= $sampai - 1; $j++) {
                // $j= 0;
                $idke = "idke" . $j;
                if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                    $id_dtl = $_POST['com_iddtl' . $j];
                    $item_num1 = $_POST['com_line' . $j];
                    $item_num = $fungsi->linenum($item_num1);
                    $item_numli = $item_num * 10;
                    $item_numline = sprintf("%06d", $item_numli);
                    $item_numlinePO = sprintf("%05d", $item_numli);
                    $material = $_POST['com_produk' . $j];
                  
                    $qty = $_POST['com_qty' . $j];
                    $qtyx = $_POST['com_qtyx' . $j];

                    // if($sales_org=="7900"){ 
                    //     $kontrak = $_POST['com_kontrak' . $j];
                    // }else{
                    //     $kontrak = "";
                    // }
                    $kontrak = $_POST['com_kontrak' . $j];

                    $posnr = $fungsi->linenum($_POST['com_posnr' . $j]);
                    $distrik = $_POST['com_distrik' . $j];

                    $tgl_kirimPO = $_POST['com_tgl_kirim' . $j];
                    list($day, $month, $year) = split("-", $tgl_kirimPO);
                    $tgl_kirim = $year . $month . $day;
                    $tgl_kirimpp = $year .'-'. $month .'-'. $day;

                    $tgl_trmPO = $_POST['com_tgl_terima' . $j];
                    list($day1, $month1, $year1) = split("-", $tgl_trmPO);
                    $tgl_trm_po = $year1 . $month1 . $day1;
                    $tgl_trmpp = $year1 .'-'. $month1 .'-'. $day1;

                    // UPDATE TANGGAL KIRIM DAN TANGGAL TERIMA DI PENGAJUAN PP
                    $sql22 = "UPDATE OR_TRANS_DTL SET TGL_KIRIM_PP = TO_DATE('$tgl_kirimpp', 'yyyy-mm-dd'), TGL_TERIMA = TO_DATE('$tgl_trmpp', 'yyyy-mm-dd')  WHERE NO_PP = '$no_pp' ";
                    $query22 = oci_parse($conn, $sql22);
                    $upds = oci_execute($query22);

                    // $field_names = array('TGL_KIRIM_PP', 'TGL_TERIMA');
                    // $field_data = array("2024-06-07", "2024-07-07");
                    // $tablename = "OR_TRANS_DTL";
                    // $field_id = array('NO_PP');
                    // $value_id = array("$no_pp");
                    // $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    
                    ///////////////////////////////////////////////////////////// SOCC v2.0
                    // $plant_socc = $_POST['plant'];
                    // $kode_distrik_socc = $_POST['com_distrik' . $j];
                    // $material_for_socc_deldate = substr($_POST['com_produk' . $j],0,7);
                    // $tgl_terima_leadtime_deliverydate = $fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc_deldate);
                    ////////////////////////////////////////////////////////////


                    $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL WHERE ORG_MD = '{$sales_org}' AND PLANT_MD = '{$plant}' AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                    $querymaterial = oci_parse($conn, $strmaterial);
                    oci_execute($querymaterial);
                    $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);


                    if ($p == 1) {
                        $material = $_POST['com_produk' . $j];
                    } else if ($p == 2) {
                        $material = $rowmaterial['KD_MATERIAL_OPCO'];
                    } else if ($p == 3) {
                        $material = $rowmaterial['KD_MATERIAL_MD_2'];
                    }

                    $mtr_group = substr($material, 0, 7);


                    if (($nomorso != "") and ( $qty1 == $qtyx1))
                        $status1 = "APPROVE";
                    else
                        $status1 = "PROCESS";

                    if ($mtr_group == "121-301") {
                        $nm_material = "SEMEN ZAK";
                        $po_unit = "ZAK";
                    } else if ($mtr_group == "121-302") {
                        $nm_material = "SEMEN CURAH";
                        $po_unit = "TO";
                    } else if ($mtr_group == "121-200") {
                        $nm_material = "CLINKER";
                        $po_unit = "TO";
                    }

                    //    $strsloc = "SELECT * FROM ZERP_MAP_SLOC WHERE ORG = '{$sales_org1}' AND PLANT = '$plant1' AND KD_MATERIAL = '{$mtr_group}' AND DEL = 0";
                    //    $queryloc = oci_parse($conn, $strsloc);
                    //    oci_execute($queryloc);
                    //    $rowloc = oci_fetch_array($queryloc, OCI_ASSOC);
                    // CEK HARGA DARI BAPI COMMIT HARGA
                    //smbr
                    // echo $COM_HARGANYA;
                    // if ($COM_HARGANYA == "1000" && $p==1) {
                    //     // foreach ($builder as $val) {
                    //     //     // if ($item_numline == $val['item_number']) {
                    //     //         $hargaPO = $hargasmbr;
                    //     //         $perPO = "1";
                    //     //         $satuanPO = $satuansmbr;
                    //     //         $satuan2PO = $satuansmbr;
                    //     //         $currencyPO = "IDR";
                    //     //     // }

                    //     //     // echo "ini smbr 1".$COM_HARGANYA;
                    //     // }

                    //     //perbaikan untuk go live 30 03 2023
                    //     $hargaPO = $hargasmbr;
                    //     $perPO = "1";
                    //     if($satuansmbr == 'TON'){
                    //         $satuanPO = 'TO';
                    //         $satuan2PO = 'TO';
                    //     }elseif($satuansmbr == 'BGB'){
                    //         $satuanPO = 'BAG';
                    //         $satuan2PO = 'BAG';
                    //     }else{
                    //         $satuanPO = $satuansmbr;
                    //         $satuan2PO = $satuansmbr;
                    //     }
                    //     $currencyPO = "IDR";
                    // //smbr
                    // }else if ($COM_HARGANYA == "1000" && $p==2) {
                    //     // echo "ini smbr 2 ".$COM_HARGANYA;
                    //         foreach ($builder as $val) {
                    //             // if ($item_numline == $val['item_number']) {
                    //                 $hargaPO = $hargasmbr;
                    //                 $perPO = "1";
                    //                 $satuanPO = $satuansmbr;
                    //                 $satuan2PO = $satuansmbr;
                    //                 $currencyPO = "IDR";
                    //             // }
                    //         }
                    // }else 
                    // if(($COM_HARGANYA != "PTSC" && $COM_HARGANYA != "ID50") && ($p==1)) {
                        foreach ($builder as $val) {
                            if ($item_numline == $val['item_number']) {
                                $hargaPO = $val['nilai_harga'];
                                $perPO = $val['per'];
                                $satuanPO = $val['satuan'];
                                $satuan2PO = $val['satuan2'];
                                $currencyPO = $val['currency'];
                            }
                            
                        }
                    //smbr
                    // }else if (($COM_HARGANYA != "PTSC" && $COM_HARGANYA != "ID50" && ($p==2))) {
                    //     // echo "ini MD 2".$COM_HARGANYA;
                    //         foreach ($builder as $val) {
                    //             if ($item_numline == $val['item_number']) {
                    //                 $hargaPO = $val['nilai_harga'];
                    //                 $perPO = $val['per'];
                    //                 $satuanPO = $val['satuan'];
                    //                 $satuan2PO = $val['satuan2'];
                    //                 $currencyPO = $val['currency'];
                    //             }
                    //         }
                          
                    // }
                    // else {
                    //     //  echo "ini sbi";
                    //     // // print_r($response);
                    //     // if ($p == 1) {
                    //     //     // $datas = $data_response_sbi['datasbi']->Data->Items[$j];
                    //     //     $datas = $response->Data->Items[$j];
                    //     // } else if ($p == 3) {
                    //     //     $datas = $responseSBI2->Data->Items[$j];
                    //     // }
                    //     // $hargaPO = $datas->SAPSalesOrderNetPriceUnit;
                    //     // $perPO = $datas->SAPSalesOrderPerUnit;
                    //     // $satuanPO = $datas->SAPSalesOrderUnit;
                    //     // $satuan2PO = $datas->SAPSalesOrderUnit;
                    //     // $currencyPO = $datas->SAPSalesOrderCurrency;
                    //     // echo "harga ptsc : ".$hargaPO;
                    //     foreach ($builder as $val) {
                    //         if ($item_numline == $val['item_number']) {
                    //             $hargaPO = $val['nilai_harga'];
                    //             $perPO = $val['per'];
                    //             $satuanPO = $val['satuan'];
                    //             $satuan2PO = $val['satuan2'];
                    //             $currencyPO = $val['currency'];
                    //         }
                    //         }
                        
                    // }
                    //    print_r($builder);
                    //    //print_r($hargaPO);
                    //    //print_r($item_numline);

                    $fce5->POITEM->row["PO_ITEM"] = $item_numlinePO;
                    // $fce5->POITEM->row["SHORT_TEXT"] = $nm_material;
                    $fce5->POITEM->row["MATERIAL"] = $material;
                    $fce5->POITEM->row["PLANT"] = $plant_tujuan;
//                                    $fce5->POITEM->row["STGE_LOC"] = $rowloc['KD_SLOC'];
                    // $fce5->POITEM->row["MATL_GROUP "] = $mtr_group;
                    $fce5->POITEM->row["QUANTITY"] = round(($qty * 0.1) + $qty);
                    $fce5->POITEM->row["STGE_LOC"] = $str_loc;
                    // $fce5->POITEM->row["PO_UNIT"] = $po_unit;
                    $fce5->POITEM->row["PRICE_UNIT"] = $hargaPO;
                    // $fce5->POITEM->row["NET_PRICE"] =  $amount_po_ics;
                    // $fce5->POITEM->row["GR_IND"] = "X";
                    // $fce5->POITEM->row["IR_IND"] = "X";
                    // $fce5->POITEM->row["GR_BASEDIV"] = "X";
//                  $fce5->POITEM->row["FUNDS_CTR"] = "7902000000";
                    $fce5->POITEM->Append($fce5->POITEM->row);

                    $fce5->POITEMX->row["PO_ITEM"] = $item_numlinePO;
                    // $fce5->POITEMX->row["SHORT_TEXT"] = "X";
                    $fce5->POITEMX->row["MATERIAL"] = "X";
                    $fce5->POITEMX->row["PLANT"] = "X";
//                                    $fce5->POITEMX->row["STGE_LOC"] = "X"; 
                    // $fce5->POITEMX->row["MATL_GROUP "] = "X";
                    $fce5->POITEMX->row["QUANTITY"] = "X";
                    // $fce5->POITEMX->row["PO_UNIT"] = "X";
                    $fce5->POITEMX->row["PRICE_UNIT"] = "X";
                    $fce5->POITEMX->row["NET_PRICE"] = "X";
                    $fce5->POITEMX->row["STGE_LOC"] = "X";
                    // $fce5->POITEMX->row["GR_IND"] = "X";
                    // $fce5->POITEMX->row["IR_IND"] = "X";
                    // $fce5->POITEMX->row["GR_BASEDIV"] = "X";
//                                        $fce5->POITEMX->row["FUNDS_CTR"] = "X";
                    $fce5->POITEMX->Append($fce5->POITEMX->row);

                    // $fce5->POTEXTITEM->row["PO_ITEM"] = '000010';
                    // $fce5->POTEXTITEM->row["TEXT_ID"] = "F01";
                    // $fce5->POTEXTITEM->row["TEXT_FORM"] = "/";
                    // $fce5->POTEXTITEM->row["TEXT_LINE"] = "SEMEN PPC";
                    // $fce5->POTEXTITEM->Append($fce5->POTEXTITEM->row);

                    
                    // $fce5->POSCHEDULE->row["SCHED_LINE"] = "0000";
                    //////////////////////////////////////////////////// SOCC V.2
                    $fce5->POSCHEDULE->row["PO_ITEM"] = $item_numlinePO;
                    $fce5->POSCHEDULE->row["DELIVERY_DATE"] = $tgl_trm_po;
                    //old
                    //------------------------------------------------------
                    //new
                    // $fce5->POSCHEDULE->row["DELIVERY_DATE"] = $tgl_terima_leadtime_deliverydate;
                    ///////////////////////////////////////////////////
                    // $fce5->POSCHEDULE->row["QUANTITY "] = round(($qty * 0.1) + $qty);
                    // $fce5->POSCHEDULE->row["STAT_DATE"] = $tgl_kirim;
                    // $fce5->POSCHEDULE->row["PO_DATE"] = $tgl_kirim;
                    $fce5->POSCHEDULE->Append($fce5->POSCHEDULE->row);

                    // $fce5->POSCHEDULEX->row["SCHED_LINE"] = "0000";
                    $fce5->POSCHEDULEX->row["PO_ITEM"] = $item_numlinePO;
                    $fce5->POSCHEDULEX->row["DELIVERY_DATE"] = "X";
                    // $fce5->POSCHEDULEX->row["QUANTITY "] = "X";
                    // $fce5->POSCHEDULEX->row["STAT_DATE"] = "X";
                    // $fce5->POSCHEDULEX->row["PO_DATE"] = "X";
                    $fce5->POSCHEDULEX->Append($fce5->POSCHEDULEX->row);
    
                    // ============================================================
                    // perbaikan harga so ICS
					$fce5->POCOND->row["ITM_NUMBER"] = $item_numlinePO;
					$fce5->POCOND->row["COND_ST_NO"] = '001';
					$fce5->POCOND->row["COND_TYPE"] = "PBXX";
					// $fce5->POCOND->row["COND_COUNT"] = '01';
					$fce5->POCOND->row["COND_VALUE"] = $hargaPO;
					$fce5->POCOND->row["COND_P_UNT"] = $perPO;
					$fce5->POCOND->row["COND_UNIT"] = $satuanPO;
                    $fce5->POCOND->row["COND_UNIT_SO"] = $satuan2PO;
					$fce5->POCOND->row["CURRENCY"] = $currencyPO;
					$fce5->POCOND->row["CURRENCY_ISO"] = $currencyPO;
					$fce5->POCOND->row["CHANGE_ID"] = "U";
					$fce5->POCOND->Append($fce5->POCOND->row);
				
                    $fce5->POCONDX->row["COND_ST_NO"] = '001';
                    $fce5->POCONDX->row["COND_TYPE"] = "x";
                    $fce5->POCONDX->row["COND_VALUE"] = "x";
                    $fce5->POCONDX->row["COND_P_UNT"] = "x";
                    $fce5->POCONDX->row["COND_UNIT"] = "x";
                    $fce5->POCONDX->row["COND_UNIT_SO"] = "x";
                    $fce5->POCONDX->row["CURRENCY"] = "x";
                    $fce5->POCONDX->row["CURRENCY_ISO"] = "x";
                    $fce5->POCONDX->row["CHANGE_ID"] = "x";
                    $fce5->POCONDX->Append($fce5->POCONDX->row);

                     // ===================================================================== COND_COUNT
                     // mappingan from mapping_po_ics tabel oracle
                     $fce5->POCOND->row["ITM_NUMBER"] = '000010';
                    //  $fce5->POCOND->row["COND_ST_NO"] = '002';
                    // $fce5->POCOND->row["COND_COUNT"] = '01';
                     $fce5->POCOND->row["COND_TYPE"] = "ZBA2";
					 $fce5->POCOND->row["COND_P_UNT"] = "1";
                     $fce5->POCOND->row["COND_VALUE"] = $amount_po_ics;
                     $fce5->POCOND->row["CURRENCY"] = 'IDR';
                     $fce5->POCOND->row["CURRENCY_ISO"] = 'IDR';
                     $fce5->POCOND->row["CHANGE_ID"] = "I";
                     $fce5->POCOND->Append($fce5->POCOND->row);
                    

                    $fce5->POCONDX->row["ITM_NUMBER"] = '000010';
                    // $fce5->POCONDX->row["COND_ST_NO"] = '002';
                    // $fce5->POCONDX->row["COND_COUNT"] = 'x';
                    $fce5->POCONDX->row["COND_TYPE"] = "X";
                    $fce5->POCONDX->row["COND_VALUE"] = "X";
					$fce5->POCONDX->row["COND_P_UNT"] = "X";
                    $fce5->POCONDX->row["CURRENCY"] = "X";
                    $fce5->POCONDX->row["CURRENCY_ISO"] = "X";
                    $fce5->POCONDX->row["CHANGE_ID"] = "X";
                    $fce5->POCONDX->Append($fce5->POCONDX->row);

                }
            }
           

            $fce5->Call();

            if ($fce5->GetStatus() == SAPRFC_OK) {
                $nopoT = $fce5->EXPPURCHASEORDER;
                $fce5->RETURN->Reset();
                while ($fce5->RETURN->Next()) {
                    $tipe = $fce5->RETURN->row["TYPE"];
                    $msg = $fce5->RETURN->row["MESSAGE"];
                    $show_ket_po .= $msg;
                    $show_ket_po .= '<br>';
                    if ($tipe == 'E') {
                        $next = "N";
                    }
                }
                //Commit Transaction
                $fce5 = $sap2->NewFunction("BAPI_TRANSACTION_COMMIT");
                $fce5->Call();
            }

     
          //PROSES ROLLBACK JIKA PO GAGAL
            if ($next == "N") {
                $jerror = 0;
                // for ($e = 1; $e <= $jerror; $e++) {
                    $return = "Y";
                        $fce5 = $sap2->NewFunction("BAPI_SALESORDER_CHANGE");
                        if ($fce5 == false) {
                            $sap2->PrintStatus();
                            exit;
                        }
                        //header entri
                        $fce5->SALESDOCUMENT = $nomorso; //"ZOR";
                        $fce5->ORDER_HEADER_INX["UPDATEFLAG"] = 'D'; //"Z1";

                        $fce5->Call();

                        if ($fce5->GetStatus() == SAPRFC_OK) {
                            $fce5->RETURN->Reset();
                            while ($fce5->RETURN->Next()) {
                                $msg .= '<div align="center">';
                                $error = $fce5->RETURN->row["TYPE"];
                                $msg .= $fce5->RETURN->row["MESSAGE"];

                                if ($error == 'A' || $error == 'X' || $error == 'E') {
                                    $return = 'N';
                                }
                            }
                            $msg .= '<br></div>';
                            //Commit Transaction
                            $fce5 = $sap2->NewFunction("BAPI_TRANSACTION_COMMIT");
                            $fce5->Call();
                        } else {
                            $fce5->PrintStatus();
                        }

                    // }

                    if ($return == 'Y') {
                        $rollbackso = true;
                        $str = "UPDATE OR_TRANS_HDR SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE ID='$idh' ";
                        $query = oci_parse($conn, $str);
                        oci_execute($query);
                        $str = "UPDATE OR_TRANS_APP SET NO_SO = '', QTY_APPROVE=0, STATUS_LINE='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE NO_SO='$nomorso' ";
                        $query = oci_parse($conn, $str);
                        oci_execute($query);
                        $str = "UPDATE OR_TRANS_DTL SET NO_SO = '',  STATUS_LINE='OPEN',LAST_UPDATED_BY='$user_name_in',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$nomorso'";
                        $query = oci_parse($conn, $str);
                        oci_execute($query);
                        $str = "UPDATE OR_TRANS_HDR_ICS SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE VBELN='$nomorso'";
                        $query = oci_parse($conn, $str);
                        oci_execute($query);
                        $str = "UPDATE OR_TRANS_DTL_ICS SET NO_SO = '-',  STATUS_LINE='OPEN',LAST_UPDATED_BY='$user_name_in',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$nomorso'";
                        $query = oci_parse($conn, $str);
                        oci_execute($query);
                        $show_ket .= 'Sales Order ' . $nomorso . ' has been success roolback ';
                    } else {
                        $rollbackso = false;
                        $show_ket .= 'Sales Order ' . $nomorso . ' has been failed roolback';
                    }
                // }
            }


            if (($nomorso != null || $nomorso != 0 || $nomorso != '') && ($nopoT != null || $nopoT != 0 || $nopoT != '')) {
                # code...
                // insert nomer PO di detil SO 
                // $fce5 = $sap2->NewFunction("BAPI_SALESORDER_CHANGE");
                // $fce5->SALESDOCUMENT = $nomorso;//"ZOR";
                // $fce5->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                // $fce5->ORDER_HEADER_INX["PURCH_NO_S"]='X';
                
                // $fce5->ORDER_HEADER_IN["PURCH_NO_S"]= $nopoT;
                // echo ' ini nomor pod yg update '. $fce5->ORDER_HEADER_IN["PURCH_NO_S"];
                // //detail entri item
                // echo ' no so atas '.  $nomorso;
                // echo ' no po atas '.  $nopoT;

                // $fce5->Call();
                
                // if ($fce5->GetStatus() == SAPRFC_OK ) {
                //     $fce5->RETURN->Reset();
                //     while ($fce5->RETURN->Next()){
                //     $error = $fce5->RETURN->row["TYPE"];
                //     // $this->_data[] = $fce5->RETURN->row;
                //     // $msg .= ' ';
                //     $msg = $fce5->RETURN->row["MESSAGE"];
                //     }
                //     //Commit Transaction
                //     $fce5 = $sap2->NewFunction("BAPI_TRANSACTION_COMMIT");
                //     $fce5->Call();
                // }

                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                $fce5 = $sap2->NewFunction("Z_ZCSD_UPDATE_REF_SO");
                if ($fce5 == false) {
                    $sap2->PrintStatus();
                    exit;
                }

                $fce5->I_ZVBELN = $nomorso;
                $fce5->I_VBELN = $nomorso;      
                // so_reff_smbr($SO1,$PO2,$SO4);

                $fce5->I_PO = $nopoT;
                //$fce5->I_LINE_ITEM = $numline_pertama;
                $fce5->I_LINE_ITEM = '00010';
                $fce5->Call();
                // echo ' no so  '.  $nomorso;
                // echo ' no po  '.  $nopoT;
                if ($fce5->GetStatus() == SAPRFC_OK) {
                    //while ( $fce5->ZMESSAGE->Next() ){
                    $tipe = $fce5->ZMESSAGE["TYPE"];
                    $msg = $fce5->ZMESSAGE["MESSAGE"];
                    $show_ket .= $msg . 'no zso : '. $fce5->I_ZVBELN. '; no so : '. $fce5->I_VBELN. '; no po : '. $fce5->I_PO. '; line : '.$fce5->I_LINE_ITEM.'<br>';
                    //delete so ref gagal
                    
                    //}
                }
            }
            // else{
            //     $jerror = 0;
            //     // for ($e = 1; $e <= $jerror; $e++) {
            //         $return = "Y";
            //             $fce5 = $sap2->NewFunction("BAPI_SALESORDER_CHANGE");
            //             if ($fce5 == false) {
            //                 $sap2->PrintStatus();
            //                 exit;
            //             }
            //             //header entri
            //             $fce5->SALESDOCUMENT = $nomorso; //"ZOR";
            //             $fce5->ORDER_HEADER_INX["UPDATEFLAG"] = 'D'; //"Z1";

            //             $fce5->Call();

            //             if ($fce5->GetStatus() == SAPRFC_OK) {
            //                 $fce5->RETURN->Reset();
            //                 while ($fce5->RETURN->Next()) {
            //                     $msg .= '<div align="center">';
            //                     $error = $fce5->RETURN->row["TYPE"];
            //                     $msg .= $fce5->RETURN->row["MESSAGE"];

            //                     if ($error == 'A' || $error == 'X' || $error == 'E') {
            //                         $return = 'N';
            //                     }
            //                 }
            //                 $msg .= '<br></div>';
            //                 //Commit Transaction
            //                 $fce5 = $sap2->NewFunction("BAPI_TRANSACTION_COMMIT");
            //                 $fce5->Call();
            //             } else {
            //                 $fce5->PrintStatus();
            //             }

            //         // }

            //         if ($return == 'Y') {
            //             $rollbackso = true;
            //             $str = "UPDATE OR_TRANS_HDR SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE ID='$idh' ";
            //             $query = oci_parse($conn, $str);
            //             oci_execute($query);
            //             $str = "UPDATE OR_TRANS_APP SET NO_SO = '', QTY_APPROVE=0, STATUS_LINE='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE NO_SO='$nomorso' ";
            //             $query = oci_parse($conn, $str);
            //             oci_execute($query);
            //             $str = "UPDATE OR_TRANS_DTL SET NO_SO = '',  STATUS_LINE='OPEN',LAST_UPDATED_BY='$user_name_in',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$nomorso'";
            //             $query = oci_parse($conn, $str);
            //             oci_execute($query);
            //             $str = "UPDATE OR_TRANS_HDR_ICS SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_name_in' WHERE VBELN='$nomorso'";
            //             $query = oci_parse($conn, $str);
            //             oci_execute($query);
            //             $str = "UPDATE OR_TRANS_DTL_ICS SET NO_SO = '-',  STATUS_LINE='OPEN',LAST_UPDATED_BY='$user_name_in',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$nomorso'";
            //             $query = oci_parse($conn, $str);
            //             oci_execute($query);
            //             $show_ket .= 'Sales Order ' . $nomorso . ' has been success roolback ';
            //         } else {
            //             $rollbackso = false;
            //             $show_ket .= 'Sales Order ' . $nomorso . ' has been failed roolback';
            //         }
            //     // }
            // }


            $fce5->Close();
            $sap2->Close();
        }
            // 'Purchase Order has been made with a number : ' . $nopoplus
            if ($rollbackso) {
                $show_ket .= $show_ket_po;
                $pesan_telegram .= " Approve dengan no SO $nomorso (Approve By $user_name_in)";
            }else{
                $show_ket .= 'Sales Order has been made with a number : ' . $nomorso ;
                $show_ket .= '<br>';
                $show_ket .= $show_ket_po;
                $pesan_telegram .= " Approve dengan no SO $nomorso (Approve By $user_name_in)";
            }

            // if ($sales_org == 4000) {
            //     botTelegram('-394888712', $pesan_telegram);
            // }
        }
        if (isset($_POST['lasthalaman'])) {
            $habis = "list_approve_pp_ics.php";
        } else {
            $habis = "approve_pp_ics.php";
        }
        break;
    //============================================================================================================================
    



}
?>
