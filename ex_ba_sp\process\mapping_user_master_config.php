<?php
session_start();

include_once ('../helper.php');
include ('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

switch ($action) {
    case 'get':
        $sql = "SELECT eumc.ID, tub.NAMA_LENGKAP AS NAMA_USER, emc.NAMA AS NAMA_CONFIG, \"VALUE\" 
                FROM EX_USER_MASTER_CONFIG eumc 
                LEFT JOIN EX_MASTER_CONFIG emc 
                ON eumc.CONFIG_ID = emc.ID 
                LEFT JOIN TB_USER_BOOKING tub
                ON tub.ID = eumc.USER_ID ORDER BY NAMA_LENGKAP";

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        
        $data = array();

        while($row=oci_fetch_array($query)){
            array_push($data, $row);
        }

        echo json_encode($data);

        break;
  
    case 'get_users':
        $data = get_users($conn);
        echo json_encode($data);

        break;
    case 'get_configs':
        $sql = "SELECT ID,NAMA FROM EX_MASTER_CONFIG WHERE ORG = '3000'";

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        
        $data = array();

        while($row=oci_fetch_array($query)){
            array_push($data, $row);
        }

        echo json_encode($data);

        break;
    case 'insert':
        $user_id  = $_POST['user_id'];
        $config_id = $_POST['config_id'];
        $by  = $_SESSION['user_name'];
        
        try {
            $sql = "SELECT 1 FROM EX_USER_MASTER_CONFIG WHERE USER_ID = :user_id AND CONFIG_ID = :config_id";
            $query = oci_parse($conn, $sql);
            oci_bind_by_name($query, ':user_id', $user_id);
            oci_bind_by_name($query, ':config_id', $config_id);
            oci_execute($query);

            if (oci_fetch($query)) {
                $result = array(
                    'status' => 'success'
                );
                echo json_encode($result);
                
                exit;
            }

            $field_names = array('USER_ID', 'CONFIG_ID', 'CREATED_BY', 'CREATED_DATE');
            $field_data = array("$user_id", "$config_id", "$by", "SYSDATE");
            $tablename = "EX_USER_MASTER_CONFIG";
            $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
            $result = array(
                'status' => 'success'
            );
            echo json_encode($result);
        } catch (Exception $e) {
            $result = array(
                'status' => 'error',
                'message' => $e->getMessage()
            );
            echo json_encode($result);
        }

        break;

    case 'delete':
        $id = $_POST['userconfig_id'];

        $tablename = "EX_USER_MASTER_CONFIG";
        $field_id = array('ID');
        $value_id = array("$id");
        
        try {
            $fungsi->delete($conn, $tablename, $field_id, $value_id);
            $result = array(
                'status' => 'success'
            );
            echo json_encode($result);
        } catch (Exception $e) {
            $result = array(
                'status' => 'error',
                'message' => $e->getMessage()
            );
            echo json_encode($result);
        }

    break;
  
    default:
      // ⚠️ Unknown action
      $result = array(
        'status' => 'error',
        'message' => 'Invalid action'
        );
      echo json_encode($result);
      break;
  }
?>