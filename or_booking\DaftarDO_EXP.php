<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$bo_conn=$fungsi->bo_koneksi();
$halaman_id=3363;
$user_id=$_SESSION['user_id'];
$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];


//echo "icin";
//exit();

$page="DaftarDo_exp.php";
$lastpage="DaftarDo_exp.php";
$action= $_REQUEST['action'];
$isian= strtoupper($_REQUEST['isian']);
$hal= $_REQUEST['hal'];
$plant=$_REQUEST['plant'];
$sold_to=$_REQUEST['sold_to'];
$tanggal= $_REQUEST['tanggal'];
$tanggal1= $_REQUEST['tanggal1'];
$distributor_id=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$_SESSION['user_id'],"DISTRIBUTOR_ID");
$distributor_agen=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$_SESSION['user_id'],"NAMA_DISTRIBUTOR");
$agen_id=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$_SESSION['user_id'],"AGEN_ID");
$agen_name=$fungsi->findOneByOne($bo_conn,"TB_AGEN","ID",$agen_id,"NAMA_LENGKAP");

$komen="";
include ('formula.php'); 
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi Booking Online: Search Jadwal Exp Data DO  :)</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Jadwal Exp Data DO </th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
	

		
		<tr>
    <td width="175"><strong>Plant </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2"><? $mialo1= "select * from TB_PLANT ";
				$querymialo1= oci_parse($bo_conn, $mialo1);
				oci_execute($querymialo1);
			?><select name="plant" id="plant" onChange="document.form1.nama_plant.value=this.options[this.selectedIndex].title"/><option value="">--Pilih Plant--</option>
			<?
			while($datamia=oci_fetch_array($querymialo1))
			{
			echo("<option value='$datamia[KODE_PLANT]' title='$datamia[DESC_PLANT]'>$datamia[KODE_PLANT] - $datamia[DESC_PLANT]</option>");
			}
			 ?>
			</select>
		    <input name="nama_plant" type="hidden" id="nama_plant" value="" readonly="true"size="20"/>
	</td>
	</tr>
		<tr>
    <td width="175"><strong>Distributor </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2"><? $mialo1= "select * from TB_DISTRIBUTOR ";
				$querymialo1= oci_parse($bo_conn, $mialo1);
				oci_execute($querymialo1);
			?><select name="sold_to" id="sold_to" /><option value="">--Pilih Distributor--</option>
			<?
			while($datamia=oci_fetch_array($querymialo1))
			{
			echo("<option value='$datamia[ID]' title='$datamia[NAMA_LENGKAP]'>$datamia[ID] - $datamia[NAMA_LENGKAP]</option>");
			}
			 ?>
			</select>
		    <input name="nama_DIST" type="hidden" id="nama_DIST" value="" readonly="true"size="20"/>
	</td>
	</tr>
		
		
	
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2"><input name="Submit" type="submit" class="button" value="Show" /></td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>

<?
echo" <div align='center' class='error1'>";

 $sqlexp="select to_char(TGL_EXPIRED,'DD-MM-YYYY') as TGL_EXPIRED,to_char(TGL_DO,'DD-MM-YYYY') as TGL_DO from EXPIRED_DO where PLANT='$plant'";
        $queryexp= oci_parse($bo_conn, $sqlexp);
	   oci_execute($queryexp);
        $dataexp=oci_fetch_array($queryexp);
        $tgldo=$dataexp['TGL_DO'];
        $tglexp=$dataexp['TGL_EXPIRED'];
        $ada=oci_num_rows($queryexp);
if(isset($_REQUEST['isian'])|| isset($_REQUEST['plant'])|| isset($_REQUEST['sold_to'])){
//TB_DO_VS_DISTRIBUTOR_AGEN.DISTRIBUTOR_ID='$distributor' AND

    $mialo="SELECT TB_DO_VS_DISTRIBUTOR_AGEN.*,TB_DO_DATA.* 
	from TB_DO_DATA INNER JOIN TB_DO_VS_DISTRIBUTOR_AGEN ON 
	(TB_DO_DATA.ORDER_ID=TB_DO_VS_DISTRIBUTOR_AGEN.ORDER_ID) 
	WHERE TB_DO_VS_DISTRIBUTOR_AGEN.STATUS_ORDER < '3' 
	AND TB_DO_VS_DISTRIBUTOR_AGEN.DELETE_MARK ='0' 
	AND TB_DO_DATA.PLANT='$plant' AND 
	TB_DO_VS_DISTRIBUTOR_AGEN.DISTRIBUTOR_ID='$sold_to' and 
	TB_DO_DATA.ORDER_DATE + INTERVAL '30' DAY <=SYSDATE 
	AND TB_DO_VS_DISTRIBUTOR_AGEN.TANGGAL_ACTIVE <= SYSDATE 
	ORDER BY TB_DO_DATA.ORDER_DATE DESC";
	//echo "$mialo";
	
	//$mialo="Select * from TB_DO_DATA";
	

	$querymialo= oci_parse($bo_conn, $mialo);
	oci_execute($querymialo);
	while($datamia=oci_fetch_array($querymialo))
	{
	$id_view[]=$datamia['ID'];
	$plant1[]=$datamia['PLANT'];
	$no_do[]=$datamia['ORDER_NO'];
	$no_so[]=$datamia['PRESALES_ORD_NO'];
	$distributor_view[]=$datamia['DISTRIBUTOR'];
	$tipe_semen_view[]=$datamia['TIPE_SEMEN'];
	//$tgl_active_view[]=$datamia['ORDER_DATE'];
	$status_order_view[]=$datamia['STATUS_ORDER'];
	$toko[]=$datamia['SHIPTO'];
	$distik[]=$datamia['DISTRIK'];
	$kota_tujuan_view[]=$datamia['KOTA_TUJUAN'];
	$qty_view[]=$datamia['QTY_1'];
	//$tgl_expired[] = date('d-m-Y',strtotime( '+30 days' , strtotime ($datamia['ORDER_DATE'])) );
	// $tgl_expired[] = date('d-m-Y',strtotime( '+30 days' , strtotime ($datamia['ORDER_DATE'])) );
	 $tgl_active_view[]=date('d-m-Y',strtotime ($datamia['ORDER_DATE']));
	$tgl[]=date('d-m-Y');
	
	 if ($ada>0) {
                    if (strtotime($datamia['ORDER_DATE'])<strtotime($tgldo)){ 
                        $tgl_expired[]=$tglexp;
                    } else { 
                        $tgl_expired[] = date('d-m-Y',strtotime( '+30 days' , strtotime ($datamia['ORDER_DATE'])));
                    }
                } else {
                        $tgl_expired[] = date('d-m-Y',strtotime( '+30 days' , strtotime ($datamia['ORDER_DATE'])) );
                }
         
	}

	}  
	$total=count($id_view);
	include ('../include/ulang.php');
	if($table){
?>
<p></p>
<div align="center">
<table width="800" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Daftar Distribusi DO </span></th>
</tr>
</table>
</div> 
<div align="center">
<table width="800" align="center" class="adminlist">
  <tr class="quote">
    <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
	 <td align="center"><strong>Plant</strong></td>
	 <td align="center"><strong>Nomor SO</strong></td>
	 <td align="center"><strong>Nomor DO</strong></td>
     <td align="center">Tipe Semen </td>
     <td align="center"><strong>Tanggal Create</strong></td>
	  <td align="center"><strong>Tanggal EXP</strong></td>
	  <td align="center"><strong>Distributor</strong></td>
    <td align="center"><strong>Toko</strong></td>
	<td align="center"><strong>Distrik</strong></td>
	<td align="center"><strong>QTY</strong></td>
     <td align="center"><strong>Status </strong></td>
	
	 
	
	 
  </tr>
  <?  for($i=$awal; $i<$maks;$i++) {
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
	$b=$i+1;
 	?>
    <td align="center"><? echo $b; ?></td>
	<td align="center"><? echo $plant1[$i]; ?></td>
	<td align="center"><? echo $no_so[$i]; ?></td>
	<td align="center"><? echo $no_do[$i]; ?></td>
    <td align="center"><? echo $tipe_semen_view[$i]; ?></td>
	<td align="center"><? echo $tgl_active_view[$i]; ?></td>
    <td align="center"><? echo $tgl_expired[$i];?></td>
   
    <td align="center"><? echo $distributor_view[$i]; ?></td>
	<td align="center"><? echo $toko[$i]; ?></td>
	<td align="center"><? echo $kota_tujuan_view[$i]; ?></td>
	<td align="center"><? echo $qty_view[$i]; ?></td>
    <td align="center"><? echo $status_order_view[$i]." / ".$fungsi->status_order($status_order_view[$i]); ?></td>
	<td align="center"><? ?> 

 </tr>
  <? } ?>
</table>

	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
<?
$terus= "satu";
include ('../include/komponen.php');
}?>
<div align="center">
  <p>
    <?
echo $komen;

?>
<div align="center">
<form name="export" method="post" action="DaftarDo_exp_xls.php" target="_blank">
<input name="plant" type="hidden" id="org" value="<?=$_POST['plant']?>"/>
<input name="sold_to" type="hidden" id="org" value="<?=$_POST['sold_to']?>"/>
<input name="tanggal" type="hidden" id="org" value="<?=$_POST['tanggal']?>"/>
<input name="tanggal1" type="hidden" id="org" value="<?=$_POST['tanggal1']?>"/>

&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="Export" /> 	
  <p>
    <input type="button" name="Submit2" value="Print" onClick="javascript:window.print();" />
  </p>
</div>

</p><br><br>

</body>
</html>
