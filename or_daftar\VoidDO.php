<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$bo_conn=$fungsi->bo_koneksi();
$halaman_id=1733;
$user_id=$_SESSION['user_id'];
$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];

$mialo= "SELECT PLANT FROM TB_USER_VS_PLANT WHERE USER_ID='$user_id' AND DELETE_MARK=0";
$querymialo= oci_parse($conn, $mialo);
oci_execute($querymialo);
while($datamia=oci_fetch_array($querymialo)){
	$provnyo[]="'".$datamia['PLANT']."'";
}
$jmlnya=count($provnyo);
$str_plant=$provnyo[0];
for ($z=1;$z<$jmlnya;$z++) {
	$str_plant=$str_plant.','.$provnyo[$z];
}

$page="VoidDO.php";
$lastpage="VoidDO.php";
$action= $_REQUEST['action'];
$isian= strtoupper($_REQUEST['isian']);
$hal= $_REQUEST['hal'];
$user_id= $_SESSION['user_id'];

$komen="";
if (isset($_POST['void'])) {
	$total_cek=$_POST['total'];
	$ORGN_CODE = '';
	$order_id = '';
	for($j=0; $j<$total_cek;$j++) { 
		$nama_box_cek="box_do".$j;
		if(isset($_POST[$nama_box_cek]) and $_POST[$nama_box_cek] != ""){
			$order_id=$_POST[$nama_box_cek];
	
			$ambilmialo= "SELECT A.ORGN_CODE, B.DAFTAR_ID
			FROM TB_DO_DATA A
			LEFT JOIN TB_DAFTAR_DETAIL B ON (A.ORDER_ID = B.ORDER_ID)
			WHERE A.ORDER_ID = '$order_id'";
			$queryambil= oci_parse($bo_conn, $ambilmialo);
			oci_execute($queryambil);
			$datarq=oci_fetch_array($queryambil);
			$ORGN_CODE = $datarq['ORGN_CODE'];
			$ID_DAFTAR = $datarq['DAFTAR_ID'];
			
			$sap = new SAPConnection();
			$sap->Connect("../include/sapclasses/logon_data.conf");
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce = $sap->NewFunction ("Z_ZCSD_IO052_CLEAR_BOLNR");
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce->I_VBELN = $order_id;	
			$fce->Call();
			if ($fce->GetStatus() == SAPRFC_OK ) {
				$fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
				$fce->Call();			
				$fce->Close();
			}
			###VOID DO
			$fce = $sap->NewFunction ("Z_ZCSD_DEL_DO");
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce->VBELN = $order_id;
			$fce->Call();
			if ($fce->GetStatus() == SAPRFC_OK ) {
				$fce->RETURN->Reset();
				while ( $fce->RETURN->Next() ){
					echo '<div align="center"><br>';
					$error=$fce->RETURN->row["TYPE"];
					$NUMBER = $fce->RETURN->row["NUMBER"];
					echo $fce->RETURN->row["MESSAGE"];
				}
				echo '<br></div>';
				$fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
				$fce->Call();
				$fce->Close();

				if ($error=='S'){
					$ket=$_POST['ket'];
					$str = "UPDATE TB_DO_DATA SET KET='$ket',DELETE_MARK=1,LAST_UPDATED_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ORDER_ID='$order_id'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
					$str = "UPDATE TB_DO_VS_DISTRIBUTOR_AGEN SET DELETE_MARK=1,LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ORDER_ID='$order_id'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);					
					###VOID BOOOKING
					$str = "UPDATE TB_DAFTAR SET DELETE_MARK='1',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ID ='$ID_DAFTAR'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
					$str = "UPDATE TB_DAFTAR_DETAIL SET DELETE_MARK='1' WHERE  DAFTAR_ID ='$ID_DAFTAR'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
					
					IF($datarq['ORGN_CODE'] == '7900'){
						$mialo= "SELECT ALAMAT,ID_USERNAME,API_KEY FROM TB_API WHERE KODE='MD04' AND DELETE_MARK=0 AND APLIKASI='EPOOOL' AND COMPANY = '7900'";
					}ELSE{
						$mialo= "SELECT ALAMAT,ID_USERNAME,API_KEY FROM TB_API WHERE KODE='EP04' AND DELETE_MARK=0 AND APLIKASI='EPOOOL' AND COMPANY = '3000'";
					}
					$querymialo= oci_parse($bo_conn, $mialo);
					oci_execute($querymialo);
					while($datamia=oci_fetch_array($querymialo)){
						$api=$datamia['ALAMAT'];
						$id=$datamia['ID_USERNAME'];
						$key=$datamia['API_KEY'];
					}
					 
					//=====================================api=============================
					$postdata = http_build_query(
					array('id_username' => $id,
						  'api_key' => $key,
						  'no_referensi' => $order_id
					));

					$opts = array('http' =>
					array('method'  => 'POST',
						'header'  => 'Content-type: application/x-www-form-urlencoded',
						'content' => $postdata
					));

					$context  = stream_context_create($opts);
					$result = file_get_contents($api, false, $context);
					echo '<div align="center"><br>Data DO '.$order_id.' telah di Void<br></div>';		
				} else { echo '<div align="center">DO Gagal di Void, Cek Data DO di SAP</div>';}
				
				IF($NUMBER == '302'){
					$ket=$_POST['ket'];
					###VOID DO
					$str = "UPDATE TB_DO_DATA SET KET='$ket',DELETE_MARK=1,LAST_UPDATED_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ORDER_ID='$order_id'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
					###VOID DO AGEN
					$str = "UPDATE TB_DO_VS_DISTRIBUTOR_AGEN SET DELETE_MARK=1,LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ORDER_ID='$order_id'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);					
					###VOID BOOOKING
					$str = "UPDATE TB_DAFTAR SET DELETE_MARK='1',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='$user_id' WHERE ID ='$ID_DAFTAR'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
					$str = "UPDATE TB_DAFTAR_DETAIL SET DELETE_MARK='1' WHERE  DAFTAR_ID ='$ID_DAFTAR'";
					$query= oci_parse($bo_conn, $str);
					oci_execute($query);
				}
			} else
				$fce->PrintStatus();
				$sap->Close();
		}

	}
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<script>
function popUp(URL) 
{
	day = new Date();
	id = day.getTime();
	eval("page" + id + " = window.open(URL, '" + id + "', 'toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=1,width=700,height=600,left = 34,top = 102');");
}
checked=false;
function checkedAll (frm1) {
	var aa= document.getElementById('fsimpan');
	 if (checked == false)
          {
           checked = true
          }
        else
          {
          checked = false
          }
	for (var i =0; i < aa.elements.length; i++) 
	{
	 aa.elements[i].checked = checked;
	}
      }

</script>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi Booking Online: Search Data DO Distributor Agen :)</title>
<script language="javascript" type="text/javascript">
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

	function getdoinduk(distributor_id) {		
		var strURL="carisovoid.php?distributor_id="+distributor_id;
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('nosodiv').innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
</script>

</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar DO Distributor</th>
</tr></table></div>
<?php 
		if(!isset($_POST['Submit']))
		{
		?>
<form  id="form1" name="form1" method="post" action="VoidDO.php">
		<table width="500" align="center" class="adminform">
			<tr height="30">
			  <td width="180" class="puso">&nbsp;</td>
			  <td width="14" class="puso">&nbsp;</td>
			  <td width="290">&nbsp;</td>
			  </tr>
			<tr>
				<td>Entrikan Nomor SO atau Nomor DO</td>
				<td>&nbsp;</td>
				<td><input name="isian" type="text" id="isian" value="" size="10"/></td>
			</tr>
			<tr>
				<td>Pilih Sistem DO atau SO</td>
				<td>&nbsp;</td>
				<td>
					<select name="oncheck" id="oncheck">
						<option value='P'>Menggunakan FOT Epoool</option>
						<option value='T'>Tidak Menggunakan Epoool</option>
					</select>
				</td>
			</tr>
			<tr>
				<td>&nbsp;</td>
				<td>&nbsp;</td>
				<td><input name="Submit" type="submit" class="button" value="Show" /></td>
			</tr>
			<tr>
				<td>&nbsp;</td>
				<td>&nbsp;</td>
			</tr>
		</table>
</form>
<?
		}
echo" <div align='center' class='error1'>";
if(isset($_REQUEST['isian']) || isset($_REQUEST['hal'])){
$isian1=$_REQUEST['isian'];
$oncheck=$_REQUEST['oncheck'];
$isian=$fungsi->sapcode($isian1);
IF($oncheck == 'P'){
	$mialo= "SELECT B.PLANT,B.ORDER_ID,B.ORDER_NO,B.PRESALES_ORD_NO,B.DISTRIBUTOR,B.ITEM_NO,B.TIPE_SEMEN,B.KOTA_TUJUAN,INCOTERM,
	B.QTY_1,B.ORDER_DATE,B.TGL_SCEDULE,D.DAFTAR_ID, D.DELETE_MARK AS DELETE_DETAIL,E.STATUS_UPLOAD AS STATUS,E.DELETE_MARK AS DELETE_DAFTAR,
	E.STATUS_UPLOAD,E.STATUS_CHECKIN,B.ORGN_CODE
	FROM TB_DO_DATA B 
	LEFT JOIN TB_DAFTAR_DETAIL D ON (B.ORDER_ID=D.ORDER_ID AND D.DELETE_MARK = '0') 
	LEFT JOIN TB_DAFTAR E ON (D.DAFTAR_ID=E.ID AND D.DELETE_MARK = '0') 
	WHERE B.DELETE_MARK=0  
	AND B.PLANT IN ($str_plant)  
	AND B.INCOTERM <> 'FRC' 
	AND (B.ORDER_NO='$isian' OR B.PRESALES_ORD_NO='$isian') ORDER BY PLANT,PRESALES_ORD_NO,ORDER_ID ASC";
	$querymialo= oci_parse($bo_conn, $mialo);
	oci_execute($querymialo);
	while($datamia=oci_fetch_array($querymialo)){
		IF($datamia['STATUS_CHECKIN'] != '1' AND $datamia['STATUS_UPLOAD'] != '1') {
			$sap = new SAPConnection();
			$sap->Connect("../include/sapclasses/logon_data.conf");
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK ) {
			   $sap->PrintStatus();
			   exit;
			}	
			
			$fce = $sap->NewFunction ("ZCSD_CEK_REFF_TRNS_MD");
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce->I_VKORG = $datamia['ORGN_CODE'];
			$fce->I_WERKS = $datamia['PLANT'];
			$fce->I_NO_DO = $fungsi->sapcode($datamia['ORDER_ID']);
			$fce->Call();
			if ($fce->GetStatus() == SAPRFC_OK ) {	
				IF($fce->WA_HDR1['NO_TRANSAKSI'] == '' AND  $fce->WA_HDR2['NO_TRANSAKSI'] == ''){
					$VBELN1 = $fce->WA_LIKP1['VBELN'];
					$VSTEL1 = $fce->WA_LIKP1['VSTEL'];
					$VKORG1 = $fce->WA_LIKP1['VKORG'];
				}
			}
			
			IF($fungsi->sapcode($VBELN1) == $fungsi->sapcode($datamia['ORDER_ID'])){
				$id_view[]=$datamia['ORDER_ID'];
				$distributor_view[]=$datamia['DISTRIBUTOR'];
				$tipe_semen_view[]=$datamia['TIPE_SEMEN'];
				$sales_no_view[]=$datamia['PRESALES_ORD_NO'];
				$order_no_view[]=$datamia['ORDER_NO'];
				$status_order_view[]=$datamia['STATUS'];
				$item_no_view[]=$datamia['ITEM_NO'];
				$kota_tujuan_view[]=$datamia['KOTA_TUJUAN'];
				$qty_view[]=$datamia['QTY_1'];
				$tgl_exp[]=$fungsi->findOneByOne($conn,"OR_TRANS_APP","NO_SO",$datamia['PRESALES_ORD_NO'],"EXPIRE_DATE");
				$plant1[]=$datamia['PLANT'];
				$ORDER_DATE[]=$datamia['ORDER_DATE'];
			}				
		}
	}
}ELSEIF($oncheck == 'T'){
	$mialo= "SELECT * FROM (SELECT PLANT,ORDER_ID,ORDER_NO,PRESALES_ORD_NO,DISTRIBUTOR,ITEM_NO,TIPE_SEMEN,KOTA_TUJUAN,TGL_SCEDULE,
	QTY_1,ORDER_DATE,(SELECT STATUS_ORDER FROM TB_DO_VS_DISTRIBUTOR_AGEN 
	WHERE TB_DO_VS_DISTRIBUTOR_AGEN.ORDER_ID=TB_DO_DATA.ORDER_ID AND DELETE_MARK=0) AS STATUS 
	FROM TB_DO_DATA WHERE DELETE_MARK=0 AND OP_ORDR_TYPE<>'ZNL' AND PLANT IN ($str_plant) AND 
	(ORDER_NO='$isian' OR PRESALES_ORD_NO='$isian') ORDER BY PLANT,PRESALES_ORD_NO,ORDER_ID ASC)a WHERE 
	(STATUS=0 OR STATUS IS NULL)";
	echo $mialo;
	$querymialo= oci_parse($bo_conn, $mialo);
	oci_execute($querymialo);
	while($datamia=oci_fetch_array($querymialo)){
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   $sap->PrintStatus();
		   exit;
		}			
		$fce = &$sap->NewFunction("Z_ZAPPSD_SELECT_TRANS_DTL2_N1");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}
		$fce->XPARAM["NO_DO"] = $datamia['ORDER_ID'];
		$fce->XDATA_APP["NMORG"] = $datamia['ORGN_CODE'];
		$fce->XDATA_APP["NMPLAN"] = $datamia['PLANT'];
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			IF ($fce->RETURN['TYPE'] == 'E') {
				$id_view[]=$datamia['ORDER_ID'];
				$distributor_view[]=$datamia['DISTRIBUTOR'];
				$tipe_semen_view[]=$datamia['TIPE_SEMEN'];
				$sales_no_view[]=$datamia['PRESALES_ORD_NO'];
				$order_no_view[]=$datamia['ORDER_NO'];
				$status_order_view[]=$datamia['STATUS'];
				$item_no_view[]=$datamia['ITEM_NO'];
				$kota_tujuan_view[]=$datamia['KOTA_TUJUAN'];
				$qty_view[]=$datamia['QTY_1'];
				$tgl_exp[]=$fungsi->findOneByOne($conn,"OR_TRANS_APP","NO_SO",$datamia['PRESALES_ORD_NO'],"EXPIRE_DATE");
				$plant1[]=$datamia['PLANT'];
				$ORDER_DATE[]=$datamia['ORDER_DATE'];
			}
		}
	}
}	
	$total=count($id_view);
	include ('../include/ulang.php');
	if($table){
		
		$mialo_plant="SELECT * FROM TB_PLANT";
	$querymialo_plant= oci_parse($bo_conn, $mialo_plant);
	oci_execute($querymialo_plant);
	while($datamia_plant=oci_fetch_array($querymialo_plant))
	{
	//echo $umur_do=$datamia_plant['UMURDO']; 
	 $umur[$datamia_plant['KODE_PLANT']]=$datamia_plant['UMURDO']; 
	}
?>
<p></p>
<div align="center">
<table width="800" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Daftar Distribusi DO : <?=$distributor_view[0]?></span></th>
</tr>
</table>
</div> 
<div align="center">
<form  id="fsimpan" name="fsimpan" method="post" action="" onSubmit="validasi('total','','R');ask();return document.hasil">
<table width="800" align="center" class="adminlist">
	  <tr class="quote">
		<td width='100px'><strong>Keterangan Void</strong></td>
		<td>
			<textarea name='ket' cols='50' required rows='2'></textarea>
		</td>
	</tr>

</table>
<table width="800" align="center" class="adminlist">
  <tr class="quote">
    <td align="center"><strong>No.</strong></td>
	<td align="center"><strong><input type="button" class="button" onClick="checkedAll(fsimpan);" value="Pilih Semua"></strong></td>
	<td align="center"><strong>Distributor</strong></td>
	 <td align="center"><strong>Nomor SO</strong></td>
	 <td align="center"><strong>Nomor DO</strong></td>
      <td align="center"><strong>Item No </strong></td>
     <td align="center"><strong>Tipe Semen</strong> </td>
    <td align="center"><strong>Tujuan</strong></td>
	<td align="center"><strong>QTY</strong></td>
	 <td align="center"><strong>Tanggal Exp</strong></td>
	<td align="center"><strong>STATUS</strong></td>
  </tr>
  <?  for($i=$awal; $i<$total;$i++) {
 		$nama_box="box_do".$i;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
	$b=$i+1;
 		if (isset($_POST[$nama_box]) and $cek_pilihan_do){
			$nama_box="box_do".$i;
			if ($_POST[$nama_box] == $order_id_view[$i]){
			$pilihan_do="checked";
			}
			$cek_do="disabled='disabled'";
		}else {
		$pilihan_do="";
		}
	?>
	<td align="center"><? echo $b; ?></td>
	<td align="center">
	<? $nilai=$order_no_view[$i];
	echo "<input name=$nama_box type='checkbox' $pilihan_do value='$nilai' $cek_do />";
	?>
	</td>    
	 <td align="center"><? echo $distributor_view[$i]; ?></td>
	<td align="center"><? echo $sales_no_view[$i]; ?></td>
	<td align="center"><? echo $order_no_view[$i]; ?></td>
    <td align="center"><? echo $item_no_view[$i]; ?></td>
    <td align="center"><? echo $tipe_semen_view[$i]; ?></td>
    <td align="center"><? echo $kota_tujuan_view[$i]; ?></td>
	<td align="center"><? echo $qty_view[$i]; ?></td>
	 <td align="center"><?  echo date('Y-m-d',strtotime( '+'.$umur[$plant1[$i]].' days' , strtotime ($ORDER_DATE[$i]))); ?></td>
	
    <td align="center"><? echo $status_order_view[$i]." / ".$fungsi->status_order($status_order_view[$i]); ?></td>

 </tr>
  <? } ?>
</table>
<p>
<input name="sonumber" type="hidden" value="<?=$noso?>"/>
<input name="total" type="hidden" size="80" value="<?=$i?>"/>
<input name="void" type="submit" class="button" value="Void"/>&nbsp;&nbsp;
<input name="batal" type="submit" class="button" value="Batal"/>
</form>
</div>
<?
$terus= "satu";
//include ('../include/komponen.php');
}?>
<div align="center">
  <p>
<?
echo $komen;
?>
</div>
<? } ?>
<p>&nbsp;</p>
<? include ('ekor.php'); ?>
</body>
