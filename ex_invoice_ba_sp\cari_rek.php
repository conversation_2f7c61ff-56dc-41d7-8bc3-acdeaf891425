<?
session_start();
include('../include/sapclasses/sap.php');
require_once ('../security_helper.php');
sanitize_global_input();
$currentPage="cari_rek.php";

$no_vendor = $_REQUEST['no_vendor'];
//$no_vendor="**********";

if(isset($_REQUEST['isian'])){ 
	$isian = $_REQUEST['isian'];
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_SEL_BANK");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
	
		$fce->XLIFNR = $no_vendor;

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			while ( $fce->ZDATA->Next() ){
				$lifnr[]= $fce->ZDATA->row["LIFNR"];
				$banka[]= $fce->ZDATA->row["BANKA"];
				$brnch[]= $fce->ZDATA->row["BRNCH"];
				$bankn[]= $fce->ZDATA->row["BANKN"];
				$bvtyp[]= $fce->ZDATA->row["BVTYP"];
		}
		}else
   		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	

}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>
<script>
<!--
function setForm() {
	var btn = document.getElementById("cekdata"); 
	if(btn.value != 0){
	var kenya=btn.value;
	var bvtyp='bvtyp'+kenya;
	var komponen_bvtyp=document.getElementById(bvtyp); 
	var acc_br='acc_branch'+kenya;
	var komponen_acc_br=document.getElementById(acc_br); 
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var cek;
	var komponen_acc_id=document.getElementById(acc_id); 
	opener.document.getElementById("no_rek").value = komponen_acc_id.value;
	opener.document.getElementById("nama_bank").value = komponen_acc_no.value;
	opener.document.getElementById("cabang_bank").value = komponen_acc_br.value;
	opener.document.getElementById("bvtyp").value = komponen_bvtyp.value;

    self.close();
	}else
		{
			alert('Pilih Data Account Vendor Dahulu')
			return false;
		}
}
//-->
</script>
<script> 
function checkForother(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
} 
</script> 
<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<script type="text/javascript">

function addLoadEvent(func) {
  var oldonload = window.onload;
  if (typeof window.onload != 'function') {
    window.onload = func;
  } else {
    window.onload = function() {
      oldonload();
      func();
    }
  }
}

function addClass(element,value) {
  if (!element.className) {
    element.className = value;
  } else {
    newClassName = element.className;
    newClassName+= " ";
    newClassName+= value;
    element.className = newClassName;
  }
}

function removeClassName(oElm, strClassName){
	var oClassToRemove = new RegExp((strClassName + "\s?"), "i");
	oElm.className = oElm.className.replace(oClassToRemove, "").replace(/^\s?|\s?$/g, "");
}


function stripeTables() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var i=0; i<tbodies.length; i++) {
				var odd = true;
				var rows = tbodies[i].getElementsByTagName("tr");
				for (var j=0; j<rows.length; j++) {
					if (odd == false) {
						odd = true;
					} else {
						addClass(rows[j],"odd");
						odd = false;
					}
				}
			}
		}
	}
}
function highlightRows() {
  if(!document.getElementsByTagName) return false;
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			  var tbodies = tables[m].getElementsByTagName("tbody");
			  for (var j=0; j<tbodies.length; j++) {
				 var rows = tbodies[j].getElementsByTagName("tr");
				 for (var i=0; i<rows.length; i++) {
					   rows[i].oldClassName = rows[i].className
					   rows[i].onmouseover = function() {
						  if( this.className.indexOf("selected") == -1)
							 addClass(this,"highlight");
					   }
					   rows[i].onmouseout = function() {
						  if( this.className.indexOf("selected") == -1)
							 this.className = this.oldClassName
					   }
				 }
			  }
		}
	}
}

function selectRowRadio(row) {
	var radio = row.getElementsByTagName("input")[0];
	radio.checked = true;
	checkForother(radio);
}

function removeSelectedStateFromOtherRows() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var rows = tbodies[j].getElementsByTagName("tr");
				for (var i=0; i<rows.length; i++) {
					if (rows[i].className.indexOf("selected") != -1) {
						removeClassName(rows[i], "selected");
						removeClassName(rows[i], "highlight");
					}
				}
			}
		}
	}
}

function lockRow() {
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var rows = tbodies[j].getElementsByTagName("tr");
				for (var i=0; i<rows.length; i++) {
					rows[i].oldClassName = rows[i].className;
					rows[i].onclick = function() {
						if (this.className.indexOf("selected") != -1) {
							this.className = this.oldClassName;
						} else {
							removeSelectedStateFromOtherRows();
							addClass(this,"selected");
						}
						selectRowRadio(this);
					}
				}
			}
		}
	}
}

addLoadEvent(stripeTables);
addLoadEvent(highlightRows);
addLoadEvent(lockRow);


function lockRowUsingRadio() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var radios = tbodies[j].getElementsByTagName("input");
				for (var i=0; i<radios.length; i++) {
					radios[i].onclick = function(evt) {
						if (this.parentNode.parentNode.className.indexOf("selected") != -1){
							this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
						} else {
							removeSelectedStateFromOtherRows();
							addClass(this.parentNode.parentNode,"selected");
						}
						if (window.event && !window.event.cancelBubble) {
							window.event.cancelBubble = "true";
						} else {
							evt.stopPropagation();
						}
					}
				}
			}
		}
	}
}
addLoadEvent(lockRowUsingRadio);
</script>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Rekening</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar Data Rekening </th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		<td width="173" class="puso">Filter Data </td>
		<td width="26" class="puso">:</td>
		<td width="585"><input name="isian" type="text" class="" value="<? echo $isian; ?>" size="40"/>
		&nbsp;&nbsp;</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="Show" />
		<input name="org" id="org" type="hidden"  value="<?=$org?>" />
		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?
if(isset($_POST['isian'])){
$total=count($lifnr);
	if($total>0){
?>
		<p></p>
		<div align="center">
		<table width="800" align="center" class="adminlist">
		<tr>
		<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Vendor </span> </th>
		</tr>
		</table>
		</div> 
		<div align="center">
		<form  name="formKaryawan">
	<table id="test1" width="800" align="center" class="pickme">
	<thead >
		  <tr class="quote">
			<td><div align="center"><strong>&nbsp;&nbsp; Cek.</strong></div></td>
			<td align="center"><strong>Nama Bank  </strong></td>
			<td align="center"><strong>Cabang </strong></td>
			<td align="center"><strong>No Rekening </strong></td>
			</tr>
		  </thead>
		  <tbody >
		  <?  for($i=0; $i<$total;$i++) {
				if(($i % 2) == 0)	{	 
				echo "<tr class='odd0'>";
					}
				else	{	
				echo "<tr class='odd1'>";
					}	
			$b=$i+1;
			$acc_id="acc_id".$b;
			$acc_no="acc_no".$b;
			$acc_branch="acc_branch".$b;
			$bvtypke="bvtyp".$b;


			?>
			<td align="center"><input name="radiokaryawan" type="radio" value="<?=$b;?>" onChange="checkForother(this)" id="<?=$b?>"/>
			<input id="<?=$acc_id;?>" name="<?=$acc_id;?>" type="hidden" value="<?=$bankn[$i]?>" />
			<input id="<?=$bvtypke;?>" name="<?=$bvtypke;?>" type="hidden" value="<?=$bvtyp[$i]?>" />
			<input id="<?=$acc_branch;?>" name="<?=$acc_branch;?>" type="hidden" value="<?=$brnch[$i]?>" />
			<input id="<?=$acc_no;?>" name="<?=$acc_no;?>" type="hidden" value="<?=$banka[$i]?>" />	</td>    
			<td align="center"><? echo $banka[$i]; ?></td>
			<td align="center"><? echo $brnch[$i]; ?></td>
			<td align="center"><? echo $bankn[$i]; ?></td>
			</tr>
		  <? } ?>
	  </tbody>
		</table>
		</div>
		<div align="center">
	<?
	}else $komen = " Maaf.. <br> Tidak Ada Data Yang Di Temukan..";
	
	?>
  <br />
  <br />
  <input type="button" value="Oke" name="kartu" class="button" onClick="setForm()">
    <input type="button" name="Submit2" value="Cancel" onClick="window.close();" class="button" />
    <input id="cekdata" name="cekdata" type="hidden" value="0" />
    <input id="org" name="org" type="hidden" value="<?=$org?>" />
</div>
</form>

<div align="center">
<?
echo $komen;
}
?></div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
