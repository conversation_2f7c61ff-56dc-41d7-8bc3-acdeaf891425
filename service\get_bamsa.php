<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);

    if ($role['status'] == true && $jmlData > 0) {

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      $dirr = $_SERVER['PHP_SELF'];
      if (empty($token_in)) {
        $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap", "data" => null);
        header('Content-Type: application/json');
        echo json_encode($responseRequest);
      } else {
        // get data only SMBR BY Invoice Type
        if (isset($_POST['INVOICE_TYPE']) && $_POST['INVOICE_TYPE'] == "AR") {
          $param['COMPANY_CODE'] = "1000";
          $param['TAHUN'] = $_POST['TAHUN'];
          $param['VENDOR'] = $_POST['VENDOR'];
          $param['SALES_ORG'] = '';
          $param['DOCUMENT_NUMBER'] = $_POST['DOCUMENT_NUMBER'];
          $param['BASTB'] = $_POST['BAMSA']; 
        // General Data
        }else if (isset($_POST['INVOICE_TYPE']) && $_POST['INVOICE_TYPE'] == "AP") {
          $param['COMPANY_CODE'] = "";
          $param['TAHUN'] = $_POST['TAHUN'];
          $param['VENDOR'] = $_POST['VENDOR'];
          $param['SALES_ORG'] = '1000';
          $param['DOCUMENT_NUMBER'] = $_POST['DOCUMENT_NUMBER'];
          $param['BASTB'] = $_POST['BAMSA'];
        }else {
          $param['COMPANY_CODE'] = $_POST['COMPANY_CODE'];
          $param['TAHUN'] = $_POST['TAHUN'];
          $param['VENDOR'] = $_POST['VENDOR'];
          $param['SALES_ORG'] = $_POST['SALES_ORG'];
          $param['DOCUMENT_NUMBER'] = $_POST['DOCUMENT_NUMBER'];
          $param['BASTB'] = $_POST['BAMSA'];
          $param['BRAND'] = $_POST['BRAND'];
        }

        $param['TGL_CREATE_BASTB_FROM'] = $_POST['TGL_CREATE_BASTB_FROM'];
        $param['TGL_CREATE_BASTB_TO'] = $_POST['TGL_CREATE_BASTB_TO'];
        $param['I_TRANSACTION_TYPE'] = "R";

        // echo "<pre>";
        // print_r($param);
        // echo "</pre>";
        
        $get = new get_bamsa();
        $result = $get->get_data($param);
        $result2 = array();
        if (!empty($result)) {
          foreach ($result as $k => $v) {
            if (isset($_POST['INVOICE_TYPE'])) {
              if (strtoupper($_POST['INVOICE_TYPE']) == "AP") {
                if ($v['VKORG'] == "1000") {
                  if($param["BASTB"]){
                    if ($v["BAMSA"] == $param["BASTB"]) {
                      array_push($result2, $v);
                    }
                  }else {
                    array_push($result2, $v);
                  }
                }
              }else if (strtoupper($_POST['INVOICE_TYPE']) == "AR") {
                if ($v['KORPE'] == "1000") {
                  if($param["BASTB"]){
                    if ($v["BAMSA"] == $param["BASTB"]) {
                      array_push($result2, $v);
                    }
                  }else {
                    array_push($result2, $v);
                  }
                }
              }else {
                if ($v['KORPE'] == "1000" || $v['VKORG'] == "1000") {
                  if($param["BASTB"]){
                    if ($v["BAMSA"] == $param["BASTB"]) {
                      array_push($result2, $v);
                    }
                  }else {
                    array_push($result2, $v);
                  }
                }
              }
            }else {
              if($param["BASTB"]){
                if ($v["BAMSA"] == $param["BASTB"]) {
                  array_push($result2, $v);
                }
              }else {
                array_push($result2, $v);
              }
            }

          }
          foreach ($result2 as $k => $v) {
            $param2["I_BUKRS"] = $v["KORPE"];
            $param2["I_BASTB"] = $v["BAMSA"];
            $param2["I_DISP"] = "";

            $result_detail = $get->get_data_detail($param2);
            // Kondisi Total Royalty AR SMBR tidak pakai PPH
            // if ($v["KORPE"] == "1000") {
            //   $result2[$k]["WHTTAX"] = 0;
            //   $result2[$k]["TOTMS"] = $v["TOTMS"];
            //   if ($result_detail) {
            //     foreach ($result_detail as $kbil => $vbil) {
            //       $result_detail[$kbil]["WHTTAX"] = 0;
            //       $result_detail[$kbil]["TOTMS"] = $vbil["TOTMS"];
            //       if ($vbil["ITEM"]) {
            //         foreach ($vbil["ITEM"] as $kitem => $vitem) {
            //           $result_detail[$kbil]["ITEM"][$kitem] = $vitem;
            //           $result_detail[$kbil]["ITEM"][$kitem]["WHTTAX"] = 0;
            //           $result_detail[$kbil]["ITEM"][$kitem]["TOTMS"] = $vitem["TOTMS"];
            //         }
            //       }
            //     }
            //   }
            // }
            $result2[$k]["BILLING"] = $result_detail;

            $param3["I_BUKRS"] = $v["PEMILIK_BRAND"];
            $param3["I_BASTB"] = $v["BAMSA"];
            $param3["I_DTYPE"] = "";

            $file = array();
            $result_file = $get->get_bamsa_file($param3);
            if (count($result_file) > 0) {
              foreach ($result_file as $kk => $vv) {
                $fname = $vv["FNAME"];
                $urlFile = "https://dev-app1.sig.id/data/MD_Paperless/".$fname;
                $base64 = $fname != "" && file_exists($urlFile) ? base64_encode(file_get_contents($urlFile)) : "";
                if ($vv["DTYPE"] == "BA" ) {
                  if ($v["SPRD2"] == "X") {
                    $mapFile["TYPE"] = $vv['DDESCH'];
                    $mapFile["NAME"] = $fname;
                    $mapFile["BASE64"] = $base64;
                    array_push($file, $mapFile);
                  }
                }else if ($vv["DTYPE"] == "IV") {
                  if ($v["SMKPB"] == "X") {
                    $mapFile["TYPE"] = $vv['DDESCH'];
                    $mapFile["NAME"] = $fname;
                    $mapFile["BASE64"] = $base64;
                    array_push($file, $mapFile);
                  }
                }else {
                    $mapFile["TYPE"] = $vv['DDESCH'];
                    $mapFile["NAME"] = $fname;
                    $mapFile["BASE64"] = $base64;
                    array_push($file, $mapFile);
                }
              }
            }
            $result2[$k]["FILE"] = $file;
          }
        }
        
        if (empty($result2)) {
          $responseRequest = array(
            'responseCode' => 404,
            'responseMessage' => 'No Data Found',
            'data' => null
          );
          header('Content-Type: application/json');
          echo json_encode($responseRequest);
        } else {
          $responseRequest = array(
            'responseCode' => 200,
            'responseMessage' => "Success Data Found",
            'data' => $result2
          );
          header('Content-Type: application/json');
          echo json_encode($responseRequest);
        }
      }
    } else {
      $responseRequest = array(
        'responseCode' => 401,
        'responseMessage' => $role["keterangan"] ? $role["keterangan"] : "Data User Not Found",
        'data' => null
      );
      header('Content-Type: application/json');
      echo json_encode($responseRequest);
    }
    $byLog = 'get_bamsa';
    $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class get_bamsa
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function get_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZDIS_INT_MON_BASTMSA");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }
  
  function get_data_detail($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCFM_DISP_BASTMSA_DTL_MSA");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc_detail($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }
  
  function get_bamsa_file($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCFM_DISP_BAST_MSA_FILE");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc_bastb_file($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {
    $fce->COMPANY_CODE = $param['COMPANY_CODE'];
    $fce->TAHUN = $param['TAHUN'];
    $fce->SALES_ORG = $param['SALES_ORG'];
    $fce->VENDOR = !empty($param['VENDOR']) ? sprintf("%010s", $param['VENDOR']) : "";
    $fce->DOCUMENT_NUMBER = $param['DOCUMENT_NUMBER'];
    $fce->BAMSA = $param['BASTB'];
    $fce->BRAND = $param['BRAND'];
    
    $TGL_CREATE_BASTB_FROM = !empty($param['TGL_CREATE_BASTB_FROM']) ? strval(date('Ymd', strtotime($param['TGL_CREATE_BASTB_FROM']))) : "";
    $fce->TGL_CREATE_BASTB_FROM = $TGL_CREATE_BASTB_FROM;
    
    $TGL_CREATE_BASTB_TO = !empty($param['TGL_CREATE_BASTB_TO']) ? strval(date('Ymd', strtotime($param['TGL_CREATE_BASTB_TO']))) : "";
    $fce->TGL_CREATE_BASTB_FROM = $TGL_CREATE_BASTB_TO;

    $fce->I_TRANSACTION_TYPE = $param['I_TRANSACTION_TYPE'];
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->T_DATA->Reset();
      while ($fce->T_DATA->Next()) {
        $fce->T_DATA->row['NTGEW'] = (float) $fce->T_DATA->row['NTGEW'];
        $fce->T_DATA->row['NETWR'] = $fce->T_DATA->row['NETWR'] * 100;
        $fce->T_DATA->row['FWBAS'] = $fce->T_DATA->row['FWBAS'] * 100;
        $fce->T_DATA->row['FWSTE'] = $fce->T_DATA->row['FWSTE'] * 100;
        $fce->T_DATA->row['TOTMS'] = $fce->T_DATA->row['TOTMS'] * 100;
        $fce->T_DATA->row['WHTTAX'] = $fce->T_DATA->row['WHTTAX'] * 100;
        $fce->T_DATA->row['BRAND'] = $fce->T_DATA->row['BRAND'] == "SMBR-MDK" ? "SMBR" : $fce->T_DATA->row['BRAND'];
        $fce->T_DATA->row['BEGDA'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['BEGDA'])));
        $fce->T_DATA->row['ENDDA'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['ENDDA'])));
        $fce->T_DATA->row['CPUDT'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['CPUDT'])));
        $fce->T_DATA->row['DBAST2'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DBAST2'])));
        $fce->T_DATA->row['DPRD1'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRD1'])));
        $fce->T_DATA->row['DPRD2'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRD2'])));
        $fce->T_DATA->row['DPRC'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRC'])));
        $fce->T_DATA->row['DELDAT'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DELDAT'])));
        $fce->T_DATA->row['DSPG'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DSPG'])));
        $fce->T_DATA->row['DICM'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DICM'])));
        $this->_data['T_DATA'][] = $fce->T_DATA->row;
      }
    }
    return $this->_data['T_DATA'];
  }

  function rfc_detail($fce, $param)
  {
    $data_detail = array();
    $map_detail = array();
    $result_detail = array();
    
    $fce->I_BUKRS = $param['I_BUKRS'];
    $fce->I_BAMSA = $param['I_BASTB'];
    $fce->I_DISP = $param['I_DISP'];

    $fce->Call();
    
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->IT_DATA->Reset();
      while ($fce->IT_DATA->Next()) {
        $fce->IT_DATA->row['NTGEW'] = (float) $fce->IT_DATA->row['NTGEW'];
        $fce->IT_DATA->row['NETWR'] = $fce->IT_DATA->row['NETWR'] * 100;
        $fce->IT_DATA->row['FWBAS'] = $fce->IT_DATA->row['FWBAS'] * 100;
        $fce->IT_DATA->row['FWSTE'] = $fce->IT_DATA->row['FWSTE'] * 100;
        $fce->IT_DATA->row['TOTMS'] = $fce->IT_DATA->row['TOTMS'] * 100;
        $fce->IT_DATA->row['FKDAT'] = strval(date('Y-m-d', strtotime($fce->IT_DATA->row['FKDAT'])));
        $fce->IT_DATA->row['CPUDT'] = strval(date('Y-m-d', strtotime($fce->IT_DATA->row['CPUDT'])));
        $data_detail[] = $fce->IT_DATA->row;
      }
    }

    foreach ($data_detail as $key => $value) {
      $map_detail[$value["VBELN"]][] = $value;
    }

    $itr_result = 0;
    foreach ($map_detail as $key => $value) {
      foreach ($value as $key2 => $value2) {
        $result_detail[$itr_result]["BAMSA"] = $value2["BAMSA"];
        $result_detail[$itr_result]["BUKRS"] = $value2["BUKRS"];
        $result_detail[$itr_result]["VBELN"] = $value2["VBELN"];
        $result_detail[$itr_result]["VKORG"] = $value2["VKORG"];
        $result_detail[$itr_result]["FKDAT"] = $value2["FKDAT"];
        $result_detail[$itr_result]["NTGEW"] = $value2["NTGEW"];
        $result_detail[$itr_result]["NETWR"] = $value2["NETWR"];
        $result_detail[$itr_result]["FWBAS"] = $value2["FWBAS"];
        $result_detail[$itr_result]["FWSTE"] = $value2["FWSTE"];
        $result_detail[$itr_result]["TOTMS"] = $value2["TOTMS"];
        $result_detail[$itr_result]["WAERK"] = $value2["WAERK"];
        $result_detail[$itr_result]["ITEM"][] = $value2;
      }
      $itr_result++;
    }

    return $result_detail;
  }

  function rfc_bastb_file($fce, $param)
  {
    $data = array();

    $fce->I_BUKRS = $param['I_BUKRS'];
    $fce->I_BAMSA = $param['I_BASTB'];
    $fce->I_DTYPE = $param['I_DTYPE'];
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->T_BASTBF->Reset();
      while ($fce->T_BASTBF->Next()) {
        $data[] = $fce->T_BASTBF->row;
      }
    }
    return $data;
  }
}
