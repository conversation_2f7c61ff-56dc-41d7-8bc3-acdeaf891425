<?php
include('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$params = base64_decode($_GET['kode']);
$params = json_decode($params);
$no_ba = $params->no_ba;
$level = $params->level;

$query_ba = "SELECT
                EX_BA.ID,
                EX_BA.STATUS_BA
            FROM
                EX_BA
                JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
            WHERE 
                EX_BA.DELETE_MARK = '0' 
                AND EX_BA.NO_BA = :no_ba
            ORDER BY
                EX_BA.ID DESC";

$sql_ba = oci_parse($conn, $query_ba);

oci_bind_by_name($sql_ba, ":no_ba", $no_ba);

oci_execute($sql_ba);

$data = oci_fetch_array($sql_ba);
$status = $data['STATUS_BA'];
$isValid = false;
if ($data) {
    if($status >= 50 && $level == 'kabiro'){
        $isValid = true;
        $msg = "DOKUMEN TELAH DISETUJUI KABIRO TERKAIT";
    }else if($status >= 20 && $level == 'pejabat_eks'){
        $isValid = true;
        $msg = "DOKUMEN TELAH DISETUJUI PEJABAT EKSPEDITUR TERKAIT";
    }else{
        $msg = "DOKUMEN BELUM DISETUJUI ATAU TELAH DIREJECT";
    }
} else {
    $msg = "DOKUMEN TIDAK DITEMUKAN";
}

if($isValid){
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $msg; ?></div>
            <a href="<?= get_base_home() ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php
}else{
    ?>

    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-danger" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $msg; ?></div>
            <a href="<?= get_base_home() ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>

    <?php
}

?>