<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$titlepage='Mapping Plant Rencana Kirim';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];


$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
<SCRIPT LANGUAGE="JavaScript">
alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
</SCRIPT>
<a href="../login.php">Login....</a>
<?

exit();
}

?>

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title><?=$titlepage;?></title>
    <!-- import easyui -->
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
    <script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
    <script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>

</head>

<body>

    <div align="center">
        <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px" idField="itemid"
            rownumbers="true" pagination="true">
            <thead>
                <tr>
                    <!-- <th field="ck" checkbox="true"></th> -->
                    <th field="ID" align="center">ID</th>
                    <th field="ORG" align="center" width="10%">ORG</th>
                    <th field="PLANT_PENGIRIM" align="center">Plant Pengirim</th>
                    <th field="PLANT_TUJUAN" align="center">Plant Tujuan</th>
                    <th field="DELETE_MARK" align="center" formatter="cek">Delete Mark</th>
                    <th field="CREATED_AT">Created At</th>
                    <th field="CREATED_BY">Created By</th>
                    <th field="UPDATED_AT">Updated At</th>
                    <th field="UPDATED_BY">Updated By</th>

                    <!-- <th field="DELETE_MARK" align="center">Delete Mark</th> -->
                </tr>
            </thead>
        </table>
        <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="deleteAct()">Delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-excel"
                href="template_xls/template_mapping_plant_rencana_kirim.xls">Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload" onclick="uploadAct()">Upload Excel</a>
        </div>

        <div id="dlg" class="easyui-dialog" closed="true" buttons="#dlg-buttons">
            <div class="ftitle">Create data</div>
            <form id="fm" method="post" novalidate>
                <div class="fitem">
                    <label>Org</label>
                    <input style="width:200px;" class="easyui-textbox" id="nm_org" name="nm_org" required="true">
                    <input id="ORG" name="ORG" readonly="true" type="hidden">
                </div>
                <div class="fitem">
                    <label>Plant Pengirim</label>
                    <!-- <input id="PLANT" name="PLANT" class="easyui-textbox" maxlength="10"> -->
                    <select required="true" id="PLANT_PENGIRIM" class="easyui-combobox" name="PLANT_PENGIRIM"
                        style="width:200px;">
                        <?php
                            $sql = oci_parse($conn, "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                            $result=oci_execute($sql);
                            while ($row = oci_fetch_array($sql)){
                                echo "<option value=".$row['WERKS'].">" .$row['WERKS']." (". $row['NAME1'] . ")</option>";
                            }
                        ?>
                    </select>
                </div>
                <div class="fitem">
                    <label>Plant Tujuan</label>
                    <!-- <input id="PLANT" name="PLANT" class="easyui-textbox" maxlength="10"> -->
                    <select required="true" id="PLANT_TUJUAN" class="easyui-combobox" name="PLANT_TUJUAN"
                        style="width:200px;">
                        <?php
                            $sql = oci_parse($conn, "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                            $result=oci_execute($sql);
                            while ($row = oci_fetch_array($sql)){
                                echo "<option value=".$row['WERKS'].">" .$row['WERKS']." (". $row['NAME1'] . ")</option>";
                            }
                        ?>
                    </select>
                </div>
            </form>
        </div>
        <div id="dlg-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>

    <div id="dlgEdit" class="easyui-dialog" closed="true" buttons="#dlg-buttons-edit">
        <div class="ftitle" id="titleEdit">Detail data</div>
        <form id="fmEdit" method="post" novalidate>
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>ID</label>
                <input id="idEdit" name="ID" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Org</label>
                <input id="orgEdit" name="ORG" type="hidden">
                <input id="orgEditName" class="easyui-combogrid" name="NAMEORG" style="width:200px;">
            </div>
            <div class="fitem">
                <label>Plant Pengirim</label>
                <!--<input id="plantEdit" name="PLANT" class="easyui-textbox" maxlength="10"> -->
                <select required="true" id="plantPengirimEdit" class="easyui-combobox" name="PLANT_PENGIRIM"
                    style="width:200px;">
                    <?php
                            $sql = oci_parse($conn, "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                            $result=oci_execute($sql);
                            while ($row = oci_fetch_array($sql)){
                                echo "<option value=".$row['WERKS'].">" .$row['WERKS']." (". $row['NAME1'] . ")</option>";
                            }
                        ?>
                </select>
            </div>
            <div class="fitem">
                <label>Plant Tujuan</label>
                <!--<input id="plantEdit" name="PLANT" class="easyui-textbox" maxlength="10"> -->
                <select required="true" id="plantTujuanEdit" class="easyui-combobox" name="PLANT_TUJUAN"
                    style="width:200px;">
                    <?php
                            $sql = oci_parse($conn, "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN");
                            $result=oci_execute($sql);
                            while ($row = oci_fetch_array($sql)){
                                echo "<option value=".$row['WERKS'].">" .$row['WERKS']." (". $row['NAME1'] . ")</option>";
                            }
                        ?>
                </select>
            </div>
            <div class="fitem">
                <label>Status</label>
                <select class="easyui-combobox" required="true" id="statusEdit" name="DELETE_MARK" maxlength="10" required="true">
                    <option value="1">Inactive</option>
                    <option value="0">Active</option>
                </select> 
            </div>
        </form>
    </div>
    <div id="dlg-buttons-edit">
        <a href="javascript:void(0)" id="modal-submit" class="easyui-linkbutton c6" iconCls="icon-ok"
            onclick="saveEditAct()" style="width:90px" id="savedata">Oke</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
            onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
    </div>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true"
        buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload"
                    name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px"
                id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>

    <script type="text/javascript">
    $('#nm_org').combogrid({
        panelWidth:450,
        url: 'getdata_org.php',
        idField:'ORGINH',
        textField:'NAMEORG',
        fitColumns:true,
        method: 'get',
        loadMsg: 'Searching...',
        onSelect: function(index,row){
            var desc = row.ORGINH;  // the product's description
            var kode = row.NAMEORG;
//            $('#dist').val(desc);
            $('#nm_org').textbox('setValue', desc); 
            $('#ORG').val(desc);
        },    
        onChange:  function(newValue,oldValue) {
            $('#nm_org').textbox('setValue','');
//            $('#nm_dist').textbox('setValue','');
        },
        columns:[[
        {field:'ORGINH',title:'Kode',align:'center',width:100},
        {field:'NAMEORG',title:'Org',align:'center',width:350}
        ]]
    });
    $(function(){
        $("#dg").datagrid({
            url: 'mapping_plant_rencana_kirim_act.php?act=show',
            singleSelect: true,
            pagination: true,
            pageList: [10, 50, 100, 300, 500, 1000, 5000, 10000],
            pageSize: 20,
            rownumbers: true,
            loadMsg: 'Processing, please wait..',
            height: 'auto',
            toolbar: '#toolbar'

        });
        $('#dg').datagrid('enableFilter');
    });

    $('#dlg').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    $('#dlgEdit').dialog({
    title: 'My Dialog',
    // width: 277,
    // height: 277,
    closed: true,
    cache: false,
    // href: 'get_content.php',
    modal: true
});

    var url;

    function newAct() {
        $('#dlg').dialog('open').dialog('setTitle', 'New Data');
        $('#fm').form('clear');
        url = 'mapping_plant_rencana_kirim_act.php?act=add';
    }

    function cek(val,row){
        if(val=='0'){
            return `<span><button style="width:120px" class="btn green">Active</button></span>`;               
        } else {
            return `<span><button style="width:120px" class="btn yellow">Inactive</button></span>`;
        } 
    }


    function saveAct(){
    $('#fm').form('submit',{
        url: url,
        onSubmit: function(){ 
            return $(this).form('validate');
        },
        success: function(result){
            var result = eval('('+result+')');
            if (result.errorMsg){ 
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400,  // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                         width: 400,  // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                }
            }
        });
    }

    function formatDate(dateStr) {
        const months = {
            JAN: '01',
            FEB: '02',
            MAR: '03',
            APR: '04',
            MAY: '05',
            JUN: '06',
            JUL: '07',
            AUG: '08',
            SEP: '09',
            OCT: '10',
            NOV: '11',
            DEC: '12'
        };

        const parts = dateStr.split('-');
        const day = parts[0];
        const month = months[parts[1].toUpperCase()];
        const year = `20${parts[2]}`; // Tambahkan '20' untuk tahun singkat

        return `${year}-${month}-${day}`; // Format YYYY-MM-DD
    }


    function editAct() {
        var row = $('#dg').datagrid('getSelected'); // Ambil data baris yang dipilih
        if (row) {
            document.getElementById("titleEdit").textContent = "Edit Master Data";
            $('#modal-submit').attr('onclick', 'saveEditAct()');
            $('#dlgEdit').dialog('open').dialog('setTitle', 'Edit Data');

            // enable semua input
            $('#orgEditName').combogrid('enable');
            $('#plantPengirimEdit').combobox('enable');
            $('#plantTujuanEdit').combobox('enable');
            $('#statusEdit').combobox('enable');
            // Set nilai pada form
            // Set nilai default untuk kode org dan nama org
            $('#orgEdit').val(row.ORG); // Kode organisasi
            $('#orgEditName').combogrid({
                panelWidth: 450,
                url: 'getdata_org.php',
                idField: 'ORGINH',
                textField: 'NAMEORG',
                fitColumns: true,
                method: 'get',
                loadMsg: 'Loading...',
                onLoadSuccess: function() {
                    var grid = $('#orgEditName').combogrid('grid'); // Akses grid combogrid
                    var data = grid.datagrid('getData').rows; // Ambil semua data dari grid
                    var selectedOrg = row.ORG; // Ambil kode organisasi dari data yang dipilih
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].ORGINH == selectedOrg) {
                            // Temukan nama organisasi yang sesuai
                            $('#orgEditName').textbox('setValue', data[i].NAMEORG); // Tampilkan nama org
                            break;
                        }
                    }
                },
                onSelect: function(index, row) {
                    $('#orgEditName').textbox('setValue', row.NAMEORG); // Tampilkan nama organisasi
                    $('#orgEdit').val(row.ORGINH); // Simpan kode organisasi
                },
                columns: [
                    [{
                            field: 'ORGINH',
                            title: 'Kode',
                            width: 100,
                            align: 'center'
                        },
                        {
                            field: 'NAMEORG',
                            title: 'Org',
                            width: 350,
                            align: 'left'
                        }
                    ]
                ]
            });

            // Set nilai lainnya ke form// Set nilai lainnya ke form
            $('#plantPengirimEdit').combobox('setValue', row.PLANT_PENGIRIM);
            $('#plantTujuanEdit').combobox('setValue', row.PLANT_TUJUAN);
            $('#idEdit').textbox('setValue', row.ID);
            $('#statusEdit').combobox('setValue', row.DELETE_MARK);

            url = 'mapping_plant_rencana_kirim_act.php?act=edit';
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function saveEditAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400,  // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400,  // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function deleteAct() {
        var row = $('#dg').datagrid('getSelected');
        if (row) {
            document.getElementById("titleEdit").textContent = "Delete Master Data";
            $('#modal-submit').attr('onclick', 'saveDeleteAct()');
            $('#dlgEdit').dialog('open').dialog('setTitle', 'Delete Data');
            
            // Tambahkan CSS pada orgEditName untuk membuatnya terlihat disabled
            $('#orgEditName').css({
                'background-color': '#e9ecef',
                'color': '#6c757d',
                'border': '1px solid #dee2e6',
                'cursor': 'not-allowed'
            });

            // Disable semua input lainnya
            $('#orgEditName').combogrid('disable'); // Nonaktifkan combogrid secara internal
            $('#plantPengirimEdit').combobox('disable');
            $('#plantTujuanEdit').combobox('disable');
            $('#statusEdit').combobox('disable');

            // Set nilai pada form
            $('#orgEdit').val(row.ORG); // Kode organisasi
                        $('#orgEditName').combogrid({
                panelWidth: 450,
                url: 'getdata_org.php',
                idField: 'ORGINH',
                textField: 'NAMEORG',
                fitColumns: true,
                method: 'get',
                onLoadSuccess: function () {
                    var grid = $('#orgEditName').combogrid('grid');
                    var data = grid.datagrid('getData').rows;
                    var selectedOrg = row.ORG;
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].ORGINH == selectedOrg) {
                            $('#orgEditName').textbox('setValue', data[i].NAMEORG);
                            break;
                        }
                    }

                    // Nonaktifkan setelah data berhasil di-set
                    $('#orgEditName').combogrid('disable');
                    $('#orgEditName').css({
                        'background-color': '#e9ecef',
                        'color': '#6c757d',
                        'border': '1px solid #dee2e6',
                        'cursor': 'not-allowed'
                    });
                },
                columns: [[
                    { field: 'ORGINH', title: 'Kode', width: 100, align: 'center' },
                    { field: 'NAMEORG', title: 'Org', width: 350, align: 'left' }
                ]]
            });

            // Set nilai lainnya pada form// Set nilai lainnya pada form
            $('#plantPengirimEdit').combobox('setValue', row.PLANT_PENGIRIM);
            $('#plantTujuanEdit').combobox('setValue', row.PLANT_TUJUAN);
            $('#idEdit').textbox('setValue', row.ID);
            $('#statusEdit').combobox('setValue', row.DELETE_MARK);

            // URL untuk request delete
            url = 'mapping_plant_rencana_kirim_act.php?act=delete';
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function uploadAct() {
        $('#dlg_upload').dialog('open').dialog('setTitle', 'Upload Excel Data');
        $('#uploadForm').form('clear');
    }

    function saveUploadAct() {
        $('#uploadForm').form('submit', {
            url: 'mapping_plant_rencana_kirim_act.php?act=upload_file',
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.data,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function saveDeleteAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    </script>

    <style type="text/css">
    .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .icon-upload {
        background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
        background: transparent url("icon/excel.png") no-repeat scroll center center;
    }


    .btn:not([disabled]) {
        color: white;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }

    thead th{
        text-align: left;
        padding: 7px;
    }

    tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
    }

    #fm{
        margin:0;
        padding:10px;
    }

    .ftitle{
        font-size:14px;
        font-weight:bold;
        padding:5px 0;
        margin-bottom:10px;
        border-bottom:1px solid #ccc;
    }
    .fitem{
        margin-bottom:5px;
    }

    .fitem label{
        display:inline-block;
        width:101px;
        margin-bottom:2px;
    }

    .fitem input{
        width:190px;
        margin-bottom:5px;
    }

    .fitem select{
        width:195px;
        margin-bottom:5px;
    }

    /* .fitem #valid_from, .fitem #valid_to{
        width:192px;
    } */

    #dlg, #dlgEdit {
        padding:10px 0 10px 10px;
    }
    </style>
    </div>
    <? 
include ('../include/ekor.php'); 
?>
</body>

</html>
