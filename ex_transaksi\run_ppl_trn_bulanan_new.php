<? 
session_start();
include ('../include/ex_fungsi.php');//dev atau prod
//include ('../../../../prod/sd/sdonline/include/ex_fungsi.php');//prod
include ('../include/validasi.php'); 
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
//$halaman_id=1763;//dev
$halaman_id=583;//prod
$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);
$companyin=$fungsi->arrayorg();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$page="run_ppl_trn_bulanan_new.php";
$pass="";
$namaorg='PT SEMEN INDONESIA (PERSERO) Tbk';
// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
	?>
	<!-- <SCRIPT LANGUAGE="JavaScript">
	alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
	</SCRIPT>
	<a href="../index.php">Login</a> -->
	<?
	// exit;
// }

#Tgl Update Clearing
$t1 = date("d-m-Y", strtotime("-31 days"));
$t2   = date("d-m-Y", strtotime("0 days"));
$sql_last_clearing = "SELECT to_char(max(TGL_CLEARING),'DD-MM-YYYY') as TGL_CLEARING_MAX 
    FROM EX_TRANS_HDR WHERE ORG='".$user_org."' AND DELETE_MARK='0' 
    AND TGL_CLEARING BETWEEN TO_Date('".$t1."', 'DD-MM-YYYY') 
    AND TO_Date('".$t2."', 'DD-MM-YYYY')";
//echo $sql_last_clearing;
$query_bn= @oci_parse($conn, $sql_last_clearing);
oci_execute($query_bn);
$row_bn=@oci_fetch_array($query_bn);
$tgl_clearing_v=$row_bn[TGL_CLEARING_MAX]; 
$show_ket = "Tanggal Update Clearing : ".$tgl_clearing_v;

function cekNoInvoice($tahun, $bulan, $kode_distributor, $nomor_invoice, $conn,$org){
    $bulan=sprintf('%02d',$bulan);
    $user_org=$_SESSION['user_org'];
    if($org=='2000' || $org=='7000' || $org=='5000' || $org=='7900'){
        if($org=='2000'){
            $user_orgcd='7000';
        }else{
            $user_orgcd='2000';
        }
        $orgIn = "'".$org."','".$user_orgcd."','5000'";
    }else{
        $orgIn = "'".$org."'";
    }
    $sql = "SELECT count(id) as JML
        FROM ex_fb60
        WHERE     ORG ='".$user_org."'
              AND delete_mark = '0'
              AND tahun = '$tahun'
              AND bulan = '$bulan'
              AND sold_to = '$kode_distributor'
              AND no_invoice = '$nomor_invoice'";
        $query_data= oci_parse($conn, $sql);
	oci_execute($query_data);
	$row_dist=oci_fetch_array($query_data);
        return $row_dist['JML'];
}

function cekNoInvoiceClaim($tahun, $bulan, $kode_distributor, $conn,$org){
    $bulan=sprintf('%02d',$bulan);
    $user_org=$_SESSION['user_org'];
    if($org=='2000' || $org=='7000' || $org=='5000' || $org=='7900'){
        if($org=='2000'){
            $user_orgcd='7000';
        }else{
            $user_orgcd='2000';
        }
        $orgIn = "'".$org."','".$user_orgcd."','5000'";
    }else{
        $orgIn = "'".$org."'";
    }
    $sql = "
                select SOLD_TO,sum(STATUS_CLAIM_DIST) as JMLAPPCALIM from EX_TRANS_HDR where ORG='".$user_org."' and DELETE_MARK= '0' 
                AND SOLD_TO != '**********' AND SOLD_TO != '**********' 
                and SOLD_TO='$kode_distributor'
                and to_char(TGL_CLEARING,'YYYYMM')='$tahun$bulan'
                and ( TOTAL_KLAIM_SEMEN <> 0 or TOTAL_KLAIM_KTG <> 0) 
                group by SOLD_TO
         ";
        $query_data= oci_parse($conn, $sql);
	oci_execute($query_data);
	$row_dist=oci_fetch_array($query_data);
        return $row_dist['JMLAPPCALIM'];
}

function getKlaimData($tahun, $bulan, $kode_distributor, $status_all, $conn, $org){
        unset($hasil);
        $user_org=$_SESSION['user_org'];
        $sql_group_distr='';
        $blnthn = $bulan.'-'.$tahun;
        $jumHari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
        $tgl_awal   = '01-'.$blnthn;
        $tgl_akhir  = $jumHari.'-'.$blnthn;
        
        #$cari_invoice_satu = '1';
        if($status_all=='1'){
            $sql_field_cari = " ,ax.NO_INVOICE";
            $sql_where_cari = " and ax.SOLD_TO='".$kode_distributor."' and rownum=1 ";
        } else if($status_all=='2'){
            #Ambil total klaim bulanan dari distributor
            $sql_field_cari = "";
            $sql_where_cari = " and ax.SOLD_TO='".$kode_distributor."' ";
        } else {
            $sql_field_cari = "";
            $sql_where_cari = "";
        }
        
        if($org=='2000' || $org=='7000' || $org=='5000' || $org=='7900'){
            if($org=='2000'){
                $user_orgcd='7000';
            }else{
                $user_orgcd='2000';
            }
            $orgIn = "'".$org."','".$user_orgcd."','5000'";
        }else{
            $orgIn = "'".$org."'";
        }
        
        #Klaim group distributor pada tanggal clearing bulan ?? :D
        #if($status_all=='0') 
        #echo "<br><br>KD DIS : ".$kode_distributor." : ".
        $sql_group_distr = 
                "select ax.SOLD_TO ".$sql_field_cari."
                ,(select bx.NAMA_SOLD_TO FROM EX_TRANS_HDR bx WHERE bx.SOLD_TO = ax.SOLD_TO AND bx.DELETE_MARK = '0' AND ROWNUM=1) AS NAMA_SOLDTO,
                SUM(ax.KLAIM_REZAK) AS KLAIM_REZAK, 
                SUM(ax.KLAIM_SEMEN_V) AS KLAIM_SEMEN_V, 
                SUM(ax.KLAIM_ALL_V) AS KLAIM_ALL,
                (SELECT CREATE_DATE FROM EX_FB60 WHERE ORG='".$user_org."' AND DELETE_MARK='0' AND TAHUN='".$tahun."' AND BULAN='".$bulan."' AND SOLD_TO=ax.SOLD_TO) AS CREATE_DATE,
                (SELECT APPROVE_KASI FROM EX_FB60 WHERE ORG='".$user_org."' AND DELETE_MARK='0' AND TAHUN='".$tahun."' AND BULAN='".$bulan."' AND SOLD_TO=ax.SOLD_TO) AS APPROVE_KASI,
                (SELECT APPROVE_KABIRO FROM EX_FB60 WHERE ORG='".$user_org."' AND DELETE_MARK='0' AND TAHUN='".$tahun."' AND BULAN='".$bulan."' AND SOLD_TO=ax.SOLD_TO) AS APPROVE_KABIRO,
                (SELECT COUNT(ID) FROM EX_FB60 WHERE ORG='".$user_org."' AND DELETE_MARK='0' AND TAHUN='".$tahun."' AND BULAN='".$bulan."' AND SOLD_TO=ax.SOLD_TO) AS RUN_BLOK
                from
                (
                SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH, PLANT
                ,CASE WHEN SUM(TOTAL_SEMEN_RUSAK)>SUM(KLAIM_LEBIH)
                    THEN (SUM(TOTAL_SEMEN_RUSAK)-SUM(KLAIM_LEBIH))
                    ELSE 0
                END klaim_semen_v,
                CASE WHEN SUM(TOTAL_SEMEN_RUSAK)>SUM(KLAIM_LEBIH)
                    THEN SUM(TOTAL_KTG_REZAK)+(SUM(TOTAL_SEMEN_RUSAK)-SUM(KLAIM_LEBIH))
                    ELSE SUM(TOTAL_KTG_REZAK)
                END klaim_all_v
                 FROM EX_TRANS_HDR WHERE 
                 ORG='".$user_org."' AND DELETE_MARK = '0' AND NO_INVOICE IS NOT NULL AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND 
                 TGL_CLEARING BETWEEN TO_Date('".$tgl_awal."', 'DD-MM-YYYY') AND TO_Date('".$tgl_akhir."', 'DD-MM-YYYY') 
                 and ( TOTAL_KLAIM_SEMEN <> 0 or TOTAL_KLAIM_KTG <> 0)
                 and last_update_date >= TO_Date('01-03-2012', 'DD-MM-YYYY') 
                 and STATUS_CLAIM_DIST=0
                GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC,PLANT ORDER BY SOLD_TO ASC
                ) ax
                where 
                (ax.KLAIM_SEMEN_V >0 or 
                ax.KLAIM_ALL_V>0 or 
                ax.KLAIM_REZAK>0) ".$sql_where_cari."
                GROUP BY ax.SOLD_TO ".$sql_field_cari." 
                ORDER BY ax.SOLD_TO";
        #if($status_all=='0') 
       // echo "<br>".$sql_group_distr;
        $query_distr= oci_parse($conn, $sql_group_distr);
	oci_execute($query_distr);
        #unset($klaim_distr_all);
        $no_invoice_single='';
	while($row_dist=oci_fetch_array($query_distr)){
//            echo "<br>DURI : <pre>";
//            print_r($row_dist);
//            echo "</pre>";
            $hasil[] = $row_dist;
            if($status_all=='1' && $no_invoice_single==''){
                $hasil = $row_dist[NO_INVOICE]; //dapet nomor invoice yang akan dijadikan invoice klaim bulanan 
//                echo "<pre>";
//                print_r($row_dist);
//                echo "</pre>";
            } else if($status_all=='2'){
                $hasil = $row_dist; 
            }
            
        }
//        echo "<pre>";
//        print_r($hasil);
//        echo "</pre>";
        return $hasil;
}
//kebutuhan KSO
function cektanggal($conn,$distributor,$tahun,$bulan){
    unset($cek);
    $cek=0;
    $query = "SELECT  TO_CHAR(TANGGAL_KIRIM, 'YYYY') AS TGL_KIRIM FROM EX_TRANS_HDR WHERE SOLD_TO = '$distributor' AND
        TO_CHAR(TGL_CLEARING, 'YYYYMM')='$tahun$bulan' AND DELETE_MARK = '0' GROUP BY TO_CHAR(TANGGAL_KIRIM, 'YYYY')";
    $query_bn= oci_parse($conn, $query);
    oci_execute($query_bn);unset($ahunmm);
    while($data_d=oci_fetch_array($query_bn)){
        $ahunmm[$data_d[TGL_KIRIM]]=$data_d[TGL_KIRIM];
           
    }
    $countrp=0;
    if(count($ahunmm)>1){ 
        foreach ($ahunmm as $keyth => $valth) {
            if($keyth=='2016' || $keyth=='2017'){
                $countrp += 1;
            }
        }        
    }
    
    if($countrp>=2){
        $cek=1;
    }
    return $cek;
}
//proses lempar klaim ke SAP
function doKlaim($accounting_doc,$no_invoice,$distributor,$klaim_rezak_v,$klaim_semen_v,$bulan,$tahun,$conn,$fungsi){
    $fb60_doc_no = $distributor.$bulan.$tahun;
    $blnthn = $bulan.'-'.$tahun;
    $jumHari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
    $tgl_awal   = '01-'.$blnthn;
    $tgl_akhir  = $jumHari.'-'.$blnthn;
//    echo "<br>Tgl 1:".$tgl_awal   = '01-'.$blnthn;
//    echo "<br>Tgl 2:".$tgl_akhir  = $jumHari.'-'.$blnthn;
//    echo "<br>Acc.Doc:".$accounting_doc;
    
    unset($data_klaim);
    $orgcari=$_SESSION['user_org'];
    $data_klaim = getKlaimData($tahun, $bulan, $distributor, '2', $conn,$orgcari);
    #echo "<br>Klaim Rezak:".
    $klaim_rezak_v = $data_klaim[KLAIM_REZAK];
    #echo "<br>Klaim Smeen:".
    $klaim_semen_v = $data_klaim[KLAIM_SEMEN_V];
//    echo "<pre>";
//    print_r($data_klaim);
//    echo "</pre>";

    $no_invoice_v = $no_invoice;
    $sap = new SAPConnection();		
    //$sap->Connect("/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php");
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
    if ($sap->GetStatus() != SAPRFC_OK ) { echo $sap->PrintStatus(); exit; }
    $fce = $sap->NewFunction ("Z_ZAPPSD_ACC_DOC_POST");
    if ($fce == false ) { $sap->PrintStatus(); exit; }
    
    $sql_bn= "SELECT ORG,NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS,TANGGAL_INVOICE,NAMA_PENGELOLA,PENGELOLA,BVTYP_DIS
    FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' 
    AND SOLD_TO = '$distributor' ORDER BY ID DESC";
    $query_bn= oci_parse($conn, $sql_bn);
    oci_execute($query_bn);
    $row_bn=oci_fetch_array($query_bn);
    $nama_vendor_v=$row_bn[NAMA_VENDOR]; 
    $nama_pengelola_v=$row_bn[NAMA_PENGELOLA]; 
    $kode_pengelola_v=$row_bn[PENGELOLA];
    $nama_sold_to_v=$row_bn[NAMA_SOLD_TO];
    $no_rek_dis_v=$row_bn[NO_REK_DIS];
    $nama_bank_dis_v=$row_bn[NAMA_BANK_DIS];
    $cabang_bank_dis_v=$row_bn[BANK_CABANG_DIS];  
    $tanggal_invoice_v=$row_bn[TANGGAL_INVOICE];  
    $bvtyp_v=$row_bn[BVTYP_DIS];
    $org_in=$row_bn[ORG];
    $namaorg=$companyin[$org_in];
//    echo "<pre>";
//    print_r($row_bn);
//    echo "</pre>";
    
    $cek = cektanggal($conn,$distributor,$tahun,$bulan);
    $user_name=$_SESSION['user_name'];
    // Kebutuhan KSO
    if($cek==1){
        echo "<script type='text/javascript'>alert('Tidak Bisa Dilakukan Run Claim Distributor!!!');</script>";
    } else{
        if($org_in=='7000' && $tahun<2017)
            $org_in = '2000';

        if($klaim_rezak_v > 1){
                $kode_claim_rezak = 'SG0004';
                $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND DELETE_MARK = '0' and ORG='$org_in' ";
                $query_gl= oci_parse($conn, $mialo);
                oci_execute($query_gl);
                $row_gl=oci_fetch_array($query_gl);
                $no_gl_claim_rezak=$row_gl['NO_GL']; 
                $nama_claim_rezak=$row_gl['NAMA_KOMPONEN']; 
                $keterangan_claim_rezak=$row_gl['KETERANGAN']; 
                $pajak_claim=$row_gl['TAX_CODE']; 
                $rate_claim=$row_gl['RATE']; 
                $prctr_rezakclaim=$row_gl['PRCTR']; 
                $kostl_claim=$row_gl['COST_CENTER'];

                $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_rezakclaim;//'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                $fce->ACCOUNTGL->row["ITEM_TEXT"] = $accounting_doc; //add by iljas
                $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_rezak_v;
                //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                #$fb60_doc_no .= " KLAIM REZAK ";
                $klaim_all_new_v = $klaim_all_new_v + $klaim_rezak_v;
        }
        if($klaim_semen_v > 1){
                $kode_claim_semen = 'SG0003';
                $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' AND DELETE_MARK = '0' and ORG='$org_in'";
                $query_gl= oci_parse($conn, $mialo);
                oci_execute($query_gl);
                $row_gl=oci_fetch_array($query_gl);
                $no_gl_claim_semen=$row_gl['NO_GL']; 
                $nama_claim_semen=$row_gl['NAMA_KOMPONEN']; 
                $keterangan_claim_semen=$row_gl['KETERANGAN']; 
                $pajak_claim=$row_gl['TAX_CODE']; 
                $rate_claim=$row_gl['RATE']; 
                $prctr_claim=$row_gl['PRCTR']; 
                $kostl_claim=$row_gl['COST_CENTER'];

                $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_claim;//'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                $fce->ACCOUNTGL->row["ITEM_TEXT"] = $accounting_doc; //add by iljas
                $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_semen_v;
                //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                #$fb60_doc_no .= " KLAIM SEMEN ";
                $klaim_all_new_v = $klaim_all_new_v + $klaim_semen_v;
        }

        $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
        $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
        $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
        $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = (-1)*$klaim_all_new_v;
        //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
        $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);

        $fce->ACCOUNTPAYABLE->row["ITEMNO_ACC"] = '**********';
        $fce->ACCOUNTPAYABLE->row["VENDOR_NO"] = $kode_pengelola_v;
        $fce->ACCOUNTPAYABLE->row["COMP_CODE"] = $org_in;//'2000';
        $fce->ACCOUNTPAYABLE->row["ALLOC_NMBR"] = $no_invoice;
        $fce->ACCOUNTPAYABLE->row["BLINE_DATE"] = date("Ymd");
        $fce->ACCOUNTPAYABLE->row["PYMT_METH"] = 'T'; //dibayar dengan banking online
        $fce->ACCOUNTPAYABLE->row["PMNT_BLOCK"] = '3';
        $fce->ACCOUNTPAYABLE->row["PARTNER_BK"] = $bvtyp_v;
        //	$fce->ACCOUNTPAYABLE->row["ITEM_TEXT"] = $no_invoice;
        //	$fce->ACCOUNTPAYABLE->row["TAX_CODE"] = $no_invoice;
        $fce->ACCOUNTPAYABLE->Append($fce->ACCOUNTPAYABLE->row);

        $fce->DOCUMENTHEADER["BUS_ACT"] = "RMRP";
        $fce->DOCUMENTHEADER["USERNAME"] = $user_name;
        $fce->DOCUMENTHEADER["HEADER_TXT"] = $fb60_doc_no;
        $fce->DOCUMENTHEADER["COMP_CODE"] = $org_in;//'2000';
        $fce->DOCUMENTHEADER["DOC_DATE"] = date("Ymd");
        $fce->DOCUMENTHEADER["PSTNG_DATE"] = date("Ymd");
        $fce->DOCUMENTHEADER["FISC_YEAR"] = date("Y");
        $fce->DOCUMENTHEADER["FIS_PERIOD"] = "00";
        $fce->DOCUMENTHEADER["DOC_TYPE"] = "KR";

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {
            $obj_key=$fce->OBJ_KEY;			
            $no_doc=substr($obj_key,0,10);
            $tahun = $tahun; //date("Y");
            $sold_to_v=$distributor;


            if ($obj_key != '$' and strlen($no_doc) > 5){                
                if($klaim_rezak_v > 0){
                $field_names=array('KODE_KOMPONEN','NAMA_KOMPONEN','NO_INVOICE','TOTAL','CREATE_DATE','CREATED_BY','LAST_UPDATE_DATE','LAST_UPDATED_BY','DELETE_MARK','KETERANGAN','NO_GL','ORG','NAMA_ORG','PROFIT_CENTER','KODE_PAJAK','PAJAK','SUB_TOTAL','SOLD_TO','NAMA_SOLD_TO','NO_DOC_HDR','VENDOR','NAMA_VENDOR','TAHUN','BULAN','AWKEY','ACCOUNTING_DOC','FB60_DOC_NO');
                $field_data=array("$kode_claim_rezak","$nama_claim_rezak","$no_invoice","$klaim_rezak_v","SYSDATE","$user_name","SYSDATE","$user_name","0","CLAIM REZAK","$no_gl_claim_rezak","$org_in","$namaorg","$prctr_rezakclaim","","","$klaim_rezak_v","$sold_to_v","$nama_sold_to_v","$no_doc","$kode_pengelola_v","$nama_pengelola_v","$tahun","$bulan","$obj_key","$accounting_doc","$fb60_doc_no"); 
                $tablename="EX_FB60_DTL";
                $fungsi->insert($conn,$field_names,$field_data,$tablename);  
                $ket_in = " KLAIM REZAK ";
                }
                if($klaim_semen_v > 0){
                $field_names=array('KODE_KOMPONEN','NAMA_KOMPONEN','NO_INVOICE','TOTAL','CREATE_DATE','CREATED_BY','LAST_UPDATE_DATE','LAST_UPDATED_BY','DELETE_MARK','KETERANGAN','NO_GL','ORG','NAMA_ORG','PROFIT_CENTER','KODE_PAJAK','PAJAK','SUB_TOTAL','SOLD_TO','NAMA_SOLD_TO','NO_DOC_HDR','VENDOR','NAMA_VENDOR','TAHUN','BULAN','AWKEY','ACCOUNTING_DOC','FB60_DOC_NO');
                $field_data=array("$kode_claim_semen","$nama_claim_semen","$no_invoice","$klaim_semen_v","SYSDATE","$user_name","SYSDATE","$user_name","0","CLAIM SEMEN","$no_gl_claim_semen","$org_in","$namaorg","$prctr_claim","","","$klaim_semen_v","$sold_to_v","$nama_sold_to_v","$no_doc","$kode_pengelola_v","$nama_pengelola_v","$tahun","$bulan","$obj_key","$accounting_doc","$fb60_doc_no"); 
                $tablename="EX_FB60_DTL";
                $fungsi->insert($conn,$field_names,$field_data,$tablename);  
                $ket_in .= " KLAIM SEMEN ";
                }
                $field_names=array('NO_INVOICE','TOTAL','CREATE_DATE','CREATED_BY','LAST_UPDATE_DATE','LAST_UPDATED_BY','DELETE_MARK','KETERANGAN','ORG','NAMA_ORG','PROFIT_CENTER','SOLD_TO','NAMA_SOLD_TO','NO_DOC_HDR','VENDOR','NAMA_VENDOR','TAHUN','BULAN','AWKEY','BVTYP','NO_REK','BANK','BANK_CABANG','APPROVE_KASI','APPROVE_KABIRO');
                $field_data=array("$no_invoice","$klaim_all_new_v","SYSDATE","$user_name","SYSDATE","$user_name","0","$ket_in","$org_in","$namaorg","$prctr_claim","$sold_to_v","$nama_sold_to_v","$no_doc","$kode_pengelola_v","$nama_pengelola_v","$tahun","$bulan","$obj_key","$bvtyp_v","$no_rek_dis_v","$nama_bank_dis_v","$cabang_bank_dis_v","0","0"); 
                $tablename="EX_FB60";
                $fungsi->insert($conn,$field_names,$field_data,$tablename);  
                $show_ket =  "<br>Sukses, No Accounting : ".$no_doc." Untuk Vendor ".$kode_pengelola_v ." / ". $nama_pengelola_v;   

                //memberi no doc pada no spj
                //@liyantanto 
                $sql_bnshp= "            
                SELECT NO_SHP_TRN
                FROM EX_TRANS_HDR WHERE 
                ORG='$org_in' AND DELETE_MARK = '0' AND NO_INVOICE IS NOT NULL AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND 
                TGL_CLEARING BETWEEN TO_Date('".$tgl_awal."', 'DD-MM-YYYY') AND TO_Date('".$tgl_akhir."', 'DD-MM-YYYY')
                and ( TOTAL_KLAIM_SEMEN <> 0 or TOTAL_KLAIM_KTG <> 0)
                and last_update_date >= TO_Date('01-03-2012', 'DD-MM-YYYY') 
                and STATUS_CLAIM_DIST=0
                AND SOLD_TO = '$distributor'
                group by NO_SHP_TRN            
                ";
                $query_bnshp= oci_parse($conn, $sql_bnshp);
                oci_execute($query_bnshp);
                while($row_bnshp=oci_fetch_array($query_bnshp)){
                      $noshptrn_pnj=$row_bnshp['NO_SHP_TRN'];
                      if($noshptrn_pnj!=''){
                            $field_names44=array('NO_DOC_HDR','TAHUN_FB60');
                            $field_data44=array("$no_doc","$tahun"); 
                            $tablename44="EX_TRANS_HDR";
                            $field_id44=array('NO_SHP_TRN','DELETE_MARK');
                            $value_id44=array("$noshptrn_pnj","0");
                            $fungsi->update($conn,$field_names44,$field_data44,$tablename44,$field_id44,$value_id44);                      
                      }
                }

            }else{
                    $fce->RETURN->Reset();
                    while ( $fce->RETURN->Next() ){
                            $show_ket .=  "<font color='red'";
                            $show_ket .=  "<br> ".$fce->RETURN->row["TYPE"];
                            $show_ket .=  " ".$fce->RETURN->row["NUMBER"];
                            $show_ket .=  " ".$fce->RETURN->row["MESSAGE"];
                    }
                    $show_ket .=  "<br> Gagal, Untuk Vendor ".$kode_pengelola_v ." / ". $nama_pengelola_v;
            }
        }
        unset($data_klaim);
        //echo $show_ket;
    }
}

if($_REQUEST['search_hdr'] || $_REQUEST['kdist']){ 
        $kode_distribur= $_REQUEST['kdist']; //$_REQUEST['sold_to'];
        $nama_sold_to= $_REQUEST['nama_sold_to'];        
        $id_bulan = $_REQUEST['bulan'];
        $id_tahun = $_REQUEST['tahun'];
        
//        if($kode_distribur!='') {            
            #echo "<b>No.Invoice Dist : </b>".
            $no_invoice_dist=getKlaimData($id_tahun, $id_bulan, $kode_distribur, '1', $conn, $user_org);
            
//        }
        $retunno=cekNoInvoice($id_tahun, $id_bulan, $kode_distribur, $no_invoice_dist, $conn, $user_org);
        if($retunno>0){
            echo 'Sudah pernah di-run dengan '.$no_invoice_dist.' dengan $no_invoice_dist';
            exit;
        }
        
        
        //TO_CHAR(LAST_UPDATE_DATE,'YYYY') = '".$posting_year."' AND
        //AND STATUS_RUNFB60 =''
        
        if(trim($no_invoice_dist)!=''){            
            $blnthn = $bulan.'-'.$tahun;
            $jumHari = cal_days_in_month(CAL_GREGORIAN, $id_bulan, $id_tahun);
            $tgl_awal   = '01-'.$blnthn;
            $tgl_akhir  = $jumHari.'-'.$blnthn;
        
            $sql = "
                SELECT ORG,ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, 
                SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, 
                SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH, PLANT 
                FROM EX_TRANS_HDR 
                WHERE DELETE_MARK = '0' AND
                SOLD_TO = '".$kode_distribur."' AND NO_INVOICE='".$no_invoice_dist."' AND
                DELETE_MARK = '0' AND
                NO_INVOICE IS NOT NULL AND
                SOLD_TO != '**********' AND SOLD_TO != '**********' AND
                 TGL_CLEARING BETWEEN TO_Date('".$tgl_awal."', 'DD-MM-YYYY') AND TO_Date('".$tgl_akhir."', 'DD-MM-YYYY') 
                 and last_update_date >= TO_Date('01-03-2012', 'DD-MM-YYYY') 
                 and STATUS_CLAIM_DIST=0
                GROUP BY ORG,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC,PLANT ORDER BY SOLD_TO ASC
            ";           
            $query= oci_parse($conn, $sql);
            oci_execute($query);
			//echo $sql;
            $total_invoice =0;
            $oa_semen_v=0; $idx=0; $ix=0;
            while($row=oci_fetch_array($query)){
    //		$no_gl_shp_v=$row[NO_GL_SHP];
                    $no_org_look[$idx]=$row[ORG];
                    $no_invoice_v=$row[NO_INVOICE];
                    $no_invoice_look[$idx]=$row[NO_INVOICE];
                    $no_invoice_ex_v=$row[NO_INV_VENDOR];
                    $no_invoice_ex_look[$idx]=$row[NO_INV_VENDOR];
                    $no_inv_sap_v=$row[NO_INV_SAP];
                    $no_acc_doc_v=$row[ACCOUNTING_DOC];
                    $vendor_v=$row[VENDOR]; 

                    //$tgl_clearing[$idx] = $fungsi->ex_getTglClearing($conn, $user_org, $no_acc_doc_v, $vendor_v) ;
                    $warna_plat_v=$row[WARNA_PLAT]; 

                    $dtstatus = $fungsi->ex_getClearing($no_inv_sap_v, $id_tahun);
                    $xinv_kode[$idx] = $dtstatus['KODE_STATUS'];
                    $xinv_ket[$idx]  = $dtstatus['KET_STATUS'];

                    //add by iljas 1-Nov-2011
                    $plant_v=$row[PLANT]; 
                    if($plant_v=='2490') $profit_center[$idx] = '**********'; else $profit_center[$idx] = '**********';

                    $sold_to_v[$idx]=$row[SOLD_TO];
                    $klaim_ktg_v[$idx]=$row[KLAIM_KTG];  
                    $klaim_rezak_v[$idx]=$row[KLAIM_REZAK];  

                    if($row[KLAIM_SEMEN] > $row[KLAIM_LEBIH]){
                            $klaim_semen_cekp=$row[KLAIM_SEMEN]-$row[KLAIM_LEBIH];  
                            $klaim_semen_v[$idx]=$klaim_semen_cekp;  
                    }else{ 
                            $klaim_semen_cekp=0;
                            $klaim_semen_v[$idx]=0;
                    }  

                    $klaim_all_v[$idx] = $row[KLAIM_REZAK] + $klaim_semen_cekp; 
                    $klaim_pdpks_v[$idx]=$row[KLAIM_PDPKS];  
                    $kel=$row[KELOMPOK_TRANSAKSI];  
                    $oa_semen_v += $row[OA_SEMEN];  		

                    $klaim_lebih_v[$idx]=$row[KLAIM_LEBIH];  
                    $klaim_pdpks_lebih_v[$idx]=$row[PDPKS_LEBIH];  
    //		$klaim_lebih_all_v[] = $klaim_lebih_v;

                    $distributor = $row[SOLD_TO];
                    $sql_bn= "SELECT NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS,TANGGAL_INVOICE,NAMA_PENGELOLA,PENGELOLA,BVTYP_DIS 
                    FROM EX_TRANS_HDR
                    WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' AND SOLD_TO = '$distributor' ORDER BY ID DESC";
                    // STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
                    $query_bn= oci_parse($conn, $sql_bn);
                    oci_execute($query_bn);
                    $row_bn=oci_fetch_array($query_bn);
                    $nama_vendor_v=$row_bn[NAMA_VENDOR]; 
                    $nama_pengelola_v[$idx]=$row_bn[NAMA_PENGELOLA]; 
                    $kode_pengelola_v[$idx]=$row_bn[PENGELOLA];
                    $nama_sold_to_v[$idx]=$row_bn[NAMA_SOLD_TO];
                    $no_rek_dis_v[$idx]=$row_bn[NO_REK_DIS];
                    $nama_bank_dis_v[$idx]=$row_bn[NAMA_BANK_DIS];
                    $cabang_bank_dis_v[$idx]=$row_bn[BANK_CABANG_DIS];  
                    $tanggal_invoice_v=$row_bn[TANGGAL_INVOICE];  
                    $bvtyp_v[$idx]=$row_bn[BVTYP_DIS];  $idx++;
            }
        }
	$total=count($sold_to_v);
//	if ($total < 1) $komen = "Tidak Ada Data Yang Ditemukan";
        
        #Document Check ::
        if($total>0){
            $sap = new SAPConnection();
            //$sap->Connect("../include/sapclasses/logon_data.conf");
            //$sap->Connect("/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php");
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) { echo $sap->PrintStatus(); exit; }  
            
            ?>
            <!--
            <div align="center">
            <table width="99%" align="center" class="adminlist">
            <tr>
            <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Invoice Distibutor <?=$nama_distribur?></span></th>
            </tr>
            </table>
            </div> 

            <div align="center">
            <table width="99%" align="center" class="adminlist" id="myScrollTable">
            <tr class="quote">
            <td align="center"><strong>Acc.Doc</strong></td>
            <td align="center"><strong>No.INV</strong></td>
            <td align="center"><strong>No.INV Ex</strong></td>
            <td align="center"><strong>Sold To </strong></td>
            <td align="center"><strong>Nama Sold To </strong></td>
            <td align="center"><strong>Vendor </strong></td>
            <td align="center"><strong>Nama Vendor </strong></td>
            <td align="center"><strong>Kode Biaya  </strong></td>
            <td align="center"><strong>Nama Biaya  </strong></td>
            <td align="center"><strong>No Gl </strong></td>
            <td align="center"><strong>Kode Pajak  </strong></td>
            <td align="center"><strong>Nilai  </strong></td>
            <td align="center"><strong>Mata Uang</strong></td>
            <td align="center"><strong>Status</strong></td>
            <td align="center"><strong>StatusSAP</strong></td>
            </tr >
            -->
            <?
        $totke=count($sold_to_v);
	$total_rezak_cet = 0;
	$total_semen_cet = 0;
	for ($i=0;$i<$totke;$i++){
		$total_rezak_cet = $klaim_rezak_v[$i];
		$total_semen_cet = $klaim_semen_v[$i];
                $no_invoice_x = $no_invoice_look[$i];
                
                $kode_inv = $kode_noinv_c[$i].$ket_status_c[$i];
                $msg_eksekusi = $xinv_kode[$i].' '.$xinv_ket[$i];
                
		$fce = $sap->NewFunction ("Z_ZAPPSD_ACC_DOC_CHECK");
		if ($fce == false ) {$sap->PrintStatus();exit;}
		$fce->DOCUMENTHEADER["BUS_ACT"] = "RMRP";
		$fce->DOCUMENTHEADER["USERNAME"] = $user_name;
		$fce->DOCUMENTHEADER["HEADER_TXT"] = '';
		$fce->DOCUMENTHEADER["COMP_CODE"] = $no_org_look[$i];//'2000';
		$fce->DOCUMENTHEADER["DOC_DATE"] = date("Ymd");
		$fce->DOCUMENTHEADER["PSTNG_DATE"] = date("Ymd");
		$fce->DOCUMENTHEADER["FISC_YEAR"] = date("Y");
		$fce->DOCUMENTHEADER["FIS_PERIOD"] = "00";
		$fce->DOCUMENTHEADER["DOC_TYPE"] = "KR";

                if ($klaim_rezak_v[$i] > 1 and $total_rezak_cet > 0){//untuk klaim rezak
                    $kode_claim_rezak = 'SG0004';
                    $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND DELETE_MARK = '0' and ORG='$no_org_look[$i]' ";
                    $query= oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row=oci_fetch_array($query);
                    $no_gl_claim_rezak=$row['NO_GL']; 
                    $nama_claim_rezak=$row['NAMA_KOMPONEN']; 
                    $keterangan_claim_rezak=$row['KETERANGAN']; 
                    $pajak_claim=$row['TAX_CODE']; 
                    $prctr_claimre=$row['PRCTR']; 
                    $kostl_claimre=$row['COST_CENTER'];
                    $total_rezak_cet = $klaim_rezak_v[$i];
                    $total_semen_cet = $klaim_semen_v[$i];

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_claimre; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_rezak_v[$i];
                    //$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    //echo " <br> amt rezak ".$klaim_rezak_v[$i];
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                ?>
                    <!--<tr>
                    <td align="center"><? echo $no_acc_doc_v; ?></td>
                    <td align="center"><? echo $no_invoice_look[$i]; ?></td>
                    <td align="center"><? echo $no_invoice_ex_look[$i]; ?></td>
                    <td align="center"><? echo $sold_to_v[$i]; ?></td>
                    <td align="left"><? echo $nama_sold_to_v[$i]; ?></td>
                    <td align="center"><? echo $kode_pengelola_v[$i]; ?></td>
                    <td align="left"><? echo $nama_pengelola_v[$i]; ?></td>
                    <td align="center"><? echo $kode_claim_rezak; ?></td>
                    <td align="left"><? echo $nama_claim_rezak; ?></td>
                    <td align="center"><? echo $no_gl_claim_rezak; ?></td>
                    <td align="center"><? echo $pajak_claim; ?></td>
                    <td align="right"><? echo number_format($klaim_rezak_v[$i],0,",","."); ?></td>
                    <td align="center"><? echo "IDR"; ?></td>
                    <td align="center"><? echo $kode_inv; ?></td>
                    <td align="center"><? echo $msg_eksekusi; ?></td>
                    </tr>-->
                <?            
                } 
            
                if ($klaim_semen_v[$i] > 1 and $total_semen_cet > 0){//untuk klaim semen
                    $kode_claim_semen = 'SG0003';
                    $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' AND DELETE_MARK = '0' and ORG='$no_org_look[$i]' ";
                    $query= oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row=oci_fetch_array($query);
                    $no_gl_claim_semen=$row['NO_GL']; 
                    $nama_claim_semen=$row['NAMA_KOMPONEN']; 
                    $keterangan_claim_semen=$row['KETERANGAN']; 
                    $pajak_claim=$row['TAX_CODE']; 
                    $prctr_claimse=$row['PRCTR']; 
                    $kostl_claimse=$row['COST_CENTER'];

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_claimse; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_semen_v[$i];
                    //echo " <br> amt semen ".$klaim_semen_v[$i];
                    //$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                    ?>	
                    <!--<tr>
                    <td align="center"><? echo $no_acc_doc_v; ?></td>
                    <td align="center"><? echo $no_invoice_look[$i]; ?></td>
                    <td align="center"><? echo $no_invoice_ex_look[$i]; ?></td>
                    <td align="center"><? echo $sold_to_v[$i]; ?></td>
                    <td align="left"><? echo $nama_sold_to_v[$i]; ?></td>
                    <td align="center"><? echo $kode_pengelola_v[$i]; ?></td>
                    <td align="left"><? echo $nama_pengelola_v[$i]; ?></td>
                    <td align="center"><? echo $kode_claim_semen; ?></td>
                    <td align="left"><? echo $nama_claim_semen; ?></td>
                    <td align="center"><? echo $no_gl_claim_semen; ?></td>
                    <td align="center"><? echo $pajak_claim; ?></td>
                    <td align="right"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
                    <td align="center"><? echo "IDR"; ?></td>
                    <td align="center"><? echo $kode_inv; ?></td>
                    <td align="center"><? echo $msg_eksekusi; ?></td>
                    </tr>-->
                    <?		
                }
        
                if ($klaim_semen_v[$i] + $klaim_rezak_v[$i] > 0){//untuk klaim semen
                    $b++;
                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = (-1)*$klaim_all_v[$i];
                    //echo " <br> amt total ".(-1)*$klaim_all_v[$i];
                    //$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);

                    $fce->ACCOUNTPAYABLE->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTPAYABLE->row["VENDOR_NO"] = $kode_pengelola_v[$i];
                    $fce->ACCOUNTPAYABLE->row["COMP_CODE"] = $no_org_look[$i];//'2000';
                    $fce->ACCOUNTPAYABLE->row["ALLOC_NMBR"] = $no_invoice_x;
                    $fce->ACCOUNTPAYABLE->row["BLINE_DATE"] = date("Ymd");
                    $fce->ACCOUNTPAYABLE->row["PYMT_METH"] = 'C';
                    $fce->ACCOUNTPAYABLE->row["PMNT_BLOCK"] = '3';
                    $fce->ACCOUNTPAYABLE->row["PARTNER_BK"] = $bvtyp_v[$i];
                    //$fce->ACCOUNTPAYABLE->row["ITEM_TEXT"] = $no_invoice;
                    //$fce->ACCOUNTPAYABLE->row["TAX_CODE"] = $no_invoice;
                    $fce->ACCOUNTPAYABLE->Append($fce->ACCOUNTPAYABLE->row);

                    $fce->Call();
                    $pass= "T";
                    $show_ket ="";
                    if ($fce->GetStatus() == SAPRFC_OK ) {		
                        $fce->RETURN->Reset();
                        while ( $fce->RETURN->Next() ){
                                $show_ket .=  "<br> ".$fce->RETURN->row["TYPE"];
                                $show_ket .=  " ".$fce->RETURN->row["NUMBER"];
                                $show_ket .=  " ".$fce->RETURN->row["MESSAGE"];					
                                if($fce->RETURN->row["TYPE"] == "E") $pass="F";

                                if($pass=="T"){
                                    if ($fce->RETURN->row["TYPE"]=="S") $status = "OK";
                                } else 
                                    $status="NOK";
                        }				
                    }
                    $fce->Close();	
                    ?> 
                    <!--
                    <tr class="quote">
                    <td align="center" colspan="11" style=" border-top:1px #000000 solid;">TOTAL</td>
                    <td align="right" style=" border-top:1px #000000 solid;"><? echo number_format($klaim_all_v[$i],0,",","."); ?></td>
                    <td align="center" style=" border-top:1px #000000 solid;"><? echo "IDR"; ?></td>
                    <td align="center" style=" border-top:1px #000000 solid;"><? if ($status!="OK") echo $show_ket;																	else echo $status; //$status;?></td>
                    <td align="center" style=" border-top:1px #000000 solid;"></td>
                    </tr>
                    -->
                    <? 
                }//untuk klaim semen
        }//end for
        
        #Jika Doc Check OK. ---------------------------------------------------------------------
        if ($pass=="T"){
            doKlaim($no_acc_doc_v, $no_invoice_dist, $kode_distribur, $klaim_rezak_v, $klaim_semen_v, $id_bulan, $id_tahun, $conn, $fungsi);
        }
        #----------------------------------------------------------------------------------------
        
        //echo "</table></div>";
        
                      
        
        }//end if >0
        $status_all = '0'; //select all
        $hasilx =getKlaimData($id_tahun, $id_bulan, $kode_distribur, $status_all, $conn,$user_org); 
//        echo "<pre>";
//        print_r($hasilx);
//        echo "</pre>";
                
}

        
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script language=javascript>
var message="You dont have permission to right click";
function clickIE(){if (document.all){(message);return false;}}
 
function clickNS(e) {if(document.layers||(document.getElementById&&!document.all))
{if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers){document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
    var xmlhttp=false;	
    try{ xmlhttp=new XMLHttpRequest();}
    catch(e){try{xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");}catch(e){try{xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");}catch(e1){xmlhttp=false}}}
    return xmlhttp;
}
function finddistr(org) {
    var com_org = document.getElementById('org');		
    var strURL="../ex_report/cari_distr.php?org="+com_org.value;
    popUp(strURL);
} 
		  
function ketik_distr(obj) {
    var com_org = document.getElementById('org');		
    var strURL="../or_laporan/ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
    var req = getXMLHTTP();
    if (req) {
        req.onreadystatechange = function() { if (req.readyState == 4) {if (req.status == 200) {document.getElementById("distrdiv").innerHTML=req.responseText;} else {alert("There was a problem while using XMLHTTP:\n" + req.statusText);}}}			
        req.open("GET", strURL, true);
        req.send(null);
    }
}
</script>
</head>

<body>

<div align="center">
<table width="400" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Generate PPL FB60 Turunan Bulanan</th>
</tr></table></div>
<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Data Klaim Distributor</th>
</tr>
</table>
</div>
    
<form id="data_claim" name="data_claim" method="post" action="<?=$page?>">
<table width="400" align="center" class="adminform">
<tr width="174">
    <td class="puso">&nbsp;</td>
    <td class="puso">&nbsp;</td>
    <td>&nbsp;</td>
</tr>
<!--
<tr>
    <td class="puso">Distributor</td>
    <td class="puso">:</td>
    <td class="puso">
        <input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
        <div id="distrdiv">
	  <input name="sold_to" id="sold_to" type="text" size="10" maxlength="10" value="<?=$kode_distribur?>" onChange="ketik_distr(this)"/>
	  <input name="nama_sold_to" id="nama_sold_to" type="text" size="35" value="<?=$nama_sold_to?>" readonly="true"/>	    
	  <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/></div>
    </td>
</tr>
-->
<tr>
    <td class="puso">Clearing Periode</td>
    <td class="puso">:</td>
    <td class="puso">
        <select name="bulan">
            <?
            $fungsi->ex_bulan($id_bulan);
            ?>
        </select>
        <input type="text" name="tahun" value="<? if($_REQUEST['tahun']) echo $_REQUEST['tahun']; else echo date("Y"); ?>" maxlength="4" size="5">
    </td>
</tr>
<!--
<tr>
    <td class="puso">Posting Year</td>
    <td class="puso">:</td>
    <td class="puso">
    <input type="text" name="posting_year" value="<? if($_REQUEST['posting_year']) echo $_REQUEST['posting_year']; ?>" maxlength="4" size="5"> *
    </td>
</tr>
-->
<tr width="174">
    <td class="puso">&nbsp;</td>
    <td class="puso">&nbsp;</td>
    <td><input name="search_hdr" type="submit" class="button" value="Cari"></td>
</tr>
<tr width="174">
    <td class="puso" colspan="3"><?=$show_ket?></td>
</tr>
</table>
</form>
<br />
<br />
<?
if($total>0){
?>

<? 
/*
echo "total gendral : ".$total_klaim_global;
if ($pass=="T"){?>
<form id="data_claim" name="form_generate_pplfb60_bulanan" method="post" action="komentar.php">
<table>
<tr>
<td>
<input type="hidden" value="generate_ppl_turunan_bulanan" name="action" id="action" />
<input type="hidden" value="<?=$kode_distribur?>" name="kode_distributor" />
<input type="hidden" value="<?=$id_bulan?>" name="bulan" />
<input type="hidden" value="<?=$id_tahun?>" name="tahun" />
<!--<input type="hidden" value="<?=$posting_year?>" name="posting_year" />-->
<input type="submit" value=" GENERATE " name="simpan" class="button"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="<?=$page?>" class="button"><? echo "CANCEL"; ?></a>
</td>
</tr>
</table>
</form>
<? }
*/
?>

<? } // jika data kosong ?>


<?
$ndistr = @count($hasilx);
if($ndistr>0){
    ?>
    <div align="center">
    <table width="900" align="center" class="adminlist">
    <tr>
    <th align="left" colspan="4"><span class="style5">&nbsp;Klaim per Distibutor Periode <?=$id_bulan.'-'.$id_tahun;?></span></th>
    </tr>
    </table>    
    <table width="900" align="center" class="adminlist" id="myScrollTable">
    <tr class="quote">
    <td><strong>&nbsp;&nbsp;NO.</strong></td>
    <td align="center"><strong>Kode Distributor</strong></td>
    <td align="center"><strong>Nama Distributor</strong></td>
    <td align="center"><strong>Klaim Rezak</strong></td>
    <td align="center"><strong>Klaim Semen</strong></td>
    <td align="center"><strong>Total</strong></td>
    <td align="center"><strong>Proses</strong></td>
    <td align="center"><strong>Detail</strong></td>
    <td align="center"><strong>Cetak</strong></td>
    <td align="center"><strong>Keterangan</strong></td>
    </tr>            
    <? $nomor_x = 1;
    foreach($hasilx as $data_d){
        $kode_distributor_x = $data_d[SOLD_TO];
        $nm_distributor_x = $data_d[NAMA_SOLDTO];
        $klaim_rezak_x = $data_d[KLAIM_REZAK];
        $klaim_semen_x = $data_d[KLAIM_SEMEN_V];
        $total = $data_d[KLAIM_ALL];
        $blok = $data_d[RUN_BLOK];
        $cek = cektanggal($conn,$kode_distributor_x,$id_tahun,$id_bulan);
        echo "<tr onMouseover=\"this.bgColor='#EEEEEE'\" onMouseout=\"this.bgColor='#FFFFFF'\">
        <td>&nbsp;&nbsp;".$nomor_x.".</td>
        <td align='center'>".$kode_distributor_x."</td>
        <td align='left'>&nbsp;&nbsp;".$nm_distributor_x."</td>
        <td align='right'>".number_format($klaim_rezak_x,0,',','.')."</td>
        <td align='right'>".number_format($klaim_semen_x,0,',','.')."</td>
        <td align='right'>".number_format($total,0,',','.')."</td>
        <td align='center'>";
        #echo $blok.'-';
        if($blok==0){
            $retunnocalim=cekNoInvoiceClaim($id_tahun, $id_bulan, $kode_distributor_x, $conn, $user_org);
            if($retunnocalim==0){   
                if($cek<>1) {
                    echo "<a href='?kdist=".$kode_distributor_x."&tahun=".$id_tahun."&bulan=".$id_bulan."' onClick=\"if (! confirm('Run klaim ?')) return false;\">run</a>";
                }                
            }else{
                echo "Cek";            
            }            
        }else if($blok>=1) echo "Done";
        ?>    
        </td>
        <td align="center"><a href="javascript:popUp('print_klaim_detail.php?kddis=<?=$kode_distributor_x.'&thn='.$id_tahun.'&bln='.$id_bulan?>')">
            <img src="../images/detail.gif" border="0">
            </a></td>
        <td align="center">
        <? if($blok>=1) { ?>
            <a href="javascript:popUp('print_draft_fb60_fix.php?nomor=<?=$kode_distributor_x.$id_bulan.$id_tahun;?>')">
            <img src="../images/cetak.gif" border="0">
            </a>
        <? } ?>
        </td>
        <td align="center">
        <? if($blok==0 && $cek==1) { ?>
            Tahun Pengiriman dan Invoice Berbeda, Tidak Dapat Dilakukan Run Claim </br>
        <? }
        if(strtotime($data_d[CREATE_DATE])>=strtotime('23-Dec-2017') && $blok>=1){
            if($data_d[APPROVE_KASI] == '0' && $blok>=1){ ?>
                Menunggu Approval Kasi
            <? } else if($data_d[APPROVE_KABIRO] == '0' && $data_d[APPROVE_KASI] == '1'){ ?>
                Menunggu Approval Kabiro
            <? } else if($data_d[APPROVE_KABIRO] == '2' || $data_d[APPROVE_KASI] == '2'){ ?>
                    <? if ($approve_kasi[$i] == '2'){ ?>
                            Rejected Kasi
                    <? } else if($data_d[APPROVE_KABIRO] == '2'){ ?>
                            Rejected Kabiro
        <? }}} ?>
        </td>
        </tr>
        <?
        $nomor_x++;
        $blok=0;
        unset($data_d);
    }
    echo "</table></div>";
    
//    echo "<pre>";
//    print_r($hasilx);
//    echo "</pre>";
    
}

?>


<div align="center">
<?
//echo $komen;
?>
</div>    
<? include ('../include/ekor_sg.php'); 
unset($hasilx);
?>
</body>
</html>