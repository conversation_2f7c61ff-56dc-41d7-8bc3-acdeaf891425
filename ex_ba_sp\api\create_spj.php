<?php
include ('../../include/ex_fungsi.php');

// --- Konfigurasi username & password ---
$valid_users = array(
    "smbr-jaya" => "i-love-smbr"
);

// --- Ambil credential dari header Authorization ---
if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("success" => false, "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

// --- Validasi login ---
if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("success" => false, "message" => "Invalid credentials"));
    exit;
}

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

function generate_no_shp_trn($conn) {
    $sql = "SELECT MAX(TO_NUMBER(SUBSTR(NO_SHP_TRN, 5))) AS MAX_NO 
            FROM DEV.EX_TRANS_HDR 
            WHERE NO_SHP_TRN LIKE '9999%'";
    $stmt = oci_parse($conn, $sql);
    oci_execute($stmt);

    $max_no = 0;
    $row = oci_fetch_array($stmt, OCI_ASSOC);
    if ($row && isset($row['MAX_NO'])) {
        $max_no = (int)$row['MAX_NO'];
    }

    $next_no = $max_no + 1;
    $next_no_padded = str_pad($next_no, 6, "0", STR_PAD_LEFT);
    return '9999' . $next_no_padded;
}

function is_date_column($col) {
    return preg_match('/TANGGAL|DATE|TGL/i', $col);
}

function write_log($message, $filename = 'log.txt') {
    $log_file = dirname(__DIR__) . '/' . $filename;
    $date = date('Y-m-d H:i:s');
    $entry = "[$date] $message" . PHP_EOL;

    $fh = fopen($log_file, 'a');
    if ($fh) {
        fwrite($fh, $entry);
        fclose($fh);
    }
}

$columns = array (
    'NO_SHP_TRN',
    'NO_SHP_SAP',
    'PLANT',
    'ORG',
    'SOLD_TO',
    'SHIP_TO',
    'SAL_DISTRIK',
    'SAL_GROUP',
    'SAL_OFFICE',
    'VENDOR',
    'NAMA_VENDOR',
    'NAMA_SAL_OFF',
    'NAMA_SAL_GRP',
    'NAMA_SAL_DIS',
    'NO_POL',
    'VEHICLE_TYPE',
    'WARNA_PLAT',
    'KODE_PRODUK',
    'NAMA_PRODUK',
    'KODE_KANTONG',
    'NAMA_KANTONG',
    'KODE_SEMEN',
    'NAMA_SEMEN',
    'QTY_KTG_RUSAK',
    'QTY_SEMEN_RUSAK',
    'QTY_SHP',
    'TOTAL_KTG_RUSAK',
    'TOTAL_KTG_REZAK',
    'TOTAL_SEMEN_RUSAK',
    'TOTAL_KLAIM_KTG',
    'TOTAL_KLAIM_SEMEN',
    'HARGA_TEBUS',
    'PDPKS',
    'KOMPENSASI',
    'TOTAL_KLAIM_ALL',
    'TANGGAL_DATANG',
    'TANGGAL_BONGKAR',
    'TANGGAL_INVOICE',
    'APPROVE_BY',
    'STATUS',
    'NO_INVOICE',
    'INVOICED_BY',
    'TIPE_TRANSAKSI',
    'TANGGAL_KIRIM',
    'SHP_COST',
    'KODE_SHP_COST',
    'NO_INV_VENDOR',
    'CREATE_DATE',
    'CREATED_BY',
    'LAST_UPDATE_DATE',
    'LAST_UPDATED_BY',
    'DELETE_MARK',
    'NAMA_PLANT',
    'NAMA_ORG',
    'STATUS_APP',
    'NOTE',
    'NO_INV_SAP',
    'NAMA_SOLD_TO',
    'NAMA_SHIP_TO',
    'ALAMAT_SHIP_TO',
    'TANGGAL_APPROVE',
    'HARGA_JUAL',
    'SATUAN_SHP',
    'KELOMPOK_TRANSAKSI',
    'STATUS2',
    'DENDA_PARKIR',
    'NO_ENTRY_SHEET',
    'NO_PAJAK_EX',
    'KAPASITAS_VEHICLE',
    'UOM_KAPASITAS',
    'KET_ERROR',
    'PLANT_RCV',
    'ORG_RCV',
    'PENGELOLA',
    'NAMA_PENGELOLA',
    'DOC_SHP',
    'TIPE_DO',
    'INCO',
    'TARIF_COST',
    'OPEN_BY',
    'OPEN_DATE',
    'BLOCK_BY',
    'BLOCK_DATE',
    'EBELN',
    'EBELP',
    'KOSTL',
    'PRCTR',
    'NAMA_KAPAL',
    'KLAIM_LEBIH',
    'PDPKS_LEBIH',
    'KLAIM_ALL_LEBIH',
    'QTY_LEBIH',
    'NO_REK_DIS',
    'NAMA_BANK_DIS',
    'BANK_CABANG_DIS',
    'NO_GL_SHP',
    'SUPIR',
    'KODE_KECAMATAN',
    'NAMA_KECAMATAN',
    'MODE_TRANSPORT',
    'NO_SO',
    'BERAT_TERIMA',
    'UM_REZ',
    'NO_TAGIHAN',
    'ACCOUNTING_DOC',
    'BVTYP',
    'SHP_COST_E',
    'TIPE_SO',
    'NO_SPPS',
    'PAJAK_N',
    'TGL_CMPLT',
    'TGL_FLAG',
    'FERIFY_BY',
    'TGL_CLEARING',
    'CLEARING_DOC',
    'STATUS_RUNFB60',
    'FLAG',
    'TGL_PEMUATAN',
    'DISTANCE',
    'REASON',
    'FI_DOC',
    'AMOUNT',
    'STATUS_CLAIM_DIST',
    'UPDATE_CLAIM_DIST_BY',
    'UPDATE_CLAIM_SIT_DATE',
    'NO_DOC_HDR',
    'TAHUN_FB60',
    'TANGGAL_SIAP_TAGIH',
    'QTY_KTG_RUSAK_DIST',
    'QTY_SEMEN_RUSAK_DIST',
    'STATUS_CLAIM',
    'BVTYP_DIS',
    'REJECT_STATUS',
    'FLAG_POD',
    'KETERANGAN_POD',
    'EVIDENCE_POD1',
    'EVIDENCE_POD2',
    'GEOFENCE_POD',
    'NO_BA',
    'TANGGAL_BA',
    'READY_TO_INV',
    'NO_SHP_TRN2'
);

// Buat nomor transaksi
$no_shp_trn = generate_no_shp_trn($conn);
$_POST['NO_SHP_TRN'] = $no_shp_trn;
$_POST['ORG'] = '1000';

// Siapkan array
$insert_columns = array();
$insert_placeholders = array();
$bind_values = array();

foreach ($columns as $col) {
    if (!isset($_POST[$col]) || trim($_POST[$col]) === '') {
        continue; // skip kolom yang tidak dikirim dari form
    }

    $value = $_POST[$col];

    // Format tanggal
    if (is_date_column($col)) {
        $timestamp = strtotime($value);
        if ($timestamp !== false) {
            $value = strtoupper(date('d-M-Y H:i:s', $timestamp));
            $insert_placeholders[] = "TO_DATE(:$col, 'DD-MON-YYYY HH24:MI:SS')";
        } else {
            $insert_placeholders[] = ":$col";
            $value=null;
        }
    } else {
        $insert_placeholders[] = ":$col";
    }

    $insert_columns[] = $col;
    $bind_values[$col] = $value;
}

if (count($insert_columns) === 0) {
    echo json_encode(array('success' => false, 'msg' => 'Tidak ada data yang dikirim.'));
    exit;
}

$sql = "INSERT INTO EX_TRANS_HDR (" . implode(',', $insert_columns) . ") VALUES (" . implode(',', $insert_placeholders) . ")";
$stmt = oci_parse($conn, $sql);

// cek bind value
// foreach ($bind_values as $key => $val) {
//     write_log("Bind $key => " . var_export($val, true));
// }

// Bind variabel
foreach ($bind_values as $key => &$val) {
    oci_bind_by_name($stmt, ":$key", $val);
}

// Eksekusi
$result = oci_execute($stmt);

if ($result) {
    echo json_encode(array('success' => true, 'msg' => 'Berhasil Create Shipment dengan NO_SHP_TRN: ' . $no_shp_trn));
} else {
    $e = oci_error($stmt);
    echo json_encode(array('success' => false, 'msg' => 'Gagal Create Shipment: ' . $e['message']));
}
?>
