<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);

    if ($role['status'] == true && $jmlData > 0) {

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      $dirr = $_SERVER['PHP_SELF'];
      if (empty($token_in)) {
        $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap" , "data" => null);
        header('Content-Type: application/json');
        echo json_encode($responseRequest);
      } else {
        $getBrand = "";

        $param['TAHUN'] = $_POST['TAHUN'];
        $param['BASTB'] = $_POST['BAMSA'];
        // $param['BRAND'] = empty($_POST['BRAND']) ? $getBrand : $_POST['BRAND'];
        $param['I_TRANSACTION_TYPE'] = "U";
        $param['INVOICE_AP'] = $_POST["INVOICE_AP"];
        $param['INVOICE_AR'] = $_POST["INVOICE_AR"];

        $get = new sv_bamsa();
        $result = $get->sv_data($param);
        
        if ($result["RETURN"]["TYPE"] != "S") {
          $responseRequest = array(
            'responseCode' => 404,
            'responseMessage' => 'Failed Data Update',
            'data' => null
          );
          header('Content-Type: application/json');
          echo json_encode($responseRequest);
        } else {
          if ($result["T_DATA"]) {
            foreach ($result["T_DATA"] as $k => $v) {
              $param2["I_BUKRS"] = $v["KORPE"];
              $param2["I_BASTB"] = $v["BAMSA"];
              $param2["I_DISP"] = "";
  
              $result_detail = $get->get_data_detail($param2);
              $result["T_DATA"][$k]["BILLING"] = $result_detail;
              // if ($v["PEMILIK_BRAND"] == "1000") {
              $param3["I_BASTB"] = $v["BAMSA"];
              $param3["I_INV"] = "X";
              $result_update = $get->updateStatusDoc($param3);
              // }
            }
          }

          $responseRequest = array(
            'responseCode' => 200,
            'responseMessage' => "Success Data Update",
            'data' => $result["T_DATA"]
          );
          header('Content-Type: application/json');
          echo json_encode($responseRequest);
        }
      }
    } else {
      $responseRequest = array(
        'responseCode' => 401,
        'responseMessage' => $role,
        'data' => null
      );
      header('Content-Type: application/json');
      echo json_encode($responseRequest);
    }
    $byLog = 'sv_bamsa';
    $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class sv_bamsa
{

  private $_basePath;
  private $_sapCon;
  private $_sapCon2;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
    $this->_sapCon2 = "../include/sapclasses/logon_data_mdr.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function sv_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZDIS_INT_MON_BASTMSA");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {
    $fce->TAHUN = $param['TAHUN'];
    $fce->DOCUMENT_NUMBER = $param['DOCUMENT_NUMBER'];
    $fce->BAMSA = $param['BASTB'];
    $fce->I_TRANSACTION_TYPE = $param['I_TRANSACTION_TYPE'];
    $fce->INVOICE_AP = $param['INVOICE_AP'];
    $fce->INVOICE_AR = $param['INVOICE_AR'];
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->T_DATA->Reset();
      while ($fce->T_DATA->Next()) {
        $fce->T_DATA->row['NTGEW'] = (float) $fce->T_DATA->row['NTGEW'];
        $fce->T_DATA->row['NETWR'] = $fce->T_DATA->row['NETWR'] * 100;
        $fce->T_DATA->row['FWBAS'] = $fce->T_DATA->row['FWBAS'] * 100;
        $fce->T_DATA->row['FWSTE'] = $fce->T_DATA->row['FWSTE'] * 100;
        $fce->T_DATA->row['TOTMS'] = $fce->T_DATA->row['TOTMS'] * 100;
        $fce->T_DATA->row['WHTTAX'] = $fce->T_DATA->row['WHTTAX'] * 100;
        $fce->T_DATA->row['BRAND'] = $fce->T_DATA->row['BRAND'] == "SMBR-MDK" ? "SMBR" : $fce->T_DATA->row['BRAND'];
        $fce->T_DATA->row['BEGDA'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['BEGDA'])));
        $fce->T_DATA->row['ENDDA'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['ENDDA'])));
        $fce->T_DATA->row['CPUDT'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['CPUDT'])));
        $fce->T_DATA->row['DBAST2'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DBAST2'])));
        $fce->T_DATA->row['DPRD1'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRD1'])));
        $fce->T_DATA->row['DPRD2'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRD2'])));
        $fce->T_DATA->row['DPRC'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DPRC'])));
        $fce->T_DATA->row['DELDAT'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DELDAT'])));
        $fce->T_DATA->row['DSPG'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DSPG'])));
        $fce->T_DATA->row['DICM'] = strval(date('Y-m-d', strtotime($fce->T_DATA->row['DICM'])));
        $this->_data['T_DATA'][] = $fce->T_DATA->row;
      }
      $this->_data["RETURN"] = $fce->RETURN;
    }
    return $this->_data;
  }

  function get_data_detail($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCFM_DISP_BASTMSA_DTL_MSA");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc_detail($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc_detail($fce, $param)
  {
    $data_detail = array();
    $map_detail = array();
    $result_detail = array();
    
    
    $fce->I_BUKRS = $param['I_BUKRS'];
    $fce->I_BAMSA = $param['I_BASTB'];
    $fce->I_DISP = $param['I_DISP'];

    $fce->Call();
    
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->IT_DATA->Reset();
      while ($fce->IT_DATA->Next()) {
        $fce->IT_DATA->row['NTGEW'] = (float) $fce->IT_DATA->row['NTGEW'];
        $fce->IT_DATA->row['NETWR'] = $fce->IT_DATA->row['NETWR'] * 100;
        $fce->IT_DATA->row['FWBAS'] = $fce->IT_DATA->row['FWBAS'] * 100;
        $fce->IT_DATA->row['FWSTE'] = $fce->IT_DATA->row['FWSTE'] * 100;
        $fce->IT_DATA->row['TOTMS'] = $fce->IT_DATA->row['TOTMS'] * 100;
        $fce->IT_DATA->row['FKDAT'] = strval(date('Y-m-d', strtotime($fce->IT_DATA->row['FKDAT'])));
        $fce->IT_DATA->row['CPUDT'] = strval(date('Y-m-d', strtotime($fce->IT_DATA->row['CPUDT'])));
        $data_detail[] = $fce->IT_DATA->row;
      }
    }

    foreach ($data_detail as $key => $value) {
      $map_detail[$value["VBELN"]][] = $value;
    }

    $itr_result = 0;
    foreach ($map_detail as $key => $value) {
      foreach ($value as $key2 => $value2) {
        $result_detail[$itr_result]["BAMSA"] = $value2["BAMSA"];
        $result_detail[$itr_result]["BUKRS"] = $value2["BUKRS"];
        $result_detail[$itr_result]["VBELN"] = $value2["VBELN"];
        $result_detail[$itr_result]["VKORG"] = $value2["VKORG"];
        $result_detail[$itr_result]["FKDAT"] = $value2["FKDAT"];
        $result_detail[$itr_result]["NTGEW"] = $value2["NTGEW"];
        $result_detail[$itr_result]["NETWR"] = $value2["NETWR"];
        $result_detail[$itr_result]["FWBAS"] = $value2["FWBAS"];
        $result_detail[$itr_result]["FWSTE"] = $value2["FWSTE"];
        $result_detail[$itr_result]["TOTMS"] = $value2["TOTMS"];
        $result_detail[$itr_result]["WAERK"] = $value2["WAERK"];
        $result_detail[$itr_result]["ITEM"][] = $value2;
      }
      $itr_result++;
    }

    return $result_detail;
  }

  function updateStatusDoc($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    // echo "<pre>";
    // print_r($sap);
    // echo "</pre>";exit;
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCFM_UPD_BASTMSA_STAT");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc_update_status_doc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc_update_status_doc($fce, $param)
  {
    $return = array();
    
    $fce->I_BAMSA = $param['I_BASTB'];
    $fce->I_INV = $param['I_INV'];

    $fce->Call();
    
    if ($fce->GetStatus() == SAPRFC_OK) {
      $return["RETURN"] = $fce->E_MESSAGE;
    }
    return $return;
  }
}
