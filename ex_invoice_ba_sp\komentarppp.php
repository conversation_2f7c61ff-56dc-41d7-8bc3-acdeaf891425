<?php 

session_start();
include ('../include/email.php');
$email = new Email();
require_once ("potonganoa_formula.php");
require_once('phpmailer.php');
require_once('class.smtp.php');
require_once ('../security_helper.php');
sanitize_global_input();
// mulai session untuk semua formula
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
.style1 {font-size: 20px}
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<script src="../include/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Untitled Document</title>
</head>

<body>
<p>&nbsp;</p>
<p>&nbsp;</p>
<? 
include ('../include/ex_fungsi.php');
//include ('../../../../prod/sd/sdonline/include/ex_fungsi.php');



$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$user_name_cek_id=$_SESSION['user_name'];
if($user_name_cek_id != ""){
$action=$_POST['action'];

// echo $action;exit;
include ('formulappp.php');
}else{
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Session Login Anda Habis.... \n Silahkan Login Ulang...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?
exit();
}

?>

<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
<div class="alert alert-info" role="alert">
<strong>Pesan!</strong>
<?=$show_ket;?>
<br/>
<a href="<? echo $habis ?>">&lt;&lt; &nbsp;kembali&nbsp;&gt;&gt;</a>
<? if ($action == "create_invoice_bag_darat" or $action == "create_invoice_curah_darat" ){?>
<a href="print_invoice.php?no_invoice=<?=$no_invoice_in?>">&lt;&lt; &nbsp;cetak&nbsp;&gt;&gt;</a>
<? }?>

<? if ($action == "generate_ppl" and $pass_trn > 1){ ?>
<? 
/* <a href="run_ppl_trn.php?no_invoice=<?=$no_invoice?>">&lt;&lt; &nbsp;PPL TURUNAN&nbsp;&gt;&gt;</a> */
}
?>
</div>
</div>
<? include ('ekor.php'); ?>
</p>
<p>&nbsp;</p>
<p>&nbsp;</p>

</body>
</html>
