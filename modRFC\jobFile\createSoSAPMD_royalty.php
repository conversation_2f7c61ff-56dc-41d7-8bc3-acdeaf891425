<?php
/**
 * description : class untuk scheduller post data create SO SAP
 * author : PT.Artavel
 * tambah field NAMA_KAPAL DI Tabel OR_TRANS_HDR, field NO_KONTRAK_POSNR Di tabel OR_TRANS_DTL
 * edit file or_transaksi/formula.php edit case tambah new untuk insert variabel $nama_kapal ke field NAMA_KAPAL
 */
set_time_limit(0);
class createSoSAPMD_royalty {

    private $fungsi;
    private $conn;
    private $logfile;
    public $msg;
    public $dataFuncRFC;
    private $status;

    function __construct() {
        $this->status = 'SUKSES';
        $this->fungsi = new or_fungsi();
        $this->conn = $this->fungsi->or_koneksi();
        // $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
    }

    function saveLog() {
        $this->msg = substr($this->msg, 0, 900);
        $sqllog = "INSERT INTO RFC_LOG VALUES ('CREATE SO MD',SYSDATE,'" . $this->msg . "')";
        $querylog = oci_parse($this->conn, $sqllog);
        if ($querylog) {
            $execlog = @oci_execute($querylog, 0);
        }
        //set running down
        $sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
        $query_run = oci_parse($this->conn, $sqlset_run);
        oci_execute($query_run, 0);
        //end set
    }

    function run($group_plant='') {
        // fwrite($this->logfile, "Start ".get_class($this)."pada tanggal jam ".date('d-m-Y H:i:s')."\n");
        $this->msg = "Start " . get_class($this) . "pada tanggal jam " . date('d-m-Y H:i:s') . "\n";
        $datapp = array();
        $sqlhdr = "SELECT * FROM or_trans_hdr 
                                left join Z_MAINTENANCE_PLANT_SO on Z_MAINTENANCE_PLANT_SO.PLANT=or_trans_hdr.PLANT_ASAL and Z_MAINTENANCE_PLANT_SO.DELETE_MARK=0
                                left join or_trans_dtl on or_trans_dtl.no_pp=or_trans_hdr.no_pp
                                WHERE 
                              (or_trans_hdr.STATUS = 'OPEN'
                                    OR or_trans_hdr.STATUS = 'PROCESS')
                                AND or_trans_hdr.SO_TYPE IN ('ZOR', 'ZFC')
                                AND (or_trans_dtl.STATUS_LINE = 'OPEN'
                                    OR or_trans_dtl.status_line = 'PROCESS')
                                AND (or_trans_hdr.BPLANT <> 'OTHER'
                                    OR or_trans_hdr.BPLANT IS NULL)
                                AND or_trans_hdr.NO_SHP_OLD IS NULL
                                AND TO_CHAR(OR_TRANS_HDR.CREATE_DATE , 'YYYY-MM-DD') = TO_CHAR(CURRENT_DATE, 'YYYY-MM-DD')
                                AND or_trans_hdr.DELETE_MARK = 0
                                AND or_trans_dtl.DELETE_MARK = 0
                                AND or_trans_hdr.TIPEPP = 'NEW PP'
                                AND or_trans_hdr.TERM_PAYMENT IS NOT NULL
                                AND or_trans_hdr.NAMA_TOP IS NOT NULL
                                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'
                                AND or_trans_hdr.ORG in ('7900','7000')
                                AND Z_MAINTENANCE_PLANT_SO.DELETE_MARK = '0'
                                AND (or_trans_hdr.LAST_UPDATED_BY <> 'scheduler'
                                    AND or_trans_hdr.LAST_UPDATED_BY <> 'SCHEDULER'
                                    AND or_trans_hdr.LAST_UPDATED_BY <> 'SCEDULER')
                                AND Z_MAINTENANCE_PLANT_SO.GRP_PLANT='A'
                                AND rownum <= 25
                                FOR UPDATE skip locked
                            ";

        $queryhdr = oci_parse($this->conn, $sqlhdr);
        oci_execute($queryhdr, 0);
        $i = 0;
        $dataResult = array();
        while ($datafunc = oci_fetch_assoc($queryhdr)) {
            $datapp[$i] = $datafunc;
            $datapp[$i]['TRANS_DTL'] = array();
            ///////////////////////////////////////////////// proyek SOCC V.2
            // $sqldtl = "SELECT * FROM OR_TRANS_DTL WHERE NO_PP = '" . $datafunc['NO_PP'] . "'";
            $sqldtl = "SELECT DISTINCT
                            CREATESOSAP7900ZAK_V.TANGGAL_PRELEADTIME,
                            or_trans_dtl.*
                        FROM
                            OR_TRANS_DTL
                        LEFT JOIN CREATESOSAP7900ZAK_V ON
                            or_trans_dtl.NO_PP = CREATESOSAP7900ZAK_V.NO_PP WHERE or_trans_dtl.NO_PP = '" . $datafunc['NO_PP'] . "'";
            ///////////////////////////////////////////////
            $querydtl = oci_parse($this->conn, $sqldtl);
            oci_execute($querydtl, 0);
            $this->msg .= $datafunc['NO_PP'];
            while ($datadtl = oci_fetch_assoc($querydtl)) {
                array_push($datapp[$i]['TRANS_DTL'], $datadtl);
                // $this->msg .= "while";
            }
            $i++;
        }
        if (count($datapp) > 0) {
            for ($i = 0; $i < count($datapp); $i++) {
                // $this->postSoSAP_MD($datapp[$i]);
                // $this->postSoSAP($datapp[$i]);
                $resultPostSoSAP = $this->postSoSAP($datapp[$i]);
                if ($resultPostSoSAP) {
                   $dataResult[]  = $resultPostSoSAP;
                }
                // $this->msg .= "sap";
            }
        } else {
            echo 'Tidak ada data';
        }

        // sv order API Epoool Push SO
        if (!empty($dataResult)) {
        $url = 'https://dev-integrasi-api.sig.id/epoool/api/sales_order';
        $username = 'login_epoool';
        $password = 'S1G-3p0OoL3#';
        $token = 'Bearer hc6ULG54uzXugcjYBhHg';
        $options = array(
            'http' => array(
                'header'  => "Content-type: application/json\r\n" .
                            "Authorization: Basic " . base64_encode("$username:$password") . "\r\n" .
                            "token: $token\r\n",
                'method'  => 'POST',
                'content' => json_encode($dataResult),
            )
        );
        $context  = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        }

        $this->saveLog();
        oci_commit($this->conn);
        // fclose($this->logfile);
    }
    function so_reff_m_ICS($SOMD,$PONUMBER,$SOSMBR){
            //multi ics carcons
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                //echo $sap->PrintStatus();
                exit;
            }
            
            $fce2 = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
            $fce2->SALESDOCUMENT = $SOMD;//"ZOR";
            $fce2->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
            $fce2->ORDER_HEADER_INX["PURCH_NO_S"]='X';
            $fce2->ORDER_HEADER_INX["REF_1_S"]='X';                                                
        
            $fce2->ORDER_HEADER_IN["PURCH_NO_S"]= $PONUMBER;
            $fce2->ORDER_HEADER_IN["REF_1_S"]= $SOSMBR;                                               
            // $fce2->ORDER_HEADER_INX["ORD_REASON"] = '001';
            
            
            //detail entri item
            $fce2->ORDER_ITEM_INX->row["PURCH_NO_S"]='X';
            $fce2->ORDER_ITEM_INX->row["REF_1_S"]='X';
            $fce2->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';//"Z1";
            $fce2->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
            $fce2->ORDER_ITEM_INX->Append($fce2->ORDER_ITEM_INX->row);
        
            $fce2->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
            $fce2->ORDER_ITEM_IN->row["PURCH_NO_S"] = $PONUMBER;
            $fce2->ORDER_ITEM_IN->row["REF_1_S"]= $SOSMBR;
            $fce2->ORDER_ITEM_IN->row["UPDATEFLAG"] = 'U';//"Z1";
            $fce2->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
            $fce2->ORDER_ITEM_IN->Append($fce2->ORDER_ITEM_IN->row);
        
            $fce2->Call();
            
            if ($fce2->GetStatus() == SAPRFC_OK ) {
                $fce2->RETURN->Reset();
                while ($fce2->RETURN->Next()){
                $error = $fce2->RETURN->row["TYPE"];
                // $this->_data[] = $fce2->RETURN->row;
                // $msg .= ' ';
                $msg = $fce2->RETURN->row["MESSAGE"];
                }
                //Commit Transaction
                $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                $fce->Call();
                
                $fce->Close();
        
            } else{
                $fce2->PrintStatus();
            }
                $fce2->Close();
        
            return $msg;        
    }

    function deleteso_smbr($datadelparam){

        // print_r("delete rollback so smbr (no pp) ".$datadelparam["csmsnumber"]);
    
        $curl = curl_init();
        // echo "Get Token </br>";
        curl_setopt_array($curl, array(
          //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/auth/signin',
          CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/auth/signin',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{
          "username":"smbr-md",
            "password":"ciT@la-Plg"
        }
        }',
          CURLOPT_HTTPHEADER => array(
            'api-key: TissueBasaHCrlfUtf8@Spaces4Ln35Col44!!!',
            'Content-Type: application/json'
          ),
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $return_signin_smbr = json_decode($response, true);
                $token = $return_signin_smbr["token"];
    
        //////////////////////////////////////////////////////////
    
        $curl = curl_init();
    
        curl_setopt_array($curl, array(
        //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/v1/so/cancel',
        CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/v1/so/cancel',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "csmsNumber":"'.$datadelparam["csmsnumber"].'",
            "reasonRejectionCode":"Z3"
        }',
        CURLOPT_HTTPHEADER => array(
            'Authorization:Bearer '.$token,
            'Content-Type:application/json'
        ),
        ));
    
        $response = curl_exec($curl);
    
        curl_close($curl);
        // echo $response;
    
        return $response;
    
    }
    
    function createso_smbr($dataparam){
    
                $curl = curl_init();
    
                curl_setopt_array($curl, array(
                  //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/v1/so/create',
                  CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/v1/so/create',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>'{
                    "csmsNumber":"'.$dataparam["csmsnumber"].'",
                    "sosigNumber":"'.$dataparam["sosignumber"].'",
                    "plant":"'.$dataparam["plant"].'",
                    "documentType":"'.$dataparam["documenttype"].'",
                    "soldToParty":"'.ltrim(trim($dataparam["soldtoparty"]),'0').'",
                    "shipToParty":"'.trim($dataparam["shiptoparty"]).'",
                    "paymentMethod":"'.$dataparam["paymentmethod"].'",
                    "paymentTerm":"'.$dataparam["paymentterm"].'",
                    "requestDeliveryDate":"'.$dataparam["requestdeliverydate"].'",
                    "shippingType":"'.$dataparam["shippingType"].'",
                    "incoterm":"'.$dataparam["incoterm"].'",
                    "transactionCategory":"'.$dataparam["transaction_category"].'",
                    "businessPartner":"'.$dataparam["bussinies_partner"].'",
                    "deliveryBlock":"'.$dataparam["deliveryBlock"].'",
                    "subdistCode":"'.$dataparam["subdistCode"].'",
                    "subdistName":"'.trim($dataparam["subdistName"]).'",
                    "royaltyFlag": "'.$dataparam["royaltyFlag"].'",
                    "items":[
                        {
                            "csmsNumber":"'.$dataparam["csmsnumber"].'",
                            "itemNumber":"'.$dataparam["itemnumber"].'",
                            "material":"'.$dataparam["material"].'",
                            "salesUnit":"'.$dataparam["salesunit"].'",
                            "quantity":"'.$dataparam["quantity"].'",
                            "priceList":"'.$dataparam["pricelist"].'"
                        }
                    ]
                }',
                  CURLOPT_HTTPHEADER => array(
                    'Authorization:Bearer '.$dataparam["token"],
                    'Content-Type:application/json'
                  ),
                ));
                
                $response = curl_exec($curl);
                // print_r("return CURL SMBR".curl_error($curl));       
                curl_close($curl);
    
                // echo $response;  
                // echo '<pre>';    
                // print_r($response);
    
            $data_output = json_decode($response, true);
    
            // print_r($data_output);
                // print_r($data_output["status"]);
    
                if($data_output["status"]=="200"){
                    $pesan = $response;
                    // $pesan[] = $datarequest;
                    // print_r($data_output["data"]["items"]["salesUnit"]);
                    // $returnsmbr["sonumber"]= $data_output["data"]["soNumber"];
                    // $returnsmbr["salesunit"]= $data_output["data"]["salesUnit"];
                    // $returnsmbr["subtotal"]= $data_output["data"]["subtotal"];
                }else{
                    // print_r("data tidak ada");
                    $pesan = "data tidak ada ( message : ".$data_output["message"]." errorMessage : ".$data_output["errorMessage"]." )";
                }
    
                // $returndata["daatarequest"]=json_decode($datarequest,true);
                // $retrundata["response"]=$data_output;
                // $returndata["token"]=$token;
                return $pesan;
    
    }

    function postSoSAP($datapp){
            $show_ket = 'proses pp no ' . @$datapp['NO_PP'] . "\n";
            echo "<br>";

            // CEK MAPPING PLANT ADA ATAU TIDAK
            $plantcek = $datapp['PLANT_ASAL'];
            $prclst     = $datapp['PRICELIST'];
            $inctm = $datapp['INCOTERM'];
            $str = "SELECT * FROM ZMD_MAPPING_PLANT
            WHERE PLANT_MD = '{$plantcek}' AND  DEL=0 AND PRICE_LIST ='{$prclst}'
            ";
            $query = oci_parse($this->conn, $str);
            oci_execute($query, 0);
            $row = oci_fetch_array($query, OCI_ASSOC);
            $komentarerrorincoterm = '';
            if(count($row) <= 1){

                $str1 = "SELECT * FROM ZMD_MAPPING_PLANT
                WHERE PLANT_MD = '{$plantcek}' AND  DEL=0
                ";

                //filter plant change adyjuliyanto
                $query1 = oci_parse($this->conn, $str1);
                oci_execute($query1, 0);
                $arr_data_plant = array();
    
                while ($temp_data = oci_fetch_array($query1, OCI_ASSOC)) {
                    $arr_data_plant[] = $temp_data;
                }

                
                //jika plant hanya satu
                if(count($arr_data_plant) <= 1){
                    //mengubah array menjadi objek sehingga perubahan code tidak terlalu banyak
                    $row = reset($arr_data_plant);
                }

                //jika ada plant lebih dari satu
                if(count($arr_data_plant) > 1){
                    $nullIncotermValues = array();
                    
                    $incoterm_found = false;
                    //filter jika ada incoterm di mapping plant
                    foreach ($arr_data_plant as $element) {  
                        if (trim($element['INCOTERM_MD']) === trim($inctm)) {
                            $incoterm_found = true;
                            $nullIncotermValues[] = $element;
                        }  
                    }

                    //jika tidak ditemukan akan mengambil incoterm yang bernilai null
                    if(!$incoterm_found){
                        foreach ($arr_data_plant as $element) {  
                            if (!isset($element['INCOTERM_MD']) || $element['INCOTERM_MD'] == null) {
                                $nullIncotermValues[] = $element;
                            }  
                        }
                    }

                    
                    //mengubah array menjadi objek sehingga perubahan code tidak terlalu banyak
                    $row = reset($nullIncotermValues);
                    
                    if(count($row) < 1){
                        $komentarerrorincoterm = 'Silahkan cek mapping incoterm ( plant ' . $_POST['plant'] . ' lebih dari satu )';
                    }
                    
                }
            }
            
            $lanjut = 'Y';
            $next = 'Y';
            $SO1 = '';
            $IDH1 = '';
            $SO2 = '';
            $IDH2 = '';
            $SO3 = '';
            $IDH3 = '';
            $SO4 = '';
            $IDH4 = '';
            $jerror = 0;
            $SOMDNE = 'N';
            $com_msa = '';
            $isMsa = '';
            $com_royalty = '';
            $isRoyalti = '';

            if (count($row) > 0) {
                // CEK MAPPING CUSTOMER ADA ATAU TIDAK
                $soldtocek = $datapp['SOLD_TO'];

                // echo '<pre>';
                $strsoldto = "SELECT * 
                FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                WHERE SOLD_TO_MD = '{$soldtocek}'  AND DEL = 0 AND PLANT_MD ='{$plantcek}'
                AND ORG_OPCO = '{$row['COM_OPCO']}'";
                $querysoldto = oci_parse($this->conn, $strsoldto);
                oci_execute($querysoldto, 0);
                $rowsoldto = oci_fetch_array($querysoldto, OCI_ASSOC);

                // echo $strsoldto; echo '<br>';

                // var_dump($strsoldto);
                if(count($rowsoldto) <= 1){
                    $strsoldto1 = "SELECT * 
                    FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                    WHERE SOLD_TO_MD = '{$soldtocek}'  AND DEL = 0 AND PLANT_MD ='ALL'
                    AND ORG_OPCO = '{$row['COM_OPCO']}'";
                    $querysoldto1 = oci_parse($this->conn, $strsoldto1);
                    oci_execute($querysoldto1, 0);
                    
                    $rowsoldto = oci_fetch_array($querysoldto1, OCI_ASSOC); 
                    // echo $strsoldto1; echo '<br>';
                }
                
                if(count($rowsoldto) <= 1){
                    $strsoldto2 = "SELECT * 
                    FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                    WHERE SOLD_TO_MD = '{$soldtocek}'  AND DEL = 0 AND PLANT_MD ='ALL'
                    AND ORG_OPCO = 'ALL'";;
                    $querysoldto2 = oci_parse($this->conn, $strsoldto2);
                    oci_execute($querysoldto2, 0);
                    
                    $rowsoldto = oci_fetch_array($querysoldto2, OCI_ASSOC); 
                    // echo $strsoldto2; echo '<br>';
                }
                if (count($rowsoldto) <= 1) {
                    $lanjut = 'N';
                    $komentarerror = 'SOLD TO belum Termapping : ' . $soldtocek;
                    // echo $komentarerror;
                }
                } else {
                $lanjut = 'N';
                $komentarerror = 'Plant MD belum ada : ' . $datapp['PLANT_ASAL'];
                $komentarerror .= '<br>'.$komentarerrorincoterm;
            }
            // print_r("org opco 2 :".$row["COM_OPCO_2"]);            
            // print_r("ocp 2 :".$row["PLANT_OPCO_2"]);
            // print_r("MD".$row["COM_MD_2"]);            
            // print_r("opc MD".$row["PLANT_MD_2"]);
            // CEK ICS
            if ($row["COM_MD_2"] != "" && $row["PLANT_MD_2"] != "" && $row["COM_OPCO_2"] != "" && $row["PLANT_OPCO_2"] != "") {
                $perulangan = 5;
                $perulanganSO = 4;
                $perulanganPO = 3;
                $ics = "Y";
                $loncat = "N";
    //            echo 'ICS INI 2';
            } else if ($row["COM_MD_2"] == "" && $row["PLANT_MD_2"] == "" && $row["COM_OPCO_2"] != "" && $row["PLANT_OPCO_2"] != "") {
                $perulangan = 5;
                $perulanganSO = 4;
                $perulanganPO = 2;
                $ics = "Y";
                $loncat = "Y"; /// MD @ tidak di pakai
                $cek_com_opco2 = 'ada';
    //            echo 'ICS INI 1';
            } else {
                $perulangan = 3;
                $perulanganSO = 2;
                $perulanganPO = 1;
                $ics = "N";
                $loncat = "N";
    //            echo 'ICS INI 3';
            }
    
    
    
            // JIKA LOLOS PENGECEKAN MAPPING PLANT DAN CUSTOMER
            if ($lanjut == 'Y') {
                $rdd_from_SO = '';
                $deliv_date = '';
                // PERULANGAN 3 KALI (1.parameter normal,2.ada beberapa parameter berubah, 3. create PO otomatis )
                for ($ul = 1; $ul <= $perulangan; $ul++) {
    
                    if ($next == 'Y') {
    
                        $incoterm1 = $datapp['INCOTERM'];
                        $incoterm2 = $datapp['NAMA_INCOTERM'];
                        $route     = $datapp['ROUTE'];
                        $top       = $datapp['TERM_PAYMENT'];
                        $nama_top  = $datapp['NAMA_TOP'];

                        if ($ul == 1) {
                            $sales_org = $datapp['ORG']; // GANTI ORG MAPPING 2
                            $sales_org1 = $datapp['ORG']; // GANTI ORG MAPPING 2
                            $soldto = $datapp['SOLD_TO'];   // GANTI BERDASARKAN MAPPING CUSTOMER
                            $soldto = $this->fungsi->sapcode($soldto);
                            $plant = $datapp['PLANT_ASAL'];             // GANTI ORG MAPPING 2
                            $plant1 = $datapp['PLANT_ASAL'];             // GANTI ORG MAPPING 2
                            $incotermPO1  = $incoterm1;
                            $incotermPO12 = $incoterm2;
                        } else if ($ul == 2) {
                            $sales_org = $row['COM_OPCO']; // GANTI ORG MAPPING 2
                            $plant = $row['PLANT_OPCO'];             // GANTI ORG MAPPING 2
                            $soldto = $rowsoldto['SOLD_TO_OPCO'];   // GANTI BERDASARKAN MAPPING CUSTOMER
                            $soldto = $this->fungsi->sapcode($soldto);
                            $sales_org2 = $row['COM_OPCO'];
                            $plant2 = $row['PLANT_OPCO'];
                            
                            // $incoterm1 = ISSET($row['INCOTERM_OPCO_1']) ? $row['INCOTERM_OPCO_1'] : $datapp['INCOTERM'];
                            // $incoterm2 = ISSET($row['INCOTERM_OPCO_1']) ? $row['INCOTERM_OPCO_1'] : $datapp['NAMA_INCOTERM'];
                            
                            // $incotermPO1  = $incoterm1;
                            // $incotermPO12 = $incoterm2;
                            
                            //penambahan rute
                            if($incoterm1 == 'FOT'){
                               $route = 'ZR0003'; 
                               
                            }else if($incoterm1 == 'FRC'){
                               $route = 'ZR0002';  
                               
                            }
                            
                        } else if ($ul == 3 && $ics == "Y") {
                            $sales_org = $row['COM_MD_2'];
                            $plant = $row['PLANT_MD_2'];
                            $soldto = $rowsoldto['SOLD_TO_MD_2'];
                            $soldto = $this->fungsi->sapcode($soldto);
                            $sales_org3 = $row['COM_MD_2'];
                            $plant3 = $row['PLANT_MD_2'];
                            $incoterm1 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $datapp['INCOTERM'];
                            $incoterm2 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $datapp['NAMA_INCOTERM'];
                            
                            $incotermPO2 = $incoterm1;
                            $incotermPO22 = $incoterm2;
                            
                            $route     = ISSET($row['ROUTE_SOURCE']) ? $row['ROUTE_SOURCE'] : $datapp['NAMA_INCOTERM'];
                            //penambahan rute dan TOP
                            if($incoterm1 == 'FOT'){
                               $route = 'ZR0003'; 
                               
                            }else if($incoterm1 == 'FRC'){
                               $route = 'ZR0002';  
                               
                            }
                            //echo '<br> hasil rute :'.$route;
                            
                            $top       = ISSET($row['TOP_SOURCE']) ? $row['TOP_SOURCE'] : $datapp['TERM_PAYMENT'];
                            $nama_top  = ISSET($row['DESC_TOP_SOURCE']) ? $row['DESC_TOP_SOURCE'] : $datapp['NAMA_TOP'];
                            //penambahan rute top
                            
                        } else if ($ul == 4 && $ics == "Y") {
                            $sales_org = $row['COM_OPCO_2'];
                            $plant = $row['PLANT_OPCO_2'];
                            $soldto = $rowsoldto['SOLD_TO_OPCO_2'];
                            $soldto = $this->fungsi->sapcode($soldto);
                            $sales_org4 = $row['COM_OPCO_2'];
                            $plant4 = $row['PLANT_OPCO_2'];
                            $incoterm1 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $datapp['INCOTERM'];
                            $incoterm2 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $datapp['NAMA_INCOTERM'];
                            
                            //penambahan rute dan TOP
                            $route     = ISSET($row['ROUTE_SOURCE']) ? $row['ROUTE_SOURCE'] : $datapp['NAMA_INCOTERM'];
                            if($incoterm1 == 'FOT'){
                               $route = 'ZR0003'; 
                               
                            }else if($incoterm1 == 'FRC'){
                               $route = 'ZR0002';  
                               
                            }
                            
                            $top       = ISSET($row['TOP_SOURCE']) ? $row['TOP_SOURCE'] : $datapp['TERM_PAYMENT'];
                            $nama_top  = ISSET($row['DESC_TOP_SOURCE']) ? $row['DESC_TOP_SOURCE'] : $datapp['NAMA_TOP'];
                            $topPO2    = $top;
                            //penambahan rute top
                            
                            $incotermPO2 = $incoterm1;
                            $incotermPO22 = $incoterm2;
                        }
                        $pesan_telegram = "";
                        //data header so
                        $user_name_in = $_SESSION['user_name'];
                        $idh = $datapp['ID'];
                        $ship_condition = $datapp['SHIPPING_CONDITION'];
                        $iddtl = $datapp['IDDTL'];
                        $so_type = $datapp['SO_TYPE'];
                        $nama_so_type = $datapp['NAMA_SO_TYPE'];
                        $channelnew = 10;
                        if ($so_type == 'ZEX') {
                            $distr_chan = "30";
                        } elseif ($so_type == 'ZPR') {
                            if ($sales_org == '2000') {
                                $distr_chan = "10";
                            } else if ($sales_org == '3000' || $sales_org == '5000' || $sales_org == '7000' || $sales_org == '7900' || $sales_org == '4000') { // tambahan pak yusuf untuk bisa memilih dist channel proyek MD
                                $distr_chan = 10;
                            } else if ($sales_org == '6000') {
                                $distr_chan = "10";
                            } else {
                                $distr_chan = "50";
                            }
                        } else {
                            $distr_chan = "10";
                        }
                        $division = "00";
                        $dlv_block = "";
                        // $distr_chancondi = trim($this->fungsi->findOneByOne($this->conn, "TB_USER_BOOKING", "DISTRIBUTOR_ID", $soldto, "CHANNEL"));
                        // if ($distr_chancondi != '') {
                        //     $distr_chan = $distr_chancondi;
                        // }
                        // if ($channelnew != '') {
                        //     $distr_chan = $channelnew;
                        // }
    
                        $distr_chanPO1 = $distr_chan;
                        if ($ul == 3 && $ics == "Y") {
                            $distr_chan = ISSET($row['DIST_CHANNEL_SOURCE']) ? $row['DIST_CHANNEL_SOURCE'] : $distr_chan;
                        } else if ($ul == 4 && $ics == "Y") {
                            $distr_chan = ISSET($row['DIST_CHANNEL_SOURCE']) ? $row['DIST_CHANNEL_SOURCE'] : $distr_chan;
                            $distr_chanPO2 = $distr_chan;
                        }
    
                        $no_pp = $datapp['NO_PP'];
                        $tgl_pp = date('Ymd', strtotime($datapp['TGL_PP']));
                        list($day, $month, $year) = split("-", $tgl_pp);
                        $tgl_pp = $year . $month . $day;
                        $reason = $datapp['KD_REASON'];
                        $nama_reason = $datapp['NM_REASON'];
                        $nama_plant = trim($datapp['NAMA_PLANT']);
                        //pengkondisian jika nama kapal - maka null di so
                        if($datapp['NAMA_KAPAL']== '-')
                        {
                            $nm_kapal='';
                        }else{
                            $nm_kapal = $datapp['NAMA_KAPAL'];
                        }
                        //$route = $_POST['route'];
                        $pricelist = $datapp['PRICELIST'];
                        $nama_pricelist = $datapp['NAMA_PRICELIST'];
                        $ketbayar = $datapp['CARA_BAYAR'];
                        $lcnum = $datapp['NO_KONTRAK_LC'];
                        $lcnum = $this->fungsi->sapcode($lcnum);
                        $ship_cond = $this->fungsi->findOneByOne($this->conn, "TB_ROUTE", "ROUTE", $route, "SHIP_COND");
                        $route_desc = $this->fungsi->findOneByOne($this->conn, "TB_ROUTE", "ROUTE", $route, "ROUTE_DESC");
                        $ket = "";
                        // $com_royalty = '-';

                        // print_r("pricelist harga : ".$pricelist);
                        // $sampai1 = 1;
                        $sampai1 = count($datapp['TRANS_DTL']);
                        for ($j = 0; $j <= $sampai1; $j++) {
                            $idke = "idke" . $j;
                            $pesan_telegram .= "PP $no_pp";
    
                            // if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                // $kontrakh = $datapp['NO_KONTRAK' . $j];
                                $kontrakh = $datapp['TRANS_DTL'][$j]['NO_KONTRAK']; //field no kontrak TRANS_DTL
                            // }
                        }
    
                        // jalankan bapi create so sap	
                        $sap = new SAPConnection();
                        $sap->Connect("../include/sapclasses/logon_data.conf");
                        if ($sap->GetStatus() == SAPRFC_OK)
                            $sap->Open();
                        if ($sap->GetStatus() != SAPRFC_OK) {
                            $sap->PrintStatus();
                            exit;
                        }
    
    
                        if ($sales_org == 3000 || $sales_org == 4000 || $sales_org == 5000 || $sales_org == 7000 || $sales_org == 7900) {
                            $COM = 'MD';
                        } 
                        else if($sales_org == 1000) {
                            $COM = 'SMBR';
                        }
                        else {
                            $COM = 'SBI';
                        }
    
    
                        unset($ex_so);
                        $ex_so = true;
                        //cegatan credit limit so khusus tlcc
                        //                if($sales_org=='6000'){
                        //                    require_once 'loadcreditlimit.php';
                        //                }
                        // PROSES FOR 1 DAN 2 MENYIMPAN SO
                     //   echo "Loncat gak ".$loncat ;
                        if ($ul <= $perulanganSO && $COM == 'MD') {
                            
                            if ($loncat == "N") {
                                $gass = "Y";
                            } else if ($loncat == "Y") { 
                                if ($ul == '3') {
                                    $gass = "N"; 
                                } else {
                                    $gass = "Y";
                                }
                            }
    
                            if ($gass == "Y") { // loncat tidak membuat SO MD 2 karena tidak di mammping plant
                                if ($ex_so == true) {
                                    // SOCC v2
                                    $looping_end=1;
                                    $movetestrun='Z';
                                    for ($looping = 0; $looping <= $looping_end; $looping++) {
                                    if($movetestrun!='X'){
                                    ////////////
                                    // $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
                                    $fce = $sap->NewFunction("ZBAPI_SALESORDER_CREATEFROMDAT");
                                    if ($fce == false) {
                                        $sap->PrintStatus();
                                        exit;
                                    }
    
                                    //detail entri item'
                                    //$zak=$this->fungsi->qtyso($totalzak*1000);
                                    $sampai = count($datapp['TRANS_DTL']);

                                    $ada = 0;
                                    $shipto_awal = '';
                                    for ($j = 0; $j <= $sampai - 1; $j++) {
                                        $idke = "idke" . $j;
                                        $shipto_awal = $datapp['TRANS_DTL'][0]["SHIP_TO"];
//                                        if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                            $id_dtl = $datapp['TRANS_DTL'][$j]['ID'];
                                            $item_num1 = $datapp['TRANS_DTL'][$j]['ITEM_NUMBER'];
                                            $item_num = $this->fungsi->linenum($item_num1);
    
                                            // $material = $datapp['KODE_PRODUK' . $j];
                                            $material = $datapp['TRANS_DTL'][$j]["KODE_PRODUK"];
                                            if ($ul == 1) {
                                                // $shipto = $datapp['SHIP_TO' . $j];     // GANTI BERDASARKAN MAPPING CUSTOMER 
                                                // $material1 = $datapp['KODE_PRODUK' . $j];
                                                $shipto = $datapp['TRANS_DTL'][$j]["SHIP_TO"];
                                                $material1 = $datapp['TRANS_DTL'][$j]["KODE_PRODUK"];
                                            } else if ($ul == 2 || $ul == 3 || $ul == 4) {
                                                // $shiptotocek = $datapp['SHIP_TO' . $j];
                                                $shiptotocek = $datapp['TRANS_DTL'][$j]["SHIP_TO"];
                                                $strshipto = "SELECT * 
                                                FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                                                WHERE SOLD_TO_MD = '{$soldtocek}' 
                                                AND SHIP_TO_MD = '{$shiptotocek}'  
                                                AND DEL = 0 
                                                AND ORG_OPCO = '{$row['COM_OPCO']}' 
                                                AND PLANT_MD ='{$plantcek}'";
                                                $queryshipto = oci_parse($this->conn, $strshipto);
                                                oci_execute($queryshipto, 0);
                                                $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);

                                                if(count($rowshipto) <= 1){
                                                    $strshipto1 = "SELECT * 
                                                    FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                                                    WHERE SOLD_TO_MD = '{$soldtocek}' 
                                                    AND SHIP_TO_MD = '{$shiptotocek}'  
                                                    AND DEL = 0 AND ORG_OPCO = '{$row['COM_OPCO']}' AND PLANT_MD='ALL' ";
                                                    $queryshipto1 = oci_parse($this->conn, $strshipto1);
                                                    oci_execute($queryshipto1, 0);
                                                    $rowshipto = oci_fetch_array($queryshipto1, OCI_ASSOC); 
                                                }
                                                
                                                if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                                                    $strshipto = "SELECT * 
                                                     FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                                                    WHERE SOLD_TO_MD = '{$soldtocek}' 
                                                    AND SHIP_TO_MD = '{$shiptotocek}'  
                                                    AND DEL = 0 AND PLANT_MD ='ALL' AND ORG_OPCO='ALL' ";
                                                    $queryshipto = oci_parse($this->conn, $strshipto);
                                                    oci_execute($queryshipto, 0);
                                                    $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                                                    
                                                    // if(count($rowshipto) <= 1){
                                                    //     $strshipto1 = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}'  AND DEL = 0";
                                                    //     $queryshipto1 = oci_parse($this->conn, $strshipto1);
                                                    //     oci_execute($queryshipto1, 0);
                                                    //     $rowshipto = oci_fetch_array($queryshipto1, OCI_ASSOC);  
                                                    // }
                                                }

                                                if ($ul == 2) {
                                                    $shipto = $rowshipto['SHIP_TO_OPCO'];
                                                    $soldto = $rowshipto['SOLD_TO_OPCO'];
                                                } else if ($ul == 3) {
                                                    $shipto = $rowshipto['SHIP_TO_MD_2'];
                                                    $soldto = $rowshipto['SOLD_TO_MD_2'];
                                                } else if ($ul == 4) {
                                                    $shipto = $rowshipto['SHIP_TO_OPCO_2'];
                                                    $soldto = $rowshipto['SOLD_TO_OPCO_2'];
    //                                                echo "empat ".$shipto;echo "<br>";
    //                                                echo "empat ".$soldto;
                                                }

    
                                                $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' AND KD_MATERIAL_MD = '{$material1}' AND DEL = 0";
                                                $querymaterial = oci_parse($this->conn, $strmaterial);
                                                oci_execute($querymaterial, 0);
                                                $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);
    
    
                                                if ($ul == 2) {
                                                    $material = $rowmaterial['KD_MATERIAL_OPCO'];
                                                } else if ($ul == 3) {
                                                    $material = $rowmaterial['KD_MATERIAL_MD_2'];
                                                } else if ($ul == 4) {
                                                    $material = $rowmaterial['KD_MATERIAL_OPCO_2'];
                                                }
                                            }
    
                                            $shipto = $this->fungsi->sapcode($shipto);
                                            // $distrik = $datapp['KODE_TUJUAN' . $j];
                                            // $qty = $datapp['QTY_PP' . $j];
                                            $distrik = $datapp['TRANS_DTL'][$j]["KODE_TUJUAN"];
                                            $qty = $datapp['TRANS_DTL'][$j]["QTY_PP"];
                                            // $qtyx = $datapp['QTY_APPROVE' . $j];
                                            $qtyx = $datapp['TRANS_DTL'][$j]["QTY_APPROVE"];                                            
                                            // $tgl_kirim = $datapp['TGL_KIRIM_PP' . $j];
                                            $tgl_kirim = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TGL_KIRIM_PP']));
    //                                        if($sales_org=="7900"){ 
    //                                            $kontrak = $datapp['NO_KONTRAK' . $j];
    //                                        }else{
    //                                            $kontrak = "";
    //                                        }
                                            // $kontrak = $datapp['NO_KONTRAK' . $j];
                                            $kontrak = $datapp['TRANS_DTL'][$j]["NO_KONTRAK"]; 
    
                                            $posnr = $this->fungsi->linenum($datapp['TRANS_DTL'][$j]["ITEM_NUMBER"]*10);
    
                                            //////////////////////////////////////////// proyek SOCC v.2
                                            // $plant_socc = $datapp['PLANT_ASAL'];
                                            // $kode_distrik_socc = $datapp['TRANS_DTL'][$urutan]["KODE_TUJUAN"];
                                            // $material_for_socc = substr($datapp['TRANS_DTL'][$urutan]["KODE_PRODUK"],0,7);
                                            // $tgl_terima_leadtime = $this->fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
                                            // $tgl_terima_leadtime=$tgl_terima_leadtime;
                                            // list($day, $month, $year) = split("-", $tgl_terima_leadtime);
                                            // $tgl_terima_leadtime = $year . $month . $day;

                                            //SOCC FDATE RDD KTERBALIK
                                            // $tgl_terima_leadtime = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TGL_LEADTIME']));
                                            // if(date('H:i')>='15:00'){
                                            //     $tgl_terima_leadtime = 	date('d-m-Y', strtotime($tgl_terima_leadtime. ' + 1 days'));
                                            // }else{
                                            //     $tgl_terima_leadtime = $tgl_terima_leadtime;	
                                            // }
                                
                                            // list($day, $month, $year) = split("-", $tgl_terima_leadtime);
                                            // $tgl_terima_leadtime = $year . $month . $day;
                                
                                            // $tgl_fdate_socc = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TANGGAL_PRELEADTIME']));
                                            // // $tgl_fdate_socc=Date('d-m-Y');
                                            // list($day, $month, $year) = split("-", $tgl_fdate_socc);
                                            // $tgl_fdate_socc = $year . $month . $day;
                                            /////////////////////////////////////////////////
                                            if ($datapp['TRANS_DTL'][$j]['TGL_LEADTIME']!="" || $datapp['TRANS_DTL'][$j]['TGL_LEADTIME']!=NULL) {
                                                $tgl_terima_leadtime = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TGL_LEADTIME']));
                                                //this code change by ady jul  (RDD + 30 day => PO)
                                                $tanggal_awal = $datapp['TRANS_DTL'][$j]['TGL_LEADTIME'];
                                                $tgl_deliv_temp =  date('d-m-Y', strtotime($tanggal_awal . ' +30 days'));
                                                list($dayDeliv, $monthDeliv, $yearDeliv) = split("-",$tgl_deliv_temp);
                                                $deliv_date = $yearDeliv . $monthDeliv . $dayDeliv;
                                                
                                                // if(date('H:i')>='15:00'){
                                                //     $tgl_terima_leadtime =  date('Ymd', strtotime($tgl_terima_leadtime. ' + 1 days'));
                                                // }else{
                                                    $rdd_from_SO = $tgl_terima_leadtime;    
                                                    $tgl_terima_leadtime = $tgl_terima_leadtime;    
                                                // }
                                            } else $tgl_terima_leadtime = date("Ymd");
                                
                                            if ($datapp['TRANS_DTL'][$j]['TANGGAL_PRELEADTIME']!="" || $datapp['TRANS_DTL'][$j]['TANGGAL_PRELEADTIME']!=null) {
                                                unset($tgl_fdate_socc_r);
                                                $tgl_fdate_socc = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TANGGAL_PRELEADTIME']));
                                                $tgl_fdate_socc_r = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]['TANGGAL_PRELEADTIME']));
                                            } else {
                                                //---------------------------------------------------------------------
                                                    $mysql_rddtypeload="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='RDD_TYPELOAD' and config='$inctm' and delete_mark='0'";

                                                    $mysql_set_typeload=oci_parse($this->conn,$mysql_rddtypeload);
                                                    oci_execute($mysql_set_typeload);
                                                    $row_rddtypeloadr=oci_fetch_assoc($mysql_set_typeload);
                                                    $leadtimerddtypeload=$row_rddtypeloadr[CONFIG];

                                                    if($leadtimerddtypeload!=null || $leadtimerddtypeload!=''){
                                                        $mysql_leadtime="SELECT
                                                                STANDART_AREA
                                                                FROM
                                                                    (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant1' and kota='$distrik' and kd_material='121-301' and delete_mark='0' ORDER BY id desc)
                                                                WHERE
                                                                    rownum BETWEEN 0 AND 1";
                                                                
                                                                $mysql_set_leadtime=oci_parse($this->conn,$mysql_leadtime);
                                                                oci_execute($mysql_set_leadtime);
                                                                $row_leadtimer=oci_fetch_assoc($mysql_set_leadtime);
                                                                $leadtimesonya=$row_leadtimer[STANDART_AREA];
                                                                // print_r($leadtimeso);
                                                                
                                                                if ($leadtimesonya != "" or $leadtimesonya != null){
                                                                    $leadtimeso=$leadtimesonya;
                                                                    $tgl_fdate_socc = date('Ymd',strtotime($tgl_terima_leadtime. ' -'.$leadtimeso.' day'));

                                                                }else{
                                                                    $leadtimeso=0;
                                                                    $tgl_fdate_socc = $tgl_terima_leadtime;
                                                                }
                                                    }else{
                                                        $tgl_fdate_socc = $tgl_terima_leadtime;
                                                    }

                                            //---------------------------------------------------------------------
                                            }
                                            /////////////////////////////////// penambahan kondisi tgl fdate dan rdd sama untuk FOT 
                                            if($inctm=='FOT'){
                                                $tgl_fdate_socc = $tgl_terima_leadtime;
                                            }
                                            ///////////////////////////////////////////

                                            ///////////////////////////////////////////

    
                                            if ($qtyx != $qty) {
                                                $ada = $ada + 1;
                                            }
                                            $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                                            $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $material;
                                            if ($so_type == 'ZFC') {
                                                 // if($sales_org=='7900' || $sales_org=='7000'){
                                                //     $fce->ORDER_ITEMS_IN->row["TAX_CLASS1"] = "0";    // Tambahan MD 
                                                // }  
                                                
                                                //this code change by adyjuliyanto
                                                if($sales_org=='7900'){
                                                    $fce->ORDER_ITEMS_IN->row["TAX_CLASS1"] = "0";    // Tambahan MD 
                                                }    
                                                //$fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'ZKNN';//$item_cat;///salah revisi dibawah ini   
                                                $fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'X'; //$item_cat;  
                                                if ($sales_org == '6000') {
                                                    $ITEM_CATEGvali = 'ZKNN';
                                                } else if ($sales_org == '4000' || $sales_org == '3000' || $sales_org == '7000' || $sales_org == '5000' || $sales_org == '7900') {
                                                    $ITEM_CATEGvali = 'ZKLN';
                                                    //sales FOC distrik
                                                } else {
                                                    $ITEM_CATEGvali = 'ZKNN';
                                                }
                                                $fce->ORDER_ITEMS_IN->row["ITEM_CATEG"] = $ITEM_CATEGvali; //$item_cat;
                                            }
    
    
    
                                            $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik; // penambahan distrik
                                            $fce->ORDER_ITEMS_IN->row["SALES_OFF"] = '10' . substr($distrik, 0, 2);
                                            $fce->ORDER_ITEMS_IN->row["PLANT"] = $plant;
                                            $fce->ORDER_ITEMS_IN->row["ROUTE"] = $route;
                                            //$fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                                           if ($kontrak != '' && $sales_org == "7900") {
                                               $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                                               $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = '10';
                                               $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';
                                           }
                                            // if ($kontrak != '') {
                                            //     $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                                            //     $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = $posnr;
                                            //     $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';
                                            // }
                                            $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);
    
                                            //detail entri schedule n qty'
                                            $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                                            $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $qty;
                                            //////////////////////////////////////////////////////////// proyek SOCC V.2
                                            // $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_kirim;
                                            $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_fdate_socc;
                                            // $fce->ORDER_SCHEDULES_IN->row["TP_DATE"] = 'D';                                        
                                            // $fce->ORDER_SCHEDULES_IN->row["DLV_DATE"] = $tgl_terima_leadtime;  
                                            ///////////////////////////////////////////////////////////
                                            $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);
    
                                            $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                                            $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
                                            $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
                                            $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
                                            $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);
    
                                            //detail entri distributor dan agen
                                            if ($j == 0)
                                                $item_num = '000000';
                                            else
                                                $item_num = $item_num;
                                            $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                                            $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
                                            $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
                                            $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
//                                        }
                                    }
    
                                    if ($sales_org == '3000') {
                                        $fce1 = $sap->NewFunction("Z_ZCSD_SHIPTO");
                                        if ($fce1 == false) {
                                            $sap->PrintStatus();
                                            exit;
                                        }
    
                                        //header entri
    
                                        $fce1->ZNMORG = 3000;
                                        $fce1->ZKUNNR = $shipto;
                                        $fce1->Call();
                                        if ($fce1->GetStatus() == SAPRFC_OK) {
                                            $fce1->RETURN_DATA->Reset();
                                            while ($fce1->RETURN_DATA->Next()) {
                                                $kode = $fce1->RETURN_DATA->row["VKGRP"];
                                            }
                                        }
                                        $fce->ORDER_HEADER_IN["SALES_GRP"] = $kode;
                                    }
                                    // if($ul==1 || $ul==2 ){
                                    //	$fce->TESTRUN = "X"; 
                                    // }
                                    //penambahan sales office
                                    $dt_shipto = "SELECT BZIRK, VKBUR, VKGRP FROM RFC_Z_ZCSD_SHIPTO WHERE KUNN2 = '{$shipto}' AND ROWNUM =1";
                                    //                            echo $dt_shipto;
                                    $dt_shipto_q = oci_parse($this->conn, $dt_shipto);
                                    oci_execute($dt_shipto_q, 0);
                                    $row_dt_shipto = oci_fetch_array($dt_shipto_q, OCI_ASSOC);
    
                                    $fce->ORDER_HEADER_IN["SALES_GRP"] = $row_dt_shipto["VKGRP"];
                                    $fce->ORDER_HEADER_IN["SALES_DIST"] = $row_dt_shipto["BZIRK"];
                                    $fce->ORDER_HEADER_IN["SALES_OFF"] = $row_dt_shipto["VKBUR"];
                                    // end penambahan sales office
    
    
                                    if ($so_type == 'ZFC') {
                                        $fce->ORDER_HEADER_IN["DLV_BLOCK"] = "Z1";
                                    } else {
                                        $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $dlv_block; //"Z1";
                                    }
                                    $fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type; //"ZOR";
                                    $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"3000";
                                    $fce->ORDER_HEADER_IN["DISTR_CHAN"] = $distr_chan; //"10";
                                    $fce->ORDER_HEADER_IN["DIVISION"] = $division; //"00";
                                    $fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp; //"Z1";
                                    $fce->ORDER_HEADER_IN["PURCH_DATE"] = $tgl_pp; //"Z1";
                                    $fce->ORDER_HEADER_IN["PMNTTRMS"] = $top; //"Z1";
                                    $fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1; //"Z1";
                                    $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm2;
                                    $fce->ORDER_HEADER_IN["SHIP_COND"] = $ship_cond;
                                    $fce->ORDER_HEADER_IN["NAME"] = $nm_kapal;
                                    $fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist;
                                    $fce->ORDER_HEADER_IN["ORD_REASON"] = $reason;
                                    // SOCC V2
                                    $fce->ORDER_HEADER_IN["TP_DATE"] = 'D';
                                    $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tgl_terima_leadtime;  
                                    /////

                                    if ($sales_org == "7900" || $sales_org == "7000") {
                                    $strtax = "SELECT * FROM ZMD_CUSTOMER_NOTAX WHERE KD_SOLD_TO = '{$soldto}'  AND DEL = 0";
                                    $querytax = oci_parse($this->conn, $strtax);
                                    oci_execute($querytax, 0);
                                    $rowtax = oci_fetch_array($querytax, OCI_ASSOC);
                                    if (ISSET($rowtax["KD_SOLD_TO"])) {
                                        $fce->ORDER_HEADER_IN["ALTTAX_CLS"] = "0";
                                    } else {
                                        if ($so_type == 'ZFC') {
                                        $fce->ORDER_HEADER_IN["ALTTAX_CLS"] = "0";
                                        }else{
                                        
                                        $sldto_fr = substr($soldto, 0, 6);
                                        $sldto_bw = substr($soldto, 6, 4);
                                        
                                        if($sldto_fr != '000000' || $sldto_bw < 3000){
                                        $fce->ORDER_HEADER_IN["ALTTAX_CLS"] = "1"; 
                                        }
                                        }
                                    }
    
    //                                    $fce->ORDER_HEADER_IN["ALTTAX_CLS"] = "1"; 
                                }else{
                                    // select sold to di mapping customer 
                                    $selectDataDist = "SELECT SOLD_TO_OPCO as SOLDTODIST FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '$soldto'AND DEL ='0' AND ROWNUM <= 1 ";
                                    $qryDist = oci_parse($this->conn, $selectDataDist);
                                    oci_execute($qryDist, 0);
                                    $getDataDist = oci_fetch_array($qryDist, OCI_ASSOC);

                                    $soldtoDist = $getDataDist['SOLDTODIST'];
                            
                                    ///
                                    $selectData = "SELECT TAX_ID FROM TAX_OPCO_MD WHERE SOLDTO = '$soldtoDist' AND DELETE_MARK = '0' ";
                                    $qry = oci_parse($this->conn, $selectData);
                                    oci_execute($qry, 0);
                                    $getDataTax = oci_fetch_array($qry, OCI_ASSOC);
                                    
                                    if (count($getDataTax) >= 1) {
                                        # code...
                                        $tax_id = $getDataTax['TAX_ID'];
                                        $fce->ORDER_HEADER_IN["ALTTAX_CLS"] = "$tax_id"; 
                                    }
                                }
    
                                    if ($lcnum != '') {
                                        $fce->ORDER_HEADER_IN["PMTGAR_PRO"] = 'Z00001';
                                        $fce->ORDER_HEADER_IN["DOC_NUM_FI"] = $lcnum;
                                    }
                                   if ($kontrak != '' && $sales_org=="7900") {
                                       $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
                                       $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrak;
                                   }
                                    // if ($kontrakh != '') {
                                    //     $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
                                    //     $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrakh;
                                    // }
    
                                    $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
                                    $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'AG';
                                    $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $soldto;
                                    $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
    
                                    $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
                                    $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
                                    $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
                                    $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
                                    // SOCC V.2
                                    if($looping=='0' or $looping==0){
                                        $fce->TESTRUN = "X";
                                    }
                                    //
                                    $fce->Call();
                                    if ($fce->GetStatus() == SAPRFC_OK) {
                                        $nomorso = $fce->SALESDOCUMENT;
                                        $fce->RETURN->Reset();
                                        while ($fce->RETURN->Next()) {
                                            $tipe = $fce->RETURN->row["TYPE"];
                                            $msg = $fce->RETURN->row["MESSAGE"];
                                            if ($tipe != 'S') {
                                                $show_ket .= $msg;
                                                $show_ket .= '<br>';
                                            // if($tipe=='W' && $id=='V1' && $number=='555'){
                        
                                                //SOCC v2
                                                $show_ket .= 'SO long text (cek kembali mappingan/data PP)';
                                                $show_ket .= '<br>';
                                                $movetestrun='X';
                                                $nomorso = '';
                                                ///
                                            // }
                                            }
                                            //tambah pengujian rfc test run SOCC
                                        }
                                    }
                                    //SOCC v2
                                    }
                                    // $looping +=1;
                                    }
                                    ////////////////////////////////////////////////////////////// batas socc testrun
                                    if($nomorso!='' or $nomorso!=null){
                                        //Commit Transaction
                                        $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                                        $fce->Call();
                                        //Update SO
                                        if ($nomorso != '' && $j > 0) {
                                            sleep(5); //seconds to wait..  
                                            $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                                            $fce->SALESDOCUMENT = $nomorso; //"ZOR";
                                            $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';
                                            $fce->Call();
                                            //Commit Transaction
                                            $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                                            $fce->Call();
                                        }
    
                                        if ($sales_org == '6000') {
                                            
                                            sleep(5); //seconds to wait..
                                            //Teks
                                            unset($tgl_kirimapp);
                                            for ($j = 0; $j <= $sampai - 1; $j++) {
                                                $idke = "idke" . $j;
                                                // if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                                    $item_num1 = trim($datapp['TRANS_DTL'][$j]['ITEM_NUMBER']);
                                                    $item_num = $this->fungsi->linenum($item_num1);
                                                    $com_nopolisiv = trim($datapp['TRANS_DTL'][$j]['NO_POLISI']);
                                                    $com_typetruckv = $datapp['TRANS_DTL'][$j]['TYPE_TRUCK'];
                                                    $com_catatanv = $datapp['TRANS_DTL'][$j]['CATATAN'];
                                                    $com_kodekantong = $datapp['TRANS_DTL'][$j]['KODE_BAG'];
                                                    $tgl_kirimappT = $datapp['TRANS_DTL'][$j]['TGL_KIRIM_PP'];
                                                    $com_viewdrivern = $datapp['TRANS_DTL'][$j]['DRIVERN'];
                                                    $com_viewsimv = $datapp['TRANS_DTL'][$j]['SIMDRIVER'];
                                                    list($day, $month, $year) = split("-", $tgl_kirimappT);
                                                    $tgl_kirimapp .= $year . $month . $day . "!";
    
                                                    $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                                    $fce->I_VBELN = $nomorso; //'000010';
                                                    $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                                                    $fce->I_TEXT1 = $com_nopolisiv;
                                                    $fce->I_TEXT2 = $com_typetruckv;
                                                    $fce->I_TEXT3 = $com_catatanv;
                                                    $fce->I_TEXT4 = $com_kodekantong;
                                                    $fce->I_TEXT6 = $com_viewdrivern;
                                                    $fce->I_TEXT7 = $com_viewsimv;
                                                    $fce->Call();
                                                    //                            echo "<pre>";
                                                    //                            //print_r($fce);
                                                    //                            echo "</pre>";
//                                                }
                                            }
    
                                            if ($sales_org == '6000' && $nomorso != '') {
                                                $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorso&tgleq=$tgl_kirimapp";
                                            }
                                        }
    
                                        //@liyantanto penambahan no ref pp
                                        if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {

                                            sleep(5); //s
                                            for ($j = 0; $j <= $sampai - 1; $j++) {
                                                $idke = "idke" . $j;
                                                // if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                                    $item_num1 = $datapp['TRANS_DTL'][$j]['ITEM_NUMBER'];
                                                    $item_num = $this->fungsi->linenum($item_num1);
                                                    $com_nopppref = trim($datapp['TRANS_DTL'][$j]['NO_PPREF']);
    
                                                    if ($com_nopppref != '' && $nomorso != '') {
                                                        $pputam = substr($com_nopppref, 0, 10);
                                                        $itempputam = @(substr($com_nopppref, 10, 6) / 10);
                                                        $sql_ppref = "
                                                select ID,NO_SO,ITEM_NUMBER,NO_PPREF from OR_TRANS_DTL where DELETE_MARK=0 
                                                and NO_PP='$pputam' 
                                                and ITEM_NUMBER='$itempputam'
                                                ";
                                                        $query_ppref = oci_parse($this->conn, $sql_ppref);
                                                        oci_execute($query_ppref, 0);
                                                        while ($row_ref = oci_fetch_array($query_ppref)) {
                                                            $idPPREF = $row_ref[ID];
                                                            $no_soref = $row_ref[NO_SO];
                                                            $item_soref = $row_ref[ITEM_NUMBER];
                                                        }
                                                        $com_noppputam = $no_pp . sprintf("%06d", $item_num * 10);
                                                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                                        $fce->I_VBELN = $no_soref; //'000010';
                                                        $fce->I_POSNR = sprintf("%06d", $item_soref * 10); //'000010';
                                                        $fce->I_TEXT5 = $nomorso . sprintf("%06d", $item_num * 10); //pp ref
                                                        $fce->Call();
    
                                                        //update so pp ref
                                                        $field_namesppru = array('NO_PPREF');
                                                        $field_datapput = array("$com_noppputam");
                                                        $tablenamepput = "OR_TRANS_DTL";
                                                        $field_idpput = array('ID');
                                                        $value_idpput = array("$idPPREF");
                                                        $this->fungsi->update($this->conn, $field_namesppru, $field_datapput, $tablenamepput, $field_idpput, $value_idpput);
    
                                                        $nosoref = $no_soref . sprintf("%06d", $item_soref * 10);
                                                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                                        $fce->I_VBELN = $nomorso; //'000010';
                                                        $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                                                        $fce->I_TEXT5 = $nosoref; //pp ref
                                                        $fce->Call();
                                                        //                                echo "<pre>";
                                                        //                                //print_r($fce);
                                                        //                                echo "</pre>";
                                                    }
//                                                }
                                            }
                                        }

                                        $queryItem="SELECT  otd.KODE_TUJUAN, otd.PLANT, otd.ID,otd.NO_SO,otd.ITEM_NUMBER,otd.NO_PPREF,CASE WHEN min(mbp.IS_ROYALTY)='Y' THEN 'ROYALTY' ELSE '' END AS ROYALTY
                                        FROM OR_TRANS_HDR oth 
                                        LEFT JOIN OR_TRANS_DTL otd ON otd.NO_PP=oth.NO_PP
                                        LEFT JOIN ZSD_TARGET_HEADER_SCM zths ON zths.DISTRIK=otd.KODE_TUJUAN AND to_char(otd.TGL_KIRIM_PP,'MM-YYYY')=zths.PERIODE AND zths.MATERIAL=otd.KODE_PRODUK AND zths.FLAG_DEL='X'
                                        LEFT JOIN MAPPING_BRAND_PLANT mbp ON mbp.BRAND=zths.BRAND AND mbp.PLANT_MD='".$plant1."' AND mbp.FLAG_DEL='X'
                                        WHERE oth.no_pp='".$no_pp."'
                                        GROUP BY otd.ID,otd.NO_SO,otd.ITEM_NUMBER,otd.NO_PPREF,otd.KODE_TUJUAN,otd.PLANT";

                                        $query_ppref = oci_parse($this->conn, $queryItem);
                                        oci_execute($query_ppref);
                                        while ($row_ref = oci_fetch_array($query_ppref)) {
                                            $idPPREF = $row_ref[ID];
                                            $no_soref = $row_ref[NO_SO];
                                            $item_soref = $row_ref[ITEM_NUMBER];
                                            $flagroyalty = $row_ref[ROYALTY]; 
                                            $kodetujuanmsa= $row_ref[KODE_TUJUAN];
                                            $plantmsa = $row_ref[PLANT];
                                        }
                                        
                                        $mysqlmsa="SELECT 
                                        COUNT(*) AS COUNT_DATA
                                        FROM(
                                        SELECT
                                            kp.KOORDINATOR_AREA,
                                            mp.COM_OPCO,
                                            CASE
                                                WHEN kp.KOORDINATOR_AREA = mp.COM_OPCO THEN 'FALSE'
                                                ELSE NULL
                                            END AS MSA_STATUS
                                        FROM
                                            ZMD_KOORDINATOR_PENJUALAN kp
                                        JOIN ZMD_MAPPING_PLANT mp ON
                                            kp.KOORDINATOR_AREA = mp.COM_OPCO
                                        WHERE
                                            kp.DEL = '0'
                                            AND mp.DEL = '0'
                                            AND mp.PLANT_MD = '$plantmsa'
                                            AND mp.COM_OPCO IN (
                                            SELECT
                                                KOORDINATOR_AREA
                                            FROM
                                                ZMD_KOORDINATOR_PENJUALAN
                                            WHERE
                                                DISTRICT = '$kodetujuanmsa'
                                                AND DEL = '0' )) a";

                                                // print_r("------".$mysqlmsa);
                                        $mysql_msa=oci_parse($this->conn,$mysqlmsa);
                                        oci_execute($mysql_msa);
                                        $row_refmsa = oci_fetch_assoc($mysql_msa);
                                        $msa=$row_refmsa["COUNT_DATA"];
                                        // print_r("MSA ARRAY".$row_refmsa);
                                        // print_r("MSA berapa ? ".$msa);
                                        $com_msa = $msa == 0 ? 'MSA' : '';

                                        if(($flagroyalty == 'ROYALTY') && ($com_msa=='MSA')){
                                            $com_royalty = 'ROYALTY_MSA';
                                        }else if (($flagroyalty=='ROYALTY') && ($com_msa!='MSA')){
                                            $com_royalty ='ROYALTY';
                                        }else if (($flagroyalty!='ROYALTY') && ($com_msa=='MSA')){
                                            $com_royalty ='MSA';
                                        }else{
                                            $com_royalty='';
                                        }

                                        $com_royaltyopco=$com_royalty;

                                        // print_r("royalty tidak? ".$com_royalty," - ".$flagroyalty);
                                        
                                        $nosoref = $no_soref . sprintf("%06d", 1 * 10);
                                        $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                        $fce->I_VBELN = $nomorso; //'000010';
                                        $fce->I_POSNR = sprintf("%06d", 1 * 10); //'000010';
                                        $fce->I_TEXT10 = $com_royalty;
                                        $fce->Call();
                                    }
    
                                    // Setelah SO ter create
                                    if ($ul == 1) {
                                        $SO1 = $nomorso;
                                        $IDH1 = $idh;
                                    } else if ($ul == 2) {
                                        $SO2 = $nomorso;
                                        $IDH2 = $idh;
                                    } else if ($ul == 3) {
                                        $SO3 = $nomorso;
                                        $IDH3 = $idh;
                                    } else if ($ul == 4) {
                                        $SO4 = $nomorso;
                                        $IDH4 = $idh;
                                    }
                                    if ($next == "Y" && $ul == 1) {
                                        if (($nomorso != ""))
                                            $status = "APPROVE";
                                        else
                                            $status = "PROCESS";

                                        $isRoyalti = $com_royalty == 'ROYALTY_MSA' || $com_royalty == 'ROYALTY' ? 'X' : '';                                      
                                        $isMsa = $com_royalty == 'ROYALTY_MSA' || $com_royalty == 'MSA' ? 'X' : '';  
                                        //update data header
                                        // $field_names = array('PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'STATUS', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ROUTE', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'KD_REASON', 'NM_REASON', 'ORG', "SHIPPING_CONDITION" );
                                        // $field_data = array("$plant", "$nama_plant", "$top", "$status", "$nama_top", "$so_type", "$nama_so_type", "$incoterm1", "$incoterm2", "SYSDATE", "$user_name_in", "$route", "$pricelist", "$nama_pricelist", "$lcnum", "$reason", "$nama_reason", "$sales_org", "$ship_condition");
                                        $field_names = array('STATUS','LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'IS_ROYALTY', 'IS_MSA');
                                        $field_data = array("$status","SYSDATE", "SCEDULER", "$isRoyalti", "$isMsa");                                        
                                        $tablename = "OR_TRANS_HDR";
                                        $field_id = array('ID');
                                        $value_id = array("$idh");
                                        $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                                        // update data detail
                                        $sampai1 = 1;
                                        for ($k = 0; $k <= $sampai1; $k++) {
                                            $idke = "idke" . $k;
//                                            if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                                $id_dtl1 = $datapp['TRANS_DTL'][$k]['ID'];
                                                $qty1 = $datapp['TRANS_DTL'][$k]['QTY_PP'];
                                                $qtyx1 = $datapp['TRANS_DTL'][$k]['QTY_APPROVE'];
    //                                            if($sales_org=="7900"){ 
    //                                                $kontrak = $datapp['NO_KONTRAK' . $k];
    //                                            }else{
    //                                                $kontrak = "";
    //                                            }
                                                $kontrak = $datapp['TRANS_DTL'][$k]['NO_KONTRAK'];
    
                                                $posnr = $datapp['TRANS_DTL'][$k]["NO_KONTRAK_POSNR"];
                                                $kode_distrik = $datapp['TRANS_DTL'][$k]["KODE_TUJUAN"];
                                                $tgl_kirim1 = $datapp['TRANS_DTL'][$k]['TGL_KIRIM_PP'];
                                                $tgl_terima = $this->fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
                                                $item_numline1 = trim($datapp['TRANS_DTL'][$k]['ITEM_NUMBER']);
                                                $item_numline = $this->fungsi->linenum($item_numline1);
                                                $com_kodekantong = $datapp['TRANS_DTL'][$k]['KODE_BAG'];
    
                                                // if (($nomorso != "") and ( $qty1 == $qtyx1))
                                                if (($nomorso != ""))
                                                    $status1 = "APPROVE";
                                                else
                                                    $status1 = "PROCESS";
                                                /////////////////////////////////////////// proyek SOCC V.2
                                                $tgl_kirim1 = date('d-m-Y', strtotime($datapp['TRANS_DTL'][$k]['TGL_KIRIM_PP']));
                                                $tgl_terima = $this->fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
                                                //old
                                                //-----------------------------------------------------------------------//
                                                //new
                                                // $plant_socc = $datapp['PLANT_ASAL'];
                                                // $kode_distrik_socc = $datapp['TRANS_DTL'][$k]["KODE_TUJUAN"];
                                                // $material_for_socc = substr($datapp['TRANS_DTL'][$k]["KODE_PRODUK"],0,7);
                                                // $tgl_terima_leadtime = $this->fungsi->tgl_leadtime($plant_socc, $kode_distrik_socc, $material_for_socc);
                                                // $tgl_kirim1=date('Y-m-d', strtotime(str_replace('.', '/', $tgl_kirim1)));
                                                ///////////////////////////////////////////
                                                if (($nomorso != "")){
                                                    $status1 = "APPROVE";
                                                    $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'PLANT', 'NM_PLANT', 'KODE_BAG', 'IS_ROYALTY', 'IS_MSA');
                                                    $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status1", "SYSDATE", "scheduler", "SYSDATE", "scheduler", "$plant", "$nama_plant", "$com_kodekantong", "$isRoyalti", "$isMsa");
                                                }else{
                                                    $status1 = "PROCESS";
                                                    $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'PLANT', 'NM_PLANT', 'KODE_BAG', 'IS_ROYALTY', 'IS_MSA');
                                                    $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status1", "SYSDATE", "scheduler", "SYSDATE", "scheduler", "$plant", "$nama_plant", "$com_kodekantong", "$isRoyalti", "$isMsa");
                                                }

                    //pengujian jika approve tidak update ke process
                    $sql_sql_getdataapprove = "select STATUS_LINE from OR_TRANS_DTL 
                    where DELETE_MARK=0 
                    and ID='$id_dtl1'";
            
                    $mysql_getdataapprove=oci_parse($this->conn,$sql_sql_getdataapprove);
                    oci_execute($mysql_getdataapprove, 0);
                    $row_datagetapprove=oci_fetch_assoc($mysql_getdataapprove);
                    $statusline_pp=$row_datagetapprove[STATUS_LINE]; 
                    //////
                    if($statusline_pp!='APPROVE'){
                                                $tablename = "OR_TRANS_DTL";
                                                $field_id = array('ID');
                                                $value_id = array("$id_dtl1");
                                                $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }

                                                if ($nomorso != "") {
                                                    $field_names1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'APPROVE_DATE', 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                                                    $field_data1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', "$qty1", "instgl_$tgl_kirim1", 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', "'APPROVE'", 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', "SYSDATE", 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                                                    $tablenamefrom = "OR_TRANS_DTL";
                                                    $tablenameto = "OR_TRANS_APP";
                                                    $field_id1 = array('ID');
                                                    $value_id1 = array("$id_dtl1");
                                                    $this->fungsi->insertinto($this->conn, $field_names1, $field_data1, $tablenameto, $tablenamefrom, $field_id1, $value_id1);
                                                    //@liyantanto update qty approve
                                                    if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                                                        $sql_qtya = "
                                                            update OR_TRANS_DTL set QTY_APPROVE=(
                                                                    select sum(nvl(QTY_APPROVE,0)) as QTY from OR_TRANS_APP where NO_PP='$no_pp' and ITEM_NUMBER='$item_numline1'
                                                                    and DELETE_MARK=0 and STATUS_LINE='APPROVE'
                                                            ) where ID='$id_dtl1'
                                                        ";
                                                        $query_qtya = oci_parse($this->conn, $sql_qtya);
                                                        oci_execute($query_qtya, 0);
                                                    }
                                                }
//                                            }
                                        }
                                    }
    
                                    if ($ul == 1 || $ul == 3) {
                                        $numbering = " SO MD = ";
                                    } else {
                                        $numbering = " SO OPCO = ";
                                    }
                                    $show_ket .= $ul . '' . $numbering . ' Sales Order has been made with a number : ' . $nomorso . '<br />';
                                    $pesan_telegram .= " Approve dengan no SO $nomorso (Approve By scheduler)";
    
                                    // if ($sales_org == 4000) {
                                    //     botTelegram('-394888712', $pesan_telegram);
                                    // }
                                }
                            }//if loncat MD 2 tidak pakai
                            $jerror += 1;
                    } else if (($ul == 2 || $ul == 4) && $COM == "SMBR") {

                        // penambahan up prod 30 Maret 2023
                        $shiptotocek = $datapp['TRANS_DTL'][0]["SHIP_TO"];

                        $material = $datapp['TRANS_DTL'][0]['KODE_PRODUK'];

                        $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY 
                        WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' 
                        AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                        $querymaterial = oci_parse($this->conn, $strmaterial);
                        oci_execute($querymaterial, 0);
                        $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);

                        if ($ul == 2) {
                            //penambahan kondisi get material smbr layer 2
                            if($COM == "SMBR"){
                                $materialsmbr = $rowmaterial["KD_MATERIAL_OPCO"];
                            } else {
                                $materialsbi = $rowmaterial["KD_MATERIAL_OPCO"];
                            } 
                        } else if ($ul == 4) {
                            $materialsmbr = $rowmaterial["KD_MATERIAL_OPCO_2"];
                        }
                    

                        $strorder = "SELECT * 
                        FROM ZMD_MAPPING_ORDER 
                        WHERE ORDER_TYPE_MD = '{$so_type}' 
                        AND COMPANY_OPCO = '{$sales_org}' AND DEL = 0";
                        $queryorder = oci_parse($this->conn, $strorder);
                        oci_execute($queryorder, 0);
                        $roworder = oci_fetch_array($queryorder, OCI_ASSOC);

                        list($day, $month, $year) = split("-", $tanggall);
                        $tgl_kirim = $year . $month . $day;


                        $strshipto = "SELECT * 
                        FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                        WHERE SOLD_TO_MD = '{$soldtocek}' 
                        AND SHIP_TO_MD = '{$shiptotocek}'  
                        AND DEL = 0 
                        AND PLANT_MD = '{$plant1}'
                        AND ORG_OPCO = '{$sales_org}' ";
                        $queryshipto = oci_parse($this->conn, $strshipto);
                        oci_execute($queryshipto, 0);
                        $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                        if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 

                            $strshipto = "SELECT * 
                            FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                            WHERE SOLD_TO_MD = '{$soldtocek}' 
                            AND SHIP_TO_MD = '{$shiptotocek}'  
                            AND DEL = 0 
                            AND PLANT_MD = 'ALL'
                            AND ORG_OPCO = '{$sales_org}' ";
                            $queryshipto = oci_parse($this->conn, $strshipto);
                            oci_execute($queryshipto, 0);
                            $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                        }

                        if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                      
                            $strshipto = "SELECT * 
                            FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                            WHERE SOLD_TO_MD = '{$soldtocek}' 
                            AND SHIP_TO_MD = '{$shiptotocek}'  
                            AND DEL = 0 
                            AND PLANT_MD = 'ALL'
                            AND ORG_OPCO = 'ALL' ";
                            $queryshipto = oci_parse($this->conn, $strshipto);
                            oci_execute($queryshipto, 0);
                            $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                        }

                        if ($ul == 2) {
                            //penambahan kondisi get customer smbr layer 2
                            if($COM == "SMBR"){
                                $soldtosmbr = $rowshipto['SOLD_TO_OPCO'];
                                $shiptosmbr = $rowshipto['SHIP_TO_OPCO'];
                                $SOMD = $SO1;
                            } else {
                                $soldtosbi = $rowshipto['SOLD_TO_OPCO'];
                                $shipto = $rowshipto['SHIP_TO_OPCO'];
                                $SOMD = $SO1;
                            } 
                        } else if ($ul == 4) {
                            $soldtosmbr = $rowshipto['SOLD_TO_OPCO_2'];
                            $shiptosmbr = $rowshipto['SHIP_TO_OPCO_2'];
                            $SOMD = $SO3;
                        }

                        if($incoterm1=="FOT"){
                            $incotermsmbr="AS";
                        }else{
                            $incotermsmbr="FRC";
                        }

                        $strtop = "SELECT * FROM ZMD_MAPPING_TOP WHERE COM_SMI = '{$sales_org1}' AND TOP_SMI = '{$top}' AND (COM_SB = '{$sales_org}' or COM_SB = 'ALL')  AND DEL = 0 order by COM_SB DESC";
                        // echo $strtop;
                        $querytop = oci_parse($this->conn, $strtop);
                        oci_execute($querytop, 0);
                        $rowtop = oci_fetch_array($querytop, OCI_ASSOC);
                        if (ISSET($rowtop['TOP_SB'])) {
                            $topsbi = $rowtop['TOP_SB'];
                        }else{
                            $topsbi = null;
                        }
                        
                        //cek seles unit
                        $strtop_cek = "SELECT * FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 
                        WHERE MATNR = '{$material}' AND WERKS = '{$plant1}'";
                        // echo $strtop;
                        $querytop_cek = oci_parse($this->conn, $strtop_cek);
                        oci_execute($querytop_cek, 0);
                        $rowtop_seles = oci_fetch_array($querytop_cek, OCI_ASSOC);
                        
                        ////////////////////////////////
                        $curl = curl_init();
                        // echo "Get Token </br>";
                        curl_setopt_array($curl, array(
                          //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/auth/signin',
                          CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/auth/signin',
                          CURLOPT_RETURNTRANSFER => true,
                          CURLOPT_ENCODING => '',
                          CURLOPT_MAXREDIRS => 10,
                          CURLOPT_TIMEOUT => 0,
                          CURLOPT_FOLLOWLOCATION => true,
                          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                          CURLOPT_CUSTOMREQUEST => 'POST',
                          CURLOPT_POSTFIELDS =>'{
                          "username":"smbr-md",
                            "password":"ciT@la-Plg"
                        }
                        }',
                          CURLOPT_HTTPHEADER => array(
                            'api-key: TissueBasaHCrlfUtf8@Spaces4Ln35Col44!!!',
                            'Content-Type: application/json'
                          ),
                        ));
                        
                        $response = curl_exec($curl);
                        // print_r("return SIGN IN CURL SMBR".curl_error($curl));
                        // echo '<pre>';
                        // print_r($response);
                        // echo '<br>';
                        // echo '</pre>';
                        
                        curl_close($curl);
                        $return_signin_smbr = json_decode($response, true);
                                $token = $return_signin_smbr["token"];
                        // echo '<pre>';
                        // echo json_encode($return_signin_smbr);
                        // echo '<br>';
                        // echo '</pre>';
                                // return $token;
                        // }
                        ///////////////////////////////
                        $row_sales_unit = $rowtop_seles['MEINS'];
                        if($rowtop_seles['MEINS'] == "TO"){
                            $row_sales_unit = "TON";
                        }elseif($rowtop_seles['MEINS'] == "BAG"){
                            $row_sales_unit = "BGB";
                        }

                        $dataparam= array();
                        $dataparam["csmsnumber"]=$no_pp;
                        $dataparam["plant"]=$plant;
                        $dataparam["sosignumber"]=$SO1;
                        $dataparam["documenttype"]="ZOOL";
                        $dataparam["soldtoparty"]=$soldtosmbr;
                        $dataparam["shiptoparty"]=$shiptosmbr;
                        $dataparam["paymentmethod"]="104";
                        $dataparam["paymentterm"]=$topsbi;
                        $dataparam["requestdeliverydate"]=date('Y-m-d', strtotime($tgl_fdate_socc));
                        $dataparam["shippingType"]="01";
                        $dataparam["incoterm"]=$incotermsmbr;
                        $dataparam["itemnumber"]="000010";
                        $dataparam["material"]=$materialsmbr;
                        // $dataparam["salesunit"]="ZAK";
                        $dataparam["salesunit"]=$row_sales_unit;//$rowtop_seles['MEINS'];
                        $dataparam["quantity"]=$datapp['TRANS_DTL'][0]['QTY_PP'];//$qtyx;
                        // $dataparam["pricelist"]="17";
                        //pegkondisian pricelice 30 Maret 2023
                        if($ul == 2){
                            // layer 2 (MD-SMBR)
                            //cek mapping price list
                            $strtop_pl = "SELECT PL_OPCO FROM ZMD_MAPPING_PRICELIST 
                            WHERE ORG = '{$sales_org1}' 
                            AND PL_MD = '{$prclst}' 
                            AND TOP_MD = '{$top}' 
                            AND ORG_OPCO = '{$sales_org2}' AND DEL = '0' ";
                            // echo '<pre>'; echo $strtop_pl; echo '</pre>';
                            $querytop_pl = oci_parse($this->conn, $strtop_pl);
                            oci_execute($querytop_pl, 0);
                            $set_price_list = oci_fetch_array($querytop_pl, OCI_ASSOC);

                            // if($_POST['pricelist'] == "01" || $_POST['pricelist'] == "06"){
                            //     $dataparam["pricelist"]="20";
                            // }elseif($_POST['pricelist'] == "10"){
                            //     $dataparam["pricelist"]="19";
                            // }else{
                            //     $dataparam["pricelist"]="18";
                            // }
                            $dataparam["pricelist"]=$set_price_list["PL_OPCO"];
                            $dataparam["bussinies_partner"]=$sales_org1;
                            // penambahan kondisi untuk pengecekan ICS apa tidak
                            if($cek_com_opco2 == 'ada'){
                                $dataparam["transaction_category"]="MCS";
                            }else{
                                $dataparam["transaction_category"]="MD";
                            }
                        }elseif($ul == 4){
                            // multi ICS SMBR
                            $dataparam["pricelist"]="17";
                            $dataparam["bussinies_partner"]=$sales_org2;
                            $dataparam["transaction_category"]="ICS";

                            $incoterm1 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $inctm;
                            // $incoterm2 = ISSET($row['INCOTERM_SOURCE']) ? $row['INCOTERM_SOURCE'] : $_POST['nama_incoterm'];

                            if($incoterm1=="FOT"){
                                $incotermsmbr="AS";
                            }else{
                                $incotermsmbr="FRC";
                            }
                        }
                        $deliveriBrock = $datapp['CARA_BAYAR'];
                        if ($deliveriBrock == 'CASH') {
                            $dataparam["deliveryBlock"]="Z1";
                        } else {
                            $dataparam["deliveryBlock"]="";
                        }

                        // penambahan parametter untuk SO MD penambahan subdist
                        $dataparam["subdistCode"] = $soldtocek;
                        $dataparam["subdistName"] = $datapp['NAMA_SOLD_TO'];

                        $dataparam["token"]=$token; 
                        $com_royaltysmbr = $com_royaltyopco == 'ROYALTY_MSA' ? 'ROYALTYMSA' : $com_royaltyopco;
                                                                // print_r("smbr tidak? ".$com_royaltysmbr);
                        $dataparam["royaltyFlag"]=$com_royaltysmbr;
                        // echo '<pre> data untuk lemparan SO SMBR : <br>';
                        // print_r($dataparam);
                        echo '<pre>';
                        echo "ini buat so smbr";
                        echo print_r($dataparam);
                        echo '<br>';
                        echo '</pre>';
                        $cetak_so_smbr = $this->createso_smbr($dataparam);

                        $datareturnsmbr = json_decode(json_encode($cetak_so_smbr),true);
                        $data_output = json_decode($cetak_so_smbr, true);

                        // echo '<pre>';
                        // print_r($data_output);
                        // echo '</pre>';
                        /////////////////////////////////////////////
                        // penambahan lempar parameter deleveri block smbr
                        if ($deliveriBrock == 'CASH') {
                            $deliveryBlock = 'Z1';
                        } else {
                            $deliveryBlock = '';
                        }
                        
                        $curl2 = curl_init();
                        // echo "Get Token </br>";
                        curl_setopt_array($curl2, array(
                          //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/v1/so/open-delivery-block',
                          CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/v1/so/open-delivery-block',
                          CURLOPT_RETURNTRANSFER => true,
                          CURLOPT_ENCODING => '',
                          CURLOPT_MAXREDIRS => 10,
                          CURLOPT_TIMEOUT => 0,
                          CURLOPT_FOLLOWLOCATION => true,
                          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                          CURLOPT_CUSTOMREQUEST => 'POST',
                          CURLOPT_POSTFIELDS =>'{
                            "soNumber":"'.$data_output["data"]["soNumber"].'",
                            "deliveryBlock":"'.$deliveryBlock.'"
                        }
                        }',
                          CURLOPT_HTTPHEADER => array(
                            'Authorization:Bearer '.$token,
                            'Content-Type:application/json'
                          ),
                        ));
                        
                        $response_block = curl_exec($curl2);
                        // print_r("return SIGN IN CURL SMBR".curl_error($curl));
                        // print_r($response_block);
                        
                        curl_close($curl2);
                        $return_delivery_block = json_decode($response_block, true);
                        // print_r($return_delivery_block);

                        // end API delivery block
                        ///////////////////////////////////
                        
                        if($data_output["status"]=="200"){
                            $cetak_so_smbr = $data_output["data"]["soNumber"];
                
                            foreach($data_output["data"]["items"] as $key => $value){
                                $hargasmbr = $value["itemPrice"];
                                $satuansmbr = $value["salesUnit"];
                            }
                        }
                            $datarequestsmbr = json_encode($dataparam);
                            // $datareturnsmbr = json_decode(json_encode($cetak_so_smbr),true);
                            $field_names = array('GROUP_LOG','REQUEST','RESPON','BY_LOG','LOG_DATE','TOKEN');
                            $field_data = array("1","$datarequestsmbr","$datareturnsmbr","SCHEDULER_SO_SMBR","SYSDATE","$token");
                            $tablename = "ZREPORT_LOG_SERVICE";
                            $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
                            
                            // print_r("masuk ".$cetak_so_smbr);

                        if ($ul == 2) {
                            $nomorso = $cetak_so_smbr;
                            $SO2 = $cetak_so_smbr;
                        } else if ($ul == 4) {
                            $nomorso = $cetak_so_smbr;
                            $SO4 = $cetak_so_smbr;
                        }

                        if($data_output["status"]=="200"){//$cetak_so_smbr==''
                            $show_ket .= $ul . ". SO OPCO SMBR = Sales Order has been made with a number : " . $nomorso;
                            $show_ket .= '<br>';
                        }else{
                            $next = 'N';
                            $show_ket .= $ul . ". SO OPCO SMBR = " . $cetak_so_smbr;
                            $show_ket .= '<br>';
                        }
                    
                    } else if ($ul == 2 || $ul == 4 && $COM == "SBI") {
                        //print_r($ul);
                        //print_r("<br />");
                        // build array material
                        $Tmaterial = array();
                        $Tmaterial['MaterialCode'] = array();
                        $Tmaterial['PlantCode'] = array();
                        $Tmaterial['Qty'] = array();
                        $Tmaterial['QtyUnit'] = array();
                        $Tmaterial['ScheduleLine'] = array();
                        $Tmaterial['RequestDeliveryDate'] = array();
                        $Tmaterial['RequestDeliveryQty'] = array();
                        $Dmaterial = array();
                        for ($j = 0; $j <= $sampai - 1; $j++) {
                            $material = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
                            $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE VKORG = '{$sales_org1}' AND MATNR = '{$material}'  AND WERKS = '{$plant1}'";
                            $querymat = oci_parse($this->conn, $strmat);
                            oci_execute($querymat, 0);
                            $rowmat = oci_fetch_array($querymat, OCI_ASSOC);
//                            echo json_encode($rowmat); 
                            if (count($rowmat) > 0 && $rowmat['MEINS'] == "ZAK") {
                                $qty = ( intval($datapp['TRANS_DTL'][$j]['QTY_PP']) * $rowmat['BERAT'] ) / 1000;
                                $qtyx = ( intval($datapp['TRANS_DTL'][$j]['QTY_APPROVE']) * $rowmat['BERAT'] ) / 1000;
                            } else {
                                $qty = $datapp['TRANS_DTL'][$j]['QTY_PP'];
                                $qtyx = $datapp['TRANS_DTL'][$j]['QTY_APPROVE'];
                            }



                            $shiptotocek = $datapp['TRANS_DTL'][$j]["SHIP_TO"];
                            $item_numline = sprintf("%04d", $j + 1);
                            // $tanggall = $datapp['TRANS_DTL'][$j]["TGL_KIRIM_PP"];
                            $tgl_kirim = date('Y-m-d', strtotime($datapp['TRANS_DTL'][$j]["TGL_KIRIM_PP"]));
                            // list($day, $month, $year) = split("-", $tanggall);
                            // $tgl_kirim = $year . "-" . $month . "-" . $day;

                            $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                            $querymaterial = oci_parse($this->conn, $strmaterial);
                            oci_execute($querymaterial, 0);
                            $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);

                            if ($ul == 2) {
                                $materialsbi = $rowmaterial["KD_MATERIAL_OPCO"];
                            } else if ($ul == 4) {
                                $materialsbi = $rowmaterial["KD_MATERIAL_OPCO_2"];
                            }
                            $Tmaterial['MaterialCode'] = $materialsbi;
                            $Tmaterial['PlantCode'] = $plant;
                            $Tmaterial['Qty'] = $qty;
                            $Tmaterial['QtyUnit'] = "TO";
                            $Tmaterial['ScheduleLine'] = $item_numline;
                            // $Tmaterial['RequestDeliveryDate'] = $tgl_kirim;
                            $Tmaterial['RequestDeliveryDate'] = date('Y-m-d', strtotime($tgl_fdate_socc));                            
                            $Tmaterial['RequestDeliveryQty'] = $qty;
                            $Dmaterial[] = $Tmaterial;
                        }

                        $strorder = "SELECT * FROM ZMD_MAPPING_ORDER WHERE ORDER_TYPE_MD = '{$so_type}' AND COMPANY_OPCO = '{$sales_org}' AND DEL = 0";
                        $queryorder = oci_parse($this->conn, $strorder);
                        oci_execute($queryorder, 0);
                        $roworder = oci_fetch_array($queryorder, OCI_ASSOC);

                        list($day, $month, $year) = split("-", $tanggall);
                        $tgl_kirim = $year . $month . $day;


                        $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                        WHERE SOLD_TO_MD = '{$soldtocek}' 
                        AND SHIP_TO_MD = '{$shiptotocek}'  
                        AND DEL = 0 
                        AND ORG_OPCO = '{$row['COM_OPCO']}'";
                        $queryshipto = oci_parse($this->conn, $strshipto);
                        oci_execute($queryshipto, 0);
                        $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);

                        $arr_shipto = array();

                        while ($temp_data_sipto = oci_fetch_array($queryshipto, OCI_ASSOC)) {
                            //$arr_plant_po[] = $temp_data_plant;
                            $arr_shipto[] = $temp_data_sipto;
                        }

                        if(count($arr_shipto) <= 1){
                            if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                                $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY 
                                WHERE SOLD_TO_MD = '{$soldtocek}' 
                                AND SHIP_TO_MD = '{$shiptotocek}' AND DEL = 0";
                                $queryshipto = oci_parse($this->conn, $strshipto);
                                oci_execute($queryshipto, 0);
                                $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                            }
                            if ($ul == 2) {
                                $soldtosbi = $rowshipto['SOLD_TO_OPCO'];
                                $shipto = $rowshipto['SHIP_TO_OPCO'];
                                $SOMD = $SO1;
                            } else if ($ul == 4) {
                                $soldtosbi = $rowshipto['SOLD_TO_OPCO_2'];
                                $shipto = $rowshipto['SHIP_TO_OPCO_2'];
                                $SOMD = $SO3;
                            }
                        }

                        if(count($arr_shipto) > 1){
                            if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                                $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}' AND PLANT_MD = '{$plant1}'  AND DEL = 0";
                                $queryshipto = oci_parse($this->conn, $strshipto);
                                oci_execute($queryshipto, 0);
                                $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                            }
                            if ($ul == 2) {
                                $soldtosbi = $rowshipto['SOLD_TO_OPCO'];
                                $shipto = $rowshipto['SHIP_TO_OPCO'];
                                $SOMD = $SO1;
                            } else if ($ul == 4) {
                                $soldtosbi = $rowshipto['SOLD_TO_OPCO_2'];
                                $shipto = $rowshipto['SHIP_TO_OPCO_2'];
                                $SOMD = $SO3;
                            }
                        }

                        // $tgl_ppPO = $datapp['TGL_PP'];
                        // list($day, $month, $year) = split("-", $tgl_ppPO);
                        // $tgl_ppPO = $year . "-" . $month . "-" . $day . " 00:00:00";

                        $tgl_ppPO = date('Y-m-d', strtotime($datapp['TGL_PP']));
                        // list($day, $month, $year) = split("-", $tgl_pp);
                        $tgl_ppPO = $tgl_ppPO. " 00:00:00";
                        //proyek SOCC v2
                        //old
                        // $tgl_pp = date('Ymd', strtotime($datapp['TGL_PP']));
                        // list($day, $month, $year) = split("-", $tgl_pp);
                        // $tgl_pp = $year . $month . $day;
                        ///////////////
                        //new
                        $tgl_pp=$tgl_fdate_socc;

                        $rddsbi = date('Y-m-d', strtotime($tgl_terima_leadtime));


                        if ($sales_org == "PTSC") {
                            $sales_organization = "SCBD";
                        } else if ($sales_org == "ID50") {
                            $sales_organization = "ID11";
                        }

                        if(empty($ship_condition)){
                        
                            if ($incoterm1 == "FOT") {
                                $shipcond_sbi = "PK";
                            } else if ($incoterm1 == "FRC") {
                                $shipcond_sbi = "D0";
                            } else if ($incoterm1 == "CNF") {
                                $shipcond_sbi = "DV";
                            } else {
                                $shipcond_sbi = "";
                            }
                        
                        }else{
                            $shipcond_sbi = $ship_condition;
                        }
                        
                        $strtop = "SELECT * FROM ZMD_MAPPING_TOP WHERE COM_SMI = '{$sales_org1}' AND TOP_SMI = '{$top}' AND (COM_SB = '{$sales_org}' or COM_SB = 'ALL')  AND DEL = 0 order by COM_SB DESC";
                        $querytop = oci_parse($this->conn, $strtop);
                        oci_execute($querytop, 0);
                        $rowtop = oci_fetch_array($querytop, OCI_ASSOC);
                        if (ISSET($rowtop['TOP_SB'])) {
                            $topsbi = $rowtop['TOP_SB'];
                        }else{
                            $topsbi = null;
                        }

                        // Mapping Royalty MSA
                        $isRoyaltyMsa = null;
                        if ($com_royaltyopco == "ROYALTY") {
                            $isRoyaltyMsa = "R";
                        } else if ($com_royaltyopco == "ROYALTY_MSA") {
                            $isRoyaltyMsa = "RM";
                        } else if ($com_royaltyopco == "MSA") {
                            $isRoyaltyMsa = "M";
                        } else {
                            $isRoyaltyMsa = "";
                        }
                        // print_r("msa tidak ? ".$isRoyaltyMsa);
                        //$url = 'https://sip.solusibangunindonesia.com/APIMD/CreateSalesOrder';
                        $url = 'https://integrasi-api.sig.id/apimd/createsalesorder/dev'; // Prod Synxchrox
                        $data = array('Token' => 'aSsMx7GV0HFGzlufM4DH',
                            'SystemID' => 'QASSO', //'QASSO',
                            'Data' => array('SalesDocumentType' => $roworder["ORDER_TYPE_OPCO"],
                                'SalesOrganization' => $sales_organization,
                                'DistributionChannel' => 'DB',
                                'Division' => 'CM',
                                'SalesDocumentDate' => $tgl_pp,
                                'RequestDeliveryHeader' => $rddsbi,
                                'ShippingCondition' => $shipcond_sbi, //shipcond
                                'ShippingType' => 'G4',
                                'SoldToCode' => $soldtosbi,
                                'ShipToCode' => $shipto,
                                'MDReferenceSONumber' => $SOMD,
                                // 'MDPurchaseOrderNumber' => $no_pp.($com_royalty=='ROYALTY' ? 'R' : ''),
                                'MDPurchaseOrderNumber' => $no_pp.$isRoyaltyMsa,
                                'MDPurchaseOrderItem' => '00010',
                                'PurchaseOrderNumber' => $no_pp,
                                'PurchaseOrderDate' => $tgl_ppPO,
                                'PaymentTerms' => $topsbi,
                                'Items' => $Dmaterial
                            )
                        );
                        $options = array(
                            'http' => array(
                                'header' => "Content-type: application/json\r\n",
                                'method' => 'POST',
                                'content' => json_encode($data),
                            )
                        );

//                        print_r($option)

                        $context = stream_context_create($options);

                        $result = file_get_contents($url, false, $context);

                        $response = json_decode($result);
                        if ($ul == 4) {
                            $responseSBI2 = $response;
                        }
                        $status = $response->Status;
                        $Message = $response->Message;


                        $Message_detail = $response->MessageDetail;
                        $param_send = json_encode($data);
                        $param_return = json_encode($response);

                        $field_names = array(
                            'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
                        );
                        $field_data = array(
                            "$param_send", "$param_return", "scheduler", "$no_pp", "$Message", "$Message_detail", "SYSDATE"
                        );
                        $tablename = "ZMD_LOG_SBI";
                        $sukses = $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
                        //print_r($status);
                        //print_r($Message);
                        if ($ul == 2) {
                            $nomorso = $response->Data->SAPSalesOrderNumber;
                            $SO2 = $response->Data->SAPSalesOrderNumber;
                            $tgl_deleteSO2 = $response->Data->SAPCreatedDateStr;
                        } else if ($ul == 4) {
                            $nomorso = $response->Data->SAPSalesOrderNumber;
                            $SO4 = $response->Data->SAPSalesOrderNumber;
                            $tgl_deleteSO4 = $response->Data->SAPCreatedDateStr;
                        }
                        if ($status != 1) {
                            $next = 'N';
                            $show_ket .= $ul . ". SO OPCO = " . $Message_detail;
                            $show_ket .= '<br>';
                        } else {
                            $show_ket .= $ul . ". SO OPCO = Sales Order has been made with a number adalah : " . $nomorso;
                            $show_ket .= '<br>';
                        }
                        $Dataarray = json_decode(json_encode($response->Data->Items), true);
                        //print_r($nomorso);
//                        echo json_encode($response->Data);
//                        echo json_encode($data);

                        $SOMDNE = 'Y';
                        $jerror += 1;
                    } else if ($ul == $perulangan) { // PROSES PERULANGAN TERAKHIR CREATE PO
                        // GET HARGA PER LINE ITEM
                        for ($p = 1; $p <= $perulanganPO; $p++) { // perulangan PO MULTI ICS atau TIDAK
                            if ($p == 1) {
                                $incoterm1 = $incotermPO1;
                                $incoterm2 = $incotermPO12;
                                $distr_chan = $distr_chanPO1;
                            } else {
                                $incoterm1 = $incotermPO2;
                                $incoterm2 = $incotermPO22;
                                $distr_chan = $distr_chanPO2;
                            }

                            //perubahan get KD VENDOR
                            $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$plant1}'  AND DEL = 0";
                            //filter plant change adyjuliyanto
                            $querykdven = oci_parse($this->conn, $strkdven);
                            oci_execute($querykdven, 0);
                            
                            $arr_plant_po = array();
                            
                            while ($temp_data_plant = oci_fetch_array($querykdven, OCI_ASSOC)) {
                                $arr_plant_po[] = $temp_data_plant;
                            }

                          
                            //jika plant hanya satu
                            if(count($arr_plant_po) <= 1){
                                //mengubah array menjadi objek sehingga perubahan code tidak terlalu banyak
                                $rowkdven = reset($arr_plant_po);
                            }


                            //jika ada plant lebih dari satu
                            if(count($arr_plant_po) > 1){
                                $value_po = array();

                                $incoterm_found = false;
                                //filter jika terdapat incoterm di mapping plant
                                foreach ($arr_plant_po as $value) {  
                                    if (trim($element['INCOTERM_MD']) === trim($inctm)) {
                                        $incoterm_found = true;
                                        $value_po[] = $value;
                                    }  
                                }
                                
                                //filter jika tidak ada mappign plant
                                if(!$incoterm_found){
                                    foreach ($arr_plant_po as $value) {  
                                        if (!isset($value['INCOTERM_MD']) || $value['INCOTERM_MD'] == null) {
                                            $value_po[] = $value;
                                        }  
                                    }
                                }
                                
                                //mengubah array menjadi objek sehingga perubahan code tidak terlalu banyak
                                $rowkdven = reset($value_po);
                                
                            }
                            
                            $mapping_sloc = '';
                            $schedule_line = '';
                            if ($p == 1) {
                                $SO_PO = $SO2;
                                $vendorFIX = $rowkdven["KET"];
                                $COM = $rowkdven["COM_MD"];
                                $COM_HARGA = $rowkdven["COM_OPCO"];
                                $plantPO = $plant1;
                                $top_po = $top;
                                $mapping_sloc = 'D101';
                                $schedule_line = 'X';
                                $po_deliv_date = $rdd_from_SO;
                            } else if ($p == 2) {
                                $SO_PO = ($SO3 != "" ? $SO3 : $SO4);
                                $vendorFIX = $rowkdven["KET_OPCO"];
                                $COM = $rowkdven["COM_OPCO"];
                                $COM_HARGA = $rowkdven["COM_MD_2"];
                                $schedule_line = '0001';
                                $top_po = 'ZG90';
                                $po_deliv_date = $deliv_date;
                                $plantPO = $plant2;
                                if($rowkdven['SLOC'] == null){
                                    $mapping_sloc = 'D101';
                                }else{
                                    $mapping_sloc = $rowkdven['SLOC'];
                                }
                            } else if ($p == 3) {
                                $SO_PO = $SO4;
                                $vendorFIX = $rowkdven["KET2"];
                                $COM = $rowkdven["COM_MD_2"];
                                $COM_HARGA = $rowkdven["COM_OPCO_2"];
                                $schedule_line = '0001';
                                $top_po = 'ZG90';
                                $po_deliv_date = $deliv_date;
                                $plantPO = $plant3;
                                if($rowkdven['SLOC'] == null){
                                    $mapping_sloc = 'D101';
                                }else{
                                    $mapping_sloc = $rowkdven['SLOC'];
                                }
                            }

                            if ($COM != "PTSC" && $COM != "ID50") {
                                if ($COM_HARGA != "PTSC" && $COM_HARGA != "ID50" && $COM_HARGA!="1000") {
                                    $fce = $sap->NewFunction("BAPISDORDER_GETDETAILEDLIST");
                                    $fce->I_BAPI_VIEW["SDCOND"] = "X";
                                    $fce->SALES_DOCUMENTS->row["VBELN"] = $SO_PO;
                                    $fce->SALES_DOCUMENTS->Append($fce->SALES_DOCUMENTS->row);
                                    // echo "<pre>"; 
                                    // print_r($fce);
                                    $fce->Call();
                                    if ($fce->GetStatus() == SAPRFC_OK) {
                                        $fce->ORDER_CONDITIONS_OUT->Reset();
                                        $totL = 0;
                                        $builder = array();
                                        $dataH = array();
                                        $dataH["item_number"] = array();
                                        $dataH["nilai_harga"] = array();
                                        $dataH["per"] = array();
                                        $dataH["satuan"] = array();
                                        while ($fce->ORDER_CONDITIONS_OUT->Next()) {
                                            if ($fce->ORDER_CONDITIONS_OUT->row["COND_TYPE"] == "ZPR0") {
                                                $dataH["item_number"] = $fce->ORDER_CONDITIONS_OUT->row["ITM_NUMBER"];
                                                $dataH["nilai_harga"] = $fce->ORDER_CONDITIONS_OUT->row["COND_VALUE"];
                                                $dataH["per"] = $fce->ORDER_CONDITIONS_OUT->row["COND_P_UNT"];
                                                $dataH["satuan"] = $fce->ORDER_CONDITIONS_OUT->row["COND_D_UNT"];
                                                $dataH["satuan2"] = $fce->ORDER_CONDITIONS_OUT->row["T_UNIT_ISO"];
                                                $dataH["currency"] = $fce->ORDER_CONDITIONS_OUT->row["CURRENCY"];
                                                $builder[] = $dataH;
                                                $totL += 1;
                                            }
                                        }
                                        //Commit Transaction
                                        $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                                        $fce->Call();
                                        if ($totL != 1) {
                                            $next = "N";
                                            $show_ket .= "Master Harga Belum Ada...!!!";
                                            $show_ket .= '<br>';
                                        }
                                    }
                                }

                                if ($next == "Y") {
                                    // CREATE PO
                                    sleep(2);
                                    // khsus smbr
                                    if($COM == "1000" && $COM_HARGA == "3000" ){
                                        // echo "Get Token </br>";
                                        $curl = curl_init();
                                        curl_setopt_array($curl, array(
                                          //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/auth/signin',
                                          CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/auth/signin',
                                          CURLOPT_RETURNTRANSFER => true,
                                          CURLOPT_ENCODING => '',
                                          CURLOPT_MAXREDIRS => 10,
                                          CURLOPT_TIMEOUT => 0,
                                          CURLOPT_FOLLOWLOCATION => true,
                                          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                          CURLOPT_CUSTOMREQUEST => 'POST',
                                          CURLOPT_POSTFIELDS =>'{
                                          "username":"smbr-md",
                                            "password":"ciT@la-Plg"
                                        }
                                        }',
                                          CURLOPT_HTTPHEADER => array(
                                            'api-key: TissueBasaHCrlfUtf8@Spaces4Ln35Col44!!!',
                                            'Content-Type: application/json'
                                          ),
                                        ));
                                        
                                        $response = curl_exec($curl);
                                        // print_r("return SIGN IN CURL SMBR".curl_error($curl));
                                        // print_r($response);
                                        
                                        curl_close($curl);
                                        $return_signin_smbr = json_decode($response, true);
                                        // $token = $return_signin_smbr["token"];

                                        // print_r("TOKEN : ".$token);
                                        // echo '<br>';
                                        // echo '<br>';

                                        
                                        if($satuansmbr == 'TON'){
                                            $satuanPO = 'TO';
                                            // $satuanPO = 'ZAK';
                                        }elseif($satuansmbr == 'BGB'){
                                            $satuanPO = 'BAG';
                                        }else{
                                            $satuanPO = $satuansmbr;
                                        }

                                        // Pembuatan PO SMBR
                                        $curl2 = curl_init();
                                        curl_setopt_array($curl2, array(
                                        //CURLOPT_URL => 'http://10.10.2.182:8010/smbr/md/api/v1/po/create',
                                        CURLOPT_URL => 'http://10.10.101.182:8010/smbr/md/api/v1/po/create',
                                        CURLOPT_RETURNTRANSFER => true,
                                        CURLOPT_ENCODING => '',
                                        CURLOPT_MAXREDIRS => 10,
                                        CURLOPT_TIMEOUT => 0,
                                        CURLOPT_FOLLOWLOCATION => true,
                                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                        CURLOPT_CUSTOMREQUEST => 'POST',
                                        CURLOPT_POSTFIELDS =>'{
                                            "soNumber": '.$SO2.',
                                            "soOpcoNumber": '.(int)$SO_PO.',
                                            "poDate":"'.date('Y-m-d', strtotime($tgl_fdate_socc)).'",
                                            "vendor":"'.$vendorFIX.'",
                                            "currency":"IDR",
                                            "items":[
                                                {
                                                    "itemNumber":"000010",
                                                    "material":"'.$material.'",
                                                    "plant":"'.$plant2.'",
                                                    "storageLocation":"GM01",
                                                    "quantity":'.$datapp['TRANS_DTL'][0]['QTY_PP'].',
                                                    "measure":"'.$satuanPO.'",
                                                    "netPrice":'.($net_price_smbr*1).',            
                                                    "perPrice":'.$net_per_smbr.'
                                                }
                                            ]
                                        }
                                        }',
                                        CURLOPT_HTTPHEADER => array(
                                            'Authorization:Bearer '.$token,
                                            'Content-Type:application/json'
                                        ),
                                        ));

                                        // print_r('{
                                        //     "soNumber": '.$SO2.',
                                        //     "soOpcoNumber": '.(int)$SO_PO.',
                                        //     "poDate":"'.date('Y-m-d', strtotime($tgl_fdate_socc)).'",
                                        //     "vendor":"6100174",
                                        //     "currency":"IDR",
                                        //     "items":[
                                        //         {
                                        //             "itemNumber":"000010",
                                        //             "material":"'.$material.'",
                                        //             "plant":"'.$plant2.'",
                                        //             "storageLocation":"GM01",
                                        //             "quantity":'.$_POST['com_qtyx0'].',
                                        //             "measure":"'.$satuanPO.'",
                                        //             "netPrice":'.($net_price_smbr*1).',            
                                        //             "perPrice":'.$net_per_smbr.'
                                        //         }
                                        //     ]
                                        // }');

                                        // print_r($curl2);
                                        
                                        $response_data_po = curl_exec($curl2);
                                        // print_r($response_data_po);
                                        // print_r("return SIGN IN CURL SMBR".curl_error($curl));
                                        // print_r($response_block);
                                        
                                        curl_close($curl2);
                                        $return_po_smbr = json_decode($response_data_po, true);
                                        // print_r($return_po_smbr);
                                        
                                        if($return_po_smbr["status"]=="200"){
                                            $PO2 = $return_po_smbr["data"]["poNumber"];
                                            $show_ket .= "PO SMBR has been made with a number : ".$return_po_smbr["data"]["poNumber"];
                                            $show_ket .= '<br>';
                                        }else{
                                            $next = "N";
                                        }
                                    }else{
                                        $fce = $sap->NewFunction("BAPI_PO_CREATE1");
                                        $tgl_sekarang = date("Ymd");

                                        $strven = "SELECT * FROM ZMD_MAPPING_VENDOR_MD WHERE COMPANY_CODE = '{$COM}' AND KD_VENDOR = '{$vendorFIX}' AND DEL =0";
                                        
                                        //print_r($strven);
                                        $queryven = oci_parse($this->conn, $strven);
                                        oci_execute($queryven, 0);
                                        $rowven = oci_fetch_array($queryven, OCI_ASSOC);

                                        $fce->POHEADER["COMP_CODE"] = $COM; // company kedua
                                        $fce->POHEADER["DOC_TYPE"] = "ZIC1"; // hardcode
                                        $fce->POHEADER["VENDOR"] = $vendorFIX; //
                                        $fce->POHEADER["PMNTTRMS"] = $top_po;
                                        $fce->POHEADER["PURCH_ORG"] = $rowven["PURCHASE_ORGANIZATION"];
                                        $fce->POHEADER["PUR_GROUP"] = $rowven["PURCHASE_GROUP"]; 
                                        //                        $fce->POHEADER["CURRENCY"] = "IDR";
                                        //                        $fce->POHEADER["EXCH_RATE"] = "1";
                                        $fce->POHEADER["DOC_DATE"] = $tgl_sekarang;
                                        $fce->POHEADER["INCOTERMS1"] = $incoterm1;
                                        $fce->POHEADER["INCOTERMS2"] = $incoterm2;


                                        $fce->POHEADERX["COMP_CODE"] = "x";
                                        $fce->POHEADERX["DOC_TYPE"] = "x";
                                        $fce->POHEADERX["VENDOR"] = "x";
                                        $fce->POHEADERX["PMNTTRMS"] = "x";
                                        $fce->POHEADERX["PURCH_ORG"] = "x";
                                        $fce->POHEADERX["PUR_GROUP"] = "x";
                                        //                        $fce->POHEADERX["CURRENCY"] = "x";
                                        //                        $fce->POHEADERX["EXCH_RATE"] = "x";
                                        $fce->POHEADERX["DOC_DATE"] = "x";
                                        $fce->POHEADERX["INCOTERMS1"] = "x";
                                        $fce->POHEADERX["INCOTERMS2"] = "x";

                                        $fce->POTEXTHEADER->row["TEXT_ID"] = "F01";
                                        $fce->POTEXTHEADER->row["TEXT_FORM"] = "/";
                                        $fce->POTEXTHEADER->row["TEXT_LINE"] = $rowven["KETERANGAN"];
                                        $fce->POTEXTHEADER->Append($fce->POTEXTHEADER->row);

                                        $fce->POTEXTHEADER->row["TEXT_ID"] = "F14";
                                        $fce->POTEXTHEADER->row["TEXT_FORM"] = "/";
                                        $fce->POTEXTHEADER->row["TEXT_LINE"] = "AUTO GR";
                                        $fce->POTEXTHEADER->Append($fce->POTEXTHEADER->row);

                                        $sampai = 1;
                                        for ($j = 0; $j <= $sampai - 1; $j++) {
                                            $idke = "idke" . $j;
                                                $id_dtl = $datapp['TRANS_DTL'][$j]['ID'];
                                                $item_num = $datapp['TRANS_DTL'][$j]['ITEM_NUMBER'];
                                                // print_r("item_num1 : ".$item_num1);
                                                // $item_num = $fungsi->linenum($item_num1);
                                                // print_r("item_num1 : ".$item_num);
                                                $item_numli = $item_num * 10;
                                                $item_numline = sprintf("%06d", $item_numli);
                                                $item_numlinePO = sprintf("%05d", $item_numli);
                                                $material = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
    //                                    $material1 = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
    //                                    $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' AND KD_MATERIAL_MD = '{$material1}' AND DEL = 0";
    //                                    $querymaterial = oci_parse($this->conn, $strmaterial);
    //                                    oci_execute($querymaterial, 0);
    //                                    $rowmaterial  = oci_fetch_array($querymaterial, OCI_ASSOC);
    //                                    $material = $rowmaterial['KD_MATERIAL_OPCO'];

                                                $qty = $datapp['TRANS_DTL'][$j]['QTY_PP'];
                                                $qtyx = $datapp['TRANS_DTL'][$j]['QTY_APPROVE'];

    //                                        if($sales_org=="7900"){ 
    //                                            $kontrak = $datapp['TRANS_DTL'][$j]['NO_KONTRAK'];
    //                                        }else{
    //                                            $kontrak = "";
    //                                        }
                                                $kontrak = $datapp['TRANS_DTL'][$j]['NO_KONTRAK'];

                                                // $posnr = $fungsi->linenum($datapp['TRANS_DTL'][$j]["ITEM_NUMBER"]);
                                                $posnr = $datapp['TRANS_DTL'][$j]["ITEM_NUMBER"]*10;
                                                $distrik = $datapp['TRANS_DTL'][$j]["KODE_TUJUAN"];

                                                // $tgl_kirimPO = $datapp['TRANS_DTL'][$j]["TGL_KIRIM_PP"];
                                                // list($day, $month, $year) = split("-", $tgl_kirimPO);
                                                // $tgl_kirim = $year . $month . $day;

                                                $tgl_kirim = date('Ymd', strtotime($datapp['TRANS_DTL'][$j]["TGL_KIRIM_PP"]));
                                                list($day, $month, $year) = split("-", $tgl_kirim);
                                                $tgl_kirim = $year . $month . $day;


                                                $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                                                $querymaterial = oci_parse($this->conn, $strmaterial);
                                                oci_execute($querymaterial, 0);
                                                $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);


                                                if ($p == 1) {
                                                    $material = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
                                                } else if ($p == 2) {
                                                    $material = $rowmaterial['KD_MATERIAL_OPCO'];
                                                } else if ($p == 3) {
                                                    $material = $rowmaterial['KD_MATERIAL_MD_2'];
                                                }

                                                $mtr_group = substr($material, 0, 7);


                                                // if (($nomorso != "") and ( $qty1 == $qtyx1))
                                                if (($nomorso != ""))
                                                    $status1 = "APPROVE";
                                                else
                                                    $status1 = "PROCESS";

                                                if ($mtr_group == "121-301") {
                                                    $nm_material = "SEMEN ZAK";
                                                    $po_unit = "ZAK";
                                                } else if ($mtr_group == "121-302") {
                                                    $nm_material = "SEMEN CURAH";
                                                    $po_unit = "TO";
                                                } else if ($mtr_group == "121-200") {
                                                    $nm_material = "CLINKER";
                                                    $po_unit = "TO";
                                                }

    //                                        $strsloc = "SELECT * FROM ZERP_MAP_SLOC WHERE ORG = '{$sales_org1}' AND PLANT = '$plant1' AND KD_MATERIAL = '{$mtr_group}' AND DEL = 0";
    //                                        $queryloc = oci_parse($this->conn, $strsloc);
    //                                        oci_execute($queryloc, 0);
    //                                        $rowloc = oci_fetch_array($queryloc, OCI_ASSOC);
                                                // CEK HARGA DARI BAPI COMMIT HARGA
                                                if ($COM_HARGA == "1000" && $p==1) {
                                                    // foreach ($builder as $val) {
                                                    //     // if ($item_numline == $val['item_number']) {
                                                    //         $hargaPO = $hargasmbr;
                                                    //         $perPO = "1";
                                                    //         $satuanPO = $satuansmbr;
                                                    //         $satuan2PO = $satuansmbr;
                                                    //         $currencyPO = "IDR";
                                                    //     // }

                                                    //     // echo "ini smbr 1".$COM_HARGANYA;
                                                    // }

                                                    //perbaikan untuk go live 30 03 2023
                                                    $hargaPO = $hargasmbr;
                                                    $perPO = "1";
                                                    if($satuansmbr == 'TON'){
                                                        $satuanPO = 'TO';
                                                        $satuan2PO = 'TO';
                                                    }elseif($satuansmbr == 'BGB'){
                                                        $satuanPO = 'BAG';
                                                        $satuan2PO = 'BAG';
                                                    }else{
                                                        $satuanPO = $satuansmbr;
                                                        $satuan2PO = $satuansmbr;
                                                    }
                                                    $currencyPO = "IDR";
                                                //smbr
                                                }else if ($COM_HARGA == "1000" && $p==2) {
                                                    // echo "ini smbr 2 ".$COM_HARGANYA;
                                                        foreach ($builder as $val) {
                                                            // if ($item_numline == $val['item_number']) {
                                                                $hargaPO = $hargasmbr;
                                                                $perPO = "1";
                                                                $satuanPO = $satuansmbr;
                                                                $satuan2PO = $satuansmbr;
                                                                $currencyPO = "IDR";
                                                            // }
                                                        }
                                                }else if ($COM_HARGA != "PTSC" && $COM_HARGA != "ID50") {
                                                    foreach ($builder as $val) {
                                                        if ($item_numline == $val['item_number']) {
                                                            $hargaPO = $val['nilai_harga'];
                                                            $perPO = $val['per'];
                                                            $satuanPO = $val['satuan'];
                                                            $satuan2PO = $val['satuan2'];
                                                            $currencyPO = $val['currency'];
                                                        }
                                                    }
                                                } else {
                                                    if ($p == 1) {
                                                        $datas = $response->Data->Items[$j];
                                                    } else if ($p == 3) {
                                                        $datas = $responseSBI2->Data->Items[$j];
                                                    }
                                                    $hargaPO = $datas->SAPSalesOrderNetPriceUnit;
                                                    $perPO = $datas->SAPSalesOrderPerUnit;
                                                    $satuanPO = $datas->SAPSalesOrderUnit;
                                                    $satuan2PO = $datas->SAPSalesOrderUnit;
                                                    $currencyPO = $datas->SAPSalesOrderCurrency;
                                                }
    //                                    //print_r($builder);
    //                                    //print_r($hargaPO);
    //                                    //print_r($item_numline);

                                                $fce->POITEM->row["PO_ITEM"] = $item_numlinePO;
                                                $fce->POITEM->row["SHORT_TEXT"] = $nm_material;
                                                $fce->POITEM->row["MATERIAL"] = $material;
                                                $fce->POITEM->row["STGE_LOC"] = $mapping_sloc;
                                                $fce->POITEM->row["PLANT"] = $plantPO;
    //                                    $fce->POITEM->row["STGE_LOC"] = $rowloc['KD_SLOC'];
                                                $fce->POITEM->row["MATL_GROUP "] = $mtr_group;
                                                $fce->POITEM->row["QUANTITY"] = $qty;
                                            
                                                $fce->POITEM->row["GR_IND"] = "X";
                                                $fce->POITEM->row["IR_IND"] = "X";
                                                $fce->POITEM->row["GR_BASEDIV"] = "X";
    //                                        $fce->POITEM->row["FUNDS_CTR"] = "7902000000";
                                                $fce->POITEM->Append($fce->POITEM->row);

                                                $fce->POITEMX->row["PO_ITEM"] = $item_numlinePO;
                                                $fce->POITEMX->row["SHORT_TEXT"] = "X";
                                                $fce->POITEMX->row["MATERIAL"] = "X";
                                                $fce->POITEMX->row["PLANT"] = "X";
                                                
                                                $fce->POITEMX->row["STGE_LOC"] = "X";
    //                                    $fce->POITEMX->row["STGE_LOC"] = "X"; 
                                                $fce->POITEMX->row["MATL_GROUP "] = "X";
                                                $fce->POITEMX->row["QUANTITY"] = "X";
                                                $fce->POITEMX->row["STGE_LOC"] = "X";
                                                $fce->POITEMX->row["GR_IND"] = "X";
                                                $fce->POITEMX->row["IR_IND"] = "X";
                                                $fce->POITEMX->row["GR_BASEDIV"] = "X";
    //                                        $fce->POITEMX->row["FUNDS_CTR"] = "X";
                                                $fce->POITEMX->Append($fce->POITEMX->row);

                                                $fce->POTEXTITEM->row["PO_ITEM"] = $item_numlinePO;
                                                $fce->POTEXTITEM->row["TEXT_ID"] = "F01";
                                                $fce->POTEXTITEM->row["TEXT_FORM"] = "/";
                                                $fce->POTEXTITEM->row["TEXT_LINE"] = "SEMEN PPC";
                                                $fce->POTEXTITEM->Append($fce->POTEXTITEM->row);

                                                $fce->POSCHEDULE->row["PO_ITEM"] = $item_numlinePO;
                                                $fce->POSCHEDULE->row["SCHED_LINE"] = "0001";
                                                //$fce->POSCHEDULE->row["DELIVERY_DATE"] = $tgl_kirim;
                                                $fce->POSCHEDULE->row["DELIVERY_DATE"] = $po_deliv_date;
                                                $fce->POSCHEDULE->row["QUANTITY "] = $qty;
                                                $fce->POSCHEDULE->row["STAT_DATE"] = $tgl_kirim;
                                                $fce->POSCHEDULE->row["PO_DATE"] = $tgl_kirim;
                                                $fce->POSCHEDULE->Append($fce->POSCHEDULE->row);

                                                $fce->POSCHEDULEX->row["PO_ITEM"] = $item_numlinePO;
                                                $fce->POSCHEDULEX->row["SCHED_LINE"] = $schedule_line;
                                                $fce->POSCHEDULEX->row["DELIVERY_DATE"] = "X";
                                                $fce->POSCHEDULEX->row["QUANTITY "] = "X";
                                                $fce->POSCHEDULEX->row["STAT_DATE"] = "X";
                                                $fce->POSCHEDULEX->row["PO_DATE"] = "X";
                                                $fce->POSCHEDULEX->Append($fce->POSCHEDULEX->row);



                                                $fce->POCOND->row["ITM_NUMBER"] = $item_numlinePO;
                                                $fce->POCOND->row["COND_ST_NO"] = '001';
                                                $fce->POCOND->row["COND_TYPE"] = "PBXX";
                                                $fce->POCOND->row["COND_VALUE"] = $hargaPO;
                                                $fce->POCOND->row["COND_P_UNT"] = $perPO;
                                                $fce->POCOND->row["COND_UNIT"] = $satuanPO;
                                                $fce->POCOND->row["COND_UNIT_SO"] = $satuan2PO;
                                                $fce->POCOND->row["CURRENCY"] = $currencyPO;
                                                $fce->POCOND->row["CURRENCY_ISO"] = $currencyPO;
                                                $fce->POCOND->row["CHANGE_ID"] = "U";
                                                $fce->POCOND->Append($fce->POCOND->row);

                                                $fce->POCONDX->row["ITM_NUMBER"] = $item_numlinePO;
                                                $fce->POCONDX->row["COND_ST_NO"] = '001';
                                                $fce->POCONDX->row["COND_TYPE"] = "x";
                                                $fce->POCONDX->row["COND_VALUE"] = "x";
                                                $fce->POCONDX->row["COND_P_UNT"] = "x";
                                                $fce->POCONDX->row["COND_UNIT"] = "x";
                                                $fce->POCONDX->row["COND_UNIT_SO"] = "x";
                                                $fce->POCONDX->row["CURRENCY"] = "x";
                                                $fce->POCONDX->row["CURRENCY_ISO"] = "x";
                                                $fce->POCONDX->row["CHANGE_ID"] = "x";
                                                $fce->POCONDX->Append($fce->POCONDX->row);
                                        }

                                        $fce->Call();

                                        if ($fce->GetStatus() == SAPRFC_OK) {
                                            $nopoT = $fce->EXPPURCHASEORDER;
                                            $fce->RETURN->Reset();
                                            while ($fce->RETURN->Next()) {
                                                $tipe = $fce->RETURN->row["TYPE"];
                                                $msg = $fce->RETURN->row["MESSAGE"];
                                                $show_ket .= $msg;
                                                $show_ket .= '<br>';
                                                if ($tipe == 'A' || $tipe == 'X' || $tipe == 'E') {
                                                    $next = "N";
                                                }
                                            }
                                            //Commit Transaction
                                            $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                                            $fce->Call();
                                            sleep(2);
                                        }
                                        $user_org = $_SESSION['user_org'];

                                        if ($p == 1) {
                                            $PO1 = $nopoT;
                                        } else if ($p == 2) {
                                            $PO2 = $nopoT;
                                        } else if ($p == 3) {
                                            $PO3 = $nopoT;
                                        }
                                        //SIMPAN PP ICS KEDUA

                                        if ($next == "Y" && $p == 1) {
                                            $sampai = 1;
                                            for ($j = 0; $j <= $sampai - 1; $j++) {
                                                $idke = "idke" . $j;
    //                                            if (isset($_POST[$idke]) and $_POST[$idke] != "") {
                                                    $id_dtl = $datapp['TRANS_DTL'][$j]['ID'];
                                                    $item_num = $datapp['TRANS_DTL'][$j]['ITEM_NUMBER'];
                                                    // $item_num = $fungsi->linenum($item_num1);
                                                    $item_numline = $item_num * 10; //'000010'; 
                                                    $PO_LINE = sprintf("%05d", $item_numline); //'00010';
                                                    // if ($j == 0) {
                                                        $numline_pertama = sprintf("%06d", $item_numline * 10); //'000010';
                                                    // }
                                                    $alamat1 = $datapp['TRANS_DTL'][$j]['ALAMAT_SHIP_TO'];
    //                                        $material = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
    //                                        $material1 = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
                                                    $material = $datapp['TRANS_DTL'][$j]['KODE_PRODUK'];
                                                    $strmaterial = "SELECT * FROM ZMD_MAPPING_MATERIAL_ROYALTY WHERE ORG_MD = '{$sales_org1}' AND PLANT_MD = '{$plant1}' AND KD_MATERIAL_MD = '{$material}' AND DEL = 0";
                                                    $querymaterial = oci_parse($this->conn, $strmaterial);
                                                    oci_execute($querymaterial, 0);
                                                    $rowmaterial = oci_fetch_array($querymaterial, OCI_ASSOC);


                                                    if ($p == 1) {
                                                        $material = $rowmaterial['KD_MATERIAL_OPCO'];
                                                    } else if ($p == 2) {
                                                        $material = $rowmaterial['KD_MATERIAL_MD_2'];
                                                    } else if ($p == 3) {
                                                        $material = $rowmaterial['KD_MATERIAL_OPCO_2'];
                                                    }


                                                    $qty = $datapp['TRANS_DTL'][$j]['QTY_PP'];
                                                    $qtyx = $datapp['TRANS_DTL'][$j]['QTY_APPROVE'];


    //                                            if($sales_org=="7900"){ 
    //                                                $kontrak = $datapp['TRANS_DTL'][$j]['NO_KONTRAK'];
    //                                            }else{
    //                                                $kontrak = "";
    //                                            }
                                                    $kontrak = $datapp['TRANS_DTL'][$j]['NO_KONTRAK'];

                                                    $posnr = $datapp['TRANS_DTL'][$j]["ITEM_NUMBER"]*10;
                                                    $distrik = $datapp['TRANS_DTL'][$j]["KODE_TUJUAN"];
                                                    $shiptotocek = $datapp['TRANS_DTL'][$j]["SHIP_TO"];
                                                    
                                                    $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}'  AND DEL = 0 AND ORG_OPCO = '{$row['COM_OPCO']}'";
                                                    $queryshipto = oci_parse($this->conn, $strshipto);
                                                    oci_execute($queryshipto, 0);
                                                    $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                                                    if(!ISSET($rowshipto['SHIP_TO_OPCO'])){ 
                                                        $strshipto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '{$soldtocek}' AND SHIP_TO_MD = '{$shiptotocek}'  AND DEL = 0";
                                                        $queryshipto = oci_parse($this->conn, $strshipto);
                                                        oci_execute($queryshipto, 0);
                                                        $rowshipto = oci_fetch_array($queryshipto, OCI_ASSOC);
                                                    }

                                                    if ($p == 1) {
                                                        $shipto = $rowshipto['SHIP_TO_OPCO'];
                                                    } else if ($p == 2) {
                                                        $shipto = $rowshipto['SHIP_TO_MD_2'];
                                                    } else if ($p == 3) {
                                                        $shipto = $rowshipto['SHIP_TO_OPCO_2'];
                                                    }

                                                    $shipto = $this->fungsi->sapcode($shipto);
                                                    $tgl_terimaPO = $datapp['TRANS_DTL'][$j]["TGL_TERIMA"];
                                                    $tgl_kirimPO = $datapp['TRANS_DTL'][$j]["TGL_KIRIM_PP"];
                                                    list($day, $month, $year) = split("-", $tgl_kirimPO);
                                                    $tgl_kirim = $year . $month . $day;

                                                    $mtr_group = substr($material, 0, 7);


                                                    // if (($nomorso != "") and ( $qty1 == $qtyx1))
                                                    if (($nomorso != ""))
                                                        $status1 = "APPROVE";
                                                    else
                                                        $status1 = "PROCESS";

                                                    if ($p == 1) {
                                                        $SO_ICS = $SO1;
                                                    } else if ($p == 2) {
                                                        $SO_ICS = $SO2;
                                                    } else if ($p == 3) {
                                                        $SO_ICS = $SO3;
                                                    }

                                                    $field_names = array(
                                                        'NO_PP',
                                                        'NO_PO',
                                                        'PO_LINE',
                                                        'PLANT_PO',
                                                        'NMPLANT_PO',
                                                        'KOTA',
                                                        'NAMA_KOTA',
                                                        'KODE_PRODUK',
                                                        'NM_PRODUK',
                                                        'HARGA_PO',
                                                        'ORG_PO',
                                                        'VENDOR',
                                                        'NM_VENDOR',
                                                        'QTY_PO',
                                                        'UOM',
                                                        'TGL_PO',
                                                        'STATUS',
                                                        'NOTE',
                                                        'CREATE_DATE',
                                                        'CREATED_BY',
                                                        'DELETE_MARK',
                                                        'BULAN',
                                                        'TAHUN',
                                                        'QTY_APPROVE',
                                                        'TGL_PP',
                                                        'ORG_BY',
                                                        'VBELN'
                                                    );
                                                    $kd_vendor = $vendorFIX;
                                                    $nopoplus = $nopoT . $PO_LINE;
                                                    $field_data = array(
                                                        "$no_pp",
                                                        "$nopoplus",
                                                        "$PO_LINE",
                                                        "$plant1",
                                                        "$plant1", //belum                   
                                                        "$distrik",
                                                        "$distrik", //belum
                                                        "$material",
                                                        "$material", //belum
                                                        "$hargaPO",
                                                        "$sales_org1",
                                                        "$kd_vendor",
                                                        "$kd_vendor", //belum
                                                        "$qty",
                                                        "$po_unit",
                                                        "instgl_$tgl_kirimPO",
                                                        "$status1",
                                                        "$ket",
                                                        "SYSDATE",
                                                        "scheduler",
                                                        "0",
                                                        "$month",
                                                        "$year",
                                                        "$qtyx",
                                                        "SYSDATE",
                                                        "$user_org",
                                                        "$SO_ICS"
                                                    );
                                                    $tablename = "OR_TRANS_HDR_ICS";
                                                    $sukses = $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);

                                                    //Oracle Detail
                                                    $field_names2 = array(
                                                        'NO_PP',
                                                        'KODE_PRODUK',
                                                        'NAMA_PRODUK',
                                                        'QTY_PP',
                                                        'QTY_APPROVE',
                                                        'TGL_KIRIM_PP',
                                                        'TGL_KIRIM_APPROVE',
                                                        'TGL_TERIMA',
                                                        'SHIP_TO',
                                                        'NAMA_SHIP_TO',
                                                        'ALAMAT_SHIP_TO',
                                                        'DELETE_MARK',
                                                        'STATUS_LINE',
                                                        'KODE_TUJUAN',
                                                        'NAMA_TUJUAN',
                                                        'ITEM_NUMBER',
                                                        'NO_SO',
                                                        'FLAG_KAPAL',
                                                        'KETERANGAN',
                                                        'NAMA_KAPAL',
                                                        'NO_KONTRAK',
                                                        'KD_PROV',
                                                        'NM_PROV',
                                                        'UOM',
                                                        'PLANT',
                                                        'NM_PLANT',
                                                        'PRICE_DATE',
                                                        'SOLD_TO',
                                                        'INCOTERM',
                                                        'CARA_BAYAR',
                                                        'TERM_PAYMENT',
                                                        'NAMA_SOLD_TO',
                                                        'BPLANT',
                                                        'CREATED_BY',
                                                        'CREATE_DATE',
                                                        'NAMA_INCOTERM',
                                                        'SO_TYPE',
                                                        'NAMA_SO_TYPE',
                                                        'ROUTE',
                                                        'NAMA_TOP',
                                                        'ORG',
                                                        'PRICELIST',
                                                        'NAMA_PRICELIST',
                                                        'NO_KONTRAK_LC',
                                                        'KD_REASON',
                                                        'NM_REASON',
                                                        'ROUTE_TXT',
                                                        'CHANNEL',
                                                        'DIVISION',
                                                        'TIPE_PEMBELIAN',
                                                        'NO_POH'
                                                    );

                                                    $kodedistrik2 = substr($distrik, 0, 2);
                                                    $kd_provinsi = "10" . $kodedistrik2;
                                                    $itemnumplus = sprintf("%06d", $item_numline); //'000010';
                                                    $field_data2 = array(
                                                        "$no_pp",
                                                        "$material",
                                                        "$material",
                                                        "$qty",
                                                        "$qtyx",
                                                        "instgl_$tgl_kirimPO",
                                                        "instgl_$tgl_kirimPO",
                                                        "instgl_$tgl_terimaPO",
                                                        "$shipto",
                                                        "$shipto",
                                                        "$alamat1", // belum
                                                        "0",
                                                        "$status1",
                                                        "$distrik",
                                                        "$distrik",
                                                        "$itemnumplus",
                                                        "$SO_ICS",
                                                        "0",
                                                        "$ket",
                                                        " ", //belum
                                                        "$kontrak", //belum
                                                        "$kd_provinsi", //belum
                                                        "$kd_provinsi", //belum
                                                        "$po_unit", //belum
                                                        "$plant", // plant kedua
                                                        "$plant",
                                                        "instgl_$tgl_kirimPO",
                                                        "$soldto", //kedua
                                                        "$incoterm1",
                                                        "$ketbayar", //belum
                                                        "$top",
                                                        "$soldto",
                                                        "$plant", //
                                                        "scheduler",
                                                        "SYSDATE",
                                                        "$incoterm2",
                                                        "$so_type",
                                                        "$so_type",
                                                        "$route",
                                                        "$nama_top",
                                                        "$sales_org",
                                                        "$pricelist",
                                                        "$nama_pricelist",
                                                        "$lcnum",
                                                        "$reason",
                                                        "$nama_reason",
                                                        "$ship_cond-$route_desc", //belum
                                                        "$distr_chan",
                                                        "$division",
                                                        "D", //belum
                                                        "$nopoT"
                                                    );
                                                    $tablename2 = "OR_TRANS_DTL_ICS";
                                                    $this->fungsi->insert($this->conn, $field_names2, $field_data2, $tablename2);
    //                                            }
                                            }
                                        }
                                    }
                                }
                            }//if bukan SBI
                        } // IF
                    }// Perulangan PO
                }
            }
    
                //PROSES REFERENSI 2 SO, UNTUK MENGHUBUNGKAN 2 SO
                if ($next == 'Y') {
                    //  INSERT TABEL MAPPING
                    $fceM = $sap->NewFunction("ZCSD_MAP_SO_MD_FM");
                    if ($fceM == false) {
                        $sap->PrintStatus();
                        exit;
                    }
    
                    for ($ref = 1; $ref <= $perulanganSO; $ref++) {
                        $fce = $sap->NewFunction("Z_ZCSD_UPDATE_REF_SO");
                        if ($fce == false) {
                            $sap->PrintStatus();
                            exit;
                        }
                        if ($ref == 1) {
                            $fce->I_ZVBELN = $SO1;
                            $fce->I_VBELN = $SO2;
                            $SOM = 'SO Mega Distributor 1 : ' . $SO1;
                            $PO_REF = $PO1;
                            $SOMAP = $SO1;
                            $orgMAP = $sales_org1;
                            $plantMAP = $plant1;
                        } else if ($ref == 2) {
                            $fce->I_ZVBELN = $SO2;
                            $fce->I_VBELN = $SO1;
                            $SOM = 'SO OPCO 1 : ' . $SO2;
                            $PO_REF = $PO1;
                            $SOMAP = $SO2;
                            $orgMAP = $sales_org2;
                            $plantMAP = $plant2;
                        } else if ($ref == 3) {
                            $fce->I_ZVBELN = $SO3;
                            $fce->I_VBELN = $SO4;
                            $SOM = 'SO Mega Distributor 2 : ' . $SO3;
                            $PO_REF = ($SO3 != '' ? $PO2 : "");
                            $SOMAP = $SO3;
                            $orgMAP = $sales_org3;
                            $plantMAP = $plant3;
                        } else if ($ref == 4) {
                            if ($SO3 == "") {
                                $fce->I_ZVBELN = $SO3;
                                $fce->I_VBELN = $SO4;
                            } else {
                                $fce->I_ZVBELN = $SO4;
                                $fce->I_VBELN = $SO3;
                            }
                            //------------------------------------------------------------//
                            //soreff 3 layer si di add data so layer no 3 nya dengan po dan so layer 2 nya   
                            // tambah kondisi untuk ref po jika type PO SBI baca list mapping PO
                            if($COM=='3000' or $COM=='5000' or $COM=='4000' or $COM=='7000'){
                                $fce->I_ZVBELN = $SO2;
                                $fce->I_VBELN = $SO4; 
                                // $tgl_sekarang = date("Ymd");    
                                // $strkdven = "SELECT * FROM ZMD_MAPPING_PO WHERE PLANT = '$plantcek' and MATERIAL = '$material' and DISTRIK = '$distrik' and START_DATE < TO_DATE('$tgl_sekarang','YYYYMMDD') and END_DATE > TO_DATE('$tgl_sekarang','YYYYMMDD') ORDER BY ID DESC";  
                                // print_r($strkdven.' org nya '.$COM);    
                            }elseif($COM =='PTSC' || $COM =='ID50'){    
                                $tgl_sekarang = date("Ymd");    
                                $strkdven = "SELECT * FROM ZMD_MAPPING_PO WHERE PLANT = '$plantcek' and MATERIAL = '$material' and DISTRIK = '$distrik' and START_DATE < TO_DATE('$tgl_sekarang','YYYYMMDD') and END_DATE > TO_DATE('$tgl_sekarang','YYYYMMDD') ORDER BY ID DESC";  
                                // echo '<pre>';    
                                // print_r($strkdven);  
                                $querykdven = oci_parse($conn, $strkdven);  
                                oci_execute($querykdven,0); 
                                $rowkdven = oci_fetch_array($querykdven, OCI_ASSOC);    
                                // print_r($rowkdven);  
                                $PO2 = $rowkdven['PO'];
                                // $fce->I_ZVBELN = $SO2;
                                // $fce->I_VBELN = $SO4;	
                                // $PO_REF = $PO2;
                            } else if($COM=='1000'){
                                $fce->I_ZVBELN = $SO1;
                                $fce->I_VBELN = $SO4;
                            }
                            //-----------------------------------------------------//                            
                            $SOM = 'SO OPCO 2 : ' . $SO4;
                            $PO_REF = ($SO3 == '' ? $PO2 : $PO3);
                            $SOMAP = $SO4;
                            $orgMAP = $sales_org4;
                            $plantMAP = $plant4;


                            $this->so_reff_m_ICS($SO1,$PO2,$SO4);
                            // // so_reff($SO1,$PO2,$SO4);
                            // ////////////////////////////////////////////////so reff 3->1
                            // // function so_reff_smbr($SOMD,$PONUMBER,$SOSMBR){

                            //     //kondisi refference so ke 3 ke so MD

                            //     $SOMD99=$SO1;
                            //     $PONUMBER99=$PO2;
                            //     $SOSMBR99=$SO4;

                            //     $sap = new SAPConnection();
                            //     $sap->Connect("../include/sapclasses/logon_data.conf");
                            //     if ($sap->GetStatus() == SAPRFC_OK)
                            //         $sap->Open();
                            //     if ($sap->GetStatus() != SAPRFC_OK) {
                            //         //echo $sap->PrintStatus();
                            //         exit;
                            //     }
                                
                            //     $fce2 = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                            //     $fce2->SALESDOCUMENT = $SOMD99;//"ZOR";
                            //     $fce2->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                            //     $fce2->ORDER_HEADER_INX["PURCH_NO_S"]='X';
                            //     $fce2->ORDER_HEADER_INX["REF_1_S"]='X';                                                
                            
                            //     $fce2->ORDER_HEADER_IN["PURCH_NO_S"]= $PONUMBER99;
                            //     $fce2->ORDER_HEADER_IN["REF_1_S"]= $SOSMBR99;                                               
                            //     // $fce2->ORDER_HEADER_INX["ORD_REASON"] = '001';
                                
                                
                            //     //detail entri item
                            //     $fce2->ORDER_ITEM_INX->row["PURCH_NO_S"]='X';
                            //     $fce2->ORDER_ITEM_INX->row["REF_1_S"]='X';
                            //     $fce2->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';//"Z1";
                            //     $fce2->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                            //     $fce2->ORDER_ITEM_INX->Append($fce2->ORDER_ITEM_INX->row);
                            
                            //     $fce2->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                            //     $fce2->ORDER_ITEM_IN->row["PURCH_NO_S"] = $PONUMBER99;
                            //     $fce2->ORDER_ITEM_IN->row["REF_1_S"]= $SOSMBR99;
                            //     $fce2->ORDER_ITEM_IN->row["UPDATEFLAG"] = 'U';//"Z1";
                            //     $fce2->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                            //     $fce2->ORDER_ITEM_IN->Append($fce2->ORDER_ITEM_IN->row);
                            
                            //     $fce2->Call();
                                
                            //     if ($fce2->GetStatus() == SAPRFC_OK ) {
                            //         $fce2->RETURN->Reset();
                            //         while ($fce2->RETURN->Next()){
                            //         $error = $fce2->RETURN->row["TYPE"];
                            //         // $this->_data[] = $fce2->RETURN->row;
                            //         // $msg .= ' ';
                            //         $msg = $fce2->RETURN->row["MESSAGE"];
                            //         }
                            //         //Commit Transaction
                            //         $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                            //         $fce->Call();
                                    
                            //         $fce->Close();
                            
                            //     } else{
                            //         $fce2->PrintStatus();
                            //     }
                            //         $fce2->Close();
                            
                            //     return $msg;
                            

                            ////////////////////////////////////////////////
                        }
                        $fce->I_PO = $PO_REF;
                        // $fce->I_LINE_ITEM = $numline_pertama;
                        $fce->I_LINE_ITEM = '00010';
                        $fce->Call();
                        if ($fce->GetStatus() == SAPRFC_OK) {
                            //while ( $fce->ZMESSAGE->Next() ){
                            $tipe = $fce->ZMESSAGE["TYPE"];
                            $msg = $fce->ZMESSAGE["MESSAGE"];
                            $show_ket .= $SOM . ' ' . $msg . '<br>';
                            if($ref == 2){
                                if ($tipe != 'S') {
                                    $next = "N";
                                    // $jerror +=2;
                                }
                            }
                            //}
                        }
    
    
                        if ($SOMAP != "") {
                            $fceM->T_DATA->row["NO_PP"] = $no_pp;
                            $fceM->T_DATA->row["VKORG"] = $orgMAP;
                            $fceM->T_DATA->row["WERKS"] = $plantMAP;
                            $fceM->T_DATA->row["NO_SO"] = $SOMAP;
                            $fceM->T_DATA->row["SEQ_NO"] = $ref * 10;
                            $fceM->T_DATA->row["NO_PO"] = $PO_REF;
                            $fceM->T_DATA->Append($fceM->T_DATA->row);
                        }
                    }
    
    
                    $fceM->Call();
                    if ($fceM->GetStatus() == SAPRFC_OK) {
                        //while ( $fce->ZMESSAGE->Next() ){
    //                    $tipe = $fceM->ZMESSAGE["TYPE"];
    //                    $msg = $fceM->ZMESSAGE["MESSAGE"];
    //                    $show_ket .= $ref . ' ' . $msg . '<br>';
                        //}
                    }
                }
    //            echo "Jumlah error ".$jerror ." - ".$SO4;
                //PROSES DELETE SO JIKA PROSES SIMPAN SO ERROR KONDISI 'N'
                if ($next == "N") {
                    $SOSBI = "N";
                    $SOSMBRCEK = "N";
                    if ($SOMDNE == "Y") {
                        $SOSBIK = "Y";
                    } else {
                        $SOSBIK = "N";
                    }
                    for ($e = 1; $e <= $jerror; $e++) {
    
                        $return = "Y";
    
                        // CEK SO SBI ATAU TIDAK
                        $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$plant1}'";
                        $querykdven = oci_parse($this->conn, $strkdven);
                        oci_execute($querykdven, 0);
                        $rowkdven = oci_fetch_array($querykdven, OCI_ASSOC);
                        if ($e == 1) {
                            $noyo = $SO1;
                            $IDHU = $IDH1;
                            $SOSBICEK = "N";
                        } else if ($e == 2) {
                            $noyo = $SO2;
                            $IDHU = $IDH2;
                            if ($rowkdven["COM_OPCO"] == "PTSC" || $rowkdven["COM_OPCO"] == "ID50") {
                                $SOSBICEK = "Y";
                            } else if ($rowkdven["COM_OPCO"] == "1000"){
                                $SOSMBRCEK = "Y";
                            // } else if ($rowkdven["COM_OPCO"] == "1000" && $rowkdven["COM_OPCO_2"] == "3000"){
                            //     $SOSMBRCEK = "Y";
                            }else{
                                $SOSBICEK = "N";
                                $SOSMBRCEK = "N";
                            }
                        } else if ($e == 3) {
                            $noyo = $SO3;
                            $IDHU = $IDH3;
                            $SOSBICEK = "N";
                        } else if ($e == 4) {
                            $noyo = $SO4;
                            $IDHU = $IDH4;
                            if ($rowkdven["COM_OPCO_2"] == "PTSC" || $rowkdven["COM_OPCO_2"] == "ID50") {
                                $SOSBICEK = "Y";
                            } else if ($rowkdven["COM_OPCO_2"] == "1000"){
                                $SOSMBRCEK = "Y";
                            } else if ($rowkdven["COM_OPCO"] == "1000" && $rowkdven["COM_OPCO_2"] == "3000"){
                                $SOSMBRCEK = "Y";
                            }else{
                                $SOSBICEK = "N";
                                $SOSMBRCEK = "N";
                            }
                        }
    
                        if ($SOSBICEK == 'Y') {
                            if ($e == 2 || $e == 4) {
    
                                if ($e == 2) {
                                    $TGLend = $tgl_deleteSO2;
    //                                print_r("TGL DELETE". $TGLend);
                                } else if ($e == 4) {
                                    $TGLend = $tgl_deleteSO4;
    //                                print_r("TGL DELETE". $TGLend);
                                }
                                ///////////////////////////////////////////////////////////////////////////////////
                                //pengkondisian jika so kosong maka tidak menajalankan rollback SO SBI
                                if(($noyo!=null or $noyo!='') and ($TGLend!='' or $TGLend!=null)){
                                //////////////////////////////////////////////////////////////////////////////////
                                //$url = 'https://sip.solusibangunindonesia.com/APIMD/DeleteSalesOrder';
                                $url = 'https://integrasi-api.sig.id/apimd/deletesalesorder/dev'; // Prod Synxchrox
                                $data = array(
                                    'Token' => 'aSsMx7GV0HFGzlufM4DH',
                                    'SystemID' => 'QASSO', //'QASSO',
                                    'SAPSalesOrderNumber' => $noyo,
                                    'SAPCreatedDate' => $TGLend,
                                );
                                $options = array(
                                    'http' => array(
                                        'header' => "Content-type: application/json\r\n",
                                        'method' => 'POST',
                                        'content' => json_encode($data),
                                    )
                                );
    
                                $context = stream_context_create($options);
                                $result = file_get_contents($url, false, $context);
                                $response = json_decode($result);
                                $status = $response->Status;
                                $Message = $response->Message;
                                $Message_detail = isset($response->MessageDetail) ? $response->MessageDetail : $Message;
                                $param_send = json_encode($data);
                                $param_return = json_encode($response);
                                $field_names = array(
                                    'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
                                );
                                $field_data = array(
                                    "$param_send", "$param_return", "SCHEDULER_MD", "$no_pp", "$Message", "$Message_detail", "SYSDATE"
                                );
                                $tablename = "ZMD_LOG_SBI";
                                $sukses = $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
                                if ($status != '1') {
                                    $return = 'N';
                                }
                                ///////////////////////////////////
                                //logic pengujia insert zmd log sbi jika suskes hapus so opco
                                if ($status == '1') {
                                    $ZMD_LOG_SBI = 'Y';
                                }
                                ///////////////////////////////////
                                }
                                ///////////////////////////////////                                
                            }
                        } else if($SOSMBRCEK=='Y'){
                            $datadelparam["csmsnumber"]= (int) $no_pp;
    
                            $delete_so_smbr = $this->deleteso_smbr($datadelparam);
    
                            $data_delete_output = json_decode($delete_so_smbr, true);
    
                            // print_r($data_delete_output);
    
                            $status = $data_delete_output["cancelSoStatus"];
                            $Message = $data_delete_output["soMessage"];
                            $field_names = array('GROUP_LOG','REQUEST','RESPON','BY_LOG','LOG_DATE','TOKEN');
                            $field_data = array("1","$datarequestsmbr","$datareturnsmbr","SCHEDULER_DELETE_SMBR","SYSDATE","$token");
                            $tablename = "ZREPORT_LOG_SERVICE";
                            $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
                            if ($status != "200") {
                                $return = 'N';
                            }
                            $show_ket .= $Message;
    
                        }else {
                            $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                            if ($fce == false) {
                                $sap->PrintStatus();
                                exit;
                            }
    
                            //header entri    
                            $fce->SALESDOCUMENT = $noyo; //"ZOR";
                            $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'D'; //"Z1";
    
                            $fce->Call();
    
                            if ($fce->GetStatus() == SAPRFC_OK) {
                                $fce->RETURN->Reset();
                                while ($fce->RETURN->Next()) {
                                    $msg .= '<div align="center">';
                                    $error = $fce->RETURN->row["TYPE"];
                                    $msg .= $fce->RETURN->row["MESSAGE"];
    
                                    if ($error == 'A' || $error == 'X' || $error == 'E') {
                                        $return = 'N';
                                    }
                                }
                                $msg .= '<br></div>';
                                //Commit Transaction
                                $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                                $fce->Call();
                            } else {
                                $fce->PrintStatus();
                            }
                        }
    
                        if ($return == 'Y') {
                            $str = "UPDATE OR_TRANS_HDR SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='scheduler' WHERE ID='$IDHU' ";
                            $query = oci_parse($this->conn, $str);
                            oci_execute($query, 0);
                            $str = "UPDATE OR_TRANS_APP SET NO_SO = '', QTY_APPROVE=0, STATUS_LINE='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='scheduler' WHERE NO_SO='$noyo' ";
                            $query = oci_parse($this->conn, $str);
                            oci_execute($query, 0);
                            $str = "UPDATE OR_TRANS_DTL SET NO_SO = '',  STATUS_LINE='OPEN',LAST_UPDATED_BY='scheduler',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$noyo'";
                            $query = oci_parse($this->conn, $str);
                            oci_execute($query, 0);
                            $str = "UPDATE OR_TRANS_HDR_ICS SET STATUS='OPEN',LAST_UPDATE_DATE=SYSDATE,LAST_UPDATED_BY='scheduler' WHERE VBELN='$noyo'";
                            $query = oci_parse($this->conn, $str);
                            oci_execute($query, 0);
                            $str = "UPDATE OR_TRANS_DTL_ICS SET NO_SO = '',  STATUS_LINE='OPEN',LAST_UPDATED_BY='scheduler',LAST_UPDATE_DATE=SYSDATE WHERE NO_SO='$noyo'";
                            $query = oci_parse($this->conn, $str);
                            oci_execute($query, 0);
                            $show_ket .= 'Sales Order ' . $noyo . ' has been success roolback ';
                        } else {
                            $show_ket .= 'Sales Order ' . $noyo . ' has been failed roolback';
                        }
                    }
                }
                $fce->Close();
                $sap->Close();
            } else {
                $show_ket .= $komentarerror;
                print_r($show_ket);
            }
            
            
            $field_names = array(
                'SEND_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN_DETAIL', 'TGL'
            );
            ///////////////////////////////////////////
            //cek RB delete opco SBI
            if($ZMD_LOG_SBI=='Y'){
                $field_data = array(
                    "SI", "scheduler", "$no_pp", "CSMS $show_ket", "SYSDATE"
                );
            }
            else{
                $field_data = array(
                    "SI", "scheduler", "$no_pp", "$show_ket", "SYSDATE"
                );
            }
            //////////////////////////////////////////
            $tablename = "ZMD_LOG_SBI";
            $sukses = $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
            
            echo $show_ket;
            $this->msg .= $show_ket;
            // if (isset($_POST['lasthalaman'])) {
            //     $habis = "list_approve_pp.php";
            // } else {
            //     $habis = "approve_pp.php";
            // }

            if ($nomorso != "") {
                $no_pp = $datapp['NO_PP'];
                $sqlOrTrans =  "SELECT
                            hdr.ID AS ID_HDR,
                            dtl.ID AS ID_DTL,
                            hdr.*,
                            dtl.*,
                            z.NAME_KEY,
                            r.PALLET,
                            r.KVGR1,
                            TO_CHAR(dtl.TGL_KIRIM_PP, 'YYYY-MM-DD HH24:MI:SS') AS TGL_KIRIM_PP,
                            TO_CHAR(dtl.TGL_LEADTIME, 'YYYY-MM-DD HH24:MI:SS') AS TGL_LEADTIME,
                            TO_CHAR(dtl.APPROVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS APPROVE_DATE
                        FROM
                            OR_TRANS_HDR hdr
                        JOIN
                            OR_TRANS_DTL dtl ON
                            hdr.NO_PP = dtl.NO_PP
                        LEFT JOIN
                            ZREPORT_M_PRICE z ON hdr.TERM_PAYMENT = z.KEY
                        LEFT JOIN
                            RFC_Z_ZCSD_SHIPTO r ON dtl.SHIP_TO = r.KUNN2
                        WHERE hdr.NO_PP = '$no_pp'
                        ";
                $sqlOrTrans = oci_parse($this->conn, $sqlOrTrans);
                oci_execute($sqlOrTrans);
                $datapp=oci_fetch_assoc($sqlOrTrans);
                // get ORG Name
                $arrayOrg = $this->fungsi->arrayorg();
                $orgCode = $datapp['ORG'];
                $orgName = isset($arrayOrg[$orgCode]) ? $arrayOrg[$orgCode] : '';
                // convert format tanggal
                $tglKirim = $datapp['TGL_KIRIM_PP'];
                $tglPP = $datapp['TGL_PP'];
                $tglLeadtime = $datapp['TGL_LEADTIME'];
                $tglApprove = $datapp['APPROVE_DATE'];
                $tglKirim = $this->fungsi->tanggalSvOrder($tglKirim);
                $tglPP = $this->fungsi->tanggalSvOrder($tglPP);
                $tglLeadtime = $this->fungsi->tanggalSvOrder($tglLeadtime);
                $tglApprove = $this->fungsi->tanggalSvOrder($tglApprove);
                
                if ($datapp['SO_TYPE'] == 'ZFC') {
                    $deliveryBlock = "Z1";
                }

                $dataResult = array(
                    "DeliveryBlock" => $deliveryBlock,
                    "OrderReason" => "",
                    "ReasonForRejection" => "",
                    "alamatShipto" => trim($datapp['ALAMAT_SHIP_TO']),
                    "com" => $datapp['ORG'],
                    "com_name" => $orgName,
                    "descTop" => $datapp['NAME_KEY'],
                    "harga" => $datapp['HARGA'],
                    "incoterm" => $datapp['INCOTERM'],
                    "kodeDistributor" => $datapp['SOLD_TO'],
                    "kodeDistrik" => $datapp['KODE_TUJUAN'],
                    "kodeProduk" => $datapp['KODE_PRODUK'],
                    "kodeShipto" => $datapp['SHIP_TO'],
                    "lineSO" => "10",
                    "namaDistributor" => trim($datapp['NAMA_SOLD_TO']),
                    "namaDistrik" => $datapp['NAMA_TUJUAN'],
                    "namaKapal" => $datapp['NAMA_KAPAL'],
                    "namaProduk" => trim($datapp['NAMA_PRODUK']),
                    "namaShipto" => trim($datapp['NAMA_SHIP_TO']),
                    "noKontrak" => $datapp['NO_KONTRAK'],
                    "noPP" => $datapp['NO_PP'],
                    "pallet" => $datapp['PALLET'],
                    "plant" => $datapp['PLANT_ASAL'],
                    "price" => $datapp['PRICELIST'],
                    "qtyRelease" => "0",
                    "qtySisa" => $datapp['QTY_PP'],
                    "qtySo" => $datapp['QTY_PP'],
                    "salesOrder" => $datapp['NO_SO'],
                    "tanggalKirim" => $tglKirim,
                    "tanggalRDD" => $tglLeadtime,
                    "tanggalSO" => $tglApprove,
                    "tipeSO" => $datapp['SO_TYPE'],
                    "tipeTruk" => $datapp['KVGR1'],
                    "top" => $datapp['NAMA_TOP'],
                    "uom" => $datapp['UOM']
                );
                return $dataResult;
            } else {
                return false;
            }
    }

}

?>
