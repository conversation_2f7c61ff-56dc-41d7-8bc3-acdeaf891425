<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=4874;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
//    $orgIn= $user_org;
// }



 

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="report_tracking.php";



if(isset($_POST["cancelInv"]) && $_POST["cancelInv"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Apakah ada yakin ingin Cancel PPL invoice <? echo $_POST["nomer_invoice"] ; ?> ?
				<input type="hidden" value="<? echo $_POST["nomer_invoice"] ; ?>" name="nor_invoice">
			</div>
			<div class="alert alert-danger" role="alert">
				<label>Alasan Cancel PPL Invoice</label>
				<textarea class="form-control" name="cancel_inv_ket" rows="5" required></textarea>
			</div>
			<button type="submit" name="cancelInv" value="cancelInv" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Iya&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


if((isset($_POST["cancelInv"]) && $_POST["cancelInv"] == 'cancelInv')) {
	$nomer_invoice = $_POST["nor_invoice"];
	$keterangan_cancel = $_POST['cancel_inv_ket'];
	$sql_inv = "SELECT NO_BA, ID, WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$nomer_invoice' ";
	// echo $nomer_invoice;exit;
	$query_inv = oci_parse($conn, $sql_inv);
	oci_execute($query_inv);
	$row_inv = oci_fetch_assoc($query_inv);
	$no_ba = $row_inv[NO_BA];
	$id_trans = $row_inv[ID];
	$warna_plat = $row_inv[WARNA_PLAT];
	$status_ba = '45';
	$keter = 'Cancel PPL Admin Verifikasi ';
	
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	$action = "cancel_ppl_invoice";
	include ('formula.php'); 
	
}

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
// if (isset($_POST['vendor']))
// $vendor = $_POST['vendor'];

$tgl_invoice_start = $_POST['tgl_invoice_start'];
$tgl_invoice_end = $_POST['tgl_invoice_end'];
$no_invoice = $_POST['no_invoice']; 
$no_faktur_pajak = $_POST['no_faktur_pajak']; 

$currentPage="report_tracking.php";
$komen="";
if(isset($_POST['cari'])){
	if($vendor=="" and $tgl_invoice_start == "" and $tgl_invoice_end == "" and $no_invoice == "" and $no_faktur_pajak == ""){
		$sql= "SELECT DISTINCT EI.*, to_char(EI.TGL_INVOICE,'DD-MM-YYYY') as TGL_INVOICE1, ETH.NO_BA, ETH.WARNA_PLAT 
		FROM EX_INVOICE  EI
		join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
		WHERE EI.DELETE_MARK ='0' AND ETH.DELETE_MARK ='0' AND ETH.NO_BA IS NOT NULL AND ETH.ORG in ($user_org) AND EI.NO_INVOICE IS NOT NULL ORDER BY EI.ORG,EI.NO_VENDOR, EI.NO_INVOICE DESC";
	}else {
		$pakeor=0;
		$sql= "SELECT DISTINCT EI.*, to_char(TGL_INVOICE,'DD-MM-YYYY') as TGL_INVOICE1, ETH.NO_BA, ETH.WARNA_PLAT FROM EX_INVOICE  EI
		join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
		WHERE ";
		// if($vendor!=""){
		// $sql.=" ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
		$pakeor=1;
		$sql.="  EI.DELETE_MARK ='0' AND ETH.DELETE_MARK ='0' ";
		 
		if($no_invoice!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_INVOICE LIKE '$no_invoice' ";
			}else{
			$sql.=" EI.NO_INVOICE LIKE '$no_invoice' ";
			$pakeor=1;
			}
		}
		if($no_faktur_pajak!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			}else{
			$sql.=" EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			$pakeor=1;
			}
		}

		if($tgl_invoice_start!="" && $tgl_invoice_end!=""){
			if($pakeor==1){
				$sql.=" AND EI.TGL_INVOICE BETWEEN TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') AND TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
			}else{
				$sql.=" EI.TGL_INVOICE BETWEEN TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') AND TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
				$pakeor=1;
			}
		} else if($tgl_invoice_start!="" && $tgl_invoice_end==""){
			if($pakeor==1){
				$sql.=" AND EI.TGL_INVOICE >= TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') ";
			}else{
				$sql.=" EI.TGL_INVOICE >= TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') ";
				$pakeor=1;
			}
		} else if($tgl_invoice_start=="" && $tgl_invoice_end!=""){
			if($pakeor==1){
				$sql.=" AND EI.TGL_INVOICE <= TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
			}else{
				$sql.=" EI.TGL_INVOICE <= TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
				$pakeor=1;
			}
		}

		// if($tgl_invoice!=""){
		// 	if($pakeor==1){
		// 		$sql.=" AND TO_CHAR(EI.TGL_INVOICE, 'YYYY-MM-DD') = '$tgl_invoice' ";
		// 	}else{
		// 		$sql.=" TO_CHAR(EI.TGL_INVOICE, 'YYYY-MM-DD') = '$tgl_invoice' ";
		// 		$pakeor=1;
		// 	}
		// }

		$sql.=" AND ETH.ORG in ($user_org) AND ETH.NO_BA IS NOT NULL ORDER BY EI.ORG, EI.NO_INVOICE DESC";
	} 
	//  echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
        
        $sqlcek = "SELECT NO_INVOICE FROM KPI_TERIMA_INV_VENDOR A, KPI_TERIMA_ASSINGMENT B, TB_USER_BOOKING C WHERE C.ID = B.ASSING_TO AND B.NO_GROUP_VENDOR = A.NOGROUP_VENDOR
                    AND A.DEL = '0' AND C.ID = '$user_id' AND C.DELETE_MARK = '0' AND C.ASSIGN_TYPE = 'OPERATOR'";
        $querycek = oci_parse($conn, $sqlcek);
        oci_execute($querycek);
        $noinvfilter = array();
        while($datacek=oci_fetch_assoc($querycek)){
            array_push($noinvfilter, $datacek[NO_INVOICE]);
        }
        
        $sqlcek1 = "SELECT ASSIGN_TYPE FROM TB_USER_BOOKING WHERE ID = '$user_id' AND DELETE_MARK = '0'";
        $querycek1 = oci_parse($conn, $sqlcek1);
        oci_execute($querycek1);
        while($datacek1=oci_fetch_assoc($querycek1)){
            $jenisuser = $datacek1[ASSIGN_TYPE];
        }

	while($row=oci_fetch_array($query)){ 
                $com[]=$row[ORG]; 
                $no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
                $no_invoice_v[]=$row[NO_INVOICE];
                $no_invoice_in=$row[NO_INVOICE];
                $no_faktur_v[]=$row[NO_PAJAK_EX];
                $vendor_v[]=$row[NO_VENDOR];
                $nama_vendor_v[]=$row[NAMA_VENDOR];
                $no_pajak_ex_v[]=$row[NO_PAJAK_EX];
                $TGL_INVOICE_v[]=$row[TGL_INVOICE];
                // $tgl_ba_v[]=$row[TANGGAL_BA];
                $no_ba_v[]=$row[NO_BA];
                $status_v[]=$row[TRACKING_INV];  
				$no_ppl_v[]=$row[ACCOUNTING_DOC];
				$doc_number_v[]=$row[INV_DOC_NUMBER_CONV];
				$ket_ekspedisi_v[]=$row[KETERANGAN_EKSPEDISI];
				$warna_plat_v[]=$row[WARNA_PLAT];
				
				$sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE,  NAMA, NAMA_LENGKAP from EX_BA_INVOICE 
				JOIN TB_USER_BOOKING TUB ON TUB.ID = EX_BA_INVOICE.CREATED_BY
				where EX_BA_INVOICE.ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";
				//echo $sqlS;
				$query_s= @oci_parse($conn, $sqlS);
				@oci_execute($query_s);
				$row_s=@oci_fetch_array($query_s); 
				$nama_v[]=$row_s[NAMA_LENGKAP];
				$keterangan_v[]=$row_s[KOMENTAR_REJECT];
				//$status_v[]=$row_s[STATUS_BA_INVOICE];
				//$status_id =$row_s[STATUS_BA_INVOICE];
				
				$status_id = $row[TRACKING_INV];
				if($status_id==10){
					$status_name_v[]= "CREATE INVOICE";
				}else if($status_id==20){
					$status_name_v[]= "UPLOAD INVOICE";
				}else if($status_id==30){
					$status_name_v[]= "REVERSED";
				}else if($status_id==40){
					$status_name_v[]= "REJECTED";
				}else if($status_id==45){
					$status_name_v[]= "CANCEL PPL & INVOICE";
				}else if($status_id==50){
					$status_name_v[]= 'APPROVED BY SPV';
				}else if($status_id==60){
					$status_name_v[]= 'GENERATE PPL';
				}else if($status_id==70){
					$status_name_v[]= 'SIMULATE & POSTING PPL';
				}else if($status_id==80){
					$status_name_v[]= 'REJECT BY MANAJER VERIFIKASI';
				}else if($status_id==90){
					$status_name_v[]= 'APPROVED  BY MANAJER VERIFIKASI';
				}else if($status_id==100){
					$status_name_v[]= 'REJECT BY SM VERIFIKASI';
				}else if($status_id==110){
					$status_name_v[]= 'APPROVED  BY SM VERIFIKASI';
				}else if($status_id==120){
					$status_name_v[]= 'EKSPEDISI BENDAHARA';
				}else {
					$status_name_v[]= "";
				}
				
				
               
				
				 

                $sqlcek="SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE  NO_INVOICE = '$no_invoice_in' AND DELETE_MARK = '0' ";
                $querycek= oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data=oci_fetch_assoc($querycek);
                $total_klaim_in=$row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in=$row_data[TOTAL_KTG];
                $total_semen_in=$row_data[TOTAL_SEMEN];
                $total_pdpks_in=$row_data[TOTAL_PDPKS];
                $total_pdpkk_in=$row_data[TOTAL_PDPKK];

                if ($row[PAJAK_INV] > 0) 
                $pajak = 0.1*$row_data[TOTAL_KLAIM];
                else 
                $pajak = 0;


                $klaim_semen_v[]=$row_data[TOTAL_SEMEN];
                $klaim_ktg_v[]=$row_data[TOTAL_KTG];
                $pdpks_v[]=$row_data[TOTAL_PDPKS]; 
                $pend_ktg_v[]=$row_data[TOTAL_PDPKK]; 
                $total_klaim_v[]=$row_data[TOTAL_KLAIM];
                $pajak_v[]=$pajak;
                $status_dokumen[] = $row['STATUS_DOKUMEN'];
                // var_dump($row);
                         
	}
	$total=count($no_invoice_v);
	if ($total < 1){
		$komen = "Tidak Ada Data Yang Ditemukan";
	}

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Tracking Document Invoice </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Data Reporting </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Faktur Pajak</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_faktur_pajak" name="no_faktur_pajak" value="<?=$no_faktur_pajak?>"/></td>
    </tr>
	<tr width="174">
      <td class="puso">Tanggal Invoice</td>
      <td class="puso">:</td>
      <td>
		<input type="date" name="tgl_invoice_start" value="<?=$tgl_invoice_start?>"/>
		<span style="margin: 0 10px;">s/d</span>
		<input type="date" name="tgl_invoice_end" value="<?=$tgl_invoice_end?>"/>
	</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){

?>

<form name="export" method="post" action="report_tracking_xls.php">
	<input name="no_invoice" type="hidden" value="<?=$no_invoice?>"/>
	<input name="tgl_invoice_start" type="hidden" value="<?=$tgl_invoice_start?>"/>
	<input name="tgl_invoice_end" type="hidden" value="<?=$tgl_invoice_end?>"/>
	<input name="no_faktur_pajak" type="hidden" value="<?=$no_faktur_pajak?>"/>
	<!-- <input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	 -->
	&nbsp;&nbsp;

	<div style="text-align: right; padding: 10px 34px;">
		<input name="excel" type="Submit" id="excel" value="Export" /> 	
	</div>
</form>

<!--<form id="data_claim" name="data_claim" method="post" action="komentar.php" > -->

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Reporting </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td> 
		<td align="center"><strong >No BA Rekaputalasi </strong></td>
		<td align="center"><strong >No Invoice </strong></td>
		<td align="center"><strong >No PPL </strong></td>
		<td align="center"><strong >No Ekspedisi Dokumen </strong></td>
		<td align="center"><strong >No Faktur Pajak </strong></td>
		<!-- <td align="center"><strong >Total </strong></td> -->
		<td align="center"><strong >Warna Plat </strong></td>
		 <td align="center"><strong>Status </strong></td>
		 <td align="center"><strong>User </strong></td> 
		 <td align="center"><strong>Action </strong></td> 
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	
			
			
			$data = explode("disimpan", $ket_ekspedisi_v[$i]);

			$out = array();
			$step = 0;
			$last = count($data);
			$last--;

			foreach($data as $key=>$item){

			   foreach(explode(' ',$item) as $value){
				$out[$key][$step++] = $value;
			   }

			   if ($key!=$last){
				$out[$key][$step++] = ' '; // not inserting last "space"
			   }

			}
 
			

		?>     
		
		<td align="center"><? echo $i+1; ?></td>       
		<td align="center"><?  echo $no_ba_v[$i]; ?></td>
		<td align="center"><? if($no_invoice_v[$i]!=''){ ?>
			<a href="javascript:popUp('report_tracking_detail.php?no_invoice=<?=$no_invoice_v[$i]?>')" ><? echo $no_invoice_v[$i];?></a>
		<? }else{ ?>
			<?php echo $no_invoice_v[$i]; ?>
		<? } ?>
		</td>
		<td align="center"><? echo $no_ppl_v[$i]; ?></td> 
		<td align="center"><? if(substr($ket_ekspedisi_v[$i], 0,1) == 'D' ||substr($ket_ekspedisi_v[$i], 0,1) == 'N' ){
			echo substr($ket_ekspedisi_v[$i],14,10);
		}else{
			echo $ket_ekspedisi_v[$i];
		}  ?></td>  
		<td align="center"><? echo  $no_faktur_v[$i]; ?></td> 
		<!-- <td align="center"><? echo number_format($total_klaim_v[$i],0,",","."); ?></td> -->
		<td align="center"><? echo $warna_plat_v[$i]; ?></td> 
		<td align="center"><? echo $status_name_v[$i]; ?></td> 
		<td align="center"><? echo $nama_v[$i];; ?></td>  
		<td align="center"><? echo '<a href="detail_invoice_ba.php?no_ba='.$no_ba_v[$i].'" style="margin-right: 4px; cursor: pointer; padding: 2px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Detail</a>'; ?>
		</tr>
	  <? } ?>
		</tbody>

	  <tr class="quote">
		<td colspan="16" align="center">
		<a href="report_tracking.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		<!--</form>-->

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
