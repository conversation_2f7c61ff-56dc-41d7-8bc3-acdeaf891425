<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../pgr_sanitizer.php');

$_POST= sanitize_input($_POST, TRUE);

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=4870; //PROD
// $halaman_id=3197; DEV
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
// print_r($_SESSION);
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}




$page="lihat_ba_hdr_ex_report.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$no_ba = $_POST['no_ba'];
$status_value = $_POST['status'];
// echo $status_value;
// $no_invoice_expeditur = $_POST['no_invoice_expeditur'];

$currentPage="lihat_ba_hdr_ex_report.php";
$komen="";
if(isset($_POST['cari'])){
	
	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_ba == "" and $status_value == "0"){
		// $sql= "SELECT EX_BA.*, to_char(EX_BA.TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1, TB_USER_BOOKING.NAMA_LENGKAP  FROM EX_BA LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL WHERE EX_BA.DELETE_MARK ='0' AND EX_BA.ORG in ($user_org) AND EX_BA.NO_BA IS NOT NULL AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ORDER BY EX_BA.ID DESC";

		$sql= "SELECT
					EX_BA.ID,
					EX_BA.NO_BA,
					EX_BA.NO_VENDOR,
					EX_BA.TOTAL_INV,
					EX_BA.PAJAK_INV,
					EX_BA.NAMA_VENDOR,
					EX_BA.KLAIM_KTG,
					EX_BA.KLAIM_SEMEN,
					EX_BA.PDPKS,
					EX_BA.PDPKK,
					EX_BA.DELETE_MARK,
					EX_BA.ORG,
					EX_BA.TOTAL_INVOICE,
					EX_BA.TGL_BA,
					EX_BA.STATUS_BA,
					EX_BA.FILENAME,
					EX_BA.ALASAN_REJECT,
					EX_BA.ID_USER_APPROVAL,
					EX_BA.TIPE_ALASAN,
					TB_USER_BOOKING.NAMA_LENGKAP,
					COUNT(EX_TRANS_HDR.ID) AS JML_SPJ,
					SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
					SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
					SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
					SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
				  SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
				  SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
					to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
				FROM
					EX_BA
					JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
					LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
				WHERE EX_BA.DELETE_MARK = '0' 
					AND EX_BA.ORG IN ($user_org) 
					AND EX_BA.NO_BA IS NOT NULL 
					AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') 
				GROUP BY EX_BA.ID,
					EX_BA.NO_BA,
					EX_BA.NO_VENDOR,
					EX_BA.TOTAL_INV,
					EX_BA.PAJAK_INV,
					EX_BA.NAMA_VENDOR,
					EX_BA.KLAIM_KTG,
					EX_BA.KLAIM_SEMEN,
					EX_BA.PDPKS,
					EX_BA.PDPKK,
					EX_BA.DELETE_MARK,
					EX_BA.ORG,
					EX_BA.TOTAL_INVOICE,
					EX_BA.TGL_BA,
					EX_BA.STATUS_BA,
					EX_BA.FILENAME,
					EX_BA.ALASAN_REJECT,
					EX_BA.TIPE_ALASAN,
					TB_USER_BOOKING.NAMA_LENGKAP,
					EX_BA.ID_USER_APPROVAL
				ORDER BY
					EX_BA.ID DESC";
	}else {
	// START non-prepared statement
		// $pakeor=0;
		// $sql= "SELECT
		// 			EX_BA.ID,
		// 			EX_BA.NO_BA,
		// 			EX_BA.NO_VENDOR,
		// 			EX_BA.TOTAL_INV,
		// 			EX_BA.PAJAK_INV,
		// 			EX_BA.NAMA_VENDOR,
		// 			EX_BA.KLAIM_KTG,
		// 			EX_BA.KLAIM_SEMEN,
		// 			EX_BA.PDPKS,
		// 			EX_BA.PDPKK,
		// 			EX_BA.DELETE_MARK,
		// 			EX_BA.ORG,
		// 			EX_BA.TOTAL_INVOICE,
		// 			EX_BA.TGL_BA,
		// 			EX_BA.STATUS_BA,
		// 			EX_BA.FILENAME,
		// 			EX_BA.ALASAN_REJECT,
		// 			EX_BA.ID_USER_APPROVAL,
		// 			EX_BA.TIPE_ALASAN,
		// 			TB_USER_BOOKING.NAMA_LENGKAP,
		// 			COUNT(EX_TRANS_HDR.ID) AS JML_SPJ,
		// 			SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
		// 			SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
		// 			SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
		// 			SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
		// 			SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
		// 		  SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
		// 		  SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
		// 		  SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
		// 		  SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
		// 			to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
		// 		FROM
		// 			EX_BA
		// 			JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
		// 			LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
		// 		WHERE ";
		// if($vendor!=""){
		// $sql.=" EX_BA.NO_VENDOR LIKE '$vendor'";
		// $pakeor=1;
		// }
		// if($tanggal_mulai!="" or $tanggal_selesai!=""){

		// 	if ($tanggal_mulai=="")
		// 	$tanggal_mulai_sql = "01-01-1990";
		// 	else
		// 	$tanggal_mulai_sql = $tanggal_mulai;

		// 	if ($tanggal_selesai=="")
		// 	$tanggal_selesai_sql = "12-12-9999";
		// 	else
		// 	$tanggal_selesai_sql = $tanggal_selesai;

		// 	if($pakeor==1){
		// 	$sql.=" AND EX_BA.TGL_BA BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + (1-1/24/60/60)";
		// 	}else{
		// 	$sql.=" EX_BA.TGL_BA BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + (1-1/24/60/60)";
		// 	$pakeor=1;
		// 	}
		// }
		// if($no_ba!=""){
		// 	if($pakeor==1){
		// 	$sql.=" AND EX_BA.NO_BA LIKE '$no_ba' ";
		// 	}else{
		// 	$sql.=" EX_BA.NO_BA LIKE '$no_ba' ";
		// 	$pakeor=1;
		// 	}
		// }
		// if ($status_value == 10) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('10') ";
		// 	}
		// 	elseif ($status_value == 11) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('11') ";
		// 	}
		// 	elseif ($status_value == 20) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('20') ";
		// 	}
		// 	elseif ($status_value == 21) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('21') ";
		// 	}
		// 	elseif ($status_value == 1) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('1') ";
		// 	}
		// 	elseif ($status_value == 30) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('30') ";
		// 	}
		// 	elseif ($status_value == 40) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('40') ";
		// 	}
		// 	elseif ($status_value == 50) {
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('50') ";
		// 	}
		// 	else{
		// 		$sql.=" AND EX_BA.STATUS_BA IN ('10','20','11','21','1','30','40','50') ";
		// 	}

		// 	$sql.=" AND EX_BA.DELETE_MARK = '0' 
		// 			AND EX_BA.ORG IN ($user_org) 
		// 			AND EX_BA.NO_BA IS NOT NULL 
		// 		GROUP BY EX_BA.ID,
		// 			EX_BA.NO_BA,
		// 			EX_BA.NO_VENDOR,
		// 			EX_BA.TOTAL_INV,
		// 			EX_BA.PAJAK_INV,
		// 			EX_BA.NAMA_VENDOR,
		// 			EX_BA.KLAIM_KTG,
		// 			EX_BA.KLAIM_SEMEN,
		// 			EX_BA.PDPKS,
		// 			EX_BA.PDPKK,
		// 			EX_BA.DELETE_MARK,
		// 			EX_BA.ORG,
		// 			EX_BA.TOTAL_INVOICE,
		// 			EX_BA.TGL_BA,
		// 			EX_BA.STATUS_BA,
		// 			EX_BA.FILENAME,
		// 			EX_BA.ALASAN_REJECT,
		// 			EX_BA.TIPE_ALASAN,
		// 			TB_USER_BOOKING.NAMA_LENGKAP,
		// 			EX_BA.ID_USER_APPROVAL
		// 		ORDER BY
		// 			EX_BA.ID DESC";
		// $query= oci_parse($conn, $sql);
	// END non-prepared statement

	// START prepared statemenet
		$params = array();

		$sql = "SELECT
			EX_BA.ID,
			EX_BA.NO_BA,
			EX_BA.NO_VENDOR,
			EX_BA.TOTAL_INV,
			EX_BA.PAJAK_INV,
			EX_BA.NAMA_VENDOR,
			EX_BA.KLAIM_KTG,
			EX_BA.KLAIM_SEMEN,
			EX_BA.PDPKS,
			EX_BA.PDPKK,
			EX_BA.DELETE_MARK,
			EX_BA.ORG,
			EX_BA.TOTAL_INVOICE,
			EX_BA.TGL_BA,
			EX_BA.STATUS_BA,
			EX_BA.FILENAME,
			EX_BA.ALASAN_REJECT,
			EX_BA.ID_USER_APPROVAL,
			EX_BA.TIPE_ALASAN,
			TB_USER_BOOKING.NAMA_LENGKAP,
			COUNT(EX_TRANS_HDR.ID) AS JML_SPJ,
			SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
			SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
			SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
			SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
			SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
			SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
			SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
			SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
			SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
			TO_CHAR(EX_BA.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1
		FROM
			EX_BA
			JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
			LEFT JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA.ID_USER_APPROVAL
		WHERE 1=1";

		if ($user_org) {
			$sql .= " AND EX_BA.ORG = :org";
			$params[':org'] = $user_org;
		} else {
			// paksa query tidak hasilkan data jika org kosong
			$sql .= " AND 1=0";
		}

		// Vendor
		if ($vendor != "") {
			$sql .= " AND EX_BA.NO_VENDOR LIKE :vendor";
			$params[':vendor'] = $vendor;
		}

		// Tanggal mulai & selesai
		$tanggal_mulai_sql = (isset($tanggal_mulai) && is_valid_date($tanggal_mulai)) ? $tanggal_mulai : "01-01-1990";
		$tanggal_selesai_sql = (isset($tanggal_selesai) && is_valid_date($tanggal_selesai)) ? $tanggal_selesai : "12-12-9999";

		$sql .= " AND EX_BA.TGL_BA BETWEEN TO_DATE(:tanggal_mulai, 'DD-MM-YYYY') AND TO_DATE(:tanggal_selesai, 'DD-MM-YYYY') + (1-1/24/60/60)";
		$params[':tanggal_mulai'] = $tanggal_mulai_sql;
		$params[':tanggal_selesai'] = $tanggal_selesai_sql;

		// No BA
		if ($no_ba != "") {
			$sql .= " AND EX_BA.NO_BA LIKE :no_ba";
			$params[':no_ba'] = $no_ba;
		}

		// Status BA
		$allowed_statuses = array('10', '11', '20', '21', '1', '30', '40', '50');
		if (in_array((string)$status_value, $allowed_statuses)) {
			$sql .= " AND EX_BA.STATUS_BA = :status_ba";
			$params[':status_ba'] = $status_value;
		} else {
			$sql .= " AND EX_BA.STATUS_BA IN ('10','11','20','21','1','30','40','50')";
		}

		$sql .= " AND EX_BA.DELETE_MARK = '0'
				AND EX_BA.NO_BA IS NOT NULL
		GROUP BY
			EX_BA.ID,
			EX_BA.NO_BA,
			EX_BA.NO_VENDOR,
			EX_BA.TOTAL_INV,
			EX_BA.PAJAK_INV,
			EX_BA.NAMA_VENDOR,
			EX_BA.KLAIM_KTG,
			EX_BA.KLAIM_SEMEN,
			EX_BA.PDPKS,
			EX_BA.PDPKK,
			EX_BA.DELETE_MARK,
			EX_BA.ORG,
			EX_BA.TOTAL_INVOICE,
			EX_BA.TGL_BA,
			EX_BA.STATUS_BA,
			EX_BA.FILENAME,
			EX_BA.ALASAN_REJECT,
			EX_BA.TIPE_ALASAN,
			TB_USER_BOOKING.NAMA_LENGKAP,
			EX_BA.ID_USER_APPROVAL
		ORDER BY EX_BA.ID DESC";

		$query = oci_parse($conn, $sql);
		foreach ($params as $key => &$val) {
			oci_bind_by_name($query, $key, $val);
		}
	// END prepared statement
	}
	// echo $sql;
	oci_execute($query);
	$status = array();
	while($row=oci_fetch_array($query)){

		// $no_cek = $row[NO_BA];
		// $sql_cek= "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0'";

		// $query_cek= oci_parse($conn, $sql_cek);
		// oci_execute($query_cek);
		// $row_cek=oci_fetch_array($query_cek);
		//  // print_r($row_cek);
		// if($row_cek[HIT] == 0){
		// 	$sql_upd= "UPDATE EX_TRANS_HDR SET DELETE_MARK ='1' WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0'";
		// 	$query_upd= oci_parse($conn, $sql_cek);
		// 	oci_execute($query_upd);
		// }else{
		// print_r($row);
			// 
      $com[]=$row[ORG];
			$no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
			$no_ba_v[]=$row[NO_BA];
			$filename[]=$row[FILENAME];
			$id[]=$row[ID];
			$no_invoice_v[]=$row[NO_INVOICE];
			$no_invoice_ex_v[]=$row[NO_INVOICE_EX];
			$vendor_v[]=$row[NO_VENDOR];
			$nama_vendor_v[]=$row[NAMA_VENDOR];
			$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
			$tgl_invoice_v[]=$row[TGL_INVOICE];
			$tgl_ba_v[]=$row[TGL_BA];
			$klaim_semen_v[]=$row[KLAIM_SEMEN];
			$klaim_ktg_v[]=$row[KLAIM_KTG];
			$qty_ktg_rusak[]=$row[QTY_KTG_RUSAK];
			$qty_semen_rusak[]=$row[QTY_SEMEN_RUSAK];
			$qty_shp[]=$row[QTY_SHP];
			$total_ktg_rusak[]=$row[TOTAL_KTG_RUSAK];
			$total_ktg_rezak[]=$row[TOTAL_KTG_REZAK];
			$total_semen_rusak[]=$row[TOTAL_SEMEN_RUSAK];
			$total_klaim_ktg[]=$row[TOTAL_KLAIM_KTG];
			$total_klaim_semen[]=$row[TOTAL_KLAIM_SEMEN];
			$total_oa_v[]=$row[SHP_COST];
			$pdpks_v[]=$row[PDPKS]; 
			$pend_ktg_v[]=$row[PDPKK]; 
			$pajak_v[]=$row[PAJAK_INV];
			$total_spj_v[]=$row[JML_SPJ];
			$total_klaim_v[]=$row[TOTAL_INV];
			$nama_lengkap_v[]=$row[NAMA_LENGKAP];
      $no_baf[]=$row[NO_BAF];
      $status[]=$row[STATUS_BA];
      // $filename[]=$row[FILENAME];
      $alasan_reject[]=$row[ALASAN_REJECT];
      $tipe_alasan[]=$row[TIPE_ALASAN];
		// }
	}
	$total=count($no_ba_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: List BA :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
	<script src="../include/jquery.min.js"></script>
	<script src="../include/bootstrap/js/bootstrap.min.js"></script>
	<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar BA </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search BA </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No BASTP</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba?>"/></td>
    </tr>
    <!-- <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_expeditur?>"/></td>
    </tr> -->
    <tr>
      <td  class="puso">Periode BASTP </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>" />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
    <tr width="174">
      <td class="puso">Status</td>
      <td class="puso">:</td>
      <td><select name="status" id="Status">
      	<option value="0">Semua</option>
      	<option value="10">Open</option>
      	<option value="11">Rejected</option>
      	<option value="20">Submitted</option>
      	<!-- <option value="21">Revisi</option> -->
      	<option value="1">Reverse</option>
		<option value="30">Waiting Approval Pejabat Transportasi 1</option>
		<option value="40">Waiting Approval Pejabat Transportasi 2</option>
		<option value="50">Completed</option>
      </select></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){

?>
<form id="data_claim" name="data_claim" method="post" action="komentar.php" >

	<div align="center">
				<table width="95%" align="center" class="table table-responsive adminlist">
					<tr>
						<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BASTP </span></th>
					</tr>
				</table>
			</div>
			<div align="center">
				<table width="95%" align="center" class="table table-bordered table-responsive table-hover adminlist"
					id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td>
                 <td align="center"><strong >Org</strong></td>   
		 <!-- <td align="center"><strong >No Invoice </strong></td> -->
                 <td align="center"><strong >BASTP </strong></td>
		 <!-- <td align="center"><strong>No Invoice EX </strong></td> -->
		 <td align="center"><strong>Expeditur </strong></td>
		 <td align="center"><strong>Nama Expeditur </strong></td>
		 <!-- <td align="center"><strong>No Pajak EX </strong></td> -->
		 <td align="center"><strong>Tgl BASTP </strong></td>
		 <td align="center"><strong>Klaim Semen </strong></td>
		 <td align="center"><strong>Klaim Kantong</strong></td>
		 <td align="center"><strong>PDPKS</strong></td>
		 <td align="center"><strong>Pend. Ktg</strong></td>
		 <td align="center"><strong>Total OA</strong></td>
		 <!-- <td align="center"><strong>Pajak (PPN)</strong></td>
		 <td align="center"><strong>Total</strong></td> -->
		 <td align="center"><strong>User Approval</strong></td>
		 <td align="center"><strong>Status</strong></td>
		 <td align="center"><strong>Aksi</strong></td>
		 <td align="center"><strong>Cetak</strong></td>
		 <!-- <td align="center"><strong>Tipe Alasan Reject</strong></td> -->
		 <td align="center"><strong>Keterangan</strong></td>
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
                $orgCom="orgke".$i;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

		<td align="center"><? echo $b; ?></td>
                <td align="center"><? echo $com[$i]; ?><input name="<?=$orgCom;?>" id="<?=$orgCom;?>" type="hidden" value="<?=$com[$i];?>" /></td>
		<? 
		$no_cek=$no_ba_v[$i];
		$sql_print= "SELECT KODE_PRODUK FROM EX_TRANS_HDR WHERE NO_BA = '$no_cek' AND DELETE_MARK = '0' GROUP BY KODE_PRODUK ";
		$query_print= oci_parse($conn, $sql_print);
		oci_execute($query_print);
	
		$row_print=oci_fetch_array($query_print);
		$kode_produk=$row_print[KODE_PRODUK];
		
		$rest = substr($kode_produk, 0, -5);
		?>	
                <td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a></td>
                <!-- <td align="center"><? echo $no_invoice_ex_v[$i]; ?></td> -->
		<td align="center"><? echo $vendor_v[$i]; ?></td>
		<td align="center"><? echo $nama_vendor_v[$i]; ?></td>
		<!-- <td align="center"><? echo $no_pajak_ex_v[$i]; ?></td> -->
		<td align="center"><? echo $tgl_ba_v[$i]; ?></td>
		<!-- <td align="center"><? echo number_format($klaim_semen_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($klaim_ktg_v[$i],0,",","."); ?></td> -->
		<td align="center"><? echo number_format($qty_semen_rusak[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_ktg_rusak[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pdpks_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($pend_ktg_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_oa_v[$i],0,",","."); ?></td>		
		<!-- <td align="center"><? echo number_format($total_klaim_v[$i],0,",","."); ?></td> -->
		<!-- <td align="center"><? echo number_format($pajak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."); ?></td> -->
		<td align="center"><? echo $nama_lengkap_v[$i] ?></td>
		<td align="center"><? if ($status[$i] == '10' && $total_spj_v[$i] > 40 && $filename[$i] == '') {?>
			<a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BASTP yang belum ditandatangi</a><br>
			<a href="upload_ba.php?no_ba=<?=$id[$i]?>" class="btn btn-success btn-sm" style="height: 30px;">Upload File</a>
		<? }elseif($status[$i] == '10' && $total_spj_v[$i] > 40 && $filename[$i] != ''){?>
			Open
		<? }elseif($status[$i] == '10' && $total_spj_v[$i] <= 40){?>
			Open
			<!-- <a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BA yang belum ditandatangi</a><br>
			<a href="upload_ba.php?no_ba=<?=$id[$i]?>" class="btn btn-success btn-sm" style="height: 30px;">Upload File</a> -->
		<? }elseif($status[$i] == '11'){
				echo "Rejected";				
		}elseif($status[$i] == '20'){				
				echo "Submitted";
		}elseif($status[$i] == '21'){				
				echo "Revisi";
		}elseif($status[$i] == '1'){				
				echo "Reverse";
		}elseif($status[$i] == '30'){				
			echo "Waiting Approval Kasie";
		}elseif($status[$i] == '40'){				
			echo "Waiting Approval Kabiro";
		} elseif($status[$i] == '50'){				
			echo "Completed";
		}   ?></td>
      <!-- $tipe_alasan[]=$row[TIPE_ALASAN]; -->
      <!-- hanya role ekspeditur BA yang bisa reject BA -->
		 <td align="center"><? if ($status[$i] == '11') {
				?>
				<a href="reverse_ba.php?no_ba=<?=$no_ba_v[$i]?>" class="btn btn-danger btn-sm"
								style="height: 30px;font-size: 10px;">Reverse BA</a></td>
				<?php
		}else{
				?>
				-
				<?php
		} ?></td> 
		<td align="center"><? if ($status[$i] == '10') {
				?>
				<a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BASTP yang belum ditandatangi</a></td>
				<?php
		}elseif($status[$i] == '11' || $status[$i] == '1') {
				?>
				<!-- <a href="javascript:popUp('print_ba.php?no_ba=<?=$no_ba_v[$i]?>')">BA yang belum ditandatangi</a></td> -->
			-</td>
				<?php
		}else{
			?>
			<a href="javascript:popUp('upload/<?=$filename[$i]?>')">Download</a>
			<?php
		} ?></td>
		
		<!-- <td align="center"><? if ($status[$i] == '21' || $status[$i] == '30' || $status[$i] == '40' || $status[$i] == '50') {
			?>
				-</td>
				<?php
		} elseif ($tipe_alasan[$i] == '1') {
				?>
				SPJ Tidak Sesuai</td>
				<?php
		}elseif ($tipe_alasan[$i] == '2'){
				?>
				Upload SPJ</td>
				<?php
		}elseif ($tipe_alasan[$i] == '3'){
				?>
				Other</td>
				<?php
		}elseif ($tipe_alasan[$i] == '4'){
				?>
				Reverse BA</td>
				<?php
		}elseif($tipe_alasan[$i] == ''){
				?>
				-</td>
				<?php
		} ?></td> -->

		<td align="center"><? if ($alasan_reject[$i] == '' || $status[$i] == '21' || $status[$i] == '30' || $status[$i] == '40' || $status[$i] == '50') {
				?>
				-</td>
				<?php
		}else{
				?>
				<?=$alasan_reject[$i]?></td>
				<?php
		} ?></td>
		</tr>
	  <? } ?>
		</tbody>
	  <tr class="quote">
		<td colspan="2" align="center">
		<? $excel="CetakExcel_inv_hdr.php"; ?>
	<!-- 	<a href="<?php printf("%s?&tanggal_mulai=$tanggal_mulai&tanggal_selesai=$tanggal_selesai&no_invoice=$no_invoice&no_invoice_expeditur=$no_invoice_expeditur", $excel,$vendor,$tanggal_mulai,$tanggal_selesai,$no_invoice,$no_invoice_expeditur); ?>"class="button">EXCEL</a> -->
	</td>
		<td colspan="16" align="center">
		<a href="lihat_ba_hdr_ex_report.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
