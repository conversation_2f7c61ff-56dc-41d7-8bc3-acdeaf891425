<?php
session_start();
include_once '../helper.php';

$user = new User_SP();
$admin_portal = $user->get_admin_portal();
$user_id_admin = $admin_portal['ID'];

$user_id = $_SESSION['user_id'];
if(!$user_id){
    echo 'Harap login terlebih dahulu';
    exit;
};
if($user_id != $user_id_admin){
    echo 'Anda tidak berhak akses halaman ini';
    exit;
};

?>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Aplikasi SGG Online: Mapping User Approver</title>
    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.full.min.js"></script>
  <style>
    .container {
      max-width: 800px;
      margin: 50px auto;
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    h2 {
      text-align: center;
      margin-bottom: 20px;
    }

    .config-form {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .config-form input {
      flex: 1;
      padding: 10px;
    }

    .config-form button {
      padding: 10px 20px;
    }

    ul.config-list {
      list-style: none;
      padding: 0;
    }

    .config-item {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      margin-bottom: 10px;
      background: #f9f9f9;
      border-radius: 5px;
      transition: transform 0.3s ease-out;
    }

    .config-item input {
      flex: 1;
      padding: 5px;
    }

    .config-actions button {
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
    }

    .config-item button {
      background: green;
      color: white;
      border: none;
    }

    .config-item button.save {
      background: green;
    }

    .config-item button.cancel {
      background: grey;
    }

    button.delete {
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
      background: red;
      color: white;
      border: none;
    }

    button.add {
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
      background: green;
      color: white;
      border: none;
    }


    .config-value{
        display: none;
    }

    .config-item.deleting {
        transform: scale(0);
        opacity: 0;
    }

    #loading {
        position: fixed;
        top: 50%;
        left: 50%;
        font-weight: bold;
        transform: translate(-50%, -50%);
        background: #fff;
        padding: 10px 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    #success-notification {
        position: fixed;
        top: 50%;
        left: 50%;
        font-weight: bold;
        transform: translate(-50%, -50%);
        background: #4CAF50;
        padding: 10px 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        color: white;
    }

    #error-notification {
      position: fixed;
      top: 50%;
      left: 50%;
      font-weight: bold;
      transform: translate(-50%, -50%);
      background:rgb(198, 7, 7);
      padding: 10px 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
      color: white;
    }

    .dropdown {
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 6px;
      background-color: white;
      font-size: 14px;
      flex: 1;
      min-width: 120px;
      transition: border-color 0.3s ease;
    }

    .dropdown:focus {
      border-color: #007bff;
      outline: none;
      box-shadow: 0 0 5px rgba(0,123,255,0.3);
    }

    .empty-row td {
      text-align: center;
      padding: 20px;
      color: #888;
      font-style: italic;
      background-color: #fcfcfc;
    }

    .config-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 30px;
    font-family: Arial, sans-serif;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

  .config-table th,
  .config-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  .config-table thead {
    background-color: #f8f8f8;
  }

    .config-table tbody tr:hover {
        background-color: #f1f1f1;
    }

    #config-content{
        max-height: 350px;
        overflow: auto;
    }

    .select2-container {
        width: 300px !important;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>List User Approver</h2>

    <select id="user-dropdown" style="width: 300px;"></select>

    <button type="submit" class="add" onclick="handleClickAdd()">Add</button>

    <div id="config-content"></div>

  </div>

  <div id="success-notification" style="display: none; background: #4CAF50; color: white; padding: 10px; border-radius: 5px;"></div>

  <div id="error-notification" style="display: none; background:rgb(198, 7, 7); color: white; padding: 10px;border-radius: 5px;"></div>

  <div id="loading" style="display:none;">Loading...</div>
  
  <script src="../js/mapping_user_approver.js?v=<?= time() ?>"></script>
</body>
</html>
