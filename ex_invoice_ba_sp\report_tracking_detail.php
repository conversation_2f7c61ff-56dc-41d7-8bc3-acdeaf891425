<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$user_id=$_SESSION['user_id'];
$no_inv = $_GET['no_invoice'];
$user_name=$_SESSION['user_name'];
// echo $no_inv;
 
	$sqlchek = "select EB.*, to_char(EB.CREATED_AT,'DD-MM-YYYY') as TANGGAL1,  NAMA, NAMA_LENGKAP from EX_BA_INVOICE EB
	JOIN TB_USER_BOOKING TUB ON TUB.ID = EB.CREATED_BY
	WHERE EB.NO_INVOICE =  '".$no_inv."' ORDER BY  EB.ID DESC";
	//echo $sqlchek;
	$querycek = oci_parse($conn, $sqlchek);
	oci_execute($querycek);
	 
 
?>

<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../include/jquery.min.js"></script>
<script type="text/javascript">
	function checkstatus() {
		var tdok = $("#tdok").val();
		var datacek = 0;
		var tanya;
		$.each($('.cek:checked'), function(index, val) {
    		datacek++;
    	});
    	if (datacek == tdok) {
    		tanya = confirm('Dokumen Telah lengkap, apakah anda yakin untuk melanjutkan proses PPL ?');
    	}else{
    		tanya = confirm('Dokumen Tidak lengkap, apakah anda yakin untuk melanjutkan proses Pengembalian Ke vendor ?');
    	}
    	// alert(tanya);
    	return tanya;
	}
</script>
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<?php if ($_POST['save']): ?>
	<center>Data berhasil disimpan <br><input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" />	</center>
<?php else: ?>
<br /><br /> 
<table  class="adminlist" cellspacing="0" cellpadding="0" border="0" width="80%" align="center">
		  
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Tanggal</strong></td>
									<td align="center" style="width: 100px;"><strong>Status</strong></td>
									<td align="center" style="width: 120px;"><strong>User</strong></td>
									<td align="center" style="width: 200px;"><strong>Keterangan</strong></td> 
								</tr>
							</thead>
							
							<tbody id="isiLampiran">
								<? $b=0;  while ($datafunc = oci_fetch_assoc($querycek)) {
									$status_id = $datafunc['STATUS_BA_INVOICE'];
				
									if($status_id==10){
										$status_name_v= "CREATE INVOICE";
									}else if($status_id==20){
										$status_name_v= "UPLOAD INVOICE";
									}else if($status_id==30){
										$status_name_v= "REVERSED";
									}else if($status_id==40){
										$status_name_v= "REJECTED";
									}else if($status_id==45){
										$status_name_v= "CANCEL PPL & INVOICE";
									}else if($status_id==50){
										$status_name_v= 'APPROVED BY SPV';
									}else if($status_id==60){
										$status_name_v= 'GENERATE PPL';
									}else if($status_id==70){
										$status_name_v= 'SIMULATE & POSTING PPL';
									}else if($status_id==80){
										$status_name_v= 'REJECT BY MANAJER VERIFIKASI';
									}else if($status_id==90){
										$status_name_v= 'APPROVED  BY MANAJER VERIFIKASI';
									}else if($status_id==100){
										$status_name_v= 'REJECT BY SM VERIFIKASI';
									}else if($status_id==110){
										$status_name_v= 'APPROVED  BY SM VERIFIKASI';
									}else if($status_id==120){
										$status_name_v= 'EKSPEDISI BENDAHARA';
									}else {
										$status_name_v= "";
									}
									
									?>     
									<tr  class='row-file'>
									<td align="center"><? echo ++$b; ?></td>
									<td align="center"><? echo $datafunc['TANGGAL1']; ?></a></td>
									<td align="center"><? echo $status_name_v; ?></a></td>
									<td align="center"><? echo $datafunc['NAMA_LENGKAP']; ?></a></td>
									<td align="center"><? echo $datafunc['KOMENTAR_REJECT']; ?></a></td>
									</tr>
								<? } ?>
							</tbody>
				 
			 
		</table> 
<br /><br />
 
</div>
</div>
<p>&nbsp;</p>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<?php endif; ?>
</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>