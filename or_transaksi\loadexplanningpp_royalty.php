<?php

/*
 * cek planning dist harian
 */
include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$user_org_in=trim($_GET['orgin']);
$mp_coics=$fungsi->getComin($conn,$user_org_in);
if(count($mp_coics)>0){
    unset($ainorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $ainorg[$keyOrg]=$valorgm;
          $orgcounter++;
    }       
}else{
   $ainorg[$org]=$org;
}

//koneksi MARKETPLACE
$username_con3  = "marketplace";

//  $password_con3= "s3m3ngres1k"; //PASS PROD
//  $db3='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = sggdata3.semenindonesia.com)(PORT = 1521))) (CONNECT_DATA = (SID = SGG)(SERVER = DEDICATED)))';  //DATABASE PROD

// $password_con3  = "semengres1k"; //PASS DEV
// $db3            ='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))'; //DATABASE DEV

// $conn_3pl       = oci_connect($username_con3, $password_con3, $db3 , 'AL32UTF8');

$action_ca=trim($_GET['action']);
if ($action_ca == 'viewtop') {
    $kemasan = $_GET['jkemasan'];
    $distid = $_GET['distid'];
    $prop = $_GET['prop'];
    $route = $_GET['route'];
    $com = $_GET['com'];
    $shipto = $_GET['shipto'];
    $plant = $_GET['plant'];
    $so_type = $_GET['so_type'];
    $brand = $_GET['brand'];

    $sqlget0 = "select TOP from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
                AND (DISTRIBUTOR_ID = '".$distid."' OR DISTRIBUTOR_ID = 'all') 
                AND (KODE_PROP = '".$prop."' OR KODE_PROP = 'all') 
                AND (ROUTE = '".$route."' OR ROUTE = 'all')
                AND (BRAND = '".$brand."' OR BRAND = 'all')
                AND (PLANT_ID = '".$plant."')
                AND (SHIPTO_ID = '".$shipto."')
                AND ORG = '".$com."'
                AND DELETE_MARK = '0'
                AND (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO)
                ORDER BY DISTRIBUTOR_ID ASC,
                SHIPTO_ID ASC";
//    echo $sqlget0;
    $queryget0 = oci_parse($conn, $sqlget0);
    oci_execute($queryget0);
    $top = oci_fetch_assoc($queryget0);
    $top = @$top['TOP'];
    if(!$top){
        $sqlget2 = "select TOP from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
                AND (DISTRIBUTOR_ID = '".$distid."' OR DISTRIBUTOR_ID = 'all') 
                AND (KODE_PROP = '".$prop."' OR KODE_PROP = 'all') 
                AND (ROUTE = '".$route."' OR ROUTE = 'all')
                AND (BRAND = '".$brand."' OR BRAND = 'all')
                AND (PLANT_ID = '".$plant."' OR PLANT_ID = 'all')
                AND (SHIPTO_ID = '".$shipto."')
                AND ORG = '".$com."'
                AND DELETE_MARK = '0'
                AND (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO)
                ORDER BY DISTRIBUTOR_ID ASC,
                SHIPTO_ID ASC";
        
//    echo $sqlget2;
        $queryget2 = oci_parse($conn, $sqlget2);
        oci_execute($queryget2);
        $top = oci_fetch_assoc($queryget2);
        $top = @$top['TOP'];
        // tambahan terkait FOC
        if(!$top){
            $sqlget1 = "select TOP from (select * from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
                        AND (DISTRIBUTOR_ID = '".$distid."') 
                        AND (KODE_PROP = '".$prop."' OR KODE_PROP = 'all') 
                        AND (ROUTE = '".$route."' OR ROUTE = 'all')
                        AND (BRAND = '".$brand."' OR BRAND = 'all')
                        AND (SHIPTO_ID = '".$shipto."' OR SHIPTO_ID = 'all')
                        AND (PLANT_ID = '".$plant."' OR PLANT_ID = 'all')
                        AND ORG = '".$com."'
                        AND DELETE_MARK = '0'
                        AND (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO)) a 
                        where KODE_PROP <> 'all' or DISTRIBUTOR_ID <> 'all' or ROUTE <> 'all' or SHIPTO_ID <> 'all'
                        ORDER BY DISTRIBUTOR_ID ASC,
                        SHIPTO_ID ASC";
            
//    echo $sqlget1;
            $queryget1 = oci_parse($conn, $sqlget1);
            oci_execute($queryget1);
            $top = oci_fetch_assoc($queryget1);
            $top = @$top['TOP'];
        //
        if(!$top){
            $sqlget1 = "select TOP from (select * from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
                        AND (DISTRIBUTOR_ID = '".$distid."' OR DISTRIBUTOR_ID = 'all') 
                        AND (KODE_PROP = '".$prop."' OR KODE_PROP = 'all') 
                        AND (ROUTE = '".$route."' OR ROUTE = 'all')
                        AND (BRAND = '".$brand."' OR BRAND = 'all')
                        AND (SHIPTO_ID = '".$shipto."' OR SHIPTO_ID = 'all')
                        AND (PLANT_ID = '".$plant."' OR PLANT_ID = 'all')
                        AND ORG = '".$com."'
                        AND DELETE_MARK = '0'
                        AND (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO)) a 
                        where KODE_PROP <> 'all' or DISTRIBUTOR_ID <> 'all' or ROUTE <> 'all' or SHIPTO_ID <> 'all'
                        ORDER BY DISTRIBUTOR_ID ASC,
                        SHIPTO_ID ASC";
            
//echo $sqlget1;
//echo "<br>";
            $queryget1 = oci_parse($conn, $sqlget1);
            oci_execute($queryget1);
            $top = oci_fetch_assoc($queryget1);
            $top = @$top['TOP'];
            if (!$top) {
                $sqlget2 = "select TOP from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
                            AND DISTRIBUTOR_ID = 'all' 
                            AND KODE_PROP = 'all' 
                            AND ROUTE = 'all'
                            AND SHIPTO_ID = 'all'
                            AND BRAND = 'all'
                            AND ORG = '".$com."'
                            AND DELETE_MARK = '0'
                            AND (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO)
                            ORDER BY DISTRIBUTOR_ID ASC,
                            SHIPTO_ID ASC";
                
//    echo $sqlget2;
                $queryget2 = oci_parse($conn, $sqlget2);
                oci_execute($queryget2);
                $top = oci_fetch_assoc($queryget2);
                $top = @$top['TOP'];
            
                }
            }
        }
    }
    if($so_type=='ZFC'){ 
        echo "0001";
    }else{
        echo $top; 
    }
    die();
}
else if ($action_ca == 'viewtopcombo') {
    $kemasan = $_GET['jkemasan'];
    $distid = $_GET['distid'];
    $prop = $_GET['prop'];
    $route = strtolower($_GET['route']);
    $com = $_GET['com'];
    $shipto = $_GET['shipto'];
    $plant = $_GET['plant'];
    $so_type = $_GET['so_type'];
    $brand = $_GET['brand'];

//    $sqlget0 = "select TOP from OR_MAP_TOP_BRAND WHERE MATERIAL = '".$kemasan."' 
//                AND (DISTRIBUTOR_ID = '".$distid."' OR DISTRIBUTOR_ID = 'all') 
//                AND (KODE_PROP = '".$prop."' OR KODE_PROP = 'all') 
//                AND (ROUTE = '".$route."' OR ROUTE = 'all')
//                AND (PLANT_ID = '".$plant."')
//                AND (SHIPTO_ID = '".$shipto."')
//                AND ORG = '".$com."'
//                AND DELETE_MARK = '0'
//                ORDER BY DISTRIBUTOR_ID";
                // pengecekan apakah dia org 7900 atau bukan, di ambil dari plant
    $cekOrg = substr("{$plant}",0,2);
    if ($cekOrg == '79' || $cekOrg == 79) {
        $org = '7900';
    }else{
        $org = '7000';
    }

    $sqlget0 = "SELECT TOP from OR_MAP_TOP_BRAND WHERE 
                ORG = '{$org}' AND
                MATERIAL = '{$kemasan}' AND 
                (DISTRIBUTOR_ID = '{$distid}' OR DISTRIBUTOR_ID = 'all') AND
                (KODE_PROP = '{$prop}' OR KODE_PROP = 'all') AND
                (PLANT_ID = '{$plant}' OR PLANT_ID = 'all') AND
                (lower(ROUTE) = '{$route}' OR ROUTE = 'all') AND
                (SHIPTO_ID = '{$shipto}' OR SHIPTO_ID = 'all') AND
                (BRAND = '{$brand}' OR BRAND = 'all') AND
                (CURRENT_DATE BETWEEN VALID_FROM AND VALID_TO) AND
                DELETE_MARK = '0'
                GROUP BY TOP, DISTRIBUTOR_ID,
                        SHIPTO_ID
                ORDER BY
                        DISTRIBUTOR_ID ASC,
                        SHIPTO_ID ASC"; 

                



// echo $sqlget0;
    $queryget0 = oci_parse($conn, $sqlget0);
    oci_execute($queryget0); 
    if($so_type=='ZFC'){ 
//        echo "0001";
        
            echo '  <select name="top" id="top"  readonly onChange="document.tambah.nama_top.value=this.options[this.selectedIndex].title">';   
                                echo '<option value="0001" title="0001" selected>0001 - Cash</option>'; 
            echo '</select>';
    }else{
    //    $orgkey=trim($_SESSION['user_org']);
       $orgkey= $org;
                $sql_price= "select KEY from ZREPORT_M_PRICE 
                            where DELETE_MARK=0 and ORG='$orgkey' and TIPE=1
                            group by KEY
                            ";	
                //  echo $sql_price;            
                $query_price=oci_parse($conn,$sql_price);
                oci_execute($query_price);unset($dataprice);
                while ($price=oci_fetch_assoc($query_price)){
                    $dataprice[$price['KEY']] = $price['KEY'];
                }
                 
//                echo "<pre>";
//                print_r($dataprice);
//                echo "</pre>";
                
		$sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZCSD_SEL_TOP");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$top_tampung = array();
		$top_data['id'] = array();
		$top_data['nama'] = array();
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){			
                        if(count($dataprice)>0){
                            if($dataprice[$fce->RETURN_DATA->row["ZTERM"]]!=''){ 
                                $top_data['id']= $fce->RETURN_DATA->row["ZTERM"];
                                $top_data['nama']= $fce->RETURN_DATA->row["TEXT1"];
                            }
                        }else{     
                            $top_data['id']= $fce->RETURN_DATA->row["ZTERM"];
                            $top_data['nama']= $fce->RETURN_DATA->row["TEXT1"];
                        }
                        $top_tampung[] = $top_data;
                    }
    
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
    
        $nomer= 1; 
        $selected = '';
            echo '  <select name="top" id="top"  readonly onChange="document.tambah.nama_top.value=this.options[this.selectedIndex].title">
                        <option value="" title="">---Pilih TOP---</option>'; 
            
          
        while ($val=oci_fetch_array($queryget0)){  
            if($nomer==1){
                $selected = 'selected';
            }
            else{
                $selected = '';
            }
                foreach ($top_tampung as $t){
                  if($val['TOP']==$t['id']){
                      $nama = $t['nama'];
                  }
                }
                if($val['TOP']!='0001'){
                    echo '<option value="'.$val['TOP'].'" title="'.$nama.'" '.$selected.' >'.$val['TOP'].' - '.$nama.'</option>';
                }
            
            $nomer++;  
          }  
            echo '</select>';
    }
    die();
}else if($action_ca == 'cekharianbulanan'){
    $kode_plant_in = $_GET['plant'];
    $produk = $_GET['produkin'];
    if ($produk == '121-301') {
        $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE PLANT = '".$kode_plant_in."' AND DELETE_MARK = 0";
        $qplant = oci_parse($conn, $sqlplant);
        oci_execute($qplant);
        $rplant = oci_fetch_assoc($qplant);
        $planharian = $rplant['PLANT'];
        if (@$planharian) {
            echo 'harian';
        }else{
            echo 'bulanan';
        }
    }else{
        echo 'bulanan';
    }
    die();    
    
}else if($action_ca == 'cekkbongkar'){
    $cektipesemen = $_GET['tipe'];
    $qty = $_GET['qty'];
    $shipto = $_GET['shipto'];
    
    if($cektipesemen == 'CURAH'){
        $pengali = 25;
    }else{
        $pengali = 1;
    }
    
    // $str = "SELECT (UNLOADING_RATE_TON*25) as kbongkar FROM GUDANG_SIDIGI WHERE KD_GDG='$shipto'";
    // $query = oci_parse($conn_3pl, $str);
    // oci_execute($query);
    // $kbongkar=oci_fetch_array($query);
    // $var = $kbongkar['KBONGKAR'];
    $var = 8000;
    $var2 = $var/25;
    
    if($var != ''){
        if($var < ($qty*$pengali)){
            echo $var.' ZAK/ '.$var2.' TON';
        }else{
            echo '0';
        }
    } else {
        echo '0';
    }
    
    die();
}else if($action_ca == 'cekplant'){
    $cekplant = $_GET['plant'];
    $cekdistrik = $_GET['distrik'];
    $cektipesemen = $_GET['tipesemen'];
    if(count($cekdistrik) == 4){
        $cekdistrik = substr($cekdistrik,2,2);
    }
    if($cektipesemen == 'ZAK'){
        $cekproduk = '121-301';
    }else{
        $cekproduk = '121-302';
    }
    $sqlcekplant = "SELECT COUNT(ID) AS TOTAL FROM OR_MAP_PLANT WHERE PLANT = '$cekplant' AND TIPE_SEMEN = '$cekproduk' AND (DISTRIK = 'ALL' OR DISTRIK LIKE '$cekdistrik%')
    AND STATUS = '1' AND DELETE_MARK = 0";
    //echo $sqlcekplant;
    $qcekplant = oci_parse($conn, $sqlcekplant);
    oci_execute($qcekplant);
    $cekrplant = oci_fetch_assoc($qcekplant);
    $cekaktifplant = $cekrplant['TOTAL'];
    if (@$cekaktifplant > 0) {
        echo 'hold';
    }else{
        echo 'nohold';
    }
    die();
}else if($action_ca == 'cekshiptotop'){
    $com = $_GET['com'];
    $shipto = $_GET['shipto'];

    $sqlget0 = "select count(TOP) as TOP from OR_MAP_TOP_BRAND WHERE SHIPTO_ID = '".$shipto."' AND ORG = '".$com."' AND DELETE_MARK = '0'";
    $queryget0 = oci_parse($conn, $sqlget0);
    oci_execute($queryget0);
    $top = oci_fetch_assoc($queryget0);
    $top = @$top['TOP'];
    echo $top;
    die();
}

$sap = new SAPConnection();
$sap->Connect("../include/sapclasses/logon_data.conf");
if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
if ($sap->GetStatus() != SAPRFC_OK ) {
   echo $sap->PrintStatus();
   exit;
}

function setPlanningDist($conn,$org,$dist,$kota,$produk,$kode_plant_in,$qty,$tglkirim,$lelangflag,$applelangca,$tgl1nya_n,$action_ca) {
        
    //fungsi Cek main Brand atau fighting Brand
    if (($kota!='')&&($dist!='')&&($kode_plant_in!='')){
        $periode=date("Y-m-d H:i:s");
        $mapping ="SELECT STATUS FROM ZMD_MAPPING_BRAND WHERE
               PLANT='".$kode_plant_in."'and
               DISTRICT='".$kota."' and 
               DISTRIBUTOR='".$dist."' and
               to_char(PERIODE,'YYYYMM')='".date('Ym',strtotime($periode))."' and 
               del_mark = '0'";
        $qcekbrand = oci_parse($conn, $mapping);
        oci_execute($qcekbrand);
        $cekrbrand = oci_fetch_assoc($qcekbrand);
        $cekrbrand = @$cekrbrand['STATUS'];
//        echo $cekrbrand;
    }
    
    global $mp_coics;
    $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE PLANT = '".$kode_plant_in."' AND DELETE_MARK = 0";
    $qplant = oci_parse($conn, $sqlplant);
    oci_execute($qplant);
    $rplant = oci_fetch_assoc($qplant);
    $planharian = $rplant['PLANT'];
    // var_dump($planharian);
    if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
    }else{
       $inorg= $user_org;
    }
    $kdproduk=substr($produk,0,7);
    //tgl kirim
    list($day,$month,$year)=split("-",$tglkirim);
    $tgl_kirim=$year.$month.$day;
    $tglbackkemaren=date("Ymd", strtotime("$tgl_kirim -1 days"));
    $date_now=date("Ymd");
    $jam_now=date("H");
    
    list($day2,$month2,$year2)=split("-",$tgl1nya_n);
    $tgl1nya_n=$year2.$month2.$day2;
    
    unset($sqlUtama);unset($sqlchild);unset($blacklistdis);
    $querysoldto = '';
    
    //CEK SOLDTO GABUNGAN UNTUK PERHITUNGAN TARGET
    $tanggal_soldto = date("Ymd");
    
    if($tanggal_soldto > "20220531"){
        if($dist == '0000000265'){
            $dist_target = '0000000145';
        } else if(($dist == '0000000106')||($dist == '0000000108')){
            $dist_target = '0000000107';
        } else if(($dist == '0000000238')||($dist == '0000000240')||($dist == '0000000254')){
            $dist_target = '0000000239';
        } else if($dist == '0000037253'){
            $dist_target = '0000037045';
        } else {
            $dist_target = $dist;
        }
    } else {
        $dist_target = $dist;
    }
    
    //UNTUK PERHITUNGAN PENGURANGAN TARGET DIST GABUNGAN
    
    if($tanggal_soldto > "20220531"){
        if(($dist == '0000000265')||($dist == '0000000145')){
            $querysoldto =  " and SOLD_TO in ('0000000265','0000000145') "; 
        } else if(($dist == '0000000106')||($dist == '0000000108')||($dist == '0000000107')){
            $querysoldto =  " and SOLD_TO in ('0000000106','0000000108','0000000107') "; 
        } else if(($dist == '0000000238')||($dist == '0000000240')||($dist == '0000000254')||($dist == '0000000239')){
            $querysoldto =  " and SOLD_TO in ('0000000238','0000000240','0000000254','0000000239') "; 
        } else if(($dist == '0000037253')||($dist == '0000037045')){
            $querysoldto =  " and SOLD_TO in ('0000037045','0000037253') "; 
        } else {
            $querysoldto = '';
        }
    } else {
        $querysoldto = '';
    }
    
    
    if($dist!=''){
//        if($date_now==$tglbackkemaren && $jam_now>='09'){
    //        echo $tglbackkemaren;
        
        if($lelangflag!=''){
            $sqlchild .="";
            $sqlUtama .="";
            $blacklistdis=" and tbblack.SOLD_TO='$dist_target' ";
        }else{            
            $sqlUtama .=" and DISTRIBUTOR='$dist_target' ";
            if($querysoldto!=''){
                $sqlchild .=$querysoldto;
            } else {
                $sqlchild .=" and SOLD_TO='$dist_target' "; 
            }    
        }
    }
    
    if($kota!=''){
        if (in_array($produk, array('121-301', '121-701','121-800'))) {
            $sqlUtama .=" and DISTRIK='$kota' ";
        }else{
            $sqlUtama .=" and DISTRIK LIKE '".substr($kota, 2,2)."%'";
        }
    }

    
    if($kode_plant_in!=''){
        $sqlUtama .=" and PLANTSET='$kode_plant_in' ";
        $sqlchild .=" and PLANT_ASAL='$kode_plant_in' ";
    }
    
    if($applelangca!=''){
        $sqlchild .=" and (FLAG_LELANG is null or (FLAG_LELANG is not null and STATUS='APPROVE')) ";
    }else{
        if($lelangflag!='' && $dist_target!=''){
            $sqlchild .=" and (SOLD_TO='$dist_target' and FLAG_LELANG is not null) or FLAG_LELANG is null ";
        }else{
            $sqlchild .=" and FLAG_LELANG is null ";
        }
    }
    
    unset($filtgl);unset($filtgl2);
    if($tgl_kirim){
        if (in_array($produk, array('121-301', '121-701','121-800')) && $planharian) {
            $filtgl .=" and to_char(TANGGAL_TARGET,'YYYYMMDD')='$tgl_kirim' ";
            $filtgl2 .=" and to_char(TGL_KIRIM_PP,'YYYYMMDD')='$tgl_kirim'  ";
        }else{
            $filtgl .=" and to_char(TANGGAL_TARGET,'YYYYMM')='".date('Ym',strtotime($tgl_kirim))."'";
            $filtgl2 .=" and to_char(TGL_KIRIM_PP,'YYYYMM')='".date('Ym',strtotime($tgl_kirim))."'";
        }
    }
    if($tgl1nya_n){
        if (in_array($produk, array('121-301', '121-701','121-800')) && $planharian) {
            $filtgl .=" and to_char(TANGGAL_TARGET,'YYYYMMDD')>='$tgl1nya_n' ";
            $filtgl2 .=" and to_char(TGL_KIRIM_PP,'YYYYMMDD')>='$tgl1nya_n'  ";
        }else{
            $filtgl .=" and to_char(TANGGAL_TARGET,'YYYYMM')>='".date('Ym',strtotime($tgl1nya_n))."'";
            $filtgl2 .=" and to_char(TGL_KIRIM_PP,'YYYYMM')>='".date('Ym',strtotime($tgl1nya_n))."'";
        }
    }
    
    // if (mainbrand){      // kondisi tetap, hanya ditambah value target = target x porsi
    
    if(($cekrbrand == 'MB')||($cekrbrand == '')){

    if (in_array($produk, array('121-301', '121-701','121-800'))) {
        $sqlviw="          /* if */
            select tb4.*,nvl(tb5.QTY,0) as QTY,nvl(nvl(tb4.TARGET,0)-nvl(tb5.QTY,0),0) as SELISIH
            from (
              select ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET, sum(TARGET) as TARGET
              from (
                select *
                from (
                  select tballt.*,
                  case
                    when tbblack.ID is not null $blacklistdis then '1'
                    else '0'
                  end as ST_BLACK
                  from (
                    select * from (
                      select tb1.ORG,tb1.TIPE,tb1.DISTRIBUTOR,tb1.DISTRIK,tb1.TANGGAL_TARGET,to_char(tb1.TANGGAL_TARGET,'YYYYMMDD') as TGLTARGETF,
                        case
                          when PLANT is null then (
                              select PLANT 
                              from ZSD_MAPPING_PLANTKOTA
                              where DELETE_MARK=0 and DISTRIK=tb1.DISTRIK and ORG=tb1.ORG and rownum=1
                            )
                          else PLANT
                        end as PLANTSET,
                        tb1.TARGET AS TARGET
                      from ZSD_TARGET_HARIAN_NEW tb1 where AKTIF_MARK=0 and TARGET>0 
                        and ORG IN ($inorg) and TIPE='$kdproduk' $filtgl
                    )
                    where ORG IN ($inorg) $sqlUtama
                  ) tballt
                  left join ZSD_BLACKLISTDIST tbblack on (
                    tballt.ORG=tbblack.ORG  and 
                    tballt.DISTRIBUTOR=tbblack.SOLD_TO  and 
                    tbblack.AREA in (select KD_AREA from ZREPORT_M_KOTA where KD_KOTA=tballt.DISTRIK group by KD_AREA) and
                    to_char(tbblack.FROM_AKTIF,'YYYYMMDD')<= tballt.TGLTARGETF and
                    to_char(tbblack.TO_AKTIF,'YYYYMMDD')>= tballt.TGLTARGETF and
                    tbblack.DELETE_MARK=0 and tbblack.STATUS='APPROVE'
                  ) 
                )
                where ST_BLACK = 0
              ) tb group by ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET  
            ) tb4
            left join (
              select PLANT_ASAL,KODE_TUJUAN,SUM(QTY) as QTY from (
                select tb3.*,tb2.*,

/*
                  case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-0240','121-301-0180', '121-701-0002', '121-701-0006') then (QTY_PP*40)/1000
                    when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then (QTY_PP*50)/1000
                    else QTY_PP
                  end as QTY
 */

                /*   case 
                     when mat.MATNR is not null then (mat.NTGEW * QTY_PP)/1000 
                     else QTY_PP				 
                   end as QTY */
                CASE WHEN mat.MATNR IS NOT NULL AND REGEXP_LIKE(mat.NTGEW, '^\d+(\.\d+)?$') AND REGEXP_LIKE(QTY_PP, '^\d+(\.\d+)?$') THEN (mat.NTGEW * QTY_PP) / 1000 ELSE 0 END AS QTY
                
                from (
                  select PLANT_ASAL,NO_PP
                  from OR_TRANS_HDR 
                  where DELETE_MARK=0 and ORG IN ($inorg) and PLANT_ASAL is not null and NO_SO_OLD is null
                    and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                    $sqlchild
                ) tb3
                inner join (
                  select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY_PP) as QTY_PP
                  from (
                    select NO_PP,KODE_PRODUK,KODE_TUJUAN,PLANT,TGL_KIRIM_PP,
                      case when QTY_APPROVE is not null then QTY_APPROVE
                      else QTY_PP
                      end as QTY_PP
                    from OR_TRANS_DTL
                    where DELETE_MARK=0 and STATUS_LINE<>'REJECTED' $filtgl2
                      and KODE_PRODUK like '$kdproduk%'
                  )
                  group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP
                ) tb2 on(tb3.NO_PP=tb2.NO_PP)
                left join RFC_Z_ZCSD_LIST_MAT_SALES_2 mat on trim(mat.MATNR) = trim(tb2.KODE_PRODUK)
                    and mat.WERKS = tb3.PLANT_ASAL
              ) tb3
              group by PLANT_ASAL,KODE_TUJUAN
            ) tb5 on (tb4.PLANTSET=tb5.PLANT_ASAL and tb4.DISTRIK=tb5.KODE_TUJUAN)
        ";
        //  echo $sqlviw; //exit;
    }else{
        $sqldist = '';
        if($kota!=''){
            $sqldist =" and KODE_TUJUAN LIKE '".substr($kota, 2,2)."%'";
        }
        $sqlviw="          /* else */
            select tb4.*,nvl(tb5.QTY,0) as QTY,nvl(nvl(tb4.TARGET,0)-nvl(tb5.QTY,0),0) as SELISIH from(
              select ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET, sum(TARGET) as TARGET from (
                select * from(
                select tballt.*,
                case when tbblack.ID is not null $blacklistdis then '1'
                else '0'
                end as ST_BLACK
                from (
                select * from (
                select tb1.ORG,tb1.TIPE,tb1.DISTRIBUTOR,tb1.DISTRIK,tb1.TANGGAL_TARGET,to_char(tb1.TANGGAL_TARGET,'YYYYMMDD') as TGLTARGETF,
                case when PLANT is null then (select PLANT from ZSD_MAPPING_PLANTKOTA where DELETE_MARK=0 and DISTRIK=tb1.DISTRIK and ORG=tb1.ORG and rownum=1)
                else PLANT
                end as PLANTSET,
                tb1.TARGET AS TARGET
                from ZSD_TARGET_HARIAN_NEW tb1 where AKTIF_MARK=0 and TARGET>0 
                and ORG IN ($inorg) and TIPE='$kdproduk' $filtgl
                ) where ORG IN ($inorg) $sqlUtama
                )tballt left join ZSD_BLACKLISTDIST tbblack on(
                    tballt.ORG=tbblack.ORG  and 
                    tballt.DISTRIBUTOR=tbblack.SOLD_TO  and 
                    tbblack.AREA in (select KD_AREA from ZREPORT_M_KOTA where KD_KOTA=tballt.DISTRIK group by KD_AREA) and
                    to_char(tbblack.FROM_AKTIF,'YYYYMMDD')<= tballt.TGLTARGETF and
                    to_char(tbblack.TO_AKTIF,'YYYYMMDD')>= tballt.TGLTARGETF and
                    tbblack.DELETE_MARK=0 and tbblack.STATUS='APPROVE'
                ) 
                )where ST_BLACK=0
              )tb group by ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET  
            )tb4 left join (
                select PLANT_ASAL,SUBSTR(KODE_TUJUAN,1,2) AS KODE_TUJUAN,SUM(QTY) as QTY from (
                select tb3.*,tb2.*,
                case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-0240','121-301-0180') then (QTY_PP*40)/1000
                when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then (QTY_PP*50)/1000
                else QTY_PP
                end as QTY
                from (
                select PLANT_ASAL,NO_PP from OR_TRANS_HDR 
                where DELETE_MARK=0 and ORG IN ($inorg) and PLANT_ASAL is not null and NO_SO_OLD is null
                and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                $sqlchild
                )tb3 inner join (
                    select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY_PP) as QTY_PP from(
                    select NO_PP,KODE_PRODUK,KODE_TUJUAN,PLANT,TGL_KIRIM_PP,
                    case when QTY_APPROVE is not null then QTY_APPROVE
                    else QTY_PP
                    end as QTY_PP
                    from OR_TRANS_DTL where DELETE_MARK=0 and STATUS_LINE<>'REJECTED' $filtgl2
                    and KODE_PRODUK like '$kdproduk%' $sqldist
                    )group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP
                )tb2 on(tb3.NO_PP=tb2.NO_PP)
                )tb3
                group by PLANT_ASAL,SUBSTR(KODE_TUJUAN,1,2)
            )tb5 on (tb4.PLANTSET=tb5.PLANT_ASAL and SUBSTR(tb4.DISTRIK,1,2)=SUBSTR(tb5.KODE_TUJUAN,1,2))
           
        ";
    }
    
//    echo $sqlviw;
    
    $query2= oci_parse($conn, $sqlviw);
    oci_execute($query2);unset($data);
    while($row=oci_fetch_array($query2)){
            $data[]=$row;
    }
    return $data;
    
    } else {
        
        if (in_array($produk, array('121-301', '121-701','121-800'))) {
            
            if($tanggal_soldto > "20220531"){
                if(($dist == '0000000265')||($dist == '0000000145')){
                    $querysoldto =  " and SOLD_TO in ('0000000265','0000000145') "; 
                    $querydist = " and DISTRIBUTOR in ('0000000265','0000000145') ";
                } else if(($dist == '0000000106')||($dist == '0000000108')||($dist == '0000000107')){
                    $querysoldto =  " and SOLD_TO in ('0000000106','0000000108','0000000107') ";
                    $querydist = " and DISTRIBUTOR in ('0000000106','0000000108','0000000107') ";
                } else if(($dist == '0000000238')||($dist == '0000000240')||($dist == '0000000254')||($dist == '0000000239')){
                    $querysoldto =  " and SOLD_TO in ('0000000238','0000000240','0000000254','0000000239') "; 
                    $querydist = " and DISTRIBUTOR in ('0000000238','0000000240','0000000254','0000000239') ";
                } else if(($dist == '0000037253')||($dist == '0000037045')){
                    $querysoldto =  " and SOLD_TO in ('0000037045','0000037253') "; 
                    $querydist = " and DISTRIBUTOR in ('0000037045','0000037253') ";
                } else {
                    $querysoldto = " and SOLD_TO = '".$dist."' ";
                    $querydist = " and DISTRIBUTOR = '".$dist."' ";
                }
            } else {
                $querysoldto = " and SOLD_TO = '".$dist."' ";
                $querydist = " and DISTRIBUTOR = '".$dist."' ";
            }
            
            
     
        // } if(Fighting brand) {                           //jika kondisi fighting brand 
    // - Cek mapping mana saja plant main brand-nya ( where dist,kota,periode,status = MB )
    
        $getmapping ="SELECT PLANT FROM ZMD_MAPPING_BRAND WHERE
               DISTRICT='".$kota."' and 
               DISTRIBUTOR='".$dist."' and
               to_char(PERIODE,'YYYYMM')='".date('Ym',strtotime($tglkirim))."' and 
               del_mark = '0' and
               status = 'MB'";
        $qgetmap= oci_parse($conn, $getmapping);
        oci_execute($qgetmap);
        unset($datamb);$plantmb='';
        while($row=oci_fetch_array($qgetmap)){
                $inplant .= "'".$row['PLANT']."',";
        }
                $inplant= rtrim($inplant, ',');  
        
//        $iniplant =implode("','",$datamb['PLANT']);
//        echo "plant".$inplant;
//        echo "<pre>";
//        print_r($datamb);
//        echo "</pre>";
//        echo $getmapping;

    // - Cek sum nilai PP main brand 
    // select sum(qty_PP) from or_trans_dtl where plant in(SQL Plant main brand) and status <> 'reject' and dist = '' and distrik = '';
    
        $getqtyppmb = "SELECT
                        PLANT_ASAL,
                        KODE_TUJUAN,
                        SUM (QTY) AS QTY
                FROM
                        (
                                SELECT
                                        tb3.*, tb2.*, /*
                                  case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-0240','121-301-0180', '121-701-0002', '121-701-0006') then (QTY_PP*40)/1000
                                    when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then (QTY_PP*50)/1000
                                    else QTY_PP
                                  end as QTY
                 */
                                        CASE
                                WHEN mat.MATNR IS NOT NULL THEN
                                        (mat.NTGEW * QTY_PP) / 1000
                                ELSE
                                        QTY_PP
                                END AS QTY
                                FROM
                                        (
                                                SELECT
                                                        PLANT_ASAL,
                                                        NO_PP
                                                FROM
                                                        OR_TRANS_HDR
                                                WHERE
                                                        DELETE_MARK = 0
                                                AND ORG IN ($inorg)
                                                AND PLANT_ASAL IS NOT NULL
                                                AND NO_SO_OLD IS NULL
                                                AND (
                                                        TIPEPP <> 'NEW PROYEK'
                                                        OR TIPEPP IS NULL
                                                        OR (
                                                                TIPEPP = 'NEW PROYEK'
                                                                AND SO_TYPE = 'ZPR'
                                                                AND STATUS = 'APPROVE'
                                                        )
                                                ) 
                                                $querysoldto
                                                AND PLANT_ASAL in ($inplant)
                                        ) tb3
                                INNER JOIN (
                                        SELECT
                                                NO_PP,
                                                KODE_PRODUK,
                                                KODE_TUJUAN,
                                                TGL_KIRIM_PP,
                                                SUM (QTY_PP) AS QTY_PP
                                        FROM
                                                (
                                                        SELECT
                                                                NO_PP,
                                                                KODE_PRODUK,
                                                                KODE_TUJUAN,
                                                                PLANT,
                                                                TGL_KIRIM_PP,
                                                                CASE
                                                        WHEN QTY_APPROVE IS NOT NULL THEN
                                                                QTY_APPROVE
                                                        ELSE
                                                                QTY_PP
                                                        END AS QTY_PP
                                                        FROM
                                                                OR_TRANS_DTL
                                                        WHERE
                                                                DELETE_MARK = 0
                                                        AND KODE_TUJUAN = '".$kota."'
                                                        AND STATUS_LINE <> 'REJECTED' $filtgl2
                                                        AND KODE_PRODUK LIKE '$kdproduk%'
                                                )
                                        GROUP BY
                                                NO_PP,
                                                KODE_PRODUK,
                                                KODE_TUJUAN,
                                                TGL_KIRIM_PP
                                ) tb2 ON (tb3.NO_PP = tb2.NO_PP)
                                LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 mat ON TRIM (mat.MATNR) = TRIM (tb2.KODE_PRODUK)
                                AND mat.WERKS = tb3.PLANT_ASAL
                        ) tb3
                GROUP BY
                        PLANT_ASAL,
                        KODE_TUJUAN";
        
        $qgetqtyppmb= oci_parse($conn, $getqtyppmb);
        oci_execute($qgetqtyppmb);
        $datatotqty = 0;
        while($row1=oci_fetch_array($qgetqtyppmb)){
                $datatotqty +=$row1['QTY'];
        }
        
//        echo $getqtyppmb;
        
    // - Cek Prosentase Actual nilai PP Mainbrand dibanding target , hasilnya berupa prosentase
    // Select ((Cek sum nilai PP main brand) / target ) as prosentase_act from zsd_target where plant, dist,distrik,periode
        
        $sumtarget = "select sum(TARGET) as TARGET from ZSD_TARGET_HARIAN_NEW
                        where PLANT in ($inplant)
                        and DISTRIK = '".$kota."'
                        $querydist
                        and to_char(TANGGAL_TARGET,'YYYYMM')='".date('Ym',strtotime($tglkirim))."'
                        and AKTIF_MARK = '0'
                        and TIPE = '121-301'";
        
        $qsumtarget= oci_parse($conn, $sumtarget);
        oci_execute($qsumtarget);
        $datatottgt = 0;
        while($row2=oci_fetch_array($qsumtarget)){
                $datatottgt +=$row2['TARGET'];
        }
//        echo $sumtarget;
//                
        // - mengganti value target = target x ( Cek Prosentase Actual nilai PP Mainbrand dibanding target , hasilnya berupa prosentase)
        
        $sqltargetfb = "select TARGET from ZSD_TARGET_HARIAN_NEW
                        where PLANT = '".$kode_plant_in."'
                        and DISTRIK = '".$kota."'
                        $querydist
                        and to_char(TANGGAL_TARGET,'YYYYMM')='".date('Ym',strtotime($tglkirim))."'
                        and AKTIF_MARK = '0'
                        and TIPE = '121-301'";
        
        $qtargetfb= oci_parse($conn, $sqltargetfb);
        oci_execute($qtargetfb);
        $targetfb = 0;
        while($row3=oci_fetch_array($qtargetfb)){
                $targetfb +=$row3['TARGET'];
        }
        
//        echo 'tes'.$sqltargetfb;
        
        //total pp yang sudah dibuat oleh fighting brand
        
        $totppfb = "SELECT
                        PLANT_ASAL,
                        KODE_TUJUAN,
                        SUM (QTY) AS QTY
                FROM
                        (
                                SELECT
                                        tb3.*, tb2.*, /*
                                  case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-0240','121-301-0180', '121-701-0002', '121-701-0006') then (QTY_PP*40)/1000
                                    when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then (QTY_PP*50)/1000
                                    else QTY_PP
                                  end as QTY
                 */
                                        CASE
                                WHEN mat.MATNR IS NOT NULL THEN
                                        (mat.NTGEW * QTY_PP) / 1000
                                ELSE
                                        QTY_PP
                                END AS QTY
                                FROM
                                        (
                                                SELECT
                                                        PLANT_ASAL,
                                                        NO_PP
                                                FROM
                                                        OR_TRANS_HDR
                                                WHERE
                                                        DELETE_MARK = 0
                                                AND ORG IN ($inorg)
                                                AND PLANT_ASAL IS NOT NULL
                                                AND NO_SO_OLD IS NULL
                                                AND (
                                                        TIPEPP <> 'NEW PROYEK'
                                                        OR TIPEPP IS NULL
                                                        OR (
                                                                TIPEPP = 'NEW PROYEK'
                                                                AND SO_TYPE = 'ZPR'
                                                                AND STATUS = 'APPROVE'
                                                        )
                                                ) 
                                                $querysoldto
                                                AND PLANT_ASAL = '".$kode_plant_in."'
                                        ) tb3
                                INNER JOIN (
                                        SELECT
                                                NO_PP,
                                                KODE_PRODUK,
                                                KODE_TUJUAN,
                                                TGL_KIRIM_PP,
                                                SUM (QTY_PP) AS QTY_PP
                                        FROM
                                                (
                                                        SELECT
                                                                NO_PP,
                                                                KODE_PRODUK,
                                                                KODE_TUJUAN,
                                                                PLANT,
                                                                TGL_KIRIM_PP,
                                                                CASE
                                                        WHEN QTY_APPROVE IS NOT NULL THEN
                                                                QTY_APPROVE
                                                        ELSE
                                                                QTY_PP
                                                        END AS QTY_PP
                                                        FROM
                                                                OR_TRANS_DTL
                                                        WHERE
                                                                DELETE_MARK = 0
                                                        AND KODE_TUJUAN = '".$kota."'
                                                        AND STATUS_LINE <> 'REJECTED' $filtgl2
                                                        AND KODE_PRODUK LIKE '$kdproduk%'
                                                )
                                        GROUP BY
                                                NO_PP,
                                                KODE_PRODUK,
                                                KODE_TUJUAN,
                                                TGL_KIRIM_PP
                                ) tb2 ON (tb3.NO_PP = tb2.NO_PP)
                                LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 mat ON TRIM (mat.MATNR) = TRIM (tb2.KODE_PRODUK)
                                AND mat.WERKS = tb3.PLANT_ASAL
                        ) tb3
                GROUP BY
                        PLANT_ASAL,
                        KODE_TUJUAN";
        
        $qtotppfb= oci_parse($conn, $totppfb);
        oci_execute($qtotppfb);
        $jumppfb = 0;
        while($row4=oci_fetch_array($qtotppfb)){
                $jumppfb +=$row4['QTY'];
        }
//       echo ' tes '.$totppfb;
        
        //////////////////////////////////////////////////////
        //persentase fb
        $persenmb =  (($datatotqty/$datatottgt)*100);//ceil(($datatotqty/$datatottgt)*100);        
        
        $targetfbblmdikurangipp = (round(($persenmb/100) * $targetfb));
        if($targetfbblmdikurangipp>$targetfb){
            $targetfbblmdikurangipp=$targetfb;
            $endtgt = $targetfbblmdikurangipp-$jumppfb;
        } else {
            $endtgt = (round(($persenmb/100) * $targetfb))-$jumppfb;
        }
                  
        echo $endtgt;

//        echo "qty MB = ".$datatotqty." total target MB = ".$datatottgt." persentase nya = ".$persenmb. " persen target fb =".$targetfbblmdikurangipp." total qty pp nya ".$jumppfb." target fb ".$targetfb;
//        echo " hasil akir = ".$endtgt;
        
    
        } else {
            
            $sqldist = '';
            if($kota!=''){
                $sqldist =" and KODE_TUJUAN LIKE '".substr($kota, 2,2)."%'";
            }
            $sqlviw="          /* else */
                select tb4.*,nvl(tb5.QTY,0) as QTY,nvl(nvl(tb4.TARGET,0)-nvl(tb5.QTY,0),0) as SELISIH from(
                  select ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET, sum(TARGET) as TARGET from (
                    select * from(
                    select tballt.*,
                    case when tbblack.ID is not null $blacklistdis then '1'
                    else '0'
                    end as ST_BLACK
                    from (
                    select * from (
                    select tb1.ORG,tb1.TIPE,tb1.DISTRIBUTOR,tb1.DISTRIK,tb1.TANGGAL_TARGET,to_char(tb1.TANGGAL_TARGET,'YYYYMMDD') as TGLTARGETF,
                    case when PLANT is null then (select PLANT from ZSD_MAPPING_PLANTKOTA where DELETE_MARK=0 and DISTRIK=tb1.DISTRIK and ORG=tb1.ORG and rownum=1)
                    else PLANT
                    end as PLANTSET,
                    tb1.TARGET AS TARGET
                    from ZSD_TARGET_HARIAN_NEW tb1 where AKTIF_MARK=0 and TARGET>0 
                    and ORG IN ($inorg) and TIPE='$kdproduk' $filtgl
                    ) where ORG IN ($inorg) $sqlUtama
                    )tballt left join ZSD_BLACKLISTDIST tbblack on(
                        tballt.ORG=tbblack.ORG  and 
                        tballt.DISTRIBUTOR=tbblack.SOLD_TO  and 
                        tbblack.AREA in (select KD_AREA from ZREPORT_M_KOTA where KD_KOTA=tballt.DISTRIK group by KD_AREA) and
                        to_char(tbblack.FROM_AKTIF,'YYYYMMDD')<= tballt.TGLTARGETF and
                        to_char(tbblack.TO_AKTIF,'YYYYMMDD')>= tballt.TGLTARGETF and
                        tbblack.DELETE_MARK=0 and tbblack.STATUS='APPROVE'
                    ) 
                    )where ST_BLACK=0
                  )tb group by ORG,TIPE,DISTRIK,PLANTSET,TANGGAL_TARGET  
                )tb4 left join (
                    select PLANT_ASAL,SUBSTR(KODE_TUJUAN,1,2) AS KODE_TUJUAN,SUM(QTY) as QTY from (
                    select tb3.*,tb2.*,
                    case when KODE_PRODUK in ('121-301-0110','121-301-0050','121-301-0240','121-301-0180') then (QTY_PP*40)/1000
                    when KODE_PRODUK in ( '121-301-0020','121-301-0060','121-301-0056') then (QTY_PP*50)/1000
                    else QTY_PP
                    end as QTY
                    from (
                    select PLANT_ASAL,NO_PP from OR_TRANS_HDR 
                    where DELETE_MARK=0 and ORG IN ($inorg) and PLANT_ASAL is not null and NO_SO_OLD is null
                    and (TIPEPP<>'NEW PROYEK' or TIPEPP is null or (TIPEPP='NEW PROYEK' and SO_TYPE='ZPR' and STATUS='APPROVE'))
                    $sqlchild
                    )tb3 inner join (
                        select NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM(QTY_PP) as QTY_PP from(
                        select NO_PP,KODE_PRODUK,KODE_TUJUAN,PLANT,TGL_KIRIM_PP,
                        case when QTY_APPROVE is not null then QTY_APPROVE
                        else QTY_PP
                        end as QTY_PP
                        from OR_TRANS_DTL where DELETE_MARK=0 and STATUS_LINE<>'REJECTED' $filtgl2
                        and KODE_PRODUK like '$kdproduk%' $sqldist
                        )group by NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP
                    )tb2 on(tb3.NO_PP=tb2.NO_PP)
                    )tb3
                    group by PLANT_ASAL,SUBSTR(KODE_TUJUAN,1,2)
                )tb5 on (tb4.PLANTSET=tb5.PLANT_ASAL and SUBSTR(tb4.DISTRIK,1,2)=SUBSTR(tb5.KODE_TUJUAN,1,2))

            ";
            
            $query2= oci_parse($conn, $sqlviw);
            oci_execute($query2);unset($data);
            while($row=oci_fetch_array($query2)){
                    $data[]=$row;
            }
            return $data;
            
            
        }

        
        
    }
}

$sold_to_in=trim($_GET['soldin']);
$kode_distrik_in=trim($_GET['kodedistrik']);
$produk_in=trim($_GET['produkin']);
$plant=trim($_GET['plant']);
$qty_in=trim($_GET['qty_in']);
$tgl_kirim_in=trim($_GET['tglkirim']);
$tglkontrak=trim($_GET['tglkontrak']);
$tgl1nya_in=trim($_GET['tgl1nya']);
$lelangflagca=trim($_GET['flaglelang']);
$approvelelangca=trim($_GET['approvelelang']); 
$plant_select=trim($_GET['plant_select']);
$loadtype = trim($_GET['loadtype']);


//echo "<pre>";
//print_r($cekdata);
//echo "</pre>";
switch ($action_ca) {
    case "viewplant2":
        $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
        $jmlkota=count($cekdata);
            unset($plantarray);
            echo '  <select name="plant" id="plant" onChange="loadKota()">
                        <option value="" title="">---Pilih Plant---</option>
                        ';
                        if($jmlkota>0){
                            $plan_in = array();
                            foreach($cekdata as $key => $valpla){
                                array_push($plan_in, "'".$valpla['PLANTSET']."'");              
                                    $plantarray[$valpla['PLANTSET']]=$valpla['PLANTSET'];               
                            }

                            $plantSAP = array();
                            //oracle function
                            if(count($ainorg)>0){
                                foreach ($ainorg as $keyOrg2 => $valorgm2){
                                    $sql = "SELECT * FROM RFC_Z_ZAPP_SELECT_SYSPLAN WHERE XPARAM = '".$keyOrg2."'";
                                    if (count($plan_in) > 0) {
                                        $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                                        $sql .= "WERKS IN ('".implode($plantarray, "','")."')";
                                    }
                                    // echo $sql;
                                    $query= @oci_parse($conn, $sql);
                                    @oci_execute($query);
                                    while($datafunc=@oci_fetch_assoc($query)){
                                        $plantSAP[$datafunc["WERKS"]]=$datafunc["NAME1"];
                                    }
                                }
                            } 
                            
                            if($user_org_in == '7900'){
                            $sqlzmdplant = "SELECT PLANT_MD FROM ZMD_MAPPING_PLANT WHERE DEL = 0";
                            $queryzmdplant= @oci_parse($conn, $sqlzmdplant);
                            @oci_execute($queryzmdplant);
                            $plantMD = array();
                            while($datazmdplant=@oci_fetch_assoc($queryzmdplant)){
                                $plantMD[]=$datazmdplant["PLANT_MD"];
                            }
                            
                            foreach ($plantSAP as $keyplant => $valueplant) {
                                // echo '<option value="'.$keyzmdplant.'" title="'.$keyzmdplant.'">'.$keyzmdplant.' '.$keyzmdplant.'</option>';
//                                if ((array_search($keyplant, $plantMD) == '')) {
//                                    unset($plantSAP[$keyplant]);
//                                }
    
                                if (in_array($keyplant, $plantMD)){ 
                                    echo '<option value="'.$keyplant.'" title="'.$valueplant.'">'.$keyplant.' '.$valueplant.'</option>'; 
                                }
                                
                                
                             }
                            }else{
                            foreach($plantSAP as $kyPla => $valsap){
                                echo '<option value="'.$kyPla.'" title="'.$valsap.'">'.$kyPla.' '.$valsap.'</option>';
                              } 
                            }
                        }
                echo  '</select>';
                
//                            print_r($plantSAP);
//                            echo "<br />";
//                            print_r($plantMD);
    break;


    case "cekShiptoEx";
       $shipto = trim($_GET['shipto']);
        $soldto = trim($_GET['soldto']);
        $brand = trim($_GET['brand']);
        $kd_material = trim($_GET['kd_material']);
        $cekShipto = "SELECT MATERIAL,
                            PLANT 
                        FROM
                            EX_MAPPING_SHIPTO 
                        WHERE
                        SHIPTO = '$shipto' 
                        AND SOLDTO = '$soldto' 
                        AND FLAG_DEL != 'Y' 
                        AND BRAND = '$brand' 
                            --AND MATERIAL = '$kd_material'
                            AND ROWNUM <= 1
                            GROUP BY MATERIAL, PLANT
        ";
        

            $qshipto = oci_parse($conn, $cekShipto);
            oci_execute($qshipto);
            $rplant = oci_fetch_assoc($qshipto);
            $materialT = $rplant['MATERIAL'];
            $plantT = $rplant['PLANT'];
            
            if (count($rplant) > 0) {
                // join untuk ammbil nama sekarang 
                $cekNamaPlant = "SELECT
                                sys.WERKS,
                                sys.NAME1 
                                FROM
                                RFC_Z_ZAPP_SELECT_SYSPLAN sys 
                                WHERE
                                WERKS = '$plantT'
                                GROUP BY
                                sys.WERKS,
                                sys.NAME1 ";

                $qnamaplant = oci_parse($conn, $cekNamaPlant);
                oci_execute($qnamaplant);
                $nmPlant = oci_fetch_assoc($qnamaplant);
                
                $_response = array();
                $_response['WERKS'] = $nmPlant['WERKS'];
                $_response['NM_PLANT']  = $nmPlant['NAME1'];
                $lengkap = $nmPlant['WERKS'].';'.$nmPlant['NAME1'];

                $cekNamaMaterial = "SELECT
                                mat.MATNR,
                                mat.MAKTX,
                                upper(mat.MEINS) AS MEINS 
                                FROM
                                RFC_Z_ZCSD_LIST_MAT_SALES_2 mat 
                                WHERE
                                mat.MATNR = '$materialT'
                                and rownum <= 1
                                GROUP BY
                                mat.MATNR,
                                mat.MAKTX,
                                upper(mat.MEINS) ";
                
                $qnamaMaterial = oci_parse($conn, $cekNamaMaterial);
                oci_execute($qnamaMaterial);
                $nmMaterial = oci_fetch_assoc($qnamaMaterial);

                $_response['MATNR'] = $nmMaterial['MATNR'];
                $_response['MAKTX']  = $nmMaterial['MAKTX'];
                $_response['MEINS']  = $nmMaterial['MEINS'];
                $_material = $nmMaterial['MATNR'].';'.$nmMaterial['MAKTX'].';'.$nmMaterial['MEINS'];

                echo $lengkap.';'.$_material;
                // echo json_encode($_response);
            }
    break;


    case "getPlant":
        $brand = trim($_GET['brand']);
        $distrik = trim($_GET['distrik']);
        $material = trim($_GET['material']);
        $incoterm = trim($_GET['incoterm']);
        $periodeNow = $date = date('m-Y');

        if ($incoterm == 'FRC' || $incoterm == 'CIF') {           
            $getPlant = array();
            $cekPlant = "SELECT
            	scm.PLANT,
                sys.NAME1 ,
                scm.PRIORITAS
        FROM
            ZSD_TARGET_HEADER_SCM scm
            LEFT JOIN
            RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        WHERE
        scm.BRAND = '$brand' 
        AND scm.DISTRIK = '$distrik'  
        AND  scm.MATERIAL = '$material' 
        AND scm.INCOTERM = '$incoterm' 
        AND coalesce(scm.QUARY,0) != '0' 
        AND scm.PERIODE = '$periodeNow' 
        AND scm.FLAG_DEL != 'Y'
        GROUP BY scm.PLANT,
        sys.NAME1,
        scm.PRIORITAS
        ORDER BY
        scm.PRIORITAS";

        $qplant = oci_parse($conn, $cekPlant);

        $cek = oci_execute($qplant);
        $row = oci_fetch_array($qplant);

        if (count($row[0]) > 0) {
            echo '  <select name="plant" id="plant" onchange="loadShipcond()">';
            echo '<option value="">---Pilih Plant---</option>';
            echo '<option selected value="' . $row['PLANT'] . '" title="' . $row['PLANT'] . '">' . $row['PLANT'] . ' ' . $row['NAME1'] . '</option>';
            echo  '</select>';

            // Tambahan kondisi untuk FRC dan CIF
            if (($incoterm == 'FRC' || $incoterm == 'CIF') && $brand != 'CURAH') {
                oci_execute($qplant); // Menjalankan ulang query untuk digunakan kembali
                echo '<div id="plant_info">';
                while ($raw = oci_fetch_assoc($qplant)) {
                    $input_name = 'prioritasplant_' . $raw['PRIORITAS'];
                    echo '<input type="hidden" name="' . $input_name . '" value="' . $raw['PLANT'] . '">';
                }
                echo '</div>';
            }
        } else {
            echo '<select name="plant" id="plant" onchange="loadShipcond()">';
            echo '<option value="">---Pilih Plant---</option>';
            echo  '</select>';
        }






        }else{
            $getPlant = array();
            $cekPlant = "SELECT DISTINCT
            scm.PLANT , sys.NAME1
        FROM
            ZSD_TARGET_HEADER_SCM scm
            LEFT JOIN
            RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
            WHERE scm.BRAND = '$brand' 
            AND scm.DISTRIK = '$distrik'  
            AND  scm.MATERIAL = '$material' 
            AND scm.INCOTERM = '$incoterm' 
            --AND coalesce(scm.QUARY,0) != '0'  
            AND scm.PERIODE = '$periodeNow' 
            AND FLAG_DEL != 'Y' GROUP BY scm.PLANT, sys.NAME1 
			";

        $qplant = @oci_parse($conn, $cekPlant);
        @oci_execute($qplant);
        // $raw = oci_fetch_assoc($qplant);
        // $row = @oci_fetch_assoc($qplant);
        // var_dump($row);
        // if(count($row[0]) > 0){

             echo '<select name="plant" id="plant" onchange="loadShipcond()">';
             echo '<option value="">---Pilih Plant---</option>';
            //  foreach($row as $data){
                // var_dump($row['PLANT'] ,$row['NAME1']);exit;
               
            //   } 
            while ($raw = oci_fetch_assoc($qplant)) {
                
               echo '<option value="'.$raw['PLANT'].'" title="'.$raw['PLANT'].'">'.$raw['PLANT'].' '.$raw['NAME1'].'</option>';
            }



            // foreach ($getPlant as $key ) {
            //     # code...
            // }

            // var_dump($getPlant);
            
            
            // echo '<option value="'.$raw['PLANT'].'" title="'.$raw['PLANT'].'">'.$raw['PLANT'].' '.$raw['NAME1'].'</option>';
             echo  '</select>';
        // }else{
        //     echo '<select name="plant" id="plant">';
        //     echo '<option value="">---Pilih Plant---</option>';
        //      echo  '</select>';
        // }
            }
    break;

    case "getQtyBulanan":
        $jkemasan=trim($_GET['jkemasan']);
        $distrik=trim($_GET['distrik']);
        $material=trim($_GET['material']);
        $brand=trim($_GET['brand']);
        $incoterm=trim($_GET['incoterm']);
        $tanggal=trim($_GET['tanggal']);
        $plant=trim($_GET['plant']);
        $org = trim($_GET['org']);
        $qty_asli = trim($_GET['qty']);

        $getPrd = explode("-",$tanggal);
        $periode = $getPrd[1]."-".$getPrd[2];  

        if ($incoterm != 'FRC' || $incoterm != 'CIF') {
            // convert dulu JIKA ZAK
            $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE VKORG = '{$org}' AND MATNR = '{$material}'  AND WERKS = '{$plant}'";
            // var_dump($strmat);
            $querymat = oci_parse($conn, $strmat);
            oci_execute($querymat);
            $rowmat = oci_fetch_array($querymat, OCI_ASSOC);
            //echo json_encode($rowmat); 
            
            if (count($rowmat) > 0 && $rowmat['MEINS'] == "ZAK") {
                $qty = ( intval($qty_asli) * $rowmat['BERAT'] ) / 1000;
            } else {
                $qty =  $qty_asli;
            }

            $selectData = "SELECT
            QTY_BULANAN,QTY_HARIAN,QUARY 
            FROM
                ZSD_TARGET_HEADER_SCM 
            WHERE
            BRAND = '$brand' AND DISTRIK = '$distrik'  AND  MATERIAL = '$material' AND INCOTERM = '$incoterm' AND PLANT = '$plant' AND ROWNUM <= 1";
            
            $exc=oci_parse($conn,$selectData);
            oci_execute($exc);
            $dataHarian=oci_fetch_assoc($exc);


            $sisaQuary = $dataHarian['QTY_BULANAN'] - $qty; 

            // if ( $sisaQuary < 0) {
            //     echo 'Silahkan pilih plant lain karena Sisa Quota Bulanan Anda Pada Plant "'.$plant.'" sebesar "'.$sisaQuary.'" ';
            // }
        }
        

        
    break;

    case "viewkota2":
        $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
        $jmlkota=count($cekdata);
            unset($kotaayy); 
             $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE PLANT = '".$plant."' AND DELETE_MARK = 0";
            $qplant = oci_parse($conn, $sqlplant);
            oci_execute($qplant);
            $rplant = oci_fetch_assoc($qplant);
            $planharian = $rplant['PLANT'];
            // echo $jmlkota;
            echo '  <select name="kota_tujuan" id="kota_tujuan" onChange="resetKota(); clearData();">
                        <option value="">---Pilih Kota---</option>
                        ';
                        if($jmlkota>0){
                            
                            foreach($cekdata as $key => $valpla){               
                                    $kotaayy[$valpla['DISTRIK']]=$valpla['DISTRIK'];               
                            }

                            //oracle staging 
                            foreach ($kotaayy as $ktkey => $ktval){
                                if (in_array($produk_in, array('121-301', '121-701','121-800'))) {
                                    $sql = "SELECT * FROM RFC_Z_ZCSD_SAL_DIS WHERE BZIRK = '".$ktval."' ORDER BY BZIRK ASC";
                                }else{
                                    $sql = "SELECT * FROM RFC_Z_ZCSD_SAL_DIS WHERE BZIRK LIKE '".substr($ktval, 0,2)."%' ORDER BY BZIRK ASC";
                                }
                               
                                $query= oci_parse($conn, $sql);
                                oci_execute($query);
                                if (in_array($produk_in, array('121-301', '121-701','121-800'))) {
                                    while($datafunc=oci_fetch_assoc($query)){
                                        $kodeprop = '10'.substr($datafunc['BZIRK'], 0,2);
                                                $kotaSAP[$datafunc["BZIRK"]]=array($datafunc["BZTXT"],$kodeprop);
                                    }
                                }else{
                                    $proptemp = array();
                                    while($datafunc=oci_fetch_assoc($query)){
                                        $kodeprop = '10'.substr($datafunc['BZIRK'], 0,2);
                                        if (in_array($kodeprop, $proptemp)) {
                                            continue;
                                        }else{
                                            array_push($proptemp, $kodeprop);
                                        }
                                    }
                                    $proptemp = implode(",", $proptemp);
                                    $selectprop = "select * from ZREPORT_M_PROVINSI WHERE KD_PROV IN ($proptemp)";
                                    $qprop = oci_parse($conn, $selectprop);
                                    oci_execute($qprop);
                                    while($datafunc=oci_fetch_assoc($qprop)){
                                        $kotaSAP[$datafunc['KD_PROV']]=array($datafunc["NM_PROV"],$datafunc['KD_PROV']);
                                    }
                                }
                            }         
                            
                            foreach($kotaSAP as $kyKt => $valsap){
                                echo '<option value="'.$kyKt.'" data-prop="'.$valsap[1].'">'.$kyKt.' '.$valsap[0].'</option>';
                            }
                        }
                echo  '</select>';
    break;

    case "getKota":
        $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
        $jmlkota=count($cekdata);
            unset($kotaayy); 
             $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE PLANT = '".$plant."' AND DELETE_MARK = 0";
            $qplant = oci_parse($conn, $sqlplant);
            oci_execute($qplant);
            $rplant = oci_fetch_assoc($qplant);
            $planharian = $rplant['PLANT'];
            echo '  <select name="kota_tujuan" id="kota_tujuan" onChange="getPlant(); clearData();"  >
                        <option value="">---Pilih Kota---</option>
                        ';
            $brand=trim($_GET['brand']);
            $material=trim($_GET['material']);

                        if($jmlkota>0){
                            foreach($cekdata as $key => $valpla){               
                                    $kotaayy[$valpla['DISTRIK']]=$valpla['DISTRIK'];
                            }

                            //oracle staging 
                            foreach ($kotaayy as $ktkey => $ktval){
                                if (in_array($produk_in, array('121-301', '121-701','121-800'))) {
                                    $sql = "SELECT dis.*  FROM RFC_Z_ZCSD_SAL_DIS dis LEFT JOIN ZSD_TARGET_HEADER_SCM scm ON 
                                    scm.DISTRIK = dis.BZIRK  WHERE scm.DISTRIK = '".$ktval."' 	AND scm.BRAND = '".$brand."'
	                                AND scm.MATERIAL IS NOT NULL  AND  ROWNUM <= 1 ORDER BY BZIRK ASC";
                                }else{
                                    $sql = "SELECT dis.*  FROM RFC_Z_ZCSD_SAL_DIS dis LEFT JOIN ZSD_TARGET_HEADER_SCM scm ON 
                                    scm.DISTRIK = dis.BZIRK  WHERE scm.DISTRIK = '".$ktval."' AND scm.MATERIAL IS NOT NULL AND scm.BRAND = 'CURAH' AND  ROWNUM <= 1 ORDER BY BZIRK ASC";
                                    
                                }
                         
                                $query= oci_parse($conn, $sql);
                                oci_execute($query);
                                if (in_array($produk_in, array('121-301', '121-701','121-800'))) {
                                    while($datafunc=oci_fetch_assoc($query)){
                                        $kodeprop = '10'.substr($datafunc['BZIRK'], 0,2);
                                                $kotaSAP[$datafunc["BZIRK"]]=array($datafunc["BZTXT"],$kodeprop);
                                    }
                                }else{
                             
                                    $proptemp = array();
                                    while($datafunc=oci_fetch_assoc($query)){
                                        $kodeprop = '10'.substr($datafunc['BZIRK'], 0,2);
                                        if (in_array($kodeprop, $proptemp)) {
                                            continue;
                                        }else{
                                            array_push($proptemp, $kodeprop);
                                        }
                                    }
                                    $proptemp = implode(",", $proptemp);
                                    $selectprop = "select * from ZREPORT_M_PROVINSI WHERE KD_PROV IN ($proptemp)";
                                     
                                    $qprop = oci_parse($conn, $selectprop);
                                    oci_execute($qprop);
                                    while($datafunc=oci_fetch_assoc($qprop)){
                                        $kotaSAP[$datafunc['KD_PROV']]=array($datafunc["NM_PROV"],$datafunc['KD_PROV']);
                                    }
                                }
                            }         
                            
                            foreach($kotaSAP as $kyKt => $valsap){
                                echo '<option value="'.$kyKt.'" data-prop="'.$valsap[1].'">'.$kyKt.' '.$valsap[0].'</option>';
                            }
                        }
                echo  '</select>';
    break;

    case "getKotaRoyalti":
        $brand = $_GET['brand'];
        
        $dist_target = $sold_to_in;
        $cek_assoc = "select * from MAPPING_SOLDTO_ASSOC where FLAG_DEL != 'Y' and SOLDTO = '$dist_target' ";
        $ascSoldto = oci_parse($conn, $cek_assoc);
        oci_execute($ascSoldto);
        $getDist = oci_fetch_assoc($ascSoldto);
        if (oci_num_rows($ascSoldto) != 0) {
            $dist_target = $getDist['SOLDTO_ASSOC'];
        }
        $queryDistrikDist = "SELECT zthnb.DISTRIK , rzzsd.BZTXT, zmp.KD_PROV, zmp.NM_PROV 
                            FROM ZSD_TARGET_HARIAN_NEW_BRAND zthnb 
                            LEFT JOIN RFC_Z_ZCSD_SAL_DIS rzzsd ON rzzsd.BZIRK=zthnb.DISTRIK
                            LEFT JOIN ZREPORT_M_PROVINSI zmp ON zmp.KD_PROV='10'||SUBSTR(rzzsd.BZIRK,1,2)
                            WHERE zthnb.DISTRIBUTOR='$dist_target' AND to_char(zthnb.TANGGAL_TARGET,'YYYYMM')='".date('Ym')."'
                            AND zthnb.BRAND='$brand' AND zthnb.DEL_MARK='0' AND zthnb.TIPE='$produk_in'
                            GROUP BY zthnb.DISTRIK , rzzsd.BZTXT, zmp.KD_PROV, zmp.NM_PROV ";
                       // echo $queryDistrikDist;

        $query= oci_parse($conn, $queryDistrikDist);
        oci_execute($query);
        echo '  <select name="kota_tujuan" id="kota_tujuan" onChange="getPlant(); clearData();"  >
        <option value="">---Pilih Kota---</option>
        ';

       while($datafunc=oci_fetch_assoc($query)){
            echo '<option value="'.$datafunc["DISTRIK"].'" data-prop="'.$datafunc["KD_PROV"].'">'.$datafunc["DISTRIK"].' '.$datafunc["BZTXT"].'</option>';
        }

        echo  '</select>';
    break;
    
    case "viewshipcond":  
//        print_r($plant); 
                        if($plant!=''){
            $mysql= "select * from ZMD_SHIPPING_CONDITION 
           where DEL = 0 AND PLANT = '{$plant}'
           ";			 
           $mysql_set=oci_parse($conn,$mysql);
           oci_execute($mysql_set);
            echo '  <select name="ship_condition" id="ship_condition" >
                        <option value="">---Pilih Shipping Condition ---</option>
                        ';
            
                            while ($val=oci_fetch_assoc($mysql_set)){ 
                                echo("<option value='{$val['SHIPPING_CONDITION']}' title='{$val['DESCRIPTION']}' ");
                                if($val['SHIPPING_CONDITION'] == $plant_select){echo("selected");}
                                echo(">{$val['SHIPPING_CONDITION']}"." - "."{$val['DESCRIPTION']}</option>");
                            }
                            
                         
                echo  '</select>';
                        } 
    break;

    
    case "viewsisa":
        $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
        $jmlkota=count($cekdata);
            unset($tSisa);
            if($jmlkota>0){                            
                foreach($cekdata as $key => $valpla){               
                        $tSisa +=$valpla['SELISIH'];               
                }
            }
            if ($tglkontrak) {
                $tglkontrak = strtotime('$tglkontrak');
                $tglkirim = strtotime('$tgl_kirim_in');
                if ($tglkontrak < $tglkirim) {
                    echo 'tgldiluarkontrak';
                }else{
                    echo $tSisa;
                }
            }else{
                echo $tSisa;
            }
    break;

    case "getKuota":
        $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
        

        $mapping ="SELECT QTY_HARIAN,TANGGAL FROM ZSD_TARGET_HARIAN_SCM WHERE TANGGAL = '21-09-2023' AND PLANT = '79I8' AND DISTRIK = '251001' GROUP BY QTY_HARIAN,TANGGAL";


        $qcekbrand = oci_parse($conn, $mapping);
        oci_execute($qcekbrand);
        $cekrbrand = oci_fetch_assoc($qcekbrand);
        $cekrbrand = @$cekrbrand['STATUS'];
        // return $cekrbrand;
        if($cekrbrand==''or$cekrbrand==null){
            $cekrbrand='MB';
            // return $cekrbrand;
        }

        echo $cekrbrand;
    break;



    case "getKonversi":
        // $org = trim($_GET['org']);
        $material = trim($_GET['material']);
        $plant = trim($_GET['plant']);
        $qtyMasuk = trim($_GET['qty_in']);


        $sqlGetOrg = "SELECT ORG FROM OR_MAP_TOP_BRAND WHERE PLANT_ID = '$plant' AND ROWNUM <= 1 AND DELETE_MARK = 0";
                // echo $sqlGetOrg;
        $exGetOrg = oci_parse($conn, $sqlGetOrg);
        oci_execute($exGetOrg);
        $cekOrg = oci_fetch_assoc($exGetOrg);
        $org = $cekOrg['ORG']; 

        $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE VKORG = '{$org}' AND MATNR = '{$material}'  AND WERKS = '{$plant}'";
        // var_dump($strmat);
            $querymat = oci_parse($conn, $strmat);
            oci_execute($querymat);
            $rowmat = oci_fetch_array($querymat, OCI_ASSOC);
            if (count($rowmat) > 0 && $rowmat['MEINS'] == "ZAK") {
                $qty = ( intval($qtyMasuk) * $rowmat['BERAT'] ) / 1000;
            } else {
                $qty =  $qtyMasuk;
            }


            print_r($qty);
        
    break;

    case "cekmapbrand":
        $periode=trim($_GET['tglkirim']);
        $mapping ="SELECT STATUS FROM ZMD_MAPPING_BRAND WHERE
               PLANT='".$plant."'and
               DISTRICT='".$kode_distrik_in."' and 
               DISTRIBUTOR='".$sold_to_in."' and
               to_char(PERIODE,'YYYYMM')='".date('Ym',strtotime($periode))."' and 
               del_mark = '0'";
        $qcekbrand = oci_parse($conn, $mapping);
        oci_execute($qcekbrand);
        $cekrbrand = oci_fetch_assoc($qcekbrand);
        $cekrbrand = @$cekrbrand['STATUS'];
        // return $cekrbrand;
        if($cekrbrand==''or$cekrbrand==null){
            $cekrbrand='MB';
            // return $cekrbrand;
        }

        echo $cekrbrand;

    break;
    case "leadtime_so":
        //////////////////////////////////////////////
                $mysql="SELECT
                STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        /////////////////////////////////////////////
        //rddmin+1
        $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
        $panjang_kode_distrik = strlen($kode_distriknya);
        if($panjang_kode_distrik==4 or$panjang_kode_distrik=='4'){

            $kodeprovtgl = $kode_distriknya."_".$plant;
            $kodehorizon = $kode_distriknya;
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
            //---------------------------------------------------------
            $mysql_hotizon_rdd="SELECT
                NAMA_CONFIG
            FROM
                (select NAMA_CONFIG from ZSD_CONFIG where NAMA_CONFIG='HORIZON_RDD_$kodehorizon' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }else{

            $kodeprovtgl = substr($kode_distriknya,0,2)."_".$plant;
            $kodehorizon = substr($kode_distriknya,0,2);
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_10$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
            //---------------------------------------------------------
            $mysql_hotizon_rdd="SELECT
                NAMA_CONFIG
            FROM
                (select NAMA_CONFIG from ZSD_CONFIG where NAMA_CONFIG='HORIZON_RDD_10$kodehorizon' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }

        // echo $mysql_tglleadtimeplus;
        $mysql_settglleadtimeplus=oci_parse($conn,$mysql_tglleadtimeplus);
        oci_execute($mysql_settglleadtimeplus);
        $row_leadtimetglleadtimeplus=oci_fetch_assoc($mysql_settglleadtimeplus);
        $leadtimesotglleadtimeplus=$row_leadtimetglleadtimeplus[CONFIG];
        //------------------------------------------------------------
        $mysql_sethorizon=oci_parse($conn,$mysql_hotizon_rdd);
        oci_execute($mysql_sethorizon);
        $row_horizon=oci_fetch_assoc($mysql_sethorizon);
        $horizon_rdd=$row_horizon[NAMA_CONFIG];

        if($horizon_rdd!=null or $horizon_rdd!=''){
                if($horizon_rdd!=null or $horizon_rdd!=''){
                    // $leadtimeso = 0;
                    print_r(date('d-m-Y', strtotime(' +'.$leadtimeso.' day'))."/".$leadtimesotglleadtimeplus."/".$horizon_rdd);
                 }else{
                // print_r('');
                return null;
                }
         }else{
            if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
                print_r(date('d-m-Y', strtotime(' +'.$leadtimeso.' day'))."/".$leadtimesotglleadtimeplus."/".$horizon_rdd);
                }else{
                // print_r('');
                return null;
                }

        }

        // $mysql="SELECT
        //              STANDART_AREA
        //         FROM
        //             (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        //         WHERE
        //             rownum BETWEEN 0 AND 1";

        // $mysql_set=oci_parse($conn,$mysql);
        // oci_execute($mysql_set);
        // echo $mysql;
        // while ($val=oci_fetch_assoc($mysql_set)){ 
        //         print_r(date('d-m-Y', strtotime(' +'.$val['STANDART_AREA'].' day')));
        //         $flag='ada';
        // }
        // if($flag!='ada'){
        //     print_r(date('d-m-Y', strtotime(' +2 day')));
        // }
        //////////////////////////////////////////////
    break;
    case "config_umur_so":
        $mysql="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='UMUR_SO' and delete_mark='0'";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_config=oci_fetch_assoc($mysql_set);
        $configso=$row_config[CONFIG]; 

        if ($configso != 0 or $configso != "" or $configso != null){
            print_r($configso);
        }else{
            // print_r('');
            print_r($configso=0);
        }
    break;
    case "config_rdd_typeload":
        $mysql="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='RDD_TYPELOAD' and config='$loadtype' and delete_mark='0'";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_config=oci_fetch_assoc($mysql_set);
        $configso=$row_config[CONFIG]; 
        ///////////////
        $mysql1="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='TYPE_MATERIAL' and config='$produk_in' and delete_mark='0'";
        $mysql_set1=oci_parse($conn,$mysql1);
        oci_execute($mysql_set1);
        $row_config1=oci_fetch_assoc($mysql_set1);
        $configso1=$row_config1[CONFIG]; 
        //////////////
        if($loadtype=='FOT'){
            $mysql2 = "SELECT
            INCOTERM_SOURCE
        FROM
            (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$plant' and INCOTERM_SOURCE='FRC' ORDER BY id_plant desc)
        WHERE
            rownum BETWEEN 0 AND 1";
        $mysql_set2=oci_parse($conn,$mysql2);
        oci_execute($mysql_set2);
        $row_config2=oci_fetch_assoc($mysql_set2);
        $configso2=$row_config2[INCOTERM_SOURCE];
        } 

        

        if ($configso != 0 or $configso != "" or $configso != null){
            $configso = $configso;
        }else{
            // print_r('');
            $configso=0;
        }
        if ($configso1 != 0 or $configso1 != "" or $configso1 != null){
            $configso1=$configso1;
        }else{
            // print_r('');
            $configso1=0;
        }
        if ($configso2 != 0 or $configso2 != "" or $configso2 != null){
            $rdd_config= $configso2."/".$configso1;
        }else{
            // print_r('');
            $rdd_config= $configso."/".$configso1;
        }

        print_r($rdd_config);
    break;

    case "config_umur_so_reschedule":

        $user_org_in = $_REQUEST['orgin'];
        $plant = $_REQUEST['plant'];
        $produk_in = $_REQUEST['produkin'];
        $kode_distrik_in = $_REQUEST['kodedistrik'];

        $mysql="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='UMUR_SO' and delete_mark='0'";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_config=oci_fetch_assoc($mysql_set);
        $configso=$row_config[CONFIG]; 

        if ($configso != 0 or $configso != "" or $configso != null){
            echo json_encode($configso);
        }else{
            // print_r('');
            echo json_encode($configso=0);
        }
    break;


    case "leadtime_so_reschedule":
        //////////////////////////////////////////////
        $user_org_in = $_REQUEST['orgin'];
        $plant = $_REQUEST['plant'];
        $produk_in = $_REQUEST['produkin'];
        $kode_distrik_in = $_REQUEST['kodedistrik'];

                $mysql="SELECT
                STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        /////////////////////////////////////////////
        //rddmin+1
        $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
        $panjang_kode_distrik = strlen($kode_distriknya);
        if($panjang_kode_distrik==4 or$panjang_kode_distrik=='4'){

            $kodeprovtgl = $kode_distriknya."_".$plant;
            $kodehorizon = $kode_distriknya;
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
            //---------------------------------------------------------
            $mysql_hotizon_rdd="SELECT
                NAMA_CONFIG
            FROM
                (select NAMA_CONFIG from ZSD_CONFIG where NAMA_CONFIG='HORIZON_RDD_$kodehorizon' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }else{

            $kodeprovtgl = substr($kode_distriknya,0,2)."_".$plant;
            $kodehorizon = substr($kode_distriknya,0,2);
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_10$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
            //---------------------------------------------------------
            $mysql_hotizon_rdd="SELECT
                NAMA_CONFIG
            FROM
                (select NAMA_CONFIG from ZSD_CONFIG where NAMA_CONFIG='HORIZON_RDD_10$kodehorizon' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }

        // echo $mysql_tglleadtimeplus;
        $mysql_settglleadtimeplus=oci_parse($conn,$mysql_tglleadtimeplus);
        oci_execute($mysql_settglleadtimeplus);
        $row_leadtimetglleadtimeplus=oci_fetch_assoc($mysql_settglleadtimeplus);
        $leadtimesotglleadtimeplus=$row_leadtimetglleadtimeplus[CONFIG];
        //------------------------------------------------------------
        $mysql_sethorizon=oci_parse($conn,$mysql_hotizon_rdd);
        oci_execute($mysql_sethorizon);
        $row_horizon=oci_fetch_assoc($mysql_sethorizon);
        $horizon_rdd=$row_horizon[NAMA_CONFIG];

        if($horizon_rdd!=null or $horizon_rdd!=''){
                if($horizon_rdd!=null or $horizon_rdd!=''){
                    // $leadtimeso = 0;
                    echo json_encode(date('d-m-Y', strtotime(' +'.$leadtimeso.' day'))."/".$leadtimesotglleadtimeplus."/".$horizon_rdd);
                 }else{
                // print_r('');
                return null;
                }
         }else{
            if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
                echo json_encode(date('d-m-Y', strtotime(' +'.$leadtimeso.' day'))."/".$leadtimesotglleadtimeplus."/".$horizon_rdd);
                }else{
                // print_r('');
                return null;
                }

        }

        // $mysql="SELECT
        //              STANDART_AREA
        //         FROM
        //             (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        //         WHERE
        //             rownum BETWEEN 0 AND 1";

        // $mysql_set=oci_parse($conn,$mysql);
        // oci_execute($mysql_set);
        // echo $mysql;
        // while ($val=oci_fetch_assoc($mysql_set)){ 
        //         print_r(date('d-m-Y', strtotime(' +'.$val['STANDART_AREA'].' day')));
        //         $flag='ada';
        // }
        // if($flag!='ada'){
        //     print_r(date('d-m-Y', strtotime(' +2 day')));
        // }
        //////////////////////////////////////////////
    break;

    case "getdatagudangRescheduleSo";
    {

        //////
        $dist2=$_GET['soldin'];
        $dist = (int)$dist2;
        $tglleadtimepp = str_replace(" ","",$_GET['tgl_ldtime']);
        $distrik = $kode_distrik_in;
        $kode_distri_si= $dist2;
        $kode_gudang_si= $shipto;
        // $distrik='233001';
        // $kode_distri_si='0000000138';
        // $kode_gudang_si='1380016000';
        //////////////
        $mysqlperkapgudang="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
        $mysql_setperkapgudang=oci_parse($conn,$mysqlperkapgudang);
        oci_execute($mysql_setperkapgudang);
        $row_configperkapgudang=oci_fetch_assoc($mysql_setperkapgudang);
        $configperkapgudang=$row_configperkapgudang[CONFIG]; 
        if($configperkapgudang!=null or $configperkapgudang!=''){
            $configperkapgudangfix = intval($configperkapgudang);
        }else{
            $configperkapgudangfix = 100;
        }
        //////////////
        $mysql="SELECT
            STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
            $leadtime=$leadtimeso;
        }else{
            $leadtime=0;
        }

        // echo "leadtime".$leadtime;
        //////////////////////////////////////////////////////////////
        //intransit
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
            }
            $fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL");
            if ($fce == false ) {
                $sap->PrintStatus();
                exit;
            }

                    $fce->XVKORG = $_GET['orgin'];
                    $fce->XKUNNR2 = $shipto;
                    $fce->XBZIRK = $kode_distrik_in;

                        $hari_h = date('Ymd');
                        // $vdatuhigh = date('d.m.Y',strtotime());
                        $vdatulow = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));

                        $fce->LR_EDATU->row["SIGN"] = 'I';
                        $fce->LR_EDATU->row["OPTION"] = 'BT';
                        $fce->LR_EDATU->row["LOW"] = $vdatulow;
                        $fce->LR_EDATU->row["HIGH"] = $hari_h;
                        $fce->LR_EDATU->Append($fce->LR_EDATU->row);



                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {
                    $fce->RETURN_DATA->Reset();
                    $s=0;
                    while ($fce->RETURN_DATA->Next()){
                // $qty1[$s] = $fce->RETURN_DATA->row["KWMENG"];
                // $qty2[$s]= $fce->RETURN_DATA->row["KWMENGX"];
                $intransit += $fce->RETURN_DATA->row["RFMNG"];
                // $s++;
                        }
                    }else {
                        $fce->PrintStatus();
                    }


                    $fce->Close();
                    $sap->Close();
                    // $totallog = count($qty2);


                    // $intransit = @array_sum($qty1);

        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$kode_distri_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_soldto = json_decode($response, true);
        $adasoldto=false;
//         print_r($data_output_soldto);
        foreach ($data_output_soldto as $valuesoldto) {
            $kode_soldto_si_mdxl_temp = $valuesoldto['kode_distributor_mdxl'];
            $kode_soldto_si_mdxl = sprintf("%'010s",$kode_soldto_si_mdxl_temp);
//            $adasoldto=true;
            // $adashipto2=1;
        }
//             echo '<br>';
//            if($adashipto){
//                $mdxlshipto=$kode_gudang_si_mdxl;
//            }
//
//            if($mdxlshipto==NULL or $mdxlshipto==''){
//                $mdxlshipto=$kode_gudang_si;
//            }
//                 print_r($kode_soldto_si_mdxl);
        /////////////////////////////////////////////////////////////

//                     echo 'tetete'.$kode_soldto_si_mdxl.'rere'.$dist.'jiji';
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_shipto = json_decode($response, true);
        $adashipto=false;
        // print_r($data_output_shipto);
        foreach ($data_output_shipto as $valueshipto) {
            $kode_gudang_si_mdxl = $valueshipto['kode_gudang_mdxl'];
            $adashipto=true;
            // $adashipto2=1;
            }
            
           
            // echo '<br>';
            if($adashipto){
                $mdxlshipto=$kode_gudang_si_mdxl;
            }

            if($mdxlshipto==NULL or $mdxlshipto==''){
                $mdxlshipto=$kode_gudang_si;
            }
            
//                 print_r($mdxlshipto);
        /////////////////////////////////////////////////////////////
        // $datenow = date('Y-m-d');
        // $dateyst = date('Y-m-d', strtotime($datenow. ' - 30 days'));
        $curl = curl_init();
        //uat socc
        // $dist = '0000000106';

        curl_setopt_array($curl, array(
         //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&distributor='.$kode_soldto_si_mdxl.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        // CURLOPT_POSTFIELDS => array('end_date' => $datenow,'start_date' => $dateyst),
        ));

        $response = curl_exec($curl);

        curl_close($curl);


        
        $data_output = json_decode($response, true);
        $ada = false;

                                                            ///////////////////////////
                                                            $sap = new SAPConnection();
                                                            $sap->Connect("../include/sapclasses/logon_data.conf");
                                                            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                                                            if ($sap->GetStatus() != SAPRFC_OK ) {
                                                            echo $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
                                                            if ($fce == false ) {
                                                            $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            
                                                            //header entri
                                                            $fce->X_VKORG = '7900';
                                                            $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
                                                            $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
                                                            $fce->X_KUNNR = $dist2;
                                                            $fce->X_STATUS = '70';
                                                            $fce->X_BZIRK = $distrik;
                                                            ///////////////////////////
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7900';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7000';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            ///////////////////////////                                
                                                            ///////////////////////////
                                                            $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                            $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                            $fce->LR_KODE_DA->row["LOW"] = $shipto;
                                                            $fce->LR_KODE_DA->row["HIGH"] = '';
                                                            $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
                                                            foreach ($data_output["items"] as $key => $value) {
                                                                if (count($value["shipto_info"]) !== 0) {
                                                                //     $adadimdxl = false;
                                                                // }else{
                                                                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                                                                    $rptrealallshipto = 0;
                                                                    foreach ($value["shipto_info"] as $key2 => $value2) {
                                                                        if($value2['kode_shipto']==$mdxlshipto){
                                                                            $shiptoinfomdxl=$value["shipto_info"];
                                                                            if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
                                                                                foreach ($value["shipto_info"] as $key3 => $value3) {
                                                                                    // print_r($value3['kode_shipto']);
                                                                                    ///////////////////////////
                                                                                    $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                                                    $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                                                    $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
                                                                                    $fce->LR_KODE_DA->row["HIGH"] = '';
                                                                                    $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);                        
                                                                                    ///////////////////////////
                                                                                    // $rptrealallshipto += $rptrealavgin;
                                
                                                                                }
                                                                            }
                                                                        }
                                                                        // print_r("datanya ".$value2['kode_shipto']);
                                                                    }
                                                                }
                                                            }
                            
                                                            // echo "<pre>";
                                                            // print_r($fce);
                                                            // echo "</pre>";
                                                                     
                                                             $fce->Call();	
                                                                 $fce->ZDATA->Reset();
                                                                 $rptrealavgin=0;
                                                                 while ( $fce->ZDATA->Next() ){
                                                                     $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
                                                                     // print_r($fce->ZDATA->row["KWANTUM"]);
                                                                 }
                                                     
                                                             $fce->Close();	
                                                             $sap->Close();	            
                                                            //  $rptrealavgin = $rptrealavgin/7;
                                                             $realisasi_shipment = $rptrealavgin;

                                //////////////////////////////////////////////////////////////
                            
                            
                //////////////////////////////////////////////////////////////
        
        ////uat socc
                foreach ($data_output["items"] as $key => $value) {
                    if (count($value["shipto_info"]) !== 0) {
                    //     $adadimdxl = false;
                    // }else{
                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                    foreach ($value["shipto_info"] as $key2 => $value2) {
                        // print_r("datanya ".$value2['kode_shipto']);
                        if($value2['kode_shipto']==$mdxlshipto){                    
                            $volume_stock_gudang=$value["volume_stock_gudang"];
                            $penjualan_avg=$value["penjualan_avg"];
                            $sell_out=$value["sell_out"];



                            ////////////////////////////////////////perubahan rumus 13/06/2023
                            // new
                            $forcase_level_stok = $volume_stock_gudang+$realisasi_shipment - ($penjualan_avg * $leadtime);
                            ///////////////////////////////////////////////////////////////
                            // old
                            // // $forcase_level_stok = $forcase_level_stok+$rptrealavgin;
                            // ///////////////////////////////////////////////////////////////////////////////////// perubahan tanggal 08-12-2022
                            //                 /////////////////////////////////////////////
                            //                 $tgl_cek = date('Ymd', strtotime($tglleadtimepp. ' -'.$leadtime.' day'));
                            //                 $tgl_cek2 = date('Ymd');
                            //                 $tgl_qtyrddnya=false;
                            //                 if($tgl_cek>$tgl_cek2){
                            //                     $tgl_qtyrddnya=true;
                            //                 }
                            //             //////////////////////////////////////////////
                            //     if($tgl_qtyrddnya){
                            //         $startTimeStamp1 = date("d-m-Y");
                            //         $startTimeStamp = strtotime($startTimeStamp1); 
                            //         $endTimeStamp = strtotime($tglleadtimepp);
                            //         $timeDiff = abs($endTimeStamp - $startTimeStamp);
                            //         $numberDays = $timeDiff/86400;  // 86400 seconds in one day
                            //         // and you might want to convert to integer
                            //         $numberDays = intval($numberDays);
                            //             $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime);
                            //             $avg_sell_out_RDD=$numberDays*$penjualan_avg;
                            //             $forcase_level_stok = $forcase_level_stok-$avg_sell_out_RDD;
                            //             $avg_sell_in_RDD=$numberDays*$rptrealavgin;
                            //             $forcase_level_stok = $forcase_level_stok+$avg_sell_in_RDD;
                            //     }else{
                            //         $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                            //     }
                            //     ////////////////////////////////////////////////////////////////////////////////////
                            // ////////////////////////////////////////////////////////////////////////////////////    
                            ////////////////////////////////////////perubahan rumus 13/06/2023                        
                            $mdxlkapasitas_gudang=$value["kapasitas_gudang"];
                            $kapasitas_gudang= ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
                            // echo "kapasitas gudang 90% dari ".$mdxlkapasitas_gudang." adalah : ".$kapasitas_gudang;
                            // echo "<br>";
                            $order_qty=$kapasitas_gudang-$forcase_level_stok;
                            $kapasitas_bongkar=$value["kapasitas_bongkar"];
                            $ada=true;
                            // $ada2=1;
                        }
                    }
                }
            }
                /////////////////////////////////////////////////
                if($ada){
                    $order_qty = $order_qty;
                    $kapasitas_bongkar = $kapasitas_bongkar;
                }else{
                            /////////////////////////////////////////////
                            //rddmin+1
                            $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
                            $panjang_kode_distrik = strlen($kode_distriknya);
                            if($panjang_kode_distrik==4 or $panjang_kode_distrik=='4'){
                                $kodeprovgudang = $kode_distriknya;
                                $parameterconfiggudang="GUDANG_".$kodeprovgudang;
                            }else{
                                $kodeprovgudang = substr($kode_distriknya,0,2);
                                $parameterconfiggudang="GUDANG_10".$kodeprovgudang;
                            }
                            $mysql_gudang="SELECT
                                CONFIG
                            FROM
                                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                            WHERE
                                rownum BETWEEN 0 AND 1";
                            // echo $mysql_tglleadtimeplus;
                            $mysql_setgudang=oci_parse($conn,$mysql_gudang);
                            oci_execute($mysql_setgudang);
                            $row_setgudang=oci_fetch_assoc($mysql_setgudang);
                            $configbongkar=$row_setgudang[CONFIG]; 
                            //////////////
                            //////////////
                            /////////
                    //kurang rumus intransit;
                    if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null){
                        $mysqlconbongkar="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";

                        $mysql_configbongkar=oci_parse($conn,$mysqlconbongkar);
                        oci_execute($mysql_configbongkar);
                        $row_configbongkar=oci_fetch_assoc($mysql_configbongkar);
                        $configbongkar=$row_configbongkar[CONFIG];
                    }
 
                    if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null){

                        // $forcase_level_stok = 0-(0*$leadtime)+$intransit;
                        $forcase_level_stok = 0;
                        $kapasitas_gudang=$configbongkar;
                        $order_qty=$kapasitas_gudang-$forcase_level_stok;
                        $kapasitas_bongkar=$configbongkar;
                    }
                }

                ///////////////////////////
                $sqlgetpp="SELECT
                sum(or_trans_dtl.QTY_PP) as TOTPP
            FROM
                OR_TRANS_hdr
            JOIN or_trans_dtl ON
                or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
            WHERE
                (or_trans_dtl.STATUS_LINE = 'OPEN'
                    OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                    OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                AND or_trans_hdr.SOLD_TO = '$dist2'
                AND or_trans_hdr.DELETE_MARK = '0'
                AND or_trans_dtl.DELETE_MARK = '0'
                AND or_trans_dtl.KODE_TUJUAN = '$distrik'
                AND or_trans_dtl.SHIP_TO = '$kode_gudang_si'
                AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$tglleadtimepp'
                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'";


                $mysql_getpp=oci_parse($conn,$sqlgetpp);
                oci_execute($mysql_getpp);
                $row_getpp=oci_fetch_assoc($mysql_getpp);
                $getpp=$row_getpp[TOTPP];

                $final_qty = $order_qty - $getpp;
                

                ///////////////////////////
                // echo ($final_qty;
                if($final_qty>(200-$getpp)){
                    $max_order_qty_socc=200-$getpp;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
                // echo $final_qty; echo " - ";
            print_r(floor($max_order_qty_socc));
    }
    break;

    case "config_rdd_typeload_reschedule":

        $user_org_in = $_REQUEST['orgin'];
        $plant = $_REQUEST['plant'];
        $produk_in = $_REQUEST['produkin'];
        $loadtype = $_REQUEST['loadtype'];


        $mysql="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='RDD_TYPELOAD' and config='$loadtype' and delete_mark='0'";
        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_config=oci_fetch_assoc($mysql_set);
        $configso=$row_config[CONFIG]; 
        ///////////////
        $mysql1="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='TYPE_MATERIAL' and config='$produk_in' and delete_mark='0'";
        $mysql_set1=oci_parse($conn,$mysql1);
        oci_execute($mysql_set1);
        $row_config1=oci_fetch_assoc($mysql_set1);
        $configso1=$row_config1[CONFIG]; 
        //////////////
        if($loadtype=='FOT'){
            $mysql2 = "SELECT
            INCOTERM_SOURCE
        FROM
            (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$plant' and INCOTERM_SOURCE='FRC' ORDER BY id_plant desc)
        WHERE
            rownum BETWEEN 0 AND 1";
        $mysql_set2=oci_parse($conn,$mysql2);
        oci_execute($mysql_set2);
        $row_config2=oci_fetch_assoc($mysql_set2);
        $configso2=$row_config2[INCOTERM_SOURCE];
        } 

        

        if ($configso != 0 or $configso != "" or $configso != null){
            $configso = $configso;
        }else{
            // print_r('');
            $configso=0;
        }
        if ($configso1 != 0 or $configso1 != "" or $configso1 != null){
            $configso1=$configso1;
        }else{
            // print_r('');
            $configso1=0;
        }
        if ($configso2 != 0 or $configso2 != "" or $configso2 != null){
            $rdd_config= $configso2."/".$configso1;
        }else{
            // print_r('');
            $rdd_config= $configso."/".$configso1;
        }

        echo json_encode($rdd_config);
    break;

    case "pengecekan_rdd_min":        
        $tgl_rdd_cek=$_GET['tgl_rdd'];
        //////////////////////////////////////////////
                $mysql="SELECT
                STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        /////////////////////////////////////////////
        //rddmin+1
        $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
        $panjang_kode_distrik = strlen($kode_distriknya);
        if($panjang_kode_distrik==4 or$panjang_kode_distrik=='4'){
            $kodeprovtgl = $kode_distriknya."_".$plant;
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }else{
            $kodeprovtgl = substr($kode_distriknya,0,2)."_".$plant;
            $mysql_tglleadtimeplus="SELECT
                    CONFIG
            FROM
                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_10$kodeprovtgl' and DELETE_MARK='0' ORDER BY ID desc)
            WHERE
                rownum BETWEEN 0 AND 1";
        }

        // echo $mysql_tglleadtimeplus;
        $mysql_settglleadtimeplus=oci_parse($conn,$mysql_tglleadtimeplus);
        oci_execute($mysql_settglleadtimeplus);
        $row_leadtimetglleadtimeplus=oci_fetch_assoc($mysql_settglleadtimeplus);
        $leadtimesotglleadtimeplus=$row_leadtimetglleadtimeplus[CONFIG]; 

        $tgl_cek = strtotime(date('Ymd', strtotime($tgl_rdd_cek. ' -'.$leadtimeso.' day')));
        $tgl_cek2 = strtotime(date('Ymd'));
            if ($leadtimesotglleadtimeplus!='' or $leadtimesotglleadtimeplus!=null){
                if($tgl_cek>$tgl_cek2){
                    $tgl_rddnya='RDDMIN_TRUE';
                }else{
                    $tgl_rddnya='RDDMIN_FALSE';
                }
            }else{
                $tgl_rddnya='RDDMIN_TRUE';
            }   
            print_r($tgl_rddnya);         
        //////////////////////////////////////////////
    break;
    case "getdatagudang";
    {

        //////
        $dist2=$_GET['soldin'];
        $dist = (int)$dist2;
        $tglleadtimepp = str_replace(" ","",$_GET['tgl_ldtime']);
        $distrik = $kode_distrik_in;
        $kode_distri_si= $dist2;
        $kode_gudang_si= $shipto;
        // $distrik='233001';
        // $kode_distri_si='0000000138';
        // $kode_gudang_si='1380016000';
        //////////////
        $mysqlperkapgudang="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
        $mysql_setperkapgudang=oci_parse($conn,$mysqlperkapgudang);
        oci_execute($mysql_setperkapgudang);
        $row_configperkapgudang=oci_fetch_assoc($mysql_setperkapgudang);
        $configperkapgudang=$row_configperkapgudang[CONFIG]; 
        if($configperkapgudang!=null or $configperkapgudang!=''){
            $configperkapgudangfix = intval($configperkapgudang);
        }else{
            $configperkapgudangfix = 100;
        }
        //////////////
        $mysql="SELECT
            STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
            $leadtime=$leadtimeso;
        }else{
            $leadtime=0;
        }

        // echo "leadtime".$leadtime;
        //////////////////////////////////////////////////////////////
        //intransit
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
            }
            $fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL");
            if ($fce == false ) {
                $sap->PrintStatus();
                exit;
            }

                    $fce->XVKORG = $_GET['orgin'];
                    $fce->XKUNNR2 = $shipto;
                    $fce->XBZIRK = $kode_distrik_in;

                        $hari_h = date('Ymd');
                        // $vdatuhigh = date('d.m.Y',strtotime());
                        $vdatulow = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));

                        $fce->LR_EDATU->row["SIGN"] = 'I';
                        $fce->LR_EDATU->row["OPTION"] = 'BT';
                        $fce->LR_EDATU->row["LOW"] = $vdatulow;
                        $fce->LR_EDATU->row["HIGH"] = $hari_h;
                        $fce->LR_EDATU->Append($fce->LR_EDATU->row);



                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {
                    $fce->RETURN_DATA->Reset();
                    $s=0;
                    while ($fce->RETURN_DATA->Next()){
                // $qty1[$s] = $fce->RETURN_DATA->row["KWMENG"];
                // $qty2[$s]= $fce->RETURN_DATA->row["KWMENGX"];
                $intransit += $fce->RETURN_DATA->row["RFMNG"];
                // $s++;
                        }
                    }else {
                        $fce->PrintStatus();
                    }


                    $fce->Close();
                    $sap->Close();
                    // $totallog = count($qty2);


                    // $intransit = @array_sum($qty1);

        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$kode_distri_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_soldto = json_decode($response, true);
        $adasoldto=false;
//         print_r($data_output_soldto);
        foreach ($data_output_soldto as $valuesoldto) {
            $kode_soldto_si_mdxl_temp = $valuesoldto['kode_distributor_mdxl'];
            $kode_soldto_si_mdxl = sprintf("%'010s",$kode_soldto_si_mdxl_temp);
//            $adasoldto=true;
            // $adashipto2=1;
        }
//             echo '<br>';
//            if($adashipto){
//                $mdxlshipto=$kode_gudang_si_mdxl;
//            }
//
//            if($mdxlshipto==NULL or $mdxlshipto==''){
//                $mdxlshipto=$kode_gudang_si;
//            }
//                 print_r($kode_soldto_si_mdxl);
        /////////////////////////////////////////////////////////////

//                     echo 'tetete'.$kode_soldto_si_mdxl.'rere'.$dist.'jiji';
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_shipto = json_decode($response, true);
        $adashipto=false;
        // print_r($data_output_shipto);
        foreach ($data_output_shipto as $valueshipto) {
            $kode_gudang_si_mdxl = $valueshipto['kode_gudang_mdxl'];
            $adashipto=true;
            // $adashipto2=1;
            }
            
           
            // echo '<br>';
            if($adashipto){
                $mdxlshipto=$kode_gudang_si_mdxl;
            }

            if($mdxlshipto==NULL or $mdxlshipto==''){
                $mdxlshipto=$kode_gudang_si;
            }
            
//                 print_r($mdxlshipto);
        /////////////////////////////////////////////////////////////
        // $datenow = date('Y-m-d');
        // $dateyst = date('Y-m-d', strtotime($datenow. ' - 30 days'));
        $curl = curl_init();
        //uat socc
        // $dist = '0000000106';

        curl_setopt_array($curl, array(
         //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&distributor='.$kode_soldto_si_mdxl.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        // CURLOPT_POSTFIELDS => array('end_date' => $datenow,'start_date' => $dateyst),
        ));

        $response = curl_exec($curl);

        curl_close($curl);


        
        $data_output = json_decode($response, true);
        $ada = false;

                                                            ///////////////////////////
                                                            $sap = new SAPConnection();
                                                            $sap->Connect("../include/sapclasses/logon_data.conf");
                                                            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                                                            if ($sap->GetStatus() != SAPRFC_OK ) {
                                                            echo $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
                                                            if ($fce == false ) {
                                                            $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            
                                                            //header entri
                                                            $fce->X_VKORG = '7900';
                                                            $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
                                                            $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
                                                            $fce->X_KUNNR = $dist2;
                                                            $fce->X_STATUS = '70';
                                                            $fce->X_BZIRK = $distrik;
                                                            ///////////////////////////
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7900';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7000';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            ///////////////////////////                                
                                                            ///////////////////////////
                                                            $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                            $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                            $fce->LR_KODE_DA->row["LOW"] = $shipto;
                                                            $fce->LR_KODE_DA->row["HIGH"] = '';
                                                            $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
                                                            foreach ($data_output["items"] as $key => $value) {
                                                                if (count($value["shipto_info"]) !== 0) {
                                                                //     $adadimdxl = false;
                                                                // }else{
                                                                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                                                                    $rptrealallshipto = 0;
                                                                    foreach ($value["shipto_info"] as $key2 => $value2) {
                                                                        if($value2['kode_shipto']==$mdxlshipto){
                                                                            $shiptoinfomdxl=$value["shipto_info"];
                                                                            if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
                                                                                foreach ($value["shipto_info"] as $key3 => $value3) {
                                                                                    // print_r($value3['kode_shipto']);
                                                                                    ///////////////////////////
                                                                                    $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                                                    $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                                                    $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
                                                                                    $fce->LR_KODE_DA->row["HIGH"] = '';
                                                                                    $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);                        
                                                                                    ///////////////////////////
                                                                                    // $rptrealallshipto += $rptrealavgin;
                                
                                                                                }
                                                                            }
                                                                        }
                                                                        // print_r("datanya ".$value2['kode_shipto']);
                                                                    }
                                                                }
                                                            }
                            
                                                            // echo "<pre>";
                                                            // print_r($fce);
                                                            // echo "</pre>";
                                                                     
                                                             $fce->Call();	
                                                                 $fce->ZDATA->Reset();
                                                                 $rptrealavgin=0;
                                                                 while ( $fce->ZDATA->Next() ){
                                                                     $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
                                                                     // print_r($fce->ZDATA->row["KWANTUM"]);
                                                                 }
                                                     
                                                             $fce->Close();	
                                                             $sap->Close();	            
                                                            //  $rptrealavgin = $rptrealavgin/7;
                                                             $realisasi_shipment = $rptrealavgin;

                                //////////////////////////////////////////////////////////////
                            
                            
                //////////////////////////////////////////////////////////////
        
        ////uat socc
                foreach ($data_output["items"] as $key => $value) {
                    if (count($value["shipto_info"]) !== 0) {
                    //     $adadimdxl = false;
                    // }else{
                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                    foreach ($value["shipto_info"] as $key2 => $value2) {
                        // print_r("datanya ".$value2['kode_shipto']);
                        if($value2['kode_shipto']==$mdxlshipto){                    
                            $volume_stock_gudang=$value["volume_stock_gudang"];
                            $penjualan_avg=$value["penjualan_avg"];
                            $sell_out=$value["sell_out"];



                            ////////////////////////////////////////perubahan rumus 13/06/2023
                            // new
                            $forcase_level_stok = $volume_stock_gudang+$realisasi_shipment - ($penjualan_avg * $leadtime);
                            ///////////////////////////////////////////////////////////////
                            // old
                            // // $forcase_level_stok = $forcase_level_stok+$rptrealavgin;
                            // ///////////////////////////////////////////////////////////////////////////////////// perubahan tanggal 08-12-2022
                            //                 /////////////////////////////////////////////
                            //                 $tgl_cek = date('Ymd', strtotime($tglleadtimepp. ' -'.$leadtime.' day'));
                            //                 $tgl_cek2 = date('Ymd');
                            //                 $tgl_qtyrddnya=false;
                            //                 if($tgl_cek>$tgl_cek2){
                            //                     $tgl_qtyrddnya=true;
                            //                 }
                            //             //////////////////////////////////////////////
                            //     if($tgl_qtyrddnya){
                            //         $startTimeStamp1 = date("d-m-Y");
                            //         $startTimeStamp = strtotime($startTimeStamp1); 
                            //         $endTimeStamp = strtotime($tglleadtimepp);
                            //         $timeDiff = abs($endTimeStamp - $startTimeStamp);
                            //         $numberDays = $timeDiff/86400;  // 86400 seconds in one day
                            //         // and you might want to convert to integer
                            //         $numberDays = intval($numberDays);
                            //             $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime);
                            //             $avg_sell_out_RDD=$numberDays*$penjualan_avg;
                            //             $forcase_level_stok = $forcase_level_stok-$avg_sell_out_RDD;
                            //             $avg_sell_in_RDD=$numberDays*$rptrealavgin;
                            //             $forcase_level_stok = $forcase_level_stok+$avg_sell_in_RDD;
                            //     }else{
                            //         $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                            //     }
                            //     ////////////////////////////////////////////////////////////////////////////////////
                            // ////////////////////////////////////////////////////////////////////////////////////    
                            ////////////////////////////////////////perubahan rumus 13/06/2023                        
                            $mdxlkapasitas_gudang=$value["kapasitas_gudang"];
                            $kapasitas_gudang= ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
                            // echo "kapasitas gudang 90% dari ".$mdxlkapasitas_gudang." adalah : ".$kapasitas_gudang;
                            // echo "<br>";
                            $order_qty=$kapasitas_gudang-$forcase_level_stok;
                            $kapasitas_bongkar=$value["kapasitas_bongkar"];
                            $ada=true;
                            // $ada2=1;
                        }
                    }
                }
            }
                /////////////////////////////////////////////////
                if($ada){
                    $order_qty = $order_qty;
                    $kapasitas_bongkar = $kapasitas_bongkar;
                }else{
                            /////////////////////////////////////////////
                            //rddmin+1
                            $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
                            $panjang_kode_distrik = strlen($kode_distriknya);
                            if($panjang_kode_distrik==4 or $panjang_kode_distrik=='4'){
                                $kodeprovgudang = $kode_distriknya;
                                $parameterconfiggudang="GUDANG_".$kodeprovgudang;
                            }else{
                                $kodeprovgudang = substr($kode_distriknya,0,2);
                                $parameterconfiggudang="GUDANG_10".$kodeprovgudang;
                            }
                            $mysql_gudang="SELECT
                                CONFIG
                            FROM
                                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                            WHERE
                                rownum BETWEEN 0 AND 1";
                            // echo $mysql_tglleadtimeplus;
                            $mysql_setgudang=oci_parse($conn,$mysql_gudang);
                            oci_execute($mysql_setgudang);
                            $row_setgudang=oci_fetch_assoc($mysql_setgudang);
                            $configbongkar=$row_setgudang[CONFIG]; 
                            //////////////
                            //////////////
                            /////////
                    //kurang rumus intransit;
                    if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null){
                        $mysqlconbongkar="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";

                        $mysql_configbongkar=oci_parse($conn,$mysqlconbongkar);
                        oci_execute($mysql_configbongkar);
                        $row_configbongkar=oci_fetch_assoc($mysql_configbongkar);
                        $configbongkar=$row_configbongkar[CONFIG];
                    }
 
                    if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null){

                        // $forcase_level_stok = 0-(0*$leadtime)+$intransit;
                        $forcase_level_stok = 0;
                        $kapasitas_gudang=$configbongkar;
                        $order_qty=$kapasitas_gudang-$forcase_level_stok;
                        $kapasitas_bongkar=$configbongkar;
                    }
                }

                ///////////////////////////
                $sqlgetpp="SELECT
                sum(or_trans_dtl.QTY_PP) as TOTPP
            FROM
                OR_TRANS_hdr
            JOIN or_trans_dtl ON
                or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
            WHERE
                (or_trans_dtl.STATUS_LINE = 'OPEN'
                    OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                    OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                AND or_trans_hdr.SOLD_TO = '$dist2'
                AND or_trans_hdr.DELETE_MARK = '0'
                AND or_trans_dtl.DELETE_MARK = '0'
                AND or_trans_dtl.KODE_TUJUAN = '$distrik'
                AND or_trans_dtl.SHIP_TO = '$kode_gudang_si'
                AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$tglleadtimepp'
                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'";


                $mysql_getpp=oci_parse($conn,$sqlgetpp);
                oci_execute($mysql_getpp);
                $row_getpp=oci_fetch_assoc($mysql_getpp);
                $getpp=$row_getpp[TOTPP];

                $final_qty = $order_qty - $getpp;
                

                ///////////////////////////
                // echo ($final_qty;
                if($final_qty>($kapasitas_bongkar-$getpp)){
                    $max_order_qty_socc=$kapasitas_bongkar-$getpp;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
                // echo $final_qty; echo " - ";
            print_r(floor($max_order_qty_socc));
    }
    break;
    case "getdatagudangcekmdxl";
    {

        //////
        $dist2=$_GET['soldin'];
        $dist = (int)$dist2;
        $tglleadtimepp = str_replace(" ","",$_GET['tgl_ldtime']);
        $distrik = $kode_distrik_in;
        $kode_distri_si= $dist2;
        $kode_gudang_si= $shipto;
        // $distrik='233001';
        // $kode_distri_si='0000000138';
        // $kode_gudang_si='1380016000';
        /////////////////////
                //////////////
                $mysqlperkapgudang="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
                $mysql_setperkapgudang=oci_parse($conn,$mysqlperkapgudang);
                oci_execute($mysql_setperkapgudang);
                $row_configperkapgudang=oci_fetch_assoc($mysql_setperkapgudang);
                $configperkapgudang=$row_configperkapgudang[CONFIG]; 
                if($configperkapgudang!=null or $configperkapgudang!=''){
                    $configperkapgudangfix = intval($configperkapgudang);
                }else{
                    $configperkapgudangfix = 100;
                }
                //////////////

        //////////////
        $mysql="SELECT
            STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        echo $mysql;

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
            $leadtime=$leadtimeso;
        }else{
            $leadtime=0;
        }

        // echo "leadtime".$leadtime;
        //////////////////////////////////////////////////////////////
        //intransit
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
            }
            $fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL");
            if ($fce == false ) {
                $sap->PrintStatus();
                exit;
            }

                        $fce->XVKORG = $_GET['orgin'];
                        $fce->XKUNNR2 = $shipto;
                        $fce->XBZIRK = $kode_distrik_in;

                            $hari_h = date('Ymd');
                            // $vdatuhigh = date('d.m.Y',strtotime());
                            $vdatulow = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
            
                            $fce->LR_EDATU->row["SIGN"] = 'I';
                            $fce->LR_EDATU->row["OPTION"] = 'BT';
                            $fce->LR_EDATU->row["LOW"] = $vdatulow;
                            $fce->LR_EDATU->row["HIGH"] = $hari_h;
                            $fce->LR_EDATU->Append($fce->LR_EDATU->row);



                    $fce->Call();
                    if ($fce->GetStatus() == SAPRFC_OK ) {
                        $fce->RETURN_DATA->Reset();
                        $s=0;
                        while ($fce->RETURN_DATA->Next()){
                            // $qty1[$s] = $fce->RETURN_DATA->row["KWMENG"];
                            // $qty2[$s]= $fce->RETURN_DATA->row["KWMENGX"];
                            $intransit += $fce->RETURN_DATA->row["RFMNG"];
                            // $s++;
                        }
                    }else {
                        $fce->PrintStatus();
                    }


                    $fce->Close();
                    $sap->Close();
                    // $totallog = count($qty2);


                    // $intransit = @array_sum($qty1);


                    
                    
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$kode_distri_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_soldto = json_decode($response, true);
        $adasoldto=false;
//         print_r($data_output_soldto);
        foreach ($data_output_soldto as $valuesoldto) {
            $kode_soldto_si_mdxl_temp = $valuesoldto['kode_distributor_mdxl'];
            $kode_soldto_si_mdxl = sprintf("%'010s",$kode_soldto_si_mdxl_temp);
//            $adasoldto=true;
            // $adashipto2=1;
        }
//             echo '<br>';
//            if($adashipto){
//                $mdxlshipto=$kode_gudang_si_mdxl;
//            }
//
//            if($mdxlshipto==NULL or $mdxlshipto==''){
//                $mdxlshipto=$kode_gudang_si;
//            }
//                 print_r($kode_soldto_si_mdxl);
        /////////////////////////////////////////////////////////////

//                     echo 'tetete'.$kode_soldto_si_mdxl.'rere'.$dist.'jiji';
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',            
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_shipto = json_decode($response, true);
        $adashipto=false;
        // print_r($data_output_shipto);
        foreach ($data_output_shipto as $valueshipto) {
            $kode_gudang_si_mdxl = $valueshipto['kode_gudang_mdxl'];
            $adashipto=true;
            // $adashipto2=1;
            }
            
           
            // echo '<br>';
            if($adashipto){
                $mdxlshipto=$kode_gudang_si_mdxl;
            }

            if($mdxlshipto==NULL or $mdxlshipto==''){
                $mdxlshipto=$kode_gudang_si;
            }
            
//                 print_r($mdxlshipto);
        /////////////////////////////////////////////////////////////
        // $datenow = date('Y-m-d');
        // $dateyst = date('Y-m-d', strtotime($datenow. ' - 30 days'));
        $curl = curl_init();
        //uat socc
        // $dist = '0000000106';

        curl_setopt_array($curl, array(
         //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&distributor='.$kode_soldto_si_mdxl.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        // CURLOPT_POSTFIELDS => array('end_date' => $datenow,'start_date' => $dateyst),
        ));

        $response = curl_exec($curl);

        curl_close($curl);


        
        $data_output = json_decode($response, true);
        $ada = false;
                                                            ///////////////////////////
                                                            ///////////////////////////
                                                            $sap = new SAPConnection();
                                                            $sap->Connect("../include/sapclasses/logon_data.conf");
                                                            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                                                            if ($sap->GetStatus() != SAPRFC_OK ) {
                                                            echo $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
                                                            if ($fce == false ) {
                                                            $sap->PrintStatus();
                                                            exit;
                                                            }
                                                            
                                                            //header entri
                                                            $fce->X_VKORG = '7900';
                                                            $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
                                                            $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
                                                            $fce->X_KUNNR = $dist2;
                                                            $fce->X_STATUS = '70';
                                                            $fce->X_BZIRK = $distrik;
                                                            ///////////////////////////
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7900';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            $fce->LRI_VKORG->row["SIGN"] = 'I';
                                                            $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                                            $fce->LRI_VKORG->row["LOW"] = '7000';
                                                            $fce->LRI_VKORG->row["HIGH"] = '';
                                                            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                                            ///////////////////////////                                
                                                            ///////////////////////////
                                                            $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                            $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                            $fce->LR_KODE_DA->row["LOW"] = $shipto;
                                                            $fce->LR_KODE_DA->row["HIGH"] = '';
                                                            $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
                                                            foreach ($data_output["items"] as $key => $value) {
                                                                if (count($value["shipto_info"]) !== 0) {
                                                                //     $adadimdxl = false;
                                                                // }else{
                                                                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                                                                    $rptrealallshipto = 0;
                                                                    foreach ($value["shipto_info"] as $key2 => $value2) {
                                                                        if($value2['kode_shipto']==$mdxlshipto){
                                                                            $shiptoinfomdxl=$value["shipto_info"];
                                                                            if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
                                                                                foreach ($value["shipto_info"] as $key3 => $value3) {
                                                                                    // print_r($value3['kode_shipto']);
                                                                                    ///////////////////////////
                                                                                    $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                                                    $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                                                    $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
                                                                                    $fce->LR_KODE_DA->row["HIGH"] = '';
                                                                                    $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);                        
                                                                                    ///////////////////////////
                                                                                    // $rptrealallshipto += $rptrealavgin;
                                
                                                                                }
                                                                            }
                                                                        }
                                                                        // print_r("datanya ".$value2['kode_shipto']);
                                                                    }
                                                                }
                                                            }
                            
                                                            // echo "<pre>";
                                                            // print_r($fce);
                                                            // echo "</pre>";
                                                                     
                                                             $fce->Call();	
                                                                 $fce->ZDATA->Reset();
                                                                 $rptrealavgin=0;
                                                                 while ( $fce->ZDATA->Next() ){
                                                                     $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
                                                                     print_r($fce->ZDATA->row["KWANTUM"]);
                                                                 }
                                                     
                                                             $fce->Close();	
                                                             $sap->Close();	            
                                                            //  $rptrealavgin = $rptrealavgin/7;
                                                            $realisasi_shipment = $rptrealavgin;
                                //////////////////////////////////////////////////////////////
        ////uat socc
        foreach ($data_output["items"] as $key => $value) {
            if (count($value["shipto_info"]) !== 0) {
            //     $adadimdxl = false;
            // }else{
            //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                foreach ($value["shipto_info"] as $key2 => $value2) {
                    // print_r("datanya ".$value2['kode_shipto']);
                    if($value2['kode_shipto']==$mdxlshipto){                    
                            $volume_stock_gudang=$value["volume_stock_gudang"];
                            $penjualan_avg=$value["penjualan_avg"];
                            $sell_out=$value["sell_out"];
                            // $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                            // echo "<br>";
                            // print_r("FLS awal : ".$forcase_level_stok);
                            // echo "<br>";
                            // $forcase_level_stok = $forcase_level_stok+$rptrealavgin;
                            ////////////////////////////////////////perubahan rumus 13/06/2023
                            // new
                            $forcase_level_stok = $volume_stock_gudang+$realisasi_shipment - ($penjualan_avg * $leadtime);
                            ///////////////////////////////////////////////////////////////
                            // old
                            ///////////////////////////////////////////////////////////////////////////////////// perubahan tanggal 08-12-2022
                                //             /////////////////////////////////////////////
                                //             $tgl_cek = date('Ymd', strtotime($tglleadtimepp. ' -'.$leadtime.' day'));
                                //             $tgl_cek2 = date('Ymd');
                                //             $tgl_qtyrddnya=false;
                                //             if($tgl_cek>$tgl_cek2){
                                //                 $tgl_qtyrddnya=true;
                                //             }
                                //         //////////////////////////////////////////////
                                // if($tgl_qtyrddnya){
                                //     $startTimeStamp1 = date("d-m-Y");
                                //     $startTimeStamp = strtotime($startTimeStamp1); 
                                //     $endTimeStamp = strtotime($tglleadtimepp);
                                //     $timeDiff = abs($endTimeStamp - $startTimeStamp);
                                //     $numberDays = $timeDiff/86400;  // 86400 seconds in one day
                                //     // and you might want to convert to integer
                                //     $numberDays = intval($numberDays);
                                //     $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime);   
                                //     $fls=$forcase_level_stok;                                 
                                //         $avg_sell_out_RDD=$numberDays*$penjualan_avg;
                                //         $fls_avg_out = $forcase_level_stok-$avg_sell_out_RDD;
                                //         $forcase_level_stok = $forcase_level_stok-$avg_sell_out_RDD;
                                //         $avg_sell_in_RDD=$numberDays*$rptrealavgin;
                                //         $forcase_level_stok = $forcase_level_stok+$avg_sell_in_RDD;
                                // }else{
                                //     $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                                // }
                                // ////////////////////////////////////////////////////////////////////////////////////
                            ////////////////////////////////////////////////////////////////////////////////////   
                            $mdxlkapasitas_gudang=$value["kapasitas_gudang"];
                            $kapasitas_gudang= ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
                            echo "kapasitas gudang ".$configperkapgudangfix."% dari ".$mdxlkapasitas_gudang." adalah : ".$kapasitas_gudang;
                            echo "<br>";
                            $order_qty=$kapasitas_gudang-$forcase_level_stok;
                            $kapasitas_bongkar=$value["kapasitas_bongkar"];
                            $ada=true;
                            // $ada2=1;
                        }
                    }
                }
            }

                /////////////////////////////////////////////////
                if($ada){
                    $order_qty = $order_qty;
                    $kapasitas_bongkar = $kapasitas_bongkar;
                }else{
                            /////////////////////////////////////////////
                            //rddmin+1
                            $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
                            $panjang_kode_distrik = strlen($kode_distriknya);
                            if($panjang_kode_distrik==4 or $panjang_kode_distrik=='4'){
                                $kodeprovgudang = $kode_distriknya;
                                $parameterconfiggudang="GUDANG_".$kodeprovgudang;
                            }else{
                                $kodeprovgudang = substr($kode_distriknya,0,2);
                                $parameterconfiggudang="GUDANG_10".$kodeprovgudang;
                            }
                            $mysql_gudang="SELECT
                                CONFIG
                            FROM
                                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                            WHERE
                                rownum BETWEEN 0 AND 1";
                            // echo $mysql_tglleadtimeplus;
                            $mysql_setgudang=oci_parse($conn,$mysql_gudang);
                            oci_execute($mysql_setgudang);
                            $row_setgudang=oci_fetch_assoc($mysql_setgudang);
                            $configbongkar=$row_setgudang[CONFIG]; 
                            //////////////
                            //////////////
                            /////////
                    //kurang rumus intransit;
                    if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null){
                        $mysqlconbongkar="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";

                        $mysql_configbongkar=oci_parse($conn,$mysqlconbongkar);
                        oci_execute($mysql_configbongkar);
                        $row_configbongkar=oci_fetch_assoc($mysql_configbongkar);
                        $configbongkar=$row_configbongkar[CONFIG];
                    }
 
                    if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null){

                        // $forcase_level_stok = 0-(0*$leadtime)+$intransit;
                        $forcase_level_stok = 0;
                        $kapasitas_gudang=$configbongkar;
                        $order_qty=$kapasitas_gudang-$forcase_level_stok;
                        $kapasitas_bongkar=$configbongkar;
                    }
                }

                ///////////////////////////
                $sqlgetpp="SELECT
                sum(or_trans_dtl.QTY_PP) as TOTPP
            FROM
                OR_TRANS_hdr
            JOIN or_trans_dtl ON
                or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
            WHERE
                (or_trans_dtl.STATUS_LINE = 'OPEN'
                    OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                    OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                AND or_trans_hdr.SOLD_TO = '$dist2'
                AND or_trans_hdr.DELETE_MARK = '0'
                AND or_trans_dtl.DELETE_MARK = '0'
                AND or_trans_dtl.KODE_TUJUAN = '$distrik'
                AND or_trans_dtl.SHIP_TO = '$kode_gudang_si'
                AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$tglleadtimepp'
                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'";


                $mysql_getpp=oci_parse($conn,$sqlgetpp);
                oci_execute($mysql_getpp);
                $row_getpp=oci_fetch_assoc($mysql_getpp);
                $getpp=$row_getpp[TOTPP];

                $final_qty = $order_qty - $getpp;
                

                ///////////////////////////
                // $final_qty='800';
                if($final_qty>($kapasitas_bongkar-$getpp)){
                    $max_order_qty_socc=$kapasitas_bongkar-$getpp;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
            print_r(floor($max_order_qty_socc));

            if($_GET['pengecekanqtypp']){
                if($realisasi_shipment==null or $realisasi_shipment==''){
                    $realisasi_shipment=0;
                }
                if($max_order_qty_socc<0){
                    $max_order_qty_socc=0;
                }
                $lebihdarikapasitasbongkar = false;
                if($final_qty>($kapasitas_bongkar-$getpp)){
                    $max_order_qty_socc=$kapasitas_bongkar-$getpp;
                    $lebihdarikapasitasbongkar = true;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
                if($getpp=='' or $getpp==null){
                    $getpp = 0;
                }
                if($ada){
                    print_r("#");
                    print_r(floor($volume_stock_gudang));
                    print_r("#");
                    print_r(floor($penjualan_avg));
                    print_r("#");
                    print_r(floor($leadtime));
                    print_r("#");
                    print_r(floor($realisasi_shipment));
                    print_r("#");
                    print_r(floor($kapasitas_gudang));
                    print_r("#");
                    print_r(floor($forcase_level_stok));
                    print_r("#");
                    print_r(floor($order_qty));
                    print_r("#");
                    print_r(floor($getpp));
                    print_r("#");
                    print_r(floor($kapasitas_bongkar));
                    print_r("#");
                    if($lebihdarikapasitasbongkar){
                        print_r("<br>");
                        print_r("<br>");
                        print_r("*QTY LS Max Order lebih besar dari kemampuan bongkar, maka QTY yang bisa diorder adalah = ".$max_order_qty_socc." (sejumlah kapasitas sisa kemampuan bongkar)");
                    }else{
                        print_r("<br>");
                        print_r("<br>");
                        print_r("*QTY LS Max Order lebih kecil dari kemampuan bongkar, maka QTY yang bisa diorder adalah = ".$max_order_qty_socc." (sejumlah QTY LS MAX)");
                    }                    
                    print_r("#");
                    print_r("<br>");
                    print_r("<br>");
                    // if($tgl_qtyrddnya){
                    //     print_r("forecast_level_stok = volume_stock_gudang - (penjualan_avg X leadtime)");
                    //     print_r("<br>");
                    //     print_r($fls." = ".$volume_stock_gudang." -( ".$penjualan_avg." * ".$leadtime." )");
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     print_r("rumus tambahan (bukan RDD min leadtime, dan shipto ada di mdxl)");
                    //     print_r("<br>");
                    //     print_r("------------------------------------------------------------------------------");
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     print_r("avg_sell_out_RDD = Selisih Hari (RDD-hari H) * penjualan_AVG");
                    //     print_r("<br>");
                    //     print_r($avg_sell_out_RDD." = ".$numberDays." * ".$penjualan_avg);
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     print_r("FLS_RDD = FLS - avg_sell_out_RDD");
                    //     print_r("<br>");    
                    //     print_r($fls_avg_out." = ".$fls." - ".$avg_sell_out_RDD);
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     print_r("avg_sell_in_RDD = Selisih Hari (RDD-hari H) * avg selling in");
                    //     print_r("<br>");
                    //     print_r($avg_sell_in_RDD." = ".$numberDays." * ".$rptrealavgin);
                    //     print_r("<br>");
                    //     print_r("<br>");
                    //     echo "<br>";
                    //     print_r("FLS awal + AVG selling in");
                    //     echo "<br>";
                    //     print_r($fls_avg_out." + ".$avg_sell_in_RDD." : ".$forcase_level_stok);
                    //     echo "<br>";
                    //     print_r("------------------------------------------------------------------------------");
                    //     print_r("<br>");
                    // }else{
                        print_r("forecast_level_stok = volume_stock_gudang + Intransit - (penjualan_avg X leadtime)");
                        print_r("<br>");
                        print_r($forcase_level_stok." = ".$volume_stock_gudang." + ".$realisasi_shipment." - ( ".$penjualan_avg." * ".$leadtime." )");
                        print_r("<br>");
                        // print_r("FLS avg selling in ");                
                        // print_r("<br>");
                        // print_r($forcase_level_stok."=".$forcase_level_stok." + ".$rptrealavgin);
                        print_r("<br>");
                        print_r("<br>");
                    // }
                    print_r("<br>");
                    print_r("<br>");
                    print_r("order_qty = kapasitas gudang - forecast level stock");                
                    print_r("<br>");
                    print_r($order_qty."=".$kapasitas_gudang." - ".$forcase_level_stok);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("Final Order QTY PP = order qty - jumlah total PP terbentuk di tgl RDD yg sama");                    
                    print_r("<br>");                    
                    print_r($final_qty." = ".$order_qty." - ".$getpp);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("QTY mula yang bisa diorder = ".$final_qty);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("Kapasitas Bongkar = ".$kapasitas_bongkar);
                    print_r("#");
                    // if($lebihdarikapasitasbongkar){
                    //     print_r("dikarenakan QTY LS Max Order lebih besar dari kemampuan bongkar maka QTY yang bisa di order adalah = ".$max_order_qty_socc)." (sejumlah kapasitas sisa kemampuan bongkar)";
                    // }else{
                        print_r("<br>");
                        print_r("<br>");
                        print_r("QTY Max yang bisa diorder = ".$max_order_qty_socc);
                    // }
                }else{
                    print_r("#");
                    print_r(floor($volume_stock_gudang));
                    print_r("#");
                    print_r(floor($penjualan_avg));
                    print_r("#");
                    print_r(floor($leadtime));
                    print_r("#");
                    print_r(floor($intransit));
                    print_r("#");
                    print_r(floor($kapasitas_gudang));
                    print_r("#");
                    print_r(floor($forcase_level_stok));
                    print_r("#");
                    print_r(floor($order_qty));
                    print_r("#");
                    print_r(floor($getpp));
                    print_r("#");
                    print_r(floor($kapasitas_bongkar));
                    print_r("#");
                    if($lebihdarikapasitasbongkar){
                        print_r("<br>");
                        print_r("<br>");
                        print_r("*QTY LS Max Order lebih besar dari kemampuan bongkar, maka QTY yang bisa diorder adalah = ".$max_order_qty_socc." (sejumlah kapasitas sisa kemampuan bongkar)");
                    }else{
                        print_r("<br>");
                        print_r("<br>");
                        print_r("*QTY LS Max Order lebih kecil dari kemampuan bongkar, maka QTY yang bisa diorder adalah = ".$max_order_qty_socc." (sejumlah QTY LS MAX)");
                    }                    
                    print_r("#");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("forecast_level_stok = volume_stock_gudang - (penjualan_avg X leadtime) + intransit");
                    print_r("<br>");
                    print_r($forcase_level_stok." = "."0"." -( "."0"." * ".$leadtime." )+ ".$intransit);
                    print_r("<br>");
                    print_r("<br>");
                    // print_r("FLS avg selling in ");                
                    // print_r("<br>");
                    // print_r($forcase_level_stok."=".$forcase_level_stok." + ".$rptrealavgin);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("order_qty = kapasitas gudang - forecast level stock");                
                    print_r("<br>");
                    print_r($order_qty."=".$kapasitas_gudang." - ".$forcase_level_stok);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("Final Order QTY PP = order qty - jumlah total PP terbentuk di tgl RDD yg sama");                    
                    print_r("<br>");                    
                    print_r($final_qty." = ".$order_qty." - ".$getpp);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("<br>");
                    print_r("QTY mula yang bisa diorder = ".$final_qty);
                    print_r("<br>");
                    print_r("<br>");
                    print_r("Kapasitas Bongkar = ".$kapasitas_bongkar);
                    print_r("#");
                    // if($lebihdarikapasitasbongkar){
                    //     print_r("dikarenakan QTY LS Max Order lebih besar dari kemampuan bongkar maka QTY yang bisa di order adalah = ".$max_order_qty_socc)." (sejumlah kapasitas sisa kemampuan bongkar)";
                    // }else{
                        print_r("<br>");
                        print_r("<br>");
                        print_r("QTY Max yang bisa diorder = ".$max_order_qty_socc);
                    // }
                }


                //print_r("#".floor($max_order_qty_socc)."#".$intransit."#".$getpp."#".$kapasitas_bongkar."#".$volume_stock_gudang."#".$penjualan_avg."#".$kapasitas_gudang."#".$leadtime);
            }
    }
    break;
    case "getdatasimulasigudangcekmdxl";
    {

        //////
        $dist2=$_GET['soldin'];
        $orgsocc=$_GET['orgin'];
        $dist = (int)$dist2;
        $tglleadtimeppqty = str_replace(" ","",$_GET['tgl_ldtime']);
        if($tglleadtimeppqty=='' or $tglleadtimeppqty==null){
            $tglleadtimeppqty=date('d-m-Y');
        }
        $distrik = $kode_distrik_in;
        $kode_distri_si= $dist2;
        $kode_gudang_si= $shipto;
        // $distrik='233001';
        // $kode_distri_si='0000000138';
        // $kode_gudang_si='1380016000';
        ////////////////////
                //////////////
                $mysqlperkapgudang="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
                $mysql_setperkapgudang=oci_parse($conn,$mysqlperkapgudang);
                oci_execute($mysql_setperkapgudang);
                $row_configperkapgudang=oci_fetch_assoc($mysql_setperkapgudang);
                $configperkapgudang=$row_configperkapgudang[CONFIG]; 
                if($configperkapgudang!=null or $configperkapgudang!=''){
                    $configperkapgudangfix = intval($configperkapgudang);
                }else{
                    $configperkapgudangfix = 100;
                }
                //////////////

        //////////////
        $mysql="SELECT
            STANDART_AREA
        FROM
            (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in' and delete_mark='0' ORDER BY id desc)
        WHERE
            rownum BETWEEN 0 AND 1";

        $mysql_set=oci_parse($conn,$mysql);
        oci_execute($mysql_set);
        $row_leadtime=oci_fetch_assoc($mysql_set);
        $leadtimeso=$row_leadtime[STANDART_AREA]; 

        if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
            $leadtime=$leadtimeso;
        }else{
            $leadtime=0;
        }

        // echo "leadtime".$leadtime;
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$kode_distri_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',            
             CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$dist2.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_soldto = json_decode($response, true);
        $adasoldto=false;
//         print_r($data_output_soldto);
        foreach ($data_output_soldto as $valuesoldto) {
            $kode_soldto_si_mdxl_temp = $valuesoldto['kode_distributor_mdxl'];
            $kode_soldto_si_mdxl = sprintf("%'010s",$kode_soldto_si_mdxl_temp);
//            $adasoldto=true;
            // $adashipto2=1;
        }
//             echo '<br>';
//            if($adashipto){
//                $mdxlshipto=$kode_gudang_si_mdxl;
//            }
//
//            if($mdxlshipto==NULL or $mdxlshipto==''){
//                $mdxlshipto=$kode_gudang_si;
//            }
//                 print_r($kode_soldto_si_mdxl);
        /////////////////////////////////////////////////////////////

//                     echo 'tetete'.$kode_soldto_si_mdxl.'rere'.$dist.'jiji';
        //////////////////////////////////////////////////////////////
        $curl = curl_init();
        curl_setopt_array($curl, array(
             //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',         
             CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A',   
        //   CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$kode_gudang_si,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $data_output_shipto = json_decode($response, true);
        $adashipto=false;
        // print_r($data_output_shipto);
        foreach ($data_output_shipto as $valueshipto) {
            $kode_gudang_si_mdxl = $valueshipto['kode_gudang_mdxl'];
            $adashipto=true;
            // $adashipto2=1;
            }
            
           
            // echo '<br>';
            if($adashipto){
                $mdxlshipto=$kode_gudang_si_mdxl;
            }

            if($mdxlshipto==NULL or $mdxlshipto==''){
                $mdxlshipto=$kode_gudang_si;
            }
            
//                 print_r($mdxlshipto);
        /////////////////////////////////////////////////////////////
        // $datenow = date('Y-m-d');
        // $dateyst = date('Y-m-d', strtotime($datenow. ' - 30 days'));
        $curl = curl_init();
        //uat socc
        // $dist = '0000000106';

        curl_setopt_array($curl, array(
         //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&distributor='.$kode_soldto_si_mdxl.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
        // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_URL => 'http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        // CURLOPT_POSTFIELDS => array('end_date' => $datenow,'start_date' => $dateyst),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $data_output = json_decode($response, true);
        ///////////////////////////////////////////////////////////////////////////////////////
        $mysqlumurso="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='UMUR_SO' and delete_mark='0'";

        $mysql_setumurso=oci_parse($conn,$mysqlumurso);
        oci_execute($mysql_setumurso);
        $row_configumurso=oci_fetch_assoc($mysql_setumurso);
        $configsoumurso=$row_configumurso[CONFIG]; 
            //////////////////////////////////////////////////////////////            
        //intransit
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
        echo $sap->PrintStatus();
        exit;
        }
        $fce = $sap->NewFunction ("Z_ZAPPSD_SO_ALL");
        if ($fce == false ) {
            $sap->PrintStatus();
            exit;
        }

                    $fce->XVKORG = $_GET['orgin'];
                    $fce->XKUNNR2 = $shipto;
                    $fce->XBZIRK = $kode_distrik_in;

                        $hari_h = date('Ymd', strtotime($tglleadtimeppqty));
                        // $vdatuhigh = date('d.m.Y',strtotime());
                        $vdatulow = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
        
                        $fce->LR_EDATU->row["SIGN"] = 'I';
                        $fce->LR_EDATU->row["OPTION"] = 'BT';
                        $fce->LR_EDATU->row["LOW"] = $vdatulow;
                        $fce->LR_EDATU->row["HIGH"] = $hari_h;
                        $fce->LR_EDATU->Append($fce->LR_EDATU->row);



                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {
                    $fce->RETURN_DATA->Reset();
                    $s=0;
                    $intransit=0;
                    while ($fce->RETURN_DATA->Next()){
                        // $qty1[$s] = $fce->RETURN_DATA->row["KWMENG"];
                        // $qty2[$s]= $fce->RETURN_DATA->row["KWMENGX"];
                        $intransit += $fce->RETURN_DATA->row["RFMNG"];
                        // $s++;
                    }
                }else {
                    $fce->PrintStatus();
                }


                $fce->Close();
                $sap->Close();
                // $totallog = count($qty2);


                // $intransit = @array_sum($qty1);

                                ///////////////////////////
                                $sap = new SAPConnection();
                                $sap->Connect("../include/sapclasses/logon_data.conf");
                                if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                                if ($sap->GetStatus() != SAPRFC_OK ) {
                                echo $sap->PrintStatus();
                                exit;
                                }
                                $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
                                if ($fce == false ) {
                                $sap->PrintStatus();
                                exit;
                                }
                                
                                //header entri
                                $fce->X_VKORG = '7900';
                                $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtime.' day'));
                                $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
                                $fce->X_KUNNR = $dist2;
                                $fce->X_STATUS = '70';
                                $fce->X_BZIRK = $distrik;
                                ///////////////////////////
                                $fce->LRI_VKORG->row["SIGN"] = 'I';
                                $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                $fce->LRI_VKORG->row["LOW"] = '7900';
                                $fce->LRI_VKORG->row["HIGH"] = '';
                                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                $fce->LRI_VKORG->row["SIGN"] = 'I';
                                $fce->LRI_VKORG->row["OPTION"] = 'EQ';
                                $fce->LRI_VKORG->row["LOW"] = '7000';
                                $fce->LRI_VKORG->row["HIGH"] = '';
                                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);                        
                                ///////////////////////////                                
                                ///////////////////////////
                                $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                $fce->LR_KODE_DA->row["LOW"] = $shipto;
                                $fce->LR_KODE_DA->row["HIGH"] = '';
                                $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
                                foreach ($data_output["items"] as $key => $value) {
                                    if (count($value["shipto_info"]) !== 0) {
                                    //     $adadimdxl = false;
                                    // }else{
                                    //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                                        $rptrealallshipto = 0;
                                        foreach ($value["shipto_info"] as $key2 => $value2) {
                                            if($value2['kode_shipto']==$mdxlshipto){
                                                $shiptoinfomdxl=$value["shipto_info"];
                                                if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
                                                    foreach ($value["shipto_info"] as $key3 => $value3) {
                                                        // print_r($value3['kode_shipto']);
                                                        ///////////////////////////
                                                        $fce->LR_KODE_DA->row["SIGN"] = 'I';
                                                        $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
                                                        $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
                                                        $fce->LR_KODE_DA->row["HIGH"] = '';
                                                        $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);                        
                                                        ///////////////////////////
                                                        // $rptrealallshipto += $rptrealavgin;
    
                                                    }
                                                }
                                            }
                                            // print_r("datanya ".$value2['kode_shipto']);
                                        }
                                    }
                                }

                                // echo "<pre>";
                                // print_r($fce);
                                // echo "</pre>";
                                         
                                 $fce->Call();	
                                     $fce->ZDATA->Reset();
                                     $rptrealavgin=0;
                                     while ( $fce->ZDATA->Next() ){
                                         $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
                                         // print_r($fce->ZDATA->row["KWANTUM"]);
                                     }
                         
                                 $fce->Close();	
                                 $sap->Close();	            
                                                            //  $rptrealavgin = $rptrealavgin/7;
                                                            $realisasi_shipment = $rptrealavgin;
    //////////////////////////////////////////////////////////////
        ///////////////////////////////////////////////////////////////////////////////////////
        // $hari_ini = date('Ymd');
        // $hari_ini_qty = date('Ymd', strtotime($hari_ini. ' +'.$leadtime.' day')); 
        for ($loop = 1; $loop <= 14; $loop++) {
            // echo "<br>";
            // echo $loop;
            // echo "<br>";

            if($loop>='2' or $loop>=2){
                $looping=$loop-1;
                $tglleadtimepp=date('d-m-Y', strtotime($tglleadtimepploopke2. ' +'.$looping.' day'));
            }else{
                $tglleadtimepploopke2 = date('Ymd', strtotime($tglleadtimeppqty));
                $tglleadtimepp=date('d-m-Y',strtotime($tglleadtimeppqty));
            }
            

            // echo "<br>";
            // print_r($tglleadtimepp);
            // echo "<br>";
            $ada = false;
            foreach ($data_output["items"] as $key => $value) {
                if (count($value["shipto_info"]) !== 0) {
                //     $adadimdxl = false;
                // }else{
                //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
                    foreach ($value["shipto_info"] as $key2 => $value2) {
                        // print_r("datanya ".$value2['kode_shipto']);
                        if($value2['kode_shipto']==$mdxlshipto){                    
                            $volume_stock_gudang=$value["volume_stock_gudang"];
                            $penjualan_avg=$value["penjualan_avg"];
                            $sell_out=$value["sell_out"];
                            // $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                            // $forcase_level_stok = $forcase_level_stok+$rptrealavgin;
                            ////////////////////////////////////////perubahan rumus 13/06/2023
                            // new
                            $forcase_level_stok = $volume_stock_gudang+$realisasi_shipment - ($penjualan_avg * $leadtime);
                            ///////////////////////////////////////////////////////////////
                            // old                            
                            ///////////////////////////////////////////////////////////////////////////////////// perubahan tanggal 08-12-2022
                                //             /////////////////////////////////////////////
                                //             $tgl_cek = date('Ymd', strtotime($tglleadtimepp. ' -'.$leadtime.' day'));
                                //             $tgl_cek2 = date('Ymd');
                                //             $tgl_qtyrddnya=false;
                                //             if($tgl_cek>$tgl_cek2){
                                //                 $tgl_qtyrddnya=true;
                                //             }
                                //         //////////////////////////////////////////////
                                // if($tgl_qtyrddnya){
                                //     $startTimeStamp1 = date("d-m-Y");
                                //     $startTimeStamp = strtotime($startTimeStamp1); 
                                //     $endTimeStamp = strtotime($tglleadtimepp);
                                //     $timeDiff = abs($endTimeStamp - $startTimeStamp);
                                //     $numberDays = $timeDiff/86400;  // 86400 seconds in one day
                                //     // and you might want to convert to integer
                                //     $numberDays = intval($numberDays);
                                //         $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime);
                                //         $fls=$forcase_level_stok;                                    
                                //         $avg_sell_out_RDD=$numberDays*$penjualan_avg;
                                //         $forcase_level_stok = $forcase_level_stok-$avg_sell_out_RDD;
                                //         $avg_sell_in_RDD=$numberDays*$rptrealavgin;
                                //         $forcase_level_stok = $forcase_level_stok+$avg_sell_in_RDD;
                                //     }else{
                                //         $forcase_level_stok = $volume_stock_gudang-($penjualan_avg*$leadtime)+$intransit;
                                //     }
                                // ////////////////////////////////////////////////////////////////////////////////////
                            ////////////////////////////////////////////////////////////////////////////////////   
                            $mdxlkapasitas_gudang=$value["kapasitas_gudang"];
                            // $mdxlkapasitas_gudang= (90 / 100) * $mdxlkapasitas_gudang;
                            $kapasitas_gudang= ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
                            $order_qty=$kapasitas_gudang-$forcase_level_stok;
                            $kapasitas_bongkar=$value["kapasitas_bongkar"];
                            $ada=true;
                            // $ada2=1;
                        }
                    }
                }
            }

                /////////////////////////////////////////////////
                if($ada){
                    $order_qty = $order_qty;
                    $kapasitas_bongkar = $kapasitas_bongkar;
                }else{
                            /////////////////////////////////////////////
                            //rddmin+1
                            $kode_distriknya = str_replace(' ', '', $kode_distrik_in);
                            $panjang_kode_distrik = strlen($kode_distriknya);
                            if($panjang_kode_distrik==4 or $panjang_kode_distrik=='4'){
                                $kodeprovgudang = $kode_distriknya;
                                $parameterconfiggudang="GUDANG_".$kodeprovgudang;
                            }else{
                                $kodeprovgudang = substr($kode_distriknya,0,2);
                                $parameterconfiggudang="GUDANG_10".$kodeprovgudang;
                            }
                            $mysql_gudang="SELECT
                                CONFIG
                            FROM
                                (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                            WHERE
                                rownum BETWEEN 0 AND 1";
                            // echo $mysql_tglleadtimeplus;
                            $mysql_setgudang=oci_parse($conn,$mysql_gudang);
                            oci_execute($mysql_setgudang);
                            $row_setgudang=oci_fetch_assoc($mysql_setgudang);
                            $configbongkar=$row_setgudang[CONFIG]; 
                            //////////////
                            //////////////
                            /////////
                    //kurang rumus intransit;
                    if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null){
                        $mysqlconbongkar="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";

                        $mysql_configbongkar=oci_parse($conn,$mysqlconbongkar);
                        oci_execute($mysql_configbongkar);
                        $row_configbongkar=oci_fetch_assoc($mysql_configbongkar);
                        $configbongkar=$row_configbongkar[CONFIG];
                    }
 
                    if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null){

                        // $forcase_level_stok = 0-(0*$leadtime)+$intransit;
                        $forcase_level_stok = 0;
                        $kapasitas_gudang=$configbongkar;
                        $order_qty=$kapasitas_gudang-$forcase_level_stok;
                        $kapasitas_bongkar=$configbongkar;
                    }
                }


                $sqlgetpp="SELECT
                sum(or_trans_dtl.QTY_PP) as TOTPP
            FROM
                OR_TRANS_hdr
            JOIN or_trans_dtl ON
                or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
            WHERE
                (or_trans_dtl.STATUS_LINE = 'OPEN'
                    OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                    OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                AND or_trans_hdr.SOLD_TO = '$dist2'
                AND or_trans_hdr.DELETE_MARK = '0'
                AND or_trans_dtl.DELETE_MARK = '0'
                AND or_trans_dtl.KODE_TUJUAN = '$distrik'
                AND or_trans_dtl.SHIP_TO = '$kode_gudang_si'
                AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$tglleadtimepp'
                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'";


                $mysql_getpp=oci_parse($conn,$sqlgetpp);
                oci_execute($mysql_getpp);
                $row_getpp=oci_fetch_assoc($mysql_getpp);
                $getpp=$row_getpp[TOTPP];

                $final_qty = $order_qty - $getpp;
                

                ///////////////////////////
                // $final_qty='800';
                if($final_qty>($kapasitas_bongkar-$getpp)){
                    $max_order_qty_socc=$kapasitas_bongkar-$getpp;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
            // print_r(floor($max_order_qty_socc));

            if($_GET['pengecekanqtypp']){
                if($realisasi_shipment==null or $realisasi_shipment==''){
                    $realisasi_shipment=0;
                }
                if($max_order_qty_socc<0){
                    $max_order_qty_socc=0;
                }
                $lebihdarikapasitasbongkar = false;
                if($final_qty>($kapasitas_bongkar-$getpp)){
                    $max_order_qty_socc=$kapasitas_bongkar-$getpp;
                    $lebihdarikapasitasbongkar = true;
                }else{
                    $max_order_qty_socc=$final_qty;
                }
                if($getpp=='' or $getpp==null){
                    $getpp = 0;
                }
                // $x=$loop;
                // $y=$loop+1;
                // while($x = $loop) {
                $result[$loop]['TGLRDD'] = $tglleadtimepp;
                $result[$loop]['VOLUME_STOCK_GUDANG'] = number_format($volume_stock_gudang,2);
                $result[$loop]['PENJUALAN_AVG'] = number_format($penjualan_avg,2);
                $result[$loop]['PENJUALAN_AVG_IN']=number_format($rptrealavgin,2);
                $result[$loop]['LEADTIME'] = $leadtime;
                $result[$loop]['INTRANSIT'] = number_format($realisasi_shipment,2);
                $result[$loop]['KAPASITAS_GUDANG'] = number_format($kapasitas_gudang,2);
                $result[$loop]['FORCASE_LEVEL_STOCK'] = number_format($forcase_level_stok,2);
                $result[$loop]['ORDER_QTY'] = number_format($order_qty,2);
                $result[$loop]['GETPP'] = number_format($getpp,2);
                $result[$loop]['KAPASITAS_BONGKAR'] = number_format($kapasitas_bongkar,2);
                $result[$loop]['SLOT_QTY_PP'] = number_format($max_order_qty_socc,2);

                // // $result[$loop]['HISTORY_DATE'] = ;
                // // $result[$loop]['USERS'] = ;
                // // $result[$loop]['HISTORY_STATUS'] = ;
                // // $result[$loop]['STATUS_UPDATE'] = ;
                // $x++;
                // }
            }
        }
        $i=0;
        // echo json_encode($result);
        foreach($result as $key=>$value) {
            $hasil[$i]['TGLRDD'] = date('d-m-Y', strtotime($value['TGLRDD']));
            $hasil[$i]['VOLUME_STOCK_GUDANG'] = $value['VOLUME_STOCK_GUDANG'];
            $hasil[$i]['PENJUALAN_AVG'] = $value['PENJUALAN_AVG'];
            $hasil[$i]['PENJUALAN_AVG_IN'] = $value['PENJUALAN_AVG_IN'];
            $hasil[$i]['LEADTIME'] = $value['LEADTIME'];
            $hasil[$i]['INTRANSIT'] = $value['INTRANSIT'];
            $hasil[$i]['KAPASITAS_GUDANG'] = $value['KAPASITAS_GUDANG'];
            $hasil[$i]['FORCASE_LEVEL_STOCK'] = $value['FORCASE_LEVEL_STOCK'];
            $hasil[$i]['ORDER_QTY'] = $value['ORDER_QTY'];
            $hasil[$i]['GETPP'] = $value['GETPP'];
            $hasil[$i]['KAPASITAS_BONGKAR'] =$value['KAPASITAS_BONGKAR'];
            $hasil[$i]['SLOT_QTY_PP'] = $value['SLOT_QTY_PP'];
            $i++;
        }
        echo json_encode($hasil);        
    }
    break;
    // case "getdatashipto";
    // {
    //     // $cekdata=setPlanningDist($conn,$user_org_in,$sold_to_in,$kode_distrik_in,$produk_in,$plant,$qty_in,$tgl_kirim_in,$lelangflagca,$approvelelangca,$tgl1nya_in,$action_ca);
    //     // $jmldata=count($cekdata);
    //     //     unset($kotaayy); 
    //         //  $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE PLANT = '".$plant."' AND DELETE_MARK = 0";
    //         // $qplant = oci_parse($conn, $sqlplant);
    //         // oci_execute($qplant);
    //         // $rplant = oci_fetch_assoc($qplant);
    //         // $planharian = $rplant['PLANT'];
    //         // echo $jmlkota;
    //         echo '  <select name="kdshipto" id="kdshipto">
    //                     <option value="">---Pilih Kota---</option>
    //                     ';
    //                         //oracle staging 
                                
    //                             $sql = "SELECT DISTINCT KUNN2, SHIPTO_NAME, SHIPTO_ADDR, NAME1, STRAS, BZIRK, BZTXT, VKBUR, BEZEB, KVGR1, PALLET, INCO1 FROM RFC_Z_ZCSD_SHIPTO WHERE NOT EXISTS (SELECT t2.KODE_SHIP_TO FROM OR_DISTBLOCK t2 WHERE t2.PLANT = '7911' and t2.DELETE_MARK = 0 and KUNN2 = t2.KODE_SHIP_TO) AND KUNN2 NOT IN ( SELECT t3.KODE_SHIPTO FROM MASTER_CUSTOMER_REPLENISHMENT t3 WHERE DEL = '0' AND SUBSTR(ORG, 1, 1) = SUBSTR('7911', 1, 1) ) AND ZKTOKD1 = 'ZSG1'AND ZKTOKD2 = 'ZSG2' AND KUNNR = '0000000138' AND BZIRK = '251001' ORDER BY KUNN2";
    //                             // $sql = "SELECT DISTINCT KUNN2, SHIPTO_NAME, SHIPTO_ADDR, NAME1, STRAS, BZIRK, BZTXT,
    //                             // VKBUR, BEZEB, KVGR1, PALLET, INCO1 FROM RFC_Z_ZCSD_SHIPTO 
    //                             // WHERE NOT EXISTS (SELECT t2.KODE_SHIP_TO FROM OR_DISTBLOCK t2 WHERE t2.PLANT = '".$hidplant."' and t2.DELETE_MARK = 0 and KUNN2 = t2.KODE_SHIP_TO)
    //                             // $map_replenisment
    //                             // AND ZKTOKD1 = '".$org1."'AND ZKTOKD2 = '".$org2."' AND KUNNR = '".$soldto."'";
                                
    //                             $query= oci_parse($conn, $sql);
    //                             oci_execute($query);
    //                                 while($datafunc=oci_fetch_assoc($query)){
    //                                     // $kodeprop = '10'.substr($datafunc['BZIRK'], 0,2);
    //                                             $kotaSAP[$datafunc["KUNN2"]]=array($datafunc["KUNN2"]);
    //                                 }
                            
    //                         foreach($kotaSAP as $kyKt => $valsap){
    //                             echo '<option value="'.$kyKt.'" data-prop="'.$valsap[0].'">'.$kyKt.' '.$valsap[0].'</option>';
    //                         }
    //             echo  '</select>';
    // }
    // break;
}
// $sap->Close();			

?>
