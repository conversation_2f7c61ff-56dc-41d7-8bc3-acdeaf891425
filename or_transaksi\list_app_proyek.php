<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=198;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];

$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}

 if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
} 

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="list_app_proyek.php";
$no_pp = $_POST['no_pp'];
$no_pp = $fungsi->sapcode($no_pp);
$branch_plant = $_POST['branch_plant'];
$sold_to = $_POST['sold_to'];
$sold_to = $fungsi->sapcode($sold_to);
$ship_to = $_POST['ship_to'];
$produk = $_POST['produk'];
$jenis_kirim = $_POST['jenis_kirim'];

$currentPage="list_app_proyek.php";
$komen="";
if(isset($_POST['cari'])){
        
        //parameter tipe pp
        unset($tipeflagpp);
        if($user_org=='2000' || $user_org=='7000'){
            $tipeflagpp=" AND (TIPEPP is not null or KODE_PRODUK like '121-302%') ";
        }
        
	if($no_pp=="" and $branch_plant=="" and $sold_to == "" and $ship_to == "" and $produk == "" and $jenis_kirim == ""){
		//$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE DELETE_MARK = '0' AND SO_TYPE='ZPR' AND KD_REASON is null AND ORG='$user_org' $tipeflagpp AND STATUS_LINE IN ('OPEN','PROCESS') AND KD_PROV IN (SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0) ORDER BY NO_PP ASC";
                $sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY')
            as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE KODE_PRODUK not like '121-301%' AND DELETE_MARK = '0' AND SO_TYPE='ZPR' AND KD_REASON is null
            AND ORG IN ($inorg) $tipeflagpp AND STATUS_LINE IN ('OPEN','PROCESS') AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0) AND (NOTE != 'ics_pp' OR NOTE IS NULL)
            ORDER BY NO_PP ASC";
	}else {
		$pakeor=0;
		//$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE STATUS_LINE IN ('OPEN','PROCESS') AND SO_TYPE='ZPR' AND KD_REASON is null AND ORG='$user_org' AND ";
                $sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY')
                as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE KODE_PRODUK not like '121-301%' AND STATUS_LINE IN ('OPEN','PROCESS') AND SO_TYPE='ZPR' AND KD_REASON is null
                AND ORG IN ($inorg) AND ";
		if($no_pp!=""){
			if($pakeor==1){
			$sql.=" NO_PP LIKE '$no_pp' ";
			}else{
			$sql.=" NO_PP LIKE '$no_pp' ";
			$pakeor=1;
			}
		}
		if($branch_plant!=""){
			if($pakeor==1){
			$sql.=" AND BPLANT LIKE '$branch_plant' ";
			}else{
			$sql.=" BPLANT LIKE '$branch_plant' ";
			$pakeor=1;
			}
		}
		if($sold_to!=""){
			if($pakeor==1){
			$sql.=" AND SOLD_TO LIKE '$sold_to' ";
			}else{
			$sql.=" SOLD_TO LIKE '$sold_to' ";
			$pakeor=1;
			}
		}
		if($ship_to!=""){
			if($pakeor==1){
			$sql.=" AND SHIP_TO LIKE '$ship_to' ";
			}else{
			$sql.=" SHIP_TO LIKE '$ship_to' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KODE_PRODUK LIKE '$produk' ";
			}else{
			$sql.=" KODE_PRODUK LIKE '$produk' ";
			$pakeor=1;
			}
		}
		if($jenis_kirim!=""){
			if($pakeor==1){
			$sql.=" AND INCOTERM LIKE '$jenis_kirim' ";
			}else{
			$sql.=" INCOTERM LIKE '$jenis_kirim' ";
			$pakeor=1;
			}
		}
		$sql.=" AND DELETE_MARK = '0' $tipeflagpp AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0) ORDER BY NO_PP ASC";
	}

	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_pp_v[]=$row[NO_PP];
		$tgl_kirim_v[]=$row[TGL_KIRIM_PP];
		$tgl_pp_v[]=$row[TGL_PP];
		$produk_v[]=$row[NAMA_PRODUK];
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[NAMA_SHIP_TO];
		$alamat_v[]=$row[ALAMAT_SHIP_TO];
		$kddistrik_v[]=$row[KODE_TUJUAN];
		$nmdistrik_v[]=$row[NAMA_TUJUAN];
		$qty_v[]=$row[QTY_PP];
		$id_v[]=$row[ID];  
		$itemnum_v[]=$row[ITEM_NUMBER];  
		$status_v[]=$row[STATUS_LINE];
                $tipeorder_v[]=$row[TIPEPP];  
				$royalty_v[]=$row[IS_ROYALTY];  
								                $msa_v[]=$row[IS_MSA];  
		$plant_v[]=$row[PLANT_ASAL];
		/////////////////////
							$mysqlmsadistrik = "
							    SELECT
									kp.KOORDINATOR_AREA, kp.DISTRICT, othv.KODE_TUJUAN, othv.NO_PP, zmp.COM_OPCO 
								FROM
									ZMD_KOORDINATOR_PENJUALAN kp
								LEFT JOIN
									or_trans_hdr_v othv
									ON kp.DISTRICT = othv.KODE_TUJUAN
								LEFT JOIN ZMD_MAPPING_PLANT zmp
									ON othv.PLANT_ASAL = zmp.PLANT_MD 
								WHERE
									kp.DEL = '0'
									AND othv.DELETE_MARK = '0'
								AND othv.KODE_TUJUAN='$row[KODE_TUJUAN]'
								AND othv.NO_PP='$row[NO_PP]'";
                            $mysql_msadistrik=oci_parse($conn,$mysqlmsadistrik);
                            oci_execute($mysql_msadistrik);
                            $row_msadistrik=oci_fetch_assoc($mysql_msadistrik);
							$plant_flag=substr($row[PLANT_ASAL], 0, 2);
								$opco_koordinator[]=$row_msadistrik[KOORDINATOR_AREA];
							if($plant_flag==='79'){
								$org_v[]=$row_msadistrik[COM_OPCO];
							}else{
								// $opco_koordinator[]=7000;
								$org_v[]=7000;
							}
			////////////////////	
	}
	$total=count($no_pp_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Permintaan Pembelian :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Approval PP Proyek</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form SearchApproval Permintaan Pembelian </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Permintaan Pembelian </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" value="<?=$no_pp?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Branch Plant </td>
      <td  class="puso">:</td>
      <td ><select name="branch_plant" id="Branch Plant" >
        <option value="">---Pilih---</option>
        <? $fungsi->or_jns_plant($branch_plant); ?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Sold To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="sold_to" name="sold_to"  value="<?=$sold_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="ship_to" name="ship_to"  value="<?=$ship_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Produk </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="produk" name="produk"  value="<?=$produk?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Jenis Pengiriman </td>
      <td  class="puso">:</td>
      <td ><select name="jenis_kirim" id="Jenis Pengiriman" >
          <option value="">---Pilih---</option>
          <? $fungsi->or_jenis_kirim($jenis_kirim); ?>
      </select></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Approval Permintaan Pembelian </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >Kode Plant</strong></td>
		<td align="center"><strong >Kode Pemilik Plant</strong></td>	
		<td align="center"><strong >Tanggal  PP</strong></td>
		<td align="center"><strong >Kode </strong></td>
		<td align="center"><strong >Distributor</strong></td>
		 <td align="center"><strong>Kode   </strong></td>
		 <td align="center"><strong>Distrik   </strong></td>
		<td align="center"><strong >Kode Opco Koordinator</strong></td>			 
		 <td align="center"><strong>Ship To   </strong></td>
		 <td align="center"><strong>Produk</strong></td>
		 <td align="center"><strong>Qty</strong></td>
		 <td align="center"><strong>Tgl Kirim</strong></td>
		 <td align="center"><strong>Royalty</strong></td>
				 		 <td align="center"><strong>MSA</strong></td>
		 <td align="center"><strong>Status</strong></td>
                 <?
                 if($user_org=='2000' || $user_org=='7000'){
                 ?>
                    <td align="center"><strong>Tipe</strong></td>
                 <?
                 }
                 ?>
		 <td align="center"><strong>Approval</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	

		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp_v[$i]; ?></td>
		<td align="center"><? echo $plant_v[$i]; ?></td>
		<td align="center"><? echo $org_v[$i]; ?></td>	
		<td align="center"><? echo $tgl_pp_v[$i]; ?></td>		
		<td align="left"><? echo $sold_to_v[$i]; ?></td>
		<td align="left"><? echo $nama_sold_to_v[$i]; ?></td>
		<td align="left"><? echo $kddistrik_v[$i]; ?></td>	
		<td align="left"><? echo $nmdistrik_v[$i]; ?></td>
		<td align="center"><? echo $opco_koordinator[$i]; ?></td>			
		<td align="left"><? echo ''.$ship_to_v[$i].', '.$alamat_v[$i].''; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? 
				$tbl="OR_TRANS_APP";
				$field=array('NO_PP','ITEM_NUMBER');
				$findby=array("$no_pp_v[$i]","$item_v[$i]");
				$qtyrel=$fungsi->findSumByMany($conn,$tbl,$field,$findby,'QTY_APPROVE'); 
				$qtysisa=$qty_v[$i] - $qtyrel;
				echo number_format($qtysisa,0,",","."); ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo ($royalty_v[$i]=='X' ? 'YES' : 'NO'); ?></td>
		        <td align="center"><? echo ($msa_v[$i]=='X' ? 'YES' : 'NO'); ?></td>		
		<td align="center"><? echo $status_v[$i]; ?></td>
                <?
                 if($user_org=='2000' || $user_org=='7000'){                 
                   echo '<td align="center">'.$tipeorder_v[$i].'</td>';                
                 }
                 ?>
		<td align="center"><a href="approve_pryk.php?id_detail=<?=$id_v[$i]?>&itemnum=<?=$itemnum_v[$i]?>">APPROVE</a></td>
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="14" align="center">
                    <a href="list_approve_pp.php" target="isi" class="button">Back</a>		 
                </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
