<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$user_id=$_SESSION['user_id'];
$no_inv = $_GET['no_invoice'];
$user_name=$_SESSION['user_name'];
// echo $no_inv;

if ($_POST['save']) {
	$arrcek = $_POST['DOKTRANS'];
	$idinv = $_POST['id_inv'];
	$totaldok = $_POST['totaldok'];
	$totalapprove = 0;
	//set status ke 0 semua
	$sqlupdate = "UPDATE EX_INVOICE_DOKUMEN_TRANS SET STATUS = 0 WHERE NO_INVOICE = '".$idinv."'";
	$qupdate = oci_parse($conn, $sqlupdate);
	oci_execute($qupdate);
	//end set
	if (count($arrcek) > 0) {
		foreach ($arrcek as $key => $value) {
			if ($value) {
				$totalapprove++;
			}
			$sqlupdate = "UPDATE EX_INVOICE_DOKUMEN_TRANS SET STATUS = ".(int) $value." WHERE ID = ".$key;
			// echo $sqlupdate;
			$qupdate = oci_parse($conn, $sqlupdate);
			oci_execute($qupdate);
		}
	}
	if ($totaldok == $totalapprove) {
		$sqlupdateinv = "UPDATE EX_INVOICE SET STATUS_DOKUMEN = 1 WHERE NO_INVOICE = '".$id_inv."'";
		$qupdateinv = oci_parse($conn, $sqlupdateinv);
		$inserted = @oci_execute($qupdateinv);

		if ($inserted) {
			
			// tracking INVOICE
			$sql_inv = "SELECT ID, WARNA_PLAT, NO_BA FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$id_inv' "; 
			$query_inv = oci_parse($conn, $sql_inv);
			oci_execute($query_inv);
			$row_inv = oci_fetch_assoc($query_inv); 
			$no_ba = $row_inv[NO_BA];
			 // echo $no_ba ;exit();
			$status_ba = 50;
			
			$sql_invoice_ba = "SELECT EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$id_inv' AND DIPAKAI = 1";
			$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
			oci_execute($query_invoice_ba);

			$data_invoice_ba = array();
			while($row = oci_fetch_array($query_invoice_ba)) {
				$data_invoice_ba = $row;
			}  
			
			$field_names = array('DIPAKAI');
			$field_data = array("0");
			$tablename = "EX_BA_INVOICE";
			$field_id = array('NO_BA');
			$value_id = array("$no_ba");
			$fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
			
			$field_namess = array('TRACKING_INV');
			$field_data = array($status_ba);
			$tablename = "EX_INVOICE";
			$field_id = array('NO_INVOICE');
			$value_id = array($data_invoice_ba[NO_INVOICE]);
			$fungsi->update($conn, $field_namess, $field_data, $tablename, $field_id, $value_id);
			 
					
			$field_names = array();
			$field_data = array();
			
			foreach($data_invoice_ba as $ii => $vi) {
				if($vi != "" && !is_numeric($ii) && $ii != "KOMENTAR_REJECT" && $ii != "TGL_FAKTUR_PAJAK1") {
					$field_names[] = $ii;
					if($ii == "STATUS_BA_INVOICE") {
						$field_data[] = $status_ba;
					} else if($ii == "TGL_FAKTUR_PAJAK") {
						$field_data[] = "instgl_" . $data_invoice_ba[TGL_FAKTUR_PAJAK1];
					} else if($ii == "CREATED_BY") {
						$field_data[] = $user_id;
					} else if($ii == "CREATED_AT") {
						$field_data[] = "SYSDATE";
					} else {
						$field_data[] = $vi;
					}
				}
			}
			
			// INSERT
			$tablename = "EX_BA_INVOICE";
			$fungsi->insert($conn, $field_names, $field_data, $tablename);
			
			
			echo '<script>window.location = "create_invoice_all.php?no_invoice='.$idinv.'";</script>';
			// header('Location: http://***********/dev/sd/sdonline_artavel/ex_transaksi/create_invoice_all.php?no_invoice='.$idinv);
			exit;
		}
	}else{
		$sqldokkurang = "select TB2.NAMA_DOKUMEN from EX_INVOICE_DOKUMEN_TRANS TB1
		 JOIN EX_INVOICE_DOKUMEN_INV TB2 ON TB1.ID_INVOICE_DOKUMEN = TB2.ID
		 WHERE NO_INVOICE = '".$idinv."' AND TB1.STATUS = 0";

		$querydokkurang = oci_parse($conn, $sqldokkurang);
		oci_execute($querydokkurang);
		$datadokkurang = array();
		while ($row = oci_fetch_assoc($querydokkurang)) {
			array_push($datadokkurang, $row['NAMA_DOKUMEN']);
		}
		$datadokkurang = implode(",", $datadokkurang);
		$sqlupdatedok = "update KPI_TERIMA_INV_VENDOR set tgl_dikembalikan=to_date('".date("Ymd")."','YYYYMMDD'),
	    DEL=1,ALASAN='".$datadokkurang." Kurang Lengkap',
	    UPDATE_BY='$user_name',
	    LAST_UPDATE=SYSDATE,FLAG_CETAKTERM=0
	    where NO_INVOICE = '".$idinv."' AND DEL = 0";
	    $querydok=@oci_parse($conn, $sqlupdatedok);
	    @oci_execute($querydok);
	}
	oci_commit($conn);
} else {
	$sqlstatus = "select STATUS_DOKUMEN FROM EX_INVOICE WHERE NO_INVOICE = '".$no_inv."'";
	$qstatus = oci_parse($conn, $sqlstatus);
	oci_execute($qstatus);
	$datastatus = oci_fetch_assoc($qstatus);
	if ($datastatus['STATUS_DOKUMEN']) {
		echo '<center><h3>Dokumen sudah lengkap</h3></center>';
		die();
	}
	
	$sqlchek = "select TB1.NO_INVOICE,TB1.STATUS,TB2.NAMA_DOKUMEN,TB1.ID from EX_INVOICE_DOKUMEN_TRANS TB1
	 JOIN EX_INVOICE_DOKUMEN_INV TB2 ON TB1.ID_INVOICE_DOKUMEN = TB2.ID
	 WHERE NO_INVOICE = '".$no_inv."'";
	$querycek = oci_parse($conn, $sqlchek);
	oci_execute($querycek);
	 
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$no_inv' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba); 
	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
}
?>

<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../include/jquery.min.js"></script>
<script type="text/javascript">
	function checkstatus() {
		var tdok = $("#tdok").val();
		var datacek = 0;
		var tanya;
		$.each($('.cek:checked'), function(index, val) {
    		datacek++;
    	});
    	if (datacek == tdok) {
    		tanya = confirm('Dokumen Telah lengkap, apakah anda yakin untuk melanjutkan proses PPL ?');
    	}else{
    		tanya = confirm('Dokumen Tidak lengkap, apakah anda yakin untuk melanjutkan proses Pengembalian Ke vendor ?');
    	}
    	// alert(tanya);
    	return tanya;
	}
</script>
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<?php if ($_POST['save']): ?>
	<center>Data berhasil disimpan <br><input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" />	</center>
<?php else: ?>
<br /><br /> 
<table  class="adminlist" cellspacing="0" cellpadding="0" border="0" width="60%" align="center">
		  
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
									<td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
									<td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
									<td align="center" style="width: 200px;"><strong>Submit By</strong></td> 
								</tr>
							</thead>
							<tbody id="isiLampiran"><?php
								$lampiran = $data_invoice_ba[LAMPIRAN] != "" ? json_decode($data_invoice_ba[LAMPIRAN]) : array();
								$bulan = array(null, "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");
								foreach($lampiran as $i=>$l) {
									$tgl = explode("-", $l[1]);
									$fileUrl = "./lampiran/".htmlspecialchars($l[3]);

									if (strtolower($l[0]) == "ba rekapitulasi") {
										$fileUrl = "../ex_ba_sp/upload/".htmlspecialchars($l[3]);
									}

									echo "
										<tr class='row-file'>
											<td align='center' class='nomor-file'>".($i+1)."</td>
											<td align='center'>".htmlspecialchars($l[0])."</td>
											<td align='center'>".$tgl[2]."-".$bulan[intval($tgl[1])]."-".$tgl[0]."</td>
											<td align='center'><a  href='javascript:popUp(\"" . $fileUrl . "\")'>".htmlspecialchars($l[2])."</a></td>
											<td align='center'>".htmlspecialchars($l[4])."</td> 
										</tr>
									";
								}
							?></tbody>
				 
			 
		</table> 
<br /><br />

<form id="data_claim" name="data_claim" method="post" action="invoice_dokumen_trans_proses.php" >

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Dokumen Invoice <?php echo $no_inv; ?> </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >Dokumen </strong></td>
		 <td align="center"><strong>Cheklist </strong></td>
      </tr >
	  </thead>
	  <tbody>
  <? $b=0;  while ($datafunc = oci_fetch_assoc($querycek)) {
		?>     
		<tr>
		<td align="center"><? echo ++$b; ?></td>
		<td align="center"><? echo $datafunc['NAMA_DOKUMEN']; ?></a></td>	
		<input type="hidden" name="id_inv" value="<?php echo $no_inv; ?>" />
		<td align="center"><input type="checkbox" class="cek" value="1" name="DOKTRANS[<?php echo $datafunc['ID'] ?>]" <?php echo (($datafunc['STATUS']) ? 'checked' : ''); ?> /></td>
		</tr>
	  <? } ?>
		</tbody>
		<input type="hidden" name="totaldok" id="tdok" value="<?php echo $b; ?>">
	  <tr class="quote">
		<td colspan="14" align="center">
		<?php if ($b > 0): ?>
		<input name="save" type="submit" class="button" value="Simpan" onClick="return checkstatus();"; />	
		<?php endif ?>	 
		<input name="close" type="submit" class="button" value="Close" onClick="self.close();return false;" />		 
		</td>
	    </tr>
	</table>
	</div>
		</form>
</div>
</div>
<p>&nbsp;</p>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<?php endif; ?>
</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>