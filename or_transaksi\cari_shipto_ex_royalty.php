<?php
session_start();
include ('../include/or_fungsi.php');
//include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once '../include/oracleDev.php'; 
$fungsi2=new conntoracleDEVSD();
$connDEVSD=$fungsi2->DEVSDdb();

//Translate
require_once('../include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

$data_set =array();
$currentPage="cari_shiptosi.php";
$nourut=$_GET['nourut'];
$hidplant = trim($_GET['filplant']);//hidden plant selain 7403
$pricelist = trim($_GET['pricelist']);
$distrikh = trim($_GET['distrik']);

$tanggal = trim($_GET['tanggal']);


if($pricelist == '19'){
    $sqlarea = "SELECT KD_AREA FROM ZREPORT_M_KOTA WHERE KD_KOTA = '".$distrikh."'";
    $queryarea = oci_parse($connDEVSD, $sqlarea);
    oci_execute($queryarea);
    while ($rowarea = oci_fetch_array($queryarea)){   
        $area = $rowarea[KD_AREA];
    }
    $sqldis = "SELECT KD_KOTA FROM ZREPORT_M_KOTA WHERE KD_AREA = '".$area."'";
    $querydis = oci_parse($connDEVSD, $sqldis);
    oci_execute($querydis);
    while ($rowdis = oci_fetch_array($querydis)){   
        $distrikarea1[] = $rowdis[KD_KOTA];
    }
    if(count($distrikarea1)>0){
        unset($distrikarea);
        for($counter = 0; $counter<count($distrikarea1); $counter++){
            $distrikarea .="'".$distrikarea1[$counter]."',";
        }
        $distrikarea= rtrim($distrikarea, ',');        
    }
}

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$soldto=$fungsi->sapcode($distr_id);

if ($org=='2000' or $org=='5000' or $org=='7000' or substr(trim($_REQUEST['filplant']), 0,2)=="79") { $org1='ZSG1';$org2='ZSG2';}
elseif ($org=='3000') { $org1='ZSP1';$org2='ZSP2';}
elseif ($org=='4000') { $org1='ZST1';$org2='ZST2';}
elseif ($org=='6000') { $org1='ZTL1';$org2='ZTL2';}
else {$org1='ZSG1';$org2='ZSG2';}

if(isset($_POST['kode']) or isset($_POST['nama'])){
	$kode = $_POST['kode'];
	$nama = strtoupper($_POST['nama']);
	$ndistrik = strtoupper($_REQUEST['ndistrik']);
        $distrik = trim($_REQUEST['distrik']);
        $hidplant = trim($_REQUEST['filplant']);//hidden plant selain 7403
        $kode = $_POST['kode'];
	$nama = strtoupper($_POST['nama']);
	$ndistrik = strtoupper($_REQUEST['ndistrik']);

	//fungsi menggunakan ORACLE
	/*$sql = "SELECT * FROM RFC_Z_ZCSD_SHIPTO WHERE ZKTOKD1 = '".$org1."'
	 AND ZKTOKD2 = '".$org2."' AND ZNMORG = '".$org."' AND KUNNR = '".$soldto."'";*/
//	 $sql = "SELECT DISTINCT KUNN2, SHIPTO_NAME, SHIPTO_ADDR, NAME1, STRAS, BZIRK, BZTXT,
//         VKBUR, BEZEB, KVGR1, PALLET, INCO1 FROM RFC_Z_ZCSD_SHIPTO 
//         WHERE NOT EXISTS (SELECT t2.KODE_SHIP_TO FROM OR_DISTBLOCK t2 WHERE t2.PLANT = '".$hidplant."' and t2.DELETE_MARK = 0 and KUNN2 = t2.KODE_SHIP_TO)
//         AND ZKTOKD1 = '".$org1."'AND ZKTOKD2 = '".$org2."' AND KUNNR = '".$soldto."'";
         
        // customer replenishment jatim,jateng,DIY,Bali tidak boleh create PP manual
        if(substr($distrik, 0, 2) == '26' ||substr($distrik, 0, 2) == '25' || substr($distrik, 0, 2) == '24' || substr($distrik, 0, 2) == '23'){
        
        $map_replenisment = " AND KUNN2 NOT IN (
		SELECT
			t3.KODE_SHIPTO
		FROM
			MASTER_CUSTOMER_REPLENISHMENT t3
		WHERE
			DEL = '0'
			AND SUBSTR(ORG, 1, 1) = SUBSTR('".$hidplant."', 1, 1)
	) ";
        
        }else{
        $map_replenisment = "";
        }
        
         $sql = "SELECT DISTINCT
		 sp.KUNN2,
		 sp.SHIPTO_NAME,
		 sp.SHIPTO_ADDR,
		 sp.NAME1,
		 sp.STRAS,
		 sp.BZIRK,
		 sp.BZTXT,
		 sp.VKBUR,
		 sp.BEZEB,
		 sp.KVGR1,
		 sp.PALLET,
		 sp.INCO1 
	 FROM RFC_Z_ZCSD_SHIPTO sp
	-- RIGHT JOIN EX_MAPPING_SHIPTO ex ON EX.SHIPTO = sp.KUNN2
         WHERE NOT EXISTS (SELECT t2.KODE_SHIP_TO FROM OR_DISTBLOCK t2 WHERE t2.PLANT = '".$hidplant."' and t2.PRICELIST = '".$pricelist."' and t2.DELETE_MARK = 0 and KUNN2 = t2.KODE_SHIP_TO)
         $map_replenisment
         AND ZKTOKD1 = '".$org1."'AND ZKTOKD2 = '".$org2."' AND KUNNR = '".$soldto."'";
	if ($kode) {
		$sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
		$sql .= "UPPER(sp.KUNN2) LIKE '%".strtoupper($kode)."%'";
	}
	if ($nama) {
		$sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
		$sql .= "UPPER(sp.NAME1) LIKE '%".strtoupper($nama)."%'";
	}
	if ($ndistrik) {
		$sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
		$sql .= "sp.BZTXT = '".$ndistrik."'";
	}
	if ($distrik && $pricelist != '19') {
		 $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
		// if (substr($distrik, 0,2) == '10' || substr($distrik, 0,2) == '25') {
		// 	$sql .= "sp.BZIRK LIKE '".substr($distrik, 2,2)."%'";
		// }else{
		// }
			$sql .= "sp.BZIRK = '".$distrik."'";
	}
        if ($distrikarea && $pricelist == '19') {
		$sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sp.BZIRK IN ($distrikarea)";
	}
        $sql .= " ORDER BY sp.KUNN2";
        // echo $sql;
		// exit;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($datafunc=oci_fetch_assoc($query)){
		
//            if($hidplant!=7601){
//                if($hidplant!=7416){
//                    if($hidplant!='7403' && $datafunc["PALLET"]!='')
//                        continue;
//                }
//            }
                            
		$shipto[]= $datafunc["KUNN2"];
	
		if($org=='6000'){
            $nama_shipto[]= $datafunc["SHIPTO_NAME"];
            $alamat[]= $datafunc["SHIPTO_ADDR"];
        }else{
            $nama_shipto[]= $datafunc["NAME1"];
            $alamat[]= $datafunc["STRAS"];
        }  
		$kode_distrik[]= $datafunc["BZIRK"];
		$nama_distrik[]= $datafunc["BZTXT"];
		$kode_prov[] = $datafunc["VKBUR"];
		$nama_prov[] = $datafunc["BEZEB"];
        $tipetruk[] = $datafunc["KVGR1"];
        $palletF[] = $datafunc["PALLET"];
        if($org=='6000'){
            $inco_val[] = $datafunc["INCO1"];
        }
	}
    // var_dump($shipto);
	/*$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		if($org=='6000'){
                    $fce = $sap->NewFunction ("Z_ZCSD_SHIPTO2");
                }else{
                    $fce = $sap->NewFunction ("Z_ZCSD_SHIPTO");
                }
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->ZKTOKD1 = $org1;
		$fce->ZKTOKD2 = $org2;
		$fce->ZKUNNR = $soldto;
		$fce->ZKUNN2 = $kode;
		$fce->ZNAME1 = $nama;
		$fce->ZBZTXT = $ndistrik;
                if($distrik!=''){
                    $fce->ZBZIRK = $distrik;
                }
                $fce->ZNMORG = $org;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
                            if($hidplant!='7403' && $fce->RETURN_DATA->row["PALLET"]!='')
                                continue;
                            else
				$shipto[]= $fce->RETURN_DATA->row["KUNN2"];
				$nama_shipto[]= $fce->RETURN_DATA->row["NAME1"];
				$alamat[]= $fce->RETURN_DATA->row["STRAS"];
				$kode_distrik[]= $fce->RETURN_DATA->row["BZIRK"];
				$nama_distrik[]= $fce->RETURN_DATA->row["BZTXT"];
				$kode_prov[] = $fce->RETURN_DATA->row["VKBUR"];
				$nama_prov[] = $fce->RETURN_DATA->row["BEZEB"];
                                $tipetruk[] = $fce->RETURN_DATA->row["KVGR1"];
                                $palletF[] = $fce->RETURN_DATA->row["PALLET"];
                                
                                if($org=='6000'){
                                    $inco_val[] = $fce->RETURN_DATA->row["INCO1"];
                                }
                                
                        }
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	*/
}
?>
<script language=javascript>
    
    function getXMLHTTP() {
    var x = false;
    try {
        x = new XMLHttpRequest();
    }
    catch(e) {
        try {
            x = new ActiveXObject("Microsoft.XMLHTTP");
        }
        catch(ex) {
            try {
                req = new ActiveXObject("Msxml2.XMLHTTP");
            }
            catch(e1) {
                x = false;
            }
        }
    }
    return x;
}
    
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<!-- Style css jquery table sorter -->
<link href="../Templates/css-sorter/jquery-sorter-style.css" rel="stylesheet" type="text/css"/>
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script type="text/javascript" src="../Templates/css-sorter/jquery-latest.js"></script>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="../Templates/css-sorter/jquery.js"></script>
<script type="text/javascript">
                
                
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

	$(document).ready(function() 
    { 
        $("#myTable").tablesorter({widthFixed: true, widgets: ['zebra']});
			//.tablesorter( {sortList: [[0,0], [1,0]]} ); 
    } 
	); 
</script>

<style type="text/css">
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
</style>
<head>
<script>
   
function setForm() {

	var btn = document.getElementById("cekdata"); 
	if(btn.value != 0){
	var urut=<?=$nourut?>;
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var acc_alamat='acc_alamat'+kenya;
	var komponen_acc_alamat=document.getElementById(acc_alamat); 
	var acc_distrik='acc_distrik'+kenya;
	var komponen_acc_distrik=document.getElementById(acc_distrik); 
	var acc_ndistrik='acc_ndistrik'+kenya;
	var komponen_acc_ndistrik=document.getElementById(acc_ndistrik); 
	var acc_prov='acc_prov'+kenya;
	var komponen_acc_prov=document.getElementById(acc_prov); 
	var acc_nprov='acc_nprov'+kenya;
	var komponen_acc_nprov=document.getElementById(acc_nprov); 
        var acc_ntypetrucuk='acc_ntypetrucuk'+kenya;
	var komponen_ntypetrucuk=document.getElementById(acc_ntypetrucuk);
        
         
       var jkemasan='<?php echo $_GET['jkemasan']; ?>';    
    var distid='<?php  echo $_GET['distid']; ?>';   
    var route = '<?php  echo $_GET['route']; ?>';   
    var com='<?php  echo $_GET['com']; ?>';    
    var shipto=komponen_acc_id.value;   
    var plant='<?php echo  $_GET['filplant']; ?>';   
    var so_type = '<?php echo  $_GET['so_type']; ?>';  
    var kodeprop = '<?php  echo $_GET['kodeprop']; ?>';   

	var tanggal = '<?php  echo $_GET['tanggal']; ?>';  
	var soldto = '<?php  echo sprintf("%'010s",$_GET['soldto']); ?>';   
	var brand ='<?php echo $_GET['brand']; ?>';       
	var kd_material ='<?php echo $_GET['kd_material']; ?>'; 

	// disini ada pengecekan shipto exection , jadi pengecekan nya ke table mapping_shipto_ex , nah pengecekan nya jika shipti ada di mappingan maka plant di set seuai mappingan
	var URLshiptoEX="loadexplanningpp_royalty.php?jkemasan="+jkemasan+"&distid="+distid+'&prop='+kodeprop+'&route='+route+'&com='+com+'&shipto='+shipto+'&plant='+plant+'&soldto='+soldto+'&brand='+brand+'&kd_material='+kd_material+'&action=cekShiptoEx';
	var reqShiptoEx = getXMLHTTP();
      if (reqShiptoEx) {
              reqShiptoEx.onreadystatechange = function() {
                  
                      if (reqShiptoEx.readyState == 4) {
                              
                              if (reqShiptoEx.status == 200) {
								// Mendapatkan referensi ke elemen select
								opener.document.getElementById('produk'+urut).readonly = false;
								opener.document.getElementById('btn_produk'+urut).disabled = false;//array[0];
								if (reqShiptoEx.responseText.replaceAll(';','').trim().length > 0) {
                                                                
                                    const detail_exception = reqShiptoEx.responseText.split(';');
                                    // Mendapatkan referensi ke elemen select
                                    var selectElement = opener.document.getElementById("plant");
                                    var selectElement2 = opener.document.getElementById("plantBayangan");
                                    var tanggal=opener.document.getElementById('tgl_kirim1');
                                    let plantOptions = [...selectElement.options].map(o => o.value);
                                    // Membuat opsi baru
                                    if(!plantOptions.includes(detail_exception[0])){
                                        var newOption = opener.document.createElement("option");

                                        // Mengatur teks untuk opsi baru
                                        newOption.text = detail_exception[0]+" "+detail_exception[1];//['NM_PLANT'];
                                        // Mengatur nilai untuk opsi baru (opsional)

                                        // var array = reqShiptoEx.responseText.split(" ");
                                        newOption.value = detail_exception[0];//['WERKS'];
                                        tanggal.value='';
                                        // Menambahkan opsi baru ke elemen select
                                        selectElement.add(newOption);
                                        
                                        // selectElement.disabled = true;
                                        newOption.selected = true;
                                    }
                                    else{
                                        selectElement.value = detail_exception[0];
                                    }
                                    // selectElement.disabled = false;
                                    selectElement2.value = detail_exception[0];//array[0];
                                    opener.document.getElementById('plantBayangan').value = detail_exception[0];//array[0]; 

                                    opener.document.getElementById('produk'+urut).value = detail_exception[2];//array[0]; 
                                    opener.document.getElementById('nama_produk'+urut).value = detail_exception[3];//array[0]; 
                                    opener.document.getElementById('uom'+urut).value = detail_exception[4];//array[0]; 
									
									opener.load_top('1');
									opener.cekshiptotop();
								}
								else{
									opener.load_top('1');
									opener.cekshiptotop();
								}
                                 
                              } else { 
                                      alert("There was a problem while using XMLHTTP:\n" + reqShiptoEx.statusText);
                              }
                      }               
              }           
              reqShiptoEx.open("GET", URLshiptoEX, true);
              reqShiptoEx.send(null);
      }

	  

// ============================= akhir pengecekan =====================================================================
 
	// var strURL="loadexplanningpp_royalty.php?jkemasan="+jkemasan+"&distid="+distid+'&prop='+kodeprop+'&route='+route+'&com='+com+'&shipto='+shipto+'&plant='+plant+'&so_type='+so_type+'&action=viewtopcombo';
	// var req = getXMLHTTP();
	// 				if (req) {
	// 							req.onreadystatechange = function() {
									
	// 									if (req.readyState == 4) {
												
	// 											if (req.status == 200) {
	// 				//                                    document.getElementById('loadtopcombo').innerHTML=req.responseText;
	// 													opener.document.getElementById("top").innerHTML = req.responseText; 
	// 													//opener.document.getElementById("nama_top").value =  req.responseText; 
														
	// 													//$("#nama_top").val($("#top option[value='"+req.responseText+"']").text());
	// 				//                                    opener.document.getElementById("nama_top").value = 'ZD30';  
	// 											} else { 
	// 													alert("There was a problem while using XMLHTTP:\n" + req.statusText);
	// 											}
	// 									}               
	// 							}           
	// 							req.open("GET", strURL, true);
	// 							req.send(null);
	// 					}
						// opener.document.getElementById("plant").value = '79I8'; 
						// var selectElement = opener.document.getElementById("plant");
						
						// // Mengambil indeks opsi yang ingin Anda pilih (misalnya, opsi kedua memiliki indeks 1)
						// var selectedIndex = 2;

						// // Mengakses opsi yang dipilih berdasarkan indeks
						// var selectedOption = selectElement.options[value='1000'];

						// // Memilih opsi tersebut
						// selectedOption.selected = true;
						
						opener.document.getElementById("shipto"+urut).value = komponen_acc_id.value;
						opener.document.getElementById("nama_shipto"+urut).value = komponen_acc_no.value;
						opener.document.getElementById("alamat"+urut).value = komponen_acc_alamat.value;
						opener.document.getElementById("kode_distrik"+urut).value = komponen_acc_distrik.value;
						opener.document.getElementById("nama_distrik"+urut).value = komponen_acc_ndistrik.value;
						opener.document.getElementById("kode_prov"+urut).value = komponen_acc_prov.value;
						opener.document.getElementById("nama_prov"+urut).value = komponen_acc_nprov.value;
						opener.document.getElementById("typetruck"+urut).value = komponen_ntypetrucuk.value; 
						opener.load_top('1');
						opener.cekshiptotop();
						setTimeout (window.close, 1000);
				

	}else
		{
			alert("<?php $translatebhsxx->__1('Choose Shipto Data, please'); ?>");
			return false;
		}
}

</script>
<script> 
function checkForother(obj) {  
	if (!document.layers) { 
		var kenya=obj.value;
		var btn = document.getElementById("cekdata"); 
		btn.value = kenya;
		//opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
	
} 

function checkForother_db(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
	var btn = document.getElementById("cekdata"); 
	if(btn.value != 0){
	var urut=<?=$nourut?>;
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var acc_alamat='acc_alamat'+kenya;
	var komponen_acc_alamat=document.getElementById(acc_alamat); 
	var acc_distrik='acc_distrik'+kenya;
	var komponen_acc_distrik=document.getElementById(acc_distrik); 
	var acc_ndistrik='acc_ndistrik'+kenya;
	var komponen_acc_ndistrik=document.getElementById(acc_ndistrik); 
	var acc_prov='acc_prov'+kenya;
	var komponen_acc_prov=document.getElementById(acc_prov); 
	var acc_nprov='acc_nprov'+kenya;
	var komponen_acc_nprov=document.getElementById(acc_nprov); 
        var acc_ntypetrucuk='acc_ntypetrucuk'+kenya;
	var komponen_ntypetrucuk=document.getElementById(acc_ntypetrucuk);

       var jkemasan='<?php echo $_GET['jkemasan']; ?>';    
    var distid='<?php  echo $_GET['distid']; ?>';   
    var route = '<?php  echo $_GET['route']; ?>';   
    var com='<?php  echo $_GET['com']; ?>';    
    var shipto=komponen_acc_id.value;   
    var plant='<?php echo  $_GET['filplant']; ?>';   
    var so_type = '<?php echo  $_GET['so_type']; ?>';  
    var kodeprop = '<?php  echo $_GET['kodeprop']; ?>';    
	var soldto = '<?php  echo sprintf("%'010s",$_GET['soldto']); ?>';   
	var brand ='<?php echo $_GET['brand']; ?>';
	var kd_material ='<?php echo $_GET['kd_material']; ?>';

	// disini ada pengecekan shipto exection , jadi pengecekan nya ke table mapping_shipto_ex , nah pengecekan nya jika shipti ada di mappingan maka plant di set seuai mappingan
	var URLshiptoEX="loadexplanningpp_royalty.php?jkemasan="+jkemasan+"&distid="+distid+'&prop='+kodeprop+'&route='+route+'&com='+com+'&shipto='+shipto+'&plant='+plant+'&soldto='+soldto+'&brand='+brand+'&kd_material='+kd_material+'&action=cekShiptoEx';
	var reqShiptoEx = getXMLHTTP();
      if(reqShiptoEx) {
              reqShiptoEx.onreadystatechange = function() {
                  
                      if (reqShiptoEx.readyState == 4) {
                              
                              if (reqShiptoEx.status == 200) {
								
							
								opener.document.getElementById('produk'+urut).readonly = false;
								opener.document.getElementById('btn_produk'+urut).disabled = false;//array[0];
								if (reqShiptoEx.responseText.replaceAll(';','').trim().length > 0) {
                                                                
                                    const detail_exception = reqShiptoEx.responseText.split(';');
                                    // Mendapatkan referensi ke elemen select
                                    var selectElement = opener.document.getElementById("plant");
                                    var selectElement2 = opener.document.getElementById("plantBayangan");
                                    var tanggal=opener.document.getElementById('tgl_kirim1');
                                    let plantOptions = [...selectElement.options].map(o => o.value);
                                    // Membuat opsi baru
                                    if(!plantOptions.includes(detail_exception[0])){
                                        var newOption = opener.document.createElement("option");

                                        // Mengatur teks untuk opsi baru
                                        newOption.text = detail_exception[0]+" "+detail_exception[1];//['NM_PLANT'];
                                        // Mengatur nilai untuk opsi baru (opsional)

                                        // var array = reqShiptoEx.responseText.split(" ");
                                        newOption.value = detail_exception[0];//['WERKS'];
                                        tanggal.value='';
                                        // Menambahkan opsi baru ke elemen select
                                        selectElement.add(newOption);
                                        
                                        // selectElement.disabled = true;
                                        newOption.selected = true;
                                    }
                                    else{
                                        selectElement.value = detail_exception[0];
                                    }
                                    // selectElement.disabled = false;
                                    selectElement2.value = detail_exception[0];//array[0];
                                    opener.document.getElementById('plantBayangan').value = detail_exception[0];//array[0]; 

                                    opener.document.getElementById('produk'+urut).value = detail_exception[2];//array[0]; 
                                    opener.document.getElementById('nama_produk'+urut).value = detail_exception[3];//array[0]; 
                                    opener.document.getElementById('uom'+urut).value = detail_exception[4];//array[0]; 


								opener.load_top('1');
								opener.cekshiptotop();
							}else{
								opener.load_top('1');
								opener.cekshiptotop();
							}

                                 
                              } else { 
                                      alert("There was a problem while using XMLHTTP:\n" + reqShiptoEx.statusText);
                              }
                      }               
              }           
              reqShiptoEx.open("GET", URLshiptoEX, true);
              reqShiptoEx.send(null);
      }


	  

// ============================= akhir pengecekan =====================================================================

      
            	opener.document.getElementById("shipto"+urut).value = komponen_acc_id.value;
                opener.document.getElementById("nama_shipto"+urut).value = komponen_acc_no.value;
                opener.document.getElementById("alamat"+urut).value = komponen_acc_alamat.value;
                opener.document.getElementById("kode_distrik"+urut).value = komponen_acc_distrik.value;
                opener.document.getElementById("nama_distrik"+urut).value = komponen_acc_ndistrik.value;
                opener.document.getElementById("kode_prov"+urut).value = komponen_acc_prov.value;
                opener.document.getElementById("nama_prov"+urut).value = komponen_acc_nprov.value;
                opener.document.getElementById("typetruck"+urut).value = komponen_ntypetrucuk.value; 
				opener.load_top('1');
				opener.cekshiptotop();
                setTimeout (window.close, 1000);
	}else
		{
			alert("<?php $translatebhsxx->__1('Choose Shipto Data, please'); ?>");
			return false;
		}
} 

</script> 
<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?php $translatebhsxx->__1('List of Ship To Party'); ?></title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2"><?php $translatebhsxx->__1('List of Ship To Party'); ?></th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		  <td class="puso"><?php $translatebhsxx->__1('Ship To Code'); ?></td>
		  <td class="puso">:</td>
		  <td><input name="kode" type="text" class="" value="<? echo $kode; ?>" size="40" /></td>
		  </tr>
		<tr>
		  <td class="puso"><?php $translatebhsxx->__1('Ship To Name'); ?></td>
		  <td class="puso">:</td>
		  <td><input name="nama" id="nama" type="text" class="" value="<? echo $nama; ?>" size="40" /></td>
		  </tr>
		<tr>
		<td width="173" class="puso"><?php $translatebhsxx->__1('District Name'); ?></td>
		<td width="26" class="puso">:</td>
		<td width="585">
		  <input name="ndistrik" type="text" class="" value="<? echo $ndistrik; ?>" size="40" /></td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="<?php $translatebhsxx->__1('Show'); ?>" />		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?
if(isset($_POST['kode'])){
$total=count($shipto);
	if($total>0){
?>
		<p></p>
		<div align="center">
		<table width="800" align="center" class="adminlist">
		<tr>
		<th align="left" colspan="4"> <span class="style5">&nbsp;<?php $translatebhsxx->__1('Tabel of Destination Data'); ?></span> </th>
		</tr>
		</table>
		</div> 
		<div align="center">
		<form  name="formKaryawan">
			<span id="zebrax">
	<table id="myTable" width="800" align="center" class="pickme">
	<thead >
		  <tr class="quote">
			<th><div align="center"><strong>&nbsp;&nbsp; <?php $translatebhsxx->__1('Cek.'); ?></strong></div></th>
			<th align="center"><strong><?php $translatebhsxx->__1('Ship To Party Code'); ?></strong></th>
			<th align="center"><strong><?php $translatebhsxx->__1('Ship To Party Name'); ?></strong></th>
			<th align="center"><strong><?php $translatebhsxx->__1('Ship To Party Address'); ?></strong></th>
			<th align="center"><strong><?php $translatebhsxx->__1('District Name'); ?></strong></th>
                         <?if($org=='7000'){?>
                        <th align="center"><strong><?php $translatebhsxx->__1('District'); ?></strong></th>
                        <th align="center"><strong><?php $translatebhsxx->__1('Type Truck'); ?></strong></th>                       
                        <th align="center"><strong><?php $translatebhsxx->__1('Palllet'); ?></strong></th>
                        <?}else if($org=='6000'){?>
                        <th align="center"><strong><?php $translatebhsxx->__1('Incoterm'); ?></strong></th>
                        <?}?>
			</tr>
		  </thead>
		  <tbody >
		  <?  for($i=0; $i<$total;$i++) {
				if(($i % 2) == 0)	{	 
				echo "<tr class='odd0'>";
					}
				else	{	
				echo "<tr class='odd1'>";
					}	
			$b=$i+1;
			$acc_id="acc_id".$b;
			$acc_no="acc_no".$b;
			$acc_alamat="acc_alamat".$b;
			$acc_distrik="acc_distrik".$b;
			$acc_ndistrik="acc_ndistrik".$b;
			$acc_prov="acc_prov".$b;
			$acc_nprov="acc_nprov".$b;
                        $acc_ntypetruck="acc_ntypetrucuk".$b;
			?>
			<td align="center"><input name="radiokaryawan" type="radio" value="<?=$b;?>" onChange="checkForother(this)" id="<?=$b?>" onClick="checkForother_db(this)"/>
			<input id="<?=$acc_id;?>" name="<?=$acc_id;?>" type="hidden" value="<?=$shipto[$i]?>" />
			<input id="<?=$acc_no;?>" name="<?=$acc_no;?>" type="hidden" value="<?=$nama_shipto[$i]?>" />
			<input id="<?=$acc_alamat;?>" name="<?=$acc_alamat;?>" type="hidden" value="<?=$alamat[$i]?>" />
			<input id="<?=$acc_distrik;?>" name="<?=$acc_distrik;?>" type="hidden" value="<?=$kode_distrik[$i]?>" />
			<input id="<?=$acc_ndistrik;?>" name="<?=$acc_ndistrik;?>" type="hidden" value="<?=$nama_distrik[$i]?>" />
			<input id="<?=$acc_prov;?>" name="<?=$acc_prov;?>" type="hidden" value="<?=$kode_prov[$i]?>" />
			<input id="<?=$acc_nprov;?>" name="<?=$acc_nprov;?>" type="hidden" value="<?=$nama_prov[$i]?>" />
                        <input id="<?=$acc_ntypetruck;?>" name="<?=$acc_ntypetruck;?>" type="hidden" value="<?=$tipetruk[$i]?>" />
                        </td>    
			<td align="center"><? echo $shipto[$i]; ?></td>
			<td align="left"><? echo $nama_shipto[$i]; ?></td>
			<td align="left"><? echo $alamat[$i]; ?></td>
			<td align="left"><? echo $nama_distrik[$i]; ?></td>
                        <?if($org=='7000'){?>
                        <td align="left"><?=$kode_distrik[$i];?></td>
                        <td align="left"><?=$tipetruk[$i];?></td>
                        <td align="left"><?=$palletF[$i];?></td>
                        <?}else if($org=='6000'){?>
                        <td align="left"><?=$inco_val[$i];?></td>
                        <?}?>
			</tr>
		  <? } ?>
	  </tbody>
		</table>
		</span>
		</div>
		
	<?
	}else $komen = " Sorry .. <br> No Data Found..";
	
	?>
<div align="center">
  <br />
  <br />
  <input type="button" value="<?php $translatebhsxx->__1('Ok'); ?>" name="kartu" class="button" onClick="setForm()">
    <input id="cekdata" name="cekdata" type="hidden" value="0" />
</div>
</form>
<?
}
?>
<? if ($komen != "" and $komen != NULL and $komen != " "){ ?>
	
	<div align="center" >
	<p class="login">
	<?
             $translatebhsxx->__1($komen);
	?>
	</p>
	</div>
	
<? } ?>
<p>&nbsp;</p>
</p>


</body>
</html>
