<?php
$file = isset($_GET['file']) ? $_GET['file'] : '';

if (!file_exists($file)) {
    die("File not found");
}

echo 'Permissions: ' . substr(sprintf('%o', fileperms($file)), -4) . "<br>";

if (is_readable($file)) {
    echo "File is readable<br>";
} else {
    echo "File is NOT readable<br>";
}

if (is_writable($file)) {
    echo "File is writable<br>";
} else {
    echo "File is NOT writable<br>";
}

?>
