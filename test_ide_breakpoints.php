<?php
// File: test_ide_breakpoints.php

// BARIS 4: Cobalah pasang breakpoint pertama Anda di sini
echo "Memulai skrip test_ide_breakpoints.php<br>";

$levelPengguna = "Admin";
$skorPemain = 0;

// BARIS 9: Cobalah pasang breakpoint kedua Anda di sini
$skorPemain = 150;
echo "Skor pemain awal: " . $skorPemain . "<br>";

function hitungBonusSkor($skorAwal, $level) {
    // BARIS 14: Cobalah pasang breakpoint di dalam fungsi ini
    $bonus = 0;
    if ($level === "Admin") {
        $bonus = 100;
    } elseif ($level === "Member") {
        $bonus = 50;
    }
    // BARIS 20: Periksa nilai $skorAwal dan $bonus di sini
    return $skorAwal + $bonus;
}

echo "Sebelum memanggil fungsi hitungBonusSkor.<br>";

// BARIS 25: Cobalah pasang breakpoint sebelum fungsi dipanggil
$skorTotal = hitungBonusSkor($skorPemain, $levelPengguna);
// BARIS 27: Periksa nilai $skorTotal setelah fungsi kembali

echo "Skor total setelah bonus: " . $skorTotal . "<br>";

if ($skorTotal > 200) {
    // BARIS 31: Breakpoint di dalam kondisi IF
    echo "Selamat! Skor Anda sangat tinggi!<br>";
    for ($i = 1; $i <= 3; $i++) {
        // BARIS 34: Breakpoint di dalam loop FOR
        echo "Iterasi pesan ke-" . $i . " untuk skor tinggi.<br>";
        $skorTotal += $i * 10; // Ubah skor di dalam loop
    }
    // BARIS 38: Periksa nilai $skorTotal setelah loop
    echo "Skor akhir setelah loop: " . $skorTotal . "<br>";
} else {
    echo "Skor Anda standar.<br>";
}

$itemPemain = array(
    'nama' => 'Pedang Legendaris',
    'kekuatan' => 150,
    'efekSamping' => null
);

// BARIS 49: Breakpoint terakhir sebelum skrip selesai
echo "Item pemain: " . $itemPemain['nama'] . " dengan kekuatan " . $itemPemain['kekuatan'] . ".<br>";

echo "Skrip selesai.<br>";

// SQL Injection Testing Demo
// This demonstrates secure vs vulnerable approaches

echo "<h1>SQL Injection Analysis Results</h1>";

// Vulnerable query example from list_ba.php
$vulnerable_example = '
$sql.=" A.NO_VENDOR LIKE \'$vendor\'";
$sql.=" A.NO_BA LIKE \'$no_ba\' ";
$sql.=" A.TGL_BA BETWEEN TO_DATE(\'$tanggal_mulai_sql\', \'DD-MM-YYYY\') AND TO_DATE(\'$tanggal_selesai_sql\', \'DD-MM-YYYY\') ";
';

echo "<h2>❌ VULNERABLE CODE FOUND:</h2>";
echo "<pre>" . htmlspecialchars($vulnerable_example) . "</pre>";

// Secure alternative
$secure_example = '
$sql = "SELECT * FROM EX_BA WHERE NO_VENDOR LIKE :vendor AND NO_BA LIKE :no_ba 
        AND TGL_BA BETWEEN TO_DATE(:tgl_mulai, \'DD-MM-YYYY\') AND TO_DATE(:tgl_selesai, \'DD-MM-YYYY\')";
        
$query = oci_parse($conn, $sql);
oci_bind_by_name($query, \':vendor\', $vendor);
oci_bind_by_name($query, \':no_ba\', $no_ba);
oci_bind_by_name($query, \':tgl_mulai\', $tanggal_mulai);
oci_bind_by_name($query, \':tgl_selesai\', $tanggal_selesai);
oci_execute($query);
';

echo "<h2>✅ SECURE CODE EXAMPLE:</h2>";
echo "<pre>" . htmlspecialchars($secure_example) . "</pre>";

// Attack payloads for testing
echo "<h2>🔍 PENETRATION TEST PAYLOADS:</h2>";

$payloads = array(
    "Basic SQL Injection" => "TEST' OR '1'='1",
    "Union Injection" => "' UNION SELECT USERNAME,PASSWORD FROM TB_USER_BOOKING--",
    "Error-based Injection" => "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT USER FROM DUAL), 0x7e)) = 1--",
    "Time-based Blind" => "' AND (SELECT COUNT(*) FROM ALL_TABLES WHERE ROWNUM <= 1000000) > 0--"
);

foreach($payloads as $type => $payload) {
    echo "<h3>$type:</h3>";
    echo "<pre>" . htmlspecialchars($payload) . "</pre>";
}

echo "<h2>📋 SECURITY CHECKLIST:</h2>";
echo "<ul>";
echo "<li>❌ Input validation missing</li>";
echo "<li>❌ SQL injection vulnerable</li>";
echo "<li>❌ No prepared statements</li>";
echo "<li>❌ No CSRF protection</li>";
echo "<li>❌ No rate limiting</li>";
echo "</ul>";

echo "<p><strong>RECOMMENDATION:</strong> Implement prepared statements immediately!</p>";
?>