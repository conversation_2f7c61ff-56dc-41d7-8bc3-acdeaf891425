<?

session_start();
include ('../include/my_fungsi.php');

$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

//$importtargetVolume='upload_customer_replenishment_report.php?act=update';
$cancelUrl='list_tax_opco_md.php?';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

//if ($fungsi->keamanan($halaman_id,$user_id)==0) {
//?>
<!--				<SCRIPT LANGUAGE="JavaScript">
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>
	 <a href="../index.php">Login....</a>-->
<?
//
//exit();
//}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>List ZMD CUSTOMER NOTAX</title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>
<body>

<div align="center">
    <table id="dg" title="Data List ZMD Customer Notax" class="easyui-datagrid" style="width:100%;height:350px">
        <thead>
            <tr>
                <th field="ID" width="10%">ID_NOTAX</th>
                <th field="SOLDTO" width="10%">KD_SOLD_TO</th>
                <th field="SHIPTO" width="10%">KD_SHIPTO</th>
                <th field="DELETE_MARK" width="25%">DEL</th></tr>
        </thead>
    </table>
    <div id="toolbar">
<!--        <a href="upload_customer_replenishment_report.php?act=update" title="Import Exel" class="easyui-linkbutton c6" iconCls="icon-edit" target="_blank">Upload Data</a>-->
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="deleteAct()">Delete</a>
        <!--<a href="list_mat_sales_2_download.php" title="Export Exel" class="easyui-linkbutton" iconCls="icon-print" target="_blank">Export Excel</a>-->
    </div>

    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px"
         closed="true" buttons="#dlg-buttons">
        <div class="ftitle">Master Material</div>
        <form id="fm" method="post" novalidate>

            <div class="fitem">
                <label>KD_SOLD_TO:</label>
                <input name="SOLDTO" id="SOLDTO" style="width:160px" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>KD_SHIPTO:</label>
                <input name="SHIPTO" id="SHIPTO" style="width:160px" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>DEL:</label>
                <input name="DELETE_MARK" id="DELETE_MARK" style="width:160px" class="easyui-textbox">
            </div>
        </form>
    </div>

    <div id="dlg-buttons">
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
</div>

<script type="text/javascript">
 $(function(){
    $("#dg").datagrid({
            url:'list_tax_opco_md_act.php?act=show',
            singleSelect:true,
            pagination:true,
            pageList:[5,10,20,30,40,50,100,200,300],
            pageSize:10,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto',
            toolbar:'#toolbar'

    });
    $('#dg').datagrid('enableFilter');

 });
 function newAct(){
    $('#dlg').dialog('open').dialog('setTitle','Create Master Material');
    $('#ID').combo('readonly', false);
    $('#fm').form('clear');
    url = 'list_tax_opco_md_act.php?act=add';
    //$('#ID_NOTAX').combobox('setValue', '7900');
     //set value tanggal
//    $('#TARGET_DATEF').datebox('setValue', myformatter(g_now1));

//       $('#org').combobox({
//          onChange:  function(newValue,oldValue) {
//            loadshiptolist();
//            if(newValue == 'ZAK'){
//                $('#qtysementara').val("").show();
//            } else{
//                $('#qtysementara').val("").hide();
//            }
//          }
//       });
}

function editAct(){
var row = $('#dg').datagrid('getSelected');
if (row){
    $('#dlg').dialog('open').dialog('setTitle','Edit Customer Replenishment');
//    var idnh = row.ID;
    //$('#').combobox('setValue', row.VKORG);
    $('#ID').textbox('readonly',row.ID);
    $('#SOLDTO').textbox('setValue',row.SOLDTO);
    $('#SHIPTO').textbox('setValue',row.SHIPTO);
    $('#DELETE_MARK').textbox('setValue',row.DELETE_MARK);

    url = "list_tax_opco_md_act.php?act=update&ID="+row.ID;
}else{
        alert("Pilih baris data yang ingin di edit terlebih dahulu !");
    }
}

function saveAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
        } else {
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function deleteAct(){

    var row = $('#dg').datagrid('getSelected');
    if(row){
//        if(row.KET != 'Dibatalkan'){
            $.messager.confirm('Confirm','Apakah Anda Yakin Ingin Menghapusnya?',function(r){
                if(r){
                    var strURL="list_tax_opco_md_act.php?act=delete&ID="+row.ID;
                    var req = getXMLHTTP();
                    if (req) {
//                        alert(company+" "+noeks+" "+nofaktur);
                        req.onreadystatechange = function() {
                            //            lodingact(0);
                            $.messager.progress({
                                title:'Please waiting',
                                msg:'Loading data...'
                            });
                            if (req.readyState == 4) {
                                // only if "OK"
                                if (req.status == 200) {
//                                    alert("SUKSES");
                                    //                                    alert(req.responseText);
                                    //                                    body1.appendChild(newdiv);
                                } else {
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                //                lodingact(1);
                                $.messager.progress('close');
                                $('#dg').datagrid('reload');
                            }
                        }
                        req.open("GET", strURL, true);
                        req.send(null);
                    }
                }
            })
//       } else {
//            alert('Dokumen yang sudah dibatalkan, tidak dapat dibatalkan kembali!!');
//       }
    } else {
        alert('Pilih Baris yang Akan Dibatalkan Terlebih Dahulu');
    }
}

function getXMLHTTP() {
		var xmlhttp=false;
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{
			try{
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}

		return xmlhttp;
    }

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<?
include ('../include/ekor.php');
?>
</body>
</html>
