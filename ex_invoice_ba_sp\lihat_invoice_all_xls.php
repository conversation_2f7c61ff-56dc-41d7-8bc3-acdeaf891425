<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
// include ('../include/validasi.php'); 
require_once ('../MainPHPExcel/MainPHPExcel.php'); 
require_once('../include/class.translation.php');
require_once ('../security_helper.php');
sanitize_global_input();
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

// $fungsi=new ex_fungsi();
// $conn=$fungsi->ex_koneksi();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

// $halaman_id=86;
// $halaman_id=$fungsi->getmainhalam_id($conn,$_SERVER['PHP_SELF']);
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
// $mp_coics=$fungsi->getComin($conn,$user_org);
$orgIn= $user_org;

$page="lihat_invoice_bag_darat_xls.php";
$currentPage="lihat_invoice_bag_darat_xls.php";
$komen="";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_shipment = $_POST['no_shipment'];
$distributor = $_POST['distributor'];
$tipe_transaksi = $_POST['tipe_transaksi'];
$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$warna_plat = $_POST['warna_plat'];
$no_invoice = $_POST['no_invoice'];
$no_invoice_expeditur = $_POST['no_invoice_expeditur'];
$no_ba = $_POST['no_ba'];
$status_invoice = $_POST['status_invoice'];
$no_faktur_pajak = $_POST['no_faktur_pajak'];

$sql = "SELECT * FROM (";
if($no_shipment=="" and $distributor=="" and $vendor=="" and $tipe_transaksi == "" and $tanggal_mulai == "" and $tanggal_selesai == "" and $warna_plat == "" and $no_invoice == "" and $no_invoice_expeditur == "" and $no_ba == "" and $status_invoice == "" and $no_faktur_pajak == ""){
    $sql .= "SELECT DISTINCT  EI.POSTING_DATE,EI.TOTAL_INV, EI.PAJAK_INV,EI.NO_INVOICE,  EI.ACCOUNTING_DOC,  EI.NO_INV_REVERSE,   ETH.NO_BA, EI.TGL_INVOICE, EI.NO_VENDOR,  EI.NAMA_VENDOR, ETH.STATUS,  EI.KETERANGAN, EI.NO_PAJAK_EX, 
    ETH.WARNA_PLAT 
    ,ROW_NUMBER() OVER (PARTITION BY EI.NO_INVOICE ORDER BY EI.POSTING_DATE ASC) AS rn
    FROM EX_INVOICE EI
    LEFT JOIN EX_TRANS_HDR  ETH ON EI.NO_INVOICE = ETH.NO_INVOICE 
    WHERE  ETH.DELETE_MARK = '0' 
    AND EI.ORG in ($orgIn) 
    ";
    //AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED')
}else {
    $pakeor=0;
    $sql .= "SELECT DISTINCT  EI.POSTING_DATE,EI.TOTAL_INV, EI.PAJAK_INV,EI.NO_INVOICE,  EI.ACCOUNTING_DOC,  EI.NO_INV_REVERSE,  ETH.NO_BA, EI.TGL_INVOICE, EI.NO_VENDOR,  EI.NAMA_VENDOR, ETH.STATUS,  EI.KETERANGAN, EI.NO_PAJAK_EX, 
    ETH.WARNA_PLAT 
    ,ROW_NUMBER() OVER (PARTITION BY EI.NO_INVOICE ORDER BY EI.POSTING_DATE ASC) AS rn
    FROM EX_INVOICE EI
    LEFT JOIN EX_TRANS_HDR  ETH ON EI.NO_INVOICE = ETH.NO_INVOICE 
    WHERE ";
    if($no_shipment!=""){
    $sql.=" ETH.NO_SHP_TRN LIKE '$no_shipment' ";
    $pakeor=1;
    }
    if($distributor!=""){
        if($pakeor==1){
        $sql.=" AND ( ETH.NAMA_SOLD_TO LIKE '$distributor' OR ETH.SOLD_TO LIKE '$distributor' ) ";
        }else{
        $sql.=" ( ETH.NAMA_SOLD_TO LIKE '$distributor' OR ETH.SOLD_TO LIKE '$distributor' ) ";
        $pakeor=1;
        }
    }
    if($vendor!=""){
        if($pakeor==1){
        $sql.=" AND ( EI.NAMA_VENDOR LIKE '$vendor' OR EI.NO_VENDOR = '".str_pad($vendor,10,"0",STR_PAD_LEFT)."' ) ";
        }else{
        $sql.=" ( EI.NAMA_VENDOR LIKE '$vendor' OR EI.NO_VENDOR = '".str_pad($vendor,10,"0",STR_PAD_LEFT)."' ) ";
        $pakeor=1;
        }
    }
    if($tipe_transaksi!=""){
        if($pakeor==1){
        $sql.=" AND ETH.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
        }else{
        $sql.=" ETH.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
        $pakeor=1;
        }
    }
    if($tanggal_mulai!="" or $tanggal_selesai!=""){

        if ($tanggal_mulai=="")$tanggal_mulai_sql = "01-01-1990";
        else $tanggal_mulai_sql = $tanggal_mulai;
        if ($tanggal_selesai=="")$tanggal_selesai_sql = "12-12-9999";
        else $tanggal_selesai_sql = $tanggal_selesai;

        if($pakeor==1){
        $sql.=" AND EI.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
        }else{
        $sql.="  EI.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
        $pakeor=1;
        }
    }
    if($warna_plat!=""){
        if($pakeor==1){
        $sql.=" AND ETH.WARNA_PLAT LIKE '$warna_plat' ";
        }else{
        $sql.=" ETH.WARNA_PLAT LIKE '$warna_plat' ";
        $pakeor=1;
        }
    }
    if($no_invoice_expeditur!=""){
        if($pakeor==1){
        $sql.=" AND ETH.NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
        }else{
        $sql.=" ETH.NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
        $pakeor=1;
        }
    }
    if($no_invoice!=""){
        if($pakeor==1){
        $sql.=" AND EI.NO_INVOICE LIKE '$no_invoice' ";
        }else{
        $sql.=" EI.NO_INVOICE LIKE '$no_invoice' ";
        $pakeor=1;
        }
    }
    if($no_ba!=""){
        if($pakeor==1){
        $sql.=" AND ETH.NO_BA LIKE '$no_ba' ";
        }else{
        $sql.=" AND ETH.NO_BA LIKE '$no_ba' ";
        $pakeor=1;
        }
    }
    if($no_faktur_pajak!=""){
        if($pakeor==1){
        $sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
        }else{
        $sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
        $pakeor=1;
        }
    }
    // if($status_invoice!=""){
        // if($pakeor==1){
        // $sql.=" AND EBI.STATUS_BA_INVOICE LIKE '$status_invoice' ";
        // }else{
        // $sql.=" AND ETH.STATUS_BA_INVOICE LIKE '$status_invoice' ";
        // $pakeor=1;
        // }
    // }
    
    
    
    
    $sql.="  AND EI.ORG in ($orgIn) AND ETH.DELETE_MARK = '0' ORDER BY EI.NO_INVOICE DESC";
    // $sql.="  AND EI.ORG in ($orgIn) AND ETH.DELETE_MARK = '0'  AND ETH.NO_BA IS NOT NULL ORDER BY EI.NO_INVOICE DESC";// AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED') ------ ORDER BY ETH.ORG,ETH.VENDOR, ETH.NO_SHP_TRN ASC
}
$sql .= ") subquery
WHERE rn = 1";
//  echo $sql;
$query= oci_parse($conn, $sql);
oci_execute($query);
$total_tagihan=0;
while($row=oci_fetch_array($query)){  
        $sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";   
        $query_s= @oci_parse($conn, $sqlS);
        @oci_execute($query_s);
        $row_s=@oci_fetch_array($query_s); 
        
    if($status_invoice == $row_s[STATUS_BA_INVOICE] || $status_invoice==''){
        
        $no_ppl_v[]=$row[ACCOUNTING_DOC];
        $produk_v[]=$row[KODE_PRODUK];
        $no_invoice_v[]=$row[NO_INVOICE]; 
        $warna_plat_v[]=$row[WARNA_PLAT]; 
        //$keterangan_v[]=$row_s[KOMENTAR_REJECT];
        //$status_v[]=$row_s[STATUS_BA_INVOICE];
        //$status_id =$row_s[STATUS_BA_INVOICE];
            
            $no_ba_v[]=$row[NO_BA]; 
            $tgl_invoice_v[]=$row[TGL_INVOICE];  
            $vendor_v[]=$row[NO_VENDOR];
            $nama_vendor_v[]=$row[NAMA_VENDOR];
            $no_pajak_ex_v[]=$row[NO_PAJAK_EX];
            $no_inv_reverse_v[]=$row[NO_INV_REVERSE];
            $total_inv[]=$row[TOTAL_INV];
            $pajak_inv[]=$row[PAJAK_INV];
            $posting_date[]=$row[POSTING_DATE];
            //$keterangan_v[]=$row[KOMENTAR_REJECT]; 
            //$status_v[]=$row[STATUS_BA_INVOICE]; 
            //$status_id = $row[STATUS_BA_INVOICE]; 
            
            $sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";   
            $query_s= @oci_parse($conn, $sqlS);
            @oci_execute($query_s);
            $row_s=@oci_fetch_array($query_s); 
            $keterangan_v[]=$row_s[KOMENTAR_REJECT];
            $status_v[]=$row_s[STATUS_BA_INVOICE];
            $status_id =$row_s[STATUS_BA_INVOICE];
            
            if($status_id==10){
                $status_name_v[]= "CREATE INVOICE";
            }else if($status_id==20){
                $status_name_v[]= "UPLOAD INVOICE";
            }else if($status_id==30){
                $status_name_v[]= "REVERSED";
            }else if($status_id==40){
                $status_name_v[]= "REJECTED";
            }else if($status_id==45){
                $status_name_v[]= "CANCEL PPL & INVOICE";
            }else if($status_id==50){
                $status_name_v[]= 'APPROVED BY SPV';
            }else if($status_id==60){
                $status_name_v[]= 'GENERATE PPL';
            }else if($status_id==70){
                $status_name_v[]= 'SIMULATE & POSTING PPL';
            }else if($status_id==80){
                $status_name_v[]= 'REJECT BY MANAJER VERIFIKASI';
            }else if($status_id==90){
                $status_name_v[]= 'APPROVED  BY MANAJER VERIFIKASI';
            }else if($status_id==100){
                $status_name_v[]= 'REJECT BY SM ACCOUNTING';
            }else if($status_id==110){
                $status_name_v[]= 'APPROVED  BY SM ACCOUNTING';
            }else if($status_id==120){
                $status_name_v[]= 'EKSPEDISI BENDAHARA';
            }else {
                $status_name_v[]= "";
            }
             
            //$status_v[]=$row[STATUS_BA_INVOICE]; 
            $sqli = "SELECT *
                        FROM EX_BA 
                        WHERE NO_BA = '".$row[NO_BA]."'";   
            $query_li= @oci_parse($conn, $sqli);
            @oci_execute($query_li);
            $row_li=@oci_fetch_array($query_li); 
            $pajak_v[]=$row_li[PAJAK_INV];
            $total_klaim_v[]=$row_li[TOTAL_INV];
            
                #cari apa sudah di run/belum

                $no_invoice_sap_x = '';
                $sql2 = "SELECT *
                            FROM EX_INVOICE 
                            WHERE NO_INVOICE = '".$row[NO_INVOICE]."'"; 
                            #AND NO_INVOICE_EX = '*****************' ";
                
                $query_bn= @oci_parse($conn, $sql2);
                @oci_execute($query_bn);
                $row_bn=@oci_fetch_array($query_bn);
                $no_invoice_sap_x =$row_bn[NO_INVOICE_SAP];  
                
                $no_invoice_sap_v[] = $no_invoice_sap_x;
    }


} 


$total=count($no_invoice_v);

if($total>0){
       
        $namafile="daftar_invoice.xls";
        send($namafile);
        $WritePHPExcel = new PHPExcel();
        $WritePHPExcel->setActiveSheetIndex(0);
        $colomSt='A';
        $WritePHPExcel->getActiveSheet()->setTitle('Sales Order');//title sheet
       
        $Worksheet1 = $WritePHPExcel->getActiveSheet();
        
            $Worksheet1->setCellValueByColumnAndRow(0, 1, $translatebhsxx->__xls('No.'));
            $Worksheet1->setCellValueByColumnAndRow(1, 1, $translatebhsxx->__xls('No. Invoice'));
            $Worksheet1->setCellValueByColumnAndRow(2, 1, $translatebhsxx->__xls('No BA'));
            $Worksheet1->setCellValueByColumnAndRow(3, 1, $translatebhsxx->__xls('No PPL')); 
            $Worksheet1->setCellValueByColumnAndRow(4, 1, $translatebhsxx->__xls('No. Cancel PP'));
            $Worksheet1->setCellValueByColumnAndRow(5, 1, $translatebhsxx->__xls('Tanggal Invoice'));
            $Worksheet1->setCellValueByColumnAndRow(6, 1, $translatebhsxx->__xls('Posting Date'));
            $Worksheet1->setCellValueByColumnAndRow(7, 1, $translatebhsxx->__xls('No. Faktur Pajak'));
            $Worksheet1->setCellValueByColumnAndRow(8, 1, $translatebhsxx->__xls('Vendor'));  
            $Worksheet1->setCellValueByColumnAndRow(9, 1, $translatebhsxx->__xls('DPP'));
            $Worksheet1->setCellValueByColumnAndRow(10, 1, $translatebhsxx->__xls('PPN'));  
            $Worksheet1->setCellValueByColumnAndRow(11, 1, $translatebhsxx->__xls('Total SPJ'));
            $Worksheet1->setCellValueByColumnAndRow(12, 1, $translatebhsxx->__xls('Warna PLAT'));
            $Worksheet1->setCellValueByColumnAndRow(13, 1, $translatebhsxx->__xls('Status'));
            $Worksheet1->setCellValueByColumnAndRow(14, 1, $translatebhsxx->__xls('Keterangan'));
           
        
        $colomAk = $Worksheet1->getHighestColumn();
        if($colomSt!='' && $colomAk!=''){
            $WritePHPExcel->getActiveSheet()->getStyle($colomSt."1:".$colomAk."1")->getFont()->setBold(true);//bold header
            // $WritePHPExcel->getActiveSheet()->setCellValueExplicit($colomSt."1:".$colomAk."1", PHPExcel_Cell_DataType::TYPE_STRING);
        }
        $Worksheet1->getStyle($colomSt.'1:'.$colomAk.'1')->applyFromArray($styleHead);//style head         
        // Version 4 fixed
        for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true);//auto size
        }
	
	for ($i=0; $i < $total; $i++) {
            //setROW
            $j=$i+2;
            // if($colomSt!='' && $colomAk!='' ){
            //      $Worksheet1->getStyle($colomSt.$j.':'.$colomAk.$j)->applyFromArray($styleBorder);//style border record
            // }
                // if($org=='6000'){

                    $total_tagihan += $total_klaim_v[$i]+$pajak_v[$i];
                    $b=$i+1;
                        // if(($i % 2) == 0)	{	 
                        // echo "<tr class='row0' id='$rowke' >";
                        //     }
                        // else	{	
                        // echo "<tr class='row1'  id='$rowke' >";
                        //     }	
                                // $orgCom="orgke".$i;    
                                  
                    $Worksheet1->setCellValueByColumnAndRow(0, $j, $b);
                    $Worksheet1->setCellValueByColumnAndRow(1, $j, "'".sprintf("%010s", $no_invoice_v[$i]));
                    // $Worksheet1->setCellValueByColumnAndRow(2, $j, "'".sprintf("%010s", $no_ba_v[$i]));
                    if($no_ba_v[$i] == '')
                    $Worksheet1->setCellValueByColumnAndRow(2, $j, "");
                    else
                    $Worksheet1->setCellValueByColumnAndRow(2, $j, "'".$no_ba_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(3, $j, $no_ppl_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(4, $j, $no_inv_reverse_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(5, $j, $tgl_invoice_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(6, $j, isset($posting_date[$i]) ? $posting_date[$i] : '-'  );
                    $Worksheet1->setCellValueByColumnAndRow(7, $j, $no_pajak_ex_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(8, $j, $vendor_v[$i].'-'.$nama_vendor_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(9, $j, 'Rp.'.number_format($total_inv[$i],0,",","."));
                    $Worksheet1->setCellValueByColumnAndRow(10, $j, 'Rp.'.number_format($pajak_inv[$i],0,",","."));
                    $Worksheet1->setCellValueByColumnAndRow(11, $j, 'Rp.'.number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."));
                    $Worksheet1->setCellValueByColumnAndRow(12, $j, $warna_plat_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(13, $j,  $status_name_v[$i]);
                    $Worksheet1->setCellValueByColumnAndRow(14, $j, $keterangan_v[$i]);
                    
                }
	}
        
        $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
        //$objWriter->save($namafile);
        $objWriter->save("php://output");
        
// }
?>
