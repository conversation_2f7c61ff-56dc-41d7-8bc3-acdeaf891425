<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=20;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?php

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="list_edit_so_royalty.php";
$no_pp = trim($_POST['no_pp']);
$no_pp = $fungsi->sapcode($no_pp);
$branch_plant = trim($_POST['branch_plant']);
$sold_to = trim($_POST['sold_to']);
$sold_to = $fungsi->sapcode($sold_to);
$ship_to = trim($_POST['ship_to']);
$produk = trim($_POST['produk']);
$jenis_kirim = trim($_POST['jenis_kirim']);
$propinsi = trim($_POST['propinsi']);
//$tglkirimnext=date("d-m-Y",strtotime("+1 days"));
$tgl_kirim = trim($_POST['tgl_kirim']);
$plant_asal_up = trim($_POST['plant']);
$nama_plant_up = trim($_POST['nama_plant']);
$no_so = trim($_POST['no_so']);
$no_so_old = trim($_POST['no_so_old']);

$currentPage="list_edit_so_royalty.php";
$komen="";
if(isset($_POST['cari'])){
	if($no_pp=="" and $branch_plant=="" and $sold_to == "" and $ship_to == "" and $propinsi=="" and $plant_asal_up=="" and $produk == "" and $tgl_kirim == "" and $jenis_kirim == "" and $no_so == ""){
		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE DELETE_MARK = '0' AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND no_shp_old is null and FLAG_LELANG is null AND SO_TYPE<>'ZPR' AND STATUS_LINE != 'REJECTED' AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0)
 AND SO_TYPE <> 'ZRE' ORDER BY NO_PP ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE STATUS_LINE != 'REJECTED' AND SO_TYPE<>'ZPR' AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND ";
		if($no_pp!=""){
			if($pakeor==1){
			$sql.=" ltrim(NO_PP,'0') = ltrim('$no_pp','0') ";
			}else{
			$sql.=" ltrim(NO_PP,'0') = ltrim('$no_pp','0') ";
			$pakeor=1;
			}
		}
		if($branch_plant!=""){
			if($pakeor==1){
			$sql.=" AND BPLANT LIKE '$branch_plant' ";
			}else{
			$sql.=" BPLANT LIKE '$branch_plant' ";
			$pakeor=1;
			}
		}
		if($sold_to!=""){
			if($pakeor==1){
			$sql.=" AND SOLD_TO LIKE '$sold_to'";
			}else{
			$sql.=" SOLD_TO LIKE '$sold_to' ";
			$pakeor=1;
			}
		}
		if($ship_to!=""){
			if($pakeor==1){
			$sql.=" AND SHIP_TO LIKE '$ship_to' ";
			}else{
			$sql.=" SHIP_TO LIKE '$ship_to' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KODE_PRODUK LIKE '%$produk%'";
			}else{
			$sql.=" KODE_PRODUK LIKE '%$produk%' ";
			$pakeor=1;
			}
		}
                if($propinsi!=""){
			if($pakeor==1){
			$sql.=" AND KD_PROV LIKE '%$propinsi%'";
			}else{
			$sql.=" KD_PROV LIKE '%$propinsi%' ";
			$pakeor=1;
			}
		}
		if($jenis_kirim!=""){
			if($pakeor==1){
			$sql.=" AND INCOTERM LIKE '$jenis_kirim' ";
			}else{
			$sql.=" INCOTERM LIKE '$jenis_kirim' ";
			$pakeor=1;
			}
		}
                if($tgl_kirim!=""){
                    list($day,$month,$year)=split("-",$tgl_kirim);
                    $tglm=$year.$month.$day;
			if($pakeor==1){
			$sql.=" AND to_char(TGL_KIRIM_PP,'YYYYMMDD')='$tglm' ";
			}else{
			$sql.=" to_char(TGL_KIRIM_PP,'YYYYMMDD')='$tglm' ";
			$pakeor=1;
			}
		}
                if($plant_asal_up!=""){
			if($pakeor==1){
			$sql.=" AND PLANT_ASAL='$plant_asal_up' ";
			}else{
			$sql.=" PLANT_ASAL='$plant_asal_up' ";
			$pakeor=1;
			}
		}
        if($no_so!=""){
			if($pakeor==1){
			$sql.=" AND ltrim(NO_SO,'0')=ltrim('$no_so','0') ";
			}else{
			$sql.=" ltrim(NO_SO,'0')=ltrim('$no_so','0') ";
			$pakeor=1;
			}
		}
		$sql.=" AND no_shp_old is null and FLAG_LELANG is null AND SO_TYPE <> 'ZRE' AND DELETE_MARK = '0' AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0) ORDER BY NO_PP ASC";
	}
	//  echo '<pre>'; echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_pp_v[]=$row[NO_PP];
		$tgl_kirim_v[]=$row[TGL_KIRIM_PP];
		$tgl_pp_v[]=$row[TGL_PP];
		$produk_v[]=$row[NAMA_PRODUK];
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[NAMA_SHIP_TO];
		$alamat_v[]=$row[ALAMAT_SHIP_TO];
		$kddistrik_v[]=$row[KODE_TUJUAN];
		$nmdistrik_v[]=$row[NAMA_TUJUAN];
		$qty_v[]=$row[QTY_PP];
                $route[]=$row[ROUTE];
                $incoterm[]=$row[INCOTERM];
		$id_v[]=$row[ID];
		$item_v[]=$row[ITEM_NUMBER];  
		$status_v[]=$row[STATUS_LINE];  
		$royalty_v[]=$row[IS_ROYALTY];
		$msa_v[]=$row[IS_MSA];
        $no_so_v[]=$row[NO_SO];
	}
	$total=count($no_pp_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Sales Order</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar PP / SO 1</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form SearchPP / SO </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Permintaan Pembelian </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" value="<?=$no_pp?>"/><input name="org" type="hidden" id="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Sales Order </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_so" name="no_so" value="<?=$no_so?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Branch Plant </td>
      <td  class="puso">:</td>
      <td ><select name="branch_plant" id="Branch Plant" >
        <option value="">---Pilih---</option>
        <? $fungsi->or_jns_plant($branch_plant); ?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Sold To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="sold_to" name="sold_to"  value="<?=$sold_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="ship_to" name="ship_to"  value="<?=$ship_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Propinsi </td>
      <td  class="puso">:</td>
      <td >
          <select name="propinsi" id="propinsi">
            <option value=''>--All Propinsi--</option>
            <?
            $sqlpropinsi= "
                        SELECT KD_PROV,NM_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0
                        order by KD_PROV asc
                        ";

            //echo $sqlPlant;
            $query2= oci_parse($conn, $sqlpropinsi);
            oci_execute($query2);
            while($rowProv=oci_fetch_array($query2)){ 
                $KDprov=$rowProv['KD_PROV'];
                $NAMEprov=$rowProv['NM_PROV'];
                if($propinsi==$KDprov){ $selectedVi=" selected ";}else{$selectedVi="";}
            ?>
                 <option value='<?=$KDprov;?>' <?=$selectedVi;?> ><?=$KDprov;?>&nbsp;&nbsp;<?=$NAMEprov;?></option>
            <?
            }
            ?>
        </select>
      
      </td>
    </tr>  
    <tr>
            <td  class="puso">Jenis Semen</td>
            <td  class="puso">:</td>
            <td>		
                    <SELECT NAME="produk">
                            <option value="" <? if($_POST[produk]=='') echo ' selected' ?>>&nbsp;All&nbsp;</option>
                            <option value="121-301" <? if($_POST[produk]=='121-301') echo ' selected' ?>>&nbsp;Zak&nbsp;</option>
                            <option value="121-302" <? if($_POST[produk]=='121-302') echo ' selected' ?>>&nbsp;Curah&nbsp;</option>
                    </SELECT>
            </td>
    </tr>  
    <tr>
        <td ><strong>Plant</strong></td>
        <td width="10"><strong>:</strong></td>
        <td width="153" colspan="2">
            <div id="plantdiv">
                <input name="plant" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>&nbsp;&nbsp;&nbsp;&nbsp;
                <input name="nama_plant" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
                <input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
                <input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
              </div>
        </td>
    </tr>   
    <tr>
      <td  class="puso">Jenis Pengiriman </td>
      <td  class="puso">:</td>
      <td ><select name="jenis_kirim" id="Jenis Pengiriman" >
          <option value="">---Pilih---</option>
          <? $fungsi->or_jenis_kirim($jenis_kirim); ?>
      </select></td>
    </tr>
     <tr>
      <td  class="puso">Tanggal Kirim </td>
      <td  class="puso">:</td>
      <td ><input name="tgl_kirim" type="text" id="tgl_kirim" size=12 value="<?=$tglkirimnext;?>" onClick="return showCalendar('tgl_kirim');" /></td>
    </tr>   
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Approval Permintaan Pembelian </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
        <td align="center"><strong >No SO</strong></td>
		<td align="center"><strong >Tanggal  PP</strong></td>
		<td align="center"><strong >Kode </strong></td>
		<td align="center"><strong >Distributor</strong></td>
		 <td align="center"><strong>Kode   </strong></td>
		 <td align="center"><strong>Distrik   </strong></td>
		 <td align="center"><strong>Ship To   </strong></td>
		 <td align="center"><strong>Produk</strong></td>
                 <td align="center"><strong>Qty</strong></td>                    
		 <td align="center"><strong>Incoterm</strong></td>
                 <td align="center"><strong>Route</strong></td>
		 <td align="center"><strong>Tgl Kirim</strong></td>
		 <td align="center"><strong>Status</strong></td>
		 <td align="center"><strong>Royalty</strong></td>
		<td align="center"><strong>MSA</strong></td>
		 <td align="center"><strong>Approval</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	

		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp_v[$i]; ?></td>
        <td align="center"><? echo $no_so_v[$i]; ?></td>
		<td align="center"><? echo $tgl_pp_v[$i]; ?></td>
		<td align="left"><? echo $sold_to_v[$i]; ?></td>
		<td align="left"><? echo $nama_sold_to_v[$i]; ?></td>
		<td align="left"><? echo $kddistrik_v[$i]; ?></td>	
		<td align="left"><? echo $nmdistrik_v[$i]; ?></td>	
		<td align="left"><? echo ''.$ship_to_v[$i].', '.$alamat_v[$i].''; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? 
				$tbl="OR_TRANS_APP";
				$field=array('NO_PP','ITEM_NUMBER');
				$findby=array("$no_pp_v[$i]","$item_v[$i]");
				$qtyrel=$fungsi->findSumByMany($conn,$tbl,$field,$findby,'QTY_APPROVE'); 
				$qtysisa=$qty_v[$i] - $qtyrel;
				echo number_format($qtysisa,0,",","."); ?></td>
                <td align="center"><? echo $incoterm[$i]; ?></td>
                <td align="center"><? echo $route[$i]; ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $status_v[$i]; ?></td>
		<td align="center"><? echo ($royalty_v[$i]=='X' ? 'YES' : 'NO'); ?></td>
		<td align="center"><? echo ($msa_v[$i]=='X' ? 'YES' : 'NO'); ?></td>
		<td align="center"><a href="edit_so_royalty.php?id_detail=<?=$id_v[$i]?>">EDIT</a></td>
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="15" align="center">
		<a href="list_edit_so_royalty.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
