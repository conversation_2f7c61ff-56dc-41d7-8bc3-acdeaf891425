<?
session_start();
include('../include/or_fungsi.php');
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi = new or_fungsi();
$conn = $fungsi->or_koneksi();

$halaman_id=3161;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

$no_invoice = $_GET['no_invoice'];
// $tgl_invoice_start = $_POST['tgl_invoice_start'];
// $tgl_invoice_end = $_POST['tgl_invoice_end'];
$no_faktur_pajak = $_GET['no_faktur_pajak']; 

$data = array();

if ($vendor == "" and $tgl_invoice_start == "" and $tgl_invoice_end == ""  and $no_invoice == "" and $no_faktur_pajak == "") {
    $sql = "SELECT DISTINCT EI.*, to_char(EI.TGL_INVOICE,'DD-MM-YYYY') as TGL_INVOICE1, ETH.NO_BA, ETH.TANGGAL_BA, ETH.WARNA_PLAT 
    FROM EX_INVOICE  EI
    join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
    WHERE EI.DELETE_MARK ='0' AND ETH.NO_BA IS NOT NULL AND EI.ORG in ($orgIn) AND EI.NO_INVOICE IS NOT NULL ORDER BY EI.ORG,EI.NO_VENDOR, EI.NO_INVOICE DESC";
} else {
    $pakeor = 0;
    $sql = "SELECT DISTINCT EI.*, to_char(TGL_INVOICE,'DD-MM-YYYY') as TGL_INVOICE1, ETH.NO_BA, ETH.TANGGAL_BA, ETH.WARNA_PLAT FROM EX_INVOICE  EI
    join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
    WHERE ";
    // if($vendor!=""){
    // $sql.=" ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
    $pakeor = 1;
    $sql .= "  EI.DELETE_MARK ='0' ";

    if ($no_invoice != "") {
        if ($pakeor == 1) {
            $sql .= " AND EI.NO_INVOICE LIKE '$no_invoice' ";
        } else {
            $sql .= " EI.NO_INVOICE LIKE '$no_invoice' ";
            $pakeor = 1;
        }
    }
	if($no_faktur_pajak!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			}else{
			$sql.=" EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			$pakeor=1;
			}
		}

    if($tgl_invoice_start!="" && $tgl_invoice_end!=""){
        if($pakeor==1){
            $sql.=" AND EI.TGL_INVOICE BETWEEN TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') AND TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
        }else{
            $sql.=" EI.TGL_INVOICE BETWEEN TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') AND TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
            $pakeor=1;
        }
    } else if($tgl_invoice_start!="" && $tgl_invoice_end==""){
        if($pakeor==1){
            $sql.=" AND EI.TGL_INVOICE >= TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') ";
        }else{
            $sql.=" EI.TGL_INVOICE >= TO_DATE('$tgl_invoice_start', 'YYYY-MM-DD') ";
            $pakeor=1;
        }
    } else if($tgl_invoice_start=="" && $tgl_invoice_end!=""){
        if($pakeor==1){
            $sql.=" AND EI.TGL_INVOICE <= TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
        }else{
            $sql.=" EI.TGL_INVOICE <= TO_DATE('$tgl_invoice_end', 'YYYY-MM-DD') ";
            $pakeor=1;
        }
    }

    $sql .= " AND EI.ORG in ($orgIn) AND ETH.NO_BA IS NOT NULL ORDER BY EI.ORG, EI.NO_INVOICE DESC";
}
// echo $sql;
$query = oci_parse($conn, $sql);
oci_execute($query);

$sqlcek = "SELECT NO_INVOICE FROM KPI_TERIMA_INV_VENDOR A, KPI_TERIMA_ASSINGMENT B, TB_USER_BOOKING C WHERE C.ID = B.ASSING_TO AND B.NO_GROUP_VENDOR = A.NOGROUP_VENDOR
                AND A.DEL = '0' AND C.ID = '$user_id' AND C.DELETE_MARK = '0' AND C.ASSIGN_TYPE = 'OPERATOR'";
$querycek = oci_parse($conn, $sqlcek);
oci_execute($querycek);
$noinvfilter = array();
while ($datacek = oci_fetch_assoc($querycek)) {
    array_push($noinvfilter, $datacek[NO_INVOICE]);
}

$sqlcek1 = "SELECT ASSIGN_TYPE FROM TB_USER_BOOKING WHERE ID = '$user_id' AND DELETE_MARK = '0'";
$querycek1 = oci_parse($conn, $sqlcek1);
oci_execute($querycek1);
while ($datacek1 = oci_fetch_assoc($querycek1)) {
    $jenisuser = $datacek1[ASSIGN_TYPE];
}

while ($row = oci_fetch_array($query)) {
    $com[] = $row[ORG];
    $no_invoice_sap_v[] = $row[NO_INVOICE_SAP];
    $no_invoice_v[] = $row[NO_INVOICE];
    $no_invoice_in = $row[NO_INVOICE];
    $vendor_v[] = $row[NO_VENDOR];
    $nama_vendor_v[] = $row[NAMA_VENDOR];
    $no_pajak_ex_v[] = $row[NO_PAJAK_EX];
    $TGL_INVOICE_v[] = $row[TGL_INVOICE];
    $tgl_ba_v[] = $row[TANGGAL_BA];
    $no_ba_v[] = $row[NO_BA];
    $status_v[] = $row[TRACKING_INV];
    $no_ppl_v[] = $row[ACCOUNTING_DOC];
    $doc_number_v[] = $row[INV_DOC_NUMBER_CONV];
    $ket_ekspedisi_v[] = $row[KETERANGAN_EKSPEDISI];
    $warna_plat_v[] = $row[WARNA_PLAT];

    $sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE,  NAMA, NAMA_LENGKAP from EX_BA_INVOICE 
            JOIN TB_USER_BOOKING TUB ON TUB.ID = EX_BA_INVOICE.CREATED_BY
            where EX_BA_INVOICE.ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";
    //echo $sqlS;
    $query_s = @oci_parse($conn, $sqlS);
    @oci_execute($query_s);
    $row_s = @oci_fetch_array($query_s);
    $nama_v[] = $row_s[NAMA_LENGKAP];
    $keterangan_v[] = $row_s[KOMENTAR_REJECT];
    //$status_v[]=$row_s[STATUS_BA_INVOICE];
    //$status_id =$row_s[STATUS_BA_INVOICE];

    $status_id = $row[TRACKING_INV];
    if ($status_id == 10) {
        $status_name_v[] = "CREATE INVOICE";
    } else if ($status_id == 20) {
        $status_name_v[] = "UPLOAD INVOICE";
    } else if ($status_id == 30) {
        $status_name_v[] = "REVERSED";
    } else if ($status_id == 40) {
        $status_name_v[] = "REJECTED";
    } else if ($status_id == 45) {
        $status_name_v[] = "CANCEL PPL & INVOICE";
    } else if ($status_id == 50) {
        $status_name_v[] = 'APPROVED BY SPV';
    } else if ($status_id == 60) {
        $status_name_v[] = 'GENERATE PPL';
    } else if ($status_id == 70) {
        $status_name_v[] = 'SIMULATE & POSTING PPL';
    } else if ($status_id == 80) {
        $status_name_v[] = 'REJECT BY MANAJER VERIFIKASI';
    } else if ($status_id == 90) {
        $status_name_v[] = 'APPROVED  BY MANAJER VERIFIKASI';
    } else if ($status_id == 100) {
        $status_name_v[] = 'REJECT BY SM VERIFIKASI';
    } else if ($status_id == 110) {
        $status_name_v[] = 'APPROVED  BY SM VERIFIKASI';
    } else if ($status_id == 120) {
        $status_name_v[] = 'EKSPEDISI BENDAHARA';
    } else {
        $status_name_v[] = "";
    }






    $sqlcek = "SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE  NO_INVOICE = '$no_invoice_in' AND DELETE_MARK = '0' ";
    $querycek = oci_parse($conn, $sqlcek);
    oci_execute($querycek);
    $row_data = oci_fetch_assoc($querycek);
    $total_klaim_in = $row_data[TOTAL_KLAIM];
    $total_shp_in = $row_data[SHP_COST];
    $total_ktg_in = $row_data[TOTAL_KTG];
    $total_semen_in = $row_data[TOTAL_SEMEN];
    $total_pdpks_in = $row_data[TOTAL_PDPKS];
    $total_pdpkk_in = $row_data[TOTAL_PDPKK];

    if ($row[PAJAK_INV] > 0)
        $pajak = 0.1 * $row_data[TOTAL_KLAIM];
    else
        $pajak = 0;


    $klaim_semen_v[] = $row_data[TOTAL_SEMEN];
    $klaim_ktg_v[] = $row_data[TOTAL_KTG];
    $pdpks_v[] = $row_data[TOTAL_PDPKS];
    $pend_ktg_v[] = $row_data[TOTAL_PDPKK];
    $total_klaim_v[] = $row_data[TOTAL_KLAIM];
    $pajak_v[] = $pajak;
    $status_dokumen[] = $row['STATUS_DOKUMEN'];
    // var_dump($row);

}
$total = count($no_invoice_v);
if ($total < 1) {
    $komen = "Tidak Ada Data Yang Ditemukan";
}


$total = count($com);

if ($total > 0) {
    HeaderingExcel('report_tracking_invoice.xls');

    // Creating a workbook
    $workbook = new Workbook("-");
    // Adding format
    $format_bold = &$workbook->add_format();
    $format_bold->set_bold();
    // Creating the first worksheet
    $worksheet1 = &$workbook->add_worksheet('Tracking Invoice');
    //$worksheet1->set_column(1, 1, 40);
    //$worksheet1->set_row(1, 20);


    $worksheet1->write(0, 0, 'No', $format_bold);
    $worksheet1->write(0, 1, 'No BA Rekaputalasi', $format_bold);
    $worksheet1->write(0, 2, 'No Invoice', $format_bold);
    $worksheet1->write(0, 3, 'No PPL', $format_bold);
    $worksheet1->write(0, 4, 'No Dokumen', $format_bold);
    $worksheet1->write(0, 5, 'Total', $format_bold);
    $worksheet1->write(0, 6, 'Warna Plat', $format_bold);
    $worksheet1->write(0, 7, 'Status Create Invoice', $format_bold);
    $worksheet1->write(0, 8, 'Tanggal', $format_bold);
    $worksheet1->write(0, 9, 'User', $format_bold);

    $worksheet1->write(0, 10, 'Status Upload Invoice', $format_bold);
    $worksheet1->write(0, 11, 'Tanggal', $format_bold);
    $worksheet1->write(0, 12, 'User', $format_bold);

    $worksheet1->write(0, 13, 'Status Reversed Invoice', $format_bold);
    $worksheet1->write(0, 14, 'Tanggal', $format_bold);
    $worksheet1->write(0, 15, 'User', $format_bold);

    $worksheet1->write(0, 16, 'Status Rejected Invoice', $format_bold);
    $worksheet1->write(0, 17, 'Tanggal', $format_bold);
    $worksheet1->write(0, 18, 'User', $format_bold);

    $worksheet1->write(0, 19, 'Status Cancel PPL Invoice', $format_bold);
    $worksheet1->write(0, 20, 'Tanggal', $format_bold);
    $worksheet1->write(0, 21, 'User', $format_bold);

    $worksheet1->write(0, 22, 'Status Approved SPV Invoice', $format_bold);
    $worksheet1->write(0, 23, 'Tanggal', $format_bold);
    $worksheet1->write(0, 24, 'User', $format_bold);

    $worksheet1->write(0, 25, 'Status Generate PPL', $format_bold);
    $worksheet1->write(0, 26, 'Tanggal', $format_bold);
    $worksheet1->write(0, 27, 'User', $format_bold);

    $worksheet1->write(0, 28, 'Status Posting PPL', $format_bold);
    $worksheet1->write(0, 29, 'Tanggal', $format_bold);
    $worksheet1->write(0, 30, 'User', $format_bold);

    $worksheet1->write(0, 31, 'Status Reject Manajer Verifikasi', $format_bold);
    $worksheet1->write(0, 32, 'Tanggal', $format_bold);
    $worksheet1->write(0, 33, 'User', $format_bold);

    $worksheet1->write(0, 34, 'Status Approved Manajer Verifikasi', $format_bold);
    $worksheet1->write(0, 35, 'Tanggal', $format_bold);
    $worksheet1->write(0, 36, 'User', $format_bold);

    $worksheet1->write(0, 37, 'Status Rejected SM Accounting', $format_bold);
    $worksheet1->write(0, 38, 'Tanggal', $format_bold);
    $worksheet1->write(0, 39, 'User', $format_bold);

    $worksheet1->write(0, 40, 'Status Approved SM Accounting', $format_bold);
    $worksheet1->write(0, 41, 'Tanggal', $format_bold);
    $worksheet1->write(0, 42, 'User', $format_bold);

    $worksheet1->write(0, 43, 'Status Ekspedisi Bendahara', $format_bold);
    $worksheet1->write(0, 44, 'Tanggal', $format_bold);
    $worksheet1->write(0, 45, 'User', $format_bold);

    for ($i = 1; $i <= $total; $i++) {
        $worksheet1->write($i, 0, $i);
        $worksheet1->write_string($i, 1, $no_ba_v[$i - 1]);
        $worksheet1->write_string($i, 2, $no_invoice_v[$i - 1]);
        $worksheet1->write_string($i, 3, $no_ppl_v[$i - 1]);
        $worksheet1->write_string($i, 4, $ket_ekspedisi_v[$i - 1]);
        $worksheet1->write($i, 5, $total_klaim_v[$i - 1]);
        $worksheet1->write_string($i, 6, $warna_plat_v[$i - 1]);
        $sqlchek = "select KOMENTAR_REJECT, STATUS_BA_INVOICE,  NAMA, NAMA_LENGKAP, EX_BA_INVOICE.CREATED_AT from EX_BA_INVOICE 
            JOIN TB_USER_BOOKING TUB ON TUB.ID = EX_BA_INVOICE.CREATED_BY
            where EX_BA_INVOICE.ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE =  '".$no_invoice_v[$i - 1]."')";
        // echo $sqlchek;
        $querycek = oci_parse($conn, $sqlchek);
        oci_execute($querycek);
        while ($datafunc = oci_fetch_assoc($querycek)) {
          
            if ($datafunc['STATUS_BA_INVOICE'] == '10') {
                $worksheet1->write_string($i, 7, "CREATE INVOICE");
                $worksheet1->write_string($i, 8, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 9, $datafunc['NAMA_LENGKAP']);
                
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '20') {
                $worksheet1->write_string($i, 10, "UPLOAD INVOICE");
                $worksheet1->write_string($i, 11, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 12, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '30') {
                $worksheet1->write_string($i, 13, "REVERSED");
                $worksheet1->write_string($i, 14, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 15, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '40') {
                $worksheet1->write_string($i, 16, "REJECTED");
                $worksheet1->write_string($i, 17, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 18, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '45') {
                $worksheet1->write_string($i, 19, "CANCEL PPL & INVOICE");
                $worksheet1->write_string($i, 20, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 21, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '50') {
                $worksheet1->write_string($i, 22, 'APPROVED BY SPV');
                $worksheet1->write_string($i, 23, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 24, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '60') {
                $worksheet1->write_string($i, 25, 'GENERATE PPL');
                $worksheet1->write_string($i, 26, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 27, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '70') {
                $worksheet1->write_string($i, 28, 'SIMULATE & POSTING PPL');
                $worksheet1->write_string($i, 29, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 30, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '80') {
                $worksheet1->write_string($i, 31, 'REJECT BY MANAJER VERIFIKASI');
                $worksheet1->write_string($i, 32, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 33, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '90') {
                $worksheet1->write_string($i, 34, 'APPROVED  BY MANAJER VERIFIKASI');
                $worksheet1->write_string($i, 35, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 36, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '100') {
                $worksheet1->write_string($i, 37, 'REJECT BY SM VERIFIKASI');
                $worksheet1->write_string($i, 38, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 39, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '110') {
                $worksheet1->write_string($i, 40, 'APPROVED  BY SM VERIFIKASI');
                $worksheet1->write_string($i, 41, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 42, $datafunc['NAMA_LENGKAP']);
            }elseif ($datafunc['STATUS_BA_INVOICE'] == '120') {
                $worksheet1->write_string($i, 43, 'EKSPEDISI BENDAHARA');
                $worksheet1->write_string($i, 44, $datafunc['CREATED_AT']);
                $worksheet1->write_string($i, 45, $datafunc['NAMA_LENGKAP']);
            }

        }
        // $worksheet1->write_string($i, 7, $status_name_v[$i - 1]);
        // $worksheet1->write_string($i, 8, $nama_v[$i - 1]);
    }

    $workbook->close();
}
?>