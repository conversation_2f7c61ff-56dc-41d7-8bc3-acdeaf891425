<?php
    class createSO_tlcc {

        private $fungsi;
        private $conn;
        private $logfile;
        public $msg;
        public $dataFuncRFC;
        private $status;

        function __construct() {
            $this->status = 'SUKSES';
            $this->fungsi = new or_fungsi();
            $this->conn = $this->fungsi->or_koneksi();
            // $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
        }

        function saveLog() {
            $this->msg = substr($this->msg, 0, 900);
            $sqllog = "INSERT INTO RFC_LOG VALUES ('CREATESO6000',SYSDATE,'" . $this->msg . "')";
            $querylog = oci_parse($this->conn, $sqllog);
            if ($querylog) {
                $execlog = oci_execute($querylog);
            }
            //set running down
            $sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
            $query_run = oci_parse($this->conn, $sqlset_run);
            oci_execute($query_run);
            // //end set
        }

        function run($group_plant='') {
            // fwrite($this->logfile, "Start ".get_class($this)."pada tanggal jam ".date('d-m-Y H:i:s')."\n");
            $this->msg = "Start " . get_class($this) . " pada tanggal jam " . date('d-m-Y H:i:s') . "\n";
            $messageResp = "";
            $sql = "SELECT OR_TRANS_HDR_V.*, to_char(TGL_APPROVE,'DD-MM-YYYY') as TGL_APPROVE1, 
                        to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1, 
                        to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1, 
                        to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, 
                        to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1,
                        to_char(CREATE_DATE,'DD-MM-YYYY') as TGL_CREATE
                        FROM OR_TRANS_HDR_V 
                        WHERE DELETE_MARK = '0' 
                        AND STATUS_LINE IN ('OPEN','PROCESS') 
                        and ORG = '6000'
                        AND no_shp_old is null
                        -- and id = 2341491
                        AND TRUNC(CREATE_DATE) = TRUNC(SYSDATE)
                        ORDER BY TGL_KIRIM_PP ASC
                    ";
            $query = oci_parse($this->conn, $sql);
            oci_execute($query, 0);
            // echo $sql;
            $dataResult = array();
            $ii = 0;
            while ($datafunc = oci_fetch_assoc($query)) {
                $_data[$ii]['tgl_create'] = $datafunc['TGL_CREATE'];
                $_data[$ii]['tgl_pp'] = $datafunc['TGL_PP1'];
                $_data[$ii]['org'] = $datafunc['ORG'];
                $_data[$ii]['tglnya'] = $datafunc['TGL_PP1'];
                $_data[$ii]['idh'] = $datafunc['ID'];
                $_data[$ii]['distr_id'] = $datafunc['SOLD_TO'];
                $_data[$ii]['no_pp'] = $datafunc['NO_PP'];
                $_data[$ii]['so_type'] = $datafunc['SO_TYPE'];
                $_data[$ii]['nama_so_type'] = $datafunc['NAMA_SO_TYPE'];
                // $_data[$ii]['lcnum'] = $datafunc[''];
                $_data[$ii]['plant'] = $datafunc['BPLANT'];
                $_data[$ii]['nama_plant'] = $datafunc['NAMA_PLANT'];
                $_data[$ii]['incoterm'] = $datafunc['INCOTERM'];
                $_data[$ii]['nama_incoterm'] = $datafunc['NAMA_INCOTERM'];
                $_data[$ii]['route'] = $datafunc['ROUTE'];
                $_data[$ii]['sold_to'] = $datafunc['SOLD_TO'];
                $_data[$ii]['nama_sold_to'] = $datafunc['NAMA_SOLD_TO'];
                // $_data[$ii]['creditlimitv'] = $datafunc[''];
                // $_data[$ii]['unusedcreditsv'] = $datafunc[''];
                // $_data[$ii]['remaindercreditlimitv'] = $datafunc[''];
                $_data[$ii]['bayar'] = $datafunc['CARA_BAYAR'];
                $_data[$ii]['top'] = $datafunc['TOP'];
                $_data[$ii]['nama_top'] = $datafunc['NAMA_TOP'];
                $_data[$ii]['pricelist'] = $datafunc['PRICELIST'];
                $_data[$ii]['nama_pricelist'] = $datafunc['NAMA_PRICELIST'];
                $_data[$ii]['status'] = $datafunc['STATUS'];
                // $_data[$ii]['keterangan'] = $datafunc[''];
                $_data[$ii]['idke0'] = $datafunc['ID_DTL'];
                $_data[$ii]['com_iddtl0'] = $datafunc['ID_DTL'];
                $_data[$ii]['com_line0'] = 1;
                $_data[$ii]['com_produk0'] = $datafunc['KODE_PRODUK'];
                $_data[$ii]['com_distrik0'] = $datafunc['KODE_TUJUAN'];
                $_data[$ii]['com_shipto0'] = $datafunc['SHIP_TO'];
                $_data[$ii]['com_viewkodebag0'] = $datafunc['KODE_BAG'];
                // $_data[$ii]['com_viewnmbag0'] = $datafunc[''];
                $_data[$ii]['com_nopolisi0'] = $datafunc['NO_POLISI'];
                $_data[$ii]['com_typetruck0'] = $datafunc['TYPE_TRUCK'];
                $_data[$ii]['com_viewdrivern0'] = $datafunc['DRIVERN'];
                $_data[$ii]['com_viewsim0'] = $datafunc['SIMDRIVER'];
                // $_data[$ii]['com_catatan0'] = $datafunc[''];
                $_data[$ii]['com_qtyx0'] = $datafunc['QTY_PP'];
                $_data[$ii]['com_qty0'] = $datafunc['QTY_PP'];
                $_data[$ii]['com_tgl_kirim0'] = $datafunc['TGL_KIRIM_PP1'];
                $_data[$ii]['com_tgl_terima0'] = $datafunc['TGL_KIRIM_PP1'];
                $_data[$ii]['com_kontrak0'] = $datafunc['NO_KONTRAK'];
                // $_data[$ii]['com_posnr0'] = $datafunc[''];
                // $_data[$ii]['com_sisa0'] = $datafunc[''];
                $_data[$ii]['jumlah'] = 1;
                $_data[$ii]['lasthalaman'] = 'sobaru';
                $ii++;
            }
            if ($ii > 0) {
                for ($i = 0; $i < $ii; $i++) {
                    $dayCreated = date('D', strtotime($_data[$i]['tgl_create']));
                    if ($dayCreated == "Sat" || $dayCreated == "Sun") {
                        continue;
                    }else {
                        $cekSisaLimit = $this->cekSisaLimit($_data[$i]['org'], $_data[$i]['sold_to']);
                        $cekOverDue = $this->cekOverDue($_data[$i]['org'], $_data[$i]['sold_to'], $_data[$i]['com_tgl_kirim0']);
                        // $cekSisaLimit = -1;
                        // $cekOverDue = true;
                        if($cekSisaLimit > 0 && !$cekOverDue){
                            $resultApproval = $this->approval($_data[$i]);
                            $messageResp = $resultApproval."<br><hr><br>";
                            echo $messageResp;
                        }else{
                            $messageResp = "Failed Create SO with Order Resevation ".$_data[$i]['no_pp']."<br>Check Credit Limit and Over Due <br><hr>";
                            echo $messageResp;
                        }
                    }
                }
            } else {
                $messageResp = 'Tidak ada data';
                echo $messageResp; 
            }
            $this->msg .= $messageResp;
            $this->saveLog();
        }

        function approval($_datas){
            $pesan_telegram = "";
            //data header so
            $user_name_in = 'Scheduler';
            $idh = $_datas['idh'];
            $iddtl = $_datas['iddtl'];
            $so_type = $_datas['so_type'];
            $nama_so_type = $_datas['nama_so_type'];
            $sales_org = $_datas['org']; //$_datas['org'];
            $channelnew = $_datas['channels'];
            if ($so_type == 'ZEX') {
                $distr_chan = "30";
            } elseif ($so_type == 'ZPR') {
                if ($sales_org == '2000' || $sales_org == '7000' || $sales_org == '5000')
                    $distr_chan = "10";
                else if ($sales_org == '6000')
                    $distr_chan = "10";
                else
                    $distr_chan = "50";
            } else {
                $distr_chan = "10";
            }
            $division = "00";
            $dlv_block = "";
            $soldto = $_datas['sold_to'];
            $soldto = $this->fungsi->sapcode($soldto);
            $distr_chancondi = trim($this->fungsi->findOneByOne($this->conn, "TB_USER_BOOKING", "DISTRIBUTOR_ID", $soldto, "CHANNEL"));
            if ($distr_chancondi != '') {
                $distr_chan = $distr_chancondi;
            }
            if ($channelnew != '') {
                $distr_chan = $channelnew;
            }
            $oldso = $_datas['oldso'];
            $tgl_pp = $_datas['tgl_pp'];
            list($day, $month, $year) = split("-", $tgl_pp);
            $tgl_pp = $year . $month . $day;
            //$tanggal=gmdate("Ymd",time()+60*60*7);
            $top = $_datas['top'];
            $nama_top = $_datas['nama_top'];
            $reason = $_datas['reason'];
            $nama_reason = $_datas['nama_reason'];
            $oldso = $_datas['oldso'];

            $price_datep = $_datas['price_date'];
            list($dayp, $monthp, $yearp) = split("-", $price_datep);
            $price_date = $yearp . $monthp . $dayp;

            $plant = $_datas['plant'];
            $nama_plant = trim($_datas['nama_plant']);
            $incoterm1 = $_datas['incoterm'];
            $incoterm2 = $_datas['nama_incoterm'];
            $route = $_datas['route'];
            $kd_kapal = $_datas['kd_kapal'];
            $nm_kapal = $_datas['nm_kapal'];
            $nama_kapal = $kd_kapal . '-' . $nm_kapal;
            $pricelist = $_datas['pricelist'];
            $nama_pricelist = $_datas['nama_pricelist'];
            $ship_cond = $this->fungsi->findOneByOne($this->conn, "TB_ROUTE", "ROUTE", $route, "SHIP_COND");
            $no_pp = $_datas['no_pp'];
            $no_pp2 = $this->fungsi->or_new_pp_number($this->conn);
            $lcnum = $_datas['lcnum'];
            $lcnum = $this->fungsi->sapcode($lcnum);
            $sampai1 = $_datas['jumlah'];
            $ket = "";
            $tipeCreatePP_isi = trim($_datas['tipeCreatePP']);
            $material = $_datas['produk1'];
            for ($j = 0; $j <= $sampai1; $j++) {
                $idke = "idke" . $j;
                $pesan_telegram .= "PP $no_pp";

                if (isset($_datas[$idke]) and $_datas[$idke] != "") {
                    $kontrakh = $_datas['com_kontrak' . $j];
                }
            }

            $soldto_trim = ltrim($soldto, '0');
            $sql_select =  "SELECT
                                COUNT(ID_MAPPING_TLCC) AS JUMLAH
                            FROM
                                MAPPING_CREATE_SO_TLCC
                            WHERE
                                ORG = '".$sales_org."'
                                AND PLANT = '".$plant."'
                                AND SOLDTO = '".$soldto_trim."'
                                AND STATUS = 1
                                AND MATERIAL LIKE '".$material."%'";
            $query = oci_parse($this->conn, $sql_select);
            oci_execute($query);

            while ($row = oci_fetch_array($query))
            {   
                $arData['JUMLAH']= $row['JUMLAH'];
            }

            $sql_select2 =  "SELECT
                                *
                            FROM
                                MAPPING_CREATE_SO_TLCC
                            WHERE
                                ORG = '".$sales_org."'
                                AND PLANT = '".$plant."'
                                AND SOLDTO = '".$soldto_trim."'
                                AND STATUS = 1
                                AND MATERIAL LIKE '".$material."%'";
            $query = oci_parse($this->conn, $sql_select2);
            oci_execute($query);

            while ($row = oci_fetch_array($query))
            {   
                $mapData= $row;
            }
            // echo '<pre>';
            // var_dump($_datas);
            $qty = $_datas['com_qty0'];
            // var_dump($arData['JUMLAH'].'--'.$qty.'--'.$mapData['QTY_MINIMUM']);
            // die;
            if($arData['JUMLAH'] > 0 && $qty >= $mapData['QTY_MINIMUM']) {
                $show_ket .= "<b>---- SO PROMOTIONAL ----</b><br>";
                $pricelist_bonus = 68;
                $so_type_bonus = "ZFC";
                $top_bonus = "0001";
                //call bapi sales order sap
                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK)
                    $sap->Open();
                if ($sap->GetStatus() != SAPRFC_OK) {
                    $sap->PrintStatus();
                    $this->msg .= $sap->PrintStatus();
                    $this->status = 'GAGAL';
                    $this->saveLog();
                    exit;
                }

                $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
                if ($fce == false) {
                    $sap->PrintStatus();
                    $this->msg .= $sap->PrintStatus();
                    $this->status = 'GAGAL';
                    $this->saveLog();
                    exit;
                }

                //header entri
                $fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type_bonus; //"ZOR";
                $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"3000";
                $fce->ORDER_HEADER_IN["DISTR_CHAN"] = $distr_chan; //"10";
                $fce->ORDER_HEADER_IN["DIVISION"] = $division; //"00";
                $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $dlv_block; //"Z1";
                $fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp2; //"Z1";
                $fce->ORDER_HEADER_IN["PURCH_DATE"] = $tgl_pp; //"Z1";
                $fce->ORDER_HEADER_IN["PMNTTRMS"] = $top_bonus; //"Z1";
                $fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1; //"Z1";
                $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm2;
                $fce->ORDER_HEADER_IN["SHIP_COND"] = $ship_cond;
                $fce->ORDER_HEADER_IN["NAME"] = $nm_kapal;
                $fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist_bonus;
                $fce->ORDER_HEADER_IN["ORD_REASON"] = $reason;
                //$fce->ORDER_HEADER_IN["COLLECT_NO"] = $oldso;
                $fce->ORDER_HEADER_IN["REF_1"] = $oldso;
                $fce->ORDER_HEADER_IN["PRICE_DATE"] = $price_date;

                if ($lcnum != '') {
                    $fce->ORDER_HEADER_IN["PMTGAR_PRO"] = 'Z00001';
                    $fce->ORDER_HEADER_IN["DOC_NUM_FI"] = $lcnum;
                    //$fce->ORDER_HEADER_IN["DEPARTM_NO"] = 9;
                    $fce->ORDER_HEADER_IN["REC_POINT"] = 2;
                }
                if ($kontrakh != '') {
                    $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
                    $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrakh;
                }
                
                //detail entri item'
                $sampai = $_datas['jumlah'];
                for ($j = 0; $j <= $sampai; $j++) {
                    $qty_count = "com_qty" . $j;
                    if($_datas[$qty_count] >= $mapData['QTY_MINIMUM']){
                        $item_num = $j;
                        $shipto = "com_shipto" . $j;
                        $kode_distrik = "com_distrik" . $j;
                        $produk = "com_produk" . $j;
                        $qtyBonus = floor($_datas[$qty_count] / $mapData['QTY_MINIMUM']);
                        $fixQty = $qtyBonus * $mapData['QTY_BONUS'];
                        $tgl_kirim = "com_tgl_kirim" . $j;
                        $kontrak = "com_kontrak" . $j;
                        $posnr = "com_posnr" . $j;
                        $shp = "com_shipment" . $j;
                        //if(isset($_datas[$shipto])){
                        $tgl_kirim = $_datas['com_tgl_kirim' . $j];
                        list($day, $month, $year) = split("-", $tgl_kirim);
                        $tgl_kirim = $year . $month . $day;
                        $shipto = $_datas[$shipto];
                        $produk = $_datas[$produk];
                        $kode_distrik = $_datas[$kode_distrik];
                        // $qty = $_datas[$qty];
                        $kontrak = $_datas[$kontrak];
                        $posnr = $this->fungsi->linenum($_datas[$posnr]);
                        $shipment = $_datas[$shp];

                        //$show_ket .= "shipto $shipto / kode_distrik $kode_distrik / produk $produk / qty $qty / route $route / plant $plant<br>";

                        $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $produk;
                        $fce->ORDER_ITEMS_IN->row["REF_1"] = 'SO BONUS';
                        // $fce->ORDER_ITEMS_IN->row["REASON_REJ"] = 50; //17042025
                        // $fce->ORDER_ITEMS_INX->row["REASON_REJ"] = 'X'; //17042025

                        $fce->ORDER_ITEMS_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_ITEMS_INX->row["UPDATEFLAG"] = 'I'; //'000010';
                        $fce->ORDER_ITEMS_INX->row["MATERIAL"] = 'X';
                        $fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'X'; //$item_cat;  
                        $fce->ORDER_ITEMS_IN->row["ITEM_CATEG"] = "ZKNN"; //$item_cat;                  
                        if ($so_type_bonus == 'ZFC') {
                            if ($sales_org == '6000') {
                                $ITEM_CATEGvali = 'ZKNN';
                            } else if ($sales_org == '4000' || $sales_org == '3000') {
                                $ITEM_CATEGvali = 'ZKNN';
                            } else {
                                $ITEM_CATEGvali = 'ZKNN';
                            }
                        }
                        $fce->ORDER_ITEMS_IN->row["PLANT"] = $plant;
                        $fce->ORDER_ITEMS_IN->row["ROUTE"] = $route;
                        $fce->ORDER_ITEMS_IN->row["PRICE_LIST"] = $pricelist_bonus;
                        $fce->ORDER_ITEMS_IN->row["REF_1"] = 'SO PROMOT';

                        $fce->ORDER_ITEMS_INX->row["PLANT"] = 'X';
                        $fce->ORDER_ITEMS_INX->row["ROUTE"] = 'X';
                        $fce->ORDER_ITEMS_INX->row["PRICE_LIST"] = $pricelist_bonus;
                        $fce->ORDER_ITEMS_INX->row["REF_1"] = 'SO PROMOT';
                        $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $kode_distrik;
                        $fce->ORDER_ITEMS_INX->row["SALES_DIST"] = $kode_distrik;
                        // $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $kode_distrik;
                        if ($kontrak != '') {
                            $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                            $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = $posnr;
                            $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';

                            $fce->ORDER_ITEMS_INX->row["REF_DOC"] = 'X';
                            $fce->ORDER_ITEMS_INX->row["REF_DOC_IT"] = 'X';
                            $fce->ORDER_ITEMS_INX->row["REF_DOC_CA"] = 'X';
                            
                        }

                        // @liyantanto buat tlcc
                        // if ($lcnum!='') {
                        //     $fce->ORDER_ITEMS_IN->row["DEPREC_PER"] = $valperlcnum;
                        //     $fce->ORDER_ITEMS_INX->row["DEPREC_PER"] = 'X';
                        // } 
                        // if ($reason =='Z02') {
                        //     $show_ket .= " shp nya euii ". $shipment;
                        //     $fce->ORDER_ITEMS_IN->row["REF_1_S"] = $shipment;
                        //     $fce->ORDER_ITEMS_INX->row["REF_1_S"] ='X';
                        // }     

                        $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);
                        $fce->ORDER_ITEMS_INX->Append($fce->ORDER_ITEMS_INX->row);

                        //detail entri schedule n qty'
                        $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $fixQty;
                        $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_kirim;
                        $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);
                        // if ($reason =='Z02') {
                        $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
                        $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
                        $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
                        $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);
                        // }
                        //detail entri distributor dan agen
                        if ($j == 1)
                            $item_num = '000000';
                        else
                            $item_num = $item_num;

                        $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
                        $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
                        $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
                        //}   
                    }
                }

                $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
                $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'AG';
                $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $soldto;
                $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $nomorsopromot = $fce->SALESDOCUMENT;
                    $fce->RETURN->Reset();
                    while ($fce->RETURN->Next()) {
                        $tipe = $fce->RETURN->row["TYPE"];
                        $msg = $fce->RETURN->row["MESSAGE"];
                        if ($tipe != 'S') {
                            $show_ket .= $msg;
                            $show_ket .= '<br>';
                        }
                    }
                    //Commit Transaction
                    $fcecom = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                    $fcecom->Call();
                    $fcecom->Close();

                    //Update SO
                    if ($nomorsopromot != '' && $j > 1) {
                        sleep(5); //seconds to wait..   
                        $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                        $fce->SALESDOCUMENT = $nomorsopromot; //"ZOR";
                        $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';
                        $fce->Call();

                        //Commit Transaction
                        $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                        $fce->Call();
                    }

                    if ($sales_org == '6000') {
                        sleep(5); //seconds to wait..
                        //Teks
                        unset($tgl_kirimapp);
                        for ($j = 0; $j <= $sampai; $j++) {
                            $item_num1 = $j * 10;
                            $item_num = $this->fungsi->linenum($item_num1);
                            $com_nopolisiv = trim($_datas['com_nopolisi' . $j]);
                            $com_drivernamenopolv = trim($_datas['com_viewdrivern' . $j]);
                            $com_simdrivernamenopolv = trim($_datas['com_viewsim' . $j]);
                            $com_typetruckv = trim($_datas['com_typetruck' . $j]);
                            $com_catatanv = trim($_datas['com_catatan' . $j]);
                            $com_kodekantong = trim($_datas['com_viewkodebag' . $j]);
                            $tgl_kirimappT = trim($_datas['com_tgl_kirim' . $j]);
                            list($day, $month, $year) = split("-", $tgl_kirimappT);
                            $tgl_kirimapp .= $year . $month . $day . "!";

                            $test = sprintf("%06d", $item_num);

                            $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                            $fce->I_VBELN = $nomorsopromot; //'000010';
                            $fce->I_POSNR = sprintf("%06d", $item_num); //'000010';
                            // $fce->I_POSNR = 10; //'000010';
                            $fce->I_TEXT1 = $com_nopolisiv;
                            $fce->I_TEXT2 = $com_typetruckv;
                            $fce->I_TEXT3 = $com_catatanv;
                            $fce->I_TEXT4 = $com_kodekantong;
                            $fce->I_TEXT6 = $com_drivernamenopolv;
                            $fce->I_TEXT7 = $com_simdrivernamenopolv;
                            $fce->Call();
                            // echo "<pre>";
                            // print_r($fce);
                            // echo "</pre>";
                        }

                        // if ($sales_org == '6000' && $nomorsopromot != '') {
                        //     $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorsopromot&tgleq=$tgl_kirimapp";
                        // }
                    }
                }
                $fce->Close();
                $sap->Close();

                $show_ket .= 'Sales Order <b>Promotional</b> has been made with a number : ' . $nomorsopromot . '<br>';

                //update no shipment ke so
                if ($reason == 'Z02') {
                    $sap = new SAPConnection();
                    $sap->Connect("../include/sapclasses/logon_data.conf");
                    if ($sap->GetStatus() == SAPRFC_OK)
                        $sap->Open();
                    if ($sap->GetStatus() != SAPRFC_OK) {
                        $sap->PrintStatus();
                        exit;
                    }

                    $fce1 = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                    if ($fce1 == false) {
                        $sap->PrintStatus();
                        exit;
                    }
                    $fce1->SALESDOCUMENT = $nomorsopromot; //"ZOR";
                    $fce1->ORDER_HEADER_INX["UPDATEFLAG"] = 'U'; //"Z1";

                    $sampai = $_datas['jumlah'];
                    for ($j = 0; $j <= $sampai; $j++) {
                        $idke = "idke" . $j;
                        $item_num1 = $j;
                        $id_dtl = $_datas['com_iddtl' . $j];
                        $item_num = $this->fungsi->linenum($item_num1 * 10);
                        $nospj = $_datas['com_shipment' . $j];

                        //update detail item
                        $fce1->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                        $fce1->ORDER_ITEM_INX->row["ITM_NUMBER"] = $item_num;
                        $fce1->ORDER_ITEM_INX->row["REF_1_S"] = 'X';

                        $fce1->ORDER_ITEM_INX->Append($fce1->ORDER_ITEM_INX->row);

                        $fce1->ORDER_ITEM_IN->row["ITM_NUMBER"] = $item_num; //'000010';
                        $fce1->ORDER_ITEM_IN->row["REF_1_S"] = $nospj;
                        $fce1->ORDER_ITEM_IN->Append($fce1->ORDER_ITEM_IN->row);
                        //$show_ket.= $nospj.'-'.$nomorso;
                    }
                    $fce1->Call();

                    if ($fce1->GetStatus() == SAPRFC_OK) {
                        $fce1->RETURN->Reset();
                        while ($fce1->RETURN->Next()) {
                            $error = $fce1->RETURN->row["TYPE"];
                            $msg = $fce1->RETURN->row["MESSAGE"];
                            $show_ket .= $msg;
                            $show_ket .= '<br>';
                        }
                        //Commit Transaction
                        $fce1 = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                        $fce1->Call();
                        $fce1->Close();
                    }
                    $sap->Close();
                }

                if ($nomorsopromot != "") {
                    $status = "APPROVE";
                    $field_names = array('SOLD_TO', 'NAMA_SOLD_TO', 'NO_PP', 'TGL_PP', 'PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'STATUS', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'ROUTE', 'ORG', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'NO_SO_OLD', 'PRICE_DATE', 'KD_REASON', 'NM_REASON', 'TIPEPP');
                    $field_data = array("$soldto", "$nama_sold_to", "$no_pp2", "SYSDATE", "$plant", "$nama_plant", "$top_bonus", "Cash Payment", "$so_type_bonus", "Sales FOC", "$incoterm1", "$incoterm2", "$status", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "$route", "$sales_org", "$pricelist_bonus", "Bonus", "$lcnum", "$oldso", "instgl_$price_datep", "$reason", "$nama_reason", "$tipeCreatePP_isi");
                    $tablename = "OR_TRANS_HDR";
                    $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);

                    $show_ket .= "Order Reservation <b>Promotional</b> Success Made with No. $no_pp2 <br>";

                    $sampai = $_datas['jumlah'];
                    for ($k = 0; $k <= $sampai; $k++) {
                        $qty_count_in = "com_qty" . $k;
                        if($_datas[$qty_count_in] >= $mapData['QTY_MINIMUM']){
                            $shipto = "com_shipto" . $k;
                            $nama_shipto = "nama_shipto" . $k;
                            $alamat = "alamat" . $k;
                            $kode_distrik = "com_distrik" . $k;
                            $nama_distrik = "nama_distrik" . $k;
                            $kode_prov = "kode_prov" . $k;
                            $nama_prov = "nama_prov" . $k;
                            $produk = "com_produk" . $k;
                            $nama_produk = "nama_produk" . $k;
                            $qty = "com_qty" . $k;
                            $uom = "uom" . $k;
                            $tgl_kirim = "com_tgl_kirim" . $k;
                            $kontrak = "com_kontrak" . $k;
                            $shipment = "com_shipment" . $k;
                            $polisinumber = "com_nopolisi" . $k;
                            $drivernamenopolfg = "com_viewdrivern" . $k;
                            $simdrivernamenopolfg = "com_viewsim" . $k;
                            $typetruk = "com_typetruck" . $k;
                            $catatnke = "com_catatan" . $k;
                            $ktprodukke = "com_viewkodebag" . $k;
                            $qtyBonus = floor($_datas[$qty_count_in] / $mapData['QTY_MINIMUM']);
                            $fixQty = $qtyBonus * $mapData['QTY_BONUS'];
            
            
                            if (isset($_datas[$shipto])) {
                                $tgl_kirim_in = $_datas[$tgl_kirim];
                                if ($tgl_kirim_in == "")
                                    $tgl_kirim_in = date('d-m-Y');
                                $shipto_in = $_datas[$shipto];
                                $nama_shipto_in = $_datas[$nama_shipto];
                                $alamat_in = $_datas[$alamat];
                                $produk_in = $_datas[$produk];
                                $kode_distrik_in = $_datas[$kode_distrik];
                                $nama_distrik_in = $_datas[$nama_distrik];
                                $kode_prov_in = $_datas[$kode_prov];
                                $nama_prov_in = $_datas[$nama_prov];
                                $nama_produk_in = $_datas[$nama_produk];
                                // $qty_in = $_datas[$qty];
                                $uom = $_datas[$uom];
                                $kontrak = $_datas[$kontrak];
                                $shipment = $_datas[$shipment];
                                $nopolisi_in = strtoupper(trim($_datas[$polisinumber]));
                                $drivernamenopolfg_in = trim($_datas[$drivernamenopolfg]);
                                $simdrivernamenopolfg_in = trim($_datas[$simdrivernamenopolfg]);
                                $typetruk_in = trim($_datas[$typetruk]);
                                $catatnke_in = trim($_datas[$catatnke]);
                                $ktprodukke_in = trim($_datas[$ktprodukke]);
            
                                $field_names = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_PP', 'TGL_KIRIM_APPROVE', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'KD_PROV', 'NM_PROV', 'NO_KONTRAK', 'UOM', 'APPROVE_DATE', 'APPROVE_BY', 'PLANT', 'NM_PLANT', 'NO_SO_OLD', 'NO_SHP_OLD', 'PRICE_DATE', 'NO_POLISI', 'TYPE_TRUCK', 'CATATAN', 'KODE_BAG', 'DRIVERN', 'SIMDRIVER');
                                $field_data = array("$no_pp2", "$produk_in", "$nama_produk_in", "$fixQty", "$fixQty", "instgl_$tgl_kirim_in", "instgl_$tgl_kirim_in", "$shipto_in", "$nama_shipto_in", "$alamat_in", "SYSDATE", "$user_name", "0", "$kode_distrik_in", "$nama_distrik_in", "$status", "$k", "$nomorso", "$nama_kapal", "$kode_prov_in", "$nama_prov_in", "$kontrak", "$uom", "SYSDATE", "$user_name", "$plant", "$nama_plant", "$oldso", "$shipment", "instgl_$price_datep", "$nopolisi_in", "$typetruk_in", "$catatnke_in", "$ktprodukke_in", "$drivernamenopolfg_in", "$simdrivernamenopolfg_in");
                                $field_names1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_PP', 'TGL_KIRIM_APPROVE', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'KD_PROV', 'NM_PROV', 'NO_KONTRAK', 'UOM', 'APPROVE_DATE', 'APPROVE_BY', 'PLANT', 'NM_PLANT', 'NO_SO_OLD', 'NO_SHP_OLD', 'PRICE_DATE');
                                $field_data1 = array("$no_pp2", "$produk_in", "$nama_produk_in", "$fixQty", "$fixQty", "instgl_$tgl_kirim_in", "instgl_$tgl_kirim_in", "$shipto_in", "$nama_shipto_in", "$alamat_in", "SYSDATE", "$user_name", "0", "$kode_distrik_in", "$nama_distrik_in", "$status", "$k", "$nomorso", "$nama_kapal", "$kode_prov_in", "$nama_prov_in", "$kontrak", "$uom", "SYSDATE", "$user_name", "$plant", "$nama_plant", "$oldso", "$shipment", "instgl_$price_datep");
            
                                $tablename = "OR_TRANS_DTL";
                                $tablename1 = "OR_TRANS_APP";
                                $this->fungsi->insert($this->conn, $field_names, $field_data, $tablename);
                                $this->fungsi->insert($this->conn, $field_names1, $field_data1, $tablename1);
                                $show_ket .= "Item $produk_in with Qty $fixQty Success<br>";
                            }
                        }
                    }
                } else {
                    $status = "PROCESS";
                    $show_ket .= "Order Reservation Failed ! <br>";
                }
            }

            $show_ket .= "<b>---- SALES ORDER ----</b><br>";

            // jalankan bapi create so sap  
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                $sap->PrintStatus();
                exit;
            }

            unset($ex_so);
            $ex_so = true;
            //cegatan credit limit so khusus tlcc
        //                if($sales_org=='6000'){
        //                    require_once 'loadcreditlimit.php';
        //                }

            if ($ex_so == true) {
                $fce = $sap->NewFunction("BAPI_SALESORDER_CREATEFROMDAT2");
                if ($fce == false) {
                    $sap->PrintStatus();
                    exit;
                }
                //detail entri item'
                //$zak=$this->fungsi->qtyso($totalzak*1000);
                $sampai = $_datas['jumlah'];
                $ada = 0;
                for ($j = 0; $j <= $sampai - 1; $j++) { 
                    $idke = "idke" . $j;
                    if (isset($_datas[$idke]) and $_datas[$idke] != "") {
                        $id_dtl = $_datas['com_iddtl' . $j];
                        $item_num1 = $_datas['com_line' . $j];
                        $item_num = $this->fungsi->linenum($item_num1);
                        $material = $_datas['com_produk' . $j];
                        $shipto = $_datas['com_shipto' . $j];
                        $shipto = $this->fungsi->sapcode($shipto);
                        $distrik = $_datas['com_distrik' . $j];
                        $qty = $_datas['com_qty' . $j];
                        $qtyx = $_datas['com_qtyx' . $j];
                        $tgl_kirim = $_datas['com_tgl_kirim' . $j];
                        $kontrak = $_datas['com_kontrak' . $j];
                        $posnr = $this->fungsi->linenum($_datas['com_posnr' . $j]);

                        list($day, $month, $year) = split("-", $tgl_kirim);
                        $tgl_kirim = $year . $month . $day;

                        if ($qtyx != $qty) {
                            $ada = $ada + 1;
                        }

                        $fce->ORDER_ITEMS_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_ITEMS_IN->row["MATERIAL"] = $material;
                        // $fce->ORDER_ITEMS_IN->row["REASON_REJ"] = 50; //17042025
                        // $fce->ORDER_ITEMS_INX->row["REASON_REJ"] = 'X';

                        if ($so_type == 'ZFC') {
                            //$fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'ZKNN';//$item_cat;///salah revisi dibawah ini   
                            $fce->ORDER_ITEMS_INX->row["ITEM_CATEG"] = 'X'; //$item_cat;  
                            if ($sales_org == '6000') {
                                $ITEM_CATEGvali = 'ZKNN';
                            } else if ($sales_org == '4000' || $sales_org == '3000' || $sales_org == '7000' || $sales_org == '5000') {
                                $ITEM_CATEGvali = 'ZKLN';
                                //sales FOC distrik
                                if ($sales_org == '7000' || $sales_org == '5000') {
                                    $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                                    $fce->ORDER_ITEMS_IN->row["SALES_OFF"] = '10' . substr($distrik, 0, 2);
                                }
                            } else {
                                $ITEM_CATEGvali = 'ZKNN';
                            }
                            $fce->ORDER_ITEMS_IN->row["ITEM_CATEG"] = $ITEM_CATEGvali; //$item_cat;
                        }
                        $fce->ORDER_ITEMS_IN->row["PLANT"] = $plant;
                        $fce->ORDER_ITEMS_IN->row["ROUTE"] = $route;

                        if ($sales_org == '7900') {
                            $fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                            $fce->ORDER_ITEMS_IN->row["SALES_OFF"] = '10' . substr($distrik, 0, 2);
                        }

                        //$fce->ORDER_ITEMS_IN->row["SALES_DIST"] = $distrik;
                        if ($kontrak != '') {
                            $fce->ORDER_ITEMS_IN->row["REF_DOC"] = $kontrak;
                            $fce->ORDER_ITEMS_IN->row["REF_DOC_IT"] = $posnr;
                            $fce->ORDER_ITEMS_IN->row["REF_DOC_CA"] = 'G';
                        }
                        $fce->ORDER_ITEMS_IN->Append($fce->ORDER_ITEMS_IN->row);

                        //detail entri schedule n qty'
                        $fce->ORDER_SCHEDULES_IN->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_SCHEDULES_IN->row["REQ_QTY"] = $qty;
                        $fce->ORDER_SCHEDULES_IN->row["REQ_DATE"] = $tgl_kirim;
                        $fce->ORDER_SCHEDULES_IN->Append($fce->ORDER_SCHEDULES_IN->row);

                        $fce->ORDER_SCHEDULES_INX->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_SCHEDULES_INX->row["UPDATEFLAG"] = 'U';
                        $fce->ORDER_SCHEDULES_INX->row["REQ_QTY"] = 'X';
                        $fce->ORDER_SCHEDULES_INX->row["REQ_DATE"] = 'X';
                        $fce->ORDER_SCHEDULES_INX->Append($fce->ORDER_SCHEDULES_INX->row);

                        //detail entri distributor dan agen
                        if ($j == 0)
                            $item_num = '000000';
                        else
                            $item_num = $item_num;
                        $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = $item_num * 10; //'000010';
                        $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'WE';
                        $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $shipto;
                        $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);
                    }
                }

                if ($sales_org == '3000') {
                    $fce1 = $sap->NewFunction("Z_ZCSD_SHIPTO");
                    if ($fce1 == false) {
                        $sap->PrintStatus();
                        exit;
                    }

                    //header entri

                    $fce1->ZNMORG = 3000;
                    $fce1->ZKUNNR = $shipto;
                    $fce1->Call();
                    if ($fce1->GetStatus() == SAPRFC_OK) {
                        $fce1->RETURN_DATA->Reset();
                        while ($fce1->RETURN_DATA->Next()) {
                            $kode = $fce1->RETURN_DATA->row["VKGRP"];
                        }
                    }
                    $fce->ORDER_HEADER_IN["SALES_GRP"] = $kode;
                }
                if ($so_type == 'ZFC') {
                $fce->ORDER_HEADER_IN["DLV_BLOCK"] = "Z1";
                }
                
                $fce->ORDER_HEADER_IN["DOC_TYPE"] = $so_type; //"ZOR";
                $fce->ORDER_HEADER_IN["SALES_ORG"] = $sales_org; //"3000";
                $fce->ORDER_HEADER_IN["DISTR_CHAN"] = $distr_chan; //"10";
                $fce->ORDER_HEADER_IN["DIVISION"] = $division; //"00";
                $fce->ORDER_HEADER_IN["PURCH_NO_C"] = $no_pp; //"Z1";
                $fce->ORDER_HEADER_IN["PURCH_DATE"] = $tgl_pp; //"Z1";
                $fce->ORDER_HEADER_IN["PMNTTRMS"] = $top; //"Z1";
                $fce->ORDER_HEADER_IN["INCOTERMS1"] = $incoterm1; //"Z1";
                $fce->ORDER_HEADER_IN["INCOTERMS2"] = $incoterm2;
                $fce->ORDER_HEADER_IN["SHIP_COND"] = $ship_cond;
                $fce->ORDER_HEADER_IN["NAME"] = $nm_kapal;
                $fce->ORDER_HEADER_IN["PRICE_LIST"] = $pricelist;
                $fce->ORDER_HEADER_IN["ORD_REASON"] = $reason;
                if ($lcnum != '') {
                    $fce->ORDER_HEADER_IN["PMTGAR_PRO"] = 'Z00001';
                    $fce->ORDER_HEADER_IN["DOC_NUM_FI"] = $lcnum;
                }
                if ($kontrakh != '') {
                    $fce->ORDER_HEADER_IN["REFDOC_CAT"] = 'G';
                    $fce->ORDER_HEADER_IN["REF_DOC"] = $kontrakh;
                }

                $fce->ORDER_PARTNERS->row["ITM_NUMBER"] = '000000';
                $fce->ORDER_PARTNERS->row["PARTN_ROLE"] = 'AG';
                $fce->ORDER_PARTNERS->row["PARTN_NUMB"] = $soldto;
                $fce->ORDER_PARTNERS->Append($fce->ORDER_PARTNERS->row);

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $nomorso = $fce->SALESDOCUMENT;
                    $fce->RETURN->Reset();
                    while ($fce->RETURN->Next()) {
                        $tipe = $fce->RETURN->row["TYPE"];
                        $msg = $fce->RETURN->row["MESSAGE"];
                        if ($tipe != 'S') {
                            $show_ket .= $msg;
                            $show_ket .= '<br>';
                        
                        }
                    }


                    
                    //Commit Transaction
                    $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                    $fce->Call();

                    //Update SO
                    if ($nomorso != '' && $j > 0) {
                        sleep(5); //seconds to wait..    
                        $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                        $fce->SALESDOCUMENT = $nomorso; //"ZOR";
                        $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';
                        $fce->Call();

                        //Commit Transaction
                        $fce = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                        $fce->Call();
                    }

                    if ($sales_org == '6000') {
                        sleep(5); //seconds to wait..
                        //Teks
                        unset($tgl_kirimapp);
                        for ($j = 0; $j <= $sampai - 1; $j++) {
                            $idke = "idke" . $j;
                            if (isset($_datas[$idke]) and $_datas[$idke] != "") {
                                $item_num1 = $_datas['com_line' . $j];
                                $item_num = $this->fungsi->linenum($item_num1);
                                $com_nopolisiv = $_datas['com_nopolisi' . $j];
                                $com_typetruckv = $_datas['com_typetruck' . $j];
                                $com_catatanv = $_datas['com_catatan' . $j];
                                $com_kodekantong = $_datas['com_viewkodebag' . $j];
                                $tgl_kirimappT = $_datas['com_tgl_kirim' . $j];
                                $com_viewdrivern = $_datas['com_viewdrivern' . $j];
                                $com_viewsimv = $_datas['com_viewsim' . $j];
                                list($day, $month, $year) = split("-", $tgl_kirimappT);
                                $tgl_kirimapp .= $year . $month . $day . "!";

                                $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                $fce->I_VBELN = $nomorso; //'000010';
                                $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                                $fce->I_TEXT1 = $com_nopolisiv;
                                $fce->I_TEXT2 = $com_typetruckv;
                                $fce->I_TEXT3 = $com_catatanv;
                                $fce->I_TEXT4 = $com_kodekantong;
                                $fce->I_TEXT6 = $com_viewdrivern;
                                $fce->I_TEXT7 = $com_viewsimv;
                                $fce->Call();
        //                            echo "<pre>";
        //                            //print_r($fce);
        //                            echo "</pre>";
                            }
                        }

                        if ($sales_org == '6000' && $nomorso != '') {
                            $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorso&nosopromot=$nomorsopromot&tgleq=$tgl_kirimapp";
                        }
                        // if ($sales_org == '6000' && $nomorsopromot != '') {
                        //     $aksicetak = "../or_laporan/cetak_so.php?noso=$nomorsopromot&tgleq=$tgl_kirimapp";
                        // }
                    }

                    
                    $show_ket .= 'Sales Order has been made with a number : ' . $nomorso . '<br>';

                    //@liyantanto penambahan no ref pp
                    if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                        sleep(5); //s
                        for ($j = 0; $j <= $sampai - 1; $j++) {
                            $idke = "idke" . $j;
                            if (isset($_datas[$idke]) and $_datas[$idke] != "") {
                                $item_num1 = $_datas['com_line' . $j];
                                $item_num = $this->fungsi->linenum($item_num1);
                                $com_nopppref = trim($_datas['com_ppref' . $j]);

                                if ($com_nopppref != '' && $nomorso != '') {
                                    $pputam = substr($com_nopppref, 0, 10);
                                    $itempputam = @(substr($com_nopppref, 10, 6) / 10);
                                    $sql_ppref = "
                                        select ID,NO_SO,ITEM_NUMBER,NO_PPREF from OR_TRANS_DTL where DELETE_MARK=0 
                                        and NO_PP='$pputam' 
                                        and ITEM_NUMBER='$itempputam'
                                        ";
                                    $query_ppref = oci_parse($this->conn, $sql_ppref);
                                    oci_execute($query_ppref);
                                    while ($row_ref = oci_fetch_array($query_ppref)) {
                                        $idPPREF = $row_ref[ID];
                                        $no_soref = $row_ref[NO_SO];
                                        $item_soref = $row_ref[ITEM_NUMBER];
                                    }
                                    $com_noppputam = $no_pp . sprintf("%06d", $item_num * 10);
                                    $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                    $fce->I_VBELN = $no_soref; //'000010';
                                    $fce->I_POSNR = sprintf("%06d", $item_soref * 10); //'000010';
                                    $fce->I_TEXT5 = $nomorso . sprintf("%06d", $item_num * 10); //pp ref
                                    $fce->Call();

                                    //update so pp ref
                                    $field_namesppru = array('NO_PPREF');
                                    $field_datapput = array("$com_noppputam");
                                    $tablenamepput = "OR_TRANS_DTL";
                                    $field_idpput = array('ID');
                                    $value_idpput = array("$idPPREF");
                                    $this->fungsi->update($this->conn, $field_namesppru, $field_datapput, $tablenamepput, $field_idpput, $value_idpput);

                                    $nosoref = $no_soref . sprintf("%06d", $item_soref * 10);
                                    $fce = $sap->NewFunction("Z_ZAPPSD_UPD_TEXT_VBAP");
                                    $fce->I_VBELN = $nomorso; //'000010';
                                    $fce->I_POSNR = sprintf("%06d", $item_num * 10); //'000010';
                                    $fce->I_TEXT5 = $nosoref; //pp ref
                                    $fce->Call();
        //                                echo "<pre>";
        //                                //print_r($fce);
        //                                echo "</pre>";
                                }
                            }
                        }
                    }
                }
                $fce->Close();
                $sap->Close();

                if (($nomorso != "") and ( $ada == 0))
                    $status = "APPROVE";
                else
                    $status = "PROCESS";
                //update data header
                $field_names = array('PLANT_ASAL', 'NAMA_PLANT', 'TERM_PAYMENT', 'STATUS', 'NAMA_TOP', 'SO_TYPE', 'NAMA_SO_TYPE', 'INCOTERM', 'NAMA_INCOTERM', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ROUTE', 'PRICELIST', 'NAMA_PRICELIST', 'NO_KONTRAK_LC', 'KD_REASON', 'NM_REASON', 'ORG');
                $field_data = array("$plant", "$nama_plant", "$top", "$status", "$nama_top", "$so_type", "$nama_so_type", "$incoterm1", "$incoterm2", "SYSDATE", "$user_name_in", "$route", "$pricelist", "$nama_pricelist", "$lcnum", "$reason", "$nama_reason", "$sales_org");
                $tablename = "OR_TRANS_HDR";
                $field_id = array('ID');
                $value_id = array("$idh");
                $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                // update data detail
                $sampai1 = $_datas['jumlah'];
                for ($k = 0; $k <= $sampai1; $k++) {
                    $idke = "idke" . $k;
                    if (isset($_datas[$idke]) and $_datas[$idke] != "") {
                        $id_dtl1 = $_datas['com_iddtl' . $k];
                        $qty1 = $_datas['com_qty' . $k];
                        $qtyx1 = $_datas['com_qtyx' . $k];
                        $kontrak = $_datas['com_kontrak' . $k];
                        $produk = $_datas['com_produk'. $k];
                        $posnr = $_datas['com_posnr' . $k];
                        $kode_distrik = $_datas['com_distrik' . $k];
                        $tgl_kirim1 = $_datas['com_tgl_kirim' . $k];
                        $tgl_terima = $this->fungsi->tgl_terima($tgl_kirim1, $plant, $kode_distrik);
                        $item_numline1 = trim($_datas['com_line' . $k]);
                        $item_numline = $this->fungsi->linenum($item_numline1);
                        $com_kodekantong = $_datas['com_viewkodebag' . $k];

                        if (($nomorso != "") and ( $qty1 == $qtyx1))
                            $status1 = "APPROVE";
                        else
                            $status1 = "PROCESS";

                        $field_names = array('QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_TERIMA', 'NO_SO', 'STATUS_LINE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'APPROVE_DATE', 'APPROVE_BY', 'NO_KONTRAK', 'PLANT', 'NM_PLANT', 'KODE_BAG');
                        $field_data = array("$qty1", "updtgl_$tgl_kirim1", "updtgl_$tgl_terima", "$nomorso", "$status1", "SYSDATE", "$user_name_in", "SYSDATE", "$user_name_in", "$kontrak", "$plant", "$nama_plant", "$com_kodekantong");
                        $tablename = "OR_TRANS_DTL";
                        $field_id = array('ID');
                        $value_id = array("$id_dtl1");
                        $this->fungsi->update($this->conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                        if ($nomorso != "") {
                            $field_names1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', 'QTY_APPROVE', 'TGL_KIRIM_APPROVE', 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', 'STATUS_LINE', 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', 'APPROVE_DATE', 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                            $field_data1 = array('NO_PP', 'KODE_PRODUK', 'NAMA_PRODUK', 'QTY_PP', "$qty1", "instgl_$tgl_kirim1", 'TGL_KIRIM_PP', 'SHIP_TO', 'NAMA_SHIP_TO', 'ALAMAT_SHIP_TO', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KODE_TUJUAN', 'NAMA_TUJUAN', "'APPROVE'", 'ITEM_NUMBER', 'NO_SO', 'NAMA_KAPAL', "SYSDATE", 'APPROVE_BY', 'KD_PROV', 'NM_PROV', 'UOM', 'PLANT', 'NM_PLANT');
                            $tablenamefrom = "OR_TRANS_DTL";
                            $tablenameto = "OR_TRANS_APP";
                            $field_id1 = array('ID');
                            $value_id1 = array("$id_dtl1");
                            $this->fungsi->insertinto($this->conn, $field_names1, $field_data1, $tablenameto, $tablenamefrom, $field_id1, $value_id1);
                            $show_ket .= "Order Reservation Success Made with No. $no_pp <br>";
                            $show_ket .= "Item $produk with Qty $qty1 Success<br>";
                            //@liyantanto update qty approve
                            if ($sales_org == '7000' || $sales_org == '2000' || $sales_org == '5000') {
                                $sql_qtya = "
                                            update OR_TRANS_DTL set QTY_APPROVE=(
                                                    select sum(nvl(QTY_APPROVE,0)) as QTY from OR_TRANS_APP where NO_PP='$no_pp' and ITEM_NUMBER='$item_numline1'
                                                    and DELETE_MARK=0 and STATUS_LINE='APPROVE'
                                            ) where ID='$id_dtl1'
                                        ";
                                $query_qtya = oci_parse($this->conn, $sql_qtya);
                                oci_execute($query_qtya);
                            }
                        }
                    }
                }
                $pesan_telegram .= " Approve dengan no SO $nomorso (Approve By $user_name_in)";

                if ($sales_org == 4000) {
                    botTelegram('-394888712', $pesan_telegram);
                }
            }
            return $show_ket;
        }

        function cekSisaLimit($org, $distr) {
            $sisa = 0;
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                $sap->PrintStatus();
                exit;
            }
    
            $fce = $sap->NewFunction("Z_CREDIT_EXPOSURE");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }
    
            $fce->X_KKBER = $org;
            $fce->X_KUNNR = $distr;//$distributor;
            $fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK ) {		
                            $credit=$fce->Z_CREDITLIMIT*100; //credit limit
                            $delivery=$fce->Z_OPEN_DELIVERY*100; // do yg blom di billing
                            $minsum=$fce->Z_SUM_FLAG;
                            $usecredit0=$fce->Z_SUM_OPENS*100;
                            $usecredit=$minsum.$usecredit0;
                            $minsp=$fce->Z_OPEN_SP_FLAG;
                            $special0=$fce->Z_OPEN_SPECIALS*100;
                            $special=$minsp.$special0;
                            $sisa=$credit-$usecredit; //sisa kredit
                            //if ($usecredit<0) { $sisa=0; } else { $sisa=$sisa; }
    
            }else
                    $fce->PrintStatus();
    
            $fce->Close();	
            $sap->Close();	
    
            return $sisa;
            
        }
        
        function cekOverDue($org, $distr, $tgl_kirim) {
            $sisa = 0;
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                $sap->PrintStatus();
                exit;
            }
    
            $fce = $sap->NewFunction("Z_ZAPPSD_AR_AGING_DET_ST");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }

            $atgl_kirim = split("-",$tgl_kirim);
            $tgl_kirim_f = $atgl_kirim[2].$atgl_kirim[1].$atgl_kirim[0];
            
            $fce->I_DATE = $tgl_kirim_f;
            $fce->I_KUNNR = $distr;
            $fce->I_BUKRS = $org;
            $fce->Call();
            $ada_due = false;
            if ($fce->GetStatus() == SAPRFC_OK ) {
                $fce->T_DETAIL->Reset();
                while ( $fce->T_DETAIL->Next() ){
                    $tgl_due_date = $fce->T_DETAIL->row["ZDUE_DATE"];
                    $thn=substr($tgl_due_date,0,4);
                    $bln=substr($tgl_due_date,4,2);
                    $hr=substr($tgl_due_date,6,2);
                    $tgl_due_date=strtotime($hr.'-'.$bln.'-'.$thn);
                    $tgl_due_date2=$hr.'-'.$bln.'-'.$thn;
        
                    $due = $fce->T_DETAIL->row["ZDATE_DIFF"];
                    $tgl_k = strtotime($tgl_kirim);
                    if (!$ada_due){
                        if ($tgl_k>=$tgl_due_date){
                            $ada_due =  true;
                        } else {
                            $ada_due =  false;
                        }
                    } else continue;
                }
            } else 
                $fce->PrintStatus();
    
            $fce->Close();	
            $sap->Close();	
    
            return $ada_due;
            
        }
    }


