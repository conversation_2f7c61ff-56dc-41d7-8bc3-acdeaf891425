<?
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
include ('../include/email.php');
require_once ('../security_helper.php');
sanitize_global_input();
$email = new Email();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=3227;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$page="upload_invoice_ba.php";

$no_ba = $_GET["no_ba"];

if(isset($_POST["create"])) {
	$sql_invoice_ba = "
		SELECT
			EX_BA_INVOICE.*,
			to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1
		FROM EX_BA_INVOICE
		WHERE NO_BA = '$no_ba' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = oci_fetch_array($query_invoice_ba);

	if(isset($data_invoice_ba["ID"])) {
		$isi_file = json_decode($data_invoice_ba["LAMPIRAN"]);
		$files = isset($_POST["file"]) && is_array($_POST["file"]) ? $_POST["file"] : array(); 
		
		$no = 0;
		$pushNo = 0;
		foreach($files as $i => $file) {
			$nama = $file['nama'];
			if($nama!=''){
				if(intval($file['cek'])<4){
					$pushNo +=1;
				}  
			}
		}
		if($pushNo!=3){ 
			 echo "<script type='text/javascript'>alert('Lengkapi 4 Dokumen terlebih Dahulu');</script>";
			 header("Refresh:0");
			 exit();
		}
		
		// echo $pushNo;exit();
		
		
		foreach($files as $i => $file) {
			$lampiran = $file['lampiran'];

			if ($lampiran == 'Invoice') {
				continue;
			}

			$nama = $file['nama'];
			$data = $file['file'];
			$user = $file['user'];
			$tgl_upload = $file['tanggal']; 
			$exp_nama = explode(".", $nama);
			$ext = end($exp_nama); 
			$md5_name = md5($i . date("YmdHis") . $nama) . "." . $ext;
			
			list($type, $data) = explode(';', $data);
			list(, $data)      = explode(',', $data);
			$data = base64_decode($data);  
			
			file_put_contents('./lampiran/' . $md5_name, $data);
			
			$isi_file[] = array($lampiran, $tgl_upload, $nama, $md5_name, $user);
		}  
		// $isi_file[] = array('Invoice', date('Y-m-d'), $data_invoice_ba['FILE_KUITANSI'], $data_invoice_ba['FILE_KUITANSI'], $_SESSION['user_name']);

		$json_isi_file = json_encode($isi_file);
		
		// $sql_ba_invoice = "
			// UPDATE
				// EX_BA_INVOICE
			// SET
				// STATUS_BA_INVOICE = 20,
				// LAMPIRAN = '$json_isi_file',
				// UPDATED_BY = $user_id,
				// UPDATED_AT = TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD')
			// WHERE
				// ID = ".$data_invoice_ba["ID"]."
		// ";
		// $query_ba_invoice = oci_parse($conn, $sql_ba_invoice);
		// oci_execute($query_ba_invoice);
		
		$action = "upload_invoice_ba";
		include ('formula_prod.php');
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<strong>Pesan!</strong>
		<br>
		<br>
		<div class="alert alert-warning" role="alert"><?=$show_ket?></div>
		<a href="<?=$habis?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
	</div>
</div>
<?
		exit;
	}
}


$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}



$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_ba = $_GET["no_ba"];

$sql_invoice_ba = "
	SELECT
		EX_BA_INVOICE.*,
		to_char(TGL_FAKTUR_PAJAK,'YYYY-MM-DD') as TGL_FAKTUR,
		EX_INVOICE.NO_KWITANSI,
		EX_INVOICE.NO_INVOICE_EX,
		EX_INVOICE.NO_REKENING,
		EX_INVOICE.BANK,
		EX_INVOICE.BVTYP,
		EX_INVOICE.BANK_CABANG,
		TB_USER_BOOKING.NAMA_LENGKAP
	FROM
		EX_BA_INVOICE
		LEFT JOIN EX_INVOICE ON EX_INVOICE.NO_INVOICE = EX_BA_INVOICE.NO_INVOICE
		INNER JOIN TB_USER_BOOKING ON TB_USER_BOOKING.ID = EX_BA_INVOICE.ID_USER_APPROVAL
	WHERE
		EX_BA_INVOICE.NO_BA = '$no_ba'
		AND EX_BA_INVOICE.DIPAKAI = 1
";
$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
oci_execute($query_invoice_ba);

$data_invoice_ba = array();
while($row = oci_fetch_array($query_invoice_ba)) {
	$data_invoice_ba = $row;
}

if(isset($_POST["reject"]) && $_POST["reject"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Apakah ada yakin ingin Cancel Invoice <?=$data_invoice_ba[NO_INVOICE]?> ?
				<input type="hidden" value="<?=$data_invoice_ba[NO_INVOICE]?>" name="invoice_name">
			</div>
			<button type="submit" name="rejectI" value="rejectI" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Iya&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}
if((isset($_POST["rejectI"]) && $_POST["rejectI"] == 'rejectI')) {
	$nomer_invoice = $_POST["invoice_name"]; 
	$sql_inv = "SELECT NO_BA, ID, WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$nomer_invoice' ";
	// echo $nomer_invoice;exit;
	$query_inv = oci_parse($conn, $sql_inv);
	oci_execute($query_inv);
	$row_inv = oci_fetch_assoc($query_inv);
	$no_ba = $row_inv[NO_BA];
	$id_trans = $row_inv[ID];
	$warna_plat = $row_inv[WARNA_PLAT];
	$status_ba = '45';
	$keter = 'Cancel Invoice Approval Ekspeditur ';
	
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	$action = "cancel_invoice";
	include ('formula.php'); 

	//sendEmail
	$query = "SELECT * FROM EX_BA WHERE NO_BA = '$no_ba'";
	$query = oci_parse($conn, $query);
	oci_execute($query);
	$data_ba = oci_fetch_array($query);
	$no_ba_v = $data_ba['NO_BA'];
	$org_v = $data_ba['ORG'];
	$no_vendor_v = $data_ba['NO_VENDOR'];
	$nama_vendor_v = $data_ba['NAMA_VENDOR'];
	$total_semen_v = $data_ba['KLAIM_SEMEN'];
	$total_ppdks_v = $data_ba['PPDKS'];
	$total_inv_v = $data_ba['TOTAL_INV'];

    $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$data_invoice_ba[CREATED_BY];
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    // $mailTo = ', <EMAIL>';
    $mailCc = '';
    $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
    <div align=\"center\">
    <thead>
    <tr class=\"quote\">
    <td ><strong>&nbsp;&nbsp;No.</strong></td>
    <td align=\"center\"><strong>ORG</strong></td>
    <td align=\"center\"><strong>BA REKAPITULASI</strong></td>
    <td align=\"center\"><strong>EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
    <td align=\"center\"><strong>PDPKS</strong></td>
    <td align=\"center\"><strong>TOTAL</strong></td>
    <td align=\"center\"><strong>STATUS</strong></td>
    </tr>
    </thead>
    <tbody>";

    $email_content_table .= " 
    <td align=\"center\">1</td>
    <td align=\"center\">".$org_v."</td>       
    <td align=\"center\">".$no_ba_v."</td>
    <td align=\"center\">".$no_vendor_v."</td>
    <td align=\"center\">".$nama_vendor_v."</td>
    <td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
    <td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
    <td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
    <td align=\"center\">Open</td>
    </tr>";
    
    $email->sendMail($mailTo, $mailCc, 'Notifikasi Reject Atas Dokumen Invoice ', $nomer_invoice, 'Mohon untuk ditindaklanjuti Dokumen tsb.', $email_content_table);
    //end sendEmail
	
}
// if ($data_invoice_ba['LAMPIRAN'] != null && $data_invoice_ba['METERAI_KUI_STATUS'] != 0) {
// 	header("Location: upload_invoice_ba_meterai_sign.php?no_ba=$no_ba");
// 	exit;
// }

$sql_ba = "SELECT EX_BA.*, to_char(TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1 FROM EX_BA WHERE NO_BA = '$no_ba'";
$query_ba = oci_parse($conn, $sql_ba);
oci_execute($query_ba);

$data_ba = array();
while($row = oci_fetch_array($query_ba)) {
	$data_ba = $row;
}

// $sql = "
// 	SELECT
// 		A.*,
// 		B.CREATE_BY,
// 		B.PIC_GUDANG
// 	FROM
// 		(
// 			SELECT
// 				EX_TRANS_HDR.*,
// 				to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
// 				to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
// 				to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,
// 				to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF
// 			FROM
// 				EX_TRANS_HDR
// 			WHERE
// 				DELETE_MARK = '0'
// 				AND NO_BA = '$no_ba'
// 			ORDER BY
// 				PLANT,
// 				SAL_DISTRIK,
// 				KODE_KECAMATAN,
// 				KODE_PRODUK,
// 				TANGGAL_KIRIM ASC
// 		) A
// 		LEFT JOIN (
// 			SELECT
// 				m1.NO_SPJ,
// 				m1.CREATE_BY,
// 				m1.PIC_GUDANG
// 			FROM
// 				EX_INPUTCLAIM_SEMEN m1
// 				LEFT JOIN EX_INPUTCLAIM_SEMEN m2 ON (m1.NO_SPJ = m2.NO_SPJ AND m1.id < m2.id)
// 			WHERE
// 				m2.id IS NULL
// 				and m1.DELETE_MARK = '0'
// 				AND m1.STATUS = 'ELOG'
// 		) B ON (A.NO_SHP_TRN = B.NO_SPJ)
// ";
$sql= "SELECT * FROM EX_BA A JOIN EX_TRANS_HDR B ON A.NO_BA = B.NO_BA WHERE B.NO_BA = '$no_ba' ORDER BY B.NO_SHP_TRN asc";
$query = oci_parse($conn, $sql);
oci_execute($query);

while($row = oci_fetch_array($query)) {
	// $no_ba=$row[NO_BA];
	$no_ba_v[]=$row[NO_BA];
	$no_invoice_v[]=$row[NO_INVOICE];
	$tgl_invoice_v[]=$row[TANGGAL_INVOICE];
	$no_invoice_ex_v=$row[NO_INV_VENDOR];
	$spj_v[]=$row[NO_SHP_TRN];
	$spjmd[]=$row[NO_SHP_TRN2];
	$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
	$tgl_datang_v[]=$row[TANGGAL_DATANG];
	$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
	$produk_v[]=$row[KODE_PRODUK];
	$nama_produk_v[]=$row[NAMA_PRODUK];
	$shp_trn_v[]=$row[NO_SHP_TRN];
	$plant_v[]=$row[PLANT]; 
	$nama_plant_v[]=$row[NAMA_PLANT]; 
	$warna_plat_v=$row[WARNA_PLAT];
	$tipe_trukd=$row[VEHICLE_TYPE];                
	$nama_vendor_v=$row[NAMA_VENDOR]; 
	$vendor_v=$row[VENDOR];
	$TANGGAL_KIRIMFgg=trim($row[TANGGAL_KIRIMF]);
	$nmmkoqq=$row[ORG];
	$lampiran[]=$row[EVIDENCE_POD1];
	$lampiran2[]=$row[EVIDENCE_POD2];
	$tahunKIRIMFgg=substr(trim($row[TANGGAL_KIRIMF]),0,4);
	$tgl_kirim_sort[]=$row[TANGGAL_KIRIM1];
	//$flag_pod[]=$row[FLAG_POD] == 'POD-EPOOL' || $row[FLAG_POD] == 'POD-ATOM' || $row[FLAG_POD] == 'POD-FIOS' ? "POD by System" : "POD Manual";
	$flag_pod[]=$row[FLAG_POD];
	$geofence_pod[]=$row[GEOFENCE_POD];
	$tarif_cost[]=doubleval($row[TARIF_COST]);
	
	//perubahan untuk costum kereta dijadikan plat hitam
	if($tipe_trukd=='205' && $vendor_v=='0000410095' ){
		$warna_plat_v='HITAM';
	}
	$sal_dis_v[]=$row[SAL_DISTRIK]; 
	$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
	$sold_to_v[]=$row[SOLD_TO];
	$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
	$ship_to_v[]=$row[SHIP_TO];
	$qty_v[]=$row[QTY_SHP];
	$um_rez=$row[UM_REZ];
	if($um_rez > 1) {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ]/1000;
	} else {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ];
	}
	$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
	$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
	$qty_pdpks_v[]=$row[PDPKS];
	$qty_tot_klaim_ktg_v[]=$row[TOTAL_KLAIM_KTG];
	$qty_tot_klaim_smn_v[]=$row[TOTAL_KLAIM_SEMEN];
	$id_v[]=$row[ID];  
	$no_pol_v[]=$row[NO_POL];  
	$shp_cost_v[]=$row[SHP_COST];  
	$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
	$no_pajak_ex=$row[NO_PAJAK_EX];  		
	$kel=$row[KELOMPOK_TRANSAKSI];  		
	$inco=$row[INCO];  		
	$nama_kapal=$row[NAMA_KAPAL];  		
	$kode_kecamatan[]=$row[KODE_KECAMATAN];
	$tipe_truk[]=$row[VEHICLE_TYPE];
	if($row[PIC_GUDANG] != '') {
		$createby[]=$row[PIC_GUDANG];
	} else {
		if($row[CREATE_BY] != '') {
			$createby[]=$row[CREATE_BY];
		} else{
			$createby[]=$vendor;
		}
	}
	
	//Penyederhanaan Lap.OA @t 6 Feb 2012
	$item_no = trim($row[KODE_PRODUK]);                
	#Klaim Kantong
	$arr_klaim_kantong[$item_no]+=($row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]);  
	#Klaim Semen
	$arr_klaim_semen[$item_no]+=($row[TOTAL_SEMEN_RUSAK]+$row[PDPKS]);                
	$arr_nama_material[$item_no] = $row[NAMA_PRODUK];
	
	#Qty.
	$arr_qty_klaim_kantong[$item_no]+=$row[QTY_KTG_RUSAK];
	$arr_qty_klaim_semen[$item_no]+=$row[QTY_SEMEN_RUSAK];                
}
$total=count($shp_trn_v);
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Upload Invoice</th>
</tr></table>
</div>

<div align="center">
<table width="95%" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi </span></th>
</tr>
</table>
</div> 
<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
			<tr class="quote">
				<td align="center"><strong>No.</strong></td>
				<td align="center"><strong>No BA</strong></td>
				<td align="center"><strong>Tgl BA</strong></td>
				<!-- <td align="center"><strong>Tgl SPJ</strong></td> -->
				<td align="center"><strong>Area LT</strong></td>
				<td align="center"><strong>No SPJ</strong></td>
				<td align="center"><strong>SPJ MD</strong></td>
				<td align="center"><strong>Nopol</strong></td>
				<td align="center"><strong>Produk</strong></td>
				<td align="center"><strong>Distributor</strong></td>
				<td align="center"><strong>K. KTG</strong></td>
				<td align="center"><strong>K. SMN</strong></td>
				<td align="center"><strong>PPDKS</strong></td>
				<td align="center"><strong>TOT K. KTG</strong></td>
				<td align="center"><strong>TOT K. SEMEN</strong></td>
				<td align="center"><strong>Qty</strong></td>
				<td align="center"><strong>Tarif</strong></td>
				<td align="center"><strong>Jumlah</strong></td>
				<td align="center"><strong>Tanggal Datang</strong></td>
				<td align="center"><strong>Tanggal Bongkar</strong></td>
				<td align="center"><strong>POD</strong></td>
				<td align="center"><strong>Geofence</strong></td>
				<td align="center"><strong>Lampiran 1</strong></td>
				<td align="center"><strong>Lampiran 2</strong></td>
			</tr>
		</thead>
		<tbody>
			<?
				$total_qty_kantong_rusak_v = 0;
				$total_qty_semen_rusak_v = 0;
				$total_pdpks_v = 0;
				$total_tot_klaim_ktg_v = 0;
				$total_tot_klaim_smn_v = 0;
				$total_qty_v = 0;
				$total_shp_cost_v = 0;
				for($i = 0; $i < $total; $i++) {
					$total_qty_kantong_rusak_v += $qty_kantong_rusak_v[$i];
					$total_qty_semen_rusak_v += $qty_semen_rusak_v[$i];
					$total_pdpks_v += $qty_pdpks_v[$i];
					$total_tot_klaim_ktg_v += $qty_tot_klaim_ktg_v[$i];
					$total_tot_klaim_smn_v += $qty_tot_klaim_smn_v[$i];
					$total_qty_v += $qty_v[$i];
					$total_tarif_cost += $tarif_cost[$i];
					$total_shp_cost_v += $shp_cost_v[$i];
			?>
			<tr class="row<? echo ($i % 2) == 0 ? 0 : 1; ?>">
				<td align="center"><?=($i + 1)?></td>
				<td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a></td>
				<td align="center"><?=$data_ba[TGL_INVOICE1]?></td>
				<!-- <td align="center"><?=$tgl_kirim_sort[$i]?></td> -->
				<td align="center"><?=$sal_dis_v[$i]?></td>
				<td align="center"><?=$spj_v[$i]?></td>
				<td align="center"><?=$spjmd[$i]?></td>
				<td align="center"><?=$no_pol_v[$i]?></td>
				<td align="center"><?=trim(strtoupper($nama_produk_v[$i]),"SEMEN")?></td>
				<td align="center"><?=substr($nama_sold_to_v[$i],0,20)?></td>
				<td align="center"><?=number_format($qty_kantong_rusak_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_semen_rusak_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_pdpks_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_tot_klaim_ktg_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_tot_klaim_smn_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($tarif_cost[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($shp_cost_v[$i], 2, ",", ".")?></td>
				<td align="center"><? echo $tgl_datang_v[$i]; ?></td>
				<td align="center"><? echo $tgl_bongkar_v[$i]; ?></td>
				<td align="center"><?=$flag_pod[$i]?></td> 
				<? if ($geofence_pod[$i] != ''){?>
				<td align="center"><a href="javascript:popUp('https://www.google.com/search?q=<?php echo $geofence_pod[$i] ?>')">GEOFENCE POD</a></td>
				<? } else { ?>
				<td align="center">GEOFENCE POD</td>
				<? } ?> 
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran[$i] ?>')">Lampiran SPJ</a></td>
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran2[$i] ?>')">Lampiran TTD SPJ</a></td>
			</tr>
			<? } ?>
		</tbody>
		<tfoot>
			<tr class="quote">
				<td align="right" colspan="9"><strong>TOTAL</strong></td>
				<td align="center"><strong><?=number_format($total_qty_kantong_rusak_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_qty_semen_rusak_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_pdpks_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_tot_klaim_ktg_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_tot_klaim_smn_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_qty_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_tarif_cost, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_shp_cost_v, 2, ",", ".")?></strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
			</tr>
		</tfoot>
	</table>
</div>
<br/>
<br/>
<form method="post" action="" onsubmit="return validateForm()" id="form_invoice">
	<div align="center">
		<table cellspacing="0" cellpadding="0" border="0" width="95%">
			<tbody>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Invoice</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_INVOICE]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Faktur Pajak</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_FAKTUR_PAJAK]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Kwitansi Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_KWITANSI]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Invoice Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_INVOICE_EX]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">Tanggal</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="date" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[TGL_FAKTUR]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">No Rekening</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="width: 40px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[BVTYP]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td style="width: 200px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[BANK]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td style="width: 100px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[NO_REKENING]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;" colspan="2">
						<input type="text" value="<?=$data_invoice_ba[BANK_CABANG]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">User Approval</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="2">
						<input type="text" value="<?=$data_invoice_ba[NAMA_LENGKAP]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td></td>
				</tr>
				<tr style="display: none;">
					<td style="padding: 4px; font-weight: bold;">Lampiran</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="file" style="width: 248px;" onchange="tambahFile(this);" accept="image/gif, image/jpeg, image/png, application/pdf">
					</td>
					<td style="padding: 4px;">
						<!-- <a href="javascript:popUp('print_invoice_ba.php?no_ba=<?=$no_ba?>')">&lt;&lt;&nbsp;&nbsp;Cetak&nbsp;Invoice&nbsp;&nbsp;&gt;&gt;</a> -->
					</td>
				</tr>
			</tbody>
		</table>

		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<table align="center" class="adminlist">
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
									<td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
									<td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
									<td align="center" style="width: 200px;"><strong>Submit By</strong></td>
									<!-- <td align="center" style="width: 60px;"><strong>Aksi</strong></td> -->
								</tr>
							</thead>
							<tbody>
								<?php

								$lampiran = json_decode($data_invoice_ba['LAMPIRAN']);
															
								$i=0;
								if(is_array($lampiran)){
								foreach($lampiran as $l) {
									$nama = $l[0];
									$date = $l[1];
									$original_filename = $l[2];
									$filename = $l[3];
									$submit_by = $l[4];
									?>
										<tr class='row-file'>	
											<td align='center' class='nomor-file'><?=$i + 1?></td>
											<td align='center'><?=$nama?></td>
											<td align='center'><?= date('Y') ?>-<?=$bulan[date('m')]?>-<?=date('d')?></td>
											<td>
												<?php if (strtolower($nama) == 'ba rekapitulasi'): ?>
													<a href="../ex_ba_sp/upload/<?=$filename?>" target="_blank">
														<?=$original_filename?>
													</a>
												<?php else: ?>
													<a href="lampiran/<?=$filename?>" target="_blank">
														<?=$original_filename?>
													</a>
												<?php endif ?>
											</td>
											<td align='center'><?=$submit_by?></td>
											<!-- <td align='center'><p id='preview<?=$i?>'>
											</p></td> -->
										</tr>
									<?php
									$i+=1;
								}
								}
							?>
							</tbody>
						</table>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center" style="display: none;">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<table align="center" class="adminlist">
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
									<td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
									<td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
									<td align="center" style="width: 200px;"><strong>Submit By</strong></td>
									<td align="center" style="width: 60px;"><strong>Aksi</strong></td>
								</tr>
							</thead>
							<tbody id="isiLampiran"><?php
								//$lampiran = $data_invoice_ba[LAMPIRAN] != "" ? json_decode($data_invoice_ba[LAMPIRAN]) : array();
								$lampiran = array("Faktur Pajak", "Ba Rekapitulasi", "Invoice", "Kontrak/PO");
								$bulan = array(null, "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");

								if ($data_invoice_ba['PAKAI_SP_DENDA'] == '1') {
									$lampiran[] = "SP Denda";
								}

								$i=0;
								foreach($lampiran as $l) {
									if ($l == "Invoice") {
										
										?>
											<tr class='row-file'>
												<td style='display:none;' ><input type='hidden'  name='file[<?=$i?>][cek]' value='<?=$i?>' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden'  id='file<?=$i?>' name='file[<?=$i?>][file]' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden' name='file[<?=$i?>][lampiran]' value='<?=htmlspecialchars($l)?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;'><input type='hidden' id='namafile<?=$i?>' name='file[<?=$i?>][nama]'  style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][tanggal]' value='<?=date('Y-m-d')?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][user]' value='<?=$_SESSION['user_name']?>' style='width: 100%;' maxlength='40'></td>
											
												<td align='center' class='nomor-file'><?=$i + 1?></td>
												<td align='center'><?= htmlspecialchars($l) ?></td>
												<td align='center'><?= date('Y') ?>-<?=$bulan[date('m')]?>-<?=date('d')?></td>
												<td>
													<a href="lampiran/<?=$data_invoice_ba['FILE_KUITANSI']?>" target="_blank">
														<?=$data_invoice_ba['FILE_KUITANSI']?>
													</a>
												</td>
												<td align='center'><?= $_SESSION['user_name'] ?></td>
												<td align='center'><p id='preview<?=$i?>'>
												</p></td>
											</tr>
										<?php

										$i += 1;
										continue;
									}

									if ($l == "SP Denda" && $data_invoice_ba['PAKAI_SP_DENDA'] == '1') {
										?>
											<tr class='row-file'>
												<td style='display:none;' ><input type='hidden'  name='file[<?=$i?>][cek]' value='<?=$i?>' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden'  id='file<?=$i?>' name='file[<?=$i?>][file]' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden' name='file[<?=$i?>][lampiran]' value='<?=htmlspecialchars($l)?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;'><input type='hidden' id='namafile<?=$i?>' name='file[<?=$i?>][nama]'  style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][tanggal]' value='<?=date('Y-m-d')?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][user]' value='<?=$_SESSION['user_name']?>' style='width: 100%;' maxlength='40'></td>
											
												<td align='center' class='nomor-file'><?=$i + 1?></td>
												<td align='center'><?= htmlspecialchars($l) ?></td>
												<td align='center'><?= date('Y') ?>-<?=$bulan[date('m')]?>-<?=date('d')?></td>
												<td>
													<a href="lampiran/<?=$data_invoice_ba['FILE_SP_DENDA']?>" target="_blank">
														<?=$data_invoice_ba['FILE_SP_DENDA']?>
													</a>
												</td>
												<td align='center'><?= $_SESSION['user_name'] ?></td>
												<td align='center'><p id='preview<?=$i?>'>
												</p></td>
											</tr>
										<?php

										$i += 1;
										continue;
									}

									if ($l == "Ba Rekapitulasi") {
										?>
											<tr class='row-file'>
												<td style='display:none;' ><input type='hidden'  name='file[<?=$i?>][cek]' value='<?=$i?>' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden'  id='file<?=$i?>' name='file[<?=$i?>][file]' style='width: 100%;' maxlength='40'></td>
												<td style='display:none;' ><input type='hidden' name='file[<?=$i?>][lampiran]' value='<?=htmlspecialchars($l)?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;'><input type='hidden' id='namafile<?=$i?>' name='file[<?=$i?>][nama]'  style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][tanggal]' value='<?=date('Y-m-d')?>' style='width: 100%;' maxlength='40'></td>
												<td  style='display:none;' ><input type='hidden' name='file[<?=$i?>][user]' value='<?=$_SESSION['user_name']?>' style='width: 100%;' maxlength='40'></td>
											
												<td align='center' class='nomor-file'><?=$i + 1?></td>
												<td align='center'><?= htmlspecialchars($l) ?></td>
												<td align='center'><?= date('Y') ?>-<?=$bulan[date('m')]?>-<?=date('d')?></td>
												<td>
													<a href="../ex_ba_sp/upload/<?=$data_ba['FILENAME']?>" target="_blank">
														<?=$data_ba['FILENAME']?>
													</a>
												</td>
												<td align='center'><?= $_SESSION['user_name'] ?></td>
												<td align='center'><p id='preview<?=$i?>'>
												</p></td>
											</tr>
										<?php

										$i += 1;
										continue;
									}
									
									// $tgl = explode("-", $l[1]);
									echo "
										<tr class='row-file'>
										
											<td style='display:none;' ><input type='hidden'  name='file[$i][cek]' value='$i' style='width: 100%;' maxlength='40'></td>
											<td style='display:none;' ><input type='hidden'  id='file$i' name='file[$i][file]' style='width: 100%;' maxlength='40'></td>
											<td style='display:none;' ><input type='hidden' name='file[$i][lampiran]' value='".htmlspecialchars($l)."' style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;'><input type='hidden' id='namafile$i' name='file[$i][nama]'  style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;' ><input type='hidden' name='file[$i][tanggal]' value='".date("Y")."-".date("m")."-".date("d")."' style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;' ><input type='hidden' name='file[$i][user]' value='".$_SESSION['user_name']."' style='width: 100%;' maxlength='40'></td>
										
											<td align='center' class='nomor-file'>".($i+1)."</td>
											<td align='center'>".htmlspecialchars($l)."</td>
											<td align='center'>".date("Y")."-".$bulan[date("m")]."-".date("d")."</td>
											<td align='center'><input type='file' nomor='$i' onchange='editFile(this);'  name='file[$i][files]' style='width: 248px;' accept='image/gif, image/jpeg, image/png, application/pdf'></a></td>
											<td align='center'>".$_SESSION['user_name']."</td>
											<td align='center'><p id='preview$i'></p></td>
										</tr>
									";
									$i+=1;
								}
							?>
							</tbody>
						</table>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>
		
		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 300px;">
						<!-- Old submit process -->
						<!-- <button type="submit" name="create" id = "tombol_create" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Submit</button> -->

						<?php if ($data_invoice_ba['ID_USER_APPROVAL'] == $user_id): ?>
							<!-- New submit process -->
							<button type="button" id="submit_esign_keyla" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">
								Submit E-Sign (Keyla)
							</button>

							<button type="button" id="submit_esign_otp" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #007bff; color: #fff; border: 1px solid #007bff; border-radius: 4px;">
								Submit E-Sign (OTP)
							</button>
						<?php endif ?>
							</form>

					</td>
					<?php if ($data_invoice_ba['ID_USER_APPROVAL'] == $user_id): ?>
					<td style="width: 50px;"><form id="data_claim" name="data_claim" method="post" action="" >
								<button type="submit" id="reject" name="reject" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: grey; color: #fff; border: 1px solid #007bff; border-radius: 4px;">
									Reject
								</button>
							</form>
						</td>
					<?php endif ?>
					<td><a href="list_ba_approve.php" style="font-size: 14px; text-decoration: none; margin-left: 4px; padding: 4px; background-color: #ff0000; color: #fff; border: 1px solid #000; border-radius: 4px;">Back</a></td>
				</tr>
			</tbody>
		</table>
	</div>



<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
<script src="../include/jquery.min.js"></script>
<script type="text/javascript">
	//We write the table and the div to hide the content out, so older browsers won't see it
	obj=document.getElementById("tunggu_ya");
	obj.style.display = "none";
	obj_tampil=document.getElementById("halaman_tampil");
	obj_tampil.style.display = "inline";
	
	var monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
	var hari_ini = new Date();
	var tgl = String(hari_ini.getDate()).padStart(2, '0');
	var bln = hari_ini.getMonth();
	var thn = hari_ini.getFullYear();
	
	var jumlahFile = 4;
	var indexFile = 4;

	$('#submit_esign_keyla').click(function() {
		$('#form_invoice').attr('action', 'upload_invoice_ba_meterai_sign.php?no_ba=<?=$no_ba?>&submit=save_uploaded_files&type=keyla'); //save_uploaded_files
		$('#form_invoice').submit();
		$('#submit_esign_keyla').html('Loading...');
	});

	$('#submit_esign_otp').click(function() {
		$('#form_invoice').attr('action', 'upload_invoice_ba_meterai_sign.php?no_ba=<?=$no_ba?>&submit=save_uploaded_files&type=otp'); //save_uploaded_files
		$('#form_invoice').submit();
		$('#submit_esign_otp').html('Loading...');

	});

	$("#tombol_create").click(function(){
	  //$("#tombol_create").attr('disabled','disabled');
	});
	
	function tambahFile(file) {
		if((file.value).length > 0) {
			var reader = new FileReader();
			var user = "<?php echo $_SESSION['user_name'] ?>";
			reader.readAsDataURL(file.files[0]);
			reader.onload = function () {
				$("#isiLampiran").append(
					"<tr class='row-file'>" +
						"<td align='center' class='nomor-file'></td>" +
						"<td align='center'>" +
							"<input type='hidden' name='file["+indexFile+"][cek]'  value=\""+indexFile+"\">" +
							"<input type='text' name='file["+indexFile+"][lampiran]' required='required' style='width: 100%;' maxlength='40'>" +
							"<input type='hidden' name='file["+indexFile+"][nama]' style='display: none;' value=\""+file.files[0].name+"\">" +
							"<input type='hidden' name='file["+indexFile+"][file]' style='display: none;' value=\""+reader.result+"\">" +
						"</td>" +
						"<td align='center'><input type='hidden' name='file["+indexFile+"][tanggal]' style='display: none;' value='"+thn+'-'+String(bln+1).padStart(2, '0')+'-'+tgl+"'>"+tgl+"-"+monthNames[bln]+"-"+thn+"</td>" +
						"<td align='center'><a href='javascript:void(0);' data-base64=\""+reader.result+"\" data-name=\""+file.files[0].name+"\" onclick='previewFile(this);'>"+file.files[0].name+"</a></td>" +
						"<td align='center'><input type='text' name='file["+indexFile+"][user]' value=\""+user+"\" style='width: 100%;' readonly='readonly'>" +
						"<td align='center' style='font-size: 18px;'><a href='javascript:void(0);' onclick='kurangiFile(this);' style='cursor: pointer; text-decoration: none;'>&times;</a></td>" +
					"</tr>"
				);
				file.value = "";
				jumlahFile++;
				indexFile++;
				refreshNomor();
			}
			reader.onerror = function (error) {
				alert('Error: ', error);
			}
		}
	}
	
	function editFile(file) { 
			var reader = new FileReader();
			var user = "<?php echo $_SESSION['user_name'] ?>";
			reader.readAsDataURL(file.files[0]);
			console.log(file);
			var cobas = $(file).attr("nomor"); 
			$("#namafile"+cobas).val(file.files[0].name) 
			reader.onload = function () {
						$("#file"+cobas).val(reader.result)
						$("#preview"+cobas).append("<a href='javascript:void(0);' style='cursor: pointer; text-decoration: none;' data-base64=\""+reader.result+"\" data-name=\""+file.files[0].name+"\" onclick='previewFile(this);'>View</a>")
					};
			
	}
	
	function kurangiFile(elm) {
		if(confirm("Hapus file?")) {
			elm.parentNode.closest('.row-file').remove();
			jumlahFile--;
			refreshNomor();
		}
	}
	
	function refreshNomor() {
		var elmNomor = document.getElementsByClassName('nomor-file');
		for (var i=0; i < elmNomor.length; i++) {
			elmNomor[i].innerHTML = i + 1;
		}
	}
	
	function previewFile(elm) {
		var w = window.open('about:blank');
 
		// FireFox seems to require a setTimeout for this to work.
		setTimeout(() => {
		  w.document.body.appendChild(w.document.createElement('iframe')).src = $(elm).data("base64");
		  w.document.body.style.margin = 0;
		  w.document.getElementsByTagName("iframe")[0].style.width = '100%';
		  w.document.getElementsByTagName("iframe")[0].style.height = '100%';
		  w.document.getElementsByTagName("iframe")[0].style.border = 0;
		}, 10);
	}
	
	function validateForm() {
		if (!$('#form_invoice').attr('action')) {
			return false;
		}

	  if (jumlahFile == 0) {
		alert("Upload invoice terlebih dahulu.");
		return false;
	  }

	  var formAction = $('#form_invoice').attr('action');

	  $('#submit_esign_keyla').prop('disabled', true);
	  $('#submit_esign_otp').prop('disabled', true);
	}
</script>

</body>
</html>
