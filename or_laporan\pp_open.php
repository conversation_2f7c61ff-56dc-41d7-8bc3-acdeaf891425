<? 
@session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=85;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];

$dirr = $_SERVER['PHP_SELF'];   
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}
$inorg= $user_org;
$currentPage="pp_open.php";
$komen=""; 
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("You are not have authorize to accessing this page... \n Login Please...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?php

exit();
}
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="pp_open.php";
$no_pp = trim($_POST['no_pp']);
$no_pp = $fungsi->sapcode($no_pp);
$branch_plant = $_POST['branch_plant'];
$sold_to = $_POST['sold_to'];
$ship_to = $_POST['ship_to'];
$produk = $_POST['produk'];
$jenis_kirim = $_POST['jenis_kirim'];
$status = $_POST['status'];
$tgl1 = $_POST['tgl1'];
$tgl2 = $_POST['tgl2'];

if(isset($_POST['cari'])){
	$plant = $_POST['plant'];
	if($no_pp=="" and $branch_plant=="" and $sold_to == "" and $ship_to == "" and $produk == "" and $jenis_kirim == "" and $status == "" and $tgl1 == "" and $tgl2 == ""){
//		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE DELETE_MARK = '0' AND ORG $user_orgx AND KD_PROV IN (SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0) ORDER BY NO_PP ASC";
            $sql= "SELECT L.PESAN_DETAIL, OR_TRANS_HDR_V.*, to_char(TGL_LEADTIME,'DD-MM-YYYY') as TGL_LEADTIME1,to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY')
            as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY')
            as TGL_TERIMA1, UPDATE_DTL.LAST_UPDATED_BY_DTL, UPDATE_DTL.LAST_UPDATE_DATE_DTL FROM OR_TRANS_HDR_V 
            LEFT JOIN ZMD_LOG_SBI L ON L.NO_PP = OR_TRANS_HDR_V.NO_PP
			LEFT JOIN (
				SELECT
					NO_PP,
					MAX(LAST_UPDATED_BY) AS LAST_UPDATED_BY_DTL,
					MAX(LAST_UPDATE_DATE) AS LAST_UPDATE_DATE_DTL
				FROM
					OR_TRANS_DTL
				GROUP BY
					NO_PP
				ORDER BY
					NO_PP
			) UPDATE_DTL ON
				OR_TRANS_HDR_V.NO_PP = UPDATE_DTL.NO_PP
            WHERE DELETE_MARK = '0' AND ORG IN ('$inorg')
            AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0)
 ORDER BY OR_TRANS_HDR_V.NO_PP ASC";
	}else {
		$pakeor=0;
//		$sql= "SELECT OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE ORG $user_orgx AND KD_PROV IN (SELECT KD_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0) AND";
                $sql= "SELECT L.PESAN_DETAIL, OR_TRANS_HDR_V.*, to_char(TGL_LEADTIME,'DD-MM-YYYY') as TGL_LEADTIME1,to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY')
                as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY')
                as TGL_TERIMA1, UPDATE_DTL.LAST_UPDATED_BY_DTL, UPDATE_DTL.LAST_UPDATE_DATE_DTL FROM OR_TRANS_HDR_V 
                LEFT JOIN ZMD_LOG_SBI L ON L.NO_PP = OR_TRANS_HDR_V.NO_PP
				LEFT JOIN (
				SELECT
					NO_PP,
					MAX(LAST_UPDATED_BY) AS LAST_UPDATED_BY_DTL,
					MAX(LAST_UPDATE_DATE) AS LAST_UPDATE_DATE_DTL
				FROM
					OR_TRANS_DTL
				GROUP BY
					NO_PP
				ORDER BY
					NO_PP
			) UPDATE_DTL ON
				OR_TRANS_HDR_V.NO_PP = UPDATE_DTL.NO_PP
                WHERE ORG IN ('$inorg') AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME='$user_name' AND zuvk.DEL=0 AND zkp.DEL=0) AND";
		if($no_pp!=""){
			if($pakeor==1){
			$sql.=" OR_TRANS_HDR_V.NO_PP LIKE '$no_pp' ";
			}else{
			$sql.=" OR_TRANS_HDR_V.NO_PP LIKE '$no_pp' ";
			$pakeor=1;
			}
		}
		if($branch_plant!=""){
			if($pakeor==1){
			$sql.=" AND BPLANT LIKE '$branch_plant' ";
			}else{
			$sql.=" BPLANT LIKE '$branch_plant' ";
			$pakeor=1;
			}
		}
		if($sold_to!=""){
			if($pakeor==1){
			$sql.=" AND SOLD_TO LIKE '$sold_to' ";
			}else{
			$sql.=" SOLD_TO LIKE '$sold_to' ";
			$pakeor=1;
			}
		}
		if($ship_to!=""){
			if($pakeor==1){
			$sql.=" AND SHIP_TO LIKE '$ship_to' ";
			}else{
			$sql.=" SHIP_TO LIKE '$ship_to' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KODE_PRODUK LIKE '$produk' ";
			}else{
			$sql.=" KODE_PRODUK LIKE '$produk' ";
			$pakeor=1;
			}
		}
		if($jenis_kirim!=""){
			if($pakeor==1){
			$sql.=" AND INCOTERM LIKE '$jenis_kirim' ";
			}else{
			$sql.=" INCOTERM LIKE '$jenis_kirim' ";
			$pakeor=1;
			}
		}
		if($status!=""){
			if($pakeor==1){
			$sql.=" AND STATUS_LINE LIKE '$status' ";
			}else{
			$sql.=" STATUS_LINE LIKE '$status' ";
			$pakeor=1;
			}
		}	
		if($tgl1!="" and $tgl2!=""){
			if ($tgl1=="")$tgl1_sql = "01-01-1990";
			else $tgl1_sql = $tgl1;
			if ($tgl2=="")$tgl2_sql = "12-12-9999";
			else $tgl2_sql = $tgl2;
			if($pakeor==1){
			$sql.=" AND TGL_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			}else{
			$sql.=" TGL_PP BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS')";
			$pakeor=1;
			}
		}			
		$sql.=" AND DELETE_MARK = '0' ORDER BY OR_TRANS_HDR_V.NO_PP ASC";
	}
	echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_pp_v[]=$row['NO_PP'];
		$tgl_kirim_v[]=$row['TGL_KIRIM_PP'];
		$tgl_pp_v[]=$row['TGL_PP'];
		$tgl_leadtime_v[]=$row['TGL_LEADTIME'];
		$kdproduk_v[]=$row['KODE_PRODUK'];
		$produk_v[]=$row['NAMA_PRODUK'];
		$sold_to_v[]=$row['SOLD_TO'];
		$namasold_to_v[]=$row['NAMA_SOLD_TO'];
		$ship_to_v[]=$row['SHIP_TO'];
		$namaship_to_v[]=$row['NAMA_SHIP_TO'];
		$alamat_v[]=$row['ALAMAT_SHIP_TO'];
		$kddistrik_v[]=$row['KODE_TUJUAN'];
		$nmdistrik_v[]=$row['NAMA_TUJUAN'];
		$qty_v[]=$row['QTY_PP'];
		$id_v[]=$row['ID'];  
		$status_v[]=$row['STATUS_LINE'];
		$nosonya[]=$row['NO_SO'];
		$create_v[]=$row['CREATED_BY'];
		$approve_v[]=$row['APPROVE_BYDTL'];
		$appdate_v[]=$row['APPROVE_DATEDTL'];  
		$last_update_v[]=$row['LAST_UPDATED_BY_DTL'];  
		$last_update_date_v[]=$row['LAST_UPDATE_DATE_DTL'];  
                $tipepp[]=$row['FLAG_LELANG']; 
                $pesan_detail[]=$row['PESAN_DETAIL']; 
				$royalty_v[]=$row[IS_ROYALTY];
				$msa_v[]=$row[IS_MSA];

	}
	$total=count($no_pp_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}

	///
	// log SOCC
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	$fce = $sap->NewFunction ("ZSD_LOG_AUTO_DEL_SOCC");
	if ($fce == false ) {
		$sap->PrintStatus();
		exit;
	 }

				$fce->I_VKORG = $user_org;

	 			// print_r($tgl2);
				// print_r($tgl1);
	 			if($date1!=""&&$date2!=""){
					$vdatuhigh = date('d.m.Y',strtotime($tgl2));
					$vdatulow = date('d.m.Y',strtotime($tgl1));
	
					$fce->R_VDATU->row["SIGN"] = 'I';
					$fce->R_VDATU->row["OPTION"] = 'BT';
					$fce->R_VDATU->row["LOW"] = $vdatulow;
					$fce->R_VDATU->row["HIGH"] = $vdatuhigh;
					$fce->R_VDATU->Append($fce->R_VDATU->row);
				}



			$fce->Call();

			
			if ($fce->GetStatus() == SAPRFC_OK ) {
				$fce->T_DATA->Reset();
				$s=0;
				while ($fce->T_DATA->Next()){
					$LOG_NO_SO[$s] = $fce->T_DATA->row["VBELN"];
					$LOG_TYPELOAD[$s]= $fce->T_DATA->row["INC"];
					$LOG_TYPESO[$s]= $fce->T_DATA->row["AUAR"];
					$LOG_TANGGALSO[$s]= $fce->T_DATA->row["VDATU"];
					$LOG_TANGGAL[$s]= $fce->T_DATA->row["LOG_DATE"];
					$s++;
				}
			}else {
				$fce->PrintStatus();
			}


			$fce->Close();
			$sap->Close();
	$totallog = count($LOG_NO_SO);

	///



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		} 
		  
function ketik_distr(obj) {
	var com_org = document.getElementById('org');		
	var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() { 
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {	
					document.getElementById("distrdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}


function ketik_produk(obj) {
	var strURL="ketik_produk.php?produk="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk() {	
		var strURL="cari_produk.php";
		popUp(strURL);
}
	
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>SGG Online Application: See the Purchase Request Data</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="../include/script.js"></script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Purchase Requisition Approval List</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Approval Request for Purchase Search</th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Request for Purchase</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" value="<?=$no_pp?>"/>
	  <input type="hidden" id="org" name="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Branch Plant </td>
      <td  class="puso">:</td>
      <td ><div id="plantdiv">
	    <input name="plant" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>&nbsp;&nbsp;&nbsp;&nbsp;
		<input name="nama_plant" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
      	<input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
		<input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
	  </div></td>
    </tr>
    <tr>
      <td  class="puso">Sold To </td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
	  <input name="sold_to" id="sold_to" type="text" class="inputlabel" size="10" maxlength="10" value="" onChange="ketik_distr(this)"/> &nbsp;&nbsp;&nbsp;&nbsp;
	  <input name="nama_sold_to" id="nama_sold_to" class="inputlabel" type="text" size="30" value="" readonly="true"/> &nbsp;&nbsp;&nbsp;&nbsp;	    
	  <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/></div></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><div id="shiptodiv">
      <input name="shipto" type="text" class="inputlabel" id="shipto" value="<?=$shipto?>" onChange="ketik_shipto(this)" maxlength="10" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_shipto" type="text" class="inputlabel" id="nama_shipto" value="<?=$nama_shipto?>" readonly="true"  size="30"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="btn_shipto" type="button" class="button" id="btn_shipto" value="..." onClick="findshipto()"/>
      <input name="val_error_shipto" type="hidden" id="val_error_shipto" value="0" />
    </div></td>
    </tr>
    <tr>
      <td  class="puso">Product </td>
      <td  class="puso">:</td>
      <td ><div id="produkdiv">
	  <input name="produk" type="text" class="inputlabel" id="produk" value="<?=$produk?>" onChange="ketik_produk(this)" maxlength="10" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_produk" type="text" class="inputlabel" id="nama_produk" value="<?=$nama_produk?>" readonly="true"  size="30"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="btn_produk" type="button" class="button" id="btn_produk" value="..." onClick="findproduk()"/>
      <input name="val_error_produk" type="hidden" id="val_error_produk" value="0" />
    </div></td>
    </tr>
    <tr>
      <td  class="puso">PP Date</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl1?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl2?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
      <td  class="puso">Delivery Type</td>
      <td  class="puso">:</td>
      <td ><select name="jenis_kirim" id="Jenis Pengiriman" >
          <option value="">---Choose---</option>
          <? $fungsi->or_jenis_kirim($jenis_kirim); ?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Status</td>
      <td  class="puso">:</td>
      <td ><select name="status" id="Status" >
          <option value="">---Choose---</option>
          <? $fungsi->or_status($status); ?>
      </select></td>
    </tr> 
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
<form name="export" method="post" action="pp_open_xls.php">
<input name="branch_plant" type="hidden" id="branch_plant" value="<?=$branch_plant?>"/>
<input name="no_pp" id="no_pp" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$no_pp;?>"/>
<input type="hidden" value="<?=$sold_to;?>" class="inputlabel" id="sold_to" name="sold_to" size="10">
<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
<input name="ship_to" type="hidden" id="ship_to" size=12 value="<?=$ship_to?>"/>
<input name="produk" type="hidden" id="produk" size=12 value="<?=$produk?>"/>
<input name="jenis_kirim" type="hidden" id="jenis_kirim" size=12 value="<?=$jenis_kirim?>"/>
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tgl1?>" />
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgl2?>" />
<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	
&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="Export" /> 	
</form>
	<table width="1200" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1200" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="17"><span class="style5">&nbsp;Purchase Requisition Approval Table </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1200" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >PP date</strong></td>
		<td align="center"><strong>Code</strong></td>
		<td align="center"><strong>Distributor  </strong></td>
		 <td align="center"><strong>Shipto Code</strong></td>
		 <td align="center"><strong>Shipto Name</strong></td>
		 <td align="center"><strong>Distrik Code</strong></td>
		 <td align="center"><strong>Distrik Name </strong></td>
		 <td align="center"><strong>Product Code</strong></td>
		 <td align="center"><strong>Produk Name</strong></td>
		 <td align="center"><strong>Qty</strong></td>
		 <td align="center"><strong>Tanggal Kirim</strong></td>
		 <td align="center"><strong>Estimasi Tgl Terima</strong></td>
		 <td align="center"><strong>Status</strong></td>
		 <td align="center"><strong>NO SO</strong></td>		 
		 <td align="center"><strong>Royalty</strong></td>
		 <td align="center"><strong>MSA</strong></td>		 		 		 
		 <td align="center"><strong>LOG SO</strong></td>		 
                 <td align="center"><strong>Tipe</strong></td>
		 <td align="center"><strong>Details</strong></td>
		 <td align="center"><strong>Log</strong></td>
		 <td align="center"><strong>Log Approve</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
                if($no_pp_v[$i] != $no_pp_v[$i-1]){
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp_v[$i]; ?></td>
		<td align="center"><? echo $tgl_pp_v[$i]; ?></td>
		<td align="center"><? echo $sold_to_v[$i]; ?></td>
		<td align="left"><? echo $namasold_to_v[$i]; ?></td>
		<td align="left"><? echo $ship_to_v[$i]; ?></td>
		<td align="left"><? echo ''.$namaship_to_v[$i].', '.$alamat_v[$i].''; ?></td>
		<td align="left"><? echo $kddistrik_v[$i]; ?></td>
		<td align="left"><? echo $nmdistrik_v[$i]; ?></td>
		<td align="left"><? echo $kdproduk_v[$i]; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $tgl_leadtime_v[$i]; ?></td>
		<td align="center"><? echo $status_v[$i]; ?></td>
		<td align="center"><? echo $nosonya[$i]; ?></td>	
		<td align="center"><?=$royalty_v[$i]=='X'?'YES':'NO'?></td>
		<td align="center"><?=$msa_v[$i]=='X'?'YES':'NO'?></td>	
		<td align="center"><?
			$returncari = false; 
			for($x=0; $x<$totallog;$x++) {
				if($nosonya[$i]==$LOG_NO_SO[$x]){
					$returncari = true; 
				}
			}

			if($returncari){
				echo "SO delete";
			}else{
				echo "-";
			}

		?></td>			
                <td align="center"><? if($tipepp[$i]!='') echo 'LELANG'; ?></td>
				<td align="center"><? echo $pesan_detail[$i]; ?></td>
		<td align="center"><a href="pp_detil.php?id_detail=<?=$id_v[$i]?>">Detil</a></td>
		<td align="center"><? echo '<a href="javascript:void()" onMouseOver = "overlib(\'<b>-- History PP --</b><br>Create PP By : '.$create_v[$i].'<br>Approve PP By : '.$approve_v[$i].'<br>Approve PP Date : '.$appdate_v[$i].'<br>Last Update PP By : '.$last_update_v[$i].'<br>Last Update PP Date : '.$last_update_date_v[$i].'\')" onMouseOut="nd()">Log</a>'; ?></td>
		
		</tr>
	  <? } }?>
	  <tr class="quote">
		<td colspan="17" align="center"></td>
	    </tr>
	</table>

	</div>
	<?
	}?>
<div align="center">

<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
