<?
session_start();
require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}
function showNilaiKOMA($nilai){
	if($nilai>0) 
        {            
            return number_format($nilai, 2, ',', '');
        }else return '0';
}


//$hakakses=array("admin");
//$halaman_id=8;

$user_id=$_SESSION['user_id'];
//$user_id='mady';

/*if ($fungsi->keamanan0($user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
*/

$bulanVolum=$_GET['bulanVolum'];
$tahunVolum=$_GET['tahunVolum'];
$nmplant=$_GET['nmplant'];


$currentPage="cetakexcel_targetvolume.php";
$komen="";   
    $sql= "
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN,TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN12 as BRAN1,PLANT_GP,NAME1,
            TARGET_GP            
            from(
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN2 as BULAN,TAHUN2 as TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN1 as BRAN12,PLANT_GP,
            TARGET_GP            
            from(            
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN as BULAN2,TAHUN as TAHUN2,PLANT,COM,BRAN1,PLANT_GP,VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP as STATUS_GP2,KONTRAK_VOL_GP,ITEM_NO as ITEM_NO2
                        from ZREPORT_TARGET_EXP where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                        PLANT='$nmplant' and COM='2000' and ITEM_NO='121-301' and BRAN1 is not null 
            ) LEFT JOIN ZREPORT_TARGET_PLANT a ON (
            a.KD_PLANT=PLANT and a.BULAN=BULAN2 and a.TAHUN=TAHUN2 and a.ITEM_NO=ITEM_NO2 and a.KD_KOTA is null and a.BRAN12=BRAN1 and a.STATUS_GP=0

            )
        )left join M_CUSTOMER on (KUNNR=PLANT_GP)
        ";
    //echo $sql;
    $query= oci_parse($conn, $sql);
    oci_execute($query);
    $q = 0;
    while($row=oci_fetch_array($query)){            
		$ID_TARGET_EXP[$q]=$row['ID_TARGET_EXP'];
		$NO_EXPEDITUR[$q]=$row['NO_EXPEDITUR'];
		$NAMA_EXPEDITUR[$q]=$row['NAMA_EXPEDITUR'];
		$KOTA[$q]=$row['KOTA'];
                $NM_KOTA[$q]=$row['NM_KOTA'];
                $TARGET_KOTA[$q]=$row['TARGET_GP'];
                $BULAN[$q]=$row['BULAN'];  
                $TAHUN[$q]=$row['TAHUN'];
                $PLANT[$q]=$row['PLANT'];
                $COM[$q]=$row['COM'];
		$VOLUME[$q]=$row['VOLUME_GP'];  
                $VOL_INDEX[$q]=$row['VOL_INDEX_GP'];  
                $ADJUSTMANT[$q]=$row['ADJUSTMANT_GP'];  
                $STATUS[$q]=$row['STATUS_GP2'];
                $KONTRAVOL[$q]=$row['KONTRAK_VOL_GP'];
                $BRAN1[$q]=$row['BRAN1'];
                $PLANT_GP[$q]=$row['PLANT_GP'];
                $NAME_GP[$q]=$row['NAME1'];
                $q++;
                
    }
    $total = count($ID_TARGET_EXP);
//================================ eksport data ================================
  include('../Spreadsheet/Excel/Writer.php');
  $todate=date("Ymd"); 
  $xls =& new Spreadsheet_Excel_Writer();
  $titt="rekap_targetVolumeGP".$bulanVolum.$tahunVolum.$nmplant."_".$todate.".xls";
  $xls->send($titt);
//======================================================================================================
?>
<style type="text/css">
<!--
.style1 {color: #FFFFFF}
-->
</style>
  <table width="248" border="1" class="style7">
     <tr class="style4">
           <td width="48" align="center"><strong>BULAN</strong></td>
	   <td width="122" align="center"><strong>TAHUN</strong></td>
           <td width="159" align="center"><strong>PLANT</strong></td>
           <td width="102" align="center">&nbsp;</td>
           <td width="100" align="center" colspan="2">KETERANGAN STATUS</td>
       </tr>
       <tr>
           <td><? echo $bulanVolum;?></td>
           <td><? echo $tahunVolum;?></td>
           <td><? echo $nmplant;?></td>
           <td width="102" align="center">&nbsp;</td>
           <td>&nbsp;0 = Aktif</td>
           <td>&nbsp;1 = Inaktif</td>
       </tr>
  </table>
  <table width="1169" border="1" class="style7">
	  <tr class="style4">
           <td width="48"><div align="center"><strong>No.</strong></div></td>
           <td width="122" align="center"><strong>NO EXPEDITUR </strong></td>
	   <td width="159" align="center"><strong>NAMA EXPEDITUR </strong></td>
	   <td width="102" align="center"><strong>KODE KOTA </strong></td>
	   <td width="100" align="center"><strong>NAMA KOTA </strong></td>
           <td width="100" align="center"><strong>KECAMATAN</strong></td>
           <td width="100" align="center"><strong>PLANT GP</strong></td>
           <td width="100" align="center"><strong>NAME GP</strong></td>
           <td width="100" align="center"><strong>TARGET KOTA </strong></td>
	   <td width="130" align="center"><strong>VOLUME</strong></td>
           <td width="104" align="center"><strong>INDEX</strong></td>
	   <td width="113" align="center"><strong>ADJUSTMANT</strong></td>
           <td width="113" align="center"><strong>KONTRAK VOLUME</strong></td>
           <td width="40" align="center"><strong>STATUS</strong></td>
	  </tr>

<?
if($total>0){
?>              
                    <?  for($i=0; $i<$total;$i++) {
                        echo "<tr>";
			
                        $no=$i+1;
                    ?>
			  
                            <td align="center"><? echo $no.".";?></td>
                            <td align="center"><? echo $NO_EXPEDITUR[$i]; ?></td>
                            <td align="center"><? echo $NAMA_EXPEDITUR[$i]; ?></td>
                            <td align="center"><? echo $KOTA[$i]; ?></td>
                            <td align="center"><? echo $NM_KOTA[$i]; ?></td>
                            <td align="center"><? echo $BRAN1[$i]; ?></td>
                            <td align="center"><? echo $PLANT_GP[$i]; ?></td>
                            <td align="center"><? echo $NAME_GP[$i]; ?></td>
                            <td align="right"><? echo showNilaiKOMA($TARGET_KOTA[$i]); ?></td>
                            <td align="right"><? echo showNilaiKOMA($VOLUME[$i]); ?></td>
                            <td align="right"><? echo showNilaiKOMA($VOL_INDEX[$i]);$keyIndex=showNilai2($VOL_INDEX[$i]);?></td>
                            <td align="left"><? echo showNilaiKOMA($ADJUSTMANT[$i]);?></td>
                            <td align="left"><? echo showNilaiKOMA($KONTRAVOL[$i]);?></td>
                            <td align="left"><? echo $STATUS[$i];?></td>
			</tr>
		  <?
                  }
}
                  ?>
  </table>
