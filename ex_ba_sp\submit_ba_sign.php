<?php

session_start();
include('../include/ex_fungsi.php');
include('../include/e_sign.php');
include ('../include/email.php');
require_once 'helper.php';
require_once ('../security_helper.php');
sanitize_global_input();

$email = new Email();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$halaman_id = 3095;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];

$no_ba = $_GET['no_ba'];
// $no_ba_v = $_GET['no_ba'];

$signType = (isset($_GET['type']) && $_GET['type'] == 'otp') ? 'otp' : 'keyla';
$isSignTypeOtp = $signType == 'otp';
$isSignTypeKeyla = $signType == 'keyla';


function showErrorMessage($message)
{
    global $no_ba, $signType;

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-danger" role="alert">
            <strong>Error!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
            <a href="submit_ba_sign.php?no_ba=<?= $no_ba ?>&type=<?= $signType ?>?&resend=0" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Coba Lagi&nbsp;&nbsp;&gt;&gt;</a>
            <a href="lihat_ba_hdr_ex.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php
}

// $query = "SELECT * FROM EX_BA WHERE ID = '$no_ba'";
// $sql = oci_parse($conn, $query);
// oci_execute($sql);

// $data = oci_fetch_array($sql);
// // print_r($data);exit;
// $no_ba_v = $data['NO_BA'];
// $org_v = $data['ORG'];
// $no_vendor_v = $data['NO_VENDOR'];
// $nama_vendor_v = $data['NAMA_VENDOR'];
// $total_semen_v = $data['KLAIM_SEMEN'];
// $total_ppdks_v = $data['PPDKS'];
// $total_inv_v = $data['TOTAL_INV'];
// $last_updated_by_v = $data['LAST_UPDATED_BY'];

$query_ba = "SELECT
                        EX_BA.ID,
                        EX_BA.NO_BA,
                        EX_BA.NO_VENDOR,
                        EX_BA.TOTAL_INV,
                        EX_BA.PAJAK_INV,
                        EX_BA.NAMA_VENDOR,
                        EX_BA.KLAIM_KTG,
                        EX_BA.KLAIM_SEMEN,
                        EX_BA.PDPKS,
                        EX_BA.PDPKK,
                        EX_BA.DELETE_MARK,
                        EX_BA.ORG,
                        EX_BA.TOTAL_INVOICE,
                        EX_BA.TGL_BA,
                        EX_BA.STATUS_BA,
                        EX_BA.FILENAME,
                        EX_BA.ALASAN_REJECT,
                        EX_BA.ID_USER_APPROVAL,
                        EX_BA.SIGN_ORDER_ID_1,
                        EX_BA.SIGN_STATUS_1,
                        EX_BA.SIGN_TOKEN_1,
                        SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
                        SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
                        SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
                        SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
                        SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
                        SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
                      SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
                      SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
                      SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
                      SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
                        to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
                    FROM
                        EX_BA
                        JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
                    WHERE EX_BA.DELETE_MARK = '0' 
                        AND EX_BA.ID = :no_ba
                    GROUP BY EX_BA.ID,
                        EX_BA.NO_BA,
                        EX_BA.NO_VENDOR,
                        EX_BA.TOTAL_INV,
                        EX_BA.PAJAK_INV,
                        EX_BA.NAMA_VENDOR,
                        EX_BA.KLAIM_KTG,
                        EX_BA.KLAIM_SEMEN,
                        EX_BA.PDPKS,
                        EX_BA.PDPKK,
                        EX_BA.DELETE_MARK,
                        EX_BA.ORG,
                        EX_BA.TOTAL_INVOICE,
                        EX_BA.TGL_BA,
                        EX_BA.STATUS_BA,
                        EX_BA.FILENAME,
                        EX_BA.ALASAN_REJECT,
                        EX_BA.SIGN_ORDER_ID_1,
                        EX_BA.SIGN_STATUS_1,
                        EX_BA.SIGN_TOKEN_1,
                        EX_BA.ID_USER_APPROVAL
                    ORDER BY
                        EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data = oci_fetch_array($sql_ba);
// print_r($data);exit;
$no_ba_v = $data['NO_BA'];
$org_v = $data['ORG'];
$no_vendor_v = $data['NO_VENDOR'];
$nama_vendor_v = $data['NAMA_VENDOR'];
$total_semen_v = $data['TOTAL_KLAIM_SEMEN'];
$total_kantong_v = $data['TOTAL_KLAIM_KTG'];
$total_ppdks_v = $data['PDPKS'];
$total_inv_v = $data['SHP_COST'];
//INSERT LOG HISTORY BA
$email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
<div align=\"center\">
<thead>
<tr class=\"quote\">
<td ><strong>&nbsp;&nbsp;No.</strong></td>
<td align=\"center\"><strong>ORG</strong></td>
<td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
<td align=\"center\"><strong>EKSPEDITUR</strong></td>
<td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
<td align=\"center\"><strong>KLAIM SEMEN</strong></td>
<td align=\"center\"><strong>PDPKS</strong></td>
<td align=\"center\"><strong>TOTAL</strong></td>
<td align=\"center\"><strong>STATUS</strong></td>
</tr>
</thead>
<tbody>";

$email_content_table .= " 
<td align=\"center\">1</td>
<td align=\"center\">".$org_v."</td>       
<td align=\"center\">".$no_ba_v."</td>
<td align=\"center\">".$no_vendor_v."</td>
<td align=\"center\">".$nama_vendor_v."</td>
<td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
<td align=\"center\">Open</td>
</tr>";
                    
$orderId = $data['SIGN_ORDER_ID_1'];

$signStatus = $data['SIGN_STATUS_1'];
$isDocBelumDikirim = !$signStatus || $signStatus == 0;
$isDocSent = $signStatus >= 1;
$isOtpSent = $signStatus == 2;
$isSigned = $signStatus == 3;
$isDownloaded = $signStatus == 4;

$eSign = new ESign();
$eSign->refreshJwtIfExpired();

if (!$data['FILENAME']) {
    // $pdfExporter = new PdfExporter();
    // $response = $pdfExporter->beritaAcara($data['NO_BA']);

    // // Menyimpan pdf ke dalam file di CSMS
    // $pdf = fopen('upload/' . $filename, 'w');
    // fwrite($pdf, $response);
    // fclose($pdf);

    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");

    // Update filename
    $filename = "DokumenBA-$no_ba_v.pdf";
    $fieldNames = array('FILENAME');
    $fieldData = array($filename);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data['FILENAME'] = $filename;
}

if ($isDocBelumDikirim) {
    $id_user_approval = $data['ID_USER_APPROVAL'];
    $query = "SELECT * FROM TB_USER_BOOKING WHERE ID = '$id_user_approval'";
    $sql = oci_parse($conn, $query);
    oci_execute($sql);

    $email_user_approval = oci_fetch_array($sql);
    $email_user_approval = $email_user_approval['ALAMAT_EMAIL'];

    $fileName = $data['FILENAME'];
    $pdfExporter = new PdfExporter();
    $fileContent = $pdfExporter->beritaAcara($data['NO_BA']);
    $fileContent = base64_encode($fileContent);

    try {
        // Send document to peruri
        $orderId = $eSign->uploadDocument(array(
            'email' => $email_user_approval,
            'fileName' => $fileName,
            'base64Document' => $fileContent,
            'lowerLeftX' => '34',
            'lowerLeftY' => '506',
            'upperRightX' => '136',
            'upperRightY' => '574',
            'page' => '1',
            'varReason' => 'Submit BASTP',
        ));
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    // Save order id yang didapatkan dari eSign
    $tableName = 'EX_BA';
    $fieldNames = array('SIGN_ORDER_ID_1', 'SIGN_STATUS_1');
    $fieldData = array($orderId, '1');
    $field_id = array('ID');
    $value_id = array("$no_ba");

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data['SIGN_ORDER_ID_1'] = $orderId;
    $isDocBelumDikirim = false;
    $isDocSent = true;
}

if ($isSignTypeOtp && ($isDocSent) && $_GET['resend'] != '1' && !isset($_POST['submit'])) {
    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");

    $dataGetOtp = null;

    try {
        $dataGetOtp = $eSign->getOtp($data['SIGN_ORDER_ID_1']);
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $fieldNames = array('SIGN_STATUS_1', 'SIGN_TOKEN_1');
    $fieldData = array('2', $dataGetOtp->token);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $isDocSent = false;
    $isOtpSent = true;
}

if ($isDocSent || $isOtpSent) {
    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");
    try {
        // cek status doc
        $resp = $eSign->checkDoc($data['SIGN_ORDER_ID_1']);

        // update status di DB
        if($resp){
            $fieldNames = array('SIGN_STATUS_1');
            $fieldData = array('3');
            $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
            $data_invoice_ba['SIGN_STATUS_1'] = 3;

            $isDocSent = false;
            // $isOtpSent = false;
            $isSigned = true;
        }
    } catch (Exception $e) {
        // $message = $e->getMessage();
        // showErrorMessage($message);
        // exit;
    }
}

if ($isSignTypeOtp && $isOtpSent && $_GET['resend'] == '1' && !isset($_POST['submit'])) {
    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");

    $dataGetOtp = null;

    try {
        $dataGetOtp = $eSign->getOtp($data['SIGN_ORDER_ID_1']);
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $fieldNames = array('SIGN_STATUS_1', 'SIGN_TOKEN_1');
    $fieldData = array('2', $dataGetOtp->token);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $show_ket = $isDocSent ? 'Kode OTP berhasil dikirim!' : 'Kode OTP berhasil dikirim ulang!';
    $habis = "submit_ba_sign.php?no_ba=$no_ba&type=$signType&resend=0";

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
            <a href="<?= $habis ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php

    exit;
}

if ((($isSignTypeKeyla && ($isDocSent || $isOtpSent)) || ($isSignTypeOtp && $isOtpSent)) && isset($_POST['submit'])) {
    $otpCode = $_POST['OTP'];
    $keylaCode = $_POST['keyla'];

    try {
        if ($isSignTypeKeyla) {
            $eSign->signing(array(
                'otpCode' => '',
                'orderId' => $data['SIGN_ORDER_ID_1'],
                'token' => $keylaCode,
            ));
        } else if ($isSignTypeOtp) {
            $eSign->signing(array(
                'otpCode' => $otpCode,
                'orderId' => $data['SIGN_ORDER_ID_1'],
                'token' => $data['SIGN_TOKEN_1'],
            ));
        } else {
            throw new Exception('Tipe sign tidak dikenali');
        }
    } catch (Exception $e) {
        showErrorMessage($e->getMessage());
        exit;
    }

    $tableName = 'EX_BA';
    $fieldNames = array('SIGN_STATUS_1');
    $fieldData = array('3');
    $field_id = array('ID');
    $value_id = array("$no_ba");

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    //sendEmail
    $mailCc = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA = :no_ba and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_bind_by_name($query, ":no_ba", $no_ba);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];

    // dev
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Verif BA Rekapitulasi' and B.ORG = '".$org_v."' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    // prod
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.ID=3255 and B.ORG = '".$org_v."' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    $user = new User_SP();
    $admin_trans = $user->get_admin_trans();
    foreach($admin_trans as $at){
        if(!empty($at['ALAMAT_EMAIL'])){
            if(empty($mailTo)){
                $mailTo = $at['ALAMAT_EMAIL'];
            }else{
                $mailTo .= ','.$at['ALAMAT_EMAIL'];
            }
        }
    }
    
    // $mailTo .= ', <EMAIL>';
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve BASTP', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }

    $isDocSent = false;
    $isOtpSent = false;
    $isSigned = true;
}

if ($isSigned) {
    $fileName = $data['FILENAME'];
    $pdfBase64Encoded = $eSign->downloadDocument($data['SIGN_ORDER_ID_1']);

    $pdfBase64Decoded = base64_decode($pdfBase64Encoded);

    if (!file_exists('upload')) {
        mkdir('upload', 0777, true);
    }

    $pdf = fopen("upload/$fileName", 'w');
    fwrite($pdf, $pdfBase64Decoded);
    fclose($pdf);

    $fieldNames = array('STATUS_BA', 'SIGN_STATUS_1');
    $fieldData = array('20', '4');

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    

    $isSigned = false;
    $isDownloaded = true;
}

if ($isDownloaded) {
    $show_ket = 'Dokumen BASTP berhasil ditandatangani';
    $habis = 'lihat_ba_hdr_ex.php';

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
            <a href="<?= $habis ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php
    $id_user_approval = $data['ID_USER_APPROVAL'];
                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
                    $field_data = array("$no_ba_v","20","SUBMITTED","$id_user_approval","SYSDATE");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
    exit;
}


?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Sign Dokumen BA</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
    table.excel {
        border-style: ridge;
        border-width: 1;
        border-collapse: collapse;
        font-family: sans-serif;
        font-size: 12px;
    }

    table.excel thead th,
    table.excel tbody th {
        background: #CCCCCC;
        border-style: ridge;
        border-width: 1;
        text-align: center;
        vertical-align: bottom;
    }

    table.excel tbody th {
        text-align: center;
        width: 20px;
    }

    table.excel tbody td {
        vertical-align: bottom;
    }

    table.excel tbody td {
        padding: 0 3px;
        border: 1px solid #EEEEEE;
    }
</style>

<body>
    <div align="center">
        <table width="800" align="center" class="adminheading" border="0">
            <tr>
                <th class="da2">Sign BA</th>
            </tr>
        </table>
    </div>

    <form method="post" name="import" id="import" enctype="multipart/form-data" action="submit_ba_sign.php?no_ba=<?= $no_ba ?>&type=<?= $signType ?>">
        <table width="800" align="center" class="adminform">
            <tr height="30">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso" colspan="3">
                    <h3 style="margin-bottom: 0 !important;">&nbsp;&nbsp;E-Sign Expeditur</h3>
                </td>
            </tr>

            <?php if ($isSignTypeOtp) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;OTP</td>
                    <td class="puso" width="20">:</td>
                    <td><input type="number" id="OTP" name="OTP" /></td>
                </tr>
            <?php endif ?>
            <?php if ($isSignTypeKeyla) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;Token Keyla</td>
                    <td class="puso" width="20">:</td>
                    <td>
                        <input type="text" id="keyla" name="keyla" autocomplete="off" />
                    </td>
                </tr>
            <?php endif ?>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td>
                    <button id="submit-button" type="submit" class="button" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">
                        Sign
                    </button>
                    <input type="hidden" name="submit" value="Sign">
                    <!-- <input name="submit" type="submit" id="submit-button" class="button" value="Sign" > -->

                    <?php if ($isSignTypeOtp) : ?>
                        <a id="resend" href="submit_ba_sign.php?no_ba=<?= $no_ba ?>&type=<?= $signType ?>&resend=1">
                            Kirim Ulang OTP
                        </a>
                    <?php endif ?>
                </td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>

            </tr>
        </table>
    </form>
    <br><br>

    <div align="center">
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

    <script>
        var form = document.getElementById('import');
        var btnSubmit = document.getElementById('submit-button');
        var btnResend = document.getElementById('resend');

        form.addEventListener('submit', function(e) {
            btnSubmit.disabled = true;
            btnSubmit.innerHTML = 'Loading...';
        });

        btnResend.addEventListener('click', function(e) {
            btnResend.innerHTML = 'Loading...';
        });
    </script>

</body>

</html>