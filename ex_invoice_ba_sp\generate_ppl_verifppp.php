<? 
	session_start();
	include ('../include/ex_fungsi.php');
	include ('../include/validasi.php'); 
	require_once ('../security_helper.php');
	sanitize_global_input();
	$fungsi=new ex_fungsi();
	$conn=$fungsi->ex_koneksi();

	$halaman_id=438;
	$user_id=$_SESSION['user_id'];
	/*

	$action_page=$fungsi->security($conn,$user_id,$halaman_id);
	*/$page="generate_ppl_verif.php";

	$no_rek= $_REQUEST['no_rek'];
	$nama_bank= $_REQUEST['nama_bank'];
	$cabang_bank= $_REQUEST['cabang_bank'];
	$bvtyp= $_REQUEST['bvtyp'];
	$keterangan_miro = $_REQUEST['keterangan_miro'];
	$skbp_val = $_REQUEST['skbp'];

	$no_invoice= $_REQUEST['no_invoice'];
	$sql= "SELECT NO_BA,NO_INVOICE,NO_INV_VENDOR,NO_SHP_TRN,KODE_PRODUK,NAMA_PRODUK,PLANT,NAMA_PLANT,WARNA_PLAT,VEHICLE_TYPE,NAMA_VENDOR,VENDOR,SAL_DISTRIK,NAMA_SAL_DIS,SOLD_TO,NAMA_SOLD_TO,SHIP_TO,QTY_SHP,QTY_KTG_RUSAK,QTY_SEMEN_RUSAK,ID,NO_POL,SHP_COST,TOTAL_KLAIM_ALL,NO_PAJAK_EX,KOSTL,PRCTR,KELOMPOK_TRANSAKSI,INCO,ORG, to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1,to_char(TANGGAL_KIRIM,'YYYY') as TAHUNKIRIMF, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
	// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_invoice_v=$row[NO_INVOICE];
		$no_ba_v=$row[NO_BA];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM1];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v=$row[PLANT]; 
		$nama_plant_v=$row[NAMA_PLANT]; 
		$warna_plat_v=$row[WARNA_PLAT]; 
        $type_plat_ingv=trim($row[VEHICLE_TYPE]);                
        if($type_plat_ingv=='205'){
            $warna_plat_v='HITAM';
        }
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR]; 
		
		$tanggal_invoice_v=$row[TANGGAL_INVOICE1];
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  
		$cost_center_v=$row[KOSTL];  
		$profit_center_v=$row[PRCTR];  
		$prctr_v[]=$row[PRCTR];  
		$org_v[$row[ORG]]=$row[ORG];  
		$tahunkrim_v[$row[TAHUNKIRIMF]]=$row[TAHUNKIRIMF];           	
	}

	$total=count($shp_trn_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

	$sql_ = "SELECT NO_DOC_DENDA,KREDIT,SALDO FROM EX_DENDAK3_SALDO WHERE NO_INVOICE LIKE '$no_invoice'";

	$query_= oci_parse($conn, $sql_);
	oci_execute($query_);
	$dendak3 = array();
	while($row=oci_fetch_assoc($query_)){
		$dendak3[] = $row;
	}

    $sqlPOEX = "SELECT MPOAT.BA_NUMBER,MPOAT.NUM,MPOAT.NILAI_TRANSAKSI,MPOA.JUMLAH FROM M_POTONGAN_OA MPOA
                    JOIN M_POTONGAN_OA_TRANS MPOAT ON MPOAT.NUM = MPOA.NUM
                    WHERE MPOAT.NO_INVOICE = '$no_invoice' AND MPOAT.IS_DELETE = '0'";

	$queryPOEX= oci_parse($conn, $sqlPOEX);
	oci_execute($queryPOEX);
	$poex = array();
	while($rowpoex=oci_fetch_assoc($queryPOEX)){
        $poex[] = $rowpoex;
	}
?>
<script language=javascript>
	<!-- Edit the message as your wish -->
	var message="You dont have permission to right click";

	function clickIE() {
		if (document.all) {
			(message);
			return false;
		}
	}
	
	function clickNS(e) {
		if (document.layers||(document.getElementById&&!document.all)) {
			if (e.which==2||e.which==3) {
				(message);
				return false;
			}
		}
	}

	if (document.layers) {
		document.captureEvents(Event.MOUSEDOWN);
		document.  onmousedown=clickNS;
	} else {
		document.onmouseup=clickNS;document.oncontextmenu  =clickIE;
	}
	
	document.oncontextmenu=new Function("return false")
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
		<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
		<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
		<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
		<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
		<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
		<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
		<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
		<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
		<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
		<script src="../include/jquery.min.js"></script>
		<script src="../include/bootstrap/js/bootstrap.min.js"></script>

		<script> 
			function getXMLHTTP() { 
				var xmlhttp=false;	
				try {
					xmlhttp=new XMLHttpRequest();
				}
				catch(e) {		
					try {			
						xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
					}
					catch(e) {
						try {
							xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
						}
						catch(e1) {
							xmlhttp=false;
						}
					}
				}
				return xmlhttp;
    		}
		</script>
		<script language="javascript">
			var j=1;
    		function start_addd() {
        		j++;
				var cek=j-1;
				if(validasi('komponen_biaya_d'+cek+'','','R','nama_komponen_d'+cek+'','','R','nilaid'+cek+'','','RisNum','keterangan_d'+cek+'','','R')) {
					if(cek>1) {
						for (var i = 1; i < cek; i++) {
							var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
							var nilai_acc_nod = obj_acc_nod.value;	
	
							var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
							var nilai_acc_nod_cek = obj_acc_nod_cek.value;	

							if (nilai_acc_nod == nilai_acc_nod_cek ) {
								alert('Data Komponen Biaya Debet Telah Diinputkan \n Silahkan Input Ulang...');
								j--;
								return false;	
							}
						} 
					}

					var body1 = document.getElementById("cobad");
					var newdiv=document.createElement("div");
					newdiv.setAttribute("id", "dd"+j); 
					newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left"><div id="voucherd'+j+'"><input name="komponen_biaya_d'+j+'" type="text" class="inputlabel" id="komponen_biaya_d'+j+'" value="" onChange="ketik_acc_nod(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_d'+j+'" type="text" class="inputlabel" id="nama_komponen_d'+j+'" value="" readonly="true"  size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnd" type="button" class="button" id="cari_btnd" value="..." onClick="find_acc()"/></div></td><td align="left"><input type="text" value="" id="nilaid'+j+'" name="nilaid'+j+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="left"><input name="keterangan_d'+j+'" type="text" id="no_fakturd'+j+'" size="50" value=""/>&nbsp;&nbsp;</td><td width="100"></td></tr></table>';
					body1.appendChild(newdiv);
		
					document.getElementById("jumlahd").value=j;
				} else {
					j--;
				}
    		}

			var k=1;
    		function start_addk() {
        		k++;
				var cek=k-1;
				if(validasi('komponen_biaya_k'+cek+'','','R','nama_komponen_k'+cek+'','','R','nilaik'+cek+'','','RisNum','keterangan_k'+cek+'','','R')) {
					if(cek>1) {
						for (var i = 1; i < cek; i++) {
							var obj_acc_nok = document.getElementById('komponen_biaya_k'+i+'');
							var nilai_acc_nok = obj_acc_nok.value;	
				
							var obj_acc_nok_cek = document.getElementById('komponen_biaya_k'+cek+'');
							var nilai_acc_nok_cek = obj_acc_nok_cek.value;	

							if (nilai_acc_nok == nilai_acc_nok_cek ) {
								alert('Data Komponen Biaya Kredit Telah Diinputkan \n Silahkan Input Ulang...');
								k--;
								return false;	
							}
						} 
					}

					var body1 = document.getElementById("cobak");
					var newdiv=document.createElement("div");
					newdiv.setAttribute("id", "kk"+k); 
					newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left"><div id="voucherk'+k+'"><input name="komponen_biaya_k'+k+'" type="text" class="inputlabel" id="komponen_biaya_k'+k+'" value="" onChange="ketik_acc_nok(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_k'+k+'" type="text" class="inputlabel" id="nama_komponen_k'+k+'" value="" readonly="true"  size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnk" type="button" class="button" id="cari_btnk" value="..." onClick="find_acck()"/></div></td><td align="left"><input type="text" value="" id="nilaik'+k+'" name="nilaik'+k+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="left"><input name="keterangan_k'+k+'" type="text" id="keterangan_k'+k+'" size="50" value=""/>&nbsp;&nbsp;</td><td width="100"></td></tr></table>';
					body1.appendChild(newdiv);
		
					document.getElementById("jumlahk").value=k;
				} else {
					k--;
				}
    		}
    
			function cek_lastd() {
				var obj = document.getElementById("jumlahd");
				var cek = obj.value;	
				if (document.hasil == true) {
					if(cek>1) {
						for (var i = 1; i < cek; i++) {
							var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
							var nilai_acc_nod = obj_acc_nod.value;	
				
							var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
							var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
			
							if (nilai_acc_nod == nilai_acc_nod_cek ) {
								alert('Data Komponen Debet Telah Diinputkan \n Silahkan Input Ulang...');
								j--;
								return false;	
								document.hasil = false;
							}
						} 
					}
					return true;
				} else {
					return false;	
					document.hasil = false;
				}	
			}

			function cek_lastk() {
				var obj = document.getElementById("jumlahk");
				var cek = obj.value;	
				if (document.hasil == true) {
					if(cek>1) {
						for (var i = 1; i < cek; i++) {
							var obj_acc_nod = document.getElementById('komponen_biaya_k'+i+'');
							var nilai_acc_nod = obj_acc_nod.value;	
				
							var obj_acc_nod_cek = document.getElementById('komponen_biaya_k'+cek+'');
							var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
			
							if (nilai_acc_nod == nilai_acc_nod_cek ) {
								alert('Data Komponen Kredit Telah Diinputkan \n Silahkan Input Ulang...');
								k--;
								return false;	
								document.hasil = false;
							}
						} 
					}
					return true;	
				} else {
					return false;	
					document.hasil = false;
				}	
			}

			function stop_addd() {
				if (j==1) {
					alert('Maaf Minimal 1 Detail Komponen Biaya..');
					return false;
				}
				k=j;
				k=k.toString();
				var body1 = document.getElementById("cobad");
				var buang = document.getElementById("dd"+k);
				body1.removeChild(buang);
				j=j-1;
				document.getElementById("jumlahd").value=j;
			}

			function stop_addk() {
				if (k==1) {
					alert('Maaf Minimal 1 Detail Komponen Biaya..');
					return false;
				}
				l=k;
				l=l.toString();
				var body1 = document.getElementById("cobak");
				var buang = document.getElementById("kk"+l);
    			body1.removeChild(buang);
				k=k-1;
				document.getElementById("jumlahk").value=k;
			}
	
			function IsNumeric(obj) {
				var strValidChars = "0123456789";
				var strChar;
				var strString = obj.value;
			
				if (strString.length == 0) {
					alert("Harus Diisi Angka..!!!");
					obj.value="";
					return false;
				} else {
					if (parseInt(strString) > 0 ) {
						for (i = 0; i < strString.length; i++) {
							strChar = strString.charAt(i);
							if (strValidChars.indexOf(strChar) == -1) {
								alert("Hanya Masukkan Angka...!");
								obj.value="";
								return false;
							}
						}
					} else {
						alert("Masukkan Angka Lebih Dari 0..!!!");
						obj.value="";
						return false;
					}	  
				} 
			}

			function ketik_acc_nod(obj) {
				var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&nourut="+j;
				var req = getXMLHTTP();
				if (req) {
					req.onreadystatechange = function() {
						if (req.readyState == 4) {
							if (req.status == 200) {						
								document.getElementById("voucherd"+j).innerHTML=req.responseText;						
							} else {
								alert("There was a problem while using XMLHTTP:\n" + req.statusText);
							}
						}				
					}			
					req.open("GET", strURL, true);
					req.send(null);
				}
			}

			function ketik_acc_nok(obj) {
				var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&nourut="+k;
				var req = getXMLHTTP();
				if (req) {
					req.onreadystatechange = function() {
						if (req.readyState == 4) {
							if (req.status == 200) {						
								document.getElementById("voucherk"+k).innerHTML=req.responseText;						
							} else {
								alert("There was a problem while using XMLHTTP:\n" + req.statusText);
							}
						}				
					}			
					req.open("GET", strURL, true);
					req.send(null);
				}
			}

			function find_acc() {	
				var strURL="cari_accd.php?nourut="+j;
				popUp(strURL);
			}

			function find_acck() {	
				var strURL="cari_acck.php?nourut="+k;
				popUp(strURL);
			}

			function find_rek() {	
				var no_vendor = document.getElementById("no_vendor");
				var strURL="cari_rek.php?no_vendor="+no_vendor.value;
				popUp(strURL);
			}
			function cek(){
				 
				
				  if(confirm("Anda Yakin Generate")){
					let generate= document.getElementById('gener_varify');
				  generate.style.display="none";
				  let cancel=document.getElementById('generate_batal');
				  cancel.style.display="none";
				  document.getElementById("loadingd").style.display = "block";
				  }else{
					 event.preventDefault();
				  }

				 
				
			}
		</script>
	</head>
	<body>
		<div align="center">
			<table width="600" align="center" class="adminheading" border="0">
				<tr>
					<th class="kb2">Generate PPL</th>
				</tr>
			</table>
		</div>
		<div align="center">
			<table width="600" align="center" class="adminlist">
				<tr>
					<th align="left" colspan="4"> Generate PPL </th>
				</tr>
			</table>
		</div>
		<form id="data_claim" name="data_claim" method="post" action="komentarppp.php"  >
  			<table width="600" align="center" class="adminform">
    			<tr width="174">
					<td class="puso">&nbsp;</td>
					<td class="puso">&nbsp;</td>
					<td>&nbsp;</td>
    			</tr>
				<tr width="174">
					<td class="puso">No BA</td>
					<td class="puso">:</td>
					<td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba_v?>" readonly="true"/></td>
				</tr>
				<tr width="174">
					<td class="puso">No Invoice</td>
					<td class="puso">:</td>
					<td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice_v?>" readonly="true"/></td>
				</tr>
				<tr width="174">
					<td class="puso">No Invoice Expeditur </td>
					<td class="puso">:</td>
					<td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_ex_v?>" readonly="true"/></td>
				</tr>
				<tr>
					<td  class="puso">Vendor</td>
					<td  class="puso">:</td>
					<td ><input type="text" id="no_vendor" name="no_vendor"  value="<?=$vendor_v?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_vendor" name="nama_vendor"  value="<?=$nama_vendor_v?>" readonly="true"/></td>
    			</tr>
				<tr>
					<td  class="puso">Tanggal  Invoice</td>
					<td  class="puso">:</td>
					<td ><input name="tanggal_invoice" type="text" id="tanggal_invoice" readonly="true" value="<?=$tanggal_invoice_v?>" /></td>
				</tr>
				<tr>
					<td  class="puso">Cost Center </td>
					<td  class="puso">:</td>
					<td ><input type="text" id="cost_center" name="cost_center"  value="<?=$cost_center_v?>" readonly="true" size="50"/>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td  class="puso">Profit Center </td>
					<td  class="puso">:</td>
					<td ><input type="text" id="profit_center" name="profit_center"  value="<?=$profit_center_v?>" readonly="true"/>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td  class="puso">Warna Plat </td>
					<td  class="puso">:</td>
					<td ><input name="warna_plat" type="text" id="warna_plat" value="<?=$warna_plat_v?>" readonly="true" /></td>
				</tr>
				<tr>
					<td  class="puso">No Rekening </td>
					<td  class="puso">:</td>
					<td >
						<input type="text" id="bvtyp" name="bvtyp"  value="<?=$bvtyp?>" readonly="true" size="8"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_bank" name="nama_bank"  value="<?=$nama_bank?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="no_rek" name="no_rek"  value="<?=$no_rek?>" readonly="true"/>
					</td>
    			</tr>    
				<tr>
					<td  class="puso">&nbsp;</td>
					<td  class="puso">&nbsp;</td>
					<td >
						<input type="text" id="cabang_bank" name="cabang_bank"  value="<?=$cabang_bank?>" readonly="true" size="50"/>
						<input type="hidden" id="plant_v" name="plant_v"  value="<?=$plant_v?>"/>
					</td>
				</tr>
				<tr>
					<td  class="puso">Keterangan</td>
					<td  class="puso">:</td>
					<td >
						<input type="text" id="keterangan_miro" name="keterangan_miro"  value="<?=$keterangan_miro?>" readonly="true" size="60"/>
					</td>
				</tr>  
				<tr>
					<td  class="puso">SKBP</td>
					<td  class="puso">:</td>
					<td> 
						<input type="text" id="skbp_v" name="skbp_v"  value="<?=$skbp_val?>" readonly="true" size="3"/>
						Surat Keterangan Bebas Pemotongan Dana/Pemungutan PPh23
					</td>
				</tr>   
  			</table>
			<br /><br />
			<? if($total>0) { ?>
			<div align="center">
				<table width="95%" align="center" class="adminlist">
					<tr>
						<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Invoice </span></th>
					</tr>
				</table>
			</div> 
			<div align="center">
				<table width="95%" align="center" class="adminlist">
					<thead>
	 					<tr class="quote">
							<td ><strong>&nbsp;&nbsp;NO.</strong></td>
							<td align="center"><strong >TGL SPJ </strong></td>
							<td align="center"><strong>AREA LT </strong></td>
							<td align="center"><strong>PRCTR </strong></td>
							<td align="center"><strong>NO SPJ </strong></td>
							<td align="center"><strong>NO POL </strong></td>
							<td align="center"><strong>PRODUK </strong></td>
							<td align="center"><strong>DISTRIBUTOR</strong></td>
							<td align="center"><strong>K.KTG</strong></td>
							<td align="center"><strong>K.SMN</strong></td>
							<td align="center"><strong>KWANTUM</strong></td>
							<td align="center"><strong>JUMLAH</strong></td>
							<td align="center"><strong>KLAIM</strong></td>
      					</tr >
	  				</thead>
	  				<tbody>
						<?
							$total_qty_kantong_rusak_v=0;
							$total_qty_semen_rusak_v=0;
							$total_qty_v=0;
							$total_shp_cost_v=0;
							$total_total_klaim_all_v=0;
						?>
  						<? for($i=0; $i<$total;$i++) {
							$b=$i+1;
							if(($i % 2) == 0) {	 
								echo "<tr class='row0' id='$rowke' >";
							} else {	
								echo "<tr class='row1'  id='$rowke' >";
							}	
						?>     
						<?
							$total_qty_kantong_rusak_v+=$qty_kantong_rusak_v[$i];
							$total_qty_semen_rusak_v+=$qty_semen_rusak_v[$i];
							$total_qty_v+=$qty_v[$i];
							$total_shp_cost_v+=$shp_cost_v[$i];
							$total_total_klaim_all_v+=$total_klaim_all_v[$i];
						?>
						<td align="center"><? echo $b; ?></td>
						<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
						<td align="center"><? echo $sal_dis_v[$i]; ?></td>
						<td align="center"><? echo $prctr_v[$i]; ?></td>
						<td align="center"><? echo $spj_v[$i]; ?></td>
						<td align="center"><? echo $no_pol_v[$i]; ?></td>
						<td align="center"><? echo $produk_v[$i]; ?></td>
						<td align="center"><? echo $nama_sold_to_v[$i]; ?></td>
						<td align="center"><? echo number_format($qty_kantong_rusak_v[$i],0,",","."); ?></td>
						<td align="center"><? echo number_format($qty_semen_rusak_v[$i],0,",","."); ?></td>
						<td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
						<td align="center"><? echo number_format($shp_cost_v[$i],0,",","."); ?></td>
						<td align="center"><? echo number_format($total_klaim_all_v[$i],0,",","."); ?></td>
						</tr>
	  					<? } ?>
					</tbody>
					<tfoot>
						<tr class="quote">
							<td colspan="8" align="center"><strong>Total</strong></td>
							<td align="center"><strong><? echo number_format($total_qty_kantong_rusak_v,0,",","."); ?></strong></td>
							<td align="center"><strong><? echo number_format($total_qty_semen_rusak_v,0,",","."); ?></strong></td>
							<td align="center"><strong><? echo number_format($total_qty_v,0,",","."); ?></strong></td>
							<td align="center"><strong><? echo number_format($total_shp_cost_v,0,",","."); ?></strong></td>
							<td align="center"><strong><? echo number_format($total_total_klaim_all_v,0,",","."); ?></strong></td>
						</tr>
					</tfoot>
				</table>
				<br />
				<? if (count($dendak3)>0) { ?>
					<div align="center">
						<table width="95%" align="center" class="adminlist">
							<tr>
								<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Pelanggaran K3 </span></th>
							</tr>
						</table>
					</div> 
					<div align="center">
						<table width="95%" align="center" class="adminlist" id="myScrollTable">
							<thead>
								<tr class="quote">
									<td ><strong>&nbsp;&nbsp;NO.</strong></td>
									<td align="center"><strong >NO DOKUMEN</strong></td>
									<td align="center"><strong>DIPOTONGKAN</strong></td>
									<td align="center"><strong>JUMLAH DENDA</strong></td>
								</tr >
							</thead>
							<tbody>
								<?
									$total_potongan=0;
									$total_denda=0;
									$total_sisa=0;
								?>
								<?  $i=0;
									foreach ($dendak3 as $key => $value) {
										$total_potongan += floatval($value['KREDIT']);
										$total_denda += floatval($value['SALDO']);

										$b=$i+1;
										if(($i % 2) == 0) {	 
												echo "<tr class='row0' id='$rowke' >";
										} else {	
											echo "<tr class='row1'  id='$rowke' >";
										}	
								?>     
								<td align="center"><? echo $b; ?></td>
								<td align="center"><?= $value['NO_DOC_DENDA']  ?></td>
								<td align="center"><?= number_format($value['KREDIT'],0,",",".") ?></td>
								<td align="center"><?= number_format($value['SALDO'],0,",",".") ?></td>
								</tr>
								<? $i++; } ?>
							</tbody>
							<tfoot>
								<tr class="quote">
									<td colspan="2" align="center"><strong>Total</strong></td>
									<td align="center"><strong><? echo number_format($total_potongan,0,",","."); ?></strong></td>
									<td align="center"><strong><? echo number_format($total_denda,0,",","."); ?></strong></td>
								</tr>
							</tfoot>
						</table>
					</div>
					<br />
				<? }?>
				<?php if (count($poex)>0) {?>
    				<div align="center">
						<table width="95%" align="center" class="adminlist">
							<tr>
								<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Potongan OA</span></th>
							</tr>
						</table>
					</div> 
					<div align="center">
						<table width="95%" align="center" class="adminlist" id="myScrollTable">
							<thead>
		  						<tr class="quote">
									<td ><strong>&nbsp;&nbsp;NO.</strong></td>
									<td align="center"><strong >NO DOKUMEN</strong></td>
									<td align="center"><strong>NUM</strong></td>
									<td align="center"><strong>DIPOTONGKAN</strong></td>
									<td align="center"><strong>NILAI MASTER POTONGAN</strong></td>
                				</tr >
							</thead>
							<tbody>
								<?
									$total_potonganpoex=0;
									$total_dendapoex=0;
									$total_sisa=0;

								?>
								<?  $i=0;
									foreach ($poex as $key => $value) {
										$total_potonganpoex += floatval($value['NILAI_TRANSAKSI']);
										$total_dendapoex += floatval($value['JUMLAH']);
										$b=$i+1;
										if(($i % 2) == 0) {	 
											echo "<tr class='row0' id='$rowke' >";
										} else {	
											echo "<tr class='row1'  id='$rowke' >";
										}	
								?>     
								<td align="center"><? echo $b; ?></td>
								<td align="center"><?= $value['BA_NUMBER']  ?></td>
								<td align="center"><?= $value['NUM']  ?></td>
								<td align="center"><?= number_format($value['NILAI_TRANSAKSI'],0,",",".") ?></td>
								<td align="center"><?= number_format($value['JUMLAH'],0,",",".") ?></td>
								</tr>
								<? $i++;} ?>
							</tbody>
							<tfoot>
								<tr class="quote">
									<td colspan="3" align="center"><strong>Total</strong></td>
									<td align="center"><strong><? echo number_format($total_potonganpoex,0,",","."); ?></strong></td>
									<td align="center"><strong><? echo number_format($total_dendapoex,0,",","."); ?></strong></td>
								</tr>
							</tfoot>
						</table>
    					<br />
					</div>
				<?php } ?>
				<?
					$jumlahd = $_POST['jumlahd'];
					$komponen_biaya_ds1 = $_POST['komponen_biaya_ds1'];
					$total_d = 0;

					if (isset($_POST['komponen_biaya_ds1']) and $_POST['komponen_biaya_ds1']!="") {
						$komponen_biaya_ds1 = $_POST['komponen_biaya_ds1'];
						$nama_komponen_ds1 = $_POST['nama_komponen_ds1'];
						$nilaids1 = $_POST['nilaids1'];
						$keterangan_ds1 = $_POST['keterangan_ds1'];
						$pajak_ds1 = $_POST['pajak_ds1'];
						$no_gl_ds1 = $_POST['no_gl_ds1'];
						$total_d+=$nilaids1;
				?>
				<table width="95%" class="adminlist">
					<tr class="quote">
						<td>Komponen Surcharge </td>
						<td align="center"> Nilai Debet </td>
						<td align="center"> Keterangan </td> 
						<td></td>
					</tr>
					<tr>
						<td>
							<input name="komponen_biaya_ds1" type="hidden"  id="komponen_biaya_ds1" value="<?=$komponen_biaya_ds1?>"/>
							<? echo $komponen_biaya_ds1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="nama_komponen_ds1" type="hidden" id="nama_komponen_ds1" value="<?=$nama_komponen_ds1?>"/>
							<? echo $nama_komponen_ds1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="pajak_ds1" type="hidden" id="pajak_ds1" value="<?=$pajak_ds1?>"/>
							<input name="no_gl_ds1" type="hidden" id="no_gl_ds1" value="<?=$no_gl_ds1?>"/>
							<? echo $pajak_ds1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
						</td>
						<td align="center">
							<input type="hidden" value="<?=$nilaids1?>" id="nilaids1" name="nilaids1" />
							<? echo number_format($nilaids1,0,",",".");?>
						</td>
						<td align="center">
							<input name="keterangan_ds1" type="hidden" id="keterangan_ds1" size="50" value="<?=$keterangan_ds1?>"/>&nbsp;&nbsp;
							<? echo $keterangan_ds1;?>
						</td>
						<td width="100"></td>
					</tr>
				<? }
					$jumlahd = $_POST['jumlahd'];
					$komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
					$total_d = 0;

					if (isset($_POST['komponen_biaya_d1']) and $_POST['komponen_biaya_d1']!="") {
						$komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
						$nama_komponen_d1 = $_POST['nama_komponen_d1'];
						$nilaid1 = $_POST['nilaid1'];
						$keterangan_d1 = $_POST['keterangan_d1'];
						$pajak_d1 = $_POST['pajak_d1'];
						$no_gl_d1 = $_POST['no_gl_d1'];
						$total_d+=$nilaid1 + $nilaids1;
				?>
				
					<tr class="quote">
						<td>Komponen Biaya </td>
						<td align="center"> Nilai Debet </td>
						<td align="center"> Keterangan </td> 
						<td> </td>
					</tr>
					<tr>
						<td>
							<input name="komponen_biaya_d1" type="hidden"  id="komponen_biaya_d1" value="<?=$komponen_biaya_d1?>"/>
							<? echo $komponen_biaya_d1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="nama_komponen_d1" type="hidden" id="nama_komponen_d1" value="<?=$nama_komponen_d1?>"/>
							<? echo $nama_komponen_d1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="pajak_d1" type="hidden" id="pajak_d1" value="<?=$pajak_d1?>"/>
							<input name="no_gl_d1" type="hidden" id="no_gl_d1" value="<?=$no_gl_d1?>"/>
							<? echo $pajak_d1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							</td>
						<td align="center">
							<input type="hidden" value="<?=$nilaid1?>" id="nilaid1" name="nilaid1" />
							<? echo number_format($nilaid1,0,",",".");?>
						</td>
						<td align="center">
							<input name="keterangan_d1" type="hidden" id="keterangan_d1" size="50" value="<?=$keterangan_d1?>"/>&nbsp;&nbsp;
							<? echo $keterangan_d1;?>
						</td>
						<td width="100"></td>
					</tr>
					<? 
						for($i = 2; $i<=$jumlahd; $i++) {
							$komp_d = "komponen_biaya_d".$i;
							$nama_komp_d = "nama_komponen_d".$i;
							$komp_nilaid = "nilaid".$i;
							$komp_ketd = "keterangan_d".$i;
							$komp_pajakd = "pajak_d".$i;
							$komp_gld = "no_gl_d".$i;

							$komponen_biaya_d = $_POST[$komp_d];
							$nama_komponen_d = $_POST[$nama_komp_d];
							$nilaid = $_POST[$komp_nilaid];
							$keterangan_d = $_POST[$komp_ketd];
							$pajak_d = $_POST[$komp_pajakd];
							$no_gl_d = $_POST[$komp_gld];

							$total_d+=$nilaid;						
					?>
					<tr>
						<td>
							<input name="<?=$komp_d?>" type="hidden" id="<?=$komp_d?>" value="<?=$komponen_biaya_d?>"/>
							<? echo $komponen_biaya_d;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="<?=$nama_komp_d?>" type="hidden" id="<?=$nama_komp_d?>" value="<?=$nama_komponen_d?>"/>
							<? echo $nama_komponen_d;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="<?=$komp_pajakd?>" type="hidden" id="<?=$komp_pajakd?>" value="<?=$pajak_d?>"/>
							<? echo $pajak_d;?>
							<input name="<?=$komp_gld?>" type="hidden" id="<?=$komp_gld?>" value="<?=$no_gl_d?>"/>
							&nbsp;&nbsp;&nbsp;&nbsp;
						</td>
						<td align="center">
							<input type="hidden" value="<?=$nilaid?>" id="<?=$komp_nilaid?>" name="<?=$komp_nilaid?>" />
							<? echo number_format($nilaid,0,",",".");?>
						</td>
						<td align="center">
							<input name="<?=$komp_ketd?>" type="hidden" id="<?=$komp_ketd?>" size="50" value="<?=$keterangan_d?>"/>&nbsp;&nbsp;
							<? echo $keterangan_d;?>
						</td>
						<td width="100"></td>
					</tr>
					<? }?>
					<tr class="quote">
						<td colspan="2"> TOTAL </td>
						<td><?=number_format($total_d,0,",",".");?></td>
						<td> </td>
					</tr>
				
				<input type="hidden" value="<?=$jumlahd?>" name="jumlahd" id="jumlahd" />
				<br/>
				
				<? }
					$jumlahk = $_POST['jumlahk'];
					$komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
					$total_k = 0;

					if (isset($_POST['komponen_biaya_k1']) and $_POST['komponen_biaya_k1']!="") {
						$komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
						$nama_komponen_k1 = $_POST['nama_komponen_k1'];
						$nilaik1 = $_POST['nilaik1'];
						$keterangan_k1 = $_POST['keterangan_k1'];
						$pajak_k1 = $_POST['pajak_k1'];
						$no_gl_k1 = $_POST['no_gl_k1'];
						$total_k+=$nilaik1;
						
				?>
					<tr class="quote">
						<td>Nama Komponen </td>
						<td align="center"> Nilai Kredit </td>
						<td align="center"> Keterangan </td> 
						<td></td>
					</tr>
					<tr>
						<td>
							<input name="komponen_biaya_k1" type="hidden"  id="komponen_biaya_k1" value="<?=$komponen_biaya_k1?>"/>
							<? echo $komponen_biaya_k1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="nama_komponen_k1" type="hidden" id="nama_komponen_k1" value="<?=$nama_komponen_k1?>"/>
							<? echo $nama_komponen_k1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="pajak_k1" type="hidden" id="pajak_k1" value="<?=$pajak_k1?>"/>
							<input name="no_gl_k1" type="hidden" id="no_gl_k1" value="<?=$no_gl_k1?>"/>
							<? echo $pajak_k1;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
						</td>
						<td align="center">
							<input type="hidden" value="<?=$nilaik1?>" id="nilaik1" name="nilaik1" />
							<? echo number_format($nilaik1,0,",",".");?>
						</td>
						<td align="center">
							<input name="keterangan_k1" type="hidden" id="keterangan_k1" size="50" value="<?=$keterangan_k1?>"/>&nbsp;&nbsp;
							<? echo $keterangan_k1;?>
						</td>
						<td width="100"></td>
					</tr>
					<? for ($i=2;$i<=$jumlahk;$i++) {
						$komp_k = "komponen_biaya_k".$i;
						$nama_komp_k = "nama_komponen_k".$i;
						$komp_nilaik = "nilaik".$i;
						$komp_ketk = "keterangan_k".$i;
						$komp_pajakk = "pajak_k".$i;
						$komp_glk = "no_gl_k".$i;

						$komponen_biaya_k = $_POST[$komp_k];
						$nama_komponen_k = $_POST[$nama_komp_k];
						$nilaik = $_POST[$komp_nilaik];
						$keterangan_k = $_POST[$komp_ketk];
						$pajak_k = $_POST[$komp_pajakk];
						$no_gl_k = $_POST[$komp_glk];
						$total_k+=$nilaik;
					?>
					<tr>
						<td align="left">
							<input name="<?=$komp_k?>" type="hidden" id="<?=$komp_k?>" value="<?=$komponen_biaya_k?>"/>
							<? echo $komponen_biaya_k;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="<?=$nama_komp_k?>" type="hidden" id="<?=$nama_komp_k?>" value="<?=$nama_komponen_k?>"/>
							<? echo $nama_komponen_k;?>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<input name="<?=$komp_pajakk?>" type="hidden" id="<?=$komp_pajakk?>" value="<?=$pajak_k?>"/>
							<? echo $pajak_k;?>
							<input name="<?=$komp_glk?>" type="hidden" id="<?=$komp_glk?>" value="<?=$no_gl_k?>"/>
						</td>
						<td align="left">
							<input type="hidden" value="<?=$nilaik?>" id="<?=$komp_nilaik?>" name="<?=$komp_nilaik?>" />
							<? echo number_format($nilaik,0,",",".");?>
						</td>
						<td align="left">
							<input name="<?=$komp_ketk?>" type="hidden" id="<?=$komp_ketk?>" size="50" value="<?=$keterangan_k?>"/>&nbsp;&nbsp;
							<? echo $keterangan_k;?>
						</td>
						<td width="100"></td>
					</tr>
					<? }?>
					<tr class="quote">
						<td colspan="2"> TOTAL </td>
						<td><?=number_format($total_k,0,",",".");?></td>
						<td> </td>
					</tr>
				</table>
				<input type="hidden" value="<?=$jumlahk?>" name="jumlahk" id="jumlahk" />
				<? } ?>
			<table>
				<tr>
					<td align="center">
						<?
							$jmltahun=count($tahunkrim_v);
							$jmlorg=count($org_v);
							if($jmltahun>1 || $jmlorg>1) {
						?>
						<div class="alert alert-warning">
							<a href="#" class="close" data-dismiss="alert" aria-label="close">Ãƒâ€”</a> 
							<strong>Warning!</strong>Mohon cek Invoice, karena dalam satu invoice tidak boleh ada company dan tahun yang berbeda. 
						</div>
						<? } else {
							if($jmltahun>0) {
								foreach ($tahunkrim_v as $keythn => $value) {
									$tahun=$keythn;
								}
							}
							if($jmlorg>0) {
								foreach ($org_v as $jmlorg => $value) {
									$user_org1b=$jmlorg;
								}
							}
							if($user_org1b=='7000' and $tahun<'2017') {
						?>
						<div class="alert alert-warning">
							<a href="#" class="close" data-dismiss="alert" aria-label="close">Ãƒâ€”</a> 
							<b><strong>Warning!</strong>Invoice PPL dengan tahun pengiriman SPJ dibawah 2017 dan company PT. Semen Indonesia (7000). 
								harus diselesaikan dengan <a href="view_byinvoice.php?no_invoice=<?=$no_invoice_v?>&keterangan_miro=MIGRASIKSO_<?=$keterangan_miro?>&bvtyp=<?=$bvtyp?>&nama_bank=<?=$nama_bank?>&no_rek=<?=$no_rek?>&skbp_v=<?=$skbp_val?>&cabang_bank=<?=$cabang_bank;?>&jumlahk=<?=$jumlahk;?>&komponen_biaya_k1=<?=$komponen_biaya_k1;?>&nama_komponen_k1=<?=$nama_komponen_k1;?>&nilaik1=<?=$nilaik1;?>&keterangan_k1=<?=$keterangan_k1;?>&pajak_k1=<?=$pajak_k1;?>&no_gl_k1=<?=$no_gl_k1;?>&jumlahd=<?=$jumlahd;?>&komponen_biaya_d1=<?=$komponen_biaya_d1;?>&nama_komponen_d1=<?=$nama_komponen_d1;?>&nilaid1=<?=$nilaid1;?>&keterangan_d1=<?=$keterangan_d1;?>&pajak_d1=<?=$pajak_d1;?>&no_gl_d1=<?=$no_gl_d1;?>&tahun=<?=$tahun;?>"  value="<?=$cabang_bank?>" class="button">
								menu baru</a> sebagai berikut.</a> 
							</b>
						</div>
						<? } else if($user_org1b=='7000' and $tahun=='2017') { ?>
						<div class="alert alert-warning">
							<a href="#" class="close" data-dismiss="alert" aria-label="close">Ãƒâ€”</a> 
							<b><strong>Warning!</strong>Invoice PPL dengan tahun pengiriman SPJ 2017 dan company Kerja Sama Operasi SG -SI (7000). 
								harus diselesaikan dengan <a href="view_byinvoice.php?no_invoice=<?=$no_invoice_v?>&keterangan_miro=MIGRASIKSO_<?=$keterangan_miro?>&bvtyp=<?=$bvtyp?>&nama_bank=<?=$nama_bank?>&no_rek=<?=$no_rek?>&skbp_v=<?=$skbp_val?>&cabang_bank=<?=$cabang_bank;?>&jumlahk=<?=$jumlahk;?>&komponen_biaya_k1=<?=$komponen_biaya_k1;?>&nama_komponen_k1=<?=$nama_komponen_k1;?>&nilaik1=<?=$nilaik1;?>&keterangan_k1=<?=$keterangan_k1;?>&pajak_k1=<?=$pajak_k1;?>&no_gl_k1=<?=$no_gl_k1;?>&jumlahd=<?=$jumlahd;?>&komponen_biaya_d1=<?=$komponen_biaya_d1;?>&nama_komponen_d1=<?=$nama_komponen_d1;?>&nilaid1=<?=$nilaid1;?>&keterangan_d1=<?=$keterangan_d1;?>&pajak_d1=<?=$pajak_d1;?>&no_gl_d1=<?=$no_gl_d1;?>&tahun=<?=$tahun;?>"  value="<?=$cabang_bank?>" class="button">
								menu baru</a> sebagai berikut.</a> 
							</b>
						</div>
						<? } else { ?>
							<br/>
							<input type="hidden" value="generate_ppl_verif" name="action" id="action" />
							<input type="submit" value=" GENERATE " name="simpan" class="button" onclick="cek()" id="gener_varify"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<?}}?>
						  <span id="loadingd" name="loadingd" style="display: none"><img src="../images/loading.gif" alt="loding" />Loading...!!</span>
						<a href="list_verifikasi_invoice.php" class="button" id="generate_batal"><? echo "CANCEL"; ?></a>
					</td>
				</tr>
			</table>
		</form>
	</div>
	<?}?>
	<div align="center">
		<? echo $komen;?>
	</div>
	<p>&nbsp;</p>
	<? if ($total> 11){ ?>
		<script type="text/javascript">
			var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
		</script>
	<? } ?>
	<? include ('../include/ekor.php'); ?>
	</body>
</html>
