<?php

session_start();
include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();
include ('../include/etc_fungsi.php');

//require_once ('../include/fpdf/fpdf.php');
//include ('../include/fpdf/html2pdf.php');
require('../include/fpdf/html2pdf.php');
require_once ('../security_helper.php');
sanitize_global_input();

$etcf=new etc_fungsi();
//require_once '../include/oracleDev.php'; 
//$fungsi2=new conntoracleDEVSD();
//$connDEVSD=$fungsi2->DEVSDdb();
$result     = array();
$user_id    =$_SESSION['user_id'];
$user_name  =$_SESSION['user_name'];
$user_org   =$_SESSION['user_org'];
$namauser   =$_SESSION['nama_lengkap'];
$vendor_id  = $_SESSION['vendor_id'];
$aksi       = htmlspecialchars($_REQUEST['act']); 

if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// }


//$nofaktur    = $_REQUEST[nofaktur];
//$idsarray = explode(',', (string)$ids);

$TGL_VALIDFROMtr = htmlspecialchars($_REQUEST['TGL_VALIDFROM']);
$TGL_VALIDTOtr = htmlspecialchars($_REQUEST['TGL_VALIDTO']);
list($year1yy,$month1yy,$day1yy)=split("-",$TGL_VALIDFROMtr);
$TGL_VALIDFROMtr=$year1yy.$month1yy.$day1yy;
list($year2yy,$month2yy,$day2yy)=split("-",$TGL_VALIDTOtr);
$TGL_VALIDTOtr=$year2yy.$month2yy.$day2yy;

//$nm_org  = htmlspecialchars($_REQUEST['nm_org']);
//$nofaktur   = htmlspecialchars($_REQUEST['nofaktur']);
//$po  = htmlspecialchars($_REQUEST['po']);
//$faktur_date  = htmlspecialchars($_REQUEST['faktur_date']);
//$bast_date   = htmlspecialchars($_REQUEST['bast_date']);
//$pic  = htmlspecialchars($_REQUEST['pic']);
//$email   = htmlspecialchars($_REQUEST['email']);
//$dasar_pajak  = htmlspecialchars($_REQUEST['dasar_pajak']);
//$gambar   = htmlspecialchars($_REQUEST['gambar']);

//$file = ($_FILES['file']);//$_FILES['file'];

if(isset ($aksi)){
    
    switch ($aksi){
        
        case 'show' :
        {
					
                    $items = array();
                     $sql= "SELECT DISTINCT EI.*, to_char(EI.TGL_INVOICE,'DD-MM-YYYY') as TGL_INVOICE1, ETH.NO_BA, ETH.TANGGAL_BA, ETH.WARNA_PLAT 
                            FROM EX_INVOICE  EI
                            join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
                            WHERE EI.DELETE_MARK ='0' AND ETH.NO_BA IS NOT NULL AND EI.ORG in ($orgIn) AND EI.NO_INVOICE IS NOT NULL ORDER BY EI.ORG,EI.NO_VENDOR, EI.NO_INVOICE DESC";
					// echo $sql;
					$query= oci_parse($conn, $sql);
					oci_execute($query);
					while($rows=oci_fetch_array($query)){  
						$statuse = '';
						if($rows[TRACKING_INV]=='10'){
							$statuse = 'CREATE INVOICE';
						}
						if($rows[TRACKING_INV]=='30'){
							$statuse = 'REVERSED ';
						}
						if($rows[TRACKING_INV]=='40'){
							$statuse = 'REJECTED';
						}
						if($rows[TRACKING_INV]=='45'){
							$statuse = 'CANCEL PPL & INVOICE';
						}
						if($rows[TRACKING_INV]=='50'){
							$statuse = 'APPROVED BY SPV';
						}
						if($rows[TRACKING_INV]=='60'){
							$statuse = 'GENERATE PPL';
						}
						if($rows[TRACKING_INV]=='70'){
							$statuse = 'SIMULATE & POSTING PPL';
						}
						if($rows[TRACKING_INV]=='80'){
							$statuse = 'REJECT BY MANAJER VERIFIKASI';
						}
						if($rows[TRACKING_INV]=='90'){
							$statuse = 'APPROVED  BY MANAJER VERIFIKASI';
						}
						if($rows[TRACKING_INV]=='100'){
							$statuse = 'REJECT BY SM VERIFIKASI';
						}
						if($rows[TRACKING_INV]=='110'){
							$statuse = 'APPROVED  BY SM VERIFIKASI';
						}
						if($rows[TRACKING_INV]=='120'){
							$statuse = 'EKSPEDISI BENDAHARA';
						} 
						
						$no_invoice_in=  $rows[NO_INVOICE];
						$row[NO_INVOICE]=  $rows[NO_INVOICE];
						$row[TGL_INVOICE]=  $rows[TGL_INVOICE];
						$row[INV_DOC_NUMBER]=  $rows[INV_DOC_NUMBER];
						$row[INV_DOC_NUMBER_CONV]=  $rows[INV_DOC_NUMBER_CONV];
						$row[NO_BA]=  $rows[NO_BA];
						$row[TANGGAL_BA]=  $rows[TANGGAL_BA];
						$row[NAMA_VENDOR]=  $rows[NAMA_VENDOR];
						$row[WARNA_PLAT]=  $rows[WARNA_PLAT];
						$row[NO_PAJAK_EX]=  $rows[NO_PAJAK_EX];
						$row[STATUS_NAME]=  $statuse; 
						$row[STATUSE]=  $rows[TRACKING_INV];  
						$row[ACCOUNTING_DOC]=  $rows[ACCOUNTING_DOC];
                        if ($row[ACCOUNTING_DOC] == '') {
                           $row[ACCOUNTING_DOC] = '-';
                        }
                        $row[KETERANGAN_EKSPEDISI]=substr($rows[KETERANGAN_EKSPEDISI],14,10);
                        if ($row[KETERANGAN_EKSPEDISI] == false) {
                           $row[KETERANGAN_EKSPEDISI] = '-';
                        }
						
						 $row[DISPLAY_BT]=   "-";
						$row[CETAK_BT]=  "-";
						 
						
						$sqlcek="SELECT KOMENTAR_REJECT, STATUS_BA_INVOICE,  NAMA, NAMA_LENGKAP from EX_BA_INVOICE 
                JOIN TB_USER_BOOKING TUB ON TUB.ID = EX_BA_INVOICE.CREATED_BY
                where EX_BA_INVOICE.ID =  (SELECT max(ID) from EX_BA_INVOICE where NO_INVOICE = '$no_invoice_in' )";
						$querycek= oci_parse($conn, $sqlcek);
						oci_execute($querycek);
						$row_data=oci_fetch_assoc($querycek); 
						$row[NAMA_LENGKAP]=  $row_data[NAMA_LENGKAP];

                        $sqlcek2="SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE  NO_INVOICE = '$no_invoice_in' AND DELETE_MARK = '0' ";
                            $querycek2= oci_parse($conn, $sqlcek2);
                            oci_execute($querycek2);
                            $row_data2=oci_fetch_assoc($querycek2);
							$row[TOTAL_KLAIM]=number_format($row_data2[TOTAL_KLAIM],2,",",".");
						array_push($items, $row);  
					}
					
               
                        $result["rows"] = $items;
                        $result["total"] = count($items);
                        $result["footer"]=array(array("TOT" => "Total","TOT" => ""));
                        
                        // print_r($items);exit;
                        echo json_encode($result);
        }
        break;
        case 'add' :
        {
            
//            if(move_uploaded_file($file["tmp_name"], $target_file)){
//                $i = 1;
//            } else {
//                $i = 'gagal';
//            }
//            echo json_encode("org = ".$nm_org."nofaktur = ".$nofaktur." email = ".$email." pic = ".$pic." faktur_date = ".$faktur_date." bast_date = ".$bast_date." dasar_pajak = ".$dasar_pajak." po = ".$po." gambar = ".$gambar." jum = ".$jum);

            try
                {
//                    $jum  = htmlspecialchars($_REQUEST['tot']);
                    $array_all['nofaktur'] = array_map('trim', explode(",", $nofaktur));
                    $array_all['nm_org'] = array_map('trim', explode(",", $nm_org));
                    $array_all['email'] = array_map('trim', explode(",", $email));
                    $array_all['pic'] = array_map('trim', explode(",", $pic));
                    $array_all['faktur_date'] = array_map('trim', explode(",", $faktur_date));
                    $array_all['bast_date'] = array_map('trim', explode(",", $bast_date));
                    $array_all['dasar_pajak'] = array_map('trim', explode(",", $dasar_pajak));
                    $array_all['po'] = array_map('trim', explode(",", $po));
                    $array_all['gambar'] = array_map('trim', explode(",", $gambar));
                    
                    
                    //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; 
                    $sap = new SAPConnection();
                    $sap->Connect("../include/sapclasses/logon_data2.conf");
                    //$sap->Connect($link_koneksi_sap);
            
                    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                    if ($sap->GetStatus() != SAPRFC_OK ) {
                        echo $sap->PrintStatus();
                        exit;
                    }

                    if($sap) {
                        $fce = &$sap->NewFunction ("Z_ZCFI3049_EKSP_FP");
                        if ($fce == false ) { $sap->PrintStatus(); exit; }
                    }
                    
                    if($jum > 0){
                        
                        $fce->I_BUKRS = $array_all['nm_org'][0];
                        $fce->I_LIFNR = $vendor_id;
                        $fce->I_EMAIL = $array_all['email'][0];
                        $fce->I_PERSON = $array_all['pic'][0];
                                                
                        for($i=0; $i<$jum; $i++){
                            
                            $fdate = explode('-',$array_all['faktur_date'][$i]);
                            $fakdate = $fdate[2] . $fdate[1] . $fdate[0];

                            $bdate = explode('-',$array_all['bast_date'][$i]);
                            $bastdate = $bdate[2] . $bdate[1] . $bdate[0];

                            $fce->T_INPUT->row["XBLNR"] = $array_all['nofaktur'][$i];
                            $fce->T_INPUT->row["BLDAT"] = $fakdate;
                            $fce->T_INPUT->row["BEDAT"] = $bastdate; 
                            $fce->T_INPUT->row["HWBAS"] = $array_all['dasar_pajak'][$i];
                            $fce->T_INPUT->row["EBELN"] = $array_all['po'][$i];    

                            $fce->T_INPUT->row["LFILE"] = $array_all['gambar'][$i];

                            $fce->T_INPUT->Append($fce->T_INPUT->row);
                        }
                    }
                       
                    $fce->call();
                    $items = array();
                    if ($fce->GetStatus() == SAPRFC_OK) {
                        $items['pesan']=$fce->E_MESSAGE;
                        $items['noeks']=$fce->E_EKSPNO;
                        $fce->T_OUTPUT->Reset();
                        while ( $fce->T_OUTPUT->Next() ){
                            $items['output'][] = $fce->T_OUTPUT->row;
                        }
                    } else {
                        $fce->PrintStatus();
                    }
                            
                    $fce->Close();
                    $sap->Close();
                            
                    if ($items['pesan']['TYPE']==='S'){
                        echo json_encode(array('success'=>true));;
                    } else {
                        echo json_encode($items['pesan']);                        
                    }
                }
            catch(Exception $e) // an exception is raised if a query fails will be raised
                {
                    $this->report->Text = $e->getMessage();
                }
        }
        break;
        case 'delete' :
        {
            try {
                    //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; 
                    $sap = new SAPConnection();
                    $sap->Connect("../include/sapclasses/logon_data2.conf");
                    //$sap->Connect($link_koneksi_sap);
            
                    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                    if ($sap->GetStatus() != SAPRFC_OK ) {
                        echo $sap->PrintStatus();
                        exit;
                    }

                    if($sap) {
                        $fce = &$sap->NewFunction ("Z_ZCFI3049_EKSP_FP_TRIM");
                        if ($fce == false ) { $sap->PrintStatus(); exit; }
                    }
                    
//                    $array_all['company'] = array_map('trim', explode(",", $company));
//                    $array_all['noeks'] = array_map('trim', explode(",", $noeks));
//                    $array_all['nofaktur'] = array_map('trim', explode(",", $nofaktur));
//                    $maks = count($array_all['noeks']);
//                    $salah = 0;
                    
//                    for($i=0;$i<$maks;$i++){
                        
                    $fp = str_replace(".", "", $nofaktur);
                    $fp = str_replace("-", "", $fp);
                    
                        $fce->I_BUKRS = $company;//'2000';
                        $fce->I_EKSPID = $noeks;//$array_all['noeks'][$i];
                        $fce->I_XBLNR = $fp;//$array_all['nofaktur'][$i];
                        $fce->I_UNAME = $vendor_id;
                        $fce->I_BATAL = 'X';

                        $fce->call();
                        $items = array();
                        if ($fce->GetStatus() == SAPRFC_OK) {
                            $items['pesan']=$fce->E_MESSAGE; 
                        } else {
                            $fce->PrintStatus();
                        }
                        $fce->Close();
                        $sap->Close();
                        
//                    }
                            
                    if ($items['pesan']['TYPE']==='S'){
                        echo json_encode(array('success'=>true));
                        echo json_encode($noeks." ".$nofaktur." ".$company);
//                        echo json_encode($maks." noeks =".print_r($array_all['noeks'])."nofaktur =".print_r($array_all['nofaktur']));
                    } else {
                        echo json_encode($items['pesan']);
                        echo json_encode($noeks." ".$nofaktur." ".$company);
//                        echo json_encode($maks." noeks =".print_r($array_all['noeks'])."nofaktur =".print_r($array_all['nofaktur']));
                    }

                }  
                
            catch(Exception $e) // an exception is raised if a query fails will be raised
                {
                    $this->report->Text = $e->getMessage();
                }
        }
        break;
        case 'upload' :
        {       
            $tgl = date("Ymd");
            $target_dir = "../upload_faktur_pajak/";
            $acak = substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, 6);
            
            $gambar = $_FILES['upload'];
            $namatemp = explode(".",$gambar["name"]);           
            $namatemp1 = str_replace(' ', '', $namatemp[0]);
            $target_file = $target_dir.$tgl."-".$namatemp1.$acak.".".$namatemp[1];
//            $tes    = dirname(__FILE__);
//                $tgl = date("Ymd");
//                $target_dir = "../upload_data/upload_file_distr/";
//                $acak = substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, 6);
//                $namatemp = explode(".",$file["name"]);
//                $namatemp1 = str_replace(' ', '', $namatemp[0]);
//                $target_file = $target_dir.$tgl."-".$namatemp1.$acak.".".$namatemp[1];
                
                $link = 'http://10.15.5.150/dev/sd/sdonline/upload_faktur_pajak/'.$tgl."-".$namatemp1.$acak.".".$namatemp[1];
                
                if($namatemp[1]=="jpg" || $namatemp[1]=="jpeg" || $namatemp[1]=="png" || $namatemp[1]=="pdf" || $namatemp[1]=="PDF"){
                    if(move_uploaded_file($gambar["tmp_name"], $target_file)){
    //                    echo json_encode('berhasil'.$target_file);
                        echo json_encode(array('success'=>true,'pesan'=>'Faktur Pajak BERHASIL Diupload', 'gambar'=>$link));
                    } else {
    //                    echo json_encode('tidak sukses'.$target_file);
                        echo json_encode(array('success'=>false, 'pesan'=>'Faktur Pajak GAGAL Di Upload', 'gambar'=>''));
                    };
                } else {
                    echo json_encode(array('success'=>false, 'pesan'=>'File yang support hanya jpg, jpeg, png, pdf, doc, dan docx', 'gambar'=>''));
                }
        }
        break;
    }
}

function toDate($tgl, $jam) {
    $disply_tgl = substr($tgl, 6, 2) . '-' . substr($tgl, 4, 2) . '-' . substr($tgl, 0, 4);
    $disply_jam = substr($jam, 0, 2) . ':' . substr($jam, 2, 2) . ':' . substr($jam, 4, 2);
    if ($tgl != '')
        $display = $disply_tgl;
    if ($jam != '')
        $display .= ' ' . $disply_jam;
    return $display;
}

?>
