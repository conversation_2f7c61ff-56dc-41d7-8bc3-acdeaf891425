<?php
/**
 * SQL Injection Testing and Secure Implementation Demo
 * This file demonstrates both vulnerable and secure approaches
 */

session_start();
include ('include/ex_fungsi.php');

// Demo class for secure database operations
class SecureDB {
    private $conn;
    
    public function __construct() {
        $fungsi = new ex_fungsi();
        $this->conn = $fungsi->ex_koneksi();
    }
    
    /**
     * VULNERABLE EXAMPLE (DO NOT USE IN PRODUCTION)
     * Shows how the original code is vulnerable to SQL injection
     */
    public function vulnerableQuery($no_ba, $vendor, $tanggal_mulai, $tanggal_selesai) {
        echo "<h3>VULNERABLE QUERY (for testing purposes only):</h3>";
        
        // This is vulnerable - direct string concatenation
        $sql = "SELECT * FROM EX_BA WHERE 1=1";
        
        if (!empty($vendor)) {
            $sql .= " AND NO_VENDOR LIKE '$vendor'";
        }
        
        if (!empty($no_ba)) {
            $sql .= " AND NO_BA LIKE '$no_ba'";
        }
        
        if (!empty($tanggal_mulai) && !empty($tanggal_selesai)) {
            $sql .= " AND TGL_BA BETWEEN TO_DATE('$tanggal_mulai', 'DD-MM-YYYY') AND TO_DATE('$tanggal_selesai', 'DD-MM-YYYY')";
        }
        
        echo "<pre>SQL: " . htmlspecialchars($sql) . "</pre>";
        
        // Show potential injection
        echo "<p><strong>Injection Example:</strong> If no_ba = \"TEST' OR '1'='1\"</p>";
        $injected_sql = str_replace("'$no_ba'", "'TEST' OR '1'='1'", $sql);
        echo "<pre>Injected SQL: " . htmlspecialchars($injected_sql) . "</pre>";
        
        return false; // Don't actually execute vulnerable query
    }
    
    /**
     * SECURE EXAMPLE (USE THIS APPROACH)
     * Shows proper prepared statement implementation
     */
    public function secureQuery($no_ba, $vendor, $tanggal_mulai, $tanggal_selesai) {
        echo "<h3>SECURE QUERY (recommended approach):</h3>";
        
        // Base query with placeholders
        $sql = "SELECT ORG, NO_BA, NO_VENDOR, NAMA_VENDOR, TGL_BA, STATUS_BA 
                FROM EX_BA 
                WHERE DELETE_MARK = '0'";
        
        $conditions = array();
        $bind_params = array();
        
        // Add conditions with parameter binding
        if (!empty($vendor)) {
            $conditions[] = "NO_VENDOR LIKE :vendor";
            $bind_params[':vendor'] = $vendor;
        }
        
        if (!empty($no_ba)) {
            $conditions[] = "NO_BA LIKE :no_ba";
            $bind_params[':no_ba'] = $no_ba;
        }
        
        if (!empty($tanggal_mulai) && !empty($tanggal_selesai)) {
            $conditions[] = "TGL_BA BETWEEN TO_DATE(:tgl_mulai, 'DD-MM-YYYY') AND TO_DATE(:tgl_selesai, 'DD-MM-YYYY')";
            $bind_params[':tgl_mulai'] = $tanggal_mulai;
            $bind_params[':tgl_selesai'] = $tanggal_selesai;
        }
        
        // Append conditions
        if (!empty($conditions)) {
            $sql .= " AND " . implode(" AND ", $conditions);
        }
        
        $sql .= " ORDER BY NO_BA DESC";
        
        echo "<pre>SQL: " . htmlspecialchars($sql) . "</pre>";
        echo "<p><strong>Parameters:</strong></p>";
        foreach ($bind_params as $param => $value) {
            echo "<pre>$param => " . htmlspecialchars($value) . "</pre>";
        }
        
        // Execute prepared statement
        try {
            $query = oci_parse($this->conn, $sql);
            
            if (!$query) {
                throw new Exception('Failed to prepare statement');
            }
            
            // Bind parameters
            foreach ($bind_params as $param => $value) {
                if (!oci_bind_by_name($query, $param, $bind_params[$param])) {
                    throw new Exception('Failed to bind parameter: ' . $param);
                }
            }
            
            // Execute query
            if (!oci_execute($query)) {
                throw new Exception('Failed to execute query');
            }
            
            echo "<p><strong>Query executed successfully!</strong></p>";
            
            // Fetch and display results (limit to 5 for demo)
            $results = array();
            $count = 0;
            while (($row = oci_fetch_assoc($query)) && $count < 5) {
                $results[] = $row;
                $count++;
            }
            
            if (!empty($results)) {
                echo "<h4>Sample Results (max 5 rows):</h4>";
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ORG</th><th>NO_BA</th><th>NO_VENDOR</th><th>NAMA_VENDOR</th><th>TGL_BA</th><th>STATUS_BA</th></tr>";
                
                foreach ($results as $row) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['ORG']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['NO_BA']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['NO_VENDOR']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['NAMA_VENDOR']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['TGL_BA']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['STATUS_BA']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No results found.</p>";
            }
            
            return $results;
            
        } catch (Exception $e) {
            echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            return false;
        }
    }
    
    /**
     * Input validation examples
     */
    public function validateInput($input, $type) {
        echo "<h4>Input Validation for: " . htmlspecialchars($input) . "</h4>";
        
        switch ($type) {
            case 'ba_number':
                $valid = preg_match('/^[A-Z0-9\-_\/]+$/i', $input);
                echo "<p>BA Number validation: " . ($valid ? "VALID" : "INVALID") . "</p>";
                return $valid;
                
            case 'date':
                $valid = preg_match('/^(\d{2})-(\d{2})-(\d{4})$/', $input, $matches);
                if ($valid) {
                    $day = (int)$matches[1];
                    $month = (int)$matches[2];
                    $year = (int)$matches[3];
                    $valid = checkdate($month, $day, $year);
                }
                echo "<p>Date validation: " . ($valid ? "VALID" : "INVALID") . "</p>";
                return $valid;
                
            case 'vendor':
                $valid = preg_match('/^\d{10}$/', $input);
                echo "<p>Vendor validation: " . ($valid ? "VALID" : "INVALID") . "</p>";
                return $valid;
                
            default:
                echo "<p>Unknown validation type</p>";
                return false;
        }
    }
}

// Demo usage
?>
<!DOCTYPE html>
<html>
<head>
    <title>SQL Injection Testing & Secure Implementation Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .vulnerable { background-color: #ffe6e6; border-color: #ff6666; }
        .secure { background-color: #e6ffe6; border-color: #66ff66; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SQL Injection Security Testing & Secure Implementation Demo</h1>
        
        <div class="warning">
            <strong>WARNING:</strong> This file is for educational and testing purposes only. 
            The vulnerable examples should NEVER be used in production code.
        </div>
        
        <?php
        // Test data
        $test_cases = array(
            array(
                'name' => 'Normal Input',
                'no_ba' => 'BA2023001',
                'vendor' => '1234567890',
                'tanggal_mulai' => '01-01-2023',
                'tanggal_selesai' => '31-12-2023'
            ),
            array(
                'name' => 'SQL Injection Attempt',
                'no_ba' => "TEST' OR '1'='1",
                'vendor' => "'; DROP TABLE EX_BA; --",
                'tanggal_mulai' => "01-01-2023' AND 1=1 AND '1'='1",
                'tanggal_selesai' => '31-12-2023'
            ),
            array(
                'name' => 'XSS Attempt',
                'no_ba' => '<script>alert("XSS")</script>',
                'vendor' => '"><script>alert("XSS")</script>',
                'tanggal_mulai' => '01-01-2023',
                'tanggal_selesai' => '31-12-2023'
            )
        );
        
        $secureDB = new SecureDB();
        
        foreach ($test_cases as $test) {
            echo "<div class='section'>";
            echo "<h2>Test Case: " . htmlspecialchars($test['name']) . "</h2>";
            
            echo "<h3>Test Input:</h3>";
            echo "<pre>";
            echo "no_ba: " . htmlspecialchars($test['no_ba']) . "\n";
            echo "vendor: " . htmlspecialchars($test['vendor']) . "\n";
            echo "tanggal_mulai: " . htmlspecialchars($test['tanggal_mulai']) . "\n";
            echo "tanggal_selesai: " . htmlspecialchars($test['tanggal_selesai']) . "\n";
            echo "</pre>";
            
            // Input validation
            echo "<h3>Input Validation Results:</h3>";
            $secureDB->validateInput($test['no_ba'], 'ba_number');
            $secureDB->validateInput($test['vendor'], 'vendor');
            $secureDB->validateInput($test['tanggal_mulai'], 'date');
            $secureDB->validateInput($test['tanggal_selesai'], 'date');
            
            // Vulnerable approach (demo only)
            echo "<div class='vulnerable'>";
            $secureDB->vulnerableQuery($test['no_ba'], $test['vendor'], $test['tanggal_mulai'], $test['tanggal_selesai']);
            echo "</div>";
            
            // Secure approach
            echo "<div class='secure'>";
            $secureDB->secureQuery($test['no_ba'], $test['vendor'], $test['tanggal_mulai'], $test['tanggal_selesai']);
            echo "</div>";
            
            echo "</div><hr>";
        }
        ?>
        
        <div class="section">
            <h2>Penetration Testing Commands</h2>
            
            <h3>Manual Testing with cURL:</h3>
            <pre>
# Test basic SQL injection
curl -X POST "http://localhost/csms/ex_invoice_ba_sp/list_ba.php" \
  -d "no_ba=TEST' OR '1'='1&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&cari=Find"

# Test UNION injection
curl -X POST "http://localhost/csms/ex_invoice_ba_sp/list_ba.php" \
  -d "no_ba=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18 FROM DUAL--&cari=Find"

# Test time-based blind injection
curl -X POST "http://localhost/csms/ex_invoice_ba_sp/list_ba.php" \
  -d "vendor=' AND (SELECT COUNT(*) FROM ALL_TABLES WHERE ROWNUM <= 10000) > 0 AND '1'='1&cari=Find"
            </pre>
            
            <h3>Automated Testing with SQLMap:</h3>
            <pre>
# Basic sqlmap test
sqlmap -u "http://localhost/csms/ex_invoice_ba_sp/list_ba.php" \
  --data="no_ba=TEST&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&cari=Find" \
  --batch --dbs

# Test with session cookies
sqlmap -u "http://localhost/csms/ex_invoice_ba_sp/list_ba.php" \
  --data="no_ba=TEST&tanggal_mulai=01-01-2023&tanggal_selesai=31-12-2023&cari=Find" \
  --cookie="PHPSESSID=your_session_id" \
  --batch --tables
            </pre>
        </div>
        
        <div class="section">
            <h2>Secure Implementation Checklist</h2>
            <ul>
                <li>✅ Use prepared statements with parameter binding</li>
                <li>✅ Validate all input data</li>
                <li>✅ Sanitize output with htmlspecialchars()</li>
                <li>✅ Implement proper error handling</li>
                <li>✅ Add CSRF protection</li>
                <li>✅ Implement rate limiting</li>
                <li>✅ Log security events</li>
                <li>✅ Use least privilege database access</li>
                <li>✅ Encrypt sensitive data</li>
                <li>✅ Regular security testing</li>
            </ul>
        </div>
    </div>
</body>
</html>