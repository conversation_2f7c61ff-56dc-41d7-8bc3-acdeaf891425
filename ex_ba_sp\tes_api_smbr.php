<?php
require_once 'api/smbr.php';

$cookieFile = '';
$ship_to = array();
$tipe = $_GET['tipe'];
$param = array(
                "lr_exti1" => array(
                    "sign"   => "I",
                    "option" => "EQ",
                    "low"    => $_GET['no_shipment'],
                    "high"   => ""
                ),
                "lr_kunnr" => array(
                    "sign"   => "I",
                    "option" => "EQ",
                    "low"    => $_GET['sold_to'],
                    "high"   => ""
                )
            );
$api = new ApiSmbr();

switch($tipe){
    case 'get_token':
        
        $response = $api->getToken($cookieFile);
        var_dump($response);
        
        break;
    case 'get_shipment_cost':
        $response = $api->getShipmentCost($param);
        if(!$response['success']){
            echo $response['msg'];
        }else{
            echo json_encode($response['data']);
        }
        break;

    default:
    echo 'tipe tidak dikenali';
}
?>