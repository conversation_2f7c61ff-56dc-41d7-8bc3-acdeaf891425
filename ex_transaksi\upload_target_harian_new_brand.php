<?
/*  Created by Dio
 * Upload target harian new brand 
 * 04 april 2024
 */

session_start();
include('../include/ex_fungsi.php');
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

//Format Nilai
function showNilai2($nilai)
{
    if ($nilai > 0) return number_format($nilai, 2);
    else return '0';
}

//$hakakses=array("admin");
//$halaman_id=1491;
$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->getmainhalam_id($conn, $dirr);
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'];
$user_org = $_SESSION['user_org'];

$page = 'upload_target_harian_new_brand.php';

$com = $fungsi->getComin($conn, $user_org);
$orgcounter = 0;
$com1 = array();
if (count($com)) {
    foreach ($com as $keyOrg => $valorgm) {
        $com1[$orgcounter] = $keyOrg;
        $orgcounter++;
    }
}



// if ($fungsi->keamanan($halaman_id, $user_id) == 0) {

// ?>
//     <SCRIPT LANGUAGE="JavaScript">
//         alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
//     </SCRIPT>

//     <a href="../index.php">Login....</a>
// <?

//     exit();
// }
function koneksi($conn)
{
    include('../include/ex_fungsi.php');
    $fungsi = new ex_fungsi();

    return $conn;
}

function updatedata($conn, $data, $arrbrand)
{
    $user_id = $_SESSION['user_id'];
    // $data['TGL_TARGET']  = date("d/m/Y", strtotime($data['TGL_TARGET'])); // Update Thamrin

    if (intval($data['TARGET']) == 0 && intval($data['PERSENTASE']) == 0) {
        $sql1 = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND 
                SET DEL_MARK = '1', 
                    LASTUPDATE_BY = '$user_id' , 
                    LASTUPDATE_DATE = sysdate
                WHERE BRAND = '" . $data['BRAND'] . "'
                    AND DISTRIK = '" . $data['DISTRIK'] . "' 
                    AND DISTRIBUTOR = '" . $data['DISTRIBUTOR'] . "'
                    AND to_char(TANGGAL_TARGET, 'YYYYMM') ='" . $data['TGL_BULAN'] . "'";
        $query1 = oci_parse($conn, $sql1);
        $upd = @oci_execute($query1);
    }else {
        if (in_array($data['BRAND'], $arrbrand) && $data['TIPE'] == '121-301') { // VERSI TARGET HARIAN
            $sql1 = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND 
                    SET TARGET = " . $data['TARGET'] . ", 
                        STATUS_BRAND = '" . $data['STATUS_BRAND'] . "',
                        PERSENTASE = " . $data['PERSENTASE'] . ",
                        LASTUPDATE_BY = '$user_id' , 
                        LASTUPDATE_DATE = sysdate
                    WHERE BRAND = '" . $data['BRAND'] . "'
                        AND DISTRIK = '" . $data['DISTRIK'] . "' 
                        AND DISTRIBUTOR = '" . $data['DISTRIBUTOR'] . "'
                        AND to_char(TANGGAL_TARGET, 'YYYYMM') ='" . $data['TGL_BULAN'] . "'";
            // AND to_char(tanggal_target, 'DD/MM/YYYY') ='" . $data['TGL_TARGET'] . "'";
            // AND tipe = '" . $data['TIPE'] . "'
    
            $query1 = oci_parse($conn, $sql1);
            $upd = @oci_execute($query1);
        } else {
            // VERSI TARGET BULANAN
            if ($data['TIPE'] == '121-301' || $data['TIPE'] == '121-701') {
                $sql1 = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND 
                        SET TARGET = " . $data['TARGET'] . ", 
                            STATUS_BRAND = '" . $data['STATUS_BRAND'] . "',
                            PERSENTASE = " . $data['PERSENTASE'] . ",
                            LASTUPDATE_BY = '$user_id' , 
                            LASTUPDATE_DATE = sysdate
                        WHERE BRAND = '" . $data['BRAND'] . "'
                            AND DISTRIK = '" . $data['DISTRIK'] . "' 
                            AND DISTRIBUTOR = '" . $data['DISTRIBUTOR'] . "'
                            AND to_char(TANGGAL_TARGET, 'YYYYMM') ='" . $data['TGL_BULAN'] . "'";
            } else {
                $sql1 = "UPDATE ZSD_TARGET_HARIAN_NEW_BRAND 
                            SET TARGET = " . $data['TARGET'] . ", 
                                STATUS_BRAND = '" . $data['STATUS_BRAND'] . "',
                                PERSENTASE = " . $data['PERSENTASE'] . ",
                                LASTUPDATE_BY = '$user_id' , 
                                LASTUPDATE_DATE = sysdate
                            WHERE BRAND = '" . $data['BRAND'] . "'
                                AND DISTRIK = '" . $data['DISTRIK'] . "' 
                                AND DISTRIBUTOR = '" . $data['DISTRIBUTOR'] . "'
                                AND to_char(TANGGAL_TARGET, 'YYYYMM') ='" . $data['TGL_BULAN'] . "'";
            }
    
            $query1 = oci_parse($conn, $sql1);
            $upd = @oci_execute($query1);
        }
    }

    // echo '<pre>';
    // echo $sql1;

    return $upd;
}

function InsertData($conn, $data, $arrbrand)
{

    $user_id = $_SESSION['user_id'];
    $user_org = $_SESSION['user_org'];

    // $data['TGL_TARGET']  = date("d/m/Y", strtotime($data['TGL_TARGET'])); // Update Thamrin

    if ($data['TIPE'] == '121-301') {
        $sql2 = "INSERT INTO ZSD_TARGET_HARIAN_NEW_BRAND (org, brand, status_brand, distrik, distributor, tanggal_target, target,  
                    persentase, tipe, create_by, create_date) 
                    values ('$user_org', '" . $data['BRAND'] . "', '" . $data['STATUS_BRAND'] . "', '" . $data['DISTRIK'] . "', '" . $data['DISTRIBUTOR'] . "', 
                    to_date('" . $data['TGL_TARGET'] . "', 'dd/mm/yyyy') , '" . $data['TARGET'] . "', '" . $data['PERSENTASE'] . "', '" . $data['TIPE'] . "',
                    '$user_id', sysdate)";

        $query2 = oci_parse($conn, $sql2);
        $ins = @oci_execute($query2);
    } else {
        $sql2 = "INSERT INTO ZSD_TARGET_HARIAN_NEW_BRAND (org, brand, status_brand, distrik, distributor, tanggal_target, target,  
                    persentase, tipe, create_by, create_date)
                    values ('$user_org', '" . $data['BRAND'] . "', '" . $data['STATUS_BRAND'] . "', '" . $data['DISTRIK'] . "', '" . $data['DISTRIBUTOR'] . "', to_date('" . $data['TGL_TARGET'] . "', 'dd/mm/yyyy') ,
                    '" . $data['TARGET'] . "', '" . $data['PERSENTASE'] . "', '" . $data['TIPE'] . "', '$user_id', sysdate)";

        $query2 = oci_parse($conn, $sql2);
        $ins = @oci_execute($query2);
    }
    // echo "<pre>";
    // echo $sql2;
    return $ins;
}
function CekSelectData($conn, $data, $action = "", $arrbrand)
{
    // var_dump($data);
    // $data['TGL_TARGET']  = date("d/m/Y", strtotime($data['TGL_TARGET']));
    // var_dump($data['TGL_TARGET']);
    // die();
    $sql_select = "SELECT * from ZSD_TARGET_HARIAN_NEW_BRAND
                WHERE BRAND = '" . $data['BRAND'] . "' 
                AND distrik = '" . $data['DISTRIK'] . "' 
                AND distributor = '" . $data['DISTRIBUTOR'] . "' 
                AND tipe = '" . $data['TIPE'] . "' 
                AND to_char(tanggal_target, 'YYYYMM') ='" . $data['TGL_BULAN'] . "'
                AND DEL_MARK = '0' ";

    // var_dump($sql_select);
    // die();
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    //$res = oci_fetch_row($query);  
    while ($row = oci_fetch_array($query)) {
        $arData[] = $row;
    }
    // echo "<br>" . print_r($arData);
    return $arData;
}

function dateFormatted($dateString, $format = 'Ymd')
{
    $dateParts = explode('/', $dateString);
    if (count($dateParts) !== 3) {
        return false;
    }

    list($day, $month, $year) = $dateParts;
    $timestamp = mktime(0, 0, 0, $month, $day, $year);
    $formattedDate = date($format, $timestamp);

    return $formattedDate;
}

function update_TargetHarian($data_trgt, $arrbrand)
{
    global $com1;
    $fail = 0;
    $fail_ = 0;

    $messPlafArr = array();

    $fungsi = new ex_fungsi();
    $conn = $fungsi->ex_koneksi();

    $koneksi_sap = "../include/sapclasses/logon_data.conf";
    $sap = new SAPConnection();
    $sap->Connect($koneksi_sap);

    if ($sap->GetStatus() == SAPRFC_OK) $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        // echo $sap->PrintStatus();
        exit;
    }

    $org = $_SESSION['user_org'];//2000;//
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['user_name'];
    $insert = 0;
    $update = 0;
    $baris = 1;

    //$tipe = (trim(strtoupper($data_trgt[TIPE])) == "CURAH") ? "121-302" : "121-301";
    /*
        // menambahkan pendefinisian untuk tipe MORTAR ZAK dan MORTAR ZAK BULANAN
        */
    if (trim(strtoupper($data_trgt[TIPE])) == "CURAH") {
        $tipe = '121-302';
    } else if (trim(strtoupper(substr($data_trgt[TIPE], 0, 6))) == "MORTAR") {
        //penambahan pengujian material CURAH/ZAK
        if (trim(strtoupper(substr($data_trgt[TIPE], 7, 3))) != "ZAK") {
            $tipe = '121-702';
        } else {
            $tipe = '121-701';
        }
    } else {
        $tipe = '121-301';
    }

    $grouping_target = array_reduce($data_trgt, "reduce_target", array());

    $_cek_persentase_msg = "";
    foreach ($grouping_target as $_distributor => $_value) {
        foreach ($_value as $_distrik => $_persentase) {
            if (floatval($_persentase) > floatval(100)) {
                $_cek_persentase_msg .= "<br>Target distributor " . $_distributor . " di distrik " . $_distrik . " lebih dari 100 persen";
            }
        }
    }

    if ($_cek_persentase_msg != "") {
        return $_cek_persentase_msg;
    }

    $sqlSodlto = "SELECT
                    SOLD_TO 
                FROM
                    MAPPING_SOLDTO_PLAFON
                WHERE
                    DEL_MARK = '0'
                ORDER BY
                    SOLD_TO";

    $querySoldto = oci_parse($conn, $sqlSodlto);
    oci_execute($querySoldto);
    
    $cekSoldto = array();
    $i=0;
    while($rowSoldto=oci_fetch_array($querySoldto)){
        $cekSoldto[$i] = $rowSoldto['SOLD_TO'];
        $i++;
    }

    $errorOra = "";
    foreach ($data_trgt as $key => $value) {


        //jika ada yg kosong
        if (trim($value[2]) != "" && trim($value[3]) != "") {
            if (trim($value[2]) != "") {
                // $org = trim($value[2]);
                $brandx = trim($value[2]);
                $distrik = trim($value[3]);

                if (!is_numeric($distrik)) {
                    $errorOra .= "<br> Gagal pada baris ".($key - 4).": Distrik harus berupa angka";
                    $messPlafArr[] = array(
                        "baris" => $key - 4,
                        "issue_type" => "district is not number",
                        "message" => "<br> Gagal pada baris ".($key - 4).": Distrik harus berupa angka",
                    );
                    $baris++;
                    continue; // Abaikan entri ini jika $distrik bukan angka
                }

                $sql_brand = "SELECT
                    COUNT(BRAND) AS JUMLAH
                FROM
                    MASTER_BRAND mb
                WHERE
                    BRAND = '$brandx'
                    AND DEL_MARK != 'Y' ";

                  // echo '<pre>';echo $grouping;
                $query_brand = oci_parse($conn, $sql_brand);
                oci_execute($query_brand);
                while ($row_brand = oci_fetch_array($query_brand))
                {   
                    $cek_brand['JUMLAH']= $row_brand['JUMLAH'];
                }
                
                
                if ($cek_brand['JUMLAH'] == 0) {
                    $errorOra .= "<br> Gagal pada baris ".($key - 4).": Brand ".$brandx." tidak tersedia";
                    $messPlafArr[] = array(
                        "baris" => $key - 4,
                        "issue_type" => "brand not available",
                        "message" => "<br> Gagal pada baris ".($key - 4).": Brand ".$brandx." tidak tersedia",
                    );
                    $baris++;
                    continue; // Abaikan entri ini jika $distrik bukan angka
                }

                $distributor = sprintf("%010s", $value[4]);
                $tgl_target = dateFormatted($value[5], "Ymd");
                $tgl_bulan = dateFormatted($value[5], "Ym");
                $tgl_bulan2 = dateFormatted($value[5], "Y-m");
                $vol_target = trim($value[6]);
                $persentase = trim($value[7]);

                // echo '<pre>';
                // print_r($value[8]); // Cek nilai asli dari Excel
                // echo '</pre>';

                unset($val);
                $val['ORG'] = $org;
                $val['BRAND'] = $brandx;
                $val['STATUS_BRAND'] = null;
                $val['DISTRIK'] = $distrik;
                $val['DISTRIBUTOR'] = $distributor;
                $val['TIPE'] = $tipe;
                $val['TGL_TARGET'] = $tgl_target;
                $val['TGL_BULAN'] = $tgl_bulan;
                $val['TARGET'] = floatval(str_replace(",",".",$vol_target));
                $val['PERSENTASE'] = floatval(str_replace(",",".",$persentase));

                $ins = false;
                $upd = false;

                $datane = CekSelectData($conn, $val, "", $arrbrand);


                if (count($datane) > 0) // ada plant kosong
                {
                    if ($tipe == '121-301') {

                        if (in_array($val['DISTRIBUTOR'], $cekSoldto)) {
                            $plafon = "SELECT
                                        mmw.KODE_DISTRIK,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.TARGET_SPC
                                            ELSE tb2.TARGET_SPC
                                        END AS TARGET_SPC,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SISA_PLAFON
                                            ELSE tb2.SISA_PLAFON
                                        END AS TARGET,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.TARGET_AKHIR
                                            ELSE tb2.TARGET_AKHIR
                                        END AS TARGET_AKHIR,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SEGMEN
                                            ELSE tb2.SEGMEN
                                        END AS SEGMEN
                                    FROM
                                        MAPPING_MASTER_WILAYAH mmw
                                    LEFT JOIN (
                                        SELECT
                                            mpt.*,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                                            CASE
                                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                ELSE tbt2.TARGET
                                            END AS TARGET_SPC,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                            WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                            ELSE tbt2.TARGET
                                        END, 0) AS SISA_PLAFON,
                                        COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_AKHIR,
                                            mms.SEGMEN,
                                            prov.NM_PROV AS NAMA_PROVINSI
                                        FROM
                                            MAPPING_PLAFON_TARGET mpt
                                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                                            mpt.KD_PROP = prov.KD_PROV
                                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                                            mpt.PERIODE = mms.PERIODE
                                            AND mpt.REGIONAL = mms.REGIONAL
                                            AND mpt.KD_PROP = mms.KD_PROP
                                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                                            AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                                            AND mpt.BRAND = mms.BRAND
                                            AND mms.DEL_MARK = '0'
                                        LEFT JOIN (
                                            SELECT
                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                mmw.KODE_REGION,
                                                mmw.KODE_PROVINSI,
                                                mmw.DISTRIK_RET,
                                                tb1.BRAND,
                                                sum(tb1.TARGET) AS TARGET
                                            FROM
                                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                tb1.DISTRIK = mmw.KODE_DISTRIK
                                            WHERE
                                                tb1.DEL_MARK = '0'
                                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                    AND tb1.BRAND = '$brandx'
                                                GROUP BY
                                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                    mmw.KODE_REGION,
                                                    mmw.KODE_PROVINSI,
                                                    mmw.DISTRIK_RET,
                                                    tb1.BRAND) tbt1 ON
                                            tbt1.PERIODE = mpt.PERIODE
                                            AND tbt1.BRAND = mpt.BRAND
                                            AND tbt1.KODE_REGION = mpt.REGIONAL
                                            AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                                            AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                                        LEFT JOIN (
                                            SELECT
                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                mmw.KODE_REGION,
                                                mmw.KODE_PROVINSI,
                                                tb1.BRAND,
                                                sum(tb1.TARGET) AS TARGET
                                            FROM
                                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                tb1.DISTRIK = mmw.KODE_DISTRIK
                                            WHERE
                                                tb1.DEL_MARK = '0'
                                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                    AND tb1.BRAND = '$brandx'
                                                GROUP BY
                                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                    mmw.KODE_REGION,
                                                    mmw.KODE_PROVINSI,
                                                    tb1.BRAND) tbt2 ON
                                            tbt2.PERIODE = mpt.PERIODE
                                            AND tbt2.BRAND = mpt.BRAND
                                            AND tbt2.KODE_REGION = mpt.REGIONAL
                                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                                        WHERE
                                            mpt.DEL_MARK = '0'
                                            AND mpt.PERIODE = '$tgl_bulan2'
                                            AND mpt.BRAND = '$brandx'
                                        ORDER BY
                                            mpt.PERIODE DESC,
                                            mpt.REGIONAL,
                                            mpt.KD_PROP,
                                            mpt.DISTRIK_RET) tb1 ON
                                        mmw.DISTRIK_RET = tb1.DISTRIK_RET
                                    LEFT JOIN (
                                        SELECT
                                            mpt.*,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                                            CASE
                                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                ELSE tbt2.TARGET
                                            END AS TARGET_SPC,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                            WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                            ELSE tbt2.TARGET
                                        END, 0) AS SISA_PLAFON,
                                        COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_AKHIR,
                                            mms.SEGMEN,
                                            prov.NM_PROV AS NAMA_PROVINSI
                                        FROM
                                            MAPPING_PLAFON_TARGET mpt
                                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                                            mpt.KD_PROP = prov.KD_PROV
                                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                                            mpt.PERIODE = mms.PERIODE
                                            AND mpt.REGIONAL = mms.REGIONAL
                                            AND mpt.KD_PROP = mms.KD_PROP
                                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                                                AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                                                    AND mpt.BRAND = mms.BRAND
                                                    AND mms.DEL_MARK = '0'
                                                LEFT JOIN (
                                                    SELECT
                                                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                        mmw.KODE_REGION,
                                                        mmw.KODE_PROVINSI,
                                                        mmw.DISTRIK_RET,
                                                        tb1.BRAND,
                                                        sum(tb1.TARGET) AS TARGET
                                                    FROM
                                                        ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                                    LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                        tb1.DISTRIK = mmw.KODE_DISTRIK
                                                    WHERE
                                                        tb1.DEL_MARK = '0'
                                                        AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                            AND tb1.BRAND = '$brandx'
                                                        GROUP BY
                                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                            mmw.KODE_REGION,
                                                            mmw.KODE_PROVINSI,
                                                            mmw.DISTRIK_RET,
                                                            tb1.BRAND) tbt1 ON
                                                    tbt1.PERIODE = mpt.PERIODE
                                                        AND tbt1.BRAND = mpt.BRAND
                                                        AND tbt1.KODE_REGION = mpt.REGIONAL
                                                        AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                                                        AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                                                    LEFT JOIN (
                                                        SELECT
                                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                            mmw.KODE_REGION,
                                                            mmw.KODE_PROVINSI,
                                                            tb1.BRAND,
                                                            sum(tb1.TARGET) AS TARGET
                                                        FROM
                                                            ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                                        LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                            tb1.DISTRIK = mmw.KODE_DISTRIK
                                                        WHERE
                                                            tb1.DEL_MARK = '0'
                                                            AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                                AND tb1.BRAND = '$brandx'
                                                            GROUP BY
                                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                                mmw.KODE_REGION,
                                                                mmw.KODE_PROVINSI,
                                                                tb1.BRAND) tbt2 ON
                                                        tbt2.PERIODE = mpt.PERIODE
                                                            AND tbt2.BRAND = mpt.BRAND
                                                            AND tbt2.KODE_REGION = mpt.REGIONAL
                                                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                                                        WHERE
                                                            mpt.DEL_MARK = '0'
                                                            AND mpt.PERIODE = '$tgl_bulan2'
                                                            AND mpt.BRAND = '$brandx'
                                                            AND mpt.DISTRIK_RET IS NULL
                                                        ORDER BY
                                                            mpt.PERIODE DESC,
                                                            mpt.REGIONAL,
                                                            mpt.KD_PROP,
                                                            mpt.DISTRIK_RET) tb2 ON
                                        mmw.KODE_PROVINSI = tb2.KD_PROP
                                    WHERE
                                        mmw.KODE_DISTRIK = '".$val['DISTRIK']."' ";
                            //   echo '<br>QUERY '.$key.' ====> <br>';echo $plafon;exit;
                            $queryPlafon = oci_parse($conn, $plafon);
                            oci_execute($queryPlafon);
                            $rowPlafon = oci_fetch_array($queryPlafon);

                            $targetDist = "SELECT
                                                TARGET
                                            FROM
                                                ZSD_TARGET_HARIAN_NEW_BRAND
                                            WHERE
                                                DEL_MARK = '0'
                                                AND TO_CHAR(TANGGAL_TARGET, 'YYYY-MM') = '".$tgl_bulan2."'
                                                AND BRAND = '".$val['BRAND']."'
                                                AND DISTRIK = '".$val['DISTRIK']."'
                                                AND DISTRIBUTOR = '".$val['DISTRIBUTOR']."' ";

                            $queryTargetDist = oci_parse($conn, $targetDist);
                            oci_execute($queryTargetDist);
                            $rowTargetDist = oci_fetch_array($queryTargetDist);

                            $targetBefore = $rowTargetDist['TARGET'] ? $rowTargetDist['TARGET'] : 0; // Target Distributor Sebelumnya
                            $cek_sum_target = isset($rowPlafon['TARGET_SPC']) && $rowPlafon['TARGET_SPC'] ? $rowPlafon['TARGET_SPC'] : 0; // Summary target terupload
                            $target_sum_before = $cek_sum_target - $targetBefore; // summary target dengan pengurangan target sebelumnya
                            $target_sum_after = $target_sum_before + $val['TARGET']; // Summary target setelah ditambahkan dengan target baru
                            $target_akhir_snop = isset($rowPlafon['TARGET_AKHIR']) && $rowPlafon['TARGET_AKHIR'] ? $rowPlafon['TARGET_AKHIR'] : 0; // Target Akhir
                            $sisa_plafon_akhir = $target_akhir_snop - $target_sum_after; // Sisa SNOP setelah terupload nilai target baru
                            $sisa_plafon_akhir = floatval($sisa_plafon_akhir);
                            $cek_plafon = isset($rowPlafon['TARGET']) && $rowPlafon['TARGET'] ? $rowPlafon['TARGET'] : 0;
                            $cek_plafon_segmen = isset($rowPlafon['SEGMEN']) && $rowPlafon['SEGMEN'] ? $rowPlafon['SEGMEN'] : 'MB';

                            echo "<br>";
                            echo "<pre>";
                            print_r($rowPlafon);
                            echo "</pre>";
                            echo "<br> target before ==> ". $targetBefore;
                            echo "<br> sum target ==> ". $cek_sum_target;
                            echo "<br> target sum before ==> ". $target_sum_before;
                            echo "<br> target sum after ==> ". $target_sum_after;
                            echo "<br> target akhir snop ==> ". $target_akhir_snop;
                            echo "<br> sisa plafon akhir ==> ". $sisa_plafon_akhir;

                            echo "<br> hasil dari ".$sisa_plafon_akhir." < -2  ==> ". ((floatval($sisa_plafon_akhir)) < -2) ? "TRUE" : "FALSE";
                            echo "<br>";

                            if (is_numeric($sisa_plafon_akhir)) {
                                $sisa_plafon_akhir = floatval($sisa_plafon_akhir);
                                echo ($sisa_plafon_akhir < -2) ? "TRUE" : "FALSE";
                            } else {
                                echo "Nilai tidak valid";
                            }
                            
                            if (( floatval($sisa_plafon_akhir) < -2 || $target_akhir_snop == 0) && $cek_plafon_segmen == "FB") {
                                $errorOra .= "<br> Gagal pada baris $baris: Target melebihi SNOP yang telah ditentukan (".number_format($cek_plafon, 0, ',', '.').")";
                                $messPlafArr[] = array(
                                    "baris" => $baris,
                                    "issue_type" => "snop",
                                    "message" => "Gagal pada baris $baris: Target melebihi SNOP yang telah ditentukan (".number_format($sisa_plafon_akhir, 0, ',', '.').")",
                                );
                                $baris++;
                                continue; // Abaikan entri ini jika target melebihi target plafon
                            }
                        }

                        $grouping = "SELECT TIPE,DISTRIK,DISTRIBUTOR,to_char(tanggal_target, 'YYYYMM'), sum(PERSENTASE) as PERSENTASE 
                                    FROM ZSD_TARGET_HARIAN_NEW_BRAND
                                    WHERE DISTRIK = '" . $val['DISTRIK'] . "'
                                    and DISTRIBUTOR = '" . $val['DISTRIBUTOR'] . "'
                                    and BRAND != '" . $val['BRAND'] . "'
                                    and to_char(tanggal_target, 'YYYYMM') = '" . $val['TGL_BULAN'] . "'
                                    and DEL_MARK = 0
                                    GROUP BY TIPE,DISTRIK,DISTRIBUTOR,to_char(tanggal_target, 'YYYYMM')";

                        //   echo '<pre>';echo $grouping;
                        $query = oci_parse($conn, $grouping);
                        oci_execute($query);
                        $row = oci_fetch_array($query);
                        $totpersentase = 0;

                        if (count($row) > 1) {
                            $arData[] = $row;
                            if (($row['PERSENTASE'] != "") || ($row['PERSENTASE'] != null)) {

                                $totpersentase = (float)$row['PERSENTASE'] + (float)$val['PERSENTASE'];

                                if ($val['DISTRIK'] == '224002' && $val['BRAND'] == 'DYNAMIX') {
                                    $totpersentase = 100;
                                }

                                if ($val['DISTRIK'] == '224002' && $val['BRAND'] == 'MDK-DYX') {
                                    $totpersentase = 52.63;
                                }
                                
                                if ($val['DISTRIK'] == '224002' && $val['BRAND'] == 'SP') {
                                    $totpersentase = 0;
                                }

                                if ($totpersentase <= 100) {
                                    $upd = UpdateData($conn, $val, $arrbrand);
                                } else {
                                    //    echo '<pre>';echo $grouping;
                                    $fail_++;
                                    $failupdatelistdata .= "<br> gagal update data ke " . $baris;
                                    $messPlafArr[] = array(
                                        "baris" => $baris,
                                        "issue_type" => "persentase",
                                        "message" => "Gagal update pada baris $baris: persentase melebihi 100%",
                                    );
                                }
                            } else {
                                $upd = UpdateData($conn, $val, $arrbrand);
                            }
                        } else {
                            $upd = UpdateData($conn, $val, $arrbrand);
                        }
                    } else { // UPDATE NON ZAK
                        $upd = UpdateData($conn, $val, $arrbrand);
                    }
                } else {
                    if (in_array($val['DISTRIBUTOR'], $cekSoldto)) {
                        $plafon = "SELECT
                                        mmw.KODE_DISTRIK,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SISA_PLAFON
                                            ELSE tb2.SISA_PLAFON
                                        END AS TARGET,
                                        CASE
                                            WHEN tb1.DISTRIK_RET IS NOT NULL THEN tb1.SEGMEN
                                            ELSE tb2.SEGMEN
                                        END AS SEGMEN
                                    FROM
                                        MAPPING_MASTER_WILAYAH mmw
                                    LEFT JOIN (
                                        SELECT
                                            mpt.*,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                                            CASE
                                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                ELSE tbt2.TARGET
                                            END AS TARGET_SPC,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                                                                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                                                    ELSE tbt2.TARGET
                                                                                END, 0) AS SISA_PLAFON,
                                            mms.SEGMEN,
                                            prov.NM_PROV AS NAMA_PROVINSI
                                        FROM
                                            MAPPING_PLAFON_TARGET mpt
                                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                                            mpt.KD_PROP = prov.KD_PROV
                                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                                            mpt.PERIODE = mms.PERIODE
                                            AND mpt.REGIONAL = mms.REGIONAL
                                            AND mpt.KD_PROP = mms.KD_PROP
                                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                                            AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                                            AND mpt.BRAND = mms.BRAND
                                            AND mms.DEL_MARK = '0'
                                        LEFT JOIN (
                                            SELECT
                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                mmw.KODE_REGION,
                                                mmw.KODE_PROVINSI,
                                                mmw.DISTRIK_RET,
                                                tb1.BRAND,
                                                sum(tb1.TARGET) AS TARGET
                                            FROM
                                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                tb1.DISTRIK = mmw.KODE_DISTRIK
                                            WHERE
                                                tb1.DEL_MARK = '0'
                                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                    AND tb1.BRAND = '$brandx'
                                                GROUP BY
                                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                    mmw.KODE_REGION,
                                                    mmw.KODE_PROVINSI,
                                                    mmw.DISTRIK_RET,
                                                    tb1.BRAND) tbt1 ON
                                            tbt1.PERIODE = mpt.PERIODE
                                            AND tbt1.BRAND = mpt.BRAND
                                            AND tbt1.KODE_REGION = mpt.REGIONAL
                                            AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                                            AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                                        LEFT JOIN (
                                            SELECT
                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                mmw.KODE_REGION,
                                                mmw.KODE_PROVINSI,
                                                tb1.BRAND,
                                                sum(tb1.TARGET) AS TARGET
                                            FROM
                                                ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                            LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                tb1.DISTRIK = mmw.KODE_DISTRIK
                                            WHERE
                                                tb1.DEL_MARK = '0'
                                                AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                    AND tb1.BRAND = '$brandx'
                                                GROUP BY
                                                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                    mmw.KODE_REGION,
                                                    mmw.KODE_PROVINSI,
                                                    tb1.BRAND) tbt2 ON
                                            tbt2.PERIODE = mpt.PERIODE
                                            AND tbt2.BRAND = mpt.BRAND
                                            AND tbt2.KODE_REGION = mpt.REGIONAL
                                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                                        WHERE
                                            mpt.DEL_MARK = '0'
                                            AND mpt.PERIODE = '$tgl_bulan2'
                                            AND mpt.BRAND = '$brandx'
                                        ORDER BY
                                            mpt.PERIODE DESC,
                                            mpt.REGIONAL,
                                            mpt.KD_PROP,
                                            mpt.DISTRIK_RET) tb1 ON
                                        mmw.DISTRIK_RET = tb1.DISTRIK_RET
                                    LEFT JOIN (
                                        SELECT
                                            mpt.*,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                                            CASE
                                                WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                ELSE tbt2.TARGET
                                            END AS TARGET_SPC,
                                            COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                                                                                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                                                                                    ELSE tbt2.TARGET
                                                                                END, 0) AS SISA_PLAFON,
                                            mms.SEGMEN,
                                            prov.NM_PROV AS NAMA_PROVINSI
                                        FROM
                                            MAPPING_PLAFON_TARGET mpt
                                        LEFT JOIN ZREPORT_M_PROVINSI prov ON
                                            mpt.KD_PROP = prov.KD_PROV
                                        LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                                            mpt.PERIODE = mms.PERIODE
                                            AND mpt.REGIONAL = mms.REGIONAL
                                            AND mpt.KD_PROP = mms.KD_PROP
                                            AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                                                AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                                                    AND mpt.BRAND = mms.BRAND
                                                    AND mms.DEL_MARK = '0'
                                                LEFT JOIN (
                                                    SELECT
                                                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                        mmw.KODE_REGION,
                                                        mmw.KODE_PROVINSI,
                                                        mmw.DISTRIK_RET,
                                                        tb1.BRAND,
                                                        sum(tb1.TARGET) AS TARGET
                                                    FROM
                                                        ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                                    LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                        tb1.DISTRIK = mmw.KODE_DISTRIK
                                                    WHERE
                                                        tb1.DEL_MARK = '0'
                                                        AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                            AND tb1.BRAND = '$brandx'
                                                        GROUP BY
                                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                            mmw.KODE_REGION,
                                                            mmw.KODE_PROVINSI,
                                                            mmw.DISTRIK_RET,
                                                            tb1.BRAND) tbt1 ON
                                                    tbt1.PERIODE = mpt.PERIODE
                                                        AND tbt1.BRAND = mpt.BRAND
                                                        AND tbt1.KODE_REGION = mpt.REGIONAL
                                                        AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                                                        AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                                                    LEFT JOIN (
                                                        SELECT
                                                            to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                                                            mmw.KODE_REGION,
                                                            mmw.KODE_PROVINSI,
                                                            tb1.BRAND,
                                                            sum(tb1.TARGET) AS TARGET
                                                        FROM
                                                            ZSD_TARGET_HARIAN_NEW_BRAND tb1
                                                        LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                                                            tb1.DISTRIK = mmw.KODE_DISTRIK
                                                        WHERE
                                                            tb1.DEL_MARK = '0'
                                                            AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$tgl_bulan2'
                                                                AND tb1.BRAND = '$brandx'
                                                            GROUP BY
                                                                to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                                                                mmw.KODE_REGION,
                                                                mmw.KODE_PROVINSI,
                                                                tb1.BRAND) tbt2 ON
                                                        tbt2.PERIODE = mpt.PERIODE
                                                            AND tbt2.BRAND = mpt.BRAND
                                                            AND tbt2.KODE_REGION = mpt.REGIONAL
                                                            AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                                                        WHERE
                                                            mpt.DEL_MARK = '0'
                                                            AND mpt.PERIODE = '$tgl_bulan2'
                                                            AND mpt.BRAND = '$brandx'
                                                            AND mpt.DISTRIK_RET IS NULL
                                                        ORDER BY
                                                            mpt.PERIODE DESC,
                                                            mpt.REGIONAL,
                                                            mpt.KD_PROP,
                                                            mpt.DISTRIK_RET) tb2 ON
                                        mmw.KODE_PROVINSI = tb2.KD_PROP
                                    WHERE
                                        mmw.KODE_DISTRIK = '".$val['DISTRIK']."' ";

                        //   echo '<pre>';echo $grouping;
                        $queryPlafon = oci_parse($conn, $plafon);
                        oci_execute($queryPlafon);
                        $rowPlafon = oci_fetch_array($queryPlafon);

                        $cek_plafon = isset($rowPlafon['TARGET']) && $rowPlafon['TARGET'] ? $rowPlafon['TARGET'] : 0;
                        $cek_plafon_segmen = isset($rowPlafon['SEGMEN']) && $rowPlafon['SEGMEN'] ? $rowPlafon['SEGMEN'] : 'MB';
                            
                        if (($val['TARGET'] > ($cek_plafon + 2) || $cek_plafon == 0) && $cek_plafon_segmen == "FB") {
                            $messPlafArr[] = array(
                                        "baris" => $baris,
                                        "issue_type" => "snop",
                                        "message" => "Gagal pada baris $baris: Target melebihi SNOP yang telah ditentukan (".number_format($cek_plafon, 0, ',', '.').")",
                            );
                            $errorOra .= "<br> Gagal pada baris $baris: Target melebihi SNOP yang telah ditentukan (".number_format($cek_plafon, 0, ',', '.').")";
                            $baris++;
                            continue; // Abaikan entri ini jika target melebihi target plafon
                        }
                    }

                    $val['TGL_TARGET'] = $value[5];
                    //insert
                    if ($tipe == '121-301') {
                        $grouping = "SELECT TIPE,DISTRIK,DISTRIBUTOR,to_char(tanggal_target, 'YYYYMM'), sum(PERSENTASE) as PERSENTASE 
                                        FROM ZSD_TARGET_HARIAN_NEW_BRAND
                                        WHERE DISTRIK = '" . $val['DISTRIK'] . "'
                                        and DISTRIBUTOR = '" . $val['DISTRIBUTOR'] . "'
                                        and BRAND != '" . $val['BRAND'] . "'
                                        and to_char(tanggal_target, 'YYYYMM') = '" . $val['TGL_BULAN'] . "'
                                        and DEL_MARK = 0
                                        GROUP BY TIPE,DISTRIK,DISTRIBUTOR,to_char(tanggal_target, 'YYYYMM')";
                        //   echo '<pre>';echo $grouping;
                        $query = oci_parse($conn, $grouping);
                        oci_execute($query);
                        $totpersentase = 0;
                        $row = oci_fetch_array($query);
                        if (count($row) > 1) {
                            $arData[] = $row;
                            if (($row['PERSENTASE'] != "") || ($row['PERSENTASE'] != null)) {
                                $totpersentase = (float)$row['PERSENTASE'] + (float)$val['PERSENTASE'];
                                if ($totpersentase <= 100) {
                                    $ins = InsertData($conn, $val, $arrbrand);
                                } else {
                                    $fail++;
                                    $failinsertlistdata .= "<br> gagal insert data ke " . $baris;
                                    $messPlafArr[] = array(
                                        "baris" => $baris,
                                        "issue_type" => "persantase",
                                        "message" => "Gagal insert pada baris $baris: persentase melebihi 100%"
                                    );
                                }
                            } else {
                                $ins = InsertData($conn, $val, $arrbrand);
                            }
                        } else {
                            $ins = InsertData($conn, $val, $arrbrand);
                        }
                    } else { // INSERT NON ZAK
                        $ins = InsertData($conn, $val, $arrbrand);
                    }
                }
                $val['TGL_TARGET'] = $tgl_target;
                //hitung
                if ($ins) {
                    $insert++;
                    $insertlistdata .= "<br> berhasil insert data ke " . $baris;
                    $messPlafArr[] = array(
                        "baris" => $baris,
                        "issue_type" => "suksess insert",
                        "message" => "berhasil insert data ke " . $baris
                    );
                    if (in_array($val['BRAND'], $arrbrand) && $val['TIPE'] == '121-301') {
                        $dataSAP[] = $val;
                    }
                }
                if ($upd) {
                    $update++;
                    $updatelistdata .= "<br> berhasil update data ke " . $baris;
                    $messPlafArr[] = array(
                        "baris" => $baris,
                        "issue_type" => "suksess update",
                        "message" => "berhasil update data ke " . $baris
                    );
                    if (in_array($val['BRAND'], $arrbrand) && $val['TIPE'] == '121-301') {
                        $dataSAP[] = $val;
                    }
                }
            } else   //end if plant kosong
            {
                $errorOra .= "<br>$baris = BRAND, DISTRIK, DISTRIBUTOR, TGL TARGET tidak boleh kosong";
                $messPlafArr[] = array(
                    "baris" => $baris,
                    "issue_type" => "brand not available",
                    "message" => "baris $baris = BRAND, DISTRIK, DISTRIBUTOR, TGL TARGET tidak boleh kosong"
                );
            }

            $baris++;
        } //end if kosong

    }  //end foreach
    // die();
    //============================================================================

    // UNTUK KEBUTUHAN REPORTING REPORT SORE


    $msg = "";
    if ($insert > 0)
        //            $msg .= "<br><br>";
        $msg .= $insert . " Insert Data";
    $msg .= $insertlistdata;
    $msg .= "<br><br>";
    if ($update > 0)
        $msg .= "<br><br>";
    $msg .= $update . " Update Data";
    $msg .= $updatelistdata;
    $msg .= "<br><br>";
    if ($insert_sap > 0)
        $msg .= $insert_sap . " Insert/Update SAP ";
    //        if ($upadate_sap>0)
    //            $msg .= $upadate_sap." Update SAP ";
    if (count($gagal) > 0) {
        $txt_gagal = implode(',', $gagal);
        $msg .= " Gagal :" . $txt_gagal;
    }
    if ($fail > 0) {
        $msg .= "<br><br>";
        $msg .= $fail . " Data insert sudah melebihi persentase ";
        $msg .= $failinsertlistdata;
        $msg .= "<br><br>";
    }
    if ($fail_ > 0) {
        $msg .= "<br><br>";
        $msg .= $fail_ . " Data update sudah melebihi persentase ";
        $msg .= $failupdatelistdata;
        $msg .= "<br><br>";
    }
    $msg .= $errorOra;

    $mesRet = array("messData" => $msg, "messPlaf" => $messPlafArr);
    return $mesRet;
}

function reduce_target($_target, $_item)
{
    $_target = array();
    $_distributor = sprintf("%010s", $_item[5]);
    $_distrik = trim($_item[4]);

    if (!isset($_target[$_distributor][$_distrik])) {
        $_target[$_distributor][$_distrik] = 0;  // Inisialisasi elemen dalam array jika belum ada
    }
    $_target[$_distributor][$_distrik] += floatval(trim($_item[8]));

    return $_target;
}

// $sqlplant = "select PLANT FROM OR_PLANT_HARIAN WHERE DELETE_MARK = 0";
$sqlbrand = "select BRAND FROM MASTER_BRAND WHERE DEL_MARK = 'Y'";
$qbrand = oci_parse($conn, $sqlbrand);
oci_execute($qbrand);
$arrbrand = array();
while ($rbrand = oci_fetch_assoc($qbrand)) {
    array_push($arrbrand, $rbrand['BRAND']);
}

############################# READ XLS ####################
error_reporting(E_ALL ^ E_NOTICE);
require_once '../ex_report/excel_reader2.php';

if (isset($_POST['Import'])) {
    $allowedExts = "xls";
    $extension = end(explode(".", $_FILES["file"]["name"]));
    if ($extension == $allowedExts) {

        $cell   = new Spreadsheet_Excel_Reader($_FILES['file']['tmp_name']);
        $jumlah_row = $cell->rowcount($sheet_index = 0);
        $jumlah_col = $cell->colcount($sheet_index = 0);
        $kode_file   = $cell->val(1, 2);


        for ($i = 5; $i <= $jumlah_row; $i++) {
            for ($j = 1; $j <= 8; $j++) {
                // $ke = $i - 4;
                // $data[$ke][$j] = $cell->val($i, $j);
                $data[$i][$j] = $cell->val($i, $j);
            }
        }
        $data[TIPE] = $cell->val(2, 2);

        // var_dump($data);
        // die();
        $messDataRet = update_TargetHarian($data, $arrbrand);
        $messData = $messDataRet["messData"];
        $messDataPlafon = $messDataRet["messPlaf"];
    } else {
        echo "<script>alert('Invalid file...!!');</script>";
    }
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Import Data Target Volume dan Index Kode DA</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<style>
    table.excel {
        border-style: ridge;
        border-width: 1;
        border-collapse: collapse;
        font-family: sans-serif;
        font-size: 12px;
    }

    table.excel thead th,
    table.excel tbody th {
        background: #CCCCCC;
        border-style: ridge;
        border-width: 1;
        text-align: center;
        vertical-align: bottom;
    }

    table.excel tbody th {
        text-align: center;
        width: 20px;
    }

    table.excel tbody td {
        vertical-align: bottom;
    }

    table.excel tbody td {
        padding: 0 3px;
        border: 1px solid #EEEEEE;
    }
</style>

<body>
    <div align="center">
        <table width="800" align="center" class="adminheading" border="0">
            <tr>
                <th class="da2">Upload Target Harian Brand</th>
            </tr>
        </table>
    </div>

    <form method="post" name="import" id="import" enctype="multipart/form-data" action="<?= $page; ?>">
        <table width="800" align="center" class="adminform">
            <tr height="30">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Upload file :</td>
                <td class="puso">:</td>
                <td> <input name="file" type="file" class="button" accept=".xls"></td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td><input name="Import" type="submit" class="button" value="Import"></td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>

            </tr>
            <tr>
                <td class="puso" colspan="3">&nbsp;&nbsp;&nbsp;Download template target harian <a href="dataxls/template_target_harian_new_brand.xls">disini</a></td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>

            </tr>
        </table>
    </form>
    <br><br>
    <?
    if ($messData) {
    ?>
        <table align="center" class="adminform" width="600">
        <?
        if (count($messDataPlafon) > 0) {
        ?>
            <tr align="right">
                <td class="puso" align="right">
                    <input name="btn_export_log" type="button" class="button" value="Export Log" onclick="exportToExcel()" style="float:right;margin: 9 9 9 9;">
                </td>
            </tr>
        <?
        }
        ?>
            <tr align="center">
                <td class="puso" align="center">
                    <? echo "<br>" . $messData . "<br>"; ?>
                </td>
            </tr>
        </table>

        <?
        if (count($messDataPlafon) > 0) {
        ?>
            <table align="center" id="log_export" class="template_export" width="600" style="display: none;">
                <tr>
                    <th>No.</th>
                    <th>Baris</th>
                    <th>Tipe Error</th>
                    <th>Log Upload Target Harian Brand</th>
                </tr>
                <? foreach ($messDataPlafon as $key => $value) {?>
                    <tr >
                        <td>
                            <? echo $key+1; ?>
                        </td>
                        <td>
                            <? echo $value['baris']; ?>
                        </td>
                        <td>
                            <? echo $value['issue_type']; ?>
                        </td>
                        <td>
                            <? echo $value['message']; ?>
                        </td>
                    </tr>
                <? } ?>
            </table>
        <?
        }
        ?>
    <? } ?>
    <div align="center">
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

    <script>
            function exportToExcel() {
                const table = document.getElementById("log_export");
                console.log(table);
                
                const workbook = XLSX.utils.table_to_book(table, {sheet:"Sheet1"});
                XLSX.writeFile(workbook, "log_upload_target_harian_brand.xlsx");
            }
    </script>
</body>


</html>
