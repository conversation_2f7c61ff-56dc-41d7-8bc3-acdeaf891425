<?
/*
 * @liyantanto
 */
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();
require('../include/fpdf/html2pdf.php');
require_once ('../security_helper.php');
sanitize_global_input();

$targetVolume='rencana_pengirimanto.php';
$titlepage='Approval Invoice';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$vendor_id  = $_SESSION['vendor_id'];
//echo $vendor_id;
//$user_id='mady';
$com=$user_org;
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,3);
	else return '0';
}
function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}
$waktu=date("d-m-Y");
//$halaman_id=1896;//dev
//$halaman_id=3753;//prod
$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);




?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
<script type="text/javascript" src="../ckeditor/ckeditor.js"></script>
<!--<script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="http://www.jeasyui.com/easyui/jquery.easyui.min.js"></script>-->
<!--<script src="https://cdn.ckeditor.com/4.10.0/standard/ckeditor.js"></script>-->
</head>   
<body>

<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px" 
           rownumbers="true" pagination="true">
        <thead>
            <tr> 
                <th data-options="field:'NO_INVOICE'">No Invoice</th>
                <th data-options="field:'TGL_INVOICE'">Tanggal Invoice</th>
                <th data-options="field:'ACCOUNTING_DOC'">No PPL</th>
                <th data-options="field:'INV_DOC_NUMBER'">No MIR7</th>
                <th data-options="field:'INV_DOC_NUMBER_CONV'">No FI Doc</th>
                <th data-options="field:'NO_BA'">No BA Rekaputalasi</th>
                <th data-options="field:'TANGGAL_BA'">Tanggal BA Rekaputalasi</th>
                <th data-options="field:'NAMA_VENDOR'">Expeditur</th>
                <th data-options="field:'WARNA_PLAT'">Warna Plat </th>
                <th data-options="field:'NO_PAJAK_EX'">No Pajak </th>
                <th data-options="field:'TOTAL_KLAIM'">Total</th> 
                <th data-options="field:'STATUS_NAME'">Status</th> 
            </tr>
        </thead>
    </table>
    <div id="toolbar">
         
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-arkdownload" plain="true" onclick="viewApprove()">Approve</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="viewDisplay()">Display</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-print" plain="true" onclick="viewPrint()">Cetak</a>
        <!--
        <a href="http://***********/dev/sd/sdonline/upload_data/upload_file_distr/20180814-examplepwphcd.pdf" class="easyui-linkbutton" iconCls="icon-edit">Edit</a>   <!--
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="showAct()">View</a>-->
<!--        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="sendMail()">Send Mail</a>-->
<!--        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="approveAct()" id="approveAct">Approve</a>
        <input class="easyui-datebox" id="TGL_VALIDFROM" name="TGL_VALIDFROM" required="true" data-options="formatter:myformatter,parser:myparser">-->
<!--        s/d-->
<!--        <input class="easyui-datebox" id="TGL_VALIDTO" name="TGL_VALIDTO" required="true" data-options="formatter:myformatter,parser:myparser">
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="newSearch" style="width:80px">Search</a>-->
<!--        <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>   -->
    </div>
    <div id="dlg" class="easyui-dialog" style="width:1000px;height:550px;padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle">Ekspedisi Faktur Pajak</div>
        <form enctype="multipart/form-data" id="fm" method="post" novalidate>            
            <div style="margin:20px 0;"></div>
            <div class="easyui-panel" style="width:900px; padding:10px 20px" title="Form Ekspedisi Faktur Pajak" style="width:100%;max-width:750px;padding:30px 60px;">
                <form id="ff" method="post" enctype="multipart/form-data">
                    <div class="fitem">
                        <label style="width:40%">Company :</label>                    
                        <input style="width:40%" class="easyui-textbox" id="org" name="org" required="true"> 
                        <input id="nm_org" name="nm_org" readonly="true" type="hidden">
                    </div>
                    <div class="fitem">
                        <label style="width:40%">No Faktur Pajak:</label>
                        <input style="width:40%" id="nofaktur" name="nofaktur" class="easyui-textbox" required="true">       
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Nomor BAST/BAPP/PO/SPK/OP:</label>
                        <input style="width:40%" id="po" name="po" class="easyui-textbox" required="true">       
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Tanggal Faktur:</label>
                        <input style="width:20%" class="easyui-datebox" id="faktur_date" name="faktur_date" data-options="formatter:myformatter,parser:myparser" required="true">
                    </div>
                    <div class="fitem">
                        <label style="width:40%" >Tanggal BAST/BAPP:</label>
                        <input style="width:20%" class="easyui-datebox" id="bast_date" name="bast_date" data-options="formatter:myformatter,parser:myparser" required="true">
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Nama PIC:</label>
                        <input style="width:40%" id="pic" name="pic " class="easyui-textbox" required="true">       
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Email PIC:</label>
                        <input style="width:40%" id="email" name="email" class="easyui-textbox" required="true">       
                    </div>
                    <div class="fitem">
                        <label style="width:40%">Nilai Dasar Pengenaan Pajak:</label>
                        <input style="width:40%" id="dasar_pajak" name="dasar_pajak" class="easyui-textbox" required="true">       
                    </div>
<!--                    <div class="fitem">
                        <label style="width:40%">Upload File</label>
                        <input id="gambar" required="true" class="easyui-filebox" label="File2:" labelPosition="top" data-options="prompt:'Choose file...'" style="width:40%">
                    </div>-->
                    <div class="fitem">
                        <label style="width:40%">FILE (pdf only, max 4 Mb) :</label>
                <!--        <input id="upload" name="upload" class="easyui-filebox" label="File1:" labelPosition="top" data-options="prompt:'Choose a file...'" style="width:50%" accept="application/pdf">-->
                        <input style="width:40%" id="upload" name="upload" style="width:100%" type="file" required="true">
                        <input type="hidden" id="GAMBAR">
                    </div>
                    <div class="col-md-12 col-xs-12">
                        <button style="margin-left:41%; margin-top:10px; margin-bottom:10px" class="btn btn-info" onclick="addFaktur(this,'#tbody-doc')" type="button">Tambah</button>
                    </div>
                    <div class="col-md-12 col-xs-12" style="margin-top:10px;margin-bottom:50px;overflow-x:auto;">
                        <table class="table table-bordered tableDoc">
                            <thead>
                                <tr>
                                    <th bgcolor="#e6e6ff" class="text-center">No</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Org</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Nomor Faktur Pajak</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Tanggal Faktur Pajak</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Tanggal BAST</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Nilai Dasar Pengenaan Pajak</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Nomor PO</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Nama</th>
                                    <th bgcolor="#e6e6ff" class="text-center">Email</th>
                                    <th bgcolor="#e6e6ff" class="text-center">File Faktur Pajak</th>
                                    <th bgcolor="#e6e6ff" class="text-center"></th>
                                </tr>
                            </thead>
                            <tbody id="tbody-doc">                          
                            </tbody>
                        </table>
                    </div>

                </form>
                <div style="text-align:center;padding:5px 0">
<!--                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveData()" id="saveData" style="width:80px">Submit</a>-->
    <!--                <a href="javascript:void(0)" class="easyui-linkbutton" onclick="clearForm()" style="width:80px">Clear</a>-->
                </div>
                
            </div>    
        </form>
    </div>

        
    
    <div id="dlg-buttons">
        <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="ekspedisi(this)" style="width:90px" id="ekspedisi">Ekspedisi</a>
        <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct(this)" style="width:90px" id="savedata">Save</a>
        <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="removeAct(this)" style="width:90px" id="removedata">Remove</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="cancelAct(this)" style="width:90px">Cancel</a>
    </div>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>

<script type="text/javascript">
    
    $('#org').combogrid({
        panelWidth:450,
        url: 'getdata_org.php',
        idField:'ORGINH',
        textField:'NAMEORG',
        fitColumns:true,
        method: 'get',
        loadMsg: 'Searching...',
        onSelect: function(index,row){
            var desc = row.ORGINH;  // the product's description
            var kode = row.NAMEORG;
//            $('#dist').val(desc);
            $('#org').textbox('setValue', desc); 
            $('#nm_org').val(desc);
        },    
        onChange:  function(newValue,oldValue) {
            $('#org').textbox('setValue','');
//            $('#nm_dist').textbox('setValue','');
        },
        columns:[[
        {field:'ORGINH',title:'Kode',align:'center',width:100},
        {field:'NAMEORG',title:'Org',align:'center',width:350}
        ]]
    });
    
function cekTgl(){
    var a = $('#TGL_VALIDFROM').datebox('getValue');
    var b = $('#TGL_VALIDTO').datebox('getValue');
    var explod = a.split('-');
    var explod2 = b.split('-');
    var tgl = new Date();
    var tgl_a = tgl.setFullYear(explod[0],explod[1],explod[2]);
    var tgl_b = tgl.setFullYear(explod2[0],explod2[1],explod2[2]);
    var milisecond = 60*60*24*1000;    
    var c = ((tgl_b-tgl_a)/ milisecond)+1;    
    if(c>31){
        return false;        
    }else{
        return true;
    }
} 

 $(function(){
    $("#dg").datagrid({
            url:'data_approval_invoiceAct.php?act=show',
            singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:10,
            rownumbers:true,
            fitColumns:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
			// columns:[[ 
            // {field:'DISPLAY_BT',title:'DISPLAY_BT',width:80,align:'center',
                // formatter:function(value,row,index){ 
                        // //var s = '<a href="javascript:void(0)" onclick="saverow(this)">Save</a> '; 
						// var s = '<a href="javascript:popUp(print_preview_ppl.php?no_invoice)" class="button">DISPLAY</a>';
                        // return s;
                     
                // }
            // }
			// ]],
            toolbar:'#toolbar'
    });    
    //set value tanggal
    var g_now = new Date();
    var period=g_now.getMonth()+1; 
    var period2=g_now.getFullYear();
//    alert(period+'-'+period2);
    var minusday = new Date(g_now.getTime() + -3 * 24 * 60 * 60 * 1000);
    $('#PERIODE').datetimespinner({
            formatter:function(date){
                    if (!date){return '';}
                    var m = date.getMonth()+1;
                    var y = date.getFullYear();
                    return (m<10?'0'+m:m)+'-'+date.getFullYear();
            },
            parser:function(s){
                    s = $.trim(s);
                    if (!s){return null;}
                    var dt = s.split('-');
                    return new Date(dt[1],parseInt(dt[0])-1,1);
            },
            selections:[[0,2],[3,7]],
            value: period+'-'+period2
    })
    $('#TGL_VALIDFROM').datebox('setValue',myformatter(minusday));
    $('#TGL_VALIDTO').datebox('setValue',myformatter(g_now));
    $('#dg').datagrid('enableFilter');
 });
 
    $("#btnExport").click(function() {        
        var myData = $('#dg').datagrid('getData');        
        var mapForm = document.createElement("form");
        mapForm.id = "formexport";
        mapForm.target = "dialogSave";
        mapForm.method = "POST";
        mapForm.action = "rencana_pengirimanto_xls.php";        
        $.each(myData.rows, function(k,v){
            $.each(v, function(k2, v2){
                var hiddenField = document.createElement("input");              
                hiddenField.type = "hidden";
                hiddenField.name = "data[" + k + "][" + k2 + "]";
                hiddenField.value = v2;
                mapForm.appendChild(hiddenField);
            });
        });            
        document.body.appendChild(mapForm);
        mapForm.submit();
        document.body.removeChild(mapForm);
        
    });
    
     $("#newSearch").click(function() {      
       if(!cekTgl()){
           $.messager.show({
                title: 'Error',
                msg: 'Maaf, tanggal maksimal 31 hari'
            });
      }else{        
       $('#dg').datagrid('load',{
            TGL_VALIDFROM: $('#TGL_VALIDFROM').datebox('getValue'),
            TGL_VALIDTO: $('#TGL_VALIDTO').datebox('getValue')
       }); // reload the user data
      }  
    });

var url, tot;

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
    function lodingact(obj){    
    //alert(obj);
    if(obj==1){
        document.getElementById("loadingd").style.display = "none";
    }else{
        document.getElementById("loadingd").style.display = "block";
    }
}

function viewDisplay(){
    var row = $('#dg').datagrid('getSelected');
    if (row){ 
		console.log(row.NO_INVOICE)
		window.open('print_preview_ppl.php?no_invoice='+row.NO_INVOICE, 'fullscreen=yes', "width=2000,height=1000"); 
        //window.open(row.LFILE, 'fullscreen=yes');
    } else{
        alert("Please, choose the file");
    }
}
function viewApprove(){
    var row = $('#dg').datagrid('getSelected');
	 
    if (row){
			if(row.STATUSE=="90"){ 
				window.open('form_approval_invoice.php?no_ba='+row.NO_BA, 'fullscreen=yes', "width=2000,height=1000");  
			}else{
				 alert("Pilih data yang berstatus 'APPROVED BY MANAJER VERIFIKASI'");
			}
    } else{
        alert("Please, choose the file");
    }
}
function viewPrint(){
    var row = $('#dg').datagrid('getSelected');
    if (row){  
			if(row.STATUSE>=110){ 
				window.open('print_draft_ppl_new.php?no_invoice='+row.NO_INVOICE, 'fullscreen=yes', "width=2000,height=1000");   
			}else{
				 alert("Data belum diapprove SM");
			}
		
    } else{
        alert("Please, choose the file");
    }
}



function newAct(){
        
    $('#ekspedisi').show();    
    $('#savedata').hide();
    $('#removedata').hide();
    $('#dlg').dialog('open').dialog('setTitle','Form Ekspedisi Faktur Pajak');
    $('#fm').form('clear');
    url = 'ekspedisi_faktur_pajakAct.php?act=add';
}

function cancelAct(){
        
    $('#dg').datagrid('reload');
    $('#dlg').dialog('close');
    $('#tbody-doc').html('');
}

function deleteAct(){
    
    var row = $('#dg').datagrid('getSelected');
       
    if(row){
        
        if(row.KET != 'Dibatalkan'){
            
            $.messager.confirm('Confirm','Apakah Anda Yakin Ingin Batal?',function(r){
                if(r){
                    
                    var strURL="ekspedisi_faktur_pajakAct.php?act=delete&company="+row.BUKRS+"&noeks="+row.EKSPNO+"&nofaktur="+row.XBLNR;        
                    var req = getXMLHTTP();
                    
                    if (req) {
                        
//                        alert(company+" "+noeks+" "+nofaktur);
                        
                        req.onreadystatechange = function() {
                            //            lodingact(0);
                            $.messager.progress({
                                title:'Please waiting',
                                msg:'Loading data...'
                            });
                            if (req.readyState == 4) {
                                // only if "OK"
                                if (req.status == 200) {
                                    alert("SUKSES");
                                    //                                    alert(req.responseText);
                                    //                                    body1.appendChild(newdiv);
                                } else {
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                //                lodingact(1);
                                $.messager.progress('close');
                                $('#dg').datagrid('reload');
                            }				
                        }			
                        req.open("GET", strURL, true);
                        req.send(null);
                    }
                    
                }
            })
       } else {
            alert('Dokumen yang sudah dibatalkan, tidak dapat dibatalkan kembali!!');
       }                
    } else {
        alert('Pilih Baris yang Akan Dibatalkan Terlebih Dahulu');
    }    
}

function ekspedisi(){
    
    alert(nofaktur);
//        var strURL="ekspedisi_faktur_pajakAct.php?act=add&nofaktur="+nofaktur+"&email="+email+"&pic="+pic+"&faktur_date="+faktur_date+"&bast_date="+bast_date+"&dasar_pajak="+dasar_pajak+"&po="+po+"&gambar="+gambar+"&jum="+tot+"&nm_org="+nm_org;
//        
//        var req = getXMLHTTP();
//        if (req) {
//            req.onreadystatechange = function() {
//                //            lodingact(0);
//                $.messager.progress({
//                    title:'Please waiting',
//                    msg:'Loading data...'
//                });
//                if (req.readyState == 4) {
//                    // only if "OK"
//                    if (req.status == 200) {
//                        alert("SUKSES");
//                        //                                    alert(req.responseText);
//                        //                                    body1.appendChild(newdiv);
//                    } else {
//                        alert("There was a problem while using XMLHTTP:\n" + req.statusText);
//                    }
//                    //                lodingact(1);
//                    $.messager.progress('close');
//                    $('#dg').datagrid('reload');
//                    $('#tbody-doc').html('');
//                    $('#dlg').dialog('close');
//                }				
//            }			
//            req.open("GET", strURL, true);
//            req.send(null);
//        }
}

function saveAct(){

    $('#fm').form('submit',{    
        url: url,
        onSubmit: function(){        

//            $("#gambar").change(function(){
//            var formData = new FormData( $("#ff")[0] );
//            $.ajax({
//                    url :  'http://***********/dev/eproc/EC_Vendor/Faktur/doInsertFp',
//                    type : 'POST',
//                    data : formData,
//                    dataType : 'json',
//                    async : false,
//                    cache : false,
//                    contentType : false,
//                    processData : false,
//                    success : function(data) {
//                      if (data.success===true) {
//                        var _tmpMessage = [data.pesan];
//                        $("#gambar").val(data.gambar)
////                             alert(data.pesan);
//                          } else {
//                            var _tmpMessage = [data.pesan];
//                            // alert(data.pesan);
//                          }
//                          bootbox.alert(_tmpMessage.join('<br />'));
//                        }
//                      });
//          });
            
            if($(this).form('validate')){
                $.messager.progress({
                    title:'Please waiting',
                    msg:'Loading data...'
                });
            }
            return $(this).form('validate');
        },
        success: function(result){        
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
            } else {
                $.messager.progress('close');
                $('#dlg').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}
function trim(str){
	    return str.replace(/^\s+|\s+$/g,'');
} 

function myformatter(date){
    var y = date.getFullYear();
    var m = date.getMonth()+1;
    var d = date.getDate();
    return (d<10?('0'+d):d)+'-'+(m<10?('0'+m):m)+'-'+y;
}
function myparser(s){
    if (!s) return new Date();
    var ss = (s.split('-'));
    var y = parseInt(ss[0],10);
    var m = parseInt(ss[1],10);
    var d = parseInt(ss[2],10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)){
        return new Date(d,m-1,y);
    } else {
        return new Date();
    }
}
function readonlyinput(){
//    $('#TARGET_DATEF').datebox({readonly: true});
    $('#alltipei').combo('readonly', true);
//    $('#PLANT').combo({readonly: true});
}   
//
//$("input[type=file]").change(function(){
//    var formData = new FormData( $("#ff")[0] );
//    $.ajax({
////            url : $("#base-url").val() + 'EC_Vendor/Faktur/doInsertFp',  // Controller URL
//            url : 'ekspedisi_faktur_pajakAct.php?act=upload',
//            type : 'POST',
//            data : formData,
//            dataType : 'json',
//            async : false,
//            cache : false,
//            contentType : false,
//            processData : false,
//            success : function(data) {
//              if (data.success===true) {
////                var _tmpMessage = [data.pesan];
////                $("#upload").val(data.gambar)
//                        alert('Sukses');
//                  } else {
////                    var _tmpMessage = [data.pesan];
//                     alert('gagal');
//                  }
////                  bootbox.alert(_tmpMessage.join('<br />'));
//                }
//              });
//  });

var nm_org = [], gambar = [],po = [],email = [],pic = [],faktur_date = [],bast_date = [],nofaktur = [],dasar_pajak = [],po,nofaktur  = [];

function addFaktur(elm,tabel) {
    
  if ($('#nofaktur').val() == '' || $('#faktur_date').datebox('getValue') =='' || $('#bast_date').datebox('getValue') =='' || $('#dasar_pajak').val() =='' || $('#po').val() =='' || $('#pic').val() =='' || $('#email').val() =='' || $('#upload').val() =='') {
      alert('Form Wajib Diisi Semua');
  } else {
      
      var temp = document.getElementById("upload");
      temp = temp.files[0];
      temp = temp.name;

        var formData = new FormData( $("#fm")[0] );
        $.ajax({
                url : 'ekspedisi_faktur_pajakAct.php?act=upload',
                type : 'POST',
                data : formData,
                dataType : 'json',
                async : false,
                cache : false,
                contentType : false,
                processData : false,
                success : function(data) {
                  if (data.success===true) {
                      
                        var _tmpMessage = [data.pesan];
                        alert(_tmpMessage);
                        $("#GAMBAR").val(data.gambar)                 
                      } else {
                        var _tmpMessage = [data.pesan];
                        alert(_tmpMessage);
                      }
    //                  bootbox.alert(_tmpMessage.join('<br />'));
                    }
    //              });
      });
       if($('#GAMBAR').val()!=''){
          var _form_group = $(elm).closest('.form-group');
          var _tbody = $(tabel);
          var _org = $('#nm_org').val();
          var _noFaktur = $('#nofaktur').val(); 
          var _tglFaktur = $('#faktur_date').datebox('getValue');
          var _tglBAST = $('#bast_date').datebox('getValue');
          var _dasarPajak = $('#dasar_pajak').val();
          var _nama = $('#pic').val();
          var _email = $('#email').val();
          var _po = $('#po').val();    
          var _GAMBAR = $('#GAMBAR').val();    
          var _urut;
          var _jmlTr = _tbody.find('tr').length + 1;
          var x = _tbody.find('tr').length;
          var _numberPattern = /\d+/g;
          
          teks = '<tr>';
          teks += '<td>'+_jmlTr+'</td>';
          teks += '<td class="text-center"><input type="text" readonly type="hidden" name="nm_org[]" value="'+_org+'"> </td>';
          teks += '<td class="text-center"><input type="text" readonly name="no_faktur[]" value="'+_noFaktur+'"> </td>';
          teks += '<td class="text-center"><input type="text" readonly name="tgl_faktur[]" value="'+_tglFaktur+'"> </td>';
          teks += '<td class="text-center"><input type="text" readonly name="tgl_bast[]" value="'+_tglBAST+'"> </td>';        
          teks += '<td class="text-center"><input type="text" readonly name="dasar_pajak[]" value="'+_dasarPajak+'"> </td>';        
          teks += '<td class="text-center"><input type="text" readonly name="po[]" value="'+_po+'"> </td>';        
          teks += '<td class="text-center"><input type="text" readonly name="nama[]" value="'+_nama+'"> </td>';        
          teks += '<td class="text-center"><input type="text" readonly name="email[]" value="'+_email+'"> </td>';        
          teks += '<td class="text-center">'+'<input type="hidden" name="file_gambar[]" value="'+temp+'"><input type="hidden" name="link_file_gambar[]" value="'+_GAMBAR+'"><a href="'+_GAMBAR+'" target="_blank">'+temp+'</a>'+'</td>';        
          teks += '<td class="text-center"><button onclick="removeRow(this)">Hapus</button></td>';
          teks += '</tr>';

          $(teks).appendTo(_tbody);
          
          nm_org[x]       = _org;
            email[x]        = _email;
            pic[x]          = _nama;
            faktur_date[x]  = _tglFaktur;
            bast_date[x]    = _tglBAST;
            nofaktur[x]     = _noFaktur;  
            dasar_pajak[x]  = _dasarPajak;
            po[x]           = _po;
            gambar[x]       = _GAMBAR;
            tot               = nofaktur.length;
          
          $('#org').textbox('setValue','');
          $('#nofaktur').textbox('setValue','');
          $('#po').textbox('setValue','');
          $('#faktur_date').datebox('setValue','');
          $('#bast_date').datebox('setValue','');
          $('#dasar_pajak').textbox('setValue','');
          $('#pic').textbox('setValue','');
          $('#email').textbox('setValue','');
          $('#upload').filebox('setValue','');
          
        } else {
            alert('Dokumen gagal diupload');
        }
    }
    
  }
  
  function removeRow(elm){
    var _tr = $(elm).closest('tr');
    var _tbody = _tr.closest('tbody');
    _tr.remove();
    setNomerUrut(_tbody);
  }
  
  function setNomerUrut(_tbody){
    var i = 1;
    _tbody.find('tr').each(function(){
      $(this).find('td:first').text(i++);
    });
  }


</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
