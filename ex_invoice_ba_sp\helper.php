<?php
if (file_exists('../include/ex_fungsi.php')) {
  include_once '../include/ex_fungsi.php';
} elseif (file_exists('../../include/ex_fungsi.php')) {
  include_once '../../include/ex_fungsi.php';
}

$base_url = "https://dev-app.sig.id/dev/sd/sdonline/"; // dev
// $base_url = "https://csms.sig.id/sdonline/"; // prod
define("BASE_URL", $base_url);

if (!function_exists('get_base_url')) {
  function get_base_url() {
    return BASE_URL;
  }
}

if (!function_exists('get_base_home')) {
  function get_base_home() {
    return BASE_URL . 'login.php';
  }
}

function get_pejabat_eks_manual(){
    $fungsi=new ex_fungsi();
    $conn=$fungsi->ex_koneksi();

    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Ekspeditur - Manual BASTP'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

class PDFExporter {
  public function beritaAcara($no_ba) {
    $urlPrint = 'http://************/dev/sd/sdonline/ex_ba_sp/print_ba_new.php?no_ba=' . $no_ba; // dev
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_ba_sp/print_ba_new.php' . '?no_ba=' . $no_ba; // prod
    // $urlPrint = 'http://***********/dev/sd/sdonline/ex_ba/print_ba_new.php?no_ba=' . $no_ba;
    // $urlPrint = 'http://**********/sdonline/ex_ba/print_ba_new.php?no_ba=' . $no_ba;
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlPrint . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlRenderPdf . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }

  public function beritaAcara2($no_ba, $server = 'dev') {
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba2.php?no_ba=" . $no_ba . '&server=' . $server;

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlConverter . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceKuitansi($no_ba) {
    $user_id = $_SESSION['user_id'];

    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id"; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id";
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=0000000900&user_id=23386";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";
    // $urlConverter = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSPDenda($no_ba) {
    $user_id = $_SESSION['user_id'];

    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSpDendaTanggalSama($no_ba) {
    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSpDendaTanggalBeda($no_ba) {
    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function draftPPL($no_invoice) {
    $urlPrint = 'http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download'; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download'; // prod
    // $urlPrint = 'http://***********/dev/sd/sdonline/ex_invoice_ba/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download';
    // $urlPrint = 'http://**********/sdonline/ex_invoice_ba/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download';
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlPrint . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlRenderPdf . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }
}

class ApiEMeterai {
  // dev synxchro
  const URL_LOGIN                     = 'https://dev-integrasi-api.sig.id/peruri/ematerai/login';
  //const URL_UPLOAD_DOC                = 'http://skedull.sig.id/bi/skedul/e_invoice/peruri_emeterei.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
  const URL_UPLOAD_DOC                = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
  const URL_GENERATE_SERIAL_NUMBER    = 'https://dev-integrasi-api.sig.id/peruri/ematerai/generate_serial_number';
  const URL_STAMPING                  = 'https://dev-integrasi-api.sig.id/peruri/ematerai/stamping';
  const URL_DOWNLOAD_DOC_PREFIX       = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

  // dev
  // const URL_LOGIN                     = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservicestg.e-meterai.co.id/api/users/login';
  // //const URL_UPLOAD_DOC                = 'http://skedull.sig.id/bi/skedul/e_invoice/peruri_emeterei.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
  //  const URL_UPLOAD_DOC                = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileuploadstg.e-meterai.co.id/uploaddoc2';
  // const URL_GENERATE_SERIAL_NUMBER    = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampv2stg.e-meterai.co.id/chanel/stampv2';
  // const URL_STAMPING                  = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampservicestg.e-meterai.co.id/keystamp/adapter/docSigningZ';
  // const URL_DOWNLOAD_DOC_PREFIX       = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

  // prod
  // const URL_LOGIN                     = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservice.e-meterai.co.id/api/users/login';
  // const URL_UPLOAD_DOC                = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://fileupload.e-meterai.co.id/uploaddoc2';
  // const URL_GENERATE_SERIAL_NUMBER    = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampv2.e-meterai.co.id/chanel/stampv2';
  // const URL_STAMPING                  = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://stampservice.e-meterai.co.id/keystamp/adapter/docSigningZ';
  // const URL_DOWNLOAD_DOC_PREFIX       = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai_download.php?url=';

  private $jwt = null;
  
  // penambahan fungsi untuk write file json untuk log
  public function cek($nama, $proses, $status, $response) {
    $createdAt = new DateTime('now');
    $createdAt = $createdAt->format('Y-m-d H:i:s');
    $fungsi = new ex_fungsi();
    $this->connection = $fungsi->ex_koneksi();
    // $cek=implode('=', $response);
    //       var_dump($response);  
    // die;
    
    if ($response != 'false') {
      if (isset($response->statusCode) && $response->statusCode == '00' || isset($response->status) && $response->status == 'True') {
        $status = "BERHASIL";
      } elseif (isset($response->status) && $response->status == 'False') {
        $status = $response->errorMessage;
      } else {
        $status = "GAGAL DARI E-MATERAI";
      }
    } 

    $response = json_encode($response);
    
    if ($response == 'false') {
      $response = "Error: Couldn t resolve host";
    }

    $qu = "INSERT INTO ZREPORT_LOG_SERVICE (ID, GROUP_LOG,REQUEST, RESPON, BY_LOG,LOG_DATE, TOKEN, DELETE_MARK) VALUES ('', '1', '$proses','$response','$nama', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), '$status', '1')";
    $sql = oci_parse($this->connection, $qu);
    oci_execute($sql);
  }
  
  public function setJwt($jwt) {
    $this->jwt = $jwt;
  }

  public function login($email, $password) {
    $headers = array(
      'Content-Type: application/json',
    );

    $data = array(
      'user' => $email,
      'password' => $password,
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::URL_LOGIN);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 6);
    // Disable SSL verification for local development
    // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
      $this->cek("E-MATERAI", "GET TOKEN", "GAGAL", curl_errno($ch));
      throw new Exception('Error: ' . curl_error($ch));
    }

    curl_close($ch);

    $response = json_decode($response);
    $this->cek("E-MATERAI", "GET TOKEN", "BERHASIL", $response);
    return $response;
  }

  public function uploadDocument($file) {
    echo "1m";
    $headers = array(
      'Content-Type: multipart/form-data',
      'Authorization: Bearer ' . $this->jwt,
    );

    $data = array(
      'file' => $file,
      'token' => $this->jwt,
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::URL_UPLOAD_DOC);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
      $this->cek("E-MATERAI", "UPLOAD DOCUMENT", "GAGAL", curl_exec($ch));
      throw new Exception('Error: ' . curl_error($ch));
    }

    curl_close($ch);

    $response = json_decode($response);
    $this->cek("E-MATERAI", "UPLOAD DOCUMENT", "BERHASIL", $response);

    return $response;
  }

  public function generateSerialNumber($data) {
    echo "2m";
    $headers = array(   
      'Content-Type: application/json',
      'Authorization: Bearer ' . $this->jwt,
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::URL_GENERATE_SERIAL_NUMBER);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 6);  // paksa TLS1.2
    // Disable SSL verification for local development
    // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    // curl_setopt($ch, CURLOPT_SSLVERSION, 6);
    // curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    // curl_setopt($ch, CURLOPT_SSLVERSION,3);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
      $this->cek("E-MATERAI", "GENERATE SERIAL NUMBER", "GAGAL", curl_errno($ch));
      throw new Exception('Error: ' . curl_error($ch));
    }

    curl_close($ch);

    $response = json_decode($response);
    $this->cek("E-MATERAI", "GENERATE SERIAL NUMBER", "BERHASIL", $response);
    return $response;
  }

  public function stamping($data) {
    echo "3m";
    $headers = array(
      'Content-Type: application/json',
      'Authorization: Bearer ' . $this->jwt,
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::URL_STAMPING);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //curl_setopt($ch, CURLOPT_SSLVERSION,3);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
      $this->cek("E-MATERAI", "STAMPING", "GAGAL", curl_errno($ch));
      throw new Exception('Error: ' . curl_error($ch));
    }

    curl_close($ch);

    $response = json_decode($response);
    $this->cek("E-MATERAI", "STAMPING", "BERHASIL", $response);
    return $response;
  }
  
  public function downloadDocument($url) {
    echo "4m";
    $headers = array(
      'Content-Type: application/json',
      'Authorization: Bearer ' . $this->jwt,
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, self::URL_DOWNLOAD_DOC_PREFIX . $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
      $this->cek("E-MATERAI", "DOWNLOAD DOCUMENT", "GAGAL", curl_errno($ch));
      throw new Exception('Error: ' . curl_error($ch));
    }

    curl_close($ch);
    $this->cek("E-MATERAI", "DOWNLOAD DOCUMENT", "BERHASIL", $response);
    return $response;
  }
}

class EMeterai {
  public function __construct() {
    $fungsi = new ex_fungsi();
    $this->connection = $fungsi->ex_koneksi();

    $this->jwt = $this->getLatestJwt()->TOKEN;

    $this->api = new ApiEMeterai();
    $this->api->setJwt($this->jwt);
  }

  private function getActiveCredentials() {
    $query = "SELECT * FROM EX_BA_USER_EMATERAI WHERE STATUS_AKTIF = 1";
    $sql = oci_parse($this->connection, $query);
    oci_execute($sql);
    $user = oci_fetch_object($sql);

    if (!$user) {
      throw new Exception('User EMeterai tidak ditemukan');
    }

    return $user;
  }

  private function getLatestJwt() {
    $query = "SELECT * FROM (SELECT ID, TOKEN, TO_CHAR(EXPIRED_AT, 'yyyy-mm-dd hh24:mi:ss') AS EXPIRED_AT, CREATED_AT FROM EX_PERURI_METERAI_JWT ORDER BY EXPIRED_AT DESC) where ROWNUM = 1";
    $sql = oci_parse($this->connection, $query);
    oci_execute($sql);
    $data = oci_fetch_object($sql);

    if ($data) {
      return $data;
    }

    return null;
  }

  private function hasActiveJwt() {
    $data = $this->getLatestJwt();

    // Check if token is not expired
    $expiredAt = new DateTime($data->EXPIRED_AT);
    $currentTime = new DateTime('now');

    $currentDateTimeGreater = $currentTime > $expiredAt;
    $isSameDate = $expiredAt->format('Y-m-d') == $currentTime->format('Y-m-d');
    $isDiff2Hours = intval($expiredAt->format('H')) - intval($currentTime->format('H')) <= 2;

    if ($currentDateTimeGreater || ($isSameDate && $isDiff2Hours)) {
      return false;
    }

    return true;
  }

  private function refreshJwt() {
    $user = $this->getActiveCredentials();

    $response = $this->api->login($user->EMAIL, $user->PASSWORD);

    $token = $response->token;
    if (empty($token)) {
      return false;
    } else {
      $createdAt = new DateTime('now');
      $createdAt = $createdAt->format('Y-m-d H:i:s');
      $expiredAt = new DateTime('now');
      $expiredAt->modify('+12 hours');
      $expiredAt = $expiredAt->format('Y-m-d H:i:s');

      $query = "INSERT INTO EX_PERURI_METERAI_JWT (TOKEN, CREATED_AT, EXPIRED_AT) VALUES ('$token', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), TO_DATE('$expiredAt', 'YYYY-MM-DD HH24:MI:SS'))";
      $sql = oci_parse($this->connection, $query);
      oci_execute($sql);

      $this->api->setJwt($token);
    }
  }

  /**
   * Merefresh token jwt.
   *
   * @return bool true jika sukses refresh, false jika token masih aktif.
   */
  public function refreshJwtIfExpired() {
    if (!$this->hasActiveJwt()) {
      $this->refreshJwt();
      return true;
    }

    return false;
  }

  public function uploadDocument($file) {
    $response = $this->api->uploadDocument($file);

    if ($response->statusCode == '00') {
      return $response;
    }

    throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
  }

  public function generateSerialNumber($data) {
    $sendData = array(
      /* [M] Unik ID dokumen yang didapatkan dari API Upload Doc */
      'idfile' => $data['idfile'],

      /* [M] Isi dengan true */
      'isUpload' => true,

      /* [M] Kode jenis dokumen sesuai dengan pilihan yang disediakan dari hasil API Jenis Dokumen */
      'namadoc' => $data['namadoc'],

      /* [M] Nomor dokumen (Jika tidak ada dapat diisi 0) */
      'nodoc' => isset($data['nodoc']) ? $data['nodoc'] : '0',

      /* [M] Tanggal dokumen dengan format [YYYY-MM-DD] (Mandatory khusus akun Pemungut) */
      'tgldoc' => $data['tgldoc'],

      /* [O] Nilai transaksi meterai (Mandatory khusus akun Pemungut) */
      // 'nilaidoc' => $data['nilaidoc'],

      /* [O] Jenis identitas pihak terutang (KTP/NPWP) (Mandatory khusus akun Pemungut) (Mandatory apabila noidentitas dan/atau namedipungut diisi) */
      // 'namejidentitas' => $data['namejidentitas'],

      /* [O] Nomor identitas pihak terutang (NIK KTP/No. NPWP) (Mandatory khusus akun Pemungut) (Mandatory apabila namejidentitas dan/atau namedipungut diisi) */
      // 'noidentitas' => $data['noidentitas'],

      /* [O] Nama pihak terutang (Mandatory khusus akun Pemungut) (Mandatory apabila noidentitas dan/atau namejidentitas diisi) */
      // 'namedipungut' => $data['namedipungut'],

      /* [M] Nama dokumen asli */
      'namafile' => $data['namafile'],

      /* [M] Isi dengan false */
      'snOnly' => false,
    );

    $response = $this->api->generateSerialNumber($sendData);

    if (isset($response->statusCode) && $response->statusCode == '00') {
      return $response;
    }

    // throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
    if (is_object($response)) {
      $msg = (!empty($response->message) ? $response->message : (!empty($response->errorMessage) ? $response->errorMessage : 'Unknown error'));
    } else {
      // fallback jika bukan object
      $msg = 'Invalid API response: ' . var_export($response, true);
    }
    throw new Exception('Error: ' . $msg);
  }

  public function stamping($data) {
    $sendData = array(
      /* [M] Isi dengan false, untuk memunculkan link download */
      'onPrem' => false,

      /* [M] Unik ID dokumen yang didapatkan dari API Upload Doc */
      'docId' => $data['docId'],

      /* [M] Keterangan proses pembubuhan */
      'certificatelevel' => $data['certificatelevel'],

      /* [M] Path folder yang berisi dokumen yang nantinya sudah selesai dibubuhkan e-meterai
         Format: "/sharefolder/final_(parameter id yang didapat dari API uploaddoc).pdf" */
      'dest' => $data['dest'],

      /* [M] Password dokumen (jika ada) */
      'docpass' => $data['docpass'],

      /* [M] Default field digital stamp yang perlu diisi dengan informasi lokasi transaksi, sebagai referensi */
      'location' => $data['location'],

      /* [M] Nama stempel */
      'profileName' => $data['profileName'],

      /* [O] Default field digital stamp yang diisi (opsional) sesuai dengan kebutuhan, sebagai referensi */
      'reason' => $data['reason'],

      /* [M] Path folder yang berisi file QR Image yang akan dibubuhkan pada dokumen
         Format: "/sharefolder/qr_(nama file qr image yang didapat dari API generate SN).png" */
      'spesimenPath' => $data['spesimenPath'],

      /* Path folder yang berisi dokumen yang akan dibubuhkan e-meterai
         Format: "/sharefolder/doc_(parameter id yang didapat dari API uploaddoc).pdf" */
      'src' => $data['src'],

      /* Koordinat specimen QR pada file PDF yang akan dibubuhkan */
      'visLLX' => $data['visLLX'],
      'visLLY' => $data['visLLY'],
      'visURX' => $data['visURX'],
      'visURY' => $data['visURY'],

      /* Informasi halaman file PDF tempat specimen QR dibubuhkan. */
      'visSignaturePage' => $data['visSignaturePage'],

      /* Token JWT yang didapat dari login */
      'jwToken' => $this->jwt,

      /* Serial number yang didapatkan dari API generate SN */
      'refToken' => $data['refToken'],
    );

    $response = $this->api->stamping($sendData);

    if ($response->errorCode == '00') {
      return $response;
    }

    throw new Exception('Error: ' . ($response->message ? $response->message : $response->errorMessage));
  }

  public function downloadDocument($url) {
    $response = $this->api->downloadDocument($url);

    return $response;
  }
}

class ApiESign
{
    // dev
    const API_KEY = '0706e266-7901-4fe4-ac1e-3cc357afbf79';//'8b6a21b7-ec21-41d0-a7c3-e5c32c3dd494';
    // prod
    // const API_KEY = '1e911ed0-a9a8-4bb3-b0e2-00739828ba92';
    const SYSTEM_ID = 'SIG';//'PERURI-DEVELOPMENT'; 

    // dev
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/jwtSandbox/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v1';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:9044/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';
    
    // prod
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/jwt/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/signing/v2';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureSIG/1.0/checkDocumentStatus/v1';

    // dev 2024
    // const URL_GENERATE_JWT      = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/getJsonWebToken/v1';
    // const URL_UPLOAD_DOCUMENT   = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    // const URL_GET_OTP           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    // const URL_SIGNING           = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v2';
    // const URL_DOWNLOAD_DOCUMENT = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    // const URL_CHECK_CERTIFICATE = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    // const URL_CHECK_DOC = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apg.peruri.co.id:9055/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';    

    // dev synxchro
    // const URL_GENERATE_JWT      = 'https://dev-integrasi-api.sig.id/peruri/esign/jwt';
    // const URL_UPLOAD_DOCUMENT   = 'https://dev-integrasi-api.sig.id/peruri/esign/senddocument';
    // const URL_GET_OTP           = 'https://dev-integrasi-api.sig.id/peruri/esign/getotp';
    // const URL_SIGNING           = 'https://dev-integrasi-api.sig.id/peruri/esign/signing';
    // const URL_DOWNLOAD_DOCUMENT = 'https://dev-integrasi-api.sig.id/peruri/esign/downloaddocument';
    // const URL_CHECK_CERTIFICATE = 'https://dev-integrasi-api.sig.id/peruri/esign/checkcertificate';
    // const URL_CHECK_DOC         = 'https://dev-integrasi-api.sig.id/peruri/esign/checkdocumentstatus';    

    //dev terbaru setelah case CPNS
     const URL_GENERATE_JWT      = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/jwtSandbox/1.0/getJsonWebToken/v1';
    //const URL_GENERATE_JWT      = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php';
    const URL_UPLOAD_DOCUMENT   = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/sendDocument/v1';
    const URL_GET_OTP           = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/getOtp/v1';
    const URL_SIGNING           = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/signing/v1';
    const URL_DOWNLOAD_DOCUMENT = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/downloadDocument/v1';
    const URL_CHECK_CERTIFICATE = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/checkCertificate/v1';
    const URL_CHECK_DOC         = 'https://skedul.sig.id/bi/skedul/e_invoice/peruri_esign.php?url=https://apgdev.peruri.co.id:19044/gateway/digitalSignatureFullJwtSandbox/1.0/checkDocumentStatus/v1';    



    private $jwt = null;

    // penambahan fungsi untuk write file json untuk log
    public function cek($nama, $proses,$status,$response){
        
               $createdAt = new DateTime('now');
            $createdAt = $createdAt->format('Y-m-d H:i:s');
      $fungsi = new ex_fungsi();
        $this->connection = $fungsi->ex_koneksi();
        // $cek=implode('=', $response);
        //       var_dump($response);  
        // die;
           if($response != 'false'){
           if($response->resultCode == '0'){
               if(isset($response->data) && isset($response->data->base64Document) ){
                   //potong base64 jika ada menjadi 50 
                  $response=$response->resultDesc;
               }
           $status="BERHASIL";
           }elseif($response->resultCode == 'BP-002'){
               $status ="Dokumen Masih Menunggu Ditandatangani";
              
            } elseif($response->resultCode == 'BP-003'){
                   $status = 'Dokumen sudah lebih dari 3 hari tidak ditandatangan (expired)';
               }else{
               $status ="GAGAL DARI E-SIGN";
           }
       } 

      $response = json_encode($response);
      
      if($response == 'false'){
      
          $response ="Error: Couldn t resolve host";
      }

        $qu="INSERT INTO ZREPORT_LOG_SERVICE (ID, GROUP_LOG,REQUEST, RESPON, BY_LOG,LOG_DATE, TOKEN, DELETE_MARK) VALUES ('', '1', '$proses','$response','$nama', TO_DATE('$createdAt', 'YYYY-MM-DD HH24:MI:SS'), '$status', '1')";
       $sql = oci_parse($this->connection, $qu);
            oci_execute($sql);
    }


    public function setJwt($jwt)
    {
        $this->jwt = $jwt;
    }

    public function generateJwt()
    {
        echo "1";
        
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
        );

        $data = array(
            'param' => array(
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_GENERATE_JWT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);
        // echo 'respon JWT '.$response;
        //die;

        if (curl_errno($ch)) {
            $this->cek("E-SIGN","GENERATE JWT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
             $this->cek("E-SIGN","GENERATE JWT","BERHASIL",$resp);
        return $response->data;
    }

    public function uploadDocument($data)
    {
        echo "2 ";
       
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_UPLOAD_DOCUMENT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);


        if (curl_errno($ch)) {
         $this->cek("E-SIGN","UPLOAD DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
           $this->cek("E-SIGN","UPLOAD DOCUMENT","BERHASIL", $resp);
        return $response;
    }

    public function getOtp($orderId)
    {
        echo "3";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_GET_OTP);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","GET OTP","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
         $this->cek("E-SIGN","GET OTP","BERHASIL",$resp);
        return $response;
    }

    public function signing($data)
    {

        echo "4";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_SIGNING);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","SIGNING ","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
          $this->cek("E-SIGN","SIGNING","BERHASIL",$resp);
        return $response;
    }

    public function downloadDocument($orderId)
    {
        echo "5";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_DOWNLOAD_DOCUMENT);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","DOWNLOAD DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
        $this->cek("E-SIGN","DOWNLOAD DOCUMENT","BERHASIL",$resp);
        return $response;
    }

    public function checkCertificate($email)
    {
        echo "6";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'email' => $email,
                'systemId' => self::SYSTEM_ID,
            ),
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_CHECK_CERTIFICATE);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","CHECK SERTIFICATE","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);

        $resp=json_decode($response);
        $response = json_decode($response);
          $this->cek("E-SIGN","CHECK SERTIFICATE","BERHASIL",$resp);
        return $response;
    }

    public function checkDoc($orderId)
    {
        echo "7";
        $headers = array(
            'Content-Type: application/json',
            'x-Gateway-APIKey: ' . self::API_KEY,
            'Authorization: Bearer ' . $this->jwt,
        );

        $data = array(
            'param' => array(
                'orderId' => $orderId,
                'systemId' => self::SYSTEM_ID,
            ),
        );
        echo json_encode($data);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::URL_CHECK_DOC);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
              $this->cek("E-SIGN","CHECK DOCUMENT","GAGAL",curl_errno($ch));
            throw new Exception('Error: ' . curl_error($ch));
        }

        curl_close($ch);
        $resp=json_decode($response);
        $response = json_decode($response);
        
        $this->cek("E-SIGN","CHECK DOCUMENT","BERHASIL",$resp);
      
        return $response;
    }
}
?>