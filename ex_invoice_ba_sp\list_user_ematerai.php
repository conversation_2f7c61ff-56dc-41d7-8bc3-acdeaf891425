<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
include_once('../include/e_sign.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=171;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}

$page="list_user_ematerai.php";
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
	//echo $vendor;

function callLoginMaterai($email, $password){
	
	$url = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservicestg.e-meterai.co.id/api/users/login';// DEV
	// $url = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservice.e-meterai.co.id/api/users/login';// PROD
 
	$headers = array(
		'Content-Type: application/json',
	);

	$data = array(
		'user' => $email, 
		'password' =>$password
	);

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

	$response = curl_exec($ch);

	if (curl_errno($ch)) {
		throw new Exception('Error: ' . curl_error($ch));
	}

	curl_close($ch);

	$response = json_decode($response);
	return $response ;
}	

   
if((isset($_POST["add_user"]))) { 
	$email = $_POST["email"];
	$password = $_POST["password"]; 
	$status_aktif = 1; 
	
	 
	$response =  callLoginMaterai($email, $password);
   
   if($response->statusCode == '00'){
	   $field_names = array('EMAIL', 'PASSWORD', 'VENDOR', 'STATUS_AKTIF', 'CREATED_AT', 'CREATED_BY');
		$field_data = array("$email", "$password", "$vendor", "$status_aktif", "SYSDATE", "$user_id");
		$tablename = "EX_BA_USER_EMATERAI";
		$fungsi->insert($conn, $field_names, $field_data, $tablename);
		 
		
	 
		$pesan = "Berhasil Tambah User";
		echo '<script language="javascript">';
		echo 'alert("'.$pesan.'");'; 
		echo '</script>';
   }else{  
		$pesan = $response->result;
		echo '<script language="javascript">';
		echo 'alert("'.$pesan.'");'; 
		echo '</script>';
   }
	

	 
 
  
	// $action = "cancel_invoice_bag_darat";
	// include ('formula.php'); 
	
}


   
if((isset($_POST["rubah_status"]))) { 
	$ide = $_POST["ide"];
	$hari_ini = date("Y-m-d");
	
	$status_aktif = $_POST["rubah_status"];
	$status_name = ($_POST["rubah_status"] == 1 ? "Active" : "Non Active");
	
	$field_names = array('STATUS_AKTIF', 'UPDATED_AT', 'UPDATED_BY');
	$field_data = array("$status_aktif", "SYSDATE", "$user_id");
	$tablename = "EX_BA_USER_EMATERAI";
	$field_id = array('ID');
	$value_id = array("$ide");
	$fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
	
 
	$pesan = "Berhasil ".$status_name." User";
	echo '<script language="javascript">';
	echo 'alert("'.$pesan.'");'; 
	echo '</script>';
	 
  
	
}

#$action_page=$fungsi->security($conn,$user_id,$halaman_id);




if(isset($_POST["cek_kuota"]) && $_POST["cek_kuota"] == '') {


$email = $_POST['emailc']; 
$password = $_POST['passworde']; 
$response =  callLoginMaterai($email, $password); 
   if($response->statusCode == '00'){	
	   $url = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservicestg.e-meterai.co.id/function/saldopos'; // DEV
	//    $url = 'http://skedul.sig.id/bi/skedul/e_invoice/peruri_emeterai.php?url=https://backendservice.e-meterai.co.id/function/saldopos';// PROD
	 
		$headers = array(
			'Content-Type: application/json',
			'x-Gateway-APIKey: a62914f9-9b30-4fb9-844d-e61ba4ea5899',
			'csrf-token: application/json', 
			'Authorization: Bearer ' . $response->token,
		);

		$data = array(
			 
		);

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		$responseC = curl_exec($ch);

		if (curl_errno($ch)) {
			throw new Exception('Error: ' . curl_error($ch));
		}

		curl_close($ch);

		$responsE = json_decode($responseC); 
    }else{  
		$pesan = $response->result;
		echo '<script language="javascript">';
		echo 'alert("'.$pesan.'");'; 
		echo '</script>';
   }
	
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 30%;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert" style="font-size: 20px;font-weight: bold;">
				<span>Cek Kuota <? echo $emailc; ?></span> <br>
				<span>Sisa Kuota E-Materai</span>
			</div>
			<div class="alert alert-warning" role="alert" align="center" >
			<span style ="   font-size: 50px; font-weight: bold; color: #2300ff;" ><? echo $responsE->result->saldo; ?></span>
			</div> 
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


if(isset($_POST["addUser"]) && $_POST["addUser"] == '') { 
	
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 40%;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert" style="font-size: 20px;font-weight: bold;">
				Form Tambah
			</div>
			<div class="alert alert-warning" role="alert" align="left" >
			<table>
			<tr>
				<td><label for="fname">Email:</label></td>
				<td><input type="text" id="email" name="email"  size="30" ></td>
			</tr>  
			<tr>
				<td>&nbsp;</td>
				<td>&nbsp;</td>
			</tr>  
			<tr >
				<td><label for="fname">Password:</label></td>
				<td><input type="password" id="password" name="password"  size="30" ></td>
			</tr>
			</table> 
			</div>
			<button type="submit" name="add_user" value="add_user" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Simpan&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


$username = $_POST['username']; 
$emaile = $_POST['emaile']; 
 
$currentPage="list_user_ematerai.php";
$komen=""; 
	if($vendor==""){
		$sql= "SELECT * from EX_BA_USER_EMATERAI 
		";
		//AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED')
	}else { 
		$sql= "SELECT * from EX_BA_USER_EMATERAI WHERE EMAIL IS NOT NULL";
		
		if($emaile!=""){ 
			$sql.=" AND ( EMAIL LIKE '$emaile' OR EMAIL LIKE '$emaile' ) ";
		}
		if($vendor!=""){ 
			$sql.=" AND ( VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
		}
		  
		$sql.="  ORDER BY EMAIL ASC";
		
	}
	//	echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
	$total_tagihan=0; 
	while($row=oci_fetch_array($query)){   
	
		$id_user_v[] 	=$row[ID]; 
		$email_v[] 		=$row[EMAIL]; 
		$password_v[] 		=$row[PASSWORD]; 
		$status_id_v[] 	=$row[STATUS_AKTIF]; 
		$status_v[] 	=($row[STATUS_AKTIF]== 1 ? 'Active' : 'Inactive'); 
	 
				 
	} 
	$total=count($id_user_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

 



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">List User E-Materai </th>
</tr></table></div>
<?
	if($total<1){
?>
<!--
<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search</th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">Email</td>
      <td class="puso">:</td>
      <td><input type="text" id="emaile" name="emaile" value="<?=$emaile?>"/></td>
    </tr>
     
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
-->
<? } ?>
<br />
<br />
<?
	//if($total<1){		
		

?>
<!--<form id="data_claim" name="data_claim" method="post" action="komentar.php" >-->

	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="center" colspan="4"><span>
	<form id="data_claim" name="data_claim" method="post" action="" > <button type="submit" name="addUser" style="font-size: 14px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #2f51ff; color: #fff; border: 1px solid #000; border-radius: 4px;">Tambah</button> </form>
	
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="70%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data User </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="70%" align="center" class="adminlist">
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td> 
		<td align="center"><strong >Email </strong></td> 
		<td align="center"><strong >Status </strong></td>  
		<td align="center"><strong>Aksi</strong></td> 
                 
      </tr >
	  
  <? $total_tagihan = 0;    
  for($i=0; $i<$total;$i++) {
		
		//if($no_ba_v[$i]!=''){ 
			$b=$i+1;
				if(($i % 2) == 0)	{	 
				echo "<tr class='row0' id='$rowke' >";
					}
				else	{	
				echo "<tr class='row1'  id='$rowke' >";
					}	
						$orgCom="orgke".$i;        
				?>     
				
				<td align="center"><? echo $b; ?></td>  
				<td align="center"><? echo $email_v[$i]; ?></td> 
				<td align="center"><? echo $status_v[$i]; ?></td> 
				<td> 
					<? if($status_id_v[$i] == '1' ){ ?> 
					<center><form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $id_user_v[$i]; ?>" name="ide"><button type="submit" name="rubah_status" value="0" style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #666666; color: #fff; border: 1px solid #000; border-radius: 4px;">Non Active</button> </form></center>
					<center><form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $password_v[$i]; ?>" name="passworde"><input type="hidden" value="<? echo $email_v[$i]; ?>" name="emailc"><button type="submit" name="cek_kuota" style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #0c7c18; color: #fff; border: 1px solid #000; border-radius: 4px;">Check Quota</button> </form></center>
					<? }?>
					<? if($status_id_v[$i] == '0' ){ ?> 
					<center><form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $id_user_v[$i]; ?>" name="ide"><button type="submit" name="rubah_status" value="1" style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #ff7800; color: #fff; border: 1px solid #000; border-radius: 4px;">Activate</button> </form></center>
					<? }?>
					
					
					 
				</td> 
						
				</tr> 
		  <? }?>  
		 

	  
	</table>
	
	
	
	</div>
	<?
	//}?>
<div align="center">
<?
echo $komen;

?></div>
		<!--</form>-->

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
