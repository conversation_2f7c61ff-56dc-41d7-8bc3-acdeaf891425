<?php
/*
 * upload master supir expeditur
 * @yopi satria
 */

session_start();
include ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
$halaman_id=3197;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $user_org;
}

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$dirr = $_SERVER['PHP_SELF']; 
// $halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$no_ba = $_GET['no_ba'];
$importtargetVolume='reverse_ba.php';
$waktu=date("d-m-Y");



function formatTGL($tgl){
    $a = explode("/", $tgl);
    $tgal = $a[3].$a[2].$a[1];
    return $tgal;
}


if(isset ($_POST['Import'])){
       
                    $user_name = $_SESSION['user_name'];
                    $keterangan = $_POST['keterangan'];
                    // $alasan_ba = $_POST['alasan_ba'];

                     $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2','NO_BA','TANGGAL_BA');
                    $field_data = array("OPEN", "SYSDATE", "$user_name", "OPEN","","");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('ALASAN_REJECT', 'STATUS_BA','LAST_UPDATE_DATE','LAST_UPDATED_BY','TIPE_ALASAN');
                    $field_data = array("$keterangan", "1", "SYSDATE", "$user_name","4");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT','KOMENTAR_REJECT');
                    $field_data = array("$no_ba","1","REVERSE","$user_id","SYSDATE","$keterangan");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
                    echo "<script>alert('Berhasil Reverse BA');</script>";
                    echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =lihat_ba_hdr_ex_report.php'>";
              
    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Reverse BA</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Reverse BA</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="reverse_ba.php?no_ba=<?=$no_ba?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
       <!--  <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih Alasan</td>
            <td class="puso">:</td>
            <td> <select name="alasan_ba" required>
                <option value="">Pilih Alasan Reject</option>
                <option value="1">SPJ Tidak Sesuai</option>
                <option value="2">Upload SPJ</option>
                <option value="3">Other</option>
            </select></td>
        </tr> -->
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Alasan Reverse BA</td>
            <td class="puso">:</td>
            <td> <textarea rows="5" name="keterangan" required></textarea></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Submit"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
       <!--  <tr>
            <td class="puso" colspan="3">&nbsp;&nbsp;&nbsp;Download template supir <a href="templete_xls/template_supir.xls">disini</a></td>
           
        </tr> -->
          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>