<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=4892;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];

if($user_org != '5000'){ //*******************************
    $mp_coics=$fungsi->getComin($conn,$user_org);
}else{
    unset($mp_coics);
}
/*echo 'User Org TB_USER :'.$user_org.'<br>';
echo 'mp_cois : '.count($mp_coics).'<br>';*/
//$mp_coics=$fungsi->getComin($conn,$user_org); ************************/
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// }



 

// $action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="list_approval_invoice_sm.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
// if (isset($_POST['vendor']))
// $vendor = $_POST['vendor']; 

$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$no_invoice = $_POST['no_invoice']; 

$currentPage="list_approval_invoice_sm.php";
$komen="";
if(isset($_POST['cari'])){
	$_SESSION['tgl_mulai_inv'] = $_POST['tanggal_mulai'];
	$_SESSION['tgl_end_inv'] = $_POST['tanggal_selesai'];
	$_SESSION['no_inv'] = $_POST['no_invoice'];
	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_invoice == "" ){
		$sql= "SELECT DISTINCT EI.ORG,EI.NO_INVOICE_SAP, EI.NO_INVOICE, EI.NO_INVOICE_EX, EI.NO_VENDOR,EI.NAMA_VENDOR,EI.NO_PAJAK_EX,EI.TRACKING_INV, TO_CHAR(EI.TGL_INVOICE, 'DD-MM-YYYY') AS TGL_INVOICE,EI.INV_DOC_NUMBER,EI.INV_DOC_NUMBER_CONV, ETH.NO_BA, TO_CHAR(ETH.TANGGAL_BA, 'DD-MM-YYYY') AS TANGGAL_BA, ETH.WARNA_PLAT FROM EX_INVOICE EI
		join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
		WHERE EI.DELETE_MARK ='0' AND EI.TRACKING_INV = 90 ";
		
		// if($_SESSION['user_name']=="VERIFIKATOR2"){
			// $sql.=" AND EI.TRACKING_INV = 70 "; 
		// }else if($_SESSION['user_name']=="VERIFIKATOR3"){  
			// $sql.=" AND EI.TRACKING_INV = 90 "; 
		// }
		
		$sql.=" AND (EI.TRACKING_INV >= 70 OR EI.TRACKING_INV = 45) ";

    if($user_name == 'ROY.HARDYANTO' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST <= 50000000 ";
		}else if($user_name == 'RAHMAT.NOVRIYAN' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST BETWEEN 50000001 AND 1000000000 ";
		}else if($user_name == 'VEKKY.ANGGALIA' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST > 1000000000 ";
		}
		
		$sql.=" AND EI.ORG in ($orgIn) AND ETH.NO_BA IS NOT NULL AND EI.NO_INVOICE IS NOT NULL ORDER BY EI.ORG,EI.NO_VENDOR, EI.NO_INVOICE DESC";
	}else {
		$pakeor=0;
		$sql= "SELECT DISTINCT EI.ORG,EI.NO_INVOICE_SAP, EI.NO_INVOICE, EI.NO_INVOICE_EX, EI.NO_VENDOR,EI.NAMA_VENDOR,EI.NO_PAJAK_EX,EI.TRACKING_INV, TO_CHAR(EI.TGL_INVOICE, 'DD-MM-YYYY') AS TGL_INVOICE,EI.INV_DOC_NUMBER,EI.INV_DOC_NUMBER_CONV, ETH.NO_BA, TO_CHAR(ETH.TANGGAL_BA, 'DD-MM-YYYY') AS TANGGAL_BA, ETH.WARNA_PLAT FROM EX_INVOICE EI join EX_TRANS_HDR ETH on ETH.NO_INVOICE = EI.NO_INVOICE
		WHERE EI.TRACKING_INV = 90 AND ";
		// if($vendor!=""){
		// $sql.=" ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
		$pakeor=1;
		$sql.="  EI.DELETE_MARK ='0' ";
		
		if($tanggal_mulai!="" or $tanggal_selesai!=""){

			if ($tanggal_mulai=="")
			$tanggal_mulai_sql = "01-01-1990";
			else
			$tanggal_mulai_sql = $tanggal_mulai;

			if ($tanggal_selesai=="")
			$tanggal_selesai_sql = "12-12-9999";
			else
			$tanggal_selesai_sql = $tanggal_selesai;

			if($pakeor==1){
			$sql.=" AND EI.TGL_APPROVE_KABIRO BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + 1";
			}else{
			$sql.=" EI.TGL_APPROVE_KABIRO BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') + 1";
			$pakeor=1;
			}
		}
		if($no_invoice!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_INVOICE LIKE '$no_invoice' ";
			}else{
			$sql.=" EI.NO_INVOICE LIKE '$no_invoice' ";
			$pakeor=1;
			}
		} 
		// if($_SESSION['user_name']=="VERIFIKATOR2"){
			// $sql.=" AND EI.TRACKING_INV = 70 "; 
		// }else if($_SESSION['user_name']=="VERIFIKATOR3"){  
			// $sql.=" AND EI.TRACKING_INV = 90 "; 
		// }
		
		
		$sql.=" AND (EI.TRACKING_INV >= 70 OR EI.TRACKING_INV = 45) ";
		
		if($user_name == 'ROY.HARDYANTO' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST <= 50000000 ";
		}else if($user_name == 'RAHMAT.NOVRIYAN' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST BETWEEN 50000001 AND 1000000000 ";
		}else if($user_name == 'VEKKY.ANGGALIA' && $user_org == '3000'){
			$sql.=" AND ETH.SHP_COST > 1000000000 ";
		}
		
		$sql.=" AND EI.ORG in ($orgIn) AND ETH.NO_BA IS NOT NULL  AND EI.NO_INVOICE IS NOT NULL ORDER BY EI.ORG, EI.NO_INVOICE DESC";
	} 
	$query= oci_parse($conn, $sql);
	oci_execute($query);
        
        $sqlcek = "SELECT NO_INVOICE FROM KPI_TERIMA_INV_VENDOR A, KPI_TERIMA_ASSINGMENT B, TB_USER_BOOKING C WHERE C.ID = B.ASSING_TO AND B.NO_GROUP_VENDOR = A.NOGROUP_VENDOR
                    AND A.DEL = '0' AND C.ID = '$user_id' AND C.DELETE_MARK = '0' AND C.ASSIGN_TYPE = 'OPERATOR'";
        $querycek = oci_parse($conn, $sqlcek);
        oci_execute($querycek);
        $noinvfilter = array();
        while($datacek=oci_fetch_assoc($querycek)){
            array_push($noinvfilter, $datacek[NO_INVOICE]);
        }
        
        $sqlcek1 = "SELECT ASSIGN_TYPE FROM TB_USER_BOOKING WHERE ID = '$user_id' AND DELETE_MARK = '0'";
        $querycek1 = oci_parse($conn, $sqlcek1);
        oci_execute($querycek1);
        while($datacek1=oci_fetch_assoc($querycek1)){
            $jenisuser = $datacek1[ASSIGN_TYPE];
        }

	while($row=oci_fetch_array($query)){
            if(($jenisuser == 'OPERATOR' && in_array($row[NO_INVOICE], $noinvfilter)) || ($jenisuser != 'OPERATOR')){
                $com[]=$row[ORG]; 
                $no_invoice_sap_v[]=$row[NO_INVOICE_SAP];
                $no_invoice_v[]=$row[NO_INVOICE];
                $no_invoice_in=$row[NO_INVOICE];
                $vendor_v[]=$row[NO_VENDOR];
                $nama_vendor_v[]=$row[NAMA_VENDOR];
                $no_pajak_ex_v[]=$row[NO_PAJAK_EX];
                $TGL_INVOICE_v[]=$row[TGL_INVOICE];
                $tgl_ba_v[]=$row[TANGGAL_BA];
                $no_ba_v[]=$row[NO_BA];
                $warna_plat_v[]=$row[WARNA_PLAT];
                $no_mir7_v[]=$row[INV_DOC_NUMBER];
                $fi_doc_v[]=$row[INV_DOC_NUMBER_CONV];
                $status_v[]=$row[TRACKING_INV];
				$statuse = '';
				if($row[TRACKING_INV]=='30'){
					$statuse = 'REVERSED ';
				}
				if($row[TRACKING_INV]=='40'){
					$statuse = 'REJECTED';
				}
				if($row[TRACKING_INV]=='45'){
					$statuse = 'CANCEL PPL & INVOICE';
				}
				if($row[TRACKING_INV]=='50'){
					$statuse = 'APPROVED BY SPV';
				}
				if($row[TRACKING_INV]=='60'){
					$statuse = 'GENERATE PPL';
				}
				if($row[TRACKING_INV]=='70'){
					$statuse = 'SIMULATE & POSTING PPL';
				}
				if($row[TRACKING_INV]=='80'){
					$statuse = 'REJECT BY MANAJER VERIFIKASI';
				}
				if($row[TRACKING_INV]=='90'){
					$statuse = 'APPROVED  BY MANAJER VERIFIKASI';
				}
				if($row[TRACKING_INV]=='100'){
					$statuse = 'REJECT BY SM VERIFIKASI';
				}
				if($row[TRACKING_INV]=='110'){
					$statuse = 'APPROVED  BY SM VERIFIKASI';
				}
				if($row[TRACKING_INV]=='120'){
					$statuse = 'EKSPEDISI BENDAHARA';
				}
                $status_name_v[]=$statuse;

                $sqlcek="SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE  NO_INVOICE = '$no_invoice_in' AND DELETE_MARK = '0' ";
                $querycek= oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data=oci_fetch_assoc($querycek);
                $total_klaim_in=$row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in=$row_data[TOTAL_KTG];
                $total_semen_in=$row_data[TOTAL_SEMEN];
                $total_pdpks_in=$row_data[TOTAL_PDPKS];
                $total_pdpkk_in=$row_data[TOTAL_PDPKK];

                if ($row[PAJAK_INV] > 0) 
                $pajak = 0.1*$row_data[TOTAL_KLAIM];
                else 
                $pajak = 0;


                $klaim_semen_v[]=$row_data[TOTAL_SEMEN];
                $klaim_ktg_v[]=$row_data[TOTAL_KTG];
                $pdpks_v[]=$row_data[TOTAL_PDPKS]; 
                $pend_ktg_v[]=$row_data[TOTAL_PDPKK]; 
                $total_klaim_v[]=$row_data[TOTAL_KLAIM];
                $pajak_v[]=$pajak;
                $status_dokumen[] = $row['STATUS_DOKUMEN'];
                // var_dump($row);
				//echo "<pre>";
				//print_r($row); 
				//echo "</pre>";
            }else{
                continue;
            }             
	}
	$total=count($no_invoice_v);
	if ($total < 1){
		$komen = "Tidak Ada Data Yang Ditemukan";
	}

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Approval Invoice </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Data Invoice </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
    </tr>
    
    <tr>
      <td  class="puso">Periode Invoice </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>" />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){

?>
<form id="data_claim" name="data_claim" method="post" action="komentar.php" >

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Invoice </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td> 
		<td align="center"><strong >No Invoice </strong></td> 
		<td align="center"><strong >Tanggal Invoice </strong></td>
		 <td align="center"><strong>No MIR7  </strong></td>
		 <td align="center"><strong>No FI Doc  </strong></td>
		 <td align="center"><strong>No BA Rekaputalasi </strong></td>
		 <td align="center"><strong>Tanggal BA Rekaputalasi  </strong></td>
		 <td align="center"><strong>Expeditur </strong></td>
		 <td align="center"><strong>Warna Plat </strong></td>
		 <td align="center"><strong>No Pajak </strong></td>
		 <td align="center"><strong>Total</strong></td>
		 <td align="center"><strong>Status</strong></td> 
		 <td align="center"><strong>Display</strong></td> 
		 <td align="center" width="100px"><strong>Cetak</strong></td> 
		 <td align="center" width="100px"><strong>Cetak Klaim</strong></td> 
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     
		
		<td align="center"><? echo $i+1; ?></td> 
        <td align="center"><?php   
			if($status_v[$i] == 90){
				echo '<a href="form_approval_invoice_sm.php?no_ba='.$no_ba_v[$i].'">'.$no_invoice_v[$i].'</a></td>';
			}else{ 
				echo $no_invoice_v[$i].'</td>';
			}
			 
			
		?></td>     
		<td align="center"><? echo $TGL_INVOICE_v[$i]; ?></td>
		<td align="center"><? echo $no_mir7_v[$i]; ?></td>
		<td align="center"><? echo $fi_doc_v[$i]; ?></td>
		<td align="center"><? echo $no_ba_v[$i]; ?></td>
		<td align="center"><? echo $tgl_ba_v[$i]; ?></td> 
		<td align="center"><? echo $vendor_v[$i]." / ".$nama_vendor_v[$i]; ?></td>
		<td align="center"><? echo $warna_plat_v[$i]; ?></td> 
		<td align="center"><? echo $no_pajak_ex_v[$i]; ?></td> 
		<td align="center"><? echo number_format($total_klaim_v[$i],0,",","."); ?></td>
		<td align="center"><? echo $status_name_v[$i]; ?></td>  
		<td align="center"><a href="javascript:popUp('print_preview_ppl.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button">DISPLAY</a></td> 
		<?php if($status_v[$i] >= '110' ){ ?>
				<td align="center"><a href="javascript:popUp('print_draft_ppl_permintaan.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button">Cetak PPL</a></td>
		<?php }else{ ?>
		<td align="center"> - </td>
		<?php }  ?>
		<?php if($status_v[$i] >= '110' ){ ?>
				<td align="center"><a href="javascript:popUp('print_draft_ppl_klaim.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button">Cetak Klaim SPJ</a></td>
		<?php }else{ ?>
		<td align="center"> - </td>
		<?php }  ?>
		</tr>
	  <? } ?>
		</tbody>

	  <tr class="quote">
		<td colspan="16" align="center">
		<a href="list_approval_invoice_sm.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
