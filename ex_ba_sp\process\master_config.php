<?php
session_start();

include_once ('../helper.php');
include_once ('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

switch ($action) {
    case 'get':
      $user_id = $_SESSION['user_id'];
      $sql = "SELECT emc.ID, NAMA, USER_ID, VALUE_TYPE, \"VALUE\" 
              FROM EX_USER_MASTER_CONFIG eumc 
              LEFT JOIN EX_MASTER_CONFIG emc 
              ON eumc.CONFIG_ID = emc.ID 
              WHERE eumc.USER_ID = :user_id";

      $query = oci_parse($conn, $sql);
      oci_bind_by_name($query, ":user_id", $user_id);
      oci_execute($query);
      
      $data = array();

      while($row=oci_fetch_array($query)){
        if($row['VALUE_TYPE'] == 'multiple_user'){
          if(!$users){
            $users = get_users($conn);
          }
          
          $ids = $row['VALUE'];
          $ids = explode(',', $ids);
          $copied_users = $users;
          foreach ($copied_users as &$user) {
              if(in_array($user['ID'], $ids)){
                $user['selected'] = true;
              }else{
                $user['selected'] = false;
              }
          }
          unset($user);

          $row['USERS'] = $copied_users;
        }
        array_push($data, $row);
      }

      echo json_encode($data);

      break;
  
    case 'update':
      $id  = $_POST['id'];
      $value = $_POST['value'];
      $user  = $_SESSION['user_name'];
  
      $field_names = array('VALUE', 'LAST_UPDATE_BY');
      $field_data = array("$value", "$user");
      $tablename = "EX_MASTER_CONFIG";
      $field_id = array('ID');
      $value_id = array("$id");
      
      try {
        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        $result = array(
            'status' => 'success'
        );
        echo json_encode($result);
      } catch (Exception $e) {
        $result = array(
            'status' => 'error',
            'message' => $e->getMessage()
        );
        echo json_encode($result);
      }

      break;
  
    default:
      // ⚠️ Unknown action
      $result = array(
        'status' => 'error',
        'message' => 'Invalid action'
        );
      echo json_encode($result);
      break;
  }
?>