<?
session_start();

function Terbilang($x)
{
  $abil = array("", "satu", "dua", "tiga", "empat", "lima", "enam", "tujuh", "delapan", "sembilan", "sepuluh", "sebelas");
  if ($x < 12)
    return " " . $abil[$x];
  elseif ($x < 20)
    return Terbilang($x - 10) . " belas";
  elseif ($x < 100)
    return Terbilang($x / 10) . " puluh" . Terbilang($x % 10);
  elseif ($x < 200)
    return " seratus" . Terbilang($x - 100);
  elseif ($x < 1000)
    return Terbilang($x / 100) . " ratus" . Terbilang($x % 100);
  elseif ($x < 2000)
    return " seribu" . Terbilang($x - 1000);
  elseif ($x < 1000000)
    return Terbilang($x / 1000) . " ribu" . Terbilang($x % 1000);
  elseif ($x < 1000000000)
    return Terbilang($x / 1000000) . " juta" . Terbilang($x % 1000000);
  elseif ($x < 1000000000000)
    return Terbilang($x / 1000000000) . " Milyar" . Terbilang($x % 1000000000);
}

include ('../include/ex_fungsi.php');
//include ('../../../../prod/sd/sdonline/include/ex_fungsi.php');//prod
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$id_user=$_SESSION['user_id'];
$vendor=$fungsi->ex_find_vendor($conn,$id_user);
//$hakakses=array("adm","admin");
//$fungsi->keamanan($hakakses);
$currentPage="print_klinker.php";
$no_invoice= $_REQUEST['no_invoice'];
$tanggal_cetak= date('d-m-Y');
$vendor=$fungsi->ex_find_vendor($conn,$id_user);

$sql= "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,((QTY_SHP*1000)-BERAT_TERIMA) as SELQTY  
FROM EX_TRANS_HDR WHERE 
DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' 
ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_invoice_v[]=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
                $tgl_invoice_v[]=$row[TANGGAL_INVOICE];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
                $tgl_kirim_vmkk=$row[TANGGAL_KIRIM1];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v=$row[PLANT]; 
		$nama_plant_v=$row[NAMA_PLANT]; 
		$warna_plat_v=$row[WARNA_PLAT]; 
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR]; 
                $orgin_v=$row[ORG]; 
		
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  
		$berat_kirim_v[]=$row[QTY_SHP];  
		$berat_terima_v[]=$row[BERAT_TERIMA];  
		$beda = $row[QTY_SHP]*1000 - $row[BERAT_TERIMA];
		//if ($beda > 0)$beda = 0;
                
                //@revisi selisih clingker liyantanto
                unset($akhirtol);
                if($beda<=0){
                     $beda=0;                   
                }else if($beda>0){
                                $tanggal_kirim_up=$row[TANGGAL_KIRIM1];
                                $sql_smn= "
                                        SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$row[KODE_PRODUK]' 
                                        AND ORG='$orgin_v'
                                        AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') 
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
				$query_smn= oci_parse($conn, $sql_smn);
				oci_execute($query_smn);
				while($row_smn=oci_fetch_array($query_smn)){
					$toleransi_up=$row_smn[TOLERANSI]; 
					$biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN]; 
					$harga_tebus_v=$row_smn[HARGA_TEBUS]; 
					$harga_tebus_45_up=$row_smn[HARGA_TEBUS_45]; 
					$harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS]; 
					$toleransi_up=$row_smn[TOLERANSI]; 
				}

				if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0){
					$sql_smn= "SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$row[KODE_PRODUK]' 
                                        AND ORG='$orgin_v'
                                        AND TUJUAN = '9999'  
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') 
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' 
                                        ORDER BY START_DATE ASC";
					$query_smn= oci_parse($conn, $sql_smn);
					oci_execute($query_smn);
					while($row_smn=oci_fetch_array($query_smn)){
						$biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN]; 
						$harga_tebus_v=$row_smn[HARGA_TEBUS]; 
						$harga_tebus_45_up=$row_smn[HARGA_TEBUS_45]; 
						$harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS];
						$toleransi_up=$row_smn[TOLERANSI]; 
					}
				}
                    
                    $nilaitoler=$toleransi_up;
                    
                    $akhirtol=@((($row[QTY_SHP]*1000)*$nilaitoler)/100);
                    $jktol=$beda-$akhirtol;
                    if($jktol>0){
                        $beda=$jktol;
                    }else{
                        $beda=0;
                    }
                    
                }
                
                $toleransi[]=$akhirtol;
		$selisih_v[]=$beda;  
		$kapasitas_v[]=$row[KAPASITAS_VEHICLE];  
		
		
	}
	$total=count($shp_trn_v);
        list($tglmm,$blnmm,$thnmm)=split("-",$tgl_kirim_vmkk);
        $tahnterminmo=$thnmm;  
        $nmkali="KERJA SAMA OPERASI SEMEN GRESIK - SEMEN INDONESIA ";
        if($tahnterminmo!='2017' && $orgin_v=='7000'){            
            $nmkali="PT. SEMEN INDONESIA (PERSERO) Tbk ";
        } else if ($orgin_v=='5000'){
            $nmkali="PT. SEMEN GRESIK ";
        }
//        if($_SESSION['user_name'] != 'ACR7496111'){
//            $nama_vendor_v = $_SESSION['nama_lengkap'];
//        }
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>print invoice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
<!--
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}
.style2 {font-family: Arial, Helvetica, sans-serif; font-size: 12; }
.style3 {font-size: 12}
-->
</style>
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" /> 

<style type="text/css">
<!--
.style4 {font-family: Arial, Helvetica, sans-serif; font-size: 10px; }
.style5 {font-size: 10px}
-->
</style>
</head>

<body>
<span class="style1"></span>
<div align="center"></div>
<?php 
	$sqlnorek = "SELECT NO_REKENING,BANK,NO_KWITANSI,TGL_PAJAK_EX FROM EX_INVOICE WHERE NO_INVOICE = '".$no_invoice."'";
	$qnorek = oci_parse($conn, $sqlnorek);
	oci_execute($qnorek);
	$datarek = oci_fetch_assoc($qnorek);
	
	$sqldirut = "select NAMA_DIRUT FROM EX_INVOICE_DIRUT_VENDOR WHERE KODE_VENDOR='".$vendor."'";
	$qdirut = oci_parse($conn, $sqldirut);
	oci_execute($qdirut);
	$datadirut = oci_fetch_assoc($qdirut);
?>
<body>
<?php 
	$sqlnorek = "SELECT NO_REKENING,BANK,NO_KWITANSI,TGL_PAJAK_EX,to_char(TGL_PAJAK_EX,'DD-MM-YYYY') as TGL_PAJAK_EX1, to_char(TGL_INVOICE,'YYYYMMDD') as TGL_INVOICE FROM EX_INVOICE WHERE NO_INVOICE = '".$no_invoice."'";
	$qnorek = oci_parse($conn, $sqlnorek);
	oci_execute($qnorek);
	$datarek = oci_fetch_assoc($qnorek);

	$sqldirut = "select NAMA_DIRUT FROM EX_INVOICE_DIRUT_VENDOR WHERE KODE_VENDOR='".$vendor."'";
	$qdirut = oci_parse($conn, $sqldirut);
	oci_execute($qdirut);
	$datadirut = oci_fetch_assoc($qdirut);
	$tgl_invoice=$datarek[TGL_INVOICE];
?>
<span class="style1"></span>
<div align="center"></div>
<table width="900">
	<tr>
		<td colspan="3" align="center"><h3>KWITANSI<br><? echo $nama_vendor_v ;?></h3><hr></td>
	</tr>
	<tr>
		<td>Nomor Kwitansi </td>
		<td>:</td>
		<td><?php echo $datarek['NO_KWITANSI']; ?></td>
	</tr>
	<tr>
		<td>Telah Terima Dari </td>
		<td>:</td>
		<td><?=$nmkali;?></td>
	</tr>
	<tr><td colspan="3" height="15"></td></tr>
	<tr>
		<td>Sejumlah Uang </td>
		<td>:</td>
		<td><span id="terbilang"></span></td>
	</tr>
	<tr>
		<td></td>
		<td></td>
		<td>
			<table style="margin-top:25px;" width="500">
				<tr>
					<td width="100" colspan="2">Ongkos Angkut<!--  - <span id="namaproduk"></span> --> <?php echo ($kel == 'LAUT') ? '100%' : ''; ?></td>
					<td align="right"><span id="grandtotal"></span></td>
				</tr>
				<?php 
					if ($kel == 'LAUT') {
						$persenppn= '1%';
					}else{
						if($tgl_invoice < 20220401){
							$persenppn = '10%';
						} else {
							$persenppn = '11%';
						}
					}
				?>
				<?	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ ?>
				<tr>
					<td colspan="2">PPN <?php echo $persenppn; ?> <?php echo $no_pajak_ex;  ?></td>
					<td align="right"><span id="grandpajak"></span></td>
				</tr>
				<?php } ?>
				<tr><td colspan="3"><hr></td></tr>
				<tr>
					<td colspan="3" align="right"><span id="grandtotalgrand"></span></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td>Jumlah</td>
		<td>:</td>
		<td><span style="font-weight: bold;font-size: 22px;" id="grandtotalgrand2"></span></td>
	</tr>
</table>
<br><br><br><br>
<table width="900">
	<tr>
		<td></td>
		<? if(strtotime(date("d-M-Y"))<=strtotime('13-JAN-17')){?>
			<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=date("d-M-Y");?><br><? echo $nama_vendor_v ;?></td>
	    <? } else if(strtotime($tgl_invoice_v[0])<=strtotime('27-MAR-17') && strtotime($tgl_invoice_v[0])>strtotime('13-JAN-17') && $kel == 'DARAT'){ ?>
	    	<?php
		            rsort($tgl_kirim_sort);
		            $date=date_create($tgl_kirim_sort[0]);
		        ?>
		        	<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=strtoupper(date_format($date,"d-M-Y"));?><br><? echo $nama_vendor_v ;?></td>
	    <? } else{ ?>
			<?php //if ($kel == 'LAUT'): ?>
				<td align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$datarek['TGL_PAJAK_EX'];?><br><? echo $nama_vendor_v ;?></td>
			<?php //else: ?>
				<?php
		            //rsort($tgl_kirim_sort);
		            //$date=date_create($tgl_kirim_sort[0]);
		        ?>
		        	<!-- <td align="center" class="style2"> <? //$fungsi->ex_cari_vendor($vendor_v)?>, <? //strtoupper(date_format($date,"d-M-Y"));?><br><? //echo $nama_vendor_v ;?></td> -->
			<?php //endif ?>
		<?php } ?>
	</tr>
	<tr><td colspan="2" height="150"></td></tr>
	<tr>
		<td>TRANSFER<br><?php echo $datarek['BANK']; ?>&nbsp;<?php echo $datarek['NO_REKENING']; ?><br>A/N&nbsp;<? echo $nama_vendor_v ;?></td>
		<td align="center"><?php echo @$datadirut['NAMA_DIRUT']; ?></td>
	</tr>
</table>
<br>
<hr>
<br>
<table width="900" align="center" >
<tr>
<td colspan="15" ><div align="center" class="style2"><?=$nmkali;?> </div></td>
</tr>
<tr >
  <td colspan="15" ><div align="center" class="style2">TAGIHAN ONGKOS ANGKUT SEMEN  </div></td>
</tr>
<tr >
  <td colspan="13" style="border-bottom:1px #000000 solid;">
  <table width="890">		
  	<tr class="style2">
		<td> Expeditur</td>
		<td><span class="style2"> : </span></td>
		<td> <span class="style2"><? echo $vendor_v."  ".$nama_vendor_v ;?></span> </td>
		<td><span class="style2"></span> </td>
		<td> <span class="style2"><? echo "PLAT ".$warna_plat_v;?></span> </td>
		<td><span class="style2"></span> </td>
		<td><span class="style2"></span> </td>
  	</tr>

  	<tr class="style2">
		<td> No Tagihan</td>
		<td><span class="style2"> : </span></td>
		<td> <span class="style2">
		  <? echo $no_invoice ." / ".$no_invoice_ex_v?>
		</span> </td>
		<td><span class="style2"></span> </td>
		<td> Pengiriman dari 
		  <?=$plant_v?>		  </td>
		<td> <span class="style2">
		  <?=$nama_plant_v?>
		</span> </td>
		<td><span class="style2"></span> </td>
  	</tr>
  </table>  </td>
</tr  >
<tr>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TGL SPJ</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> AREA LT</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO SPJ</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> NO. POL</span></td>
        <td style=" border-bottom:1px #000000 solid;"><span class="style2"> KAPASITAS</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> PRODUK</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> DISTRIBUTOR</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> KIRIM (TON)</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TERIMA (KG)</span></td>
        <td style=" border-bottom:1px #000000 solid;"><span class="style2"> SELISIH (KG)</span></td>
        <td style=" border-bottom:1px #000000 solid;"><span class="style2"> TOLERAN (KG)</span></td>	
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> STANDART</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> TARIF</span></td>
	<td style=" border-bottom:1px #000000 solid;"><span class="style2"> JUMLAH</span></td>
</tr>
<?
$b=0;
$tot_krm =0;
$tot_trm =0;
$tot_sel =0;

$sub_krm =0;
$sub_trm =0;
$sub_sel =0;
$sub_tot_jumlah =0;
$grand_tot_jumlah =0;


for($i=0; $i<$total;$i++) { 
	$b +=1;
	if ($i==0){
	?>
	<tr>
	  <td colspan="15" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v[$i] . "         ".$nama_sal_dis_v[$i];?></strong></span></td>
  </tr>
	<? 
	} 
	if ($i >0 and $sal_dis_v[$i] != $sal_dis_v[$i-1]){
	$sub_krm =0;
	$sub_trm =0;
	$sub_sel =0;
	$sub_tot_jumlah=0;
	
	?>
	<tr>
	  <td colspan="15" ><span class="style2"><strong><? echo  "AREA       ". $sal_dis_v[$i] . "         ".$nama_sal_dis_v[$i];?></strong></span></td>
	</tr>
	<?
	}
$sub_trm +=$berat_terima_v[$i];
$sub_krm +=$berat_kirim_v[$i];
$sub_sel +=$selisih_v[$i];

$tot_krm +=$berat_kirim_v[$i];
$tot_trm +=$berat_terima_v[$i];
$tot_sel +=$selisih_v[$i];


$sub_tot_jumlah +=$shp_cost_v[$i];
$grand_tot_jumlah +=$shp_cost_v[$i];


?>
<tr>
	<td> <span class="style2">
	  <?=$b?>
	</span></td>
	<td> <span class="style2">
	  <?=$tgl_kirim_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$sal_dis_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$spj_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <?=$no_pol_v[$i]?>
	</span></td>
        <td> <span class="style2">
	  <?=$kapasitas_v[$i]?>
	</span></td>
	<td> <span class="style2">
	  <? echo trim($nama_produk_v[$i],"SEMEN");?>
	</span></td>
	<td> <span class="style2">
	  <? echo substr($nama_sold_to_v[$i],0,20);?>
	</span></td>
	<td> <div align="right" class="style2">
	  <?=number_format($berat_kirim_v[$i],3,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($berat_terima_v[$i],0,",",".")?>
    </div></td>
    <td> <div align="right" class="style2">
	  <?=number_format($selisih_v[$i],0,",",".")?>
    </div></td>
        <td> <div align="right" class="style2">
	  <?=number_format($toleransi[$i],0,",",".")?><!--Toleransi-->
    </div></td>	
	<td> <div align="right" class="style2">
	  <? if ($kapasitas_v[$i] < $qty_v[$i] and $kapasitas_v[$i] > 1)
	  $standart = $kapasitas_v[$i]; 
	  else 
	  $standart = $qty_v[$i]; 
	 echo number_format($standart*1000,0,",",".");
	  ?>
    </div></td>

	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v[$i]/$standart,0,",",".")?>
    </div></td>
	<td> <div align="right" class="style2">
	  <?=number_format($shp_cost_v[$i],2,",",".")?>
    </div></td>
</tr>
<?
	if ($i >0 and $sal_dis_v[$i] != $sal_dis_v[$i+1]){
?>
	<tr>
		<td colspan="8"><div align="right" class="style2">Sub Total Per Area </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_krm,3,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_trm,0,",",".")?>
	    </div></td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_sel,0,",",".")?>
	    </div></td>
		<td><span class="style3"></span> </td>
		<td><span class="style3"></span> </td>
                <td><span class="style3"></span> </td>
		<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
		  <?=number_format($sub_tot_jumlah,2,",",".")?>
	    </div></td>
	</tr>
<? 
	}
} 
?>
<tr>
	<td colspan="8"><div align="right" class="style2">Grand Total </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_krm,3,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_trm,0,",",".")?>
    </div></td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($tot_sel,0,",",".")?>
    </div></td>
	<td><span class="style3"></span> </td>
	<td><span class="style3"></span> </td>
        <td><span class="style3"></span> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($grand_tot_jumlah,2,",",".")?>
    </div></td>
</tr>
<?	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){ ?>

<tr>
	<td colspan="6"><span class="style2"> No Seri Pajak : 
	  <?=$no_pajak_ex?>
	</span></td>
	<td colspan="2"><span class="style2"> Tanggal : 
	  <? 
		$sql_tgl= "SELECT to_char(TGL_PAJAK_EX,'DD-MM-YYYY') as TGL_PAJAK_EX1 FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
		$query_tgl= oci_parse($conn, $sql_tgl);
		oci_execute($query_tgl);
		
		$row_tgl=oci_fetch_array($query_tgl);
		echo $tgl_pajak=$row_tgl[TGL_PAJAK_EX1];
	  ?> 
    </span></td>
	<td> <div align="right" class="style2">Jumlah Pajak :</div></td>
	<td colspan="3"> </td>
	<td ><span class="style3"></span> </td> <? 
	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )){
		if(Date('Ymd')>'20220401'){
			$pajak = 0.11 * $grand_tot_jumlah;
		}else{
			$pajak = 0.1 * $grand_tot_jumlah;
		}
	}else $pajak = 0;
	?>
	<td > <div align="right" class="style2">
	  <?=number_format($pajak,2,",",".")?>
    </div></td>
</tr>

<script type="text/javascript">
	var gid = document.getElementById('grandpajak');
	gid.textContent = '<?=number_format($pajak,2,",",".")?>';
</script>
<script type="text/javascript">
	var gid4 = document.getElementById('grandtotal');
	gid4.textContent = '<?=number_format($grand_tot_jumlah,2,",",".")?>';
</script>
<tr>
	<td colspan="9"> <div align="right" class="style2">Total :</div></td>
	<? $total_all = $grand_tot_jumlah + $pajak;?>
	<td colspan="4"> </td>
	<td style="border-top:1px #000000 solid;"> <div align="right" class="style2">
	  <?=number_format($total_all,2,",",".")?>
    </div></td>
</tr>
<?php 
	$pembilang =number_format($total_all,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
<script type="text/javascript">
	var gid = document.getElementById('grandtotalgrand');
	gid.textContent = '<?=number_format($total_all,2,",",".")?>';
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format($total_all,2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
</script>
<? }else{?>
<?php 
	$pembilang =number_format($grand_tot_jumlah,2,",","");
	$pembilang = explode(",", $pembilang);
	$terbilang = '';
	if (count($pembilang) > 1) {
		$terbilang = Terbilang($pembilang[0]);
		if ($pembilang[1] != '00') {
			$terbilang.=" Koma ".Terbilang($pembilang[1]);
		}
		$terbilang = ucwords($terbilang);
	}else{
		$terbilang = ucwords(Terbilang($pembilang[0]));
	}
?>
	<script type="text/javascript">
		
	var gid2 = document.getElementById('grandtotalgrand2');
	gid2.textContent = '<?=number_format($grand_tot_jumlah,2,",",".")?>';
	var gid3 = document.getElementById('terbilang');
	gid3.textContent = '<?php echo $terbilang;?> Rupiah';
	</script>
<?	} ?>
<tr><td colspan="15" height="100"> </td>
</tr>
<tr>
	<!-- <td colspan="6"><span class="style2"> 
	</span></td>
	<td colspan="2"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
        <td ><span class="style3"></span> </td> 
	<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=date("d-m-Y")?></td>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td> -->

	<td colspan="6"><span class="style2"> 
	</span></td>
	<td colspan="3"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
	<? if(strtotime($tgl_invoice_v[0])<=strtotime('13-JAN-17')){?>
		<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$tgl_invoice_v[0];//date("d-m-Y")?></td>
	<? } else if(strtotime($tgl_invoice_v[0])<=strtotime('27-MAR-17') && strtotime($tgl_invoice_v[0])>strtotime('13-JAN-17') && $kel == 'DARAT'){ ?>
	<?php
            rsort($tgl_kirim_sort);
            $date=date_create($tgl_kirim_sort[0]);
        ?>
        	<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=strtoupper(date_format($date,"d-M-Y"));?></td>
    <? } else{ ?>
		<?php //if ($kel == 'LAUT'): ?>
			<td colspan="3" align="center" class="style2"> <?=$fungsi->ex_cari_vendor($vendor_v)?>, <?=$datarek['TGL_PAJAK_EX'];//date("d-m-Y")?></td>
		<?php //else: ?>
			<?php
	            //rsort($tgl_kirim_sort);
	            //$date=date_create($tgl_kirim_sort[0]);
	        ?>
	        	<!-- <td colspan="3" align="center" class="style2"> <? //$fungsi->ex_cari_vendor($vendor_v)?>, <? //strtoupper(date_format($date,"d-M-Y"));?></td> -->
		<?php //endif ?>
	<?php } ?>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>
<tr>
	<td colspan="6"><span class="style2"> 
	</span></td>
	<td colspan="2"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
        <td ><span class="style3"></span> </td> 
	<td colspan="3" align="center" class="style2"> <?=$nama_vendor_v?></td>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>
<tr><td colspan="15" height="30"> </td>
</tr>
<tr>
	<td colspan="4"><span class="style2"> 
	</span></td>
	<td colspan="4"><span class="style2"> 
    </span></td>
	<td> <div align="right" class="style2"></div></td>
        <td ><span class="style3"></span> </td> 
	<td colspan="3" align="center" class="style2"> <?php echo @$datadirut['NAMA_DIRUT']; ?> </td>
	<td ><span class="style3"></span> </td> 
	<td > <div align="right" class="style2">
    </div></td>
</tr>

</table>
<p>
<table width="900" align="center" >
<tr>
  <td width="900">
    <input name="Print" type="button" id="Print" value="Print"  onclick="javascript:window.print();" class="nonPrint"  /> 
    <input name="Close" type="button" id="Close" value="Close"  onclick="window.close()" class="nonPrint"  />
</div>
</td>
</tr>
</table>

<br/>
</body>
</html>
