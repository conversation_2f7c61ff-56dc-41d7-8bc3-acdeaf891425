<?php
session_start();
include('../include/ex_fungsi.php');
include ('../include/email.php');
require 'library/phpqrcode/qrlib.php';
require 'library/tcpdf/tcpdf.php';
require 'library/fpdi/fpdi.php';
require_once 'helper.php';
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$user = new User_SP();
$pejabat_eks = $user->get_pejabat_eks_manual();
$ids_pejabat = array();
foreach($pejabat_eks as $p){
    array_push($ids_pejabat, $p['ID']);
};

$user_id = $_SESSION['user_id'];

if(empty($user_id)){
    $msg = "Harap login terlebih dahulu";
    showMessage($msg, false);
    exit;
}

if(in_array($user_id, $ids_pejabat)){
    $isAuthorize = true;
};

function showMessage($msg, $isValid = true)
{
    global $no_ba;

    if($isValid){
?>
        <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
            <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
                <div class="alert alert-info" role="alert">
                    <strong>Pesan!</strong>
                    <br>
                    <br>
                    <div class="alert alert-warning" role="alert"><?= $msg ?></div>
                    <a href="lihat_ba_hdr_ex.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
                </div>
            </div>
<?php        
    }else{
?>
        <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
            <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
                <div class="alert alert-danger" role="alert">
                    <strong>Error!</strong>
                    <br>
                    <br>
                    <div class="" role="alert"><?= $msg ?></div>
                    <br>
                    <a href="submit_ba_sign_qr.php?no_ba=<?= $no_ba ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Coba Lagi&nbsp;&nbsp;&gt;&gt;</a>
                    <a href="lihat_ba_hdr_ex.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
                </div>
            </div>
<?php
    }
}

if(!$isAuthorize){
    $msg = "Anda tidak berhak akses menu ini";
    showMessage($msg, false);
    exit;
}

$no_ba = $_GET['no_ba'];

$query_ba = "SELECT
                EX_BA.ID,
                EX_BA.NO_BA,
                EX_BA.NO_VENDOR,
                EX_BA.TOTAL_INV,
                EX_BA.PAJAK_INV,
                EX_BA.NAMA_VENDOR,
                EX_BA.KLAIM_KTG,
                EX_BA.KLAIM_SEMEN,
                EX_BA.PDPKS,
                EX_BA.PDPKK,
                EX_BA.DELETE_MARK,
                EX_BA.ORG,
                EX_BA.TOTAL_INVOICE,
                EX_BA.TGL_BA,
                EX_BA.STATUS_BA,
                EX_BA.FILENAME,
                EX_BA.ALASAN_REJECT,
                EX_BA.ID_USER_APPROVAL,
                EX_BA.SIGN_ORDER_ID_1,
                EX_BA.SIGN_STATUS_1,
                EX_BA.SIGN_TOKEN_1,
                SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
                SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
                SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
                SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
                SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
                SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
                SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
                SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
                SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
                SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
                to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
            FROM
                EX_BA
                JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
            WHERE EX_BA.DELETE_MARK = '0' 
                AND EX_BA.NO_BA = :no_ba
            GROUP BY EX_BA.ID,
                EX_BA.NO_BA,
                EX_BA.NO_VENDOR,
                EX_BA.TOTAL_INV,
                EX_BA.PAJAK_INV,
                EX_BA.NAMA_VENDOR,
                EX_BA.KLAIM_KTG,
                EX_BA.KLAIM_SEMEN,
                EX_BA.PDPKS,
                EX_BA.PDPKK,
                EX_BA.DELETE_MARK,
                EX_BA.ORG,
                EX_BA.TOTAL_INVOICE,
                EX_BA.TGL_BA,
                EX_BA.STATUS_BA,
                EX_BA.FILENAME,
                EX_BA.ALASAN_REJECT,
                EX_BA.SIGN_ORDER_ID_1,
                EX_BA.SIGN_STATUS_1,
                EX_BA.SIGN_TOKEN_1,
                EX_BA.ID_USER_APPROVAL
            ORDER BY
                EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data = oci_fetch_array($sql_ba);

// print_r($data);exit;
$no_ba = $data['ID'];
$no_ba_v = $data['NO_BA'];
$org_v = $data['ORG'];
$no_vendor_v = $data['NO_VENDOR'];
$nama_vendor_v = $data['NAMA_VENDOR'];
$total_semen_v = $data['TOTAL_KLAIM_SEMEN'];
$total_kantong_v = $data['TOTAL_KLAIM_KTG'];
$total_ppdks_v = $data['PDPKS'];
$total_inv_v = $data['SHP_COST'];
$status_ba = $data['STATUS_BA'];
$id_user_approval = $data['ID_USER_APPROVAL'];
//INSERT LOG HISTORY BA
$email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
<div align=\"center\">
<thead>
<tr class=\"quote\">
<td ><strong>&nbsp;&nbsp;No.</strong></td>
<td align=\"center\"><strong>ORG</strong></td>
<td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
<td align=\"center\"><strong>EKSPEDITUR</strong></td>
<td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
<td align=\"center\"><strong>KLAIM SEMEN</strong></td>
<td align=\"center\"><strong>PDPKS</strong></td>
<td align=\"center\"><strong>TOTAL</strong></td>
<td align=\"center\"><strong>STATUS</strong></td>
</tr>
</thead>
<tbody>";

$email_content_table .= " 
<td align=\"center\">1</td>
<td align=\"center\">".$org_v."</td>       
<td align=\"center\">".$no_ba_v."</td>
<td align=\"center\">".$no_vendor_v."</td>
<td align=\"center\">".$nama_vendor_v."</td>
<td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
<td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
<td align=\"center\">Open</td>
</tr>";

if ($status_ba == 10) {
    // Update filename
    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");
    $filename = "DokumenBA-$no_ba_v.pdf";
    $fieldNames = array('FILENAME');
    $fieldData = array($filename);

    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    // URL to be encoded in QR Code
    $param = array(
        "no_ba" => $no_ba_v,
        "level" => "pejabat_eks"
    );
    $param = base64_encode(json_encode($param));
    $signUrl = get_base_url() . "ex_ba_sp/api/verify_sign_ba.php?kode=" . $param;
    $qrFile = dirname(__FILE__) . "/upload/qr_code.png";
    // Generate QR Code
    QRcode::png($signUrl, $qrFile, QR_ECLEVEL_L, 5);

    $pdfExporter = new PdfExporter();
    $response = $pdfExporter->beritaAcara($no_ba_v);

    // Menyimpan pdf ke dalam file di CSMS
    $pdf = fopen(dirname(__FILE__) . '/upload/' . $filename, 'w');
    fwrite($pdf, $response);
    fclose($pdf);

    // Open existing pdf
    $pdf = new FPDI();
    $pdf->AddPage();
    $pdf->setSourceFile( dirname(__FILE__) . '/upload/' . $filename); // Load existing PDF
    $tplIdx = $pdf->importPage(1);
    $pdf->useTemplate($tplIdx, 0, 0, 210);

    // Embed QR Code
    $pdf->Image('upload/qr_code.png', 25, 95, 20, 20, 'PNG');
    $pdf->Output(dirname(__FILE__) . '/upload/' . $filename, 'F');

    
    $data['FILENAME'] = $filename;

    // update status
    $tableName = 'EX_BA';
    $field_id = array('ID');
    $value_id = array("$no_ba");
    $fieldNames = array('STATUS_BA');
    $fieldData = array('20');
    $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    // track status
    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
    $field_data = array("$no_ba_v","20","SUBMITTED","$id_user_approval","SYSDATE");
    $tablename = "EX_BA_TRACK";
    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
    
    //sendEmail
    $mailCc = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA = :no_ba and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_bind_by_name($query, ":no_ba", $no_ba_v);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];

    // dev
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Verif BA Rekapitulasi' and B.ORG = '".$org_v."' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    // prod
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.ID=3255 and B.ORG = '".$org_v."' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    $admin_trans = $user->get_admin_trans();
    foreach($admin_trans as $at){
        if(!empty($at['ALAMAT_EMAIL'])){
            if(empty($mailTo)){
                $mailTo = $at['ALAMAT_EMAIL'];
            }else{
                $mailTo .= ','.$at['ALAMAT_EMAIL'];
            }
        }
    }
    
    // $mailTo .= ', <EMAIL>';
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve BASTP', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }

    $msg = 'Dokumen BASTP berhasil ditandatangani';
}else if($status_ba >= 20){
    $msg = 'Dokumen BASTP sudah ditandatangani';
}else{
    $msg = 'Dokumen BASTP sedang tidak dalam status perlu persetujuan pejabat ekspeditur';
}

showMessage($msg, true);
?>