<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$titlepage='Master Config Auto GR Epod';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];


$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<!-- <SCRIPT LANGUAGE="JavaScript">				
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>
	 <a href="../login.php">Login....</a> -->
<?

// exit();
// }

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>

</head>   
<body>

<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px"
           idField="itemid" rownumbers="true" pagination="true">
        <thead>
            <tr>
                <!-- <th field="ck" checkbox="true"></th> -->
                <th field="ID" align="center">ID</th>
                <th field="SHIP_TO_CODE" align="center">Shipto Code</th>
                <th field="DELETE_MARK" align="center">Delete Mark</th>
                <th field="CREATED_AT" align="center">Created At</th>
                <th field="CREATED_BY" align="center">Created By</th>
                <th field="UPDATED_AT" align="center">Updated At</th>
                <th field="UPDATED_BY" align="center">Updated By</th>
            </tr>
        </thead>
    </table>
    <div id="toolbar">
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="deleteAct()">Delete</a>
        <!-- <a class="easyui-linkbutton" plain="true" iconCls="icon-excel" href="template_xls/template_sinkron_kustomer.xls" >Download Template</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload" onclick="uploadAct()">Upload Excel</a> -->
    </div>

    <div id="dlg" class="easyui-dialog" closed="true" buttons="#dlg-buttons">
        <div class="ftitle">Detail data</div>
            <form id="fm" method="post" novalidate>
            <div class="fitem">
                <label>Shipto Code</label>    
                <input id="shipto" name="shipto" required="true" class="easyui-textbox" maxlength="10"> 
            </div>
            </form>
        </div>
        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>

    <div id="dlgEdit" class="easyui-dialog" closed="true" buttons="#dlg-buttons-edit">
        <div class="ftitle">Detail data</div>
            <form id="fmEdit" method="post" novalidate>
                <div class="fitem" style="visibility:hidden;position:fixed">
                    <label>ID</label>
                    <input id="idEdit" name="id" class="easyui-textbox">
                </div>
                <div class="fitem">
                    <label>Shipto Code</label>
                    <input id="shiptoEdit" name="shipto" class="easyui-textbox" maxlength="10">
                </div>
                <div class="fitem">
                    <label>DELETE MARK</label>
                    <input id="deleteEdit" name="delete" class="easyui-textbox">
                </div>
            </form>
        </div>
        <div id="dlg-buttons-edit">  
            <a href="javascript:void(0)" id="modal-submit" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveEditAct()" style="width:90px" id="savedata">Edit</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
<!--
    <div id="dlgDel" class="easyui-dialog" style="padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle">Detail data</div>
            <form id="fmDel" method="post" novalidate>
                <div class="fitem" style="visibility:hidden;position:fixed">
                    <label>ID</label>
                    <input id="idDel" name="id" class="easyui-textbox" style="width:200px;">
                </div>
                <div class="fitem">
                    <label>Shipto Code</label>
                    <input id="shiptoDel" name="shipto" class="easyui-textbox" maxlength="10" style="width:200px;">
                </div>
                <div class="fitem">
                    <label>DELETE MARK</label>
                    <input id="deleteDel" name="delete" class="easyui-textbox" style="width:200px;">
                </div>
            </form>
        </div>
        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveDeleteAct()" style="width:90px" id="savedata">Delete</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlgDel').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
-->
<script type="text/javascript">

 $(function(){
    $("#dg").datagrid({
            url:'master_config_auto_gr_epod_act.php?act=show',
            singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing, please wait..',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');
 });

$('#dlg').dialog({
    title: 'My Dialog',
    // width: 277,
    // height: 277,
    closed: true,
    cache: false,
    // href: 'get_content.php',
    modal: true
});
$('#dlgEdit').dialog({
    title: 'My Dialog',
    // width: 277,
    // height: 277,
    closed: true,
    cache: false,
    // href: 'get_content.php',
    modal: true
});

var url;
function newAct(){
    $('#dlg').dialog('open').dialog('setTitle','New Data');
    $('#fm').form('clear');
    url = 'master_config_auto_gr_epod_act.php?act=add';
}

function saveAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        } else {
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function editAct(){
    var row = $('#dg').datagrid('getSelected');
    if(row){
        $('#modal-submit').attr('onclick', 'saveEditAct()');
        $('#dlgEdit').dialog('open').dialog('setTitle','Edit Data');
        $('#shiptoEdit').textbox('enable');
        $('#deleteEdit').textbox('enable');
        $('#shiptoEdit').textbox('setValue',row.SHIP_TO_CODE);
        $('#deleteEdit').textbox('setValue',row.DELETE_MARK.trimEnd());
        $('#idEdit').textbox('setValue',row.ID);
        url = 'master_config_auto_gr_epod_act.php?act=edit';
    } else {
        alert('Pilih baris data terlebih dahulu!');
    }
    
}

function saveEditAct(){
$('#fmEdit').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
            $('#dlgEdit').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        } else {
            $('#dlgEdit').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
            alert(result.info);
        }
    }
});
}

function deleteAct(){
    var row = $('#dg').datagrid('getSelected');
    if(row){
        $('#modal-submit').attr('onclick', 'saveDeleteAct()');
        // $('#dlgDel').dialog('open').dialog('setTitle','Delete Data');
        // $('#shiptoDel').textbox('setValue',row.SHIP_TO_CODE);
        // $('#deleteDel').textbox('setValue',row.DELETE_MARK.trimEnd());
        // $('#idDel').textbox('setValue',row.ID);
        $('#dlgEdit').dialog('open').dialog('setTitle','Delete Data');
        $('#shiptoEdit').textbox('disable');
        $('#deleteEdit').textbox('disable');
        $('#shiptoEdit').textbox('setValue',row.SHIP_TO_CODE);
        $('#deleteEdit').textbox('setValue',row.DELETE_MARK.trimEnd());
        $('#idEdit').textbox('setValue',row.ID);
        url = 'master_config_auto_gr_epod_act.php?act=delete';
    } else {
        alert('Pilih baris data terlebih dahulu!');
    }
    
}

function saveDeleteAct(){
$('#fmEdit').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
            $('#dlgEdit').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        } else {
            $('#dlgEdit').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
            alert(result.info);
        }
    }
});
}

</script>

<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:101px;
}
.fitem input{
width:111px;
}
#dlg, #dlgEdit {
    padding:10px 0 10px 10px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
