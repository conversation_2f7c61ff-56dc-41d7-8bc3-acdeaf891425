<?php

ob_start();
session_start();
include('../include/ex_fungsi.php');
include('../include/validasi.php');
include_once('../include/e_meterai_sp.php');
include_once('../include/e_sign.php');
include ('../include/email.php');
require_once ('../security_helper.php');
sanitize_global_input();
$email = new Email();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$halaman_id = 3227;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];

$page = "upload_invoice_ba.php";


$no_ba = $_GET['no_ba'];

$signType = (isset($_GET['type']) && $_GET['type'] == 'otp') ? 'otp' : 'keyla';
$isSignTypeOtp = $signType == 'otp';
$isSignTypeKeyla = $signType == 'keyla';

function showErrorMessage($message)
{
    global $no_ba, $signType;

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-danger" role="alert">
            <strong>Error!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
            <a href="upload_invoice_ba_meterai_sign.php?no_ba=<?= $no_ba ?>&type=<?= $signType ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Coba Lagi&nbsp;&nbsp;&gt;&gt;</a>
            <a href="list_ba_approve.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php
}

// Data BA Rekapitulasi
$sql_ba = "SELECT * FROM EX_BA WHERE NO_BA = '$no_ba'";
$query_ba = oci_parse($conn, $sql_ba);
oci_execute($query_ba);
$data_ba = oci_fetch_array($query_ba);
if ($data_ba) {
    $no_ba_v = $data_ba['NO_BA'];
    $org_v = $data_ba['ORG'];
    $no_vendor_v = $data_ba['NO_VENDOR'];
    $nama_vendor_v = $data_ba['NAMA_VENDOR'];
    $total_semen_v = $data_ba['KLAIM_SEMEN'];
    $total_ppdks_v = $data_ba['PDPKS'];
    $total_inv_v = $data_ba['TOTAL_INV'];
} else {
    showErrorMessage('Data BA Rekapitulasi tidak ditemukan.');
    exit;
}

// Data Invoice BA
$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_BA = '$no_ba' AND DIPAKAI = 1";
$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
oci_execute($query_invoice_ba);
$data_invoice_ba = oci_fetch_array($query_invoice_ba);

// Data User Approval
$id_user_approval = $data_invoice_ba['ID_USER_APPROVAL'];
$id_user_pembuat = $data_invoice_ba['CREATED_BY'];
$query = "SELECT * FROM TB_USER_BOOKING WHERE ID = '$id_user_approval'";
$sql = oci_parse($conn, $query);
oci_execute($sql);
$email_user_approval = oci_fetch_array($sql);
$email_user_approval = $email_user_approval['ALAMAT_EMAIL'];

if (!$data_invoice_ba['LAMPIRAN']) {
    header("Location: upload_invoice_ba.php?no_ba=$no_ba");
    exit;
}

$eMeterai = new EMeterai();
$eMeterai->refreshJwtIfExpired();

$eSign = new ESign();
$eSign->refreshJwtIfExpired();

$isPakaiSpDendaSama = $data_invoice_ba['PAKAI_SP_DENDA_SAMA'] == 1;
$isPakaiSpDendaBeda = $data_invoice_ba['PAKAI_SP_DENDA_BEDA'] == 1;

// Status Kuitansi Meterai
$kuitansiMeteraiStatus = $data_invoice_ba['METERAI_KUI_STATUS'];
$isKuitansiMeteraiBelumDikirim = $kuitansiMeteraiStatus == 0; // doc belum dikirim
$isKuitansiMeteraiSent = $kuitansiMeteraiStatus == 1; // doc sudah dikirim
$isKuitansiMeteraiSNGenerated = $kuitansiMeteraiStatus == 2; // generate sn sukses
$isKuitansiMeteraiStamped = $kuitansiMeteraiStatus == 3; // stamping sukses
$isKuitansiMeteraiDone = $kuitansiMeteraiStatus == 4; // doc berhasil didownload

// Status Kuitansi Sign
$kuitansiSignStatus = $data_invoice_ba['SIGN_KUI_STATUS'];
$isKuitansiSignBelumDikirim = $kuitansiSignStatus == 0; // doc belum dikirim
$isKuitansiSignSent = $kuitansiSignStatus == 1; // doc sudah dikirim
$isKuitansiSignOtpSent = $kuitansiSignStatus == 2; // otp berhasil dikirim
$isKuitansiSignSigned = $kuitansiSignStatus == 3; // doc berhasil ditandatangani
$isKuitansiSignDone = $kuitansiSignStatus == 4; // doc berhasil ditandatangani

// Status SP Denda Tanggal Sama Meterai
$spDendaSamaMeteraiStatus = $data_invoice_ba['METERAI_SPDS_STATUS'];
$isSpDendaSamaMeteraiBelumDikirim = $spDendaSamaMeteraiStatus == 0; // doc belum dikirim
$isSpDendaSamaMeteraiSent = $spDendaSamaMeteraiStatus == 1; // doc sudah dikirim
$isSpDendaSamaMeteraiSNGenerated = $spDendaSamaMeteraiStatus == 2; // generate sn sukses
$isSpDendaSamaMeteraiStamped = $spDendaSamaMeteraiStatus == 3; // stamping sukses
$isSpDendaSamaMeteraiDone = $spDendaSamaMeteraiStatus == 4; // doc berhasil didownload

// Status SP Denda Tanggal Sama Sign
$spDendaSamaSignSStatus = $data_invoice_ba['SIGN_SPDS_STATUS'];
$isSpDendaSamaSignBelumDikirim = $spDendaSamaSignSStatus == 0; // doc belum dikirim
$isSpDendaSamaSignSent = $spDendaSamaSignSStatus == 1; // doc sudah dikirim
$isSpDendaSamaSignOtpSent = $spDendaSamaSignSStatus == 2; // otp berhasil dikirim
$isSpDendaSamaSignSigned = $spDendaSamaSignSStatus == 3; // doc berhasil ditandatangani
$isSpDendaSamaSignDone = $spDendaSamaSignSStatus == 4; // doc berhasil ditandatangani

// Status SP Denda Tanggal Beda Meterai
$spDendaBedaMeteraiStatus = $data_invoice_ba['METERAI_SPDB_STATUS'];
$isSpDendaBedaMeteraiBelumDikirim = $spDendaBedaMeteraiStatus == 0; // doc belum dikirim
$isSpDendaBedaMeteraiSent = $spDendaBedaMeteraiStatus == 1; // doc sudah dikirim
$isSpDendaBedaMeteraiSNGenerated = $spDendaBedaMeteraiStatus == 2; // generate sn sukses
$isSpDendaBedaMeteraiStamped = $spDendaBedaMeteraiStatus == 3; // stamping sukses
$isSpDendaBedaMeteraiDone = $spDendaBedaMeteraiStatus == 4; // doc berhasil didownload

// Status SP Denda Tanggal Beda Sign
$spDendaBedaSignStatus = $data_invoice_ba['SIGN_SPDB_STATUS'];
$isSpDendaBedaSignBelumDikirim = $spDendaBedaSignStatus == 0; // doc belum dikirim
$isSpDendaBedaSignSent = $spDendaBedaSignStatus == 1; // doc sudah dikirim
$isSpDendaBedaSignOtpSent = $spDendaBedaSignStatus == 2; // otp berhasil dikirim
$isSpDendaBedaSignSigned = $spDendaBedaSignStatus == 3; // doc berhasil ditandatangani
$isSpDendaBedaSignDone = $spDendaBedaSignStatus == 4; // doc berhasil ditandatangani

/**
 * Alur E-Meterai dan E-Sign:
 * - Kuitansi
 *   1. Kirim file kuitansi ke E-Meterai
 *   2. Generate SN
 *   3. Stamping
 *   4. Download
 *   5. Kirim file kuitansi ke E-Sign
 *   6. Kirim OTP
 *   7. Tandatangani
 *   8. Download
 * - SP Denda Sama (Jika ada)
 *   1. Kirim file SP denda ke E-Meterai
 *   2. Generate SN
 *   3. Stamping
 *   4. Download
 *   5. Kirim file SP denda ke E-Sign
 *   6. Kirim OTP
 *   7. Tandatangani
 *   8. Download
 * - SP Denda Beda (Jika ada)
 *   1. Kirim file SP denda ke E-Meterai
 *   2. Generate SN
 *   3. Stamping
 *   4. Download
 *   5. Kirim file SP denda ke E-Sign
 *   6. Kirim OTP
 *   7. Tandatangani
 *   8. Download
 */

$tableName = 'EX_BA_INVOICE';
$field_id = array('NO_BA', 'DIPAKAI');
$value_id = array("$no_ba", '1');

// Menangani proses resend OTP
$isResendOtp = isset($_GET['resend']);

if ($isResendOtp) {
    // $tableName = 'EX_BA_INVOICE';
    // $field_id = array('NO_BA', 'DIPAKAI');
    // $value_id = array("$no_ba", '1');

    $isResendKuitansi = null;

    $orderId = null;
    $fieldStatus = null;
    $fieldToken = null;

    if ($isKuitansiSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_KUI_ORDER_ID'];
        $fieldStatus = 'SIGN_KUI_STATUS';
        $fieldToken = 'SIGN_KUI_TOKEN';
    } else if ($isSpDendaSamaSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_SPDS_ORDER_ID'];
        $fieldStatus = 'SIGN_SPDS_STATUS';
        $fieldToken = 'SIGN_SPDS_TOKEN';
    } else if ($isSpDendaBedaSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_SPDB_ORDER_ID'];
        $fieldStatus = 'SIGN_SPDB_STATUS';
        $fieldToken = 'SIGN_SPDB_TOKEN';
    } else {
        showErrorMessage('Tidak dapat mengirim ulang OTP');
        exit;
    }

    $resp = null;

    try {
        $resp = $eSign->getOtp($orderId);
    } catch (Exception $e) {
        $message = 'Gagal mendapatkan OTP: ' . $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriToken = $resp->token;

    $fieldNames = array($fieldStatus, $fieldToken);
    $fieldData = array(2, $peruriToken);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $show_ket = "Kode OTP berhasil dikirim ulang";
    $habis = "upload_invoice_ba_meterai_sign.php?no_ba=$no_ba&type=$signType";

?>
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
            <a href="<?= $habis ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php

    exit;
}

/* ============================================== Start Meterai Kuitansi ============================================== */

// Jika file kuitansi belum dikirim
if ($isKuitansiMeteraiBelumDikirim) {
    $filePath = dirname(__FILE__) . '/lampiran/' . $data_invoice_ba['FILE_KUITANSI'];

    // Validasi apakah file kuitansi ada
    if (!file_exists($filePath)) {
        showErrorMessage('File kuitansi tidak ditemukan: ' . $filePath);
        exit;
    }

    $file = '@' . $filePath;
    // $file = '@' . dirname(__FILE__) . '/lampiran/' . $data_invoice_ba['FILE_KUITANSI'];
    $resp = null;
    try {
        $resp = $eMeterai->uploadDocument($file);
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal kirim dokumen kuitansi ke e-Meterai: ' . $sanitizedMessage;
        // $message = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        showErrorMessage($message);
        exit;
    }

    $peruriDocId = $resp->id;
    $peruriDocName = $resp->saveAs;

    $fieldNames = array('METERAI_KUI_STATUS', 'METERAI_KUI_DOC_ID', 'METERAI_KUI_DOC_NAME');
    $fieldData = array(1, $peruriDocId, $peruriDocName);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_KUI_STATUS'] = 1;
    $data_invoice_ba['METERAI_KUI_DOC_ID'] = $peruriDocId;
    $data_invoice_ba['METERAI_KUI_DOC_NAME'] = $peruriDocName;

    $isKuitansiMeteraiBelumDikirim = false;
    $isKuitansiMeteraiSent = true;
}

// Jika file kuitansi sudah dikirim
if ($isKuitansiMeteraiSent) {
    $dataSerialNumber = array(
        'idfile' => $data_invoice_ba['METERAI_KUI_DOC_ID'],
        'namadoc' => $data_invoice_ba['METERAI_KUI_DOC_NAME'],
        'nodoc' => $data_invoice_ba['NO_INVOICE'],
        'tgldoc' => date('Y-m-d'),
        'namafile' => $data_invoice_ba['FILE_KUITANSI'],
        "namadoc" => "2", // Dokumen Transaksi
    );

    $resp = null;
    try {
        $resp = $eMeterai->generateSerialNumber($dataSerialNumber);
    } catch (Exception $e) {
      $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal mendapatkan SN: ' . $sanitizedMessage;
        showErrorMessage($message);
        exit;
    }

    $peruriSerialNumber = $resp->result->sn;
    $peruriFilenameQR = $resp->result->filenameQR;

    $fieldNames = array('METERAI_KUI_STATUS', 'METERAI_KUI_SERIAL_NUMBER', 'METERAI_KUI_DOC_QR');
    $fieldData = array(2, $peruriSerialNumber, $peruriFilenameQR);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_KUI_STATUS'] = 2;
    $data_invoice_ba['METERAI_KUI_SERIAL_NUMBER'] = $peruriSerialNumber;
    $data_invoice_ba['METERAI_KUI_DOC_QR'] = $peruriFilenameQR;

    $isKuitansiMeteraiSent = false;
    $isKuitansiMeteraiSNGenerated = true;
}

// Jika serial number file kuitansi sudah digenerate
if ($isKuitansiMeteraiSNGenerated) {
    $dataStamping = array(
        'docId' => $data_invoice_ba['METERAI_KUI_DOC_ID'],
        'certificatelevel' => 'NOT_CERTIFIED',
        'dest' => '/sharefolder/final_' . $data_invoice_ba['METERAI_KUI_DOC_ID'] . '.pdf',
        'docpass' => '',
        'location' => 'JAKARTA',
        'profileName' => 'emeteraicertificateSigner',
        'reason' => 'Dokumen Transaksi',
        'spesimenPath' => '/sharefolder/' . $data_invoice_ba['METERAI_KUI_DOC_QR'],
        'src' => '/sharefolder/doc_' . $data_invoice_ba['METERAI_KUI_DOC_ID'] . '.pdf',
        'visLLX' => 352,
        'visLLY' => 371,
        'visURX' => 419,
        'visURY' => 441,
        'visSignaturePage' => 1,
        'refToken' => $data_invoice_ba['METERAI_KUI_SERIAL_NUMBER'],
    );

    $resp = null;
    try {
        $resp = $eMeterai->stamping($dataStamping);
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal stamping: ' . $sanitizedMessage;
        showErrorMessage($message);
        exit;
    }

    $peruriFinalFileUrl = $resp->urlFile;

    $fieldNames = array('METERAI_KUI_STATUS', 'METERAI_KUI_DOC_URL');
    $fieldData = array(3, $peruriFinalFileUrl);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_KUI_STATUS'] = 3;
    $data_invoice_ba['METERAI_KUI_DOC_URL'] = $peruriFinalFileUrl;

    $isKuitansiMeteraiSNGenerated = false;
    $isKuitansiMeteraiStamped = true;
}

// Jika meterai file kuitansi sudah dibubuhkan
if ($isKuitansiMeteraiStamped) {
    $resp = null;
    try {
        $resp = $eMeterai->downloadDocument($data_invoice_ba['METERAI_KUI_DOC_URL']);
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal mengunggah dokumen: ' . $sanitizedMessage;
        showErrorMessage($message);
        exit;
    }

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_KUITANSI'], 'w');
    fwrite($pdf, $resp);
    fclose($pdf);

    $fieldNames = array('METERAI_KUI_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_KUI_STATUS'] = 4;

    $isKuitansiMeteraiStamped = false;
    $isKuitansiMeteraiDone = true;
}

/* ============================================== End Meterai Kuitansi ============================================== */



/* ============================================== Start Sign Kuitansi ============================================== */

// Jika file kuitansi sudah didownload
if ($isKuitansiMeteraiDone && $isKuitansiSignBelumDikirim) {
    // Get base64 of document file
    $fileName = $data_invoice_ba['FILE_KUITANSI'];
    $fileContent = file_get_contents('lampiran/' . $fileName);
    $fileContent = base64_encode($fileContent);

    $orderId = null;
    try {
        // Send document to peruri
        $orderId = $eSign->uploadDocument(array(
            'email' => $email_user_approval,
            'fileName' => $fileName,
            'base64Document' => $fileContent,
            'lowerLeftX' => '420',
            'lowerLeftY' => '358',
            'upperRightX' => '562',
            'upperRightY' => '457',
            'page' => '1',
            'varReason' => 'Approve Invoice',
        ));
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal kirim dokumen kuitansi ke e-Sign: ' . $sanitizedMessage;
        showErrorMessage($message);
        exit;
    }

    // Save order id yang didapatkan dari eSign
    $fieldNames = array('SIGN_KUI_STATUS', 'SIGN_KUI_ORDER_ID');
    $fieldData = array(1, $orderId);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_KUI_STATUS'] = 1;
    $data_invoice_ba['SIGN_KUI_ORDER_ID'] = $orderId;

    $isKuitansiSignBelumDikirim = false;
    $isKuitansiSignSent = true;
}

// if ($isSignTypeOtp && ($isKuitansiSignSent) && $_GET['resend'] != '1' && isset($_GET['submit']) && $_GET['submit'] == 'save_uploaded_files') {
if ($isSignTypeOtp && ($isKuitansiSignSent)) {
    // $tableName = 'EX_BA_INVOICE';
    // $field_id = array('NO_BA', 'DIPAKAI');
    // $value_id = array("$no_ba", '1');
    // echo "OTP Sign Kuitansi tanpa ulang";

    $resp = null;
    try {
        $resp = $eSign->getOtp($data_invoice_ba['SIGN_KUI_ORDER_ID']);
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal mendapatkan OTP e-Sign: ' . $sanitizedMessage;
        showErrorMessage($message);
        exit;
    }

    $peruriToken = $resp->token;

    $fieldNames = array('SIGN_KUI_STATUS', 'SIGN_KUI_TOKEN');
    $fieldData = array(2, $peruriToken);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_KUI_STATUS'] = 2;
    $data_invoice_ba['SIGN_KUI_TOKEN'] = $peruriToken;

    $isKuitansiSignSent = false;
    $isKuitansiSignOtpSent = true;
}

if ($isKuitansiSignSent || $isKuitansiSignOtpSent) {
    try {
        // cek status doc
        $resp = $eSign->checkDoc($data_invoice_ba['SIGN_KUI_ORDER_ID']);

        // update status di DB
        if($resp){
            $fieldNames = array('SIGN_KUI_STATUS');
            $fieldData = array(3);
            $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
            $data_invoice_ba['SIGN_KUI_STATUS'] = 3;

            $isKuitansiSignSent = false;
            $isKuitansiSignOtpSent = false;
            $isKuitansiSignSigned = true;
        }
    } catch (Exception $e) {
        // $message = $e->getMessage();
        // showErrorMessage($message);
        // exit;
    }
}

if ($isKuitansiSignSigned) {
    $peruriPdfBase64Encoded = null;
    try {
        $peruriPdfBase64Encoded = $eSign->downloadDocument($data_invoice_ba['SIGN_KUI_ORDER_ID']);
    } catch (Exception $e) {
        $sanitizedMessage = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        $message = 'Gagal download kuitansi dari e-Sign: ' . $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriPdfBase64Decoded = base64_decode($peruriPdfBase64Encoded);

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_KUITANSI'], 'w');
    fwrite($pdf, $peruriPdfBase64Decoded);
    fclose($pdf);

    $fieldNames = array('SIGN_KUI_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_KUI_STATUS'] = 4;

    $isKuitansiSignSigned = false;
    $isKuitansiSignDone = true;
}

/* ============================================== End Sign Kuitansi ============================================== */



/* ============================================== Start Meterai SP Denda Tanggal Sama ============================================== */

// Jika file SP denda belum dikirim
if ($isPakaiSpDendaSama && $isKuitansiSignDone && $isSpDendaSamaMeteraiBelumDikirim) {
    $file = '@' . dirname(__FILE__) . '/lampiran/' . $data_invoice_ba['FILE_SP_DENDA_SAMA'];

    $resp = null;
    try {
        $resp = $eMeterai->uploadDocument($file);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriDocId = $resp->id;
    $peruriDocName = $resp->saveAs;

    $fieldNames = array('METERAI_SPDS_STATUS', 'METERAI_SPDS_DOC_ID', 'METERAI_SPDS_DOC_NAME');
    $fieldData = array(1, $peruriDocId, $peruriDocName);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDS_STATUS'] = 1;
    $data_invoice_ba['METERAI_SPDS_DOC_ID'] = $peruriDocId;
    $data_invoice_ba['METERAI_SPDS_DOC_NAME'] = $peruriDocName;

    $isSpDendaSamaMeteraiBelumDikirim = false;
    $isSpDendaSamaMeteraiSent = true;
}

// Jika file SP denda sudah dikirim
if ($isPakaiSpDendaSama && $isSpDendaSamaMeteraiSent) {
    $dataSerialNumber = array(
        'idfile' => $data_invoice_ba['METERAI_SPDS_DOC_ID'],
        'namadoc' => $data_invoice_ba['METERAI_SPDS_DOC_NAME'],
        'nodoc' => $data_invoice_ba['NO_INVOICE'],
        'tgldoc' => date('Y-m-d'),
        'namafile' => $data_invoice_ba['FILE_SP_DENDA_SAMA'],
        "namadoc" => "2", // Dokumen Transaksi
    );

    $resp = null;
    try {
        $resp = $eMeterai->generateSerialNumber($dataSerialNumber);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriSerialNumber = $resp->result->sn;
    $peruriFilenameQR = $resp->result->filenameQR;

    $fieldNames = array('METERAI_SPDS_STATUS', 'METERAI_SPDS_SERIAL_NUMBER', 'METERAI_SPDS_DOC_QR');
    $fieldData = array(2, $peruriSerialNumber, $peruriFilenameQR);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDS_STATUS'] = 2;
    $data_invoice_ba['METERAI_SPDS_SERIAL_NUMBER'] = $peruriSerialNumber;
    $data_invoice_ba['METERAI_SPDS_DOC_QR'] = $peruriFilenameQR;

    $isSpDendaSamaMeteraiSent = false;
    $isSpDendaSamaMeteraiSNGenerated = true;
}

// Jika serial number file SP denda sudah digenerate
if ($isPakaiSpDendaSama && $isSpDendaSamaMeteraiSNGenerated) {
    $dataStamping = array(
        'docId' => $data_invoice_ba['METERAI_SPDS_DOC_ID'],
        'certificatelevel' => 'NOT_CERTIFIED',
        'dest' => '/sharefolder/final_' . $data_invoice_ba['METERAI_SPDS_DOC_ID'] . '.pdf',
        'docpass' => '',
        'location' => 'JAKARTA',
        'profileName' => 'emeteraicertificateSigner',
        'reason' => 'Dokumen Transaksi',
        'spesimenPath' => '/sharefolder/' . $data_invoice_ba['METERAI_SPDS_DOC_QR'],
        'src' => '/sharefolder/doc_' . $data_invoice_ba['METERAI_SPDS_DOC_ID'] . '.pdf',
        'visLLX' => 325,
        'visLLY' => 200,
        'visURX' => 391,
        'visURY' => 265,
        'visSignaturePage' => 1,
        'refToken' => $data_invoice_ba['METERAI_SPDS_SERIAL_NUMBER'],
    );

    $resp = null;
    try {
        $resp = $eMeterai->stamping($dataStamping);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriFinalFileUrl = $resp->urlFile;

    $fieldNames = array('METERAI_SPDS_STATUS', 'METERAI_SPDS_DOC_URL');
    $fieldData = array(3, $peruriFinalFileUrl);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDS_STATUS'] = 3;
    $data_invoice_ba['METERAI_SPDS_DOC_URL'] = $peruriFinalFileUrl;

    $isSpDendaSamaMeteraiSNGenerated = false;
    $isSpDendaSamaMeteraiStamped = true;
}

// Jika meterai file SP denda sudah dibubuhkan
if ($isPakaiSpDendaSama && $isSpDendaSamaMeteraiStamped) {
    $resp = null;
    try {
        $resp = $eMeterai->downloadDocument($data_invoice_ba['METERAI_SPDS_DOC_URL']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_SP_DENDA_SAMA'], 'w');
    fwrite($pdf, $resp);
    fclose($pdf);

    $fieldNames = array('METERAI_SPDS_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDS_STATUS'] = 4;

    $isSpDendaSamaMeteraiStamped = false;
    $isSpDendaSamaMeteraiDone = true;
}

/* ============================================== End Meterai SP Denda Tanggal Sama ============================================== */



/* ============================================== Start Sign SP Denda Tanggal Sama ============================================== */

// Jika file SP denda sudah didownload
if ($isPakaiSpDendaSama && $isSpDendaSamaMeteraiDone && $isSpDendaSamaSignBelumDikirim) {
    // Get base64 of document file
    $fileName = $data_invoice_ba['FILE_SP_DENDA_SAMA'];
    $fileContent = file_get_contents('lampiran/' . $fileName);
    $fileContent = base64_encode($fileContent);

    $orderId = null;
    try {
        // Send document to peruri
        $orderId = $eSign->uploadDocument(array(
            'email' => $email_user_approval,
            'fileName' => $fileName,
            'base64Document' => $fileContent,
            'lowerLeftX' => '389',
            'lowerLeftY' => '187',
            'upperRightX' => '528',
            'upperRightY' => '278',
            'page' => '1',
            'varReason' => 'Approve SP Denda',
        ));
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    // Save order id yang didapatkan dari eSign
    $fieldNames = array('SIGN_SPDS_STATUS', 'SIGN_SPDS_ORDER_ID');
    $fieldData = array(1, $orderId);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDS_STATUS'] = 1;
    $data_invoice_ba['SIGN_SPDS_ORDER_ID'] = $orderId;

    $isSpDendaSamaSignBelumDikirim = false;
    $isSpDendaSamaSignSent = true;
}

if ($isPakaiSpDendaSama && $isSignTypeOtp && $isSpDendaSamaSignSent) {
    $resp = null;
    try {
        $resp = $eSign->getOtp($data_invoice_ba['SIGN_SPDS_ORDER_ID']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriToken = $resp->token;

    $fieldNames = array('SIGN_SPDS_STATUS', 'SIGN_SPDS_TOKEN');
    $fieldData = array(2, $peruriToken);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDS_STATUS'] = 2;
    $data_invoice_ba['SIGN_SPDS_TOKEN'] = $peruriToken;

    $isSpDendaSamaSignSent = false;
    $isSpDendaSamaSignOtpSent = true;
}

if ($isSpDendaSamaSignSent || $isSpDendaSamaSignOtpSent) {
    try {
        // cek status doc
        $resp = $eSign->checkDoc($data_invoice_ba['SIGN_SPDS_ORDER_ID']);

        // update status di DB
        if($resp){
            $fieldNames = array('SIGN_SPDS_STATUS');
            $fieldData = array(3);
            $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
            $data_invoice_ba['SIGN_SPDS_STATUS'] = 3;

            $isSpDendaSamaSignSent = false;
            $isSpDendaSamaSignOtpSent = false;
            $isSpDendaSamaSignSigned = true;
        }
    } catch (Exception $e) {
        // $message = $e->getMessage();
        // showErrorMessage($message);
        // exit;
    }
}

if ($isPakaiSpDendaSama && $isSpDendaSamaSignSigned) {
    $peruriPdfBase64Encoded = null;
    try {
        $peruriPdfBase64Encoded = $eSign->downloadDocument($data_invoice_ba['SIGN_SPDS_ORDER_ID']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriPdfBase64Decoded = base64_decode($peruriPdfBase64Encoded);

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_SP_DENDA_SAMA'], 'w');
    fwrite($pdf, $peruriPdfBase64Decoded);
    fclose($pdf);

    $fieldNames = array('SIGN_SPDS_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDS_STATUS'] = 4;

    $isSpDendaSamaSignSigned = false;
    $isSpDendaSamaSignDone = true;
}

/* ============================================== End Sign SP Denda Sama ============================================== */

/* ============================================== Start Meterai SP Denda Tanggal Berbeda ============================================== */

// Jika file SP denda belum dikirim
if ($isPakaiSpDendaBeda && (($isPakaiSpDendaSama && $isSpDendaSamaSignDone) || (!$isPakaiSpDendaSama && $isKuitansiSignDone)) && $isSpDendaBedaMeteraiBelumDikirim) {
    $file = '@' . dirname(__FILE__) . '/lampiran/' . $data_invoice_ba['FILE_SP_DENDA_BEDA'];

    $resp = null;
    try {
        $resp = $eMeterai->uploadDocument($file);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriDocId = $resp->id;
    $peruriDocName = $resp->saveAs;

    $fieldNames = array('METERAI_SPDB_STATUS', 'METERAI_SPDB_DOC_ID', 'METERAI_SPDB_DOC_NAME');
    $fieldData = array(1, $peruriDocId, $peruriDocName);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDB_STATUS'] = 1;
    $data_invoice_ba['METERAI_SPDB_DOC_ID'] = $peruriDocId;
    $data_invoice_ba['METERAI_SPDB_DOC_NAME'] = $peruriDocName;

    $isSpDendaBedaMeteraiBelumDikirim = false;
    $isSpDendaBedaMeteraiSent = true;
}

// Jika file SP denda sudah dikirim
if ($isPakaiSpDendaBeda && $isSpDendaBedaMeteraiSent) {
    $dataSerialNumber = array(
        'idfile' => $data_invoice_ba['METERAI_SPDB_DOC_ID'],
        'namadoc' => $data_invoice_ba['METERAI_SPDB_DOC_NAME'],
        'nodoc' => $data_invoice_ba['NO_INVOICE'],
        'tgldoc' => date('Y-m-d'),
        'namafile' => $data_invoice_ba['FILE_SP_DENDA_BEDA'],
        "namadoc" => "2", // Dokumen Transaksi
    );

    $resp = null;
    try {
        $resp = $eMeterai->generateSerialNumber($dataSerialNumber);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriSerialNumber = $resp->result->sn;
    $peruriFilenameQR = $resp->result->filenameQR;

    $fieldNames = array('METERAI_SPDB_STATUS', 'METERAI_SPDB_SERIAL_NUMBER', 'METERAI_SPDB_DOC_QR');
    $fieldData = array(2, $peruriSerialNumber, $peruriFilenameQR);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDB_STATUS'] = 2;
    $data_invoice_ba['METERAI_SPDB_SERIAL_NUMBER'] = $peruriSerialNumber;
    $data_invoice_ba['METERAI_SPDB_DOC_QR'] = $peruriFilenameQR;

    $isSpDendaBedaMeteraiSent = false;
    $isSpDendaBedaMeteraiSNGenerated = true;
}

// Jika serial number file SP denda sudah digenerate
if ($isPakaiSpDendaBeda && $isSpDendaBedaMeteraiSNGenerated) {
    $dataStamping = array(
        'docId' => $data_invoice_ba['METERAI_SPDB_DOC_ID'],
        'certificatelevel' => 'NOT_CERTIFIED',
        'dest' => '/sharefolder/final_' . $data_invoice_ba['METERAI_SPDB_DOC_ID'] . '.pdf',
        'docpass' => '',
        'location' => 'JAKARTA',
        'profileName' => 'emeteraicertificateSigner',
        'reason' => 'Dokumen Transaksi',
        'spesimenPath' => '/sharefolder/' . $data_invoice_ba['METERAI_SPDB_DOC_QR'],
        'src' => '/sharefolder/doc_' . $data_invoice_ba['METERAI_SPDB_DOC_ID'] . '.pdf',
        'visLLX' => 318,
        'visLLY' => 152,
        'visURX' => 390,
        'visURY' => 220,
        'visSignaturePage' => 1,
        'refToken' => $data_invoice_ba['METERAI_SPDB_SERIAL_NUMBER'],
    );

    $resp = null;
    try {
        $resp = $eMeterai->stamping($dataStamping);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriFinalFileUrl = $resp->urlFile;

    $fieldNames = array('METERAI_SPDB_STATUS', 'METERAI_SPDB_DOC_URL');
    $fieldData = array(3, $peruriFinalFileUrl);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDB_STATUS'] = 3;
    $data_invoice_ba['METERAI_SPDB_DOC_URL'] = $peruriFinalFileUrl;

    $isSpDendaBedaMeteraiSNGenerated = false;
    $isSpDendaBedaMeteraiStamped = true;
}

// Jika meterai file SP denda sudah dibubuhkan
if ($isPakaiSpDendaBeda && $isSpDendaBedaMeteraiStamped) {
    $resp = null;
    try {
        $resp = $eMeterai->downloadDocument($data_invoice_ba['METERAI_SPDB_DOC_URL']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_SP_DENDA_BEDA'], 'w');
    fwrite($pdf, $resp);
    fclose($pdf);

    $fieldNames = array('METERAI_SPDB_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['METERAI_SPDB_STATUS'] = 4;

    $isSpDendaBedaMeteraiStamped = false;
    $isSpDendaBedaMeteraiDone = true;
}

/* ============================================== End Meterai SP Denda Tanggal Berbeda ============================================== */



/* ============================================== Start Sign SP Denda Tanggal Berbeda ============================================== */

// Jika file SP denda sudah didownload
if ($isPakaiSpDendaBeda && $isSpDendaBedaMeteraiDone && $isSpDendaBedaSignBelumDikirim) {
    // Get base64 of document file
    $fileName = $data_invoice_ba['FILE_SP_DENDA_BEDA'];
    $fileContent = file_get_contents('lampiran/' . $fileName);
    $fileContent = base64_encode($fileContent);

    $orderId = null;
    try {
        // Send document to peruri
        $orderId = $eSign->uploadDocument(array(
            'email' => $email_user_approval,
            'fileName' => $fileName,
            'base64Document' => $fileContent,
            'lowerLeftX' => '402',
            'lowerLeftY' => '143',
            'upperRightX' => '515',
            'upperRightY' => '229',
            'page' => '1',
            'varReason' => 'Approve SP Denda Beda Tanggal',
        ));
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    // Save order id yang didapatkan dari eSign
    $fieldNames = array('SIGN_SPDB_STATUS', 'SIGN_SPDB_ORDER_ID');
    $fieldData = array(1, $orderId);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDB_STATUS'] = 1;
    $data_invoice_ba['SIGN_SPDB_ORDER_ID'] = $orderId;

    $isSpDendaBedaSignBelumDikirim = false;
    $isSpDendaBedaSignSent = true;
}

if ($isPakaiSpDendaBeda && $isSignTypeOtp && $isSpDendaBedaSignSent) {
    $resp = null;
    try {
        $resp = $eSign->getOtp($data_invoice_ba['SIGN_SPDB_ORDER_ID']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriToken = $resp->token;

    $fieldNames = array('SIGN_SPDB_STATUS', 'SIGN_SPDB_TOKEN');
    $fieldData = array(2, $peruriToken);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDB_STATUS'] = 2;
    $data_invoice_ba['SIGN_SPDB_TOKEN'] = $peruriToken;

    $isSpDendaBedaSignSent = false;
    $isSpDendaBedaSignOtpSent = true;
}

if ($isSpDendaBedaSignSent || $isSpDendaBedaSignOtpSent) {
    try {
        // cek status doc
        $resp = $eSign->checkDoc($data_invoice_ba['SIGN_SPDB_ORDER_ID']);

        // update status di DB
        if($resp){
            $fieldNames = array('SIGN_SPDB_STATUS');
            $fieldData = array(3);
            $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
            $data_invoice_ba['SIGN_SPDB_STATUS'] = 3;

            $isSpDendaBedaSignSent = false;
            $isSpDendaBedaSignOtpSent = false;
            $isSpDendaBedaSignSigned = true;
        }
    } catch (Exception $e) {
        // $message = $e->getMessage();
        // showErrorMessage($message);
        // exit;
    }
}

if ($isPakaiSpDendaBeda && $isSpDendaBedaSignSigned) {
    $peruriPdfBase64Encoded = null;
    try {
        $peruriPdfBase64Encoded = $eSign->downloadDocument($data_invoice_ba['SIGN_SPDB_ORDER_ID']);
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $peruriPdfBase64Decoded = base64_decode($peruriPdfBase64Encoded);

    $pdf = fopen('lampiran/' . $data_invoice_ba['FILE_SP_DENDA_BEDA'], 'w');
    fwrite($pdf, $peruriPdfBase64Decoded);
    fclose($pdf);

    $fieldNames = array('SIGN_SPDB_STATUS');
    $fieldData = array(4);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    $data_invoice_ba['SIGN_SPDB_STATUS'] = 4;

    $isSpDendaBedaSignSigned = false;
    $isSpDendaBedaSignDone = true;
}

/* ============================================== End Sign SP Denda Berbeda ============================================== */

// Jika semua dokumen sudah diberi meterai dan sign
if (
    // ($isPakaiSpDendaBeda && $isSpDendaBedaSignDone && $isPakaiSpDendaSama && $isSpDendaSamaSignDone) ||
    // ($isPakaiSpDendaBeda && $isSpDendaBedaSignDone && !$isPakaiSpDendaSama && $isKuitansiSignDone) ||
    // (!$isPakaiSpDendaBeda && $isPakaiSpDendaSama && $isSpDendaSamaSignDone) ||
    // (!$isPakaiSpDendaSama && $isKuitansiSignDone)
    ($isKuitansiMeteraiDone && $isKuitansiSignDone && $isPakaiSpDendaSama && $isSpDendaSamaMeteraiDone && $isSpDendaSamaSignDone && $isPakaiSpDendaBeda && $isSpDendaBedaMeteraiDone && $isSpDendaBedaSignDone) ||
    ($isKuitansiMeteraiDone && $isKuitansiSignDone && !$isPakaiSpDendaSama && $isPakaiSpDendaBeda && $isSpDendaBedaMeteraiDone && $isSpDendaBedaSignDone) ||
    ($isKuitansiMeteraiDone && $isKuitansiSignDone && $isPakaiSpDendaSama && $isSpDendaSamaMeteraiDone && $isSpDendaSamaSignDone && !$isPakaiSpDendaBeda) ||
    ($isKuitansiMeteraiDone && $isKuitansiSignDone && !$isPakaiSpDendaSama && !$isPakaiSpDendaBeda) 
) {

    // $sql_cekulang_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_BA = '$no_ba' AND DIPAKAI = 1 ORDER BY ID DESC";
    // $query_cekulang_invoice_ba = oci_parse($conn, $sql_cekulang_invoice_ba);
    // oci_execute($query_cekulang_invoice_ba);
    // $data_cekulang_invoice_ba = oci_fetch_array($query_cekulang_invoice_ba);

    // $needdata = 1;
    // $cekdata = 1;

    // if ($data_cekulang_invoice_ba['PAKAI_SP_DENDA_SAMA'] == 1) {
    //     $needdata++;
    // }
    // if ($data_cekulang_invoice_ba['PAKAI_SP_DENDA_BEDA'] == 1) {
    //     $needdata++;
    // }

    // if ($data_cekulang_invoice_ba['PAKAI_SP_DENDA_SAMA'] == 1 && $data_cekulang_invoice_ba['METERAI_SPDS_STATUS'] == 4 && $data_cekulang_invoice_ba['SIGN_SPDS_STATUS'] == 4 ) {
    //     $cekdata++;
    // }

    // if ($data_cekulang_invoice_ba['PAKAI_SP_DENDA_BEDA'] == 1 && $data_cekulang_invoice_ba['METERAI_SPDB_STATUS'] == 4 && $data_cekulang_invoice_ba['SIGN_SPDB_STATUS'] == 4 ) {
    //     $cekdata++;
    // }

    // if ($needdata == $cekdata) {
        $json_isi_file = $data_invoice_ba['LAMPIRAN'];
        $action = "upload_invoice_ba";
        include('formula.php');
  //   } else {
  //       // header("Location: upload_invoice_ba_meterai_sign.php?no_ba=$no_ba&type=$signType&resend=1");
  // //    exit;
  //   }

?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
            <a href="<?= $habis ?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php

    exit;
}

// Jika salah satu dari file tidak memiliki status OTP terkirim, maka alihkan ke halaman upload_invoice_ba.php
if (!(
    (
        ($isSignTypeKeyla && $isPakaiSpDendaBeda && ($isSpDendaBedaSignSent || $isSpDendaBedaSignOtpSent)) ||
        ($isSignTypeOtp && $isPakaiSpDendaBeda && $isSpDendaBedaSignOtpSent)
    ) ||
    (
        ($isSignTypeKeyla && $isPakaiSpDendaSama && ($isSpDendaSamaSignSent || $isSpDendaSamaSignOtpSent)) ||
        ($isSignTypeOtp && $isPakaiSpDendaSama && $isSpDendaSamaSignOtpSent)
    ) ||
    (
        ($isSignTypeKeyla && ($isKuitansiSignSent || $isKuitansiSignOtpSent)) ||
        ($isSignTypeOtp && $isKuitansiSignOtpSent)
    )
)) {
    echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =upload_invoice_ba.php?no_ba=$no_ba'>";
    exit;
}

if (isset($_POST['submit']) && $_POST['submit'] == 'submit_sign') {
    $otpCode = $_POST['OTP'];
    $keylaCode = $_POST['keyla'];

    $orderId = null;
    $token = null;
    $fieldStatus = null;

    if ($isKuitansiSignSent || $isKuitansiSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_KUI_ORDER_ID'];
        $token = $data_invoice_ba['SIGN_KUI_TOKEN'];
        $fieldStatus = 'SIGN_KUI_STATUS';
    } else if ($isSpDendaSamaSignSent || $isSpDendaSamaSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_SPDS_ORDER_ID'];
        $token = $data_invoice_ba['SIGN_SPDS_TOKEN'];
        $fieldStatus = 'SIGN_SPDS_STATUS';
    } else if ($isSpDendaBedaSignSent || $isSpDendaBedaSignOtpSent) {
        $orderId = $data_invoice_ba['SIGN_SPDB_ORDER_ID'];
        $token = $data_invoice_ba['SIGN_SPDB_TOKEN'];
        $fieldStatus = 'SIGN_SPDB_STATUS';
    } else {
        $message = 'Tidak dapat mengirim ulang OTP';
        showErrorMessage($message);
        exit;
    }

    if ($isSignTypeKeyla) {
        $otpCode = '';
        $token = $keylaCode;
    }

    try {
        $eSign->signing(array(
            'otpCode' => $otpCode,
            'orderId' => $orderId,
            'token' => $token,
        ));
    } catch (Exception $e) {
        $message = $e->getMessage();
        showErrorMessage($message);
        exit;
    }

    $fieldNames = array($fieldStatus);
    $fieldData = array(3);

    $fungsi->update($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

    //sendEmail
    $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$data_ba['ID_USER_APPROVAL'];
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];

    $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$id_user_pembuat;
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    if(empty($mailTo)){
        $mailTo = $row[ALAMAT_EMAIL];
    }else{
        $mailTo = ', '.$row[ALAMAT_EMAIL];
    }
    // $mailTo = ', <EMAIL>';
    $mailCc = '';
    $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
    <div align=\"center\">
    <thead>
    <tr class=\"quote\">
    <td ><strong>&nbsp;&nbsp;No.</strong></td>
    <td align=\"center\"><strong>ORG</strong></td>
    <td align=\"center\"><strong>BA REKAPITULASI</strong></td>
    <td align=\"center\"><strong>EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
    <td align=\"center\"><strong>PDPKS</strong></td>
    <td align=\"center\"><strong>TOTAL</strong></td>
    <td align=\"center\"><strong>STATUS</strong></td>
    </tr>
    </thead>
    <tbody>";

    $email_content_table .= " 
    <td align=\"center\">1</td>
    <td align=\"center\">".$org_v."</td>       
    <td align=\"center\">".$no_ba_v."</td>
    <td align=\"center\">".$no_vendor_v."</td>
    <td align=\"center\">".$nama_vendor_v."</td>
    <td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
    <td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
    <td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
    <td align=\"center\">Open</td>
    </tr>";
    
	//sendMail
    $email->sendMailTest($mailTo, $mailCc, 'Notifikasi Upload Invoice Atas BA ', $no_invoice_in, 'Mohon untuk ditindaklanjuti pengajuan Dokumen tsb.', $email_content_table);
    //end sendEmail

    header("Location: upload_invoice_ba_meterai_sign.php?no_ba=$no_ba&type=$signType");
    exit;
}

?>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Sign Kuitansi</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
    table.excel {
        border-style: ridge;
        border-width: 1;
        border-collapse: collapse;
        font-family: sans-serif;
        font-size: 12px;
    }

    table.excel thead th,
    table.excel tbody th {
        background: #CCCCCC;
        border-style: ridge;
        border-width: 1;
        text-align: center;
        vertical-align: bottom;
    }

    table.excel tbody th {
        text-align: center;
        width: 20px;
    }

    table.excel tbody td {
        vertical-align: bottom;
    }

    table.excel tbody td {
        padding: 0 3px;
        border: 1px solid #EEEEEE;
    }
</style>

<body>
    <div align="center">
        <table width="800" align="center" class="adminheading" border="0">
            <tr>
                <th class="da2">Sign <?= $isSpDendaSamaSignBelumDikirim ? 'Kuitansi' : 'SP Denda' ?></th>
            </tr>
        </table>
    </div>

    <form method="post" name="import" id="import" action="">
        <table width="800" align="center" class="adminform">
            <tr height="30">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso" colspan="3">
                    <h3 style="margin-bottom: 0 !important;">
                        &nbsp;&nbsp;E-Sign

                        <?php if ($isKuitansiSignOtpSent) : ?>
                            Kuitansi
                        <?php elseif ($isSpDendaSamaSignOtpSent) : ?>
                            SP Denda Tanggal Sama
                        <?php elseif ($isSpDendaBedaSignOtpSent) : ?>
                            SP Denda Tanggal Berbeda
                        <?php endif; ?>
                    </h3>
                </td>
            </tr>
            <?php if ($isSignTypeOtp) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;OTP</td>
                    <td class="puso" width="20">:</td>
                    <td><input type="number" id="OTP" name="OTP" required /></td>
                </tr>
            <?php endif ?>
            <?php if ($isSignTypeKeyla) : ?>
                <tr>
                    <td class="puso" width="120">&nbsp;&nbsp;&nbsp;Kode Keyla</td>
                    <td class="puso" width="20">:</td>
                    <td><input type="text" id="keyla" name="keyla" autocomplete="off" required /></td>
                </tr>
            <?php endif ?>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td>
                    <button type="submit" id="submit-button" name="submit" class="button" value="submit_sign" class="btn btn-success" style="margin-right: 4px; cursor: pointer; padding: 4px 8px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">
                        Sign
                    </button>

                    <?php if ($isSignTypeOtp) : ?>
                        <a id="resend" href="upload_invoice_ba_meterai_sign.php?no_ba=<?= $no_ba ?>&type=<?= $signType ?>&resend=1">
                            Kirim Ulang OTP
                        </a>
                    <?php endif ?>
                </td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
        </table>
    </form>
    <br><br>

    <div align="center">
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

    <script>
        var form = document.getElementById('import');
        var btnSubmit = document.getElementById('submit-button');
        var btnResend = document.getElementById('resend');

        form.addEventListener('submit', function(e) {
            btnSubmit.innerHTML = 'Loading...';
        });

        btnResend.addEventListener('click', function(e) {
            btnResend.innerHTML = 'Loading...';
        });
    </script>
</body>

</html>
