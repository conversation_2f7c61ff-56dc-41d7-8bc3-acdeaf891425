<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$periode = date('Y-m', strtotime(htmlspecialchars($_REQUEST['periode'])));
$regional = htmlspecialchars($_REQUEST['regional']);
$kd_prop = htmlspecialchars($_REQUEST['kd_prop']);
$distrik = htmlspecialchars($_REQUEST['distrik']);
$distrik_ret = htmlspecialchars($_REQUEST['distrik_ret']);
$brand = htmlspecialchars($_REQUEST['brand']);
$target = htmlspecialchars($_REQUEST['target']);
$target_alloc = htmlspecialchars($_REQUEST['target_alloc']);

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 8;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                $messageLog = array();

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    $mapLog = array();
                    // Skip baris yang kosong
                    if (empty($row[1]) && empty($row[2]) && empty($row[3]) && empty($row[6])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1]) || empty($row[2]) || empty($row[3]) || empty($row[6])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " memiliki data yang tidak lengkap. ";
                        array_push($messageLog, $mapLog);
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3],$row[4], $row[5], $row[6], $row[7])) {
                        $check_plafon = checkSisaPlafon($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3],$row[4], $row[5], $row[6], $row[7], $row[8]);
                        
                        if ($check_plafon['status']) {
                            if ($row[8]) {
                                $messageRows['failedUpdate'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " Gagal update, SNOP akhir tidak boleh kurang dari Target SPC (".$check_plafon['target_spc']."). ";
                                array_push($messageLog, $mapLog);
                                continue;
                            }else {
                                $messageRows['database'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " Data sudah ada di database. ";
                                array_push($messageLog, $mapLog);
                                continue;
                            }
                        }else {
                            if (update($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3], $row[4], $row[5], $row[6], $row[7], $row[8], $created_by)) {
                                $messageRows['successUpdate'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " berhasil update data. ";
                                array_push($messageLog, $mapLog);
                                continue;
                            } else {
                                $messageRows['system'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " gagal diinputkan karena kesalahan sistem. ";
                                array_push($messageLog, $mapLog);
                                continue;
                            }
                        }
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (insert($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3], $row[4], $row[5], $row[6], $row[7], $row[8], $created_by)) {
                        $messageRows['success'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " berhasil diinputkan. ";
                        array_push($messageLog, $mapLog);
                    } else {
                        $messageRows['system'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " gagal diinputkan karena kesalahan sistem. ";
                        array_push($messageLog, $mapLog);
                    }
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris yang sukses di update
                if (!empty($messageRows['successUpdate'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['successUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diupdate. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }
                
                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['failedUpdate'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['failedUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " Gagal Update, melebihi Traget SPC. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage, 'log' => $messageLog));
            }
        }
        break;        
        case 'show' :
        {
            $filt_periode = $filter_periode;
            $filt = array('periode' => $filt_periode);
            displayData($conn, $filt);
        }
        break;
        case 'add':
        {
            if (checkDuplicateData($conn,$periode,$regional,$kd_prop, $distrik_ret, $distrik, $brand, $target)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn,$periode,$regional,$kd_prop,$distrik_ret,$distrik, $brand, $target, $target_alloc, $created_by)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            $check_plafon = checkSisaPlafon($conn,$periode,$regional,$kd_prop, $distrik_ret, $distrik, $brand, $target, $target_alloc);
            if ($check_plafon['status']) {
                echo json_encode(array('errorMsg' => 'SNOP akhir tidak boleh kurang dari Target SPC ('.$check_plafon['target_spc'].')!'));
            }else {
                $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set PERIODE = '$periode', REGIONAL = '$regional', KD_PROP = '$kd_prop', DISTRIK_RET = '$distrik_ret', KD_DISTRIK = '$distrik', BRAND = '$brand', TARGET = '$target', TARGET_ALLOC = '$target_alloc', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
                $querycek= oci_parse($conn, $sqlcek);
                $return=oci_execute($querycek);
                if ($return){
                    echo json_encode(array('success'=>true,'info'=>"Edit data success"));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'multipleDel' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID'];          
                $sql = "UPDATE MAPPING_PLAFON_TARGET set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
    }
}

function checkSisaPlafon($conn,$periode,$regional,$kd_prop, $distrik_ret_bt, $distrik_bt, $brand, $target, $target_alloc){
    $distrik_ret = trim($distrik_ret_bt);
    $distrik_ret = $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null ? null : $distrik_ret ;
    $distrik = trim($distrik_bt);
    $distrik = $distrik == "" || $distrik == " " || $distrik == null ? null : $distrik ;

    $sql_count = "SELECT
                COALESCE(mpt.TARGET, 0) AS TARGET,
                COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_ALLOC,
                COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                CASE
                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                    ELSE tbt2.TARGET
                END AS TARGET_SPC,
                COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                    WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                    ELSE tbt2.TARGET
                END, 0) AS SISA_PLAFON
            FROM
                MAPPING_PLAFON_TARGET mpt
            LEFT JOIN (
                SELECT
                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                    mmw.KODE_REGION,
                    mmw.KODE_PROVINSI,
                    mmw.DISTRIK_RET,
                    tb1.BRAND,
                    sum(tb1.TARGET) AS TARGET
                FROM
                    ZSD_TARGET_HARIAN_NEW_BRAND tb1
                LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                    tb1.DISTRIK = mmw.KODE_DISTRIK
                WHERE
                    tb1.DEL_MARK = '0'
                GROUP BY
                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                    mmw.KODE_REGION,
                    mmw.KODE_PROVINSI,
                    mmw.DISTRIK_RET,
                    tb1.BRAND) tbt1 ON
                tbt1.PERIODE = mpt.PERIODE
                AND tbt1.BRAND = mpt.BRAND
                AND tbt1.KODE_REGION = mpt.REGIONAL
                AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
            LEFT JOIN (
                SELECT
                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                    mmw.KODE_REGION,
                    mmw.KODE_PROVINSI,
                    tb1.BRAND,
                    sum(tb1.TARGET) AS TARGET
                FROM
                    ZSD_TARGET_HARIAN_NEW_BRAND tb1
                LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                    tb1.DISTRIK = mmw.KODE_DISTRIK
                WHERE
                    tb1.DEL_MARK = '0'
                GROUP BY
                    to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                    mmw.KODE_REGION,
                    mmw.KODE_PROVINSI,
                    tb1.BRAND) tbt2 ON
                tbt2.PERIODE = mpt.PERIODE
                AND tbt2.BRAND = mpt.BRAND
                AND tbt2.KODE_REGION = mpt.REGIONAL
                AND tbt2.KODE_PROVINSI = mpt.KD_PROP
            WHERE
                mpt.DEL_MARK = '0'
                AND mpt.PERIODE = '$periode'
                AND mpt.REGIONAL = '$regional'
                AND mpt.KD_PROP = '$kd_prop'
                AND mpt.BRAND = '$brand' ";

    if (!$distrik_ret || $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null || empty($distrik_ret)) {
        $sql_count .= "AND (mpt.DISTRIK_RET IS NULL OR mpt.DISTRIK_RET = ' ' OR mpt.DISTRIK_RET = '') ";
    }else {
        $sql_count .= "AND mpt.DISTRIK_RET = '$distrik_ret' ";
    }
    
    if (!$distrik || $distrik == "" || $distrik == " " || $distrik == null || empty($distrik)) {
        $sql_count .= "AND (mpt.KD_DISTRIK IS NULL OR mpt.KD_DISTRIK = ' ' OR mpt.KD_DISTRIK = '') ";
    }else {
        $sql_count .= "AND mpt.KD_DISTRIK = '$distrik' ";
    }
    // echo $sql_count;
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result['status'] = (($row_count['SISA_PLAFON'] - $row_count['TARGET_ALLOC']) + $target_alloc) < 0;
    // $result['status'] = ($row_count['TARGET'] + $target_alloc) > $row_count['TARGET_SPC'];
    $result['target_spc'] = $row_count['TARGET_SPC'];

    return $result;
}

function displayData($conn, $filt){
    $org = $_SESSION['user_org'];
    if($conn){
        $cond_periode = isset($filt['periode']) && $filt['periode'] ? "AND mpt.PERIODE = '".$filt['periode']."' " : "";
        $sql1 = "SELECT
                    mpt.*,
                    COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
                    CASE
                        WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                        ELSE tbt2.TARGET
                    END AS TARGET_SPC,
                    COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(CASE
                        WHEN mpt.DISTRIK_RET IS NOT NULL THEN tbt1.TARGET
                        ELSE tbt2.TARGET
                    END, 0) AS SISA_PLAFON,
                    mms.SEGMEN,
                    prov.NM_PROV AS NAMA_PROVINSI,
                    TO_CHAR(mpt.CREATED_AT, 'DD-MON-YYYY') AS CREATED_AT_F,
                    TO_CHAR(mpt.UPDATED_AT, 'DD-MON-YYYY') AS UPDATED_AT_F
                FROM
                    MAPPING_PLAFON_TARGET mpt
                LEFT JOIN ZREPORT_M_PROVINSI prov ON
                    mpt.KD_PROP = prov.KD_PROV
                LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
                    mpt.PERIODE = mms.PERIODE
                    AND mpt.REGIONAL = mms.REGIONAL
                    AND mpt.KD_PROP = mms.KD_PROP
                    AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
                    AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
                    AND mpt.BRAND = mms.BRAND
                    AND mms.DEL_MARK = '0'
                LEFT JOIN (
                    SELECT
                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                        mmw.KODE_REGION,
                        mmw.KODE_PROVINSI,
                        mmw.DISTRIK_RET,
                        tb1.BRAND,
                        sum(tb1.TARGET) AS TARGET
                    FROM
                        ZSD_TARGET_HARIAN_NEW_BRAND tb1
                    LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                        tb1.DISTRIK = mmw.KODE_DISTRIK
                    WHERE
                        tb1.DEL_MARK = '0'
                    GROUP BY
                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                        mmw.KODE_REGION,
                        mmw.KODE_PROVINSI,
                        mmw.DISTRIK_RET,
                        tb1.BRAND) tbt1 ON
                    tbt1.PERIODE = mpt.PERIODE
                    AND tbt1.BRAND = mpt.BRAND
                    AND tbt1.KODE_REGION = mpt.REGIONAL
                    AND tbt1.KODE_PROVINSI = mpt.KD_PROP
                    AND tbt1.DISTRIK_RET = mpt.DISTRIK_RET
                LEFT JOIN (
                    SELECT
                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
                        mmw.KODE_REGION,
                        mmw.KODE_PROVINSI,
                        tb1.BRAND,
                        sum(tb1.TARGET) AS TARGET
                    FROM
                        ZSD_TARGET_HARIAN_NEW_BRAND tb1
                    LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
                        tb1.DISTRIK = mmw.KODE_DISTRIK
                    WHERE
                        tb1.DEL_MARK = '0'
                    GROUP BY
                        to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
                        mmw.KODE_REGION,
                        mmw.KODE_PROVINSI,
                        tb1.BRAND) tbt2 ON
                    tbt2.PERIODE = mpt.PERIODE
                    AND tbt2.BRAND = mpt.BRAND
                    AND tbt2.KODE_REGION = mpt.REGIONAL
                    AND tbt2.KODE_PROVINSI = mpt.KD_PROP
                WHERE
                    mpt.DEL_MARK = '0'
                    ".$cond_periode."
                ORDER BY
                    mpt.PERIODE DESC,
                    mpt.REGIONAL,
                    mpt.KD_PROP,
                    mpt.DISTRIK_RET";
        // $sql1 = "SELECT
        //             mpt.*,
        //             mms.SEGMEN,
        //             prov.NM_PROV AS NAMA_PROVINSI
        //         FROM
        //             MAPPING_PLAFON_TARGET mpt
        //         LEFT JOIN ZREPORT_M_PROVINSI prov ON
        //             mpt.KD_PROP = prov.KD_PROV
        //         LEFT JOIN MASTER_MAPPING_SEGMEN mms ON
        //             mpt.PERIODE = mms.PERIODE
        //             AND mpt.REGIONAL = mms.REGIONAL
        //             AND mpt.KD_PROP = mms.KD_PROP
        //             AND COALESCE(mpt.DISTRIK_RET, '0') = COALESCE(mms.DISTRIK_RET, '0')
        //             AND COALESCE(mpt.KD_DISTRIK, '0') = COALESCE(mms.KD_DISTRIK, '0')
        //             AND mpt.BRAND = mms.BRAND
        //             AND mms.DEL_MARK = '0'
        //         WHERE 
        //             mpt.DEL_MARK = '0'
        //         ORDER BY
        //             mpt.PERIODE DESC,
        //             mpt.REGIONAL,
        //             mpt.KD_PROP,
        //             mpt.DISTRIK_RET";
        // $sql1 = "SELECT
        //             mpt.*,
        //             CASE
        //                 WHEN mpt.REGIONAL IN ('3', '4', '5') THEN mms1.SEGMEN
        //                 WHEN mpt.REGIONAL IN ('1', '2', '6')
        //                 AND mpt.KD_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
        //                 ELSE mms3.SEGMEN
        //             END AS SEGMEN,
        //             prov.NM_PROV AS NAMA_PROVINSI
        //         FROM
        //             MAPPING_PLAFON_TARGET mpt
        //         LEFT JOIN ZREPORT_M_PROVINSI prov ON
        //             mpt.KD_PROP = prov.KD_PROV
        //         LEFT JOIN
        //                     (
        //             SELECT
        //                 DISTINCT
        //                             mms.BRAND,
        //                 mms.DISTRIK_RET,
        //                 mms.SEGMEN
        //             FROM
        //                 MASTER_MAPPING_SEGMEN mms
        //             JOIN (
        //                 SELECT
        //                     BRAND,
        //                     DISTRIK_RET,
        //                     MAX(PERIODE) AS MAX_PERIODE
        //                 FROM
        //                     MASTER_MAPPING_SEGMEN
        //                 WHERE
        //                     DEL_MARK = '0'
        //                 GROUP BY
        //                     BRAND,
        //                     DISTRIK_RET
        //                                             ) latest
        //                                             ON
        //                 mms.BRAND = latest.BRAND
        //                 AND mms.DISTRIK_RET = latest.DISTRIK_RET
        //                 AND mms.PERIODE = latest.MAX_PERIODE
        //             WHERE
        //                 mms.DEL_MARK = '0' ) mms1 ON
        //             mpt.REGIONAL IN ('3', '4', '5')
        //             AND mpt.BRAND = mms1.BRAND
        //             AND mpt.DISTRIK_RET = mms1.DISTRIK_RET
        //         LEFT JOIN
        //                                                 (
        //             SELECT
        //                 DISTINCT
        //                             mms.BRAND,
        //                 mms.KD_PROP,
        //                 mms.KD_DISTRIK,
        //                 mms.SEGMEN
        //             FROM
        //                 MASTER_MAPPING_SEGMEN mms
        //             JOIN (
        //                 SELECT
        //                     BRAND,
        //                     KD_PROP,
        //                     KD_DISTRIK,
        //                     MAX(PERIODE) AS MAX_PERIODE
        //                 FROM
        //                     MASTER_MAPPING_SEGMEN
        //                 WHERE
        //                     DEL_MARK = '0'
        //                 GROUP BY
        //                     BRAND,
        //                     KD_PROP,
        //                     KD_DISTRIK
        //                                                     ) latest
        //                                                         ON
        //                 mms.BRAND = latest.BRAND
        //                 AND mms.KD_PROP = latest.KD_PROP
        //                 AND mms.KD_DISTRIK = latest.KD_DISTRIK
        //                 AND mms.PERIODE = latest.MAX_PERIODE
        //             WHERE
        //                 mms.DEL_MARK = '0') mms2 ON
        //             mpt.REGIONAL  IN ('1', '2', '6')
        //             AND mpt.BRAND = mms2.BRAND
        //             AND mpt.KD_PROP = mms2.KD_PROP
        //             AND mpt.KD_DISTRIK = mms2.KD_DISTRIK
        //         LEFT JOIN
        //                                                 (
        //             SELECT
        //                 DISTINCT
        //                             mms.BRAND,
        //                 mms.KD_PROP,
        //                 mms.SEGMEN
        //             FROM
        //                 MASTER_MAPPING_SEGMEN mms
        //             JOIN (
        //                 SELECT
        //                     BRAND,
        //                     KD_PROP,
        //                     MAX(PERIODE) AS MAX_PERIODE
        //                 FROM
        //                     MASTER_MAPPING_SEGMEN
        //                 WHERE
        //                     DEL_MARK = '0'
        //                     AND KD_DISTRIK IS NULL
        //                 GROUP BY
        //                     BRAND,
        //                     KD_PROP,
        //                     KD_DISTRIK
        //                                                     ) latest
        //                                                         ON
        //                 mms.BRAND = latest.BRAND
        //                 AND mms.KD_PROP = latest.KD_PROP
        //                 AND mms.PERIODE = latest.MAX_PERIODE
        //             WHERE
        //                 mms.DEL_MARK = '0'
        //                 AND KD_DISTRIK IS NULL) mms3 ON
        //             mpt.REGIONAL IN ('1', '2', '6')
        //             AND mpt.BRAND = mms3.BRAND
        //             AND mpt.KD_PROP = mms3.KD_PROP
        //         WHERE 
        //             mpt.DEL_MARK = '0'
        //         ORDER BY
        //             mpt.PERIODE DESC,
        //             mpt.REGIONAL,
        //             mpt.KD_PROP,
        //             mpt.DISTRIK_RET";
            // echo $sql1;
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['PERIODE'] = $row['PERIODE'];
            $result[$i]['REGIONAL'] = $row['REGIONAL'];
            $result[$i]['KD_PROP'] = $row['KD_PROP'];
            $result[$i]['PROP'] = $row['NAMA_PROVINSI'];
            $result[$i]['BRAND'] = $row['BRAND'];
            $result[$i]['SEGMEN'] = $row['SEGMEN'];
            $result[$i]['TARGET'] = $row['TARGET'];
            $result[$i]['TARGET_PLAFON'] = $row['TARGET_PLAFON'];
            $result[$i]['TARGET_ALLOC'] = $row['TARGET_ALLOC'] ? $row['TARGET_ALLOC'] : 0;
            $result[$i]['TARGET_SPC'] = $row['TARGET_SPC'];
            $result[$i]['SISA_PLAFON'] = $row['SISA_PLAFON'];
            $result[$i]['DISTRIK_RET'] = $row['DISTRIK_RET'];
            $result[$i]['KD_DISTRIK'] = $row['KD_DISTRIK'];
            $result[$i]['CREATED_AT'] = $row['CREATED_AT_F'] == null ? '-' : $row['CREATED_AT_F'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT_F'] == null ? '-' : $row['UPDATED_AT_F'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn,$periode,$regional,$kd_prop,$distrik_ret, $distrik, $brand, $target, $target_alloc, $created_by){
    $sqlcek= "INSERT INTO MAPPING_PLAFON_TARGET (PERIODE, REGIONAL, KD_PROP, DISTRIK_RET, KD_DISTRIK, BRAND, TARGET, TARGET_ALLOC, created_at, created_by, DEL_MARK) values ('".date('Y-m', strtotime($periode))."','".$regional."','".$kd_prop."','".$distrik_ret."', '".$distrik."', '".$brand."', '".$target."','".$target_alloc."',  SYSDATE, '".$created_by."', '0')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function update($conn,$periode,$regional,$kd_prop,$distrik_ret_bt, $distrik_bt, $brand, $target, $target_alloc, $created_by){
    $distrik_ret = trim($distrik_ret_bt);
    $distrik_ret = $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null ? null : $distrik_ret ;
    $distrik = trim($distrik_bt);
    $distrik = $distrik == "" || $distrik == " " || $distrik == null ? null : $distrik ;

    $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set TARGET_ALLOC = '$target_alloc', TARGET = '$target', UPDATED_AT = SYSDATE, UPDATED_BY = '$created_by' where PERIODE = '$periode' AND REGIONAL = '$regional' AND KD_PROP = '$kd_prop' AND BRAND = '$brand'";

    if (!$distrik_ret || $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null || empty($distrik_ret)) {
        $sqlcek .= "AND (DISTRIK_RET IS NULL OR DISTRIK_RET = ' ' OR DISTRIK_RET = '') ";
    }else {
        $sqlcek .= "AND DISTRIK_RET = '$distrik_ret' ";
    }
    
    if (!$distrik || $distrik == "" || $distrik == " " || $distrik == null || empty($distrik)) {
        $sqlcek .= "AND (KD_DISTRIK IS NULL OR KD_DISTRIK = ' ' OR KD_DISTRIK = '') ";
    }else {
        $sqlcek .= "AND KD_DISTRIK = '$distrik' ";
    }
    // echo $sqlcek;
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn,$periode,$regional,$kd_prop, $distrik_ret_bt, $distrik_bt, $brand, $target) {
    $distrik_ret = trim($distrik_ret_bt);
    $distrik_ret = $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null ? null : $distrik_ret ;
    $distrik = trim($distrik_bt);
    $distrik = $distrik == "" || $distrik == " " || $distrik == null ? null : $distrik ;
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_PLAFON_TARGET
                WHERE 
                    PERIODE = '$periode'
                    AND REGIONAL = '$regional'
                    AND KD_PROP = '$kd_prop'
                    AND BRAND = '$brand'
                    -- AND TARGET = '$target'
                    AND DEL_MARK = '0'
                ";
    if (!$distrik_ret || $distrik_ret == "" || $distrik_ret == " " || $distrik_ret == null || empty($distrik_ret)) {
        $sql_count .= "AND (DISTRIK_RET IS NULL OR DISTRIK_RET = ' ' OR DISTRIK_RET = '') ";
    }else {
        $sql_count .= "AND DISTRIK_RET = '$distrik_ret' ";
    }
    
    if (!$distrik || $distrik == "" || $distrik == " " || $distrik == null || empty($distrik)) {
        $sql_count .= "AND (KD_DISTRIK IS NULL OR KD_DISTRIK = ' ' OR KD_DISTRIK = '') ";
    }else {
        $sql_count .= "AND KD_DISTRIK = '$distrik' ";
    }
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function adjustRowNumber($num) {
    return $num - 2;
}



?>
