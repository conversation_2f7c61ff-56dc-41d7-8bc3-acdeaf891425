<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
include_once('../include/sapclasses/sap.php');

$fungsi=new fungsi();
$or_fungsi=new or_fungsi();
$conn=$fungsi->koneksi();
$bo_conn=$or_fungsi->bo_koneksi();



$halaman_id=1684;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$sold_to=$fungsi->sapcode($distr_id);
$no_so = $_GET['id_detail'];
$posnr0 = $_GET['posnr'];
$itemnum=$posnr0/10;
$posnr = $fungsi->linenum($posnr0);
$sisa_so = $_GET['sisaso'];
$plant = $_GET['plant'];
$tipeso = $_GET['tipeso'];
	


$kode_distrik = $_GET['kode_distrik'];
$no_so1 = $_GET['no_so'];
$so_type = $_GET['so_type'];
$incoterm = $_GET['incoterm'];
$status = $_GET['status'];
$tgl1 = $_GET['tgl1'];
$tgl2 = $_GET['tgl2'];
$tglso = $_GET['tglso'];




/*if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu....");
				
				</SCRIPT>

	 <a   href="../index.php"></a>
<?
exit();
}*/


	$tsql1= "SELECT * FROM OR_TRANS_APP WHERE NO_SO='$no_so' and ITEM_NUMBER='$itemnum'";
	$query1= oci_parse($conn, $tsql1);
	oci_execute($query1);
	$data1=oci_fetch_assoc($query1);
	$order_date=$data1['APPROVE_DATE'];
	$so_number=$data1['NO_SO'];
	$kdproduk=$data1['KODE_PRODUK'];
	$tipesemen=$data1['NAMA_PRODUK'];
	$qty=$data1['QTY_APPROVE'];
	$shipto=$data1['NAMA_SHIP_TO'];
	$distrik=$data1['KODE_TUJUAN'];
	$tujuan=$data1['NAMA_TUJUAN'];
	$alamat=$data1['ALAMAT_SHIP_TO'];
    $uom=$data1['UOM'];
	$plant_1=$data1['PLANT'];
	
	$mialo= "SELECT EPOOOL FROM TB_PLANT WHERE KODE_PLANT='$plant_1'";
	$query2= oci_parse($bo_conn, $mialo);
	oci_execute($query2);
	$data2=oci_fetch_assoc($query2);
	$epoool=$data2['EPOOOL'];
	
	$mat=explode("-",$kdproduk);
	//echo "tes".$mat[1];

        
        
        
        
$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
			
		 $bln=array('JAN'=>1,'FEB'=>2,'MAR'=>3,'APR'=>4,'MEI'=>5,'JUN'=>6,'JUL'=>7,'AUG'=>8,'SEP'=>9,'OCT'=>10,'NOV'=>11,'DEC'=>12);
		  $pecahbln=explode('-',$tglso);

 		  $thn_order=$pecahbln[2];
		  $bln_order=$pecahbln[1];
		  $tgl_order=$pecahbln[0];  
		  $bln_order_num=$bln[$bln_order];
    	           $tanggal_order=$thn_order.$bln_order.$tgl_order;
        //  $tgl='20160211';

		//header entri
		
		$fce->XVKORG = $user_org; //org
		//echo "<br>XKUNNR =".$fce->XKUNNR = $sold_to; // sold to
		//echo "<br>XBZIRK =".$fce->XBZIRK = $distr_id; 
		$fce->XVBELN = $no_so; // sold to
		$fce->XFLAG = 'O';
		$fce->XAUART = $tipeso;
		//$fce->XINCO1 = $incoterm;
		
		$fce->LR_EDATU->row["SIGN"] = 'I';
		$fce->LR_EDATU->row["OPTION"] = 'BT';
		$fce->LR_EDATU->row["LOW"] = $tanggal_order;
		$fce->LR_EDATU->row["HIGH"] = $tanggal_order;
		$fce->LR_EDATU->Append($fce->LR_EDATU->row);
		
		$fce->Call();
		
		if ($fce->GetStatus() == SAPRFC_OK ) {
			$fce->RETURN_DATA->Reset();
			
			$s=0;
			while ( $fce->RETURN_DATA->Next() ){
			$harga[$s]= $fce->RETURN_DATA->row["NETWR"];
			print_r("ini netwr = ".$fce->RETURN_DATA->row["NETWR"]);
			$plant[$s]= $fce->RETURN_DATA->row["WERKS"];
			$qty_so[$s]= $fce->RETURN_DATA->row["KWMENG"];
			$kdshipto1[$s]= $fce->RETURN_DATA->row["KUNNR2"];
			
			$pajak[$s]= $fce->RETURN_DATA->row["MWSBP"];
			$BZIRK[$s]= $fce->RETURN_DATA->row["BZIRK"];
			$MEINS[$s]= $fce->RETURN_DATA->row["MEINS"];
			
			$s++;
			}
		}else
    	
    	$fce->PrintStatus();
 	//	print_r($fce->RETURN_DATA);
		$fce->Close();	
		$sap->Close();	

		

	

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<head>
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Entri Permintaan Pembelian</title>
<script language="javascript">
<!--
var j=1;
   function start_add() {
	//if (j==11){alert('ma\'af maksimal 10');return false;}
	    var uom=document.getElementById('uom').value;
            var plantnya=document.getElementById("plant").value;
	    var epoool=document.getElementById("status").value;
            var inco=document.getElementById("inco").value;
        j++;
		
		//alert (uom+plantnya+epoool);
		var body1 = document.getElementById("coba");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
		if ((plantnya =='3401' && uom=='TO' && epoool=='1') || (epoool=='1' && plantnya !='3401' && inco=='FRC') || (plantnya =='3201' && uom=='TO' && epoool=='1') ){
			
		newdiv.innerHTML='<table width="600" align="center" id="tabel'+j+'" class="adminform"><tr><td><input required type="text" value="0" id="lembar'+j+'" name="lembar'+j+'" size="4" onKeyUp="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();" onBlur="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();"/> Lembar x</td><td><input required type="text" value="0" id="zak'+j+'" name="zak'+j+'" size="6" onKeyUp="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();" onBlur="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);cekzak(this.value,'+j+');return cektotal();"> Zak/Ton =</td><td><input type="text" value="0" id="jmlzak'+j+'" name="jmlzak'+j+'" size="8" readonly/> Zak/Ton</td><td><input  value="<?php echo date('d-m-Y') ?>"  required type="text" id="tanggal'+j+'" name="tanggal'+j+'" size="8" readonly  onClick="return showCalendar(\'tanggal'+j+'\');" /> </td><td width="100"></td></tr></table>';
		}else{
		newdiv.innerHTML='<table width="600" align="center" id="tabel'+j+'" class="adminform"><tr><td><input type="text" value="0" id="lembar'+j+'" name="lembar'+j+'" size="4" onKeyUp="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();" onBlur="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();"/> Lembar x</td><td><input type="text" value="0" id="zak'+j+'" name="zak'+j+'" size="6" onKeyUp="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);return cektotal();" onBlur="document.getElementById(\'jmlzak'+j+'\').value=parseFloat(document.getElementById(\'lembar'+j+'\').value)*parseFloat(document.getElementById(\'zak'+j+'\').value);cekzak(this.value,'+j+');return cektotal();"> Zak/Ton =</td><td><input type="text" value="0" id="jmlzak'+j+'" name="jmlzak'+j+'" size="8" readonly/> Zak/Ton</td><td width="100"></td></tr></table>';
			
		} 
		body1.appendChild(newdiv);
		document.test.jumlah.value=j;
		
    }
	function stop_add()
	{
	if (j==1){alert('Ma\'af Minimal 1 Pecahan DO');return false;}
	k=j;
	k=k.toString();
    var body1 = document.getElementById("coba");
	var buang = document.getElementById("dd"+k);
    body1.removeChild(buang);
	j=j-1;
	document.test.jumlah.value=j;
	cektotal();
	}
	
	
	function cektotal() { 
	var totald=0;
		for (x=1;x<=j;x++){
			x.toString();
			totald= totald + parseFloat(document.getElementById("jmlzak"+x).value);
		}		
		document.getElementById("totalzak").value=totald;
	}
	function ceksisa() { 
	var totald=document.getElementById("totalzak").value;
	var sisa=document.getElementById("sisa").value;
		
		if(parseFloat(totald) > parseFloat(sisa)){
			alert('Total Pecahan DO tidak boleh lebih besar dari Sisa Qty SO');
			document.hasil = false;
			}			
	}
	function cekzak(zak,i) { 
	var jmlzak=zak;
        var uom=document.getElementById('uom').value;
		var plantnya=document.getElementById("plant").value;
		
        
        if(uom=='TO'){
		   if(parseFloat(jmlzak) > 50){
			alert('Pecahan DO tidak boleh lebih besar dari 50 Ton');
                        document.getElementById("zak1").value=50;
                       
                       
			}
                         else if(parseFloat(jmlzak) < 12){
			alert('Pecahan DO tidak boleh lebih kecil dari 12 Ton');
			document.getElementById("zak1").value=12;
			}
        } else if(uom=='ZAK'){
            if(parseFloat(jmlzak) < 20){
			alert('Pecahan DO tidak boleh lebih kecil dari 20 ZAK');
                        document.getElementById("zak1").value=20;           
			}
       
        } 
		
	/*		
	if(plantnya=='3401'){
		if(parseFloat(jmlzak) > 240){
			alert('Pecahan DO tidak boleh lebih besar dari 240 Zak');
			document.getElementById("zak"+i).value=240;
			}
			}*/
        
		document.getElementById('jmlzak'+i).value=parseFloat(document.getElementById('lembar'+i).value)*parseFloat(document.getElementById('zak'+i).value);
		}				
	
	
	function validate(f){
                              var ctr=0;
                              var msg="Field-field yang terisi adalah:\n";
                              for(var i=0;i<f.elements.length;i++){
                                    if(f.elements[i].type.toLowerCase()=="text"){
                                          if(f.elements[i].value!=""){
                                               // msg+=f.elements[i].name+"  =  "+f.elements[i].value+"\n";
                                                ctr++;
                                          }else{
                                                alert("Field: \""+f.elements[i].name+"\" masih kosong!");
                                                f.elements[i].focus();
                                                return false;
                                          }
                                    }
                              }
                              if(ctr>0) alert(msg);
                        }
						
//-->
</script>
</head>
<body>
<br />
<div align="center">
<form name="test"  method="post" action="komentar.php" onSubmit="ceksisa();return document.hasil">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Sales Order</span></th>
</tr>
</table>
<table width="600" align="center" class="adminlist">
  <tr >
    <td align="left"><strong>Tangal SO.</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$order_date?></td>
  </tr>
  <tr >
    <td align="left"><strong>No. Sales Order</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$so_number?><input name="sonumber" type="hidden" value="<?=$so_number?>"/>
	<input name="posnr" type="hidden" value="<?=$posnr?>"/>
	<input name="tipeso" type="hidden" value="<?=$tipeso?>"/>
	<input name="shipto" type="hidden" value="<?=$shipto?>"/>
	<input name="kdshipto" type="hidden" value="<?=$kdshipto?>"/>
	<input name="kdshipto1" type="hidden" value="<?=$kdshipto1?>"/>
	<input name="alamat" type="hidden" value="<?=$alamat?>"/>

<input name="kode_distrik" type="hidden" value="<?=$kode_distrik?>"/>
<input name="no_so" type="hidden" value="<?=$no_so1?>"/>
<input name="so_type" type="hidden" value="<?=$so_type?>"/>
<input name="status" type="hidden" value="<?=$status?>"/>
<input name="tgl1" type="hidden" value="<?=$tgl1?>"/>
<input name="tgl2" type="hidden" value="<?=$tgl2?>"/>
<input name="incoterm" type="hidden" value="<?=$incoterm?>"/>


</td>

  </tr>
    
  <tr>
    <td align="left"><strong>Harga Satuan</strong></td>
    <td align="left">:</td>
	<td align="left"><?  
							$harga_total= (($harga[0]*100)+($pajak[0]*100)) ;
							$hargasatuan = (($harga[0]*100)+($pajak[0]*100)) / $qty_so[0];
							print_r($harga[0]."-".$pajak[0]."-".$qty_so[0]);
							echo $HASILSATUAN = number_format($hargasatuan,2,",","."); 
                            
							// $TUJUAN = $BZIRK[0];
                            // $UOM = $MEINS[0];
                            // $CODE = '1';
                            // IF($UOM == 'TO'){
                                // $HITUNG = (round(($harga[0]*100) / $qty_so[0],2));
                            // }ELSE{
                                // $HITUNG = (($harga[0]*100) / $qty_so[0]);
                            // }
                            // $PPN = '0.1';
                            // $PPH = '0.0025';
                            // IF($TUJUAN == '141004' or  $TUJUAN == '141005' or $TUJUAN == '141007'){ ### NO PPN DAN PPH
                                // echo $HASILSATUAN=number_format($HITUNG,2);
                                // $TOTALHARGA=number_format($HITUNG * $qty_so[0],2);
                            // }ELSE{ ### HANYA PPN							
                                // IF($UOM == 'TO'){
                                    // $HARGAPPN = $HITUNG * $PPN;
                                    // $HASIL_KENA_PPN = round($HITUNG + $HARGAPPN);
                                    // echo $HASILSATUAN=number_format($HASIL_KENA_PPN,0);
                                    // $TOTALHARGA=number_format($HASIL_KENA_PPN * $qty_so[0],0);
                                // }ELSE{
                                    // $HARGAPPN = $HITUNG * $PPN;
                                    // $HASIL_KENA_PPN = $HITUNG + $HARGAPPN;
                                    // echo $HASILSATUAN=number_format($HASIL_KENA_PPN,0);	
                                    // $TOTALHARGA=number_format($HASIL_KENA_PPN * $qty_so[0],0);
                                // }
                            // }
                            
                            //$hargasatuan = ($harga[0]*100) / $qty_so[0];
                            ///$xsat = 0;
                            //$xsat = ($plant[0] == 3404) ? 1.0025 : 1.1025;                                                                    
                            //$hargapajak = $hargasatuan * $xsat;
                            //$harga_satuan=number_format($hargapajak,2,",","."); 
                            ?>
        <input type='hidden' name='harga_satuan' value='<?=$HASILSATUAN ?>' >
        </td>
  </tr>
  
  <tr>
  <tr>
    <td align="left"><strong>Total Harga</strong></td>
    <td align="left">:</td>
		<td align="left"><? ECHO $harga_total; ?></td>
  </tr>
  <tr>
    <td align="left"><strong>Toko / Agen</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$shipto?></td>
  </tr>
  <tr>
    <td align="left"><strong>Alamat</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$alamat?>, <?=$tujuan?></td>
  </tr>
  
   <tr>
    <td align="left"><strong>Material</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$kdproduk?></td>
	<input type="hidden" name="material" value="<?=$kdproduk?>"/>
  </tr>

  <tr>
    <td align="left"><strong>Tipe Semen</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$tipesemen?></td>
  </tr>
  <tr>
    <td align="left"><strong>Shipping Point</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$plant?><input name="plant" id="plant" type="hidden" value="<?=$plant?>"/></td>
  </tr>
  <tr>
    <td align="left"><strong>Sisa Qty SO</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$sisaso?><input name="sisa" id="sisa" type="hidden" value="<?=$sisaso?>"/></td>
  </tr>
  <tr>
    <td align="left"><strong>Satuan</strong></td>
    <td align="left">:</td>
	<td align="left"><?=$uom?><input name="uom" id="uom" type="hidden" value="<?=$uom?>"/></td>
        <td align="left"><input name="status" id="status" type="hidden" value="<?=$epoool?>"/></td>
         <td align="left"><input name="inco" id="inco" type="hidden" value="<?=$incoterm?>"/></td>
  </tr>

</table>
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Split DO</span></th>
</tr>
</table>
<table width="600" align="center" class="adminlist">
  <tr class="quote">
    <td align="left"><strong>Lembar</strong></td>
    <td align="left"><strong>Zak</strong></td>
    <td align="left"><strong>Total</strong></td>
	<?php
if ((($epoool == '1') and ($plant_1=='3401') and ($uom=='TO'))  or (($epoool == '1') and ($plant_1<>'3401' ) and ($incoterm=='FRC')) or (($epoool == '1') and ($plant_1=='3201') and ($uom=='TO'))){
?>
 <td align="left"><strong>Tgl</strong></td>	
<?}elseif ((($epoool == '1') and ($plant_1=='7912') and ($uom=='TO'))  or (($epoool == '1') and ($plant_1<>'7912' ) and ($incoterm=='FRC')) or (($epoool == '1') and ($plant_1=='3201') and ($uom=='TO'))){
?>
 <td align="left"><strong>Tgl</strong></td>	
<?}?>
	<td align="center" width="100">&nbsp;</td>
  </tr>
</table>
<table width="600" align="center" id="tabel" border='' class="adminform">
<tr>
<td><input type="text" value="0" id="lembar1" name="lembar1" size="4" onKeyUp="document.test.jmlzak1.value=parseFloat(document.test.lembar1.value)*parseFloat(document.test.zak1.value);return cektotal();" onBlur="document.test.jmlzak1.value=parseFloat(document.test.lembar1.value)*parseFloat(document.test.zak1.value);return cektotal();"/> Lembar x</td>
<td><input type="text" value="0" id="zak1" name="zak1" size="6" onKeyUp="document.test.jmlzak1.value=parseFloat(document.test.lembar1.value)*parseFloat(document.test.zak1.value);return cektotal();" onBlur="document.test.jmlzak1.value=parseFloat(document.test.lembar1.value)*parseFloat(document.test.zak1.value);cekzak(this.value,1);return cektotal();" /> Zak/Ton =</td>
<td><input type="text" value="0" id="jmlzak1" name="jmlzak1" size="8" readonly /> Zak/Ton</td>
<?php
if ((($epoool == '1') and ($plant_1=='3401') and ($uom=='TO'))  or (($epoool == '1') and ($plant_1<>'3401' ) and ($incoterm=='FRC')) or (($epoool == '1') and ($plant_1=='3201') and ($uom=='TO'))){

?>
<td><input name="tanggal1" required  value='<?php echo date('d-m-Y') ?>' type="text" id="tanggal1" size=12 onClick="return showCalendar('tanggal1');"/></td>
<?}elseif ((($epoool == '1') and ($plant_1=='7912') and ($uom=='TO'))  or (($epoool == '1') and ($plant_1<>'7912' ) and ($incoterm=='FRC')) or (($epoool == '1') and ($plant_1=='3201') and ($uom=='TO'))){

?>
<td><input name="tanggal1" required  value='<?php echo date('d-m-Y') ?>' type="text" id="tanggal1" size=12 onClick="return showCalendar('tanggal1');"/></td>
<?}?>
<td width="100">
<input type="button" value=" + " name="tambah" onClick="return start_add();" />
<input type="button" value="  -  " name="kurang" onClick="return stop_add();" />
</td>
</tr>
</table>
<div id="coba">
</div>
<table width="600" align="center" class="adminform">
<tr><td width="40">&nbsp;</td><td width="100">&nbsp;</td><td width="100"><strong>TOTAL</strong></td>
<td><input type="text" value="0" id="totalzak" name="totalzak" size="8" readonly/> <strong>Zak/Ton</strong></td><td>&nbsp;</td></tr>
</table>
</div>
 <p>
<div align="center">
<input type="hidden" value="1" id="jumlah" name="jumlah" size="12"/>
<input name="action" type="hidden" value="splitdo" id="action" />
<input name="split" id="split" type="submit" class="button" value="Split DO"/>&nbsp;&nbsp;
</form>
<a href="so_distr_split.php" class="button">Batal</a>
</div>
</body>
</html>
