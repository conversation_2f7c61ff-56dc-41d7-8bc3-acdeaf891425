<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=171;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// } 
if(isset($_POST["cancelInv"]) && $_POST["cancelInv"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Apakah anda yakin ingin Reject invoice <? echo $_POST["nomer_invoice"] ; ?> ?
				<input type="hidden" value="<? echo $_POST["nomer_invoice"] ; ?>" name="nor_invoice">
			</div>
			<div class="alert alert-danger" role="alert">
				<label>Alasan Reject Invoice</label>
				<textarea class="form-control" name="cancel_inv_ket" rows="5" required></textarea>
			</div>
			<button type="submit" name="cancelInv" value="cancelInv" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Iya&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}

if(isset($_POST["cancelInv2"]) && $_POST["cancelInv2"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Apakah anda yakin ingin Reverse invoice <? echo $_POST["nomer_invoice"] ; ?> ?
				<input type="hidden" value="<? echo $_POST["nomer_invoice"] ; ?>" name="nor_invoice">
			</div>
			<div class="alert alert-danger" role="alert">
				<label>Alasan Reverse Invoice</label>
				<textarea class="form-control" name="cancel_inv_ket" rows="5" required></textarea>
			</div>
			<button type="submit" name="cancelInv2" value="cancelInv2" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Iya&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


if((isset($_POST["cancelInv"]) && $_POST["cancelInv"] == 'cancelInv')) {
	$nomer_invoice = $_POST["nor_invoice"];
	$keterangan_cancel = $_POST['cancel_inv_ket'];
	$sql_inv = "SELECT ID, WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$nomer_invoice' ";
	// echo $nomer_invoice;exit;
	$query_inv = oci_parse($conn, $sql_inv);
	oci_execute($query_inv);
	$row_inv = oci_fetch_assoc($query_inv);
	$id_trans = $row_inv[ID];
	$warna_plat = $row_inv[WARNA_PLAT];
	
	// $sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND DIPAKAI = 1";
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND ROWNUM <= 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	$action = "cancel_invoice_bag_darat";
	include ('formula.php'); 
	
}

if((isset($_POST["cancelInv2"]) && $_POST["cancelInv2"] == 'cancelInv2')) {
	$nomer_invoice = $_POST["nor_invoice"];
	$keterangan_cancel = $_POST['cancel_inv_ket'];
	$sql_inv = "SELECT ID, WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$nomer_invoice' ";
	// echo $nomer_invoice;exit;
	$query_inv = oci_parse($conn, $sql_inv);
	oci_execute($query_inv);
	$row_inv = oci_fetch_assoc($query_inv);
	$id_trans = $row_inv[ID];
	$warna_plat = $row_inv[WARNA_PLAT];
	
	// $sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND DIPAKAI = 1";
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.*, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') as TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_INVOICE = '$nomer_invoice' AND ROWNUM <= 1";
	// echo $sql_invoice_ba;exit;
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	$action = "cancel_invoice_bag_darat2";
	include ('formula.php'); 
	
}

#$action_page=$fungsi->security($conn,$user_id,$halaman_id);

$page="lihat_invoice_bag_darat.php";
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_shipment = $_POST['no_shipment'];
$distributor = $_POST['distributor'];
$tipe_transaksi = $_POST['tipe_transaksi'];
$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$warna_plat = $_POST['warna_plat'];
$no_invoice = $_POST['no_invoice'];
$no_invoice_expeditur = $_POST['no_invoice_expeditur'];
$no_ba = $_POST['no_ba'];
$status_invoice = $_POST['status_invoice'];
$no_faktur_pajak = $_POST['no_faktur_pajak'];
 
$currentPage="lihat_invoice_bag_darat.php";
$komen="";
if(isset($_POST['cari'])){
	if($no_shipment=="" and $distributor=="" and $vendor=="" and $tipe_transaksi == "" and $tanggal_mulai == "" and $tanggal_selesai == "" and $warna_plat == "" and $no_invoice == "" and $no_invoice_expeditur == "" and $no_ba == "" and $status_invoice == "" and $no_faktur_pajak == ""){
		$sql= "SELECT DISTINCT  EI.POSTING_DATE,EI.TOTAL_INV, EI.PAJAK_INV,EI.NO_INVOICE,  EI.ACCOUNTING_DOC,  EI.NO_INV_REVERSE,   ETH.NO_BA, EI.TGL_INVOICE, EI.NO_VENDOR,  EI.NAMA_VENDOR, ETH.STATUS,  EI.KETERANGAN, EI.NO_PAJAK_EX,
		-- EBI.STATUS_BA_INVOICE, EBI.KOMENTAR_REJECT,  
		ETH.WARNA_PLAT 
		FROM  EX_INVOICE EI
		LEFT JOIN EX_TRANS_HDR  ETH ON EI.NO_INVOICE = ETH.NO_INVOICE 
		-- LEFT JOIN EX_BA_INVOICE EBI ON EBI.NO_INVOICE = EI.NO_INVOICE AND  EBI.DIPAKAI = 1
		WHERE  ETH.DELETE_MARK = '0'  
		-- AND ETH.NO_BA IS NOT NULL 
		AND EI.ORG in ($orgIn) 
		";
		//AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED')
	}else {
		$pakeor=0;
		$sql= "SELECT DISTINCT  EI.POSTING_DATE,EI.TOTAL_INV, EI.PAJAK_INV,EI.NO_INVOICE,  EI.ACCOUNTING_DOC,  EI.NO_INV_REVERSE,  ETH.NO_BA, EI.TGL_INVOICE, EI.NO_VENDOR,  EI.NAMA_VENDOR, ETH.STATUS,  EI.KETERANGAN, EI.NO_PAJAK_EX,
		-- EBI.STATUS_BA_INVOICE,		EBI.KOMENTAR_REJECT,  
		ETH.WARNA_PLAT 
		FROM  EX_INVOICE EI
		LEFT JOIN EX_TRANS_HDR  ETH ON EI.NO_INVOICE = ETH.NO_INVOICE  
		-- LEFT JOIN EX_BA_INVOICE EBI ON EBI.NO_INVOICE = EI.NO_INVOICE AND   EBI.DIPAKAI = 1
		WHERE ";
		if($no_shipment!=""){
		$sql.=" ETH.NO_SHP_TRN LIKE '$no_shipment' ";
		$pakeor=1;
		}
		if($distributor!=""){
			if($pakeor==1){
			$sql.=" AND ( ETH.NAMA_SOLD_TO LIKE '$distributor' OR ETH.SOLD_TO LIKE '$distributor' ) ";
			}else{
			$sql.=" ( ETH.NAMA_SOLD_TO LIKE '$distributor' OR ETH.SOLD_TO LIKE '$distributor' ) ";
			$pakeor=1;
			}
		}
		if($vendor!=""){
			if($pakeor==1){
			$sql.=" AND ( EI.NAMA_VENDOR LIKE '$vendor' OR EI.NO_VENDOR LIKE '$vendor' ) ";
			}else{
			$sql.=" ( EI.NAMA_VENDOR LIKE '$vendor' OR EI.NO_VENDOR LIKE '$vendor' ) ";
			$pakeor=1;
			}
		}
		if($tipe_transaksi!=""){
			if($pakeor==1){
			$sql.=" AND ETH.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
			}else{
			$sql.=" ETH.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
			$pakeor=1;
			}
		}
		if($tanggal_mulai!="" or $tanggal_selesai!=""){

			if ($tanggal_mulai=="")$tanggal_mulai_sql = "01-01-1990";
			else $tanggal_mulai_sql = $tanggal_mulai;
			if ($tanggal_selesai=="")$tanggal_selesai_sql = "12-12-9999";
			else $tanggal_selesai_sql = $tanggal_selesai;

			if($pakeor==1){
			$sql.=" AND EI.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
			}else{
			$sql.="  EI.TGL_INVOICE BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
			$pakeor=1;
			}
		}
		if($warna_plat!=""){
			if($pakeor==1){
			$sql.=" AND ETH.WARNA_PLAT LIKE '$warna_plat' ";
			}else{
			$sql.=" ETH.WARNA_PLAT LIKE '$warna_plat' ";
			$pakeor=1;
			}
		}
		if($no_invoice_expeditur!=""){
			if($pakeor==1){
			$sql.=" AND ETH.NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
			}else{
			$sql.=" ETH.NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
			$pakeor=1;
			}
		}
		if($no_invoice!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_INVOICE LIKE '$no_invoice' ";
			}else{
			$sql.=" EI.NO_INVOICE LIKE '$no_invoice' ";
			$pakeor=1;
			}
		}
		if($no_ba!=""){
			if($pakeor==1){
			$sql.=" AND ETH.NO_BA LIKE '$no_ba' ";
			}else{
			$sql.=" AND ETH.NO_BA LIKE '$no_ba' ";
			$pakeor=1;
			}
		}
		if($no_faktur_pajak!=""){
			if($pakeor==1){
			$sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			}else{
			$sql.=" AND EI.NO_PAJAK_EX LIKE '%$no_faktur_pajak%' ";
			$pakeor=1;
			}
		}
		// if($status_invoice!=""){
			// if($pakeor==1){
			// $sql.=" AND EBI.STATUS_BA_INVOICE LIKE '$status_invoice' ";
			// }else{
			// $sql.=" AND ETH.STATUS_BA_INVOICE LIKE '$status_invoice' ";
			// $pakeor=1;
			// }
		// }
		
		
		
		$sql.="  AND EI.ORG in ($orgIn) AND ETH.DELETE_MARK = '0'  ORDER BY EI.NO_INVOICE DESC";
		// $sql.="  AND EI.ORG in ($orgIn) AND ETH.DELETE_MARK = '0'  AND ETH.NO_BA IS NOT NULL ORDER BY EI.NO_INVOICE DESC";// AND ETH.STATUS IN ('PROGRESS','INVOICED') AND ETH.STATUS2 IN ('OPEN','INVOICED','UNINVOICED','PARTIAL_INVOICED') ------ ORDER BY ETH.ORG,ETH.VENDOR, ETH.NO_SHP_TRN ASC
	}
	// echo 'query'.$sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
	$total_tagihan=0; 
	while($row=oci_fetch_array($query)){  
			$sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";   
			$query_s= @oci_parse($conn, $sqlS);
			@oci_execute($query_s);
			$row_s=@oci_fetch_array($query_s); 
			
		if($status_invoice == $row_s[STATUS_BA_INVOICE] || $status_invoice==''){
			
			$no_ppl_v[]=$row[ACCOUNTING_DOC];
			$produk_v[]=$row[KODE_PRODUK];
			$no_invoice_v[]=$row[NO_INVOICE]; 
			$warna_plat_v[]=$row[WARNA_PLAT]; 
			//$keterangan_v[]=$row_s[KOMENTAR_REJECT];
			//$status_v[]=$row_s[STATUS_BA_INVOICE];
			//$status_id =$row_s[STATUS_BA_INVOICE];
				
				$no_ba_v[]=$row[NO_BA]; 
				$tgl_invoice_v[]=$row[TGL_INVOICE];  
				$vendor_v[]=$row[NO_VENDOR];
				$nama_vendor_v[]=$row[NAMA_VENDOR];
				$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
				$no_inv_reverse_v[]=$row[NO_INV_REVERSE];
				$total_inv[]=$row[TOTAL_INV];
				$pajak_inv[]=$row[PAJAK_INV];
				$posting_date[]=$row[POSTING_DATE];
				
				//$keterangan_v[]=$row[KOMENTAR_REJECT]; 
				//$status_v[]=$row[STATUS_BA_INVOICE]; 
				//$status_id = $row[STATUS_BA_INVOICE]; 
				
				$sqlS = "select KOMENTAR_REJECT, STATUS_BA_INVOICE from EX_BA_INVOICE where ID =  (select max(ID) from EX_BA_INVOICE where NO_INVOICE = 	$row[NO_INVOICE]) ";   
				$query_s= @oci_parse($conn, $sqlS);
				@oci_execute($query_s);
				$row_s=@oci_fetch_array($query_s); 
				$keterangan_v[]=$row_s[KOMENTAR_REJECT];
				$status_v[]=$row_s[STATUS_BA_INVOICE];
				$status_id =$row_s[STATUS_BA_INVOICE];
				
				if($status_id==10){
					$status_name_v[]= "CREATE INVOICE";
				}else if($status_id==20){
					$status_name_v[]= "UPLOAD INVOICE";
				}else if($status_id==30){
					$status_name_v[]= "REVERSED";
				}else if($status_id==40){
					$status_name_v[]= "REJECTED";
				}else if($status_id==45){
					$status_name_v[]= "CANCEL PPL & INVOICE";
				}else if($status_id==50){
					$status_name_v[]= 'APPROVED BY SPV';
				}else if($status_id==60){
					$status_name_v[]= 'GENERATE PPL';
				}else if($status_id==70){
					$status_name_v[]= 'SIMULATE & POSTING PPL';
				}else if($status_id==80){
					$status_name_v[]= 'REJECT BY MANAJER VERIFIKASI';
				}else if($status_id==90){
					$status_name_v[]= 'APPROVED  BY MANAJER VERIFIKASI';
				}else if($status_id==100){
					$status_name_v[]= 'REJECT BY SM ACCOUNTING';
				}else if($status_id==110){
					$status_name_v[]= 'APPROVED  BY SM ACCOUNTING';
				}else if($status_id==120){
					$status_name_v[]= 'EKSPEDISI BENDAHARA';
				}else {
					$status_name_v[]= "";
				}
				 
				//$status_v[]=$row[STATUS_BA_INVOICE]; 
				$sqli = "SELECT *
							FROM EX_BA 
							WHERE NO_BA = '".$row[NO_BA]."'";   
				$query_li= @oci_parse($conn, $sqli);
				@oci_execute($query_li);
				$row_li=@oci_fetch_array($query_li); 
				$pajak_v[]=$row_li[PAJAK_INV];
				$total_klaim_v[]=$row_li[TOTAL_INV];
				
					#cari apa sudah di run/belum

					$no_invoice_sap_x = '';
					$sql2 = "SELECT *
								FROM EX_INVOICE 
								WHERE NO_INVOICE = '".$row[NO_INVOICE]."'"; 
								#AND NO_INVOICE_EX = '*****************' ";
					
					$query_bn= @oci_parse($conn, $sql2);
					@oci_execute($query_bn);
					$row_bn=@oci_fetch_array($query_bn);
					$no_invoice_sap_x =$row_bn[NO_INVOICE_SAP];  
					
					$no_invoice_sap_v[] = $no_invoice_sap_x;
		}
 

	} 
	$total=count($no_invoice_v);
	if ($total < 1){$komen = "Tidak Ada Data Yang Ditemukan";}

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
	<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
	<!-- import the calendar script -->
	<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
	<!-- import the language module -->
	<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
	<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
	<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
	<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
	<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
	<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
	<script src="../include/jquery.min.js"></script>
	<script src="../include/bootstrap/js/bootstrap.min.js"></script>
	<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">


</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Invoice </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search</th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No BA</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_expeditur?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No SPJ </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_shipment" name="no_shipment" value="<?=$no_shipment?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Faktur Pajak </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_faktur_pajak" name="no_faktur_pajak" value="<?=$no_faktur_pajak?>"/></td>
    </tr>
    <tr style="display:none;">
      <td  class="puso">Distributor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="distributor" name="distributor"  value="<?=$distributor?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Tgl Invoice</td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>" />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
    <tr>
      <td  class="puso">Status Invoice </td>
      <td  class="puso">:</td>
      <td ><select name="status_invoice" id="status_invoice">
          <option value="">---Pilih---</option> 
          <option value="10">CREATE INVOICE</option> 
          <option value="20">UPLOAD INVOICE</option> 
          <option value="30">REVERSED</option> 
          <option value="40">REJECTED</option> 
          <option value="45">CANCEL PPL & INVOICE</option> 
          <option value="50">APPROVED BY SPV</option>  
          <option value="60">GENERATE PPL</option> 
          <option value="70">SIMULATE & POSTING PPL</option> 
          <option value="80">REJECT BY MANAJER VERIFIKASI</option> 
          <option value="90">APPROVED  BY MANAJER VERIFIKASI</option> 
          <option value="100">REJECT BY SM ACCOUNTING</option>  
          <option value="110">APPROVED  BY SM ACCOUNTING</option> 
          <option value="120">EKSPEDISI BENDAHARA</option>  
		   
      </select></td>
    </tr>
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){		
		

?>
<!--<form id="data_claim" name="data_claim" method="post" action="komentar.php" >-->

<div align="center">
				<table width="95%" align="center" class="table table-responsive adminlist">
					<tr>
						<th align="left" colspan="4">
							<span class="style5">&nbsp;Tabel Data Invoice </span> 
							<!-- <span class="style5">&nbsp;-</span>  -->
							<!-- <a href=""class="button">EXPORT EXCEL</a> -->
							<span class="style5">
								<div>
							<form name="export" method="post" action="lihat_invoice_bag_darat_xls.php">
							<input name="no_shipment" type="hidden" id="no_shipment" value="<?= $_POST['no_shipment']?>"/>
							<input name="distributor" type="hidden" id="distributor" value="<?=$_POST['distributor']?>"/>
							<input name="tipe_transaksi" type="hidden" id="tipe_transaksi" value="<?=$_POST['tipe_transaksi']?>"/>
							<input name="tanggal_mulai" type="hidden" id="tanggal_mulai" value="<?=$_POST['tanggal_mulai']?>"/>
							<input name="tanggal_selesai" type="hidden" id="tanggal_selesai" value="<?=$_POST['tanggal_selesai']?>"/>
							<input name="warna_plat" type="hidden" id="warna_plat" value="<?=$_POST['warna_plat']?>"/>

							<input name="no_invoice" type="hidden" id="no_invoice" value="<?=$_POST['no_invoice']?>"/>
							<input name="no_invoice_expeditur" type="hidden" id="no_invoice_expeditur" value="<?=$_POST['no_invoice_expeditur']?>"/>
							<input name="no_ba" type="hidden" id="no_ba" value="<?=$_POST['no_ba']?>"/>
							<input name="status_invoice" type="hidden" id="status_invoice" value="<?=$_POST['status_invoice']?>"/>
							<input name="no_faktur_pajak" type="hidden" id="no_faktur_pajak" value="<?=$_POST['no_faktur_pajak']?>"/>
							
							<input name="excel" type="Submit" class="button" id="excel" value="Export" /> 	
							</form>
							</div>
							</span> 
						</th>
					</tr>
				</table>
			</div>
			<div align="center">
				<table width="95%" align="center" class="table table-bordered table-responsive table-hover adminlist"
					id="myScrollTable">
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;No.</strong></td> 
		<td align="center"><strong >NO Invoice </strong></td> 
		<td align="center"><strong >NO BA </strong></td> 
		<td align="center"><strong >NO PPL </strong></td> 
		<td align="center"><strong >NO Cancel PPL </strong></td> 
		<td align="center"><strong >Tgl. Inv</strong></td>
		<td align="center"><strong >Posting Date</strong></td>
		<td align="center"><strong >No Faktur Pajak </strong></td>
		<td align="center"><strong >Vendor </strong></td>
		<td align="center"><strong >DPP</strong></td>
		<td align="center"><strong >PPN</strong></td>
		<td align="center"><strong >Total SPJ </strong></td>
		<td align="center"><strong >Warna Plat </strong></td>
		<td align="center"><strong >Status </strong></td> 
		<td align="center"><strong>Keterangan</strong></td>
		<td align="center"><strong>Display</strong></td>
		<td align="center"><strong>Cetak</strong></td>
		<td align="center"><strong>Aksi</strong></td> 
                 
      </tr >
	  
  <? $total_tagihan = 0;    
  for($i=0; $i<$total;$i++) {
		
		//if($no_ba_v[$i]!=''){
			$total_tagihan += $total_klaim_v[$i]+$pajak_v[$i];
			$b=$i+1;
				if(($i % 2) == 0)	{	 
				echo "<tr class='row0' id='$rowke' >";
					}
				else	{	
				echo "<tr class='row1'  id='$rowke' >";
					}	
						$orgCom="orgke".$i;        
				?>     
				
				<td align="center"><? echo $b; ?></td> 
				<td align="center"><? echo $no_invoice_v[$i]; ?></td> 
				<td align="center"><? echo $no_ba_v[$i]; ?></td>
				<td align="center"><? echo $no_ppl_v[$i]; ?></td>
				<td align="center"><? echo $no_inv_reverse_v[$i]; ?></td>
				<td align="center"><? echo $tgl_invoice_v[$i]; ?></td>
				<td align="center">
				<? 
				$date = $posting_date[$i];
				if ($date != '' || $date != null) {
					echo $date;
				}else{
					echo '-';
				}
				?>
				</td>
				<td align="center"><? echo $no_pajak_ex_v[$i]; ?></td>
				<td align="center"><? echo $vendor_v[$i]; ?> - <? echo $nama_vendor_v[$i]; ?></td> 
				<!--  -->
				<td align="center"><? echo 'Rp.'.number_format($total_inv[$i],0,",","."); ?></td>
				<td align="center"><? echo 'Rp.'.number_format($pajak_inv[$i],0,",","."); ?></td>
				<!--  -->
				<td align="center"><? echo 'Rp.'.number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."); ?></td> 
				<td align="center"><? echo $warna_plat_v[$i]; ?></td>
				<td align="center"><? echo $status_name_v[$i]; ?></td>
				<td align="center"><? echo $keterangan_v[$i]; ?></td> 
				
				<td><a href="javascript:popUp('print_preview_ppl.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button">DISPLAY</a></td> 
				<?php if($status_v[$i] >= '110' ){ ?>
						<td><a href="javascript:popUp('print_draft_ppl_new.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button">CETAK</a></td>
				<?php }else{ ?>
				<td> - </td>
				<?php }  ?>
				<td align="center"><? echo '<a href="detail_invoice_ba.php?no_ba='.$no_ba_v[$i].'" style="margin-right: 4px; cursor: pointer; padding: 2px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Detail</a>'; ?>
				<? if($status_v[$i] == '40' || $status_v[$i] == '45'){ ?> 
				<form id="data_claim" name="data_claim" method="post" action="" ><input type="hidden" value="<? echo $no_invoice_v[$i]; ?>" name="nomer_invoice">
					<button type="submit" name="cancelInv" style="font-size: 11px;margin-right: 4px; margin-left: 4px; margin-top: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: red; color: #fff; border: 1px solid #000; border-radius: 4px;">Reject</button> 
					<br><button type="submit" name="cancelInv2" style="font-size: 11px;margin-right: 4px; margin-left: 4px; margin-top: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #ff7800; color: #fff; border: 1px solid #000; border-radius: 4px;">Reverse</button> </form>
				<? }?>
				</td> 
						
				</tr>
		  <? }?>  
		<tr class="quote" style="display:none">
		<td colspan="6" align="center">
		TOTAL		 </td>
		<td align="center"><strong><?=number_format($total_tagihan,0,",",".");?></strong></td>
		<td align="center" colspan="3"></td>
	    </tr>

	  <tr class="quote">
		<td colspan="2" align="center">
		<? $excel="CetakExcel_bag_darat.php"; ?>
		<a style='display:none' href="<?php printf("%s?no_shipment=$no_shipment&distributor=$distributor&tipe_transaksi=$tipe_transaksi&tanggal_mulai=$tanggal_mulai&tanggal_selesai=$tanggal_selesai&warna_plat=$warna_plat&nopol=$nopol&no_invoice=$no_invoice&no_invoice_expeditur=$no_invoice_expeditur", $excel,$no_shipment,$distributor,$tipe_transaksi,$tanggal_mulai,$tanggal_selesai,$warna_plat,$nopol,$no_invoice,$no_invoice_expeditur,$vendor); ?>"class="button">EXCEL</a></td>

		<td colspan="21" align="center">
		<a href="lihat_invoice_bag_darat.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	 <tr style='display:none'>
		<td colspan="17"><BR>
			<TABLE align="left"  class="adminlist" >
			<TR class="quote">
				<TD><B>Keterangan</B> :<BR>
					&nbsp;&nbsp;&nbsp;Free for payment<BR>
					*	Skip account<BR>
					#	Payment Proposal<BR>
					1	Hold<BR>
					2	Accepted<BR>
					3	Approved<BR>
					A	Locked for payment<BR>
					B	Blocked for payment		<BR>	
					N	Postprocess inc.pmnt<BR>
					P	Payment request<BR>
					R	Invoice verification<BR>
					V	Payment clearing<BR>
				</TD>
			</TR>
			</TABLE>
		</td>
	 </tr>
	</table>
	
	
	
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		<!--</form>-->

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
