<?
############ SAP Connection  ###################################################
//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; // PROD    ../include/sapclasses/logon_data.conf";
$link_koneksi_sap = "../include/sapclasses/logon_data.conf";
################################################################################

switch ($action) {
//==============================================================================================================
    case "input_claim_bag_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
//                      echo  $sql_ktg;
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
                        $total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up, 0);
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
//                        echo "<br>nyet1: ".$sql_smn;
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
//                                echo "<br>nyet2: ".$sql_smn;
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
                        $total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up, 0);

                        $total_pdpks_up = round($pdpks * $klaim_semen_up, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "DRAFT", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "bag_claim_darat.php";
        break;
//============================================================================================================================
    //==============================================================================================================
    case "input_claim_bag_darat2":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            //$tgl_dat="tgl_dat".$k;
            //$jam_dat="jam_dat".$k;
            //$min_dat="min_dat".$k;
            //$tgl_bkr="tgl_bkr".$k;
            //$jam_bkr="jam_bkr".$k;
            //$min_bkr="min_bkr".$k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                //$jam_datang_up=$_POST[$jam_dat];
                //$min_datang_up=$_POST[$min_dat];
                //$tgl_datang_up=$_POST[$tgl_dat]." ".$jam_datang_up.":".$min_datang_up;
                //$jam_bongkar_up=$_POST[$jam_bkr];
                //$min_bongkar_up=$_POST[$min_bkr];
                //$tgl_bongkar_up=$_POST[$tgl_bkr]." ".$jam_bongkar_up.":".$min_bongkar_up;
                //if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == ""){
                //	$check = false;
                //	$k=$sampai+2;
                //}else{
                $check = true;
                //}
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                //$tgl_dat="tgl_dat".$k;
                //$jam_dat="jam_dat".$k;
                //$min_dat="min_dat".$k;
                //$tgl_bkr="tgl_bkr".$k;
                //$jam_bkr="jam_bkr".$k;
                //$min_bkr="min_bkr".$k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem]; //klaim semen
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    //$jam_datang_up=$_POST[$jam_dat];
                    //$min_datang_up=$_POST[$min_dat];
                    //$tgl_datang_up=$_POST[$tgl_dat]." ".$jam_datang_up.":".$min_datang_up;
                    //$jam_bongkar_up=$_POST[$jam_bkr];
                    //$min_bongkar_up=$_POST[$min_bkr];
                    //$tgl_bongkar_up=$_POST[$tgl_bkr]." ".$jam_bongkar_up.":".$min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
//                         echo "<br>ktg: ".$sql_ktg;
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
                        $total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up, 0);
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
//			 echo "<br>sms: ".$sql_smn;
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                            AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                            AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                            AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
                        $total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up, 0);

                        $total_pdpks_up = round($pdpks * $klaim_semen_up, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "SYSDATE", "$user_name");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {

            $show_ket .= "Data Klaim Untuk $klaim_semen_up $klaim_kantong_up $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "bag_claim_darat_1.php";
        break;
//============================================================================================================================


    case "input_claim_bag_laut":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];

        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $orgke = "orgke" . $k;




                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }

                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
//			$total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up,0);
// untuk bag laut tidak ada klaim rezak
                        $total_ktg_rezak_up = 0;
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;
                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
                        // untuk bag laut tidak ada klaim semen dan PDPKS
//			$total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up,0);
//			$total_pdpks_up = round($pdpks * $klaim_semen_up,0);
//			$total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;

                        $total_semen_rusak_up = 0;
                        $total_pdpks_up = 0;
                        $total_klaim_semen_up = 0;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "DRAFT", "PARTIAL_INVOICED");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "bag_claim_laut.php";
        break;
//============================================================================================================================
    case "input_claim_bag_laut_fob":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];

        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
// untuk laut fob tidak klaim rezak dan kantong
//			$total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up,0);
//			$total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up,0);
                        $total_ktg_rusak_up = 0;
                        $total_ktg_rezak_up = 0;

                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;
                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
// untuk laut fob tidak klaim semen dan pdpks
//			$total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up,0);
//			$total_pdpks_up = round($pdpks * $klaim_semen_up,0);
//			$total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;

                        $total_semen_rusak_up = 0;
                        $total_pdpks_up = 0;
                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "DRAFT", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "bag_claim_laut_fob.php";
        break;
//============================================================================================================================
    case "input_claim_curah_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];

        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $beratke = "beratke" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;
                    $total_klaim_lebih_up = 0;
                    $semen_lebih_up = 0;
                    $pdpks_lebih_up = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $berat_semen_up = $_POST[$kla_sem];
                    $org_up = $_POST[$orgke];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $berat_up = $_POST[$beratke] * 1000;

                    $klaim_semen_up = $berat_semen_up - $berat_up;

                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

                    if ($klaim_semen_up != 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $toleransi_up = $row_smn[TOLERANSI];
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            $toleransi_up = $row_smn[TOLERANSI];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                                $toleransi_up = $row_smn[TOLERANSI];
                            }
                        }


                        $cek_toleransi = @($berat_up * $toleransi_up) / 100;

                        if ($klaim_semen_up != 0) {
                            if ($klaim_semen_up > 0) {// klaim +
                                $klaim_lebih = $klaim_semen_up;
                                $klaim_semen_up = 0;
                            } else {

                                if (abs($klaim_semen_up) <= $cek_toleransi) {// jika di dalam toleransi
                                    $klaim_semen_up = 0;
                                    $klaim_lebih = 0;
                                } else {// jika di luar toleransi
                                    $klaim_semen_up = abs($klaim_semen_up) - $cek_toleransi;
                                    $klaim_lebih = 0;
                                }
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        // tidak ada pembagian klaim
                        // harga tebus sama dengan harga tebus pemasaran atau yang di set.

                        $biaya_klaim_semen_up = 0;
                        $harga_tebus_45_up = $harga_tebus_up;
                        $harga_tebus_45plus_up = $harga_tebus_up;

                        if ($jarak_tgl <= 45) {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            //$pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            $pdpks = 0;
                        } else {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            //$pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            $pdpks = 0;
                        }

                        $total_semen_rusak_up = round(($hrg_smn_rusak * $klaim_semen_up) / 1000, 0);
                        $semen_lebih_up = round(($hrg_smn_rusak * $klaim_lebih) / 1000, 0);
                        $total_pdpks_up = round(($pdpks * $klaim_semen_up) / 1000, 0);
                        $pdpks_lebih_up = round(($pdpks * $klaim_lebih) / 1000, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                        $total_klaim_lebih_up = $semen_lebih_up + $pdpks_lebih_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;
                    $klaim_semen_up = $klaim_semen_up / 1000;
                    $klaim_lebih = round($klaim_lebih / 1000, 3);

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS', 'QTY_LEBIH', 'KLAIM_LEBIH', 'PDPKS_LEBIH', 'KLAIM_ALL_LEBIH', 'BERAT_TERIMA');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN", "$klaim_lebih", "$semen_lebih_up", "$pdpks_lebih_up", "$total_klaim_lebih_up", "$berat_semen_up");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }

        $habis = "curah_claim_darat.php";
        break;
//============================================================================================================================
    case "input_claim_curah_laut":
        /* 	$user_id=$_SESSION['user_id'];
          $user_name=$_SESSION['user_name'];
          $sampai=$_POST['total'];
          for($k=0;$k<$sampai;$k++){
          $idke="idke".$k;
          $urutke="urutke".$k;
          $kla_sem="kla_sem".$k;
          $kla_ktg="kla_ktg".$k;
          $tgl_dat="tgl_dat".$k;
          $jam_dat="jam_dat".$k;
          $min_dat="min_dat".$k;
          $tgl_bkr="tgl_bkr".$k;
          $jam_bkr="jam_bkr".$k;
          $min_bkr="min_bkr".$k;
          $prodke="prodke".$k;
          $kirimke="kirimke".$k;
          $spjke="spjke".$k;
          $soldke="soldke".$k;
          $tebuske="tebuske".$k;

          if(isset($_POST[$urutke])){
          $total_ktg_rusak_up = 0;
          $total_ktg_rezak_up = 0;
          $total_klaim_kantong_up = 0;
          $biaya_rezak_up=0;
          $biaya_ktg_up=0;

          $biaya_klaim_semen_up=0;
          $kompensasi_up=0;
          $harga_tebus_up=0;
          $total_pdpks_up=0;
          $total_klaim_semen_up = 0;
          $total_semen_rusak_up = 0;

          $total_klaim_all = 0;


          $id_up=$_POST[$idke];
          $spj_up=$_POST[$spjke];
          $klaim_semen_up=$_POST[$kla_sem];
          $klaim_kantong_up=$_POST[$kla_ktg];
          $produk_up=$_POST[$prodke];
          $tanggal_kirim_up=$_POST[$kirimke];
          if($tanggal_kirim_up == "")$tanggal_kirim_up=date("d-m-Y");

          $sekarang = date("m/d/Y");

          list($tgl,$bln,$thn)=split("-",$tanggal_kirim_up);
          $cek_tgl = $bln."/".$tgl."/".$thn;

          $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl))/86400);
          //echo " jarak tanggal ". $jarak_tgl;
          $jam_datang_up=$_POST[$jam_dat];
          $min_datang_up=$_POST[$min_dat];
          $tgl_datang_up=$_POST[$tgl_dat]." ".$jam_datang_up.":".$min_datang_up;

          $jam_bongkar_up=$_POST[$jam_bkr];
          $min_bongkar_up=$_POST[$min_bkr];
          $tgl_bongkar_up=$_POST[$tgl_bkr]." ".$jam_bongkar_up.":".$min_bongkar_up;

          $sold_to_up=$_POST[$soldke];
          $harga_tebus_up=$_POST[$tebuske];

          // mencari biaya klaim kantong
          //			if ($klaim_kantong_up > 0){
          //			$sql_ktg= "SELECT * FROM EX_CLAIM_KTG WHERE KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          //			$query_ktg= oci_parse($conn, $sql_ktg);
          //			oci_execute($query_ktg);
          //			while($row_ktg=oci_fetch_array($query_ktg)){
          //				$biaya_rezak_up=$row_ktg[BIAYA_REZAK];
          //				$biaya_ktg_up=$row_ktg[BIAYA_KTG];
          //			}
          //			$total_ktg_rusak_up = $biaya_ktg_up * $klaim_kantong_up;
          //			$total_ktg_rezak_up = $biaya_rezak_up * $klaim_kantong_up;
          //			$total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
          //			}
          // mencari biaya klaim semen
          if ($klaim_semen_up > 0){
          $sql_smn= "SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          $query_smn= oci_parse($conn, $sql_smn);
          oci_execute($query_smn);
          while($row_smn=oci_fetch_array($query_smn)){
          $biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN];
          $harga_tebus_v=$row_smn[HARGA_TEBUS];
          $harga_tebus_45_up=$row_smn[HARGA_TEBUS_45];
          $harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS];
          }

          if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0){
          $sql_smn= "SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          $query_smn= oci_parse($conn, $sql_smn);
          oci_execute($query_smn);
          while($row_smn=oci_fetch_array($query_smn)){
          $biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN];
          $harga_tebus_v=$row_smn[HARGA_TEBUS];
          $harga_tebus_45_up=$row_smn[HARGA_TEBUS_45];
          $harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS];
          }
          }

          if ($harga_tebus_up=="" or $harga_tebus_up == 0)$harga_tebus_up=$harga_tebus_v;

          if ($jarak_tgl <= 45){
          $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
          $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
          }else{
          $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
          $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
          }
          $total_semen_rusak_up = $hrg_smn_rusak * $klaim_semen_up;

          $total_pdpks_up = $pdpks * $klaim_semen_up;

          $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
          }
          $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

          $field_names=array('QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','HARGA_TEBUS','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','LAST_UPDATE_DATE','LAST_UPDATED_BY','STATUS');
          $field_data=array("$klaim_kantong_up","$klaim_semen_up","$total_ktg_rusak_up","$total_ktg_rezak_up","$total_klaim_kantong_up","$total_semen_rusak_up","$harga_tebus_up","$total_pdpks_up","$total_klaim_semen_up","$total_klaim_all","updtgl_$tgl_datang_up","updtgl_$tgl_bongkar_up","SYSDATE","$user_name","OPEN");
          $tablename="EX_TRANS_HDR";
          $field_id=array('ID');
          $value_id=array("$id_up");
          $fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

          $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
          }
          }
          $habis = "curah_claim_laut.php";
         */ break;
//============================================================================================================================
    case "edit_claim_bag_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];

        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
                        $total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up, 0);
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
                        $total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up, 0);

                        $total_pdpks_up = round($pdpks * $klaim_semen_up, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "OPEN", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "proses_claim_bag_darat.php";
        break;
//============================================================================================================================
    case "edit_claim_bag_laut":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
//			$total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up,0);
// untuk bag laut tidak ada klaim rezak
                        $total_ktg_rezak_up = 0;
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;
                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }


                        // untuk bag laut tidak ada klaim semen dan PDPKS
//			$total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up,0);
//			$total_pdpks_up = round($pdpks * $klaim_semen_up,0);
//			$total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;

                        $total_semen_rusak_up = 0;
                        $total_pdpks_up = 0;
                        $total_klaim_semen_up = 0;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "OPEN", "PARTIAL_INVOICED");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "proses_claim_bag_laut.php";
        break;
//============================================================================================================================
    case "edit_claim_bag_laut_fob":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $klaim_semen_up = $_POST[$kla_sem];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $org_up = $_POST[$orgke];
                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

// mencari biaya klaim kantong
                    if ($klaim_kantong_up > 0) {
                        $sql_ktg = "SELECT * FROM EX_CLAIM_KTG WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_ktg = oci_parse($conn, $sql_ktg);
                        oci_execute($query_ktg);
                        while ($row_ktg = oci_fetch_array($query_ktg)) {
                            $biaya_rezak_up = $row_ktg[BIAYA_REZAK];
                            $biaya_ktg_up = $row_ktg[BIAYA_KTG];
                        }
                        $total_ktg_rusak_up = round($biaya_ktg_up * $klaim_kantong_up, 0);
                        $total_ktg_rezak_up = round($biaya_rezak_up * $klaim_kantong_up, 0);
                        $total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
                    }
// mencari biaya klaim semen
                    if ($klaim_semen_up > 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            }
                        }

                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;
                        //Update Klaim Semen untuk pembuatan PDPKS
                        list($daybbn, $monthbbn, $yearbbn) = split("-", $tanggal_kirim_up);
                        $tglmnewkirim = $yearbbn . $monthbbn . $daybbn;
                        if ($tglmnewkirim >= '20150306') {
                            $sql_persen = "
                                        SELECT * FROM EX_CLAIMPERSEN_SEMEN WHERE ORG='$org_up' 
                                        AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY')
                                        AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) 
                                        AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_persenpdpkssmn = oci_parse($conn, $sql_persen);
                            oci_execute($query_persenpdpkssmn);
                            //                      echo "<br>nyet2: ".$query_persenpdpkssmn;
                            while ($row_persensmn = oci_fetch_array($query_persenpdpkssmn)) {
                                $persen_tebus_45_up = $row_persensmn[PERSEN_TEBUS_45];
                                $persen_tebus_45plus_up = $row_persensmn[PERSEN_TEBUS_45PLUS];
                            }
                            //update 06 Maret 2015
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45_up * $harga_tebus_up) / 100) + $harga_tebus_up) - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up;
                                $pdpks = (@(($persen_tebus_45plus_up * $harga_tebus_up) / 100) + $harga_tebus_up ) - $hrg_smn_rusak;
                            }
                        } else {
                            if ($jarak_tgl <= 45) {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
                            } else {
                                $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                                $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
                            }
                        }
                        $total_semen_rusak_up = round($hrg_smn_rusak * $klaim_semen_up, 0);

                        $total_pdpks_up = round($pdpks * $klaim_semen_up, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "$total_pdpks_up", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "OPEN", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "proses_claim_bag_laut_fob.php";
        break;
//============================================================================================================================
    case "edit_claim_curah_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $beratke = "beratke" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;
                    $total_klaim_lebih_up = 0;
                    $semen_lebih_up = 0;
                    $pdpks_lebih_up = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $berat_semen_up = $_POST[$kla_sem];
                    $org_up = $_POST[$orgke];
                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $berat_up = $_POST[$beratke] * 1000;

                    $klaim_semen_up = $berat_semen_up - $berat_up;

                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

                    if ($klaim_semen_up != 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $toleransi_up = $row_smn[TOLERANSI];
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            $toleransi_up = $row_smn[TOLERANSI];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                                $toleransi_up = $row_smn[TOLERANSI];
                            }
                        }
                        $cek_toleransi = @($berat_up * $toleransi_up) / 100;


                        if ($klaim_semen_up != 0) {
                            if ($klaim_semen_up > 0) {// klaim +
                                $klaim_lebih = $klaim_semen_up;
                                $klaim_semen_up = 0;
                            } else {

                                if (abs($klaim_semen_up) <= $cek_toleransi) {// jika di dalam toleransi
                                    $klaim_semen_up = 0;
                                    $klaim_lebih = 0;
                                } else {// jika di luar toleransi
                                    $klaim_semen_up = abs($klaim_semen_up) - $cek_toleransi;
                                    $klaim_lebih = 0;
                                }
                            }
                        }


                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        $biaya_klaim_semen_up = 0;
                        $harga_tebus_45_up = $harga_tebus_up;
                        $harga_tebus_45plus_up = $harga_tebus_up;

                        if ($jarak_tgl <= 45) {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            $pdpks = 0;
                        } else {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            $pdpks = 0;
                        }

                        $total_semen_rusak_up = round(($hrg_smn_rusak * $klaim_semen_up) / 1000, 0);
                        $semen_lebih_up = round(($hrg_smn_rusak * $klaim_lebih) / 1000, 0);
                        $total_pdpks_up = round(($pdpks * $klaim_semen_up) / 1000, 0);
                        $pdpks_lebih_up = round(($pdpks * $klaim_lebih) / 1000, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                        $total_klaim_lebih_up = $semen_lebih_up + $pdpks_lebih_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;
                    $klaim_semen_up = $klaim_semen_up / 1000;
                    $klaim_lebih = round($klaim_lebih / 1000, 3);

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS', 'QTY_LEBIH', 'KLAIM_LEBIH', 'PDPKS_LEBIH', 'KLAIM_ALL_LEBIH', 'BERAT_TERIMA');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "0", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "OPEN", "$klaim_lebih", "$semen_lebih_up", "0", "$total_klaim_lebih_up", "$berat_semen_up");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "OPEN", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "proses_claim_curah_darat.php";
        break;
//============================================================================================================================
    //============================================================================================================================
    case "edit_claim_curah_darat2":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;
            $urutke = "urutke" . $k;
            $kla_sem = "kla_sem" . $k;
            $kla_ktg = "kla_ktg" . $k;
            $tgl_dat = "tgl_dat" . $k;
            $jam_dat = "jam_dat" . $k;
            $min_dat = "min_dat" . $k;
            $tgl_bkr = "tgl_bkr" . $k;
            $jam_bkr = "jam_bkr" . $k;
            $min_bkr = "min_bkr" . $k;
            $prodke = "prodke" . $k;
            $kirimke = "kirimke" . $k;
            $spjke = "spjke" . $k;
            $soldke = "soldke" . $k;
            $tebuske = "tebuske" . $k;
            $nourut = " no urut ke " . $k;
            $orgke = "orgke" . $k;

            if (isset($_POST[$urutke])) {
                $jam_datang_up = $_POST[$jam_dat];
                $min_datang_up = $_POST[$min_dat];
                $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                $jam_bongkar_up = $_POST[$jam_bkr];
                $min_bongkar_up = $_POST[$min_bkr];
                $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;
                if ($_POST[$tgl_dat] == "" or $_POST[$min_dat] == "" or $_POST[$jam_dat] == "" or $_POST[$jam_bkr] == "" or $_POST[$min_bkr] == "" or $_POST[$tgl_bkr] == "") {
                    $check = false;
                    $k = $sampai + 2;
                } else {
                    $check = true;
                }
            }
        }

        if ($check) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;
                $kla_sem = "kla_sem" . $k;
                $kla_ktg = "kla_ktg" . $k;
                $tgl_dat = "tgl_dat" . $k;
                $jam_dat = "jam_dat" . $k;
                $min_dat = "min_dat" . $k;
                $tgl_bkr = "tgl_bkr" . $k;
                $jam_bkr = "jam_bkr" . $k;
                $min_bkr = "min_bkr" . $k;
                $prodke = "prodke" . $k;
                $kirimke = "kirimke" . $k;
                $spjke = "spjke" . $k;
                $soldke = "soldke" . $k;
                $tebuske = "tebuske" . $k;
                $beratke = "beratke" . $k;
                $orgke = "orgke" . $k;

                if (isset($_POST[$urutke])) {
                    $total_ktg_rusak_up = 0;
                    $total_ktg_rezak_up = 0;
                    $total_klaim_kantong_up = 0;
                    $biaya_rezak_up = 0;
                    $biaya_ktg_up = 0;

                    $biaya_klaim_semen_up = 0;
                    $kompensasi_up = 0;
                    $harga_tebus_up = 0;
                    $total_pdpks_up = 0;
                    $total_klaim_semen_up = 0;
                    $total_semen_rusak_up = 0;

                    $total_klaim_all = 0;
                    $total_klaim_lebih_up = 0;
                    $semen_lebih_up = 0;
                    $pdpks_lebih_up = 0;


                    $id_up = $_POST[$idke];
                    $spj_up = $_POST[$spjke];
                    $berat_semen_up = $_POST[$kla_sem];

                    $klaim_kantong_up = $_POST[$kla_ktg];
                    $produk_up = $_POST[$prodke];
                    $tanggal_kirim_up = $_POST[$kirimke];
                    $berat_up = $_POST[$beratke] * 1000;
                    $org_up = $_POST[$orgke];

                    $klaim_semen_up = $berat_semen_up - $berat_up;

                    if ($tanggal_kirim_up == "")
                        $tanggal_kirim_up = date("d-m-Y");

                    $sekarang = date("m/d/Y");

                    list($tgl, $bln, $thn) = split("-", $tanggal_kirim_up);
                    $cek_tgl = $bln . "/" . $tgl . "/" . $thn;

                    $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl)) / 86400);
                    //echo " jarak tanggal ". $jarak_tgl;
                    $jam_datang_up = $_POST[$jam_dat];
                    $min_datang_up = $_POST[$min_dat];
                    $tgl_datang_up = $_POST[$tgl_dat] . " " . $jam_datang_up . ":" . $min_datang_up;

                    $jam_bongkar_up = $_POST[$jam_bkr];
                    $min_bongkar_up = $_POST[$min_bkr];
                    $tgl_bongkar_up = $_POST[$tgl_bkr] . " " . $jam_bongkar_up . ":" . $min_bongkar_up;

                    $sold_to_up = $_POST[$soldke];
                    $harga_tebus_up = $_POST[$tebuske];

                    if ($klaim_semen_up != 0) {
                        $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                        $query_smn = oci_parse($conn, $sql_smn);
                        oci_execute($query_smn);
                        while ($row_smn = oci_fetch_array($query_smn)) {
                            $toleransi_up = $row_smn[TOLERANSI];
                            $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                            $harga_tebus_v = $row_smn[HARGA_TEBUS];
                            $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                            $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                            $toleransi_up = $row_smn[TOLERANSI];
                        }

                        if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0) {
                            $sql_smn = "SELECT * FROM EX_CLAIM_SEMEN WHERE ORG='$org_up' AND KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
                            $query_smn = oci_parse($conn, $sql_smn);
                            oci_execute($query_smn);
                            while ($row_smn = oci_fetch_array($query_smn)) {
                                $biaya_klaim_semen_up = $row_smn[KLAIM_SEMEN];
                                $harga_tebus_v = $row_smn[HARGA_TEBUS];
                                $harga_tebus_45_up = $row_smn[HARGA_TEBUS_45];
                                $harga_tebus_45plus_up = $row_smn[HARGA_TEBUS_45PLUS];
                                $toleransi_up = $row_smn[TOLERANSI];
                            }
                        }
                        $cek_toleransi = @($berat_up * $toleransi_up) / 100;


                        if ($klaim_semen_up != 0) {
                            if ($klaim_semen_up > 0) {// klaim +
                                $klaim_lebih = $klaim_semen_up;
                                $klaim_semen_up = 0;
                            } else {

                                if (abs($klaim_semen_up) <= $cek_toleransi) {// jika di dalam toleransi
                                    $klaim_semen_up = 0;
                                    $klaim_lebih = 0;
                                } else {// jika di luar toleransi
                                    $klaim_semen_up = abs($klaim_semen_up) - $cek_toleransi;
                                    $klaim_lebih = 0;
                                }
                            }
                        }


                        if ($harga_tebus_up == "" or $harga_tebus_up == 0)
                            $harga_tebus_up = $harga_tebus_v;

                        $biaya_klaim_semen_up = 0;
                        $harga_tebus_45_up = $harga_tebus_up;
                        $harga_tebus_45plus_up = $harga_tebus_up;

                        if ($jarak_tgl <= 45) {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            $pdpks = 0;
                        } else {
                            $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
                            $pdpks = 0;
                        }

                        $total_semen_rusak_up = round(($hrg_smn_rusak * $klaim_semen_up) / 1000, 0);
                        $semen_lebih_up = round(($hrg_smn_rusak * $klaim_lebih) / 1000, 0);
                        $total_pdpks_up = round(($pdpks * $klaim_semen_up) / 1000, 0);
                        $pdpks_lebih_up = round(($pdpks * $klaim_lebih) / 1000, 0);

                        $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
                        $total_klaim_lebih_up = $semen_lebih_up + $pdpks_lebih_up;
                    }
                    $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;
                    $klaim_semen_up = $klaim_semen_up / 1000;
                    $klaim_lebih = round($klaim_lebih / 1000, 3);

                    $field_names = array('QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TOTAL_KLAIM_ALL', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'QTY_LEBIH', 'KLAIM_LEBIH', 'PDPKS_LEBIH', 'KLAIM_ALL_LEBIH', 'BERAT_TERIMA');
                    $field_data = array("$klaim_kantong_up", "$klaim_semen_up", "$total_ktg_rusak_up", "$total_ktg_rezak_up", "$total_klaim_kantong_up", "$total_semen_rusak_up", "$harga_tebus_up", "0", "$total_klaim_semen_up", "$total_klaim_all", "updtgl_$tgl_datang_up", "updtgl_$tgl_bongkar_up", "SYSDATE", "$user_name", "$klaim_lebih", "$semen_lebih_up", "0", "$total_klaim_lebih_up", "$berat_semen_up");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
                }
            }
        } else {
            $show_ket .= "Data Klaim Untuk $nourut Salah.. Silahkan Input Ulang... <br>";
        }
        $habis = "proses_claim_curah_darat2.php";
        break;
//============================================================================================================================
    case "edit_claim_curah_laut":
        /* 	$user_id=$_SESSION['user_id'];
          $user_name=$_SESSION['user_name'];
          $sampai=$_POST['total'];
          for($k=0;$k<$sampai;$k++){
          $idke="idke".$k;
          $urutke="urutke".$k;
          $kla_sem="kla_sem".$k;
          $kla_ktg="kla_ktg".$k;
          $tgl_dat="tgl_dat".$k;
          $jam_dat="jam_dat".$k;
          $min_dat="min_dat".$k;
          $tgl_bkr="tgl_bkr".$k;
          $jam_bkr="jam_bkr".$k;
          $min_bkr="min_bkr".$k;
          $prodke="prodke".$k;
          $kirimke="kirimke".$k;
          $spjke="spjke".$k;
          $soldke="soldke".$k;
          $tebuske="tebuske".$k;

          if(isset($_POST[$urutke])){
          $total_ktg_rusak_up = 0;
          $total_ktg_rezak_up = 0;
          $total_klaim_kantong_up = 0;
          $biaya_rezak_up=0;
          $biaya_ktg_up=0;

          $biaya_klaim_semen_up=0;
          $kompensasi_up=0;
          $harga_tebus_up=0;
          $total_pdpks_up=0;
          $total_klaim_semen_up = 0;
          $total_semen_rusak_up = 0;

          $total_klaim_all = 0;


          $id_up=$_POST[$idke];
          $spj_up=$_POST[$spjke];
          $klaim_semen_up=$_POST[$kla_sem];
          $klaim_kantong_up=$_POST[$kla_ktg];
          $produk_up=$_POST[$prodke];
          $tanggal_kirim_up=$_POST[$kirimke];
          if($tanggal_kirim_up == "")$tanggal_kirim_up=date("d-m-Y");

          $sekarang = date("m/d/Y");

          list($tgl,$bln,$thn)=split("-",$tanggal_kirim_up);
          $cek_tgl = $bln."/".$tgl."/".$thn;

          $jarak_tgl = round((strtotime($sekarang) - strtotime($cek_tgl))/86400);
          //echo " jarak tanggal ". $jarak_tgl;
          $jam_datang_up=$_POST[$jam_dat];
          $min_datang_up=$_POST[$min_dat];
          $tgl_datang_up=$_POST[$tgl_dat]." ".$jam_datang_up.":".$min_datang_up;

          $jam_bongkar_up=$_POST[$jam_bkr];
          $min_bongkar_up=$_POST[$min_bkr];
          $tgl_bongkar_up=$_POST[$tgl_bkr]." ".$jam_bongkar_up.":".$min_bongkar_up;

          $sold_to_up=$_POST[$soldke];
          $harga_tebus_up=$_POST[$tebuske];

          // mencari biaya klaim kantong
          //			if ($klaim_kantong_up > 0){
          //			$sql_ktg= "SELECT * FROM EX_CLAIM_KTG WHERE KODE_PRODUK='$produk_up' AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          //			$query_ktg= oci_parse($conn, $sql_ktg);
          //			oci_execute($query_ktg);
          //			while($row_ktg=oci_fetch_array($query_ktg)){
          //				$biaya_rezak_up=$row_ktg[BIAYA_REZAK];
          //				$biaya_ktg_up=$row_ktg[BIAYA_KTG];
          //			}
          //			$total_ktg_rusak_up = $biaya_ktg_up * $klaim_kantong_up;
          //			$total_ktg_rezak_up = $biaya_rezak_up * $klaim_kantong_up;
          //			$total_klaim_kantong_up = $total_ktg_rusak_up + $total_ktg_rezak_up;
          //			}
          // mencari biaya klaim semen
          if ($klaim_semen_up > 0){
          $sql_smn= "SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$produk_up' AND TUJUAN = '$sold_to_up'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          $query_smn= oci_parse($conn, $sql_smn);
          oci_execute($query_smn);
          while($row_smn=oci_fetch_array($query_smn)){
          $biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN];
          $harga_tebus_v=$row_smn[HARGA_TEBUS];
          $harga_tebus_45_up=$row_smn[HARGA_TEBUS_45];
          $harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS];
          }

          if ($biaya_klaim_semen_up == "" or $biaya_klaim_semen_up == 0){
          $sql_smn= "SELECT * FROM EX_CLAIM_SEMEN WHERE KODE_PRODUK='$produk_up' AND TUJUAN = '9999'  AND START_DATE <= To_date('$tanggal_kirim_up','DD-MM-YYYY') AND (END_DATE >= To_date('$tanggal_kirim_up','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";
          $query_smn= oci_parse($conn, $sql_smn);
          oci_execute($query_smn);
          while($row_smn=oci_fetch_array($query_smn)){
          $biaya_klaim_semen_up=$row_smn[KLAIM_SEMEN];
          $harga_tebus_v=$row_smn[HARGA_TEBUS];
          $harga_tebus_45_up=$row_smn[HARGA_TEBUS_45];
          $harga_tebus_45plus_up=$row_smn[HARGA_TEBUS_45PLUS];
          }
          }

          if ($harga_tebus_up=="" or $harga_tebus_up == 0)$harga_tebus_up=$harga_tebus_v;

          if ($jarak_tgl <= 45){
          $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
          $pdpks = $harga_tebus_45_up - $hrg_smn_rusak;
          }else{
          $hrg_smn_rusak = $harga_tebus_up + $biaya_klaim_semen_up;
          $pdpks = $harga_tebus_45plus_up - $hrg_smn_rusak;
          }
          $total_semen_rusak_up = $hrg_smn_rusak * $klaim_semen_up;

          $total_pdpks_up = $pdpks * $klaim_semen_up;

          $total_klaim_semen_up = $total_semen_rusak_up + $total_pdpks_up;
          }
          $total_klaim_all = $total_klaim_semen_up + $total_klaim_kantong_up;

          $field_names=array('QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','HARGA_TEBUS','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','LAST_UPDATE_DATE','LAST_UPDATED_BY','STATUS');
          $field_data=array("$klaim_kantong_up","$klaim_semen_up","$total_ktg_rusak_up","$total_ktg_rezak_up","$total_klaim_kantong_up","$total_semen_rusak_up","$harga_tebus_up","$total_pdpks_up","$total_klaim_semen_up","$total_klaim_all","updtgl_$tgl_datang_up","updtgl_$tgl_bongkar_up","SYSDATE","$user_name","OPEN");
          $tablename="EX_TRANS_HDR";
          $field_id=array('ID');
          $value_id=array("$id_up");
          $fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

          $show_ket .= "Data Klaim Untuk SPJ No $spj_up Sukses Di Tambahkan <br>";
          }
          }
          $habis = "proses_claim_curah_laut.php";
         */ break;
//============================================================================================================================
    case "create_invoice_bag_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        $totChecked = $_POST['totalCheked'];
        $no_invoice_vendor_in = trim(strtoupper($_POST['no_invoice_vendor']));
        
        $tanggal_pjk = $_POST['tanggal_pjk'];
        if(strtotime($tanggal_pjk ) >= strtotime("2025-01-01")){   
            $no_pajak_vendor_in=$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5'];                      
        }else{            
            $no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4']; 
        }
        $pajak_bin = $no_pajak_vendor_in; //$_POST['pjk1'].".".$_POST['pjk2']."-".$_POST['pjk3'].".".substr($_POST['pjk4'],0,7)."*";
        $no_vendor_in = $_POST['no_vendor'];
        $nama_vendor_in = $_POST['nama_vendor'];
        $warna_plat_in = $_POST['warna_plat'];
        $bulan = $_POST['bulan'];
        $tahun = $_POST['tahun'];
        $orgSpj = $_POST['orgSPJ'];
        $spt_cek = $_POST['spt_cek'];
        $tanggal_pjk = $_POST['tanggal_pjk'];
        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = trim($_REQUEST['nama_bank']);
        $cabang_bank = trim($_REQUEST['cabang_bank']);
        $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
        $bvtyp = $_REQUEST['bvtyp'];      
        $noBaF = $_POST['orgSPJ'].'.'.$_POST['termin'].'.'.(int)$no_vendor_in.'.'.time();
        if ($warna_plat_in == "KUNING")
            $no_pajak_vendor_in = "";
       //Limit number spj per invoice
       if($totChecked < 501){
        unset($orgchose);
        for ($kn = 0; $kn < $sampai; $kn++) {
            $idken = "idke" . $kn;
            $urutken = "urutke" . $kn;
            $orgkej = "orgke" . $kn;

            if (isset($_POST[$idken])) {
                $id_upn = $_POST[$idken];
                $orgkej = $_POST[$orgkej];
                $orgchose[$orgkej] = $orgkej;
            }
        }
       
        if (count($orgchose) == 1) {

            if ($spt_cek == "GABUNGAN") {
                $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY')='$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX <> '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                $query_spt = oci_parse($conn, $sql_spt);
                oci_execute($query_spt);
                $row_spt = oci_fetch_assoc($query_spt);
                $data_spt = $row_spt[HIT];
                if ($row_spt[HIT] > 0) {
                    $cek_data = false;
                    $show_ket .= " Pajak Anda Gabungan, harus sama untuk satu periode pajak.. <br>";
                }else
                    $cek_data = true;

                if ($cek_data) {
                    $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY') <> '$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                    $query_spt = oci_parse($conn, $sql_spt);
                    oci_execute($query_spt);
                    $row_spt = oci_fetch_assoc($query_spt);
                    $data_spt = $row_spt[HIT];

                    if ($row_spt[HIT] > 0) {
                        $cek_data = false;
                        $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
                    }else
                        $cek_data = true;
                }
            }else {
                // cek unutk transaksi
                // tidak ada boleh no pajak yang sama
                $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                $query_spt = oci_parse($conn, $sql_spt);
                oci_execute($query_spt);
                $row_spt = oci_fetch_assoc($query_spt);
                $data_spt = $row_spt[HIT];

                if ($row_spt[HIT] > 0) {
                    $cek_data = false;
                    $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
                }else
                    $cek_data = true;
            }

            if ($warna_plat_in == "KUNING") {
                $cek_data = true;
                $show_ket = "";
            }

            if ($cek_data) {
                //Cek is already cretae invoice
                //$sqlCntInv = "SELECT count(*) AS JMLHINV from EX_INVOICE where DELETE_MARK ='0' AND ORG ='$orgSpj' AND NO_VENDOR = '$no_vendor_in' AND TGL_INVOICE BETWEEN trunc(sysdate) AND trunc(sysdate)+1  ORDER BY TGL_INVOICE DESC";
                $sqlCntInv ="
                    SELECT
                            COUNT(TBL1.NO_INVOICE) AS JMLHINV
                    FROM
                            (
                                    SELECT
                                            EXI.NO_INVOICE
                                    FROM
                                            EX_INVOICE EXI
                                    JOIN EX_TRANS_HDR ETH ON ETH.NO_INVOICE = EXI.NO_INVOICE
                                    WHERE
                                            EXI.DELETE_MARK = '0'
                                    AND EXI.ORG = '$orgSpj'
                                    AND EXI.NO_VENDOR = '$no_vendor_in'
                                    AND ETH.TANGGAL_INVOICE BETWEEN TRUNC (SYSDATE)
                                    AND TRUNC (SYSDATE) + 1
                                    AND ETH.WARNA_PLAT ='$warna_plat_in'
                                    GROUP BY
                                            EXI.NO_INVOICE
                            ) TBL1
                ";
                $queryCntInv = oci_parse($conn, $sqlCntInv);
                oci_execute($queryCntInv);
                $row_cnt = oci_fetch_assoc($queryCntInv);
                $jmlhInv = $row_cnt[JMLHINV];
                $t=date('d-m-Y');
                $cektglnow = (int)date("d",strtotime($t));
                if(($jmlhInv > 100)){
                    $show_ket = "Unable create invoice (Reach Limit Day)";                
                }else{
                    $data_900 = 0;
                for ($k = 0; $k < $sampai; $k++) {
                    $idke = "idke" . $k;
                    $urutke = "urutke" . $k;

                    if (isset($_POST[$idke])) {
                        $id_up = $_POST[$idke];

                        if ($no_invoice_in == "") {
                            $no_invoice_in = $fungsi->new_invoice_number($conn);
                            $inv_old = $no_invoice_in;
                            $no_inv_data_900[] = $no_invoice_in;
                        }
                        if ($data_900 == 850) {
                            $no_invoice_in = $fungsi->new_invoice_number($conn);
                            //$no_pajak_vendor_in=$$no_pajak_vendor_in;
                            $no_inv_data_900[] = $no_invoice_in;
                            $data_900 = 0;
                        }
                        if ($no_invoice_in != $inv_old)
                            $no_pajak_vendor_in_sql = $pajak_bin;
                        else
                            $no_pajak_vendor_in_sql = $no_pajak_vendor_in;

                        if (!$no_invoice_vendor_in) {
                            $no_invoice_vendor_in = $no_invoice_in;
                        }
                        $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'STATUS2', 'NO_TAGIHAN');
                        $field_data = array("$no_invoice_vendor_in", "$user_name", "$no_invoice_in", "PROGRESS", "SYSDATE", "SYSDATE", "$user_name", "$no_pajak_vendor_in_sql", "OPEN", "$inv_old");
                        $tablename = "EX_TRANS_HDR";
                        $field_id = array('ID', 'STATUS', 'STATUS2');
                        $value_id = array("$id_up", "OPEN", "OPEN");
                        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                        $data_900+=1;
                    }
                }

                for ($in = 0; $in < count($no_inv_data_900); $in++) {
                    $no_invoice_in = $no_inv_data_900[$in];
                    $sqlcek = "SELECT ORG, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' GROUP BY ORG ";
                    $querycek = oci_parse($conn, $sqlcek);
                    oci_execute($querycek);
                    $row_data = oci_fetch_assoc($querycek);
                    $total_klaim_in = $row_data[TOTAL_KLAIM];
                    $total_shp_in = $row_data[SHP_COST];
                    $total_ktg_in = $row_data[TOTAL_KTG];
                    $total_semen_in = $row_data[TOTAL_SEMEN];
                    $total_pdpks_in = $row_data[TOTAL_PDPKS];
                    $total_pdpkk_in = $row_data[TOTAL_PDPKK];
                    $org_in = $row_data[ORG];
                    //tambahan pengujian pajak 11 persen 01-04-2022
                    if ($warna_plat_in == "HITAM")
                        if (date("Ymd") >= ********){
                            $pajak_in = round($total_shp_in * 0.11, 0);
                        }else{
                            $pajak_in = round($total_shp_in * 0.1, 0);   
                        }
                    //----------------------------------
                    else
                        $pajak_in = 0;
                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                    $field_names = array('NO_INVOICE_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_INVOICE', 'TGL_INVOICE', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'TGL_TERMIN', 'TERMIN', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI','NO_BAF');
                    $field_data = array("$no_invoice_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "$no_invoice_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$tgl_tremin", "$termin", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi","$noBaF");
                    $tablename = "EX_INVOICE";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);
                    $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
                }

                // $dendak3->pemotongan_dendak3($conn, $no_invoice_in, $no_vendor_in, $total_shp_in, $user_name);
                $total_tagihan50 = floatval($total_shp_in) * 0.5;
                //SQL GET DENDA YG SUDAH TERPOTONG
                if($orgSpj == '7000'){
                    $sql = "SELECT
					TABLEA.ID_VENDOR,
					TABLEA.NO_DOKUMEN,
					TABLEB.TERPOTONG,
					TABLEA.JUMLAH,
					(
						TABLEA.JUMLAH - TABLEB.TERPOTONG
					) AS SISA
				FROM
					(
						SELECT
							ID_VENDOR,
							NO_DOKUMEN,
							TOTAL_DENDA AS JUMLAH
						FROM
							EX_DENDAK3_DOC
						WHERE
							ID_VENDOR = '$no_vendor_in'
					) TABLEA
				LEFT JOIN (
					SELECT
						NO_DOC_DENDA AS NO_DOKUMEN,
						NVL (SUM(KREDIT), 0) AS TERPOTONG
					FROM
						EX_DENDAK3_SALDO
					GROUP BY
						NO_DOC_DENDA
				) TABLEB ON TABLEA.NO_DOKUMEN = TABLEB.NO_DOKUMEN";

                $queryk3 = oci_parse($conn, $sql);
                $raw = oci_execute($queryk3);

                $affected_doc = array();
                $nilai_denda = 0; //nilai denda yang akan dipotongkan
                $nilai_saldo = 0;

                while ($row = oci_fetch_array($queryk3)) {
                    $tmp_denda = 0;
                    //jika ada sisa dari tansaksi sbelumnya akan mengambil kolom sisa
                    if ($row[SISA] == null) {
                        $tmp_denda = floatval($row[JUMLAH]);
                    } else {
                        $tmp_denda = floatval($row[SISA]);
                    }
                    // print_r($row);
                    $jumlah_denda = floatval($row[JUMLAH]);
                    $no_doc = $row[NO_DOKUMEN];
                    //jika dibawah 50% oa maka akan langsung di potongkan
                    // $show_ket .= "$total_tagihan50 .<<< $no_doc    $tmp_denda  -> ";

                    if ($total_tagihan50 > $tmp_denda) { //jika nilai tagihan invoice > dari denda k3
                        $total_tagihan50 -= $tmp_denda;
                        $nilai_denda +=$tmp_denda;
                        // $this->insert($conn, $username, $tmp_denda, $no_doc, $no_invoice, $jumlah_denda);
                        if ($tmp_denda > 0) {
                            $sqlk31 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($tmp_denda, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
					";

                            $queryk31 = oci_parse($conn, $sqlk31);
                            $res = oci_execute($queryk31);
                        }
                    } else {
                        //jika melebihi 5-% OA maka akan otomatis mengambil nilai dari 50% OA tersebut
                        //Jika nilai denda melebihi 50% nilai invoice yang ditagihkan maka nilai denda sebesar 50% nilai invoice

                        $denda50 = $total_tagihan50;
                        // $sisa_denda = $tmp_denda - $denda50;
                        // $this->insert($conn, $username, $denda50, $no_doc, $no_invoice, $jumlah_denda);
                        if ($denda50 > 0) {
                            $sqlk32 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($denda50, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE,SYSDATE, '$user_name')
					";

                            $queryk32 = oci_parse($conn, $sqlk32);
                            $res = oci_execute($queryk32);
                        }


                        break;
                    }
                 }
                }
                //start  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1
                //Bundle POEX
                $dateNow = date('Y-m');
                $datepastMonth = date('Y-m', strtotime("-1 month"));
                $ketPOX = $totTransPOEX = $ketNiliPotongan = $saldoPOEX = '';
                $sqlCPOEX = "
                        SELECT
                        *
                FROM
                        (
                                SELECT
                                        TABLEA. ID,
                                        TABLEA. ORG,
                                        TABLEA. NAME,
                                        TABLEA.EKSPEDITUR,
                                        TABLEA.NUM,
                                        TABLEA.PERSEN,
                                        TABLEA.BA_NUMBER,
                                        TABLEA.JUMLAH,
                                        TABLEA.ACTIVE_DATE,
                                        TABLEB.TERPOTONG,
                                        TABLEA.CREATE_DATE,
                                        TABLEA.PRIORTS,
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) AS SISA
                                FROM
                                        (
                                                SELECT
                                                        ID,
                                                        ORG,
                                                        NAME,
                                                        NUM,
                                                        EKSPEDITUR,
                                                        BA_NUMBER,
                                                        PERSEN,
                                                        JUMLAH AS JUMLAH,
                                                        PRIORTS,
                                                        TO_CHAR (START_DATE, 'MM-YYYY') AS ACTIVE_DATE,
                                                        CREATE_DATE
                                                FROM
                                                        M_POTONGAN_OA
                                                WHERE
                                                        EKSPEDITUR = '$no_vendor_in'
                                                AND ORG = '$orgSpj'
                                                AND IS_DELETE = '0' --AND TO_CHAR (START_DATE, 'YYYY-MM') LIKE '2018-05'
                                                AND TO_CHAR (START_DATE, 'YYYY-MM') BETWEEN '$datepastMonth'
                                                AND '$dateNow'
                                                ORDER BY
                                                        PRIORTS ASC
                                        ) TABLEA
                                LEFT JOIN (
                                        SELECT
                                                NUM,
                                                NVL (SUM(NILAI_TRANSAKSI), 0) AS TERPOTONG
                                        FROM
                                                M_POTONGAN_OA_TRANS
                                        GROUP BY
                                                NUM
                                ) TABLEB ON TABLEA.NUM = TABLEB.NUM
                                WHERE
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) > 0
                                OR (
                                        TABLEA.JUMLAH - TABLEB.TERPOTONG
                                ) IS NULL
                                ORDER BY
                                        TABLEA.PRIORTS
                        )
                WHERE
                        SISA > 0
                OR SISA IS NULL
                AND ROWNUM = 1
                ";
                $queryCPOEX = oci_parse($conn, $sqlCPOEX);
                oci_execute($queryCPOEX);
                while ($rowkCPOEX = oci_fetch_array($queryCPOEX)) {
                    $persen = $rowkCPOEX['PERSEN'] / 100;
                    $totEksCost50 = $persen * $total_shp_in;
                    $numBA = $rowkCPOEX['BA_NUMBER'];
                    $nuM = $rowkCPOEX['NUM'];
                    $id = $rowkCPOEX['ID'];
                    if (is_null($rowkCPOEX['SISA']) && is_null($rowkCPOEX['TERPOTONG'])) {
                        $nilaiPOEX = $rowkCPOEX['JUMLAH'];
                    } else if ($rowkCPOEX['SISA'] > 0) {
                        $nilaiPOEX = $rowkCPOEX['SISA'];
                    } else {
                        $nilaiPOEX = '0';
                    }
                    if ($nilaiPOEX > '0' && $nilaiPOEX < $totEksCost50) {
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice1 bag darat',
                                                SYSDATE,
                                                '0',
                                                '$nilaiPOEX',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-1";
                            exit();
                        }
                    } else if ($nilaiPOEX > '0') {
                        $saldoPOEX = $nilaiPOEX - $totEksCost50;
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice2 bag darat',
                                                SYSDATE,
                                                '$saldoPOEX',
                                                '$totEksCost50',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'                       
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-2";
                            exit();
                        }
                        break;
                    }
                    $totEksCost50 = $totEksCost50 - $nilaiPOEX;
                }
                //end  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1

                $sql_9 = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE NO_TAGIHAN = '$inv_old' AND DELETE_MARK = '0' ";
                $query_9 = oci_parse($conn, $sql_9);
                oci_execute($query_9);
                $row_9 = oci_fetch_assoc($query_9);
                $data_9 = $row_9[HIT];

                if ($data_9 > 850) {

                    $field_names = array('PAJAK_N');
                    $field_data = array("N");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_TAGIHAN');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('PAJAK_N');
                    $field_data = array("Y");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                } else {
                    $field_names = array('NO_TAGIHAN');
                    $field_data = array("");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
              } 
            } else {
                $show_ket .= "Data No Pajak Tidak Valid cek no pajak anda pada transaksi sebelumnya..";
            }
        } else {
            $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
        }
       }else{
           $show_ket .= "Mohon maaf,jumlah spj maksimal per invoice harus 500";
       }


        $habis = "create_invoice_bag_darat.php";
        break;
//============================================================================================================================
    case "create_invoice_bag_laut75":
        $user_id = $_SESSION['user_id'];
        $orgSpj = $_POST['orgSPJ'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        $no_invoice_vendor_in = trim(strtoupper($_POST['no_invoice_vendor']));
        $tanggal_pjk = $_POST['tanggal_pjk'];
        if(strtotime($tanggal_pjk ) >= strtotime("2025-01-01")){   
        $no_pajak_vendor_in =$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5'];                      
        }elseif(strtotime($tanggal_pjk) == ''){
            $no_pajak_vendor_in =$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5']; 
        }else{ 
            $no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4'];
        }
        $no_vendor_in = $_POST['no_vendor'];
        $nama_vendor_in = $_POST['nama_vendor'];
        $nama_kapal = $_POST['nama_kapal'];
        $distributor = $_POST['distributor'];
        $tanggal_mulai = $_POST['tanggal_mulai'];
        $tanggal_selesai = $_POST['tanggal_selesai'];
        $no_shipment = $_POST['no_shipment'];
        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = $_REQUEST['nama_bank'];
        $cabang_bank = $_REQUEST['cabang_bank'];
        $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
        $bvtyp = $_REQUEST['bvtyp'];
        
        unset($orgchose);
        for ($kn = 0; $kn < $sampai; $kn++) {
            $idken = "idke" . $kn;
            $urutken = "urutke" . $kn;
            $orgkej = "orgke" . $kn;

            if (isset($_POST[$idken])) {
                $id_upn = $_POST[$idken];
                $orgkej = $_POST[$orgkej];
                $orgchose[$orgkej] = $orgkej;
            }
        }

        if (count($orgchose) == 1) {

            // cek unutk transaksi
            // tidak ada boleh no pajak yang sama
            $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
            $query_spt = oci_parse($conn, $sql_spt);
            oci_execute($query_spt);
            $row_spt = oci_fetch_assoc($query_spt);
            $data_spt = $row_spt[HIT];

            if ($row_spt[HIT] > 0) {
                $cek_data = false;
                $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
            }else
                $cek_data = true;

            if ($cek_data) {

                if ($nama_kapal == "" and $no_shipment == "" and $distributor == "" and $vendor == "" and $tipe_transaksi == "" and $tanggal_mulai == "" and $tanggal_selesai == "") {
                    $sql = "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND STATUS = 'DRAFT' AND STATUS2 = 'OPEN' AND KELOMPOK_TRANSAKSI = 'LAUT' AND TIPE_TRANSAKSI = 'BAG' ORDER BY NAMA_VENDOR,SOLD_TO,KODE_PRODUK,NO_SHP_TRN ASC";
                } else {
                    $pakeor = 0;
                    $sql = "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1 FROM EX_TRANS_HDR WHERE ";
                    if ($no_shipment != "") {
                        $sql.=" NO_SHP_TRN LIKE '$no_shipment' ";
                        $pakeor = 1;
                    }
                    if ($distributor != "") {
                        if ($pakeor == 1) {
                            $sql.=" AND NAMA_SOLD_TO LIKE '$distributor' ";
                        } else {
                            $sql.=" NAMA_SOLD_TO LIKE '$distributor' ";
                            $pakeor = 1;
                        }
                    }
                    if ($no_vendor_in != "") {
                        if ($pakeor == 1) {
                            $sql.=" AND ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$no_vendor_in' ) ";
                        } else {
                            $sql.=" ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$no_vendor_in' ) ";
                            $pakeor = 1;
                        }
                    }
                    if ($nama_kapal != "") {
                        if ($pakeor == 1) {
                            $sql.=" AND NAMA_KAPAL LIKE '$nama_kapal' ";
                        } else {
                            $sql.=" NAMA_KAPAL LIKE '$nama_kapal' ";
                            $pakeor = 1;
                        }
                    }
                    if ($tipe_transaksi != "") {
                        if ($pakeor == 1) {
                            $sql.=" AND TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
                        } else {
                            $sql.=" TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
                            $pakeor = 1;
                        }
                    }
                    if ($tanggal_mulai != "" or tanggal_selesai != "") {

                        if ($tanggal_mulai == "")
                            $tanggal_mulai_sql = "01-01-1990";
                        else
                            $tanggal_mulai_sql = $tanggal_mulai;
                        if ($tanggal_selesai == "")
                            $tanggal_selesai_sql = "12-12-9999";
                        else
                            $tanggal_selesai_sql = $tanggal_selesai;

                        if ($pakeor == 1) {
                            $sql.=" AND TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
                        } else {
                            $sql.=" TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";
                            $pakeor = 1;
                        }
                    }
                    $sql.=" AND DELETE_MARK = '0' AND STATUS = 'DRAFT' AND STATUS2 = 'OPEN' AND KELOMPOK_TRANSAKSI = 'LAUT' AND TIPE_TRANSAKSI = 'BAG' ORDER BY VENDOR, SOLD_TO, KODE_PRODUK, NO_SHP_TRN ASC";
                }
                //echo $sql;
                $query = oci_parse($conn, $sql);
                oci_execute($query);
                if ($no_invoice_in == "")
                    $no_invoice_in = $fungsi->new_invoice_number($conn);

                if (!$no_invoice_vendor_in) {
                    $no_invoice_vendor_in = $no_invoice_in;
                }
                while ($row = oci_fetch_array($query)) {

                    $id_up = $row[ID];
                    $shp_cost_v = $row[SHP_COST];
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TOTAL_KLAIM_ALL', 'STATUS2', 'QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK', 'HARGA_TEBUS', 'PDPKS', 'TOTAL_KLAIM_SEMEN', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR', 'NO_TAGIHAN');
                    $field_data = array("DRAFT", "SYSDATE", "$user_name", "0", "PARTIAL_INVOICED", '0', '0', '0', '0', '0', '0', '0', '0', '0', "SYSDATE", "SYSDATE", "$no_invoice_in");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "DRAFT", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
                for ($k = 0; $k < $sampai; $k++) {
                    $idke = "idke" . $k;
                    $urutke = "urutke" . $k;

                    if (isset($_POST[$idke])) {
                        $id_up = $_POST[$idke];

                        $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'STATUS2');
                        $field_data = array("$no_invoice_vendor_in", "$user_name", "$no_invoice_in", "PROGRESS", "SYSDATE", "SYSDATE", "$user_name", "$no_pajak_vendor_in", "PARTIAL_INVOICED");
                        $tablename = "EX_TRANS_HDR";
                        $field_id = array('ID', 'STATUS', 'STATUS2');
                        $value_id = array("$id_up", "DRAFT", "PARTIAL_INVOICED");
                        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }
                }


                $sqlcek = "SELECT ORG, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' GROUP BY ORG ";
                $querycek = oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data = oci_fetch_assoc($querycek);
                $total_klaim_in = $row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in = $row_data[TOTAL_KTG];
                $total_semen_in = $row_data[TOTAL_SEMEN];
                $total_pdpks_in = $row_data[TOTAL_PDPKS];
                $total_pdpkk_in = $row_data[TOTAL_PDPKK];
                $org_in = $row_data[ORG];

                // if (date("Ymd") >= ******** && date("Ymd")<20250101){
                //     $pajak_in = round($total_shp_in * 0.011, 0);
                // } elseif (date("Ymd") >= 20250101) {
                //     $pajak_in = round($total_shp_in * 0.012, 0);
                // } else {
                //     $pajak_in = round($total_shp_in * 0.1, 0);
                // }

                if (date("Ymd") >= ********){
                    $pajak_in = round($total_shp_in * 0.011, 0);
                } else {
                    $pajak_in = round($total_shp_in * 0.1, 0);
                }
                
                
                $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

                //liyantanto
                if ($no_vendor_in != '0000410082') {
                    $sql_pjaknew = "
                select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_in'
                group by NO_INVOICE)
                order by TGL_PAJAK_EX desc
                ) where rownum =1
        ";
                    $querycek = oci_parse($conn, $sql_pjaknew);
                    oci_execute($querycek);
                    $row_datap = oci_fetch_assoc($querycek);
                    unset($tglfakturpajak);
                    $tglfakturpajak = $row_datap[TGL_PAJAK_EXF];
                    if ($tglfakturpajak != '' && $tglfakturpajak >= '20140901') {
                        //tambahan pengujian pajak 11 persen 01-04-2022
                        // if (date("Ymd") >= ******** && date("Ymd")<20250101){
                        //     $pajak_in1 = round($total_shp_in * 0.11, 0);
                        //     $pajak_in = round($pajak_in1 * 0.11, 0);
                        // } elseif (date("Ymd")>=20250101) {
                        //     $pajak_in1 = round($total_shp_in * 0.12, 0);
                        //     $pajak_in = round($pajak_in1 * 0.12, 0);
                        // } else {
                        //     $pajak_in1 = round($total_shp_in * 0.1, 0);
                        //     $pajak_in = round($pajak_in1 * 0.1, 0);
                        // }

                        if (date("Ymd") >= ********){
                            $pajak_in1 = round($total_shp_in * 0.11, 0);
                            $pajak_in = round($pajak_in1 * 0.11, 0);
                        } else {
                            $pajak_in1 = round($total_shp_in * 0.1, 0);
                            $pajak_in = round($pajak_in1 * 0.1, 0);
                        }

                        $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                    }
                }

                $field_names = array('NO_INVOICE_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_INVOICE', 'TGL_INVOICE', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI');
                $field_data = array("$no_invoice_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "$no_invoice_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi");
                $tablename = "EX_INVOICE";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);
                $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
            } else {
                $show_ket .= "Data No Pajak Tidak Valid cek no pajak anda pada transaksi sebelumnya..";
            }
        } else {
            $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
        }
        
        //start  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1
                //Bundle POEX
                /*$dateNow = date('Y-m');
                $datepastMonth = date('Y-m', strtotime("-1 month"));
                $ketPOX = $totTransPOEX = $ketNiliPotongan = $saldoPOEX = '';
                $sqlCPOEX = "
                        SELECT
                        *
                FROM
                        (
                                SELECT
                                        TABLEA. ID,
                                        TABLEA. ORG,
                                        TABLEA. NAME,
                                        TABLEA.EKSPEDITUR,
                                        TABLEA.NUM,
                                        TABLEA.PERSEN,
                                        TABLEA.BA_NUMBER,
                                        TABLEA.JUMLAH,
                                        TABLEA.ACTIVE_DATE,
                                        TABLEB.TERPOTONG,
                                        TABLEA.CREATE_DATE,
                                        TABLEA.PRIORTS,
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) AS SISA
                                FROM
                                        (
                                                SELECT
                                                        ID,
                                                        ORG,
                                                        NAME,
                                                        NUM,
                                                        EKSPEDITUR,
                                                        BA_NUMBER,
                                                        PERSEN,
                                                        JUMLAH AS JUMLAH,
                                                        PRIORTS,
                                                        TO_CHAR (START_DATE, 'MM-YYYY') AS ACTIVE_DATE,
                                                        CREATE_DATE
                                                FROM
                                                        M_POTONGAN_OA
                                                WHERE
                                                        EKSPEDITUR = '$no_vendor_in'
                                                AND ORG = '$orgSpj'
                                                AND IS_DELETE = '0' --AND TO_CHAR (START_DATE, 'YYYY-MM') LIKE '2018-05'
                                                AND TO_CHAR (START_DATE, 'YYYY-MM') BETWEEN '$datepastMonth'
                                                AND '$dateNow'
                                                ORDER BY
                                                        PRIORTS ASC
                                        ) TABLEA
                                LEFT JOIN (
                                        SELECT
                                                NUM,
                                                NVL (SUM(NILAI_TRANSAKSI), 0) AS TERPOTONG
                                        FROM
                                                M_POTONGAN_OA_TRANS
                                        GROUP BY
                                                NUM
                                ) TABLEB ON TABLEA.NUM = TABLEB.NUM
                                WHERE
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) > 0
                                OR (
                                        TABLEA.JUMLAH - TABLEB.TERPOTONG
                                ) IS NULL
                                ORDER BY
                                        TABLEA.PRIORTS
                        )
                WHERE
                        SISA > 0
                OR SISA IS NULL
                AND ROWNUM = 1
                ";
                $queryCPOEX = oci_parse($conn, $sqlCPOEX);
                oci_execute($queryCPOEX);
                while ($rowkCPOEX = oci_fetch_array($queryCPOEX)) {
                    $persen = $rowkCPOEX['PERSEN'] / 100;
                    $totEksCost50 = $persen * $total_shp_in;
                    $numBA = $rowkCPOEX['BA_NUMBER'];
                    $nuM = $rowkCPOEX['NUM'];
                    $id = $rowkCPOEX['ID'];
                    if (is_null($rowkCPOEX['SISA']) && is_null($rowkCPOEX['TERPOTONG'])) {
                        $nilaiPOEX = $rowkCPOEX['JUMLAH'];
                    } else if ($rowkCPOEX['SISA'] > 0) {
                        $nilaiPOEX = $rowkCPOEX['SISA'];
                    } else {
                        $nilaiPOEX = '0';
                    }
                    if ($nilaiPOEX > '0' && $nilaiPOEX < $totEksCost50) {
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice1 bag laut 75',
                                                SYSDATE,
                                                '0',
                                                '$nilaiPOEX',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-1";
                            exit();
                        }
                    } else if ($nilaiPOEX > '0') {
                        $saldoPOEX = $nilaiPOEX - $totEksCost50;
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice2 bag laut 75',
                                                SYSDATE,
                                                '$saldoPOEX',
                                                '$totEksCost50',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'                       
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-2";
                            exit();
                        }
                        break;
                    }
                    $totEksCost50 = $totEksCost50 - $nilaiPOEX;
                } */ 
                //end  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1

        $habis = "create_invoice_bag_laut75.php";
        break;
//============================================================================================================================
   case "create_invoice_bag_laut":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        $orgSpj = $_POST['orgSPJ'];
        $no_invoice_vendor_in = $_POST['no_invoice_vendor'];
        $tanggal_pjk = $_POST['tanggal_pjk'];
        if(strtotime($tanggal_pjk ) >= strtotime("2025-01-01")){   
        $no_pajak_vendor_in =$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5'];                      
        }else{            
            $no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4']; 
        }
        $no_vendor_in = $_POST['no_vendor'];
        $nama_vendor_in = $_POST['nama_vendor'];
        $warna_plat_in = $_POST['warna_plat'];
        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = $_REQUEST['nama_bank'];
        $cabang_bank = $_REQUEST['cabang_bank'];
        $bvtyp = $_REQUEST['bvtyp'];
        $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
        
        unset($orgchose);
        for ($kn = 0; $kn < $sampai; $kn++) {
            $idken = "idke" . $kn;
            $urutken = "urutke" . $kn;
            $orgkej = "orgke" . $kn;

            if (isset($_POST[$idken])) {
                $id_upn = $_POST[$idken];
                $orgkej = $_POST[$orgkej];
                $orgchose[$orgkej] = $orgkej;
            }
        }

        if (count($orgchose) == 1) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;

                if (isset($_POST[$idke])) {
                    $id_up = $_POST[$idke];
                    if ($no_invoice_in == "")
                        $no_invoice_in = $fungsi->new_invoice_number($conn);
                    if (!$no_invoice_vendor_in) {
                        $no_invoice_vendor_in = $no_invoice_in;
                    }
                    $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'STATUS2');
                    $field_data = array("$no_invoice_vendor_in", "$user_name", "$no_invoice_in", "PROGRESS", "SYSDATE", "SYSDATE", "$user_name", "$no_pajak_vendor_in", "PARTIAL_INVOICED");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    //$value_id = array("$id_up", "OPEN", "PARTIAL_INVOICED");
                    $value_id = array("$id_up", "OPEN", "OPEN', 'PARTIAL_INVOICED");
                    $fungsi->update_inv($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    //echo "Update EX_TRANS_HDR by TRETENTEN HEUHEU";
                }
            }


            $sqlcek = "SELECT ORG, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' GROUP BY ORG";
            $querycek = oci_parse($conn, $sqlcek);
            oci_execute($querycek);
            $row_data = oci_fetch_assoc($querycek);
            $total_klaim_in = $row_data[TOTAL_KLAIM];
            $total_shp_in = $row_data[SHP_COST];
            $total_ktg_in = $row_data[TOTAL_KTG];
            $total_semen_in = $row_data[TOTAL_SEMEN];
            $total_pdpks_in = $row_data[TOTAL_PDPKS];
            $total_pdpkk_in = $row_data[TOTAL_PDPKK];
            $org_in = $row_data[ORG];

            //$pajak_in = round($total_shp_in * 0.1, 0);
            //tambahan pengujian pajak 11 persen 01-04-2022
            // if (date("Ymd") >= ******** && date("Ymd") < 20250101){
            //     $pajak_in = round($total_shp_in * 0.011, 0);
            // } elseif(date("Ymd") >= 20250101){
            //     $pajak_in = round($total_shp_in * 0.012, 0);
            // }else {
            //     $pajak_in = round($total_shp_in * 0.1, 0);
            // }  

            if (date("Ymd") >= ********){
                $pajak_in = round($total_shp_in * 0.011, 0);
            } else {
                $pajak_in = round($total_shp_in * 0.1, 0);
            }          

            $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

            //liyantanto
            if ($no_vendor_in != '0000410082') {
                $sql_pjaknew = "
                            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                            select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                            (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_in'
                            group by NO_INVOICE)
                            order by TGL_PAJAK_EX desc
                            ) where rownum =1
                            ";
                $querycek = oci_parse($conn, $sql_pjaknew);
                oci_execute($querycek);
                $row_datap = oci_fetch_assoc($querycek);
                unset($tglfakturpajak);
                $tglfakturpajak = $row_datap[TGL_PAJAK_EXF];
                if ($tglfakturpajak != '' && $tglfakturpajak >= '20140901') {
                                        //tambahan pengujian pajak 11 persen 01-04-2022
                    // if (date("Ymd") >= ******** && date("Ymd") < 20250101){
                    if (date("Ymd") >= ********){
                        if($no_vendor_in == '0000410003'){
                            $pajak_in1 = round($total_shp_in * 0.011, 0);
                            $pajak_in = round($pajak_in1 * 0.011, 0);
                        } else {
                            $pajak_in1 = round($total_shp_in * 0.11, 0);
                            $pajak_in = round($pajak_in1 * 0.11, 0);
                        }
                    }
                    //tambahan pengujian pajak 12 persen 01-01-2025
                    // elseif (date("Ymd") >= 20250101){
                    //     if($no_vendor_in == '0000410003'){
                    //         $pajak_in1 = round($total_shp_in * 0.012, 0);
                    //         $pajak_in = round($pajak_in1 * 0.012, 0);
                    //     } else {
                    //         $pajak_in1 = round($total_shp_in * 0.11, 0);
                    //         $pajak_in = round($pajak_in1 * 0.11, 0);
                    //     }
                    // } 
                    else {
                        $pajak_in1 = round($total_shp_in * 0.1, 0);
                        $pajak_in = round($pajak_in1 * 0.1, 0);
                    }
                    // $pajak_in1 = round($total_shp_in * 0.1, 0);
                    // $pajak_in = round($pajak_in1 * 0.1, 0);

                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                }
            }

            $field_names = array('NO_INVOICE_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_INVOICE', 'TGL_INVOICE', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'TGL_TERMIN', 'TERMIN', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$no_invoice_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "$no_invoice_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$tgl_tremin", "$termin", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi");
            $tablename = "EX_INVOICE";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
            $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
        } else {
            $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
        }

        $habis = "create_invoice_bag_laut.php";
        break;
//============================================================================================================================
    case "create_invoice_bag_laut_fob":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        $no_invoice_vendor_in = $_POST['no_invoice_vendor'];
        $tanggal_pjk = $_POST['tanggal_pjk'];

        if(strtotime($tanggal_pjk ) >= strtotime("2025-01-01")){   
			$no_pajak_vendor_in =$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5'];                      
		}else{ 
			$no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4'];
		}
        $no_vendor_in = $_POST['no_vendor'];
        $nama_vendor_in = $_POST['nama_vendor'];
        $warna_plat_in = $_POST['warna_plat'];
        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = $_REQUEST['nama_bank'];
        $cabang_bank = $_REQUEST['cabang_bank'];
        $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
        $bvtyp = $_REQUEST['bvtyp'];
        if ($warna_plat_in == "KUNING")
            $no_pajak_vendor_in = "";

        unset($orgchose);
        for ($kn = 0; $kn < $sampai; $kn++) {
            $idken = "idke" . $kn;
            $urutken = "urutke" . $kn;
            $orgkej = "orgke" . $kn;

            if (isset($_POST[$idken])) {
                $id_upn = $_POST[$idken];
                $orgkej = $_POST[$orgkej];
                $orgchose[$orgkej] = $orgkej;
            }
        }

        if (count($orgchose) == 1) {

            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;

                if (isset($_POST[$idke])) {
                    $id_up = $_POST[$idke];

                    if ($no_invoice_in == "")
                        $no_invoice_in = $fungsi->new_invoice_number($conn);

                    if (!$no_invoice_vendor_in) {
                        $no_invoice_vendor_in = $no_invoice_in;
                    }
                    $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'STATUS2');
                    $field_data = array("$no_invoice_vendor_in", "$user_name", "$no_invoice_in", "PROGRESS", "SYSDATE", "SYSDATE", "$user_name", "$no_pajak_vendor_in", "OPEN");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID', 'STATUS', 'STATUS2');
                    $value_id = array("$id_up", "OPEN", "OPEN");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
            }


            $sqlcek = "SELECT ORG, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' GROUP BY ORG ";
            $querycek = oci_parse($conn, $sqlcek);
            oci_execute($querycek);
            $row_data = oci_fetch_assoc($querycek);
            $total_klaim_in = $row_data[TOTAL_KLAIM];
            $total_shp_in = $row_data[SHP_COST];
            $total_ktg_in = $row_data[TOTAL_KTG];
            $total_semen_in = $row_data[TOTAL_SEMEN];
            $total_pdpks_in = $row_data[TOTAL_PDPKS];
            $total_pdpkk_in = $row_data[TOTAL_PDPKK];
            $org_in = $row_data[ORG];

            //tambahan pengujian pajak 11 persen 01-04-2022
            if ($warna_plat_in == "HITAM") {
                // if (date("Ymd") >= ******** && date("Ymd") < 20250101) {
                if (date("Ymd") >= ********) {
                    $pajak_in = round($total_shp_in * 0.011, 0);
                }
                //tambahan pengujian pajak 12 persen 01-01-2025
                // elseif(date("Ymd") >= 20250101) {
                //     $pajak_in = round($total_shp_in * 0.012, 0);   
                // }
                else {
                    $pajak_in = round($total_shp_in * 0.1, 0);
                }
            }

            $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

            //liyantanto
            if ($no_vendor_in != '0000410082') {
                $sql_pjaknew = "
                    select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                    select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                    (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_in'
                    group by NO_INVOICE)
                    order by TGL_PAJAK_EX desc
                    ) where rownum =1";
                $querycek = oci_parse($conn, $sql_pjaknew);
                oci_execute($querycek);
                $row_datap = oci_fetch_assoc($querycek);
                unset($tglfakturpajak);
                $tglfakturpajak = $row_datap[TGL_PAJAK_EXF];
                // if ($tglfakturpajak != '' && $tglfakturpajak >= '********' && $tglfakturpajak < '20250101' && $warna_plat_in == "HITAM") {
                //     $pajak_in1 = round($total_shp_in * 0.011, 0);
                //     $pajak_in = round($pajak_in1 * 0.011, 0);

                //     $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                // } elseif ($tglfakturpajak != '' && $tglfakturpajak >= '20250101' && $warna_plat_in == "HITAM") {
                //     $pajak_in1 = round($total_shp_in * 0.012, 0);
                //     $pajak_in = round($pajak_in1 * 0.012, 0);

                //     $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                // } elseif ($tglfakturpajak != '' && $tglfakturpajak < '********' && $warna_plat_in == "HITAM") {
                //     $pajak_in1 = round($total_shp_in * 0.1, 0);
                //     $pajak_in = round($pajak_in1 * 0.1, 0);

                //     $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                // }
                if ($tglfakturpajak != '' && $tglfakturpajak >= '********' && $warna_plat_in == "HITAM") {
                    $pajak_in1 = round($total_shp_in * 0.011, 0);
                    $pajak_in = round($pajak_in1 * 0.011, 0);

                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                } elseif ($tglfakturpajak != '' && $tglfakturpajak < '********' && $warna_plat_in == "HITAM") {
                    $pajak_in1 = round($total_shp_in * 0.1, 0);
                    $pajak_in = round($pajak_in1 * 0.1, 0);

                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                }
            }


            $field_names = array('NO_INVOICE_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_INVOICE', 'TGL_INVOICE', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$no_invoice_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "$no_invoice_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi");
            $tablename = "EX_INVOICE";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
            $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
        } else {
            $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
        }
        $habis = "create_invoice_bag_laut_fob.php";
        break;
//============================================================================================================================
    case "create_invoice_curah_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        $totChecked = $_POST['totalCheked'];
        $no_invoice_vendor_in = $_POST['no_invoice_vendor'];
        
        $tanggal_pjk = $_POST['tanggal_pjk'];
        if(strtotime($tanggal_pjk ) >= strtotime("2025-01-01")){   
            $no_pajak_vendor_in=$_POST['pjk1'].".".$_POST['pjk2'].".".$_POST['pjk3'].".".$_POST['pjk4']."-".$_POST['pjk5'];                      
        }else{            
            $no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4']; 
        }
        $pajak_bin = $no_pajak_vendor_in; //$_POST['pjk1'].".".$_POST['pjk2']."-".$_POST['pjk3'].".".substr($_POST['pjk4'],0,7)."*";
        $no_vendor_in = $_POST['no_vendor'];
        $nama_vendor_in = $_POST['nama_vendor'];
        $warna_plat_in = $_POST['warna_plat'];
        $bulan = $_POST['bulan'];
        $tahun = $_POST['tahun'];
        $orgSpj = $_POST['orgSPJ'];
        $spt_cek = $_POST['spt_cek'];
        $tanggal_pjk = $_POST['tanggal_pjk'];
        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = trim($_REQUEST['nama_bank']);
        $cabang_bank = trim($_REQUEST['cabang_bank']);
        $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
        $bvtyp = $_REQUEST['bvtyp'];
        $noBaF = $_POST['orgSPJ'].'.'.$_POST['termin'].'.'.(int)$no_vendor_in.'.'.time();
        if ($warna_plat_in == "KUNING")
            $no_pajak_vendor_in = "";
        unset($orgchose);
        
        if($totChecked < 501){
        for ($kn = 0; $kn < $sampai; $kn++) {
            $idken = "idke" . $kn;
            $urutken = "urutke" . $kn;
            $orgkej = "orgke" . $kn;

            if (isset($_POST[$idken])) {
                $id_upn = $_POST[$idken];
                $orgkej = $_POST[$orgkej];
                $orgchose[$orgkej] = $orgkej;
            }
        }

        if (count($orgchose) == 1) {
            
            // nomer Faktur Pajak sama
            // antara invoice 1 dan 2
            // cek unutk gabungan no pajak
            // satu bulan harus sama
            // beda bulan tidak boleh sama

            if ($spt_cek == "GABUNGAN") {
                $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY')='$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX <> '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                $query_spt = oci_parse($conn, $sql_spt);
                oci_execute($query_spt);
                $row_spt = oci_fetch_assoc($query_spt);
                $data_spt = $row_spt[HIT];
                if ($row_spt[HIT] > 0) {
                    $cek_data = false;
                    $show_ket .= " Pajak Anda Gabungan, harus sama untuk satu periode pajak.. <br>";
                }else
                    $cek_data = true;

                if ($cek_data) {
                    $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY') <> '$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                    $query_spt = oci_parse($conn, $sql_spt);
                    oci_execute($query_spt);
                    $row_spt = oci_fetch_assoc($query_spt);
                    $data_spt = $row_spt[HIT];

                    if ($row_spt[HIT] > 0) {
                        $cek_data = false;
                        $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
                    }else
                        $cek_data = true;
                }
            }else {
                // cek unutk transaksi
                // tidak ada boleh no pajak yang sama
                $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
                $query_spt = oci_parse($conn, $sql_spt);
                oci_execute($query_spt);
                $row_spt = oci_fetch_assoc($query_spt);
                $data_spt = $row_spt[HIT];

                if ($row_spt[HIT] > 0) {
                    $cek_data = false;
                    $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
                }else
                    $cek_data = true;
            }

            if ($warna_plat_in == "KUNING") {
                $cek_data = true;
                $show_ket = "";
            }
            if ($cek_data) {
                //Cek is already cretae invoice
                //$sqlCntInv = "SELECT count(*) AS JMLHINV from EX_INVOICE where DELETE_MARK ='0' AND ORG ='$orgSpj' AND NO_VENDOR = '$no_vendor_in' AND TGL_INVOICE BETWEEN trunc(sysdate) AND trunc(sysdate)+1  ORDER BY TGL_INVOICE DESC";
                $sqlCntInv ="
                    SELECT
                            COUNT(TBL1.NO_INVOICE) AS JMLHINV
                    FROM
                            (
                                    SELECT
                                            EXI.NO_INVOICE
                                    FROM
                                            EX_INVOICE EXI
                                    JOIN EX_TRANS_HDR ETH ON ETH.NO_INVOICE = EXI.NO_INVOICE
                                    WHERE
                                            EXI.DELETE_MARK = '0'
                                    AND EXI.ORG = '$orgSpj'
                                    AND EXI.NO_VENDOR = '$no_vendor_in'
                                    AND ETH.TANGGAL_INVOICE BETWEEN TRUNC (SYSDATE)
                                    AND TRUNC (SYSDATE) + 1
                                    AND ETH.WARNA_PLAT ='$warna_plat_in'
                                    GROUP BY
                                            EXI.NO_INVOICE
                            ) TBL1
                ";
                $queryCntInv = oci_parse($conn, $sqlCntInv);
                oci_execute($queryCntInv);
                $row_cnt = oci_fetch_assoc($queryCntInv);
                $jmlhInv = $row_cnt[JMLHINV];
                $t=date('d-m-Y');
                $cektglnow = (int)date("d",strtotime($t));
                if(($jmlhInv > 100)){
                    $show_ket = "Unable create invoice (Reach Limit Day)";
                }else{
                    $data_900 = 0;
                for ($k = 0; $k < $sampai; $k++) {
                    $idke = "idke" . $k;
                    $urutke = "urutke" . $k;

                    if (isset($_POST[$idke])) {
                        $id_up = $_POST[$idke];

                        if ($no_invoice_in == "") {
                            $no_invoice_in = $fungsi->new_invoice_number($conn);
                            $inv_old = $no_invoice_in;
                            $no_inv_data_900[] = $no_invoice_in;
                        }
                        if ($data_900 == 850) {
                            $no_invoice_in = $fungsi->new_invoice_number($conn);
                            //$no_pajak_vendor_in=$$no_pajak_vendor_in;
                            $no_inv_data_900[] = $no_invoice_in;
                            $data_900 = 0;
                        }
                        if ($no_invoice_in != $inv_old)
                            $no_pajak_vendor_in_sql = $pajak_bin;
                        else
                            $no_pajak_vendor_in_sql = $no_pajak_vendor_in;


                        if (!$no_invoice_vendor_in) {
                            $no_invoice_vendor_in = $no_invoice_in;
                        }

                        $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'STATUS2', 'NO_TAGIHAN');
                        $field_data = array("$no_invoice_vendor_in", "$user_name", "$no_invoice_in", "PROGRESS", "SYSDATE", "SYSDATE", "$user_name", "$no_pajak_vendor_in", "OPEN", "$inv_old");
                        $tablename = "EX_TRANS_HDR";
                        $field_id = array('ID');
                        $value_id = array("$id_up");
                        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                        $data_900+=1;

                        #4/14/2011
                        # cek transaksi curah
//				$cek_pdks = false;
//				$sql_critcurah= "SELECT COUNT(*) AS ADA FROM EX_TRANS_HDR WHERE ID=$id_up AND STATUS='PROGRESS' AND STATUS2='OPEN' AND DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' AND TIPE_TRANSAKSI='CURAH' AND SHIP_TO='**********'";
//				$query_critcurah= @oci_parse($conn, $sql_critcurah);
//				@oci_execute($query_critcurah);
//				$row_tcurah=@oci_fetch_assoc($query_critcurah);
//				$nt_curah = $row_tcurah[ADA];	
//				if ($nt_curah > 0) $cek_pdks = true;
//
//				# change klaim semen ke pdpks	
//				if($cek_pdks){
//			
//					#ambil data TOTAL_SEMEN_RUSAK, PDPKS, KLAIM_LEBIH, PDPKS_LEBIH
//					$sql_gettcurah= "SELECT TOTAL_SEMEN_RUSAK, PDPKS, KLAIM_LEBIH, PDPKS_LEBIH, TOTAL_KLAIM_ALL FROM EX_TRANS_HDR WHERE ID=$id_up AND STATUS='PROGRESS' AND STATUS2='OPEN' AND DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' AND TIPE_TRANSAKSI='CURAH' AND SHIP_TO='**********'";
//					$query_critcurah= oci_parse($conn, $sql_gettcurah);
//					oci_execute($query_critcurah);
//					$row_datat=oci_fetch_array($query_critcurah);
//					//echo "NILAI : ".
//					$dtl_pdpks = $row_datat[TOTAL_KLAIM_ALL];	
//					$field_names=array('NO_INV_VENDOR','INVOICED_BY','NO_INVOICE','TANGGAL_INVOICE','LAST_UPDATE_DATE','LAST_UPDATED_BY','NO_PAJAK_EX','PDPKS'); //,'TOTAL_SEMEN_RUSAK','KLAIM_LEBIH'
//					$field_data=array("$no_invoice_vendor_in","$user_name","$no_invoice_in","SYSDATE","SYSDATE","$user_name","$no_pajak_vendor_in","$dtl_pdpks"); //,"0","0"
//					$tablename="EX_TRANS_HDR";
//					$field_id=array('ID','STATUS','STATUS2');
//					$value_id=array("$id_up","PROGRESS","OPEN");
//					$fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
//				}
                    }
                }

                for ($in = 0; $in < count($no_inv_data_900); $in++) {
                    $no_invoice_in = $no_inv_data_900[$in];
                    $sqlcek = "SELECT ORG, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' GROUP BY ORG ";
                    $querycek = oci_parse($conn, $sqlcek);
                    oci_execute($querycek);
                    $row_data = oci_fetch_assoc($querycek);
                    $total_klaim_in = $row_data[TOTAL_KLAIM];
                    $total_shp_in = $row_data[SHP_COST];
                    $total_ktg_in = $row_data[TOTAL_KTG];
                    $total_semen_in = $row_data[TOTAL_SEMEN];
                    $total_pdpks_in = $row_data[TOTAL_PDPKS];
                    $total_pdpkk_in = $row_data[TOTAL_PDPKK];
                    $org_in = $row_data[ORG];
                    //pengujian pajak ppn 11%
                    if ($warna_plat_in == "HITAM")
                    if (date("Ymd") >= ********){
                        $pajak_in = round($total_shp_in * 0.11, 0);
                    }else{
                        $pajak_in = round($total_shp_in * 0.1, 0);
                    }
                    else
                        $pajak_in = 0;

                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

                    $field_names = array('NO_INVOICE_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_INVOICE', 'TGL_INVOICE', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'TGL_TERMIN', 'TERMIN', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI','NO_BAF');

                    $field_data = array("$no_invoice_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "$no_invoice_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$tgl_tremin", "$termin", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi","$noBaF");
                    $tablename = "EX_INVOICE";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);


                    $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
                }
                
                //Chek org spj
               /* $sqlOrgSpj = "SELECT ORG FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND DELETE_MARK ='0' AND NO_INVOICE='$no_invoice_in' AND ROWNUM = 1";
                $queryOrgSpj = oci_parse($conn, $sqlOrgSpj);
                oci_execute($queryOrgSpj);
                $result = oci_fetch_array($queryOrgSpj);
                $orgSpj = $result['ORG'];
                
                if($orgSpj != '5000'){
                    // $dendak3->pemotongan_dendak3($conn, $no_invoice_in, $no_vendor_in, $total_tagihan, $user_name);

                    $total_tagihan50 = floatval($total_shp_in) * 0.5;
                    //SQL GET DENDA YG SUDAH TERPOTONG
                    $sql = "SELECT
                                            TABLEA.ID_VENDOR,
                                            TABLEA.NO_DOKUMEN,
                                            TABLEB.TERPOTONG,
                                            TABLEA.JUMLAH,
                                            (
                                                    TABLEA.JUMLAH - TABLEB.TERPOTONG
                                            ) AS SISA
                                    FROM
                                            (
                                                    SELECT
                                                            ID_VENDOR,
                                                            NO_DOKUMEN,
                                                            TOTAL_DENDA AS JUMLAH
                                                    FROM
                                                            EX_DENDAK3_DOC
                                                    WHERE
                                                            ID_VENDOR = '$no_vendor_in'
                                            ) TABLEA
                                    LEFT JOIN (
                                            SELECT
                                                    NO_DOC_DENDA AS NO_DOKUMEN,
                                                    NVL (SUM(KREDIT), 0) AS TERPOTONG
                                            FROM
                                                    EX_DENDAK3_SALDO
                                            GROUP BY
                                                    NO_DOC_DENDA
                                    ) TABLEB ON TABLEA.NO_DOKUMEN = TABLEB.NO_DOKUMEN";

                    $queryk3 = oci_parse($conn, $sql);
                    $raw = oci_execute($queryk3);

                    $affected_doc = array();
                    $nilai_denda = 0; //nilai denda yang akan dipotongkan
                    $nilai_saldo = 0;

                    while ($row = oci_fetch_array($queryk3)) {
                        $tmp_denda = 0;
                        //jika ada sisa dari tansaksi sbelumnya akan mengambil kolom sisa
                        if ($row[SISA] == null) {
                            $tmp_denda = floatval($row[JUMLAH]);
                        } else {
                            $tmp_denda = floatval($row[SISA]);
                        }
                        // print_r($row);
                        $jumlah_denda = floatval($row[JUMLAH]);
                        $no_doc = $row[NO_DOKUMEN];
                        //jika dibawah 50% oa maka akan langsung di potongkan
                        // $show_ket .= "$total_tagihan50 .<<< $no_doc    $tmp_denda  -> ";

                        if ($total_tagihan50 > $tmp_denda) {
                            $total_tagihan50 -= $tmp_denda;
                            $nilai_denda +=$tmp_denda;
                            // $this->insert($conn, $username, $tmp_denda, $no_doc, $no_invoice, $jumlah_denda);
                            if ($tmp_denda > 0) {
                                $sqlk31 = "
                                                    INSERT INTO EX_DENDAK3_SALDO 
                                                    (KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
                                                    VALUES 
                                                    ($tmp_denda, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
                                            ";

                                $queryk31 = oci_parse($conn, $sqlk31);
                                $res = oci_execute($queryk31);
                            }
                        } else {
                            //jika melebihi 5-% OA maka akan otomatis mengambil nilai dari 50% OA tersebut

                            $denda50 = $total_tagihan50;
                            // $sisa_denda = $tmp_denda - $denda50;
                            // $this->insert($conn, $username, $denda50, $no_doc, $no_invoice, $jumlah_denda);
                            if ($denda50 > 0) {
                                $sqlk32 = "
                                                    INSERT INTO EX_DENDAK3_SALDO 
                                                    (KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
                                                    VALUES 
                                                    ($denda50, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
                                            ";

                                $queryk32 = oci_parse($conn, $sqlk32);
                                $res = oci_execute($queryk32);
                            }


                            break;
                        }
                    }
                }*/
                
                // $dendak3->pemotongan_dendak3($conn, $no_invoice_in, $no_vendor_in, $total_tagihan, $user_name);

                $total_tagihan50 = floatval($total_shp_in) * 0.5;
                //SQL GET DENDA YG SUDAH TERPOTONG
                if($orgSpj == '7000'){
                    $sql = "SELECT
					TABLEA.ID_VENDOR,
					TABLEA.NO_DOKUMEN,
					TABLEB.TERPOTONG,
                                        TABLEA.COMPANY_CODE,
					TABLEA.JUMLAH,
					(
						TABLEA.JUMLAH - TABLEB.TERPOTONG
					) AS SISA
				FROM
					(
						SELECT
							ID_VENDOR,
							NO_DOKUMEN,
                                                        COMPANY_CODE,
							TOTAL_DENDA AS JUMLAH
						FROM
							EX_DENDAK3_DOC
						WHERE
							ID_VENDOR = '$no_vendor_in'
					) TABLEA
				LEFT JOIN (
					SELECT
						NO_DOC_DENDA AS NO_DOKUMEN,
						NVL (SUM(KREDIT), 0) AS TERPOTONG
					FROM
						EX_DENDAK3_SALDO
					GROUP BY
						NO_DOC_DENDA
				) TABLEB ON TABLEA.NO_DOKUMEN = TABLEB.NO_DOKUMEN";

                $queryk3 = oci_parse($conn, $sql);
                $raw = oci_execute($queryk3);

                $affected_doc = array();
                $nilai_denda = 0; //nilai denda yang akan dipotongkan
                $nilai_saldo = 0;

                while ($row = oci_fetch_array($queryk3)) {
                    $tmp_denda = 0;
                    //jika ada sisa dari tansaksi sbelumnya akan mengambil kolom sisa
                    if ($row[SISA] == null) {
                        $tmp_denda = floatval($row[JUMLAH]);
                    } else {
                        $tmp_denda = floatval($row[SISA]);
                    }
                    // print_r($row);
                    $jumlah_denda = floatval($row[JUMLAH]);
                    $no_doc = $row[NO_DOKUMEN];
                    //jika dibawah 50% oa maka akan langsung di potongkan
                    // $show_ket .= "$total_tagihan50 .<<< $no_doc    $tmp_denda  -> ";

                    if ($total_tagihan50 > $tmp_denda) {
                        $total_tagihan50 -= $tmp_denda;
                        $nilai_denda +=$tmp_denda;
                        // $this->insert($conn, $username, $tmp_denda, $no_doc, $no_invoice, $jumlah_denda);
                        if ($tmp_denda > 0) {
                            $sqlk31 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($tmp_denda, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
					";

                            $queryk31 = oci_parse($conn, $sqlk31);
                            $res = oci_execute($queryk31);
                        }
                    } else {
                        //jika melebihi 5-% OA maka akan otomatis mengambil nilai dari 50% OA tersebut

                        $denda50 = $total_tagihan50;
                        // $sisa_denda = $tmp_denda - $denda50;
                        // $this->insert($conn, $username, $denda50, $no_doc, $no_invoice, $jumlah_denda);
                        if ($denda50 > 0) {
                            $sqlk32 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($denda50, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
					";

                            $queryk32 = oci_parse($conn, $sqlk32);
                            $res = oci_execute($queryk32);
                        }


                        break;
                    }
                  }
                }
                
                if($orgSpj == '5000'){
                    $sql = "SELECT
					TABLEA.ID_VENDOR,
					TABLEA.NO_DOKUMEN,
					TABLEB.TERPOTONG,
                                        TABLEA.COMPANY_CODE,
					TABLEA.JUMLAH,
					(
						TABLEA.JUMLAH - TABLEB.TERPOTONG
					) AS SISA
				FROM
					(
						SELECT
							ID_VENDOR,
							NO_DOKUMEN,
                                                        COMPANY_CODE,
							TOTAL_DENDA AS JUMLAH
						FROM
							EX_DENDAK3_DOC
						WHERE
							ID_VENDOR = '$no_vendor_in'
					) TABLEA
				LEFT JOIN (
					SELECT
						NO_DOC_DENDA AS NO_DOKUMEN,
						NVL (SUM(KREDIT), 0) AS TERPOTONG
					FROM
						EX_DENDAK3_SALDO
					GROUP BY
						NO_DOC_DENDA
				) TABLEB ON TABLEA.NO_DOKUMEN = TABLEB.NO_DOKUMEN";

                $queryk3 = oci_parse($conn, $sql);
                $raw = oci_execute($queryk3);

                $affected_doc = array();
                $nilai_denda = 0; //nilai denda yang akan dipotongkan
                $nilai_saldo = 0;

                while ($row = oci_fetch_array($queryk3)) {
                    $tmp_denda = 0;
                    //jika ada sisa dari tansaksi sbelumnya akan mengambil kolom sisa
                    if ($row[SISA] == null) {
                        $tmp_denda = floatval($row[JUMLAH]);
                    } else {
                        $tmp_denda = floatval($row[SISA]);
                    }
                    // print_r($row);
                    $jumlah_denda = floatval($row[JUMLAH]);
                    $no_doc = $row[NO_DOKUMEN];
                    //jika dibawah 50% oa maka akan langsung di potongkan
                    // $show_ket .= "$total_tagihan50 .<<< $no_doc    $tmp_denda  -> ";

                    if ($total_tagihan50 > $tmp_denda) {
                        $total_tagihan50 -= $tmp_denda;
                        $nilai_denda +=$tmp_denda;
                        // $this->insert($conn, $username, $tmp_denda, $no_doc, $no_invoice, $jumlah_denda);
                        if ($tmp_denda > 0) {
                            $sqlk31 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($tmp_denda, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
					";

                            $queryk31 = oci_parse($conn, $sqlk31);
                            $res = oci_execute($queryk31);
                        }
                    } else {
                        //jika melebihi 5-% OA maka akan otomatis mengambil nilai dari 50% OA tersebut

                        $denda50 = $total_tagihan50;
                        // $sisa_denda = $tmp_denda - $denda50;
                        // $this->insert($conn, $username, $denda50, $no_doc, $no_invoice, $jumlah_denda);
                        if ($denda50 > 0) {
                            $sqlk32 = "
						INSERT INTO EX_DENDAK3_SALDO 
						(KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
						VALUES 
						($denda50, $jumlah_denda, '$no_doc', '$no_invoice_in', SYSDATE, SYSDATE, '$user_name')
					";

                            $queryk32 = oci_parse($conn, $sqlk32);
                            $res = oci_execute($queryk32);
                        }


                        break;
                    }
                  }
                }
                
                //start !!!!!!!!!!!!!!!!!
                $dateNow = date('Y-m');
                $datepastMonth = date('Y-m', strtotime("-1 month"));
                $ketPOX = $totTransPOEX = $ketNiliPotongan = $saldoPOEX = '';
                $sqlCPOEX = "
                        SELECT
                        *
                FROM
                        (
                                SELECT
                                        TABLEA. ID,
                                        TABLEA. ORG,
                                        TABLEA. NAME,
                                        TABLEA.EKSPEDITUR,
                                        TABLEA.NUM,
                                        TABLEA.PERSEN,
                                        TABLEA.BA_NUMBER,
                                        TABLEA.JUMLAH,
                                        TABLEA.ACTIVE_DATE,
                                        TABLEB.TERPOTONG,
                                        TABLEA.CREATE_DATE,
                                        TABLEA.PRIORTS,
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) AS SISA
                                FROM
                                        (
                                                SELECT
                                                        ID,
                                                        ORG,
                                                        NAME,
                                                        NUM,
                                                        EKSPEDITUR,
                                                        BA_NUMBER,
                                                        PERSEN,
                                                        JUMLAH AS JUMLAH,
                                                        PRIORTS,
                                                        TO_CHAR (START_DATE, 'MM-YYYY') AS ACTIVE_DATE,
                                                        CREATE_DATE
                                                FROM
                                                        M_POTONGAN_OA
                                                WHERE
                                                        EKSPEDITUR = '$no_vendor_in'
                                                AND ORG = '$orgSpj'
                                                AND IS_DELETE = '0' --AND TO_CHAR (START_DATE, 'YYYY-MM') LIKE '2018-05'
                                                AND TO_CHAR (START_DATE, 'YYYY-MM') BETWEEN '$datepastMonth'
                                                AND '$dateNow'
                                                ORDER BY
                                                        PRIORTS ASC
                                        ) TABLEA
                                LEFT JOIN (
                                        SELECT
                                                NUM,
                                                NVL (SUM(NILAI_TRANSAKSI), 0) AS TERPOTONG
                                        FROM
                                                M_POTONGAN_OA_TRANS
                                        GROUP BY
                                                NUM
                                ) TABLEB ON TABLEA.NUM = TABLEB.NUM
                                WHERE
                                        (
                                                TABLEA.JUMLAH - TABLEB.TERPOTONG
                                        ) > 0
                                OR (
                                        TABLEA.JUMLAH - TABLEB.TERPOTONG
                                ) IS NULL
                                ORDER BY
                                        TABLEA.PRIORTS
                        )
                WHERE
                        SISA > 0
                OR SISA IS NULL
                AND ROWNUM = 1
                ";
                $queryCPOEX = oci_parse($conn, $sqlCPOEX);
                oci_execute($queryCPOEX);
                while ($rowkCPOEX = oci_fetch_array($queryCPOEX)) {
                    $persen = $rowkCPOEX['PERSEN'] / 100;
                    $totEksCost50 = $persen * $total_shp_in;
                    //$totEksCost50 = $rowkCPOEX['PERSEN']*$total_shp_in;
                    //echo json_encode($rowkCPOEX);
                    $numBA = $rowkCPOEX['BA_NUMBER'];
                    $nuM = $rowkCPOEX['NUM'];
                    $id = $rowkCPOEX['ID'];
                    if (is_null($rowkCPOEX['SISA']) && is_null($rowkCPOEX['TERPOTONG'])) {
                        $nilaiPOEX = $rowkCPOEX['JUMLAH'];
                    } else if ($rowkCPOEX['SISA'] > 0) {
                        $nilaiPOEX = $rowkCPOEX['SISA'];
                    } else {
                        $nilaiPOEX = '0';
                    }
                    if ($nilaiPOEX > '0' && $nilaiPOEX < $totEksCost50) {
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice1 curah darat',
                                                SYSDATE,
                                                '0',
                                                '$nilaiPOEX',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-1";
                            exit();
                        }
                    } else if ($nilaiPOEX > '0') {
                        $saldoPOEX = $nilaiPOEX - $totEksCost50;
                        $sqlIPOEX = "
                                INSERT INTO M_POTONGAN_OA_TRANS
                                VALUES
                                        (
                                                sequence_um_master.nextval,
                                                '$numBA',
                                                '$no_invoice_in',
                                                'create_invoice2 curah darat',
                                                SYSDATE,
                                                '$saldoPOEX',
                                                '$totEksCost50',
                                                '0',
                                                SYSDATE,
                                                '$user_name',
                                                '',
                                                '',
                                                '',
                                                '$nuM'                       
                                        )";
                        $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
                        $insertPOEXTrans = oci_execute($querryIPOEXT);
                        if (!$insertPOEXTrans) {
                            $error = oci_error($querryIPOEXT);
                            echo "Insert MPOEXT failed-2";
                            exit();
                        }
                        break;
                    }
                    $totEksCost50 = $totEksCost50 - $nilaiPOEX;
                }
                //end !!!!!!!!!!!!!!!!!

                $sql_9 = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE NO_TAGIHAN = '$inv_old' AND DELETE_MARK = '0' ";
                $query_9 = oci_parse($conn, $sql_9);
                oci_execute($query_9);
                $row_9 = oci_fetch_assoc($query_9);
                $data_9 = $row_9[HIT];

                if ($row_9[HIT] > 850) {

                    $field_names = array('PAJAK_N');
                    $field_data = array("N");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_TAGIHAN');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('PAJAK_N');
                    $field_data = array("Y");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                } else {
                    $field_names = array('NO_TAGIHAN');
                    $field_data = array("");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$inv_old");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
               }   
            } else {
                $show_ket .= "Data No Pajak Tidak Valid cek no pajak anda pada transaksi sebelumnya..";
            }
        } else {
            $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
        }
      }else{
          $show_ket .= "Mohon maaf,jumlah spj maksimal per invoice harus 500";
      }

        $habis = "create_invoice_curah_darat.php";
        break;
//============================================================================================================================
    case "create_invoice_curah_laut":
        /* 	$user_id=$_SESSION['user_id'];
          $user_name=$_SESSION['user_name'];
          $sampai=$_POST['total'];
          $no_invoice_vendor_in=$_POST['no_invoice_vendor'];
          $no_pajak_vendor_in=$_POST['no_pajak_vendor'];
          $no_vendor_in=$_POST['no_vendor'];
          $nama_vendor_in=$_POST['nama_vendor'];
          $warna_plat_in=$_POST['warna_plat'];
          for($k=0;$k<$sampai;$k++){
          $idke="idke".$k;
          $urutke="urutke".$k;

          if(isset($_POST[$idke])){
          $id_up=$_POST[$idke];

          if($no_invoice_in=="")$no_invoice_in = $fungsi->new_invoice_number($conn);
          $field_names=array('NO_INV_VENDOR','INVOICED_BY','NO_INVOICE','STATUS','TANGGAL_INVOICE','LAST_UPDATE_DATE','LAST_UPDATED_BY','NO_PAJAK_EX','STATUS2');
          $field_data=array("$no_invoice_vendor_in","$user_name","$no_invoice_in","PROGRESS","SYSDATE","SYSDATE","$user_name","$no_pajak_vendor_in","INVOICED");
          $tablename="EX_TRANS_HDR";
          $field_id=array('ID');
          $value_id=array("$id_up");
          $fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
          $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";
          }
          }

          $sqlcek="SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' ";
          $querycek= oci_parse($conn, $sqlcek);
          oci_execute($querycek);
          $row=oci_fetch_assoc($querycek);
          $total_inv_in=$row[TOTAL_KLAIM];
          $total_ktg_in=$row[TOTAL_KTG];
          $total_semen_in=$row[TOTAL_SEMEN];
          $total_pdpks_in=$row[TOTAL_PDPKS];
          $total_pdpkk_in=$row[TOTAL_PDPKK];

          $pajak_in = round($total_inv_in * 0.1,0);

          $field_names=array('NO_INVOICE_EX','NO_PAJAK_EX','TOTAL_INV','PAJAK_INV','NO_VENDOR','NAMA_VENDOR','KLAIM_KTG','KLAIM_SEMEN','PDPKS','NO_INVOICE','TGL_INVOICE','PDPKK','DELETE_MARK','LAST_UPDATE_DATE','LAST_UPDATED_BY');
          //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
          $field_data=array("$no_invoice_vendor_in","$no_pajak_vendor_in","$total_inv_in","$pajak_in","$no_vendor_in","$nama_vendor_in","$total_ktg_in","$total_semen_in","$total_pdpks_in","$no_invoice_in","SYSDATE","$total_pdpkk_in","0","SYSDATE","$user_name");
          $tablename="EX_INVOICE";
          $fungsi->insert($conn,$field_names,$field_data,$tablename);
          $show_ket .= "Invoice Sukses Di Buat Dengan No $no_invoice_in <br>";

          $habis = "create_invoice_curah_laut.php";
         */ break;
//============================================================================================================================
    case "cancel_invoice_bag_darat":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $warna_plat_in = $_POST['warna_plat'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;

            if (isset($_POST[$idke])) {
                $id_up = $_POST[$idke];

                $sql_inv = "SELECT NO_INVOICE, NO_SHP_TRN FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND ID = '$id_up' ";
                $query_inv = oci_parse($conn, $sql_inv);
                oci_execute($query_inv);
                $row_inv = oci_fetch_assoc($query_inv);
                $no_invoice_in = $row_inv[NO_INVOICE];
                $no_shipment_in = $row_inv[NO_SHP_TRN];

                $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'STATUS2', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'PAJAK_N', 'NO_TAGIHAN');
                $field_data = array("", "", "", "OPEN", "OPEN", "", "SYSDATE", "$user_name", "", "", "");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('ID');
                $value_id = array("$id_up");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                ///DENDA K3 part

                $tablename = "EX_DENDAK3_SALDO";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_in");
                $fungsi->delete($conn, $tablename, $field_id, $value_id);

                //start !!!!!!!!!!!!!!
                //POEX
                $tablename="M_POTONGAN_OA_TRANS";
		$field_id=array('NO_INVOICE');
		$value_id=array("$no_invoice_in");
		$fungsi->delete($conn,$tablename,$field_id,$value_id);
                
                //end !!!!!!!!!!!!!!

                $sqlcek = "SELECT SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' ";
                $querycek = oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data = oci_fetch_assoc($querycek);
                $total_klaim_in = $row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in = $row_data[TOTAL_KTG];
                $total_semen_in = $row_data[TOTAL_SEMEN];
                $total_pdpks_in = $row_data[TOTAL_PDPKS];
                $total_pdpkk_in = $row_data[TOTAL_PDPKK];

                if ($warna_plat_in == "HITAM")
                    $pajak_in = round($total_shp_in * 0.1, 0);
                else
                    $pajak_in = 0;

                $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

                $field_names = array('TOTAL_INV', 'PAJAK_INV', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'TGL_INVOICE', 'PDPKK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TOTAL_INVOICE', 'TGL_TERMIN', 'TERMIN');
                $field_data = array("$total_shp_in", "$pajak_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "SYSDATE", "$total_pdpkk_in", "SYSDATE", "$user_name", "$total_tagihan", "", "");
                $tablename = "EX_INVOICE";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_in");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                if ($total_shp_in == 0) {
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$no_invoice_in");
                    $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                    $field_data = array("1", "SYSDATE", "$user_name");
                    $tablename = "EX_INVOICE";
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
                $show_ket .= "Invoice NO SPJ $no_shipment_in Dengan NO INVOICE $no_invoice_in Berhasil di cancel<br>";
            }
        }

        $habis = "proses_invoice_bag_darat.php";
        break;
//============================================================================================================================
case "cancel_invoice_bag_laut":
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['user_name'];
    $sampai = $_POST['total'];
    echo $sampai;
    for ($k = 0; $k < $sampai; $k++) {
        $idke = 6;

        // if (isset($_POST[$idke])) {
            $id_up = '6';

            $sql_inv = "SELECT NO_INVOICE, NO_SHP_TRN FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND ID = '$id_up' ";
            $query_inv = oci_parse($conn, $sql_inv);
            oci_execute($query_inv);
            $row = oci_fetch_assoc($query_inv);
            $no_invoice_in = $row[NO_INVOICE];
            $no_shipment_in = $row[NO_SHP_TRN];

            echo $sql_inv;

            $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'PAJAK_N');
            $field_data = array("", "", "", "OPEN", "", "SYSDATE", "$user_name", "", "");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('ID');
            $value_id = array("$id_up");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            
            //start !!!!!!!!!!!!!!
            //POEX
            /*$tablename="M_POTONGAN_OA_TRANS";
    $field_id=array('NO_INVOICE');
    $value_id=array("$no_invoice_in");
    $fungsi->delete($conn,$tablename,$field_id,$value_id);*/
            //end !!!!!!!!!!!!!!

            $sqlcek = "SELECT VENDOR,SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in'  group by VENDOR ";
            $querycek = oci_parse($conn, $sqlcek);
            oci_execute($querycek);
            $row_data = oci_fetch_assoc($querycek);
            $total_klaim_in = $row_data[TOTAL_KLAIM];
            $total_shp_in = $row_data[SHP_COST];
            $total_ktg_in = $row_data[TOTAL_KTG];
            $total_semen_in = $row_data[TOTAL_SEMEN];
            $total_pdpks_in = $row_data[TOTAL_PDPKS];
            $total_pdpkk_in = $row_data[TOTAL_PDPKK];
            $no_vendor_inx = $row_data[VENDOR];

            $pajak_in = round($total_shp_in * 0.1, 0);
            $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
            //liyantanto
            if ($no_vendor_inx != '0000410082') {
                $sql_pjaknew = "
                        select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                        select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                        (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_in'
                        group by NO_INVOICE)
                        order by TGL_PAJAK_EX desc
                        ) where rownum =1";
                $querycek = oci_parse($conn, $sql_pjaknew);
                oci_execute($querycek);
                $row_datap = oci_fetch_assoc($querycek);
                unset($tglfakturpajak);
                $tglfakturpajak = $row_datap[TGL_PAJAK_EXF];
                if ($tglfakturpajak != '' && $tglfakturpajak >= '20140901') {
                    $pajak_in1 = round($total_shp_in * 0.1, 0);
                    $pajak_in = round($pajak_in1 * 0.1, 0);

                    $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                }
            }

            $field_names = array('TOTAL_INV', 'PAJAK_INV', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'TGL_INVOICE', 'PDPKK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TOTAL_INVOICE');
            $field_data = array("$total_shp_in", "$pajak_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "SYSDATE", "$total_pdpkk_in", "SYSDATE", "$user_name", "$total_tagihan");
            $tablename = "EX_INVOICE";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_in");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            if ($total_shp_in == 0) {
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_in");
                $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("1", "SYSDATE", "$user_name");
                $tablename = "EX_INVOICE";
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            }
            $show_ket .= "Invoice Untuk No SPJ " . $no_shipment_in . " Sukses Di Update <br>";
        // }else{
        //     echo "masuk else";
        // }
    }

    $habis = "proses_invoice_bag_laut.php";
    break;
//============================================================================================================================
    case "cancel_invoice_bag_laut_fob":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;

            if (isset($_POST[$idke])) {
                $id_up = $_POST[$idke];

                $sql_inv = "SELECT NO_INVOICE, NO_SHP_TRN FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND ID = '$id_up' ";
                $query_inv = oci_parse($conn, $sql_inv);
                oci_execute($query_inv);
                $row_inv = oci_fetch_assoc($query_inv);
                $no_invoice_in = $row[NO_INVOICE];
                $no_shipment_in = $row[NO_SHP_TRN];

                $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'PAJAK_N', 'NO_TAGIHAN');
                $field_data = array("", "", "", "OPEN", "", "SYSDATE", "$user_name", "", "", "");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('ID');
                $value_id = array("$id_up");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                
                //start !!!!!!!!!!!!!!
                //POEX
                /*$tablename="M_POTONGAN_OA_TRANS";
		$field_id=array('NO_INVOICE');
		$value_id=array("$no_invoice_in");
		$fungsi->delete($conn,$tablename,$field_id,$value_id);*/
                //end !!!!!!!!!!!!!!

                $sqlcek = "SELECT VENDOR,SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_INVOICE = '$no_invoice_in' group by VENDOR ";
                $querycek = oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data = oci_fetch_assoc($querycek);
                $total_klaim_in = $row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in = $row_data[TOTAL_KTG];
                $total_semen_in = $row_data[TOTAL_SEMEN];
                $total_pdpks_in = $row_data[TOTAL_PDPKS];
                $total_pdpkk_in = $row_data[TOTAL_PDPKK];
                $no_vendor_inx = $row_data[VENDOR];

                if ($warna_plat_in == "HITAM")
                    $pajak_in = round($total_shp_in * 0.1, 0);
                else
                    $pajak_in = 0;

                $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;

                //liyantanto
                if ($no_vendor_inx != '0000410082') {
                    $sql_pjaknew = "
                            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                            select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                            (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_in'
                            group by NO_INVOICE)
                            order by TGL_PAJAK_EX desc
                            ) where rownum =1";
                    $querycek = oci_parse($conn, $sql_pjaknew);
                    oci_execute($querycek);
                    $row_datap = oci_fetch_assoc($querycek);
                    unset($tglfakturpajak);
                    $tglfakturpajak = $row_datap[TGL_PAJAK_EXF];
                    if ($tglfakturpajak != '' && $tglfakturpajak >= '20140901' && $warna_plat_in == "HITAM") {
                        $pajak_in1 = round($total_shp_in * 0.1, 0);
                        $pajak_in = round($pajak_in1 * 0.1, 0);

                        $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                    }
                }

                $field_names = array('TOTAL_INV', 'PAJAK_INV', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'TGL_INVOICE', 'PDPKK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TOTAL_INVOICE');
                $field_data = array("$total_shp_in", "$pajak_in", "$total_ktg_in", "$total_semen_in", "$total_pdpks_in", "SYSDATE", "$total_pdpkk_in", "SYSDATE", "$user_name", "$total_tagihan");
                $tablename = "EX_INVOICE";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_in");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                if ($total_shp_in == 0) {
                    $field_id = array('NO_INVOICE');
                    $value_id = array("$no_invoice_in");
                    $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                    $field_data = array("1", "SYSDATE", "$user_name");
                    $tablename = "EX_INVOICE";
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
                $show_ket .= "Invoice Untuk No SPJ " . $no_shipment_in . " Sukses Di Update <br>";
            }
        }

        $habis = "proses_invoice_bag_laut_fob.php";
        break;
//============================================================================================================================
    case "reverse_invoice_laut":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $no_invoice_x = $_POST['no_invoice_x'];

        $field_names = array('NO_INV_VENDOR', 'INVOICED_BY', 'NO_INVOICE', 'STATUS', 'STATUS2', 'TANGGAL_INVOICE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'NO_PAJAK_EX', 'NO_TAGIHAN');
        $field_data = array("", "", "", "DRAFT", "OPEN", "", "SYSDATE", "$user_name", "", "");
        $tablename = "EX_TRANS_HDR";
        $field_id = array('NO_TAGIHAN'); //
        $value_id = array("$no_invoice_x");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice_x");
        $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
        $field_data = array("1", "SYSDATE", "$user_name");
        $tablename = "EX_INVOICE";
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $show_ket .= " NO INVOICE $no_invoice_x Berhasil di reverse <br>";


        $habis = "reverse_invoice_laut.php";
        break;
//============================================================================================================================
    case "open_block":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;

            if (isset($_POST[$idke])) {
                $id_up = $_POST[$idke];

                $sql_inv = "SELECT NO_SHP_TRN FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND ID = '$id_up' AND DELETE_MARK = '0' AND STATUS2 = 'PENDING' ";
                $query_inv = oci_parse($conn, $sql_inv);
                oci_execute($query_inv);
                $row_inv = oci_fetch_assoc($query_inv);
                $no_shipment_in = $row[NO_SHP_TRN];

                if ($no_shipment_in != "") {
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'OPEN_BY', 'OPEN_DATE');
                    $field_data = array("OPEN", "SYSDATE", "$user_name", "$user_name", "SYSDATE");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Open Block Untuk No SPJ $no_shipment_in Sukses <br>";
                } else {
                    $show_ket .= "Open Block Untuk No SPJ $no_shipment_in Gagal <br>";
                }
            }
        }

        $habis = "open_block.php";
        break;
//============================================================================================================================
    case "create_block":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $sampai = $_POST['total'];
        for ($k = 0; $k < $sampai; $k++) {
            $idke = "idke" . $k;

            if (isset($_POST[$idke])) {
                $id_up = $_POST[$idke];

                $sql_inv = "SELECT NO_SHP_TRN FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND ID = '$id_up' AND DELETE_MARK = '0' AND STATUS = 'DRAFT' AND STATUS2 IN ('DRAFT','OPEN') ";
                $query_inv = oci_parse($conn, $sql_inv);
                oci_execute($query_inv);
                $row_inv = oci_fetch_assoc($query_inv);
                $no_shipment_in = $row[NO_SHP_TRN];

                if ($no_shipment_in != "") {
                    $field_names = array('STATUS2', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'BLOCK_BY', 'BLOCK_DATE');
                    $field_data = array("PENDING", "SYSDATE", "$user_name", "$user_name", "SYSDATE");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Create Block Untuk No SPJ $no_shipment_in Sukses <br>";
                } else {
                    $show_ket .= "Create Block Untuk No SPJ $no_shipment_in Gagal <br>";
                }
            }
        }

        $habis = "create_block.php";
        break;
//============================================================================================================================
    case "generate_ppl":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $userOrg = $_SESSION['user_org'];


        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = $_REQUEST['nama_bank'];
        $cabang_bank = $_REQUEST['cabang_bank'];
        $skbp_vyi = $_REQUEST['skbp_v'];

        $no_invoice = $_REQUEST['no_invoice'];

        $sqlOrgInvoice = "SELECT EX_I.ORG,to_char(EX_T.TANGGAL_KIRIM,'MM-YYYY') as TANGGAL_SPJ from EX_INVOICE EX_I
                        JOIN EX_TRANS_HDR EX_T ON EX_T.NO_INVOICE = EX_I.NO_INVOICE
                        where EX_I.NO_INVOICE = '$no_invoice'
                        AND EX_T.NO_INVOICE = '$no_invoice'
                        and ROWNUM <=1";
        $queryOrgInvoice = oci_parse($conn, $sqlOrgInvoice);
        oci_execute($queryOrgInvoice);
        $result = oci_fetch_array($queryOrgInvoice);
        $orgInvoice = $result['ORG'];
        $dateSpjx = $result['TANGGAL_SPJ'];
        $dateSpj = explode('-', $result['TANGGAL_SPJ']);
        $blnKirim = $dateSpj[0];
        $tahnKirim = $dateSpj[1];
        $isNewRouting = false;
        //------------------------------------------------------------------
        $sql_select_invoice_date = "SELECT to_char(TGL_INVOICE ,'YYYYMMDD') as invoice_date,NO_VENDOR from EX_INVOICE where no_invoice='$no_invoice' and DELETE_MARK='0'";
        $query_date_invoice = oci_parse($conn, $sql_select_invoice_date);
        oci_execute($query_date_invoice);
        while ($baris = oci_fetch_array($query_date_invoice)) {
            $tanggal_create_invoice = $baris[INVOICE_DATE];
            $no_eks = $baris[NO_VENDOR];
        }
//        print_r($tanggal_create_invoice);
//        if($tanggal_create_invoice>=********){
//            echo "RN";
//        }else{
//            echo "WN";
//        }
        //------------------------------------------------------------------
        #(WAPU) 1 - Include untuk menghitung Total Groos Amount ------------------
        $nilai_invoice = 0;
        include_once("wapu_formula.php");
        $total_cekcek = 0;

        #cek apakah invoice ini masuk bulan Juli 2012 -> dst...
        $bool_periodewapu = false;
        $sql_faktur = "select no_invoice, to_char(tgl_pajak_ex,'YYYYMMDD') as tglfakturpajak from ex_invoice where tgl_pajak_ex is not null and to_char(tgl_pajak_ex,'YYYYMMDD') >= '********' and no_invoice = '" . $no_invoice . "' order by to_char(tgl_pajak_ex,'YYYYMMDD')";
        $query_faktur = @oci_parse($conn, $sql_faktur);
        @oci_execute($query_faktur);
        while ($row_fkt = @oci_fetch_array($query_faktur)) {
            $bool_periodewapu = true;
        }
        #(/WAPU) 1 ---------------------------------------------------------------
        //
	// set parameter bapi miro	
        $sap = new SAPConnection();
        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            $sap->PrintStatus();
            exit;
        }

        $fce = $sap->NewFunction("Z_ZAPPSD_BAPI_INVOICE");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }

        $sql_del = "UPDATE EX_KOMPONEN_INV SET DELETE_MARK = '1' , LAST_UPDATE_DATE = SYSDATE , LAST_UPDATED_BY = '$user_name'  WHERE NO_INVOICE = '$no_invoice' AND DELETE_MARK = '0'";
        $query_del = oci_parse($conn, $sql_del);
        oci_execute($query_del);

        $sql = "SELECT EX_TRANS_HDR.*,to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
               to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,to_char(tanggal_kirim,'YYYY') as TAHUNSPJ
               FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
        // echo $sql;
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $total_shp_cost = 0;
        $total_gl_klaim = 0;
        $total_klaim_ktg = 0;
        $total_klaim_semen = 0;
        $total_klaim_rezak = 0;
        $total_klaim_pdpks = 0;
        $pajak_shp_cost = 0;
        $total_glondong = 0;
        $n = 1;
        $pass_trn = 0;
        while ($row = oci_fetch_array($query)) {
            $no_invoice_v = $row[NO_INVOICE];
            $no_invoice_ex_v = $row[NO_INV_VENDOR];
            $spj_v[] = $row[NO_SHP_TRN];
            $tgl_kirim_v[] = $row[TANGGAL_KIRIM];
            $tgl_datang_v[] = $row[TANGGAL_DATANG1];
            $tgl_bongkar_v[] = $row[TANGGAL_BONGKAR1];
            $produk_v[] = $row[KODE_PRODUK];
            $nama_produk_v[] = $row[NAMA_PRODUK];
            $shp_trn_v[] = $row[NO_SHP_TRN];
            $shp_trn_vcek = $row[NO_SHP_TRN];
            $plant_v = $row[PLANT];
            $org_v = $row[ORG];
            $nama_org_v = $row[NAMA_ORG];
            $thunspj = $row[TAHUNSPJ];
            $warna_plat_v = $row[WARNA_PLAT];
            $type_plat_v = trim($row[VEHICLE_TYPE]);
            if ($type_plat_v == '205') {
                $warna_plat_v = 'HITAM';
            }
            $kel = $row[KELOMPOK_TRANSAKSI];
            $no_pajak_ex = trim($row[NO_PAJAK_EX]);
            // echo $no_pajak_ex;
            $no_pajak_in = str_replace("-", "", $no_pajak_ex);
            $no_pajak_in = str_replace(".", "", $no_pajak_in);
            $kode_faktur_wapu = substr($no_pajak_in, 0, 3);
            $kode_faktur_wapu2 = substr($no_pajak_in, 0, 2);
            $faktur_bool = false;
            if ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03') {
                $faktur_bool = true;
            }


            #add by iljas ---------------------------------- Wapu 12-07-2012
            $inco_v = $row[INCO];
            $inco_bool = false;
            if (trim(strtoupper($inco_v)) == 'FRC' && trim(strtoupper($kel)) == 'DARAT' && trim(strtoupper($warna_plat_v)) == 'HITAM') {
                $inco_bool = true;
            } else if (trim(strtoupper($kel)) == 'LAUT' && ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03')) {
                $inco_bool = true;
            }
            $keterangan_otomatis = "INVOICE " . $no_invoice_v;
            //kebutuhan kso:untuk SPJ KSO tidak tidak dikenakan wapu
            if (($org_v == '7000' && $thunspj == '2017') || $org_v == '5000') {
                $bool_periodewapu = false;
                if (trim(strtoupper($kel)) == 'LAUT') {
                    $inco_bool = false;
                }
            } else if (($org_v == '5000') && trim(strtoupper($warna_plat_v)) == 'KUNING') {
                $bool_periodewapu = false;
            } else if(($org_v =='7000') && trim(strtoupper($warna_plat_v))=='KUNING' && $thunspj >= '2018'){
				if(trim(strtoupper($inco_v))=='FRC' || trim(strtoupper($inco_v))=='FAS'){
					$bool_periodewapu = true;   
				}else {$bool_periodewapu = false;}				
            }
            $kode_pajak_baru = "";
            echo $bool_periodewapu ." ". $inco_bool ." ".$nilai_invoice;
			//echo 'INCOTERM '.$inco_v;
            if ($bool_periodewapu && $inco_bool && ($nilai_invoice > 10000000)) {
//                echo $kode_faktur_wapu." ".$kode_faktur_wapu2." masuk 2 ";
                if ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03' || $kode_faktur_wapu == '040' || $kode_faktur_wapu2 == '04') {
//                    echo $kode_faktur_wapu." ".$kode_faktur_wapu2." masuk 2 ";
                    if ($p1mna == 1) {
//                        echo $p1mna." masuk 3 ";
                        //pengujian tax laut
                        if($tanggal_create_invoice>=********){
                            $kode_pajak_baru = "RX";
                        } 
                        // elseif ($tanggal_create_invoice>=20250101) {
                        //     $kode_pajak_baru = "DX";
                        // }
                        else{
                            $kode_pajak_baru = "WX";
                        }
                    } else {
                        //pengujian taxcode 11%
                        if($tanggal_create_invoice>=******** && $tanggal_create_invoice<20250101){
                            $kode_pajak_baru = "RN";
                        }
                        //pengujian taxcode 12%
                        elseif($tanggal_create_invoice>=20250101){
                            $kode_pajak_baru = "DN";
                        }
                        else{
                            $kode_pajak_baru = "WN";
                        }
                        //---------------
                    }
                    $keterangan_otomatis = "INVOICE OA";
                } else {
                    // $pesan = "<br>Maaf, Invoice ini masuk WAPU";
                    echo $pesan .= "<br>Faktur Pajak harus 030";
                    exit;
                }
            }
            #end - add by iljas ---------------------------- Wapu 12-07-2012

            $nama_plant_v = $row[NAMA_PLANT];
            $warna_plat_v = $row[WARNA_PLAT];
            $type_plat_v = trim($row[VEHICLE_TYPE]);
            if ($type_plat_v == '205') {
                $warna_plat_v = 'HITAM';
            }
            $nama_vendor_v = $row[NAMA_VENDOR];
            $vendor_v = $row[VENDOR];

            $tanggal_invoice_v = $row[TANGGAL_INVOICE1];

            $sal_dis_v[] = $row[SAL_DISTRIK];
            $nama_sal_dis_v[] = $row[NAMA_SAL_DIS];
            $sold_to_v[] = $row[SOLD_TO];
            $nama_sold_to_v[] = $row[NAMA_SOLD_TO];
            $ship_to_v[] = $row[SHIP_TO];
            $qty_v[] = $row[QTY_SHP];
            $qty_kantong_rusak_v[] = $row[QTY_KTG_RUSAK];
            $qty_semen_rusak_v[] = $row[QTY_SEMEN_RUSAK];
            $id_v[] = $row[ID];
            $no_pol_v[] = $row[NO_POL];
            $shp_cost_v[] = $row[SHP_COST];
            //echo " nilai cost hdr ".$row[SHP_COST];
            $total_klaim_all_v[] = $row[TOTAL_KLAIM_ALL];
            $no_pajak_ex = $row[NO_PAJAK_EX];
            $ebeln_v = $row[EBELN];
            $ebelp_v = $row[EBELP];
            $prctr_v = $row[PRCTR];
            $kostl_v = $row[KOSTL];
            $kostl_v2000 = $kostl_v;
            $org_v = $row[ORG];
            $nama_org_v = $row[NAMA_ORG];
            if ($row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********') {
                // do not change this sscript please...
                //	$total_glondong += $row[TOTAL_KLAIM_ALL];
                //	$total_glondong += $row[TOTAL_KTG_RUSAK];

                $total_glondong += $row[TOTAL_SEMEN_RUSAK]; //remove by iljas - glondong
                //	$total_glondong += $row[TOTAL_KTG_REZAK];
                //	$total_glondong += $row[PDPKS];
                //$total_glondong = $total_glondong - $row[KLAIM_LEBIH];
                //$total_glondong = $total_glondong - $row[PDPKS_LEBIH];
            } else {
                //$total_gl_klaim+=$row[TOTAL_KLAIM_ALL];
                //$pass_trn += $row[TOTAL_SEMEN_RUSAK]+$row[TOTAL_KTG_REZAK];
                $total_klaim_ktg+=$row[TOTAL_KTG_RUSAK];
                $total_klaim_semen+=$row[TOTAL_SEMEN_RUSAK];
                $total_klaim_rezak+=$row[TOTAL_KTG_REZAK];
                $total_klaim_pdpks+=$row[PDPKS];
            }
            $approve_v = $row[TANGGAL_APPROVE];
            $kel = $row[KELOMPOK_TRANSAKSI];
            $inco = $row[INCO];
            $fknum = $row[KODE_SHP_COST];
            $no_gl_shp = $row[NO_GL_SHP];
            $pajak_n_v = $row[PAJAK_N];

            #ditambah oleh iljas---
//		$update_shpcost_tmp = "update EX_TRANS_COST 
//								set SHP_COST=$row[SHP_COST]
//							WHERE DELETE_MARK ='0' AND KODE_SHP_COST  = '$fknum'";
//
//		$query_shpcost_tmp= oci_parse($conn, $update_shpcost_tmp);
//		$query_shpcost = @oci_execute($query_shpcost_tmp);
            #if($query_shpcost) $hsill = "OK";
            #else $hsill = "XX";
            #---
            //Update EX_TRANS_COST kecuali SHP_COST
            $fce1 = $sap->NewFunction("Z_ZAPPSD_SEL_SHP_COST4"); //perubahan rfc
            if ($fce1 == false) {
                $sap->PrintStatus();
                exit;
            }
            $fce1->LR_EXTI1->row["SIGN"] = 'I';
            $fce1->LR_EXTI1->row["OPTION"] = 'EQ';
            $fce1->LR_EXTI1->row["LOW"] = $shp_trn_vcek;
            $fce1->LR_EXTI1->Append($fce1->LR_EXTI1->row);
            $fce1->LR_KUNNR->row["SIGN"] = 'I';
            $fce1->LR_KUNNR->row["OPTION"] = 'EQ';
            $fce1->LR_KUNNR->row["LOW"] = sprintf("%010d", trim($row[SHIP_TO]));
            $fce1->LR_KUNNR->Append($fce1->LR_KUNNR->row);
            $fce1->Call();
            if ($fce1->GetStatus() == SAPRFC_OK) {
                $fce1->RETURN_DATA->Reset();
                while ($fce1->RETURN_DATA->Next()) {
                    $fknum_insing = $fce1->RETURN_DATA->row["FKNUM"];
                    $lblni_insing = $fce1->RETURN_DATA->row["LBLNI"];
                    $fkpty_insing = $fce1->RETURN_DATA->row["FKPTY"];
                    $fkpos_insing = $fce1->RETURN_DATA->row["FKPOS"];
                    $ebeln_insing = $fce1->RETURN_DATA->row["EBELN"];
                    $ebelp_insing = $fce1->RETURN_DATA->row["EBELP"];
                    $netwr_insing = round($fce1->RETURN_DATA->row["NETWR"] * 100, 0);
                    if ($fknum_insing != "" and $netwr_insing != '' and $lblni_insing != "" and $fkpty_insing != "" and $fkpos_insing != "" and $ebeln_insing != "" and $ebeln_insing != "") {
                        unset($idCEKcost);
                        $sql_CEKcost = "
                                        select ID,NO_ENTRY_SHEET,EBELN from EX_TRANS_COST where delete_mark='0'
                                        and NO_SHP_TRN='$shp_trn_vcek' and FKPOS='$fkpos_insing'
                                    ";
                        $query_CEKcost = oci_parse($conn, $sql_CEKcost);
                        oci_execute($query_CEKcost);
                        $row_CEKcost = oci_fetch_array($query_CEKcost);
                        $idCEKcost = trim($row_CEKcost["ID"]);
                        $idNO_ENTRY_SHEET = trim($row_CEKcost["NO_ENTRY_SHEET"]);
                        $idEBELN = trim($row_CEKcost["EBELN"]);
                        if ($idNO_ENTRY_SHEET != $lblni_insing && $idEBELN != $ebeln_insing) {
                            if ($idCEKcost != '') {
                                $field_namessing = array('KODE_SHP_COST', 'NO_ENTRY_SHEET', 'TYPE', 'EBELN', 'EBELP');
                                $field_datasing = array("$fknum_insing", "$lblni_insing", "$fkpty_insing", "$ebeln_insing", "$ebelp_insing");
                                $tablenamesing = "EX_TRANS_COST";
                                $field_idsing = array('ID', 'NO_SHP_TRN', 'DELETE_MARK', 'FKPOS');
                                $value_idsing = array("$idCEKcost", "$shp_trn_vcek", "0", "$fkpos_insing");
                                $fungsi->update($conn, $field_namessing, $field_datasing, $tablenamesing, $field_idsing, $value_idsing);
                            } else {
                                $field_namesisertsing = array('NO_SHP_TRN', 'KODE_SHP_COST', 'SHP_COST', 'NO_ENTRY_SHEET', 'TYPE', 'FKPOS', 'DELETE_MARK', 'EBELN', 'EBELP');
                                $field_datainsertsing = array("$shp_trn_vcek", "$fknum_insing", "$netwr_insing", "$lblni_insing", "$fkpty_insing", "$fkpos_insing", "0", "$ebeln_insing", "$ebelp_insing");
                                $tablenameinsertsing = "EX_TRANS_COST";
                                $fungsi->insert($conn, $field_namesisertsing, $field_datainsertsing, $tablenameinsertsing);
                            }
                        }
                    } else {
                        $show_ket .= "Entry Sheet Tidak Ditemukan/Telah Di-reverse";
                        ?>
                        <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
                            <div class="alert alert-info" role="alert">
                                <strong>Pesan!</strong>
                        <?= $show_ket; ?>
                            </div>
                        </div>
                        <?
                        $habis = "lihat_invoice_hdr.php";
                        exit;
                    }
                }
                usleep(500);
            }
            $fce1->Close();

            $sql_num = "SELECT * FROM EX_TRANS_COST WHERE DELETE_MARK ='0' AND NO_SHP_TRN = '$shp_trn_vcek' ORDER BY FKPOS ASC ";
            $query_num = oci_parse($conn, $sql_num);
            oci_execute($query_num);

            $cek = 1;
            $nilai_per_shp = 0;
            while ($row_num = oci_fetch_array($query_num)) {
                #---
                //if($cek>1) continue;
                //$cek++;
                #/---

                $no_sheet = $row_num[NO_ENTRY_SHEET];
                $panjang = strlen(strval($n));
                if ($panjang == 1)
                    $no_ke = '00000' . $n;
                if ($panjang == 2)
                    $no_ke = '0000' . $n;
                if ($panjang == 3)
                    $no_ke = '000' . $n;
                if ($panjang == 4)
                    $no_ke = '00' . $n;
                if ($panjang == 5)
                    $no_ke = '0' . $n;
                if ($panjang == 6)
                    $no_ke = $n;
                //echo " <br> nilai shp ". $row_num[SHP_COST];
                $total_data_shp = round($row_num[SHP_COST]); //+round(0.1*$row[SHP_COST],0);
                $total_shp_cost+=round($row_num[SHP_COST]);
                $nilai_per_shp += $row_num[SHP_COST];
                //item data entri    
                $fce->X_ITEMDATA->row["INVOICE_DOC_ITEM"] = $no_ke; //'000001';
                $fce->X_ITEMDATA->row["PO_NUMBER"] = $row_num[EBELN]; //$ebeln_v;
                $fce->X_ITEMDATA->row["PO_ITEM"] = $row_num[EBELP]; //$ebelp_v;
                if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )) {
                    if ($p1mna == 1) {
                        //pengujian tax laut
                        if($tanggal_create_invoice>=********){
                            $kode_pjk_shp = 'YQ'; //VX
                        } 
                        else {
                            $kode_pjk_shp = 'VQ'; //VX
                        }
                    } else {
                        //pengujian taxcode pajak 11%
                        if($tanggal_create_invoice>=******** && $tanggal_create_invoice<20250101){
                            $kode_pajak_baru = "RN";
                        }
                        //pengujian taxcode 12%
                        elseif($tanggal_create_invoice>=20250101){
                            if($kode_faktur_wapu == '040' || $kode_faktur_wapu2 == '04') {
                                $kode_pajak_baru = "YN";
                                // echo "masuk sini kode YN. Ini = ".$kel; 
                                // echo "Ini inconya = ".$inco;
                                // echo "Ini kode faktur wapu = " .$kode_faktur_wapu;
                                // echo "Ini kode faktur wapu 2 =" . $kode_faktur_wapu2;
                            } else {
                                // echo "tidak masuk sini, ini DN";
                                $kode_pajak_baru = "DN";
                            }
                        }
                        else{
                            $kode_pajak_baru = "WN";
                        }
                        //----------------------------
                    }


                    if ($pajak_n_v == "N") {
                        if($tanggal_create_invoice>=20250101){
                            $kode_pjk_shp = 'YY';
                            $kode_pajak_baru = 'YY';
                        } else {
                            $kode_pjk_shp = 'VZ';
                        }
                    } elseif ($pajak_n_v == "Y") {
                        if ($p1mna == 1) {
                            //pengujian tax laut
                            if($tanggal_create_invoice>=********){
                                $kode_pjk_shp = "YQ";//VX
                            } else {
                                $kode_pjk_shp = "VQ";//VX
                            }
                        } else {
                        //pengujian taxcode pajak 11%
                            if($tanggal_create_invoice>=******** && $tanggal_create_invoice<20250101){
                                $kode_pjk_shp = 'YN';                            
                            }elseif($tanggal_create_invoice>=20250101){ //pengujian taxcode pajak 12%
                                $kode_pjk_shp = 'EN';                            
                            }else{
                                $kode_pjk_shp = 'VN';
                            }
                        //---------------------------- // pass mahmud bisa di blank diganti VN biar afdol...
                        }
                    }

                    if ($kode_pajak_baru != '')
                        $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
                    $fce->X_ITEMDATA->row["TAX_CODE"] = $kode_pjk_shp; //VN Tax code untuk PPN 10 % 
                }else {//($warna_plat_v=="KUNING")
                    // $kode_pjk_shp = 'VZ';
                    if($tanggal_create_invoice>=20250101){
                        $kode_pjk_shp = 'YY';
                        $kode_pajak_baru = 'YY';
                    } else {
                        $kode_pjk_shp = 'VZ';
                    }
                    if ($kode_pajak_baru != '')
                        $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
                    $fce->X_ITEMDATA->row["TAX_CODE"] = $kode_pjk_shp; //VN Tax code untuk PPN 10 % 
                }
                $fce->X_ITEMDATA->row["ITEM_AMOUNT"] = $total_data_shp;
                $fce->X_ITEMDATA->row["QUANTITY"] = '1';
                $fce->X_ITEMDATA->row["PO_UNIT"] = 'LE'; //$row[SATUAN_SHP];
                $fce->X_ITEMDATA->row["SHEET_NO"] = $no_sheet;
                //@by liyantanto
                $fce->X_ITEMDATA->row["ITEM_TEXT"] = $vendor_v . "-" . $no_invoice_v . "-" . $shp_trn_vcek; //kodevendor+noinvoice+spj
                $fce->X_ITEMDATA->row["SHEET_ITEM"] = '0000000010'; // mulai dari 0000000010
                $fce->X_ITEMDATA->Append($fce->X_ITEMDATA->row);
                $n++;
            }
            //echo "nilai_per_shp" .$nilai_per_shp;
        }
//	echo "<br> Total SHP COST ".$total_shp_cost;
//	echo "<br> kode pajak ".$kode_pjk_shp;
        if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )) {
            if ($p1mna == 1) {
                //pengujian taxcode pajak 11%
				if($tanggal_create_invoice>=********){
					//pengujian tax laut
					if($no_eks == '0000410003'){
                        $pajak_in1 = $total_shp_cost;
						$pajak_shp_cost += 0.011 * $pajak_in1;
					} else {
                        $pajak_in1 = $total_shp_cost;
						$pajak_shp_cost += 0.011 * $pajak_in1;
					}
				}else{
					$pajak_in1 = $total_shp_cost;
					$pajak_shp_cost += 0.1 * $pajak_in1;
				}
				//echo "<br> PAJAK INL ".$pajak_in1;
				//echo "<br> PAJAK SHP COST ".$pajak_shp_cost;
                //----------------------------
            } else {
                //pengujian taxcode pajak 11%                
                // if($tanggal_create_invoice>=******** && $tanggal_create_invoice < 20250101){
                //     $pajak_shp_cost += 0.11 * $total_shp_cost;
                // }
                // //pengujian taxcode pajak 12%
                if($tanggal_create_invoice>=********){
                    if($kode_pajak_baru == "YN" && ($kode_faktur_wapu == '040' || $kode_faktur_wapu2 == '04')) {
                        // echo "Ini kode_pajak_baru = ".$kode_pajak_baru;
                        // echo "Ini kode faktur wapu = " .$kode_faktur_wapu;
                        // echo "Ini kode faktur wapu 2 =" . $kode_faktur_wapu2;
                        $pajak_in1 = $total_shp_cost;
                        $pajak_shp_cost += 0.11 * $pajak_in1;
                    } elseif($kode_pajak_baru =="RX" && ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03')) {
                        $pajak_in1 = $total_shp_cost;
                        $pajak_shp_cost += 0.011 * $pajak_in1;
                    } elseif(trim($kode_pajak_baru) == 'DN' && trim($kode_faktur_wapu2) == '03'){
                        $pajak_in1 = $total_shp_cost * (11/12);
                        $pajak_shp_cost += 0.12 * $pajak_in1;
                    } else {
                        // echo "tidak masuk sini, ini perhitungan DN";
                        $pajak_in1 = $total_shp_cost * (11/12);
                        $pajak_shp_cost += 0.12 * $pajak_in1;
                    }
                }
                else{
                    $pajak_shp_cost += 0.1 * $total_shp_cost;
                }
                //----------------------------
            }
            if(trim($kode_pajak_baru) == 'DN' && trim($kode_faktur_wapu2) == '03'){
                $fce->X_TAXDATA->row["TAX_CODE"] = $kode_pjk_shp; //$row[SATUAN_SHP];
                $fce->X_TAXDATA->row["TAX_AMOUNT"] = '';
                $fce->X_TAXDATA->row["TAX_BASE_AMOUNT"] = $pajak_in1; // mulai dari 0000000010
                $fce->X_TAXDATA->Append($fce->X_TAXDATA->row);
            }else {
                $fce->X_TAXDATA->row["TAX_CODE"] = $kode_pjk_shp; //$row[SATUAN_SHP];
                $fce->X_TAXDATA->row["TAX_AMOUNT"] = $pajak_shp_cost;
                $fce->X_TAXDATA->row["TAX_BASE_AMOUNT"] = $pajak_in1; // mulai dari 0000000010
                $fce->X_TAXDATA->Append($fce->X_TAXDATA->row);
            }
            // khusus spj 1000 lebih maka pajak digabungkan menjadi satu di tagihan pertama.
            // tagihan pertama jika pjak_n = Y
            // dan unutk nilai pajak ambil semua data yang no tagihan sama dengan no invoice 
            if ($pajak_n_v == "Y") {
                $pajak_shp_cost = 0;
                $sql_pajak_1000 = "SELECT SUM(SHP_COST) AS JUM_SHPCOST FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice'";
                $query_pajak1000 = oci_parse($conn, $sql_pajak_1000);
                oci_execute($query_pajak1000);
                $row_pajak1000 = oci_fetch_array($query_pajak1000);
                $kena_pajak = $row_pajak1000["JUM_SHPCOST"];
                if ($p1mna == 1) {
                    //pengujian pajak 11%
                    if($tanggal_create_invoice>=********){
						$pajak_in1 = 0.11 * $kena_pajak;
						$pajak_shp_cost = 0.11 * $pajak_in1;
						$kode_pjk_shp = "YQ";//VX
					}else{
						$pajak_in1 = 0.1 * $kena_pajak;
						$pajak_shp_cost = 0.1 * $pajak_in1;
						$kode_pjk_shp = "VQ";//VX
					}
                } else {
                    if($tanggal_create_invoice>=******** && $tanggal_create_invoice < 20250101){
                        $pajak_shp_cost = 0.11 * $kena_pajak;
                        $kode_pjk_shp = "YN";
                    }elseif($tanggal_create_invoice>=20250101){
                        $pajak_in1 = (11/12) * $kena_pajak;
                        $pajak_shp_cost = 0.12 * $pajak_in1;
                        $kode_pjk_shp = "EN";
                    }else{
                        $pajak_shp_cost = 0.1 * $kena_pajak;
                        $kode_pjk_shp = "VN";                        
                    }
                    //-----------
                }
                if ($kode_pajak_baru != '')
                    $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
                    $fce->X_TAXDATA->row["TAX_CODE"] = $kode_pjk_shp; //$row[SATUAN_SHP];
                    $fce->X_TAXDATA->row["TAX_AMOUNT"] = $pajak_shp_cost;
                    $fce->X_TAXDATA->row["TAX_BASE_AMOUNT"] = $pajak_in1; // mulai dari 0000000010
                    $fce->X_TAXDATA->Append($fce->X_TAXDATA->row);
            }elseif ($pajak_n_v == "N") { 
                $pajak_shp_cost = 0;
                $kode_pjk_shp = 'VZ';
                if ($kode_pajak_baru != '')
                    $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
            }
        }elseif($tanggal_create_invoice>=20250101){
            $pajak_shp_cost = 0;
            $kode_pjk_shp = 'YY';

            $sql_pajak_1000 = "SELECT SUM(SHP_COST) AS JUM_SHPCOST FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice'";
            $query_pajak1000 = oci_parse($conn, $sql_pajak_1000);
            oci_execute($query_pajak1000);

            $row_pajak1000 = oci_fetch_array($query_pajak1000);
            $kena_pajak = $row_pajak1000["JUM_SHPCOST"];
            
            $pajak_shp_cost = 0.11 * $kena_pajak;
        }else //($warna_plat_v=="HITAM")
            $pajak_shp_cost = 0;

//        $UNTUK_SHP = $total_shp_cost + $pajak_shp_cost;
//        echo "total".$UNTUK_SHP.' ';
//        echo $total_shp_cost.' ';
//        echo $pajak_shp_cost.' ';
        $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
        //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
        $field_data = array("SG0001", "OA SEMEN", "", "$no_invoice", "$total_shp_cost", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "ONGKOS ANGKUT", "$no_gl_shp", "$org_v", "$nama_org_v", "$kode_pjk_shp", "$pajak_shp_cost", "$UNTUK_SHP");
        $tablename = "EX_KOMPONEN_INV";
        $fungsi->insert($conn, $field_names, $field_data, $tablename);

        $total = count($shp_trn_v);

        $sql_tgl = "SELECT to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EX1 FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
        $query_tgl = oci_parse($conn, $sql_tgl);
        oci_execute($query_tgl);

        $row_tgl = oci_fetch_array($query_tgl);
        $tgl_pajak_x = $row_tgl[TGL_PAJAK_EX1];
        if ($tgl_pajak_x == "")
            $tgl_pajak_x = date("Ymd");
        //header entri    
        if ($kode_pjk_shp == "VZ" || $kode_pjk_shp == "YY" ) {
            $fce->X_HEADERDATA["CALC_TAX_IND"] = '';
        } else {
            if ($pajak_n_v == "Y") {
                $fce->X_HEADERDATA["CALC_TAX_IND"] = ''; // isi X jika hitung pajak dari total
            } else {
                if($tanggal_create_invoice>=20250101){
                    $fce->X_HEADERDATA["CALC_TAX_IND"] = ''; // isi X jika hitung pajak dari total
                } else {
                    $fce->X_HEADERDATA["CALC_TAX_IND"] = 'X'; // isi X jika hitung pajak dari total
                }
            }
        }
        $fce->X_HEADERDATA["INVOICE_IND"] = 'X';
        $fce->X_HEADERDATA["DOC_TYPE"] = 'RE';
        $fce->X_HEADERDATA["DOC_DATE"] = $tgl_pajak_x;

        //if($org_v=='2000'){ $datepostingnew='20131231'; }else{$datepostingnew=date("Ymd");}//
        $fce->X_HEADERDATA["PSTNG_DATE"] = date("Ymd");
        $fce->X_HEADERDATA["COMP_CODE"] = $org_v;
        $fce->X_HEADERDATA["CURRENCY"] = 'IDR';
        //$fce->X_HEADERDATA["DIFF_INV"] = $vendor_v; 

        if ($no_pajak_ex != "") {
            $no_pajak_in = str_replace("-", "", $no_pajak_ex);
            $no_pajak_in = str_replace(".", "", $no_pajak_in);
            // $fce->X_HEADERDATA["REF_DOC_NO"] = $kode_wn . $no_pajak_in; // no pajak vendor dari turunan
            $fce->X_HEADERDATA["ALLOC_NMBR"] = $kode_wn . $no_pajak_in;
        }
        $fce->X_HEADERDATA["PYMT_METH"] = 'T'; // Transfer
        $bvtyp = $_POST['bvtyp'];
        $fce->X_HEADERDATA["PARTNER_BK"] = $bvtyp; // patner bank vendor
        $fce->X_HEADERDATA["HEADER_TXT"] = $no_invoice_v; // no rekap invoice dari turunan
        if ($pajak_n_v == "Y" or $pajak_n_v == "N") {
            $fce->X_HEADERDATA["PMNT_BLOCK"] = '3'; // APPROVED MANUAL
        } else {
            $fce->X_HEADERDATA["PMNT_BLOCK"] = '3'; // APPROVED UNTUK DISTRAN langsung ke verifikasi
        }
        $ket_input = trim($keterangan_otomatis);  //Wajib Pungut
        if ($ket_input != '')
            $fce->X_HEADERDATA["ITEM_TEXT"] = $ket_input;  //Wajib Pungut
        $ke_gl = 0;


        if ($total_glondong > 1) {// unutk klaim glondong
            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

//			$no_gl_claim_ktg = '71410009';
            $kode_claim_gld = 'SG0006';
//			$nama_claim_ktg = 'BIAYA KLAIM KANTONG';
//			$keterangan_claim_ktg = 'BIAYA KLAIM KANTONG';

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_gld' and ORG='$org_v' AND DELETE_MARK = '0'  AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $nama_claim_gld = $row['NAMA_KOMPONEN'];
            $keterangan_claim_gld = $row['KETERANGAN'];
            $no_gl_claim_gld = $row['NO_GL'];
            $tax_gld = $row['TAX_CODE'];
            $rate_gld = $row['RATE'];
            $prctr_v = $row['PRCTR'];
            $kostl_v = $row['COST_CENTER'];
            //echo "<br> Klaim Total Glondong ".$total_glondong;
            $pajak_gld = $total_glondong * $rate_gld;
            $total_gld = $total_glondong + $pajak_gld;

            $total_gl_klaim2 = (-1) * $total_glondong;
            $pajak_gld2 = (-1) * $pajak_gld;
            $pajak_shp_cost+=$pajak_gld2;
            $total_gld2 = $total_gl_klaim2 + $pajak_gld2;

            //gl account data entri unutk cost claim   
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_gld;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_glondong;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_gld; //'VZ'
            $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
            $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';//$kostl_v;
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$kode_claim_gld", "$nama_claim_gld", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_gld", "$no_gl_claim_gld", "$org_v", "$nama_org_v", "$tax_gld", "$pajak_gld2", "$total_gld2");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
        }

        $sql_new = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_ALL_LEBIH) AS KLAIM_LEBIH, SUM(TOTAL_KLAIM_ALL) AS KLAIM_ALL, SUM(KLAIM_LEBIH) AS KLEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";

        $query_new = oci_parse($conn, $sql_new);
        oci_execute($query_new);
        while ($row_new = oci_fetch_array($query_new)) {
            $sold_to_new_v = $row_new[SOLD_TO];
            $klaim_ktg_new_v = $row_new[KLAIM_KTG];
            $klaim_rezak_new_v = $row_new[KLAIM_REZAK];
            $klaim_semen_new_v = $row_new[KLAIM_SEMEN];
            $klaim_pdpks_new_v = $row_new[KLAIM_PDPKS];
            $klaim_all_lebih_new_v = $row_new[KLAIM_LEBIH];
            $klaim_all_new_v = $row_new[KLAIM_ALL];

            $klaim_pdpks_lebih_new_v = $row_new[PDPKS_LEBIH];
            $klaim_lebih_new_v = $row_new[KLEBIH];

            if ($klaim_pdpks_new_v > $klaim_pdpks_lebih_new_v)
                $klaim_pdpks_new_v = $klaim_pdpks_new_v - $klaim_pdpks_lebih_new_v;
            else
                $klaim_pdpks_new_v = 0;

            //@liyantanto klaim lebih tidak masuk dalam perhitungan
            /* if($klaim_semen_new_v > $klaim_lebih_new_v)
              $klaim_semen_new_v = $klaim_semen_new_v - $klaim_lebih_new_v;
              else
              $klaim_semen_new_v = 0;
             */

            //if ($klaim_all_new_v > $klaim_all_lebih_new_v){//@liyantanto mengabaikan klaim lebih update 03/03/2016
            if ($klaim_all_new_v > 0) {

                if ($klaim_ktg_new_v > 1) {// unutk klaim kantong
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_ktg = '71410009';
                    $kode_claim_ktg = 'SG0002';
                    //			$nama_claim_ktg = 'BIAYA KLAIM KANTONG';
                    //			$keterangan_claim_ktg = 'BIAYA KLAIM KANTONG';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_ktg' and ORG='$org_v' AND DELETE_MARK = '0'  AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_ktg = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_ktg = $row['KETERANGAN'];
                    $no_gl_claim_ktg = $row['NO_GL'];
                    $tax_ktg = $row['TAX_CODE'];
                    $rate_ktg = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_vce = $row['COST_CENTER'];

                    //kondisi jika gp
                    $soltmapping = substr($sold_to_new_v, 0, 7);
                    if ($org_v == '7000' && $soltmapping != '0000007') {
                        $kostl_v = $kostl_vce;
                    } else if ($soltmapping == '0000007') {
                        $kostl_v = $row['COST_CENTER_ETC'];
                    } else {
                        $kostl_v = $kostl_v2000;
                    }

                    $pajak_ktg = $klaim_ktg_new_v * $rate_ktg;
                    $total_ktg = $klaim_ktg_new_v + $pajak_ktg;
                    $total_gl_klaim2 = (-1) * $klaim_ktg_new_v;
                    $pajak_ktg2 = (-1) * $pajak_ktg;
                    $pajak_shp_cost+=$pajak_ktg2;
                    $total_ktg2 = $total_gl_klaim2 + $pajak_ktg2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_ktg;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_ktg;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_ktg; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'';//'**********';//;//PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v;
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_ktg", "$nama_claim_ktg", "$kostl_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_ktg", "$no_gl_claim_ktg", "$org_v", "$nama_org_v", "$tax_ktg", "$pajak_ktg2", "$total_ktg2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_ktg_new_v;
                    //$pass_trn += $row[TOTAL_SEMEN_RUSAK]+$row[TOTAL_KTG_REZAK];
                }


                if ($klaim_semen_new_v > 1) {// unutk klaim semen
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_semen = '71410009';
                    $kode_claim_semen = 'SG0003';
                    //			$nama_claim_semen = 'BIAYA KLAIM SEMEN';
                    //			$keterangan_claim_semen = 'BIAYA KLAIM SEMEN';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' and ORG='$org_v' AND DELETE_MARK = '0'  AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_semen = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_semen = $row['KETERANGAN'];
                    $no_gl_claim_semen = $row['NO_GL'];
                    $tax_semen = $row['TAX_CODE'];
                    $rate_semen = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_semen = $klaim_semen_new_v * $rate_semen;
                    $total_semen = $klaim_semen_new_v + $pajak_semen;
                    $total_gl_klaim2 = (-1) * $klaim_semen_new_v;
                    $pajak_semen2 = (-1) * $pajak_semen;
                    $pajak_shp_cost+=$pajak_semen2;
                    $total_semen2 = $total_gl_klaim2 + $pajak_semen2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_semen;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_semen; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_semen", "$nama_claim_semen", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_semen", "$no_gl_claim_semen", "$org_v", "$nama_org_v", "$tax_semen", "$pajak_semen2", "$total_semen2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_semen_new_v;
                    $pass_trn += $klaim_semen_new_v;
                }


                if ($klaim_rezak_new_v > 1) {// unutk klaim rezak
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_rezak = '71410009';
                    $kode_claim_rezak = 'SG0004';
                    //			$nama_claim_rezak = 'BIAYA KLAIM REZAK';
                    //			$keterangan_claim_rezak = 'BIAYA KLAIM REZAK';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' and ORG='$org_v' AND DELETE_MARK = '0'  AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_rezak = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_rezak = $row['KETERANGAN'];
                    $no_gl_claim_rezak = $row['NO_GL'];
                    $tax_rezak = $row['TAX_CODE'];
                    $rate_rezak = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_rezak = $klaim_rezak_new_v * $rate_rezak;
                    $total_rezak = $klaim_rezak_new_v + $pajak_rezak;
                    $total_gl_klaim2 = (-1) * $klaim_rezak_new_v;
                    $pajak_rezak2 = (-1) * $pajak_rezak;
                    $pajak_shp_cost+=$pajak_rezak2;
                    $total_rezak2 = $total_gl_klaim2 + $pajak_rezak2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_rezak;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_rezak; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_rezak", "$nama_claim_rezak", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_rezak", "$no_gl_claim_rezak", "$org_v", "$nama_org_v", "$tax_rezak", "$pajak_rezak2", "$total_rezak2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_rezak_new_v;
                    $pass_trn += $klaim_rezak_new_v;
                }

                if ($klaim_pdpks_new_v > 1) {
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_pdpks = '71410009';
                    $kode_claim_pdpks = 'SG0005';
                    //			$nama_claim_pdpks = 'BIAYA KLAIM PDPKS';
                    //			$keterangan_claim_pdpks = 'BIAYA KLAIM PDPKS';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_pdpks' and ORG='$org_v' AND DELETE_MARK = '0'  AND to_date('$tanggal_invoice_v', 'DD-MM-YY') BETWEEN to_date(VALID_FROM, 'DD-MM-YY') AND to_date(VALID_TO, 'DD-MM-YY')";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_pdpks = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_pdpks = $row['KETERANGAN'];
                    $no_gl_claim_pdpks = $row['NO_GL'];
                    $tax_pdpks = $row['TAX_CODE'];
                    $rate_pdpks = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_pdpks = $klaim_pdpks_new_v * $rate_pdpks;
                    $total_pdpks = $klaim_pdpks_new_v + $pajak_pdpks;
                    $total_gl_klaim2 = (-1) * $klaim_pdpks_new_v;
                    $pajak_pdpks2 = (-1) * $pajak_pdpks;
                    $pajak_shp_cost+=$pajak_pdpks2;
                    $total_pdpks2 = $total_gl_klaim2 + $pajak_pdpks2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_pdpks;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_pdpks;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_pdpks; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_pdpks", "$nama_claim_pdpks", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_pdpks", "$no_gl_claim_pdpks", "$org_v", "$nama_org_v", "$tax_pdpks", "$pajak_pdpks2", "$total_pdpks2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_pdpks_new_v;
                    //$pass_trn += $klaim_rezak_new_v;
                }
            }// end jika klaim normal lebih besar dari pada klaim lebih
            // jika klaimlebih > klaim normal maka tidak dianggap 0/ tidak ada klaim 
        }
        //List Pajak Vendor
        $fce21a = $sap->NewFunction("Z_ZCMM_VENDOR_WITHTAXTYPE2"); //perubahan rfc
        if ($fce21a == false) {
            $sap->PrintStatus();
            exit;
        }
        $fce21a->FI_BUKRS = $org_v;
        $fce21a->FI_LIFNR = sprintf("%010s", trim($vendor_v));
        $fce21a->Call();
        unset($sPajakVendor);
        if ($fce21a->GetStatus() == SAPRFC_OK) {
            $fce21a->FT_WITH_TAX->Reset();
            while ($fce21a->FT_WITH_TAX->Next()) {
                $sPajakVendor[$fce21a->FT_WITH_TAX->row["WITHT"]] = $fce21a->FT_WITH_TAX->row["TEXT40"];
            }
        }
        $fce21a->Close();

        //Surat Keterangan Bebas Pemotongan Dana/Pemungutan PPh23 maka tidak perlu dipotong pph23
        if ($skbp_vyi != 'YES') {
            if (date("Ymd") >= 20150826) {
                //update PPh Pasal 23 diantaranya adalah Jasa pengangkutan/ekspedisi, Jasa Freight Forwarding, Jasa Loading & Unloading.
                // 26 Agustus 2015
                $fce2 = $sap->NewFunction("Z_ZCSD_VENDOR"); //perubahan rfc
                if ($fce2 == false) {
                    $sap->PrintStatus();
                    exit;
                }
                if ($org_v == "7900")
                    $ktokk2 = "4100";
                if ($org_v == "2000")
                    $ktokk2 = "4100";
                if ($org_v == "7000")
                    $ktokk2 = "4100";
                if ($org_v == "5000")
                    $ktokk2 = "4100";
                if ($org_v == "3000")
                    $ktokk2 = "4200";
                if ($org_v == "4000")
                    $ktokk2 = "4100";

                $fce2->XKTOKK = $ktokk2;
                $fce2->XDLGRP = "";
                $fce2->XLIFNR = sprintf("%010s", trim($vendor_v));
                $fce2->Call();
                unset($statusNPWPvendor);
                if ($fce2->GetStatus() == SAPRFC_OK) {
                    $fce2->RETURN_DATA->Reset();
                    while ($fce2->RETURN_DATA->Next()) {
                        // $statusNPWPvendor = $fce2->RETURN_DATA->row["STCEG"];
                        $statusNPWPvendor = $fce2->RETURN_DATA->row["STCD3"];
                        $statusNITKUvendor = $fce2->RETURN_DATA->row["STCD2"] . $fce2->RETURN_DATA->row["STCD3"];
                    }
                }
                $fce2->Close();
                $statusX_WITHTAX = '60';
                $kode_pph = 'SG0Z160';
                if ($statusNPWPvendor == '') {
                    $statusX_WITHTAX = 'C6';
                    $kode_pph = 'SG00Z16';
                } else {
                    $kode_pph = 'SG0Z160';
                }

                $mialo4 = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_pph' and ORG='$org_v' AND DELETE_MARK = '0'";
                $query4 = oci_parse($conn, $mialo4);
                oci_execute($query4);
                $row4 = oci_fetch_array($query4);
                $nama_claimpph = $row4['NAMA_KOMPONEN'];
                $keterangan_claimpph = $row4['KETERANGAN'];
                $no_gl_claimpph = $row4['NO_GL'];
                $taxpph = $row4['TAX_CODE'];
                $ratepph = $row4['RATE'];
                $prctrpph = $row4['PRCTR'];
                $kostlpph = $row4['COST_CENTER'];
                if ($kode_faktur_wapu == '030' && ($kode_pajak_baru == 'WX' || $kode_pajak_baru == 'WN' || $kode_pajak_baru == 'RN')) {
                    $nilaidpp = $total_shp_cost;
                    $pajakpph = $nilaidpp * $ratepph;
                    $totalpajakplusinvoce = $nilaidpp + $pajakpph;
                    $totalpph = $nilaidpp - $totalpajakplusinvoce;
                } else {
                    $nilaidpp = ($total_shp_cost);
                    $pajakpph = $nilaidpp * $ratepph;
                    $totalpajakplusinvoce = $nilaidpp + $pajakpph;
                    $totalpph = $nilaidpp - $totalpajakplusinvoce;
                }


                if ($nama_claimpph != '' && array_key_exists('Z1', $sPajakVendor)) {
                    //X_WITHTAX
                    $fce->X_WITHTAX->row["SPLIT_KEY"] = '000001'; //'000001';
                    $fce->X_WITHTAX->row["WI_TAX_TYPE"] = 'Z1';
                    $fce->X_WITHTAX->row["WI_TAX_CODE"] = $statusX_WITHTAX;
                    $fce->X_WITHTAX->row["WI_TAX_BASE"] = $nilaidpp;
                    $fce->X_WITHTAX->row["WI_TAX_AMT"] = $pajakpph;
                    $fce->X_WITHTAX->Append($fce->X_WITHTAX->row);
                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    $field_data = array("$kode_pph", "$nama_claimpph", "$prctrpph", "$no_invoice_v", "$totalpph", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claimpph", "$no_gl_claimpph", "$org_v", "$nama_org_v", "$taxpph", "$pajakpph", "$totalpph");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);
                }
            }
        } else {
             $fce2 = $sap->NewFunction("Z_ZCSD_VENDOR"); //perubahan rfc
            if ($fce2 == false) {
                $sap->PrintStatus();
                exit;
            }
            if ($org_v == "7900")
                $ktokk2 = "4100";
            if ($org_v == "2000")
                $ktokk2 = "4100";
            if ($org_v == "7000")
                $ktokk2 = "4100";
            if ($org_v == "5000")
                $ktokk2 = "4100";
            if ($org_v == "3000")
                $ktokk2 = "4200";
            if ($org_v == "4000")
                $ktokk2 = "4100";

            $fce2->XKTOKK = $ktokk2;
            $fce2->XDLGRP = "";
            $fce2->XLIFNR = sprintf("%010s", trim($vendor_v));
            $fce2->Call();
            unset($statusNPWPvendor);
            if ($fce2->GetStatus() == SAPRFC_OK) {
                $fce2->RETURN_DATA->Reset();
                while ($fce2->RETURN_DATA->Next()) {
                    // $statusNPWPvendor = $fce2->RETURN_DATA->row["STCEG"];
                    $statusNPWPvendor = $fce2->RETURN_DATA->row["STCD3"];
                    $statusNITKUvendor = $fce2->RETURN_DATA->row["STCD2"] . $fce2->RETURN_DATA->row["STCD3"];
                }
            }
            $fce2->Close();
        }

        //penambahan pajak PPH pasal 15 untuk 410082 (VARIA USAHA LINTAS SEGARA, PT.)
        //@liyantanto 16-11-2015 Naik 17-11-2015
        // $kodepajakZmv = 'Z5';
        // if (array_key_exists($kodepajakZmv, $sPajakVendor)) {
        //     $kode_pph15 = 'SG000Z5';
        //     $mialo4 = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_pph15' and ORG='$org_v' AND DELETE_MARK = '0'";
        //     $query4 = oci_parse($conn, $mialo4);
        //     oci_execute($query4);
        //     $row4 = oci_fetch_array($query4);
        //     $nama_claimpph = $row4['NAMA_KOMPONEN'];
        //     $keterangan_claimpph = $row4['KETERANGAN'];
        //     $no_gl_claimpph = $row4['NO_GL'];
        //     $taxpph = $row4['TAX_CODE'];
        //     $ratepph = $row4['RATE'];
        //     $prctrpph = $row4['PRCTR'];
        //     $kostlpph = $row4['COST_CENTER'];

        //     $nilaidpp = $total_shp_cost;
        //     $pajakpph = $nilaidpp * $ratepph;
        //     $totalpajakplusinvoce = $nilaidpp + $pajakpph;
        //     $totalpph = $nilaidpp - $totalpajakplusinvoce;

        //     //X_WITHTAX
        //     $fce->X_WITHTAX->row["SPLIT_KEY"] = '000001'; //'000002';
        //     $fce->X_WITHTAX->row["WI_TAX_TYPE"] = $kodepajakZmv;
        //     $fce->X_WITHTAX->row["WI_TAX_CODE"] = '01';
        //     $fce->X_WITHTAX->row["WI_TAX_BASE"] = $nilaidpp;
        //     $fce->X_WITHTAX->row["WI_TAX_AMT"] = $pajakpph;
        //     $fce->X_WITHTAX->Append($fce->X_WITHTAX->row);
        //     $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
        //     $field_data = array("$kode_pph15", "$nama_claimpph", "$prctrpph", "$no_invoice_v", "$totalpph", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claimpph", "$no_gl_claimpph", "$org_v", "$nama_org_v", "$taxpph", "$pajakpph", "$totalpph");
        //     $tablename = "EX_KOMPONEN_INV";
        //     $fungsi->insert($conn, $field_names, $field_data, $tablename);
        // }

        $jumlahd = $_POST['jumlahd'];
        $komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
        $total_d = 0;

         //komponen surcharge SAP by muhammad wardana
        $komponen_biaya_ds1 = $_POST['komponen_biaya_ds1'];
        $total_ds1 = 0;

        if (isset($_POST['komponen_biaya_ds1']) and $_POST['komponen_biaya_ds1'] != "" and $_POST['nilaids1'] != '0') {
            $ke_gl++;

            $komponen_biaya_ds1 = $_POST['komponen_biaya_ds1'];
            $nama_komponen_ds1 = $_POST['nama_komponen_ds1'];
            $nilaids1 = $_POST['nilaids1'];
            $keterangan_ds1 = $_POST['keterangan_ds1'];
            $pajak_ds1 = $_POST['pajak_ds1'];
            $no_gl_ds1 = $_POST['no_gl_ds1'];
            $total_ds1+=$nilaids1;

            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_ds1' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $no_gl_claim = $row['NO_GL'];
            $rate_claim = $row['RATE'];
            $cc_biaya = $row['COST_CENTER'];
            $prctr_biaya = $row['PRCTR'];
            $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

            $pajak_claim = $nilaids1 * $rate_claim;
            $total_claim = $nilaids1 + $pajak_claim;
            $pajak_shp_cost+=$pajak_claim;

            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; 
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaids1;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'S'; 
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_ds1;
            if ($prctr_biaya != "")
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
            if ($cc_biaya != "")
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; 

            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            $field_data = array("$komponen_biaya_ds1", "$nama_komponen_ds1", "$kostl_v", "$no_invoice_v", "$nilaids1", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_ds1", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_ds1", "$pajak_claim", "$total_claim");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
        }

        if (isset($_POST['komponen_biaya_d1']) and $_POST['komponen_biaya_d1'] != "") {
            $ke_gl++;

            $komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
            $nama_komponen_d1 = $_POST['nama_komponen_d1'];
            $nilaid1 = $_POST['nilaid1'];
            $keterangan_d1 = $_POST['keterangan_d1'];
            $pajak_d1 = $_POST['pajak_d1'];
            $no_gl_d1 = $_POST['no_gl_d1'];
            $total_d+=$nilaid1;

            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d1' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $no_gl_claim = $row['NO_GL'];
            $rate_claim = $row['RATE'];
            $cc_biaya = $row['COST_CENTER'];
            $prctr_biaya = $row['PRCTR'];
            $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

            $pajak_claim = $nilaid1 * $rate_claim;
            $total_claim = $nilaid1 + $pajak_claim;
            $pajak_shp_cost+=$pajak_claim;

            //gl account data entri    
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaid1;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'S'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_d1;
            if ($prctr_biaya != "")
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
            if ($cc_biaya != "")
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
//		if($pajak_d1=="VN")
//		$fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = $nilaid1;//$kostl_v;
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$komponen_biaya_d1", "$nama_komponen_d1", "$kostl_v", "$no_invoice_v", "$nilaid1", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_d1", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_d1", "$pajak_claim", "$total_claim");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);

            for ($i = 2; $i <= $jumlahd; $i++) {
                $ke_gl++;
                $panjang = strlen(strval($ke_gl));
                if ($panjang == 1)
                    $nogl_ke = '00000' . $ke_gl;
                if ($panjang == 2)
                    $nogl_ke = '0000' . $ke_gl;
                if ($panjang == 3)
                    $nogl_ke = '000' . $ke_gl;
                if ($panjang == 4)
                    $nogl_ke = '00' . $ke_gl;
                if ($panjang == 5)
                    $nogl_ke = '0' . $ke_gl;
                if ($panjang == 6)
                    $nogl_ke = $ke_gl;


                $komp_d = "komponen_biaya_d" . $i;
                $nama_komp_d = "nama_komponen_d" . $i;
                $komp_nilaid = "nilaid" . $i;
                $komp_ketd = "keterangan_d" . $i;
                $komp_pajakd = "pajak_d" . $i;
                $komp_gld = "no_gl_d" . $i;

                $komponen_biaya_d = $_POST[$komp_d];
                $nama_komponen_d = $_POST[$nama_komp_d];
                $nilaid = $_POST[$komp_nilaid];
                $keterangan_d = $_POST[$komp_ketd];
                $pajak_d = $_POST[$komp_pajakd];
                $no_gl_d = $_POST[$komp_gld];
                $total_d+=$nilaid;

                $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d' and ORG='$org_v' AND DELETE_MARK = '0'";
                $query = oci_parse($conn, $mialo);
                oci_execute($query);
                $row = oci_fetch_array($query);
                $no_gl_claim = $row['NO_GL'];
                $rate_claim = $row['RATE'];
                $cc_biaya = $row['COST_CENTER'];
                $prctr_biaya = $row['PRCTR'];
                $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

                $pajak_claim = $nilaid * $rate_claim;
                $total_claim = $nilaid + $pajak_claim;
                $pajak_shp_cost+=$pajak_claim;

                //gl account data entri    
                $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
                $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaid;
                $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'S'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_d;
                if ($prctr_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
                if ($cc_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
                $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                $field_data = array("$komponen_biaya_d", "$nama_komponen_d", "$kostl_v", "$no_invoice_v", "$nilaid", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_d", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_d", "$pajak_claim", "$total_claim");
                $tablename = "EX_KOMPONEN_INV";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);
            }
        }

        $jumlahk = $_POST['jumlahk'];
        $komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
        $total_k = 0;

        //DENDA K3
        // Mengambil data denda k3 berdasarkan invoice yg terpilih
        $sqlk3 = "SELECT * FROM EX_DENDAK3_SALDO WHERE NO_INVOICE LIKE '$no_invoice_v'";
        $queryk3 = oci_parse($conn, $sqlk3);
        oci_execute($queryk3);

        $total_denda = 0; // variabel jumlah dari keseluruhan denda
        $no_dokumen = '';

        while ($rowk3 = oci_fetch_array($queryk3)) {
            $no_doc = $rowk3['NO_DOC_DENDA'];
            $denda = floatval($rowk3['KREDIT']);

            $no_dokumen .= " ~$no_doc";
            $total_denda += $denda;
        }
        if ($total_denda > 0) {


            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            // Mengambil informasi dari denda k3 di tabel komponen biaya
            $kode_k3 = 'SG-K3-2017';
            $mialo4 = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_k3' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query4 = oci_parse($conn, $mialo4);
            oci_execute($query4);

            $row4 = oci_fetch_array($query4);

            $nama_komp_k3 = $row4['NAMA_KOMPONEN'];
            $kode_komp_k3 = $row4['KODE_KOMPONEN'];
            $keterangan_komp_k3 = $row4['KETERANGAN'] . $no_dokumen;
            $no_gl_komp_k3 = $row4['NO_GL'];
            $taxk3 = $row4['TAX_CODE'];
            $ratek3 = $row4['RATE'];
            $prctrk3 = $row4['PRCTR'];
            $kostlk3 = $row4['COST_CENTER'];
            // $tax_basmountex=trim($row['TAX_BASE_AMOUNT']); 


            $nilaidpp = $total_shp_cost;
            $pajakk3 = $nilaidpp * $ratek3;
            // $totalpajakplusinvoce=$nilaidpp+$pajakk3;
            $totalk3 = $total_denda * -1;

            // pada proses ini denda k3 akan di masukkan kedalam variabel $_POST yag dibawa ketika generate ppl
            // 
            // Kondisi ini ada lah ketika tidak terdapat sama sekali post data untuk komponen biaya jenis terakhir
            // maka denda k3 akan otomatis dimasukkan sebagai komponen_biaya_k1
            if (!isset($_POST['komponen_biaya_k1'])) {

                $_POST['komponen_biaya_k1'] = $kode_komp_k3;
                $_POST['nama_komponen_k1'] = $nama_komp_k3;
                $_POST['nilaik1'] = $total_denda;
                $_POST['keterangan_k1'] = $keterangan_komp_k3;
                $_POST['pajak_k1'] = $taxk3;
                $_POST['no_gl_k1'] = $no_gl_komp_k3;
            } else {
                // Kondisi ini dilakukan ketika terdapat komponen biaya lain yang di inputkan dalam form ketika proses
                // generate ppl, sehingga untuk komponen biaya dimasukkan kedalam $_POST yang seolah -lah denda k3 dimasukkan
                // paling terakhir dalam proses post 
                $jumlahk++;

                $_POST['komponen_biaya_k' . $jumlahk] = $kode_komp_k3;
                $_POST['nama_komponen_k' . $jumlahk] = $nama_komp_k3;
                $_POST['nilaik' . $jumlahk] = $total_denda;
                $_POST['keterangan_k' . $jumlahk] = $keterangan_komp_k3;
                $_POST['pajak_k' . $jumlahk] = $taxk3;
                $_POST['no_gl_k' . $jumlahk] = $no_gl_komp_k3;
            }

            // $show_ket .= "<br>{$_POST['komponen_biaya_k1']} > jumlahk $i" ." < {$_POST['komponen_biaya_k'.$i]} >>>>  " ;
        }

        // $show_ket .= "jumlahk $jumlahk";
        if (isset($_POST['komponen_biaya_k1']) and $_POST['komponen_biaya_k1'] != "") {
            $komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
            $nama_komponen_k1 = $_POST['nama_komponen_k1'];
            $nilaik1 = $_POST['nilaik1'];
            $keterangan_k1 = $_POST['keterangan_k1'];
            $pajak_k1 = $_POST['pajak_k1'];
            $no_gl_k1 = $_POST['no_gl_k1'];
            $total_k+=$nilaik1;
            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k1' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $no_gl_claim = $row['NO_GL'];
            $rate_claim = $row['RATE'];
            $cc_biaya = $row['COST_CENTER'];
            $prctr_biaya = $row['PRCTR'];
            $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

            $pajak_claim = $nilaik1 * $rate_claim;
            $total_claim = $nilaik1 + $pajak_claim;

            $pajak_claim2 = (-1) * $nilaik1 * $rate_claim;
            $nilai_claim = (-1) * $nilaik1;
            $total_claim2 = $nilai_claim + pajak_claim2;

            $pajak_shp_cost+=$pajak_claim2;

            //gl account data entri    
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaik1;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_k1;
            if ($prctr_biaya != "")
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
            if ($cc_biaya != "")
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
            if ($tax_basmountex != '') {
                $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaik1 * $tax_basmountex);
            }
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);


            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$komponen_biaya_k1", "$nama_komponen_k1", "$prctr_biaya", "$no_invoice_v", "$nilai_claim", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_k1", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_k1", "$pajak_claim2", "$total_claim2");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);

            for ($i = 2; $i <= $jumlahk; $i++) {
                $ke_gl++;
                $panjang = strlen(strval($ke_gl));
                if ($panjang == 1)
                    $nogl_ke = '00000' . $ke_gl;
                if ($panjang == 2)
                    $nogl_ke = '0000' . $ke_gl;
                if ($panjang == 3)
                    $nogl_ke = '000' . $ke_gl;
                if ($panjang == 4)
                    $nogl_ke = '00' . $ke_gl;
                if ($panjang == 5)
                    $nogl_ke = '0' . $ke_gl;
                if ($panjang == 6)
                    $nogl_ke = $ke_gl;

                $komp_k = "komponen_biaya_k" . $i;
                $nama_komp_k = "nama_komponen_k" . $i;
                $komp_nilaik = "nilaik" . $i;
                $komp_ketk = "keterangan_k" . $i;
                $komp_pajakk = "pajak_k" . $i;
                $komp_glk = "no_gl_k" . $i;

                $komponen_biaya_k = $_POST[$komp_k];
                $nama_komponen_k = $_POST[$nama_komp_k];
                $nilaik = $_POST[$komp_nilaik];
                $keterangan_k = $_POST[$komp_ketk];
                $pajak_k = $_POST[$komp_pajakk];
                $no_gl_k = $_POST[$komp_glk];
                $total_k+=$nilaik;

                $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k' and ORG='$org_v' AND DELETE_MARK = '0'";
                $query = oci_parse($conn, $mialo);
                oci_execute($query);
                $row = oci_fetch_array($query);
                $no_gl_claim = $row['NO_GL'];
                $rate_claim = $row['RATE'];
                $cc_biaya = $row['COST_CENTER'];
                $prctr_biaya = $row['PRCTR'];
                $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

                $pajak_claim = $nilaik * $rate_claim;
                $total_claim = $nilaik + $pajak_claim;

                $pajak_claim2 = (-1) * $nilaik * $rate_claim;
                $nilai_claim = (-1) * $nilaik;
                $total_claim2 = $nilai_claim + $pajak_claim2;

                $pajak_shp_cost+=$pajak_claim2;

                //gl account data entri    
                $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
                $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaik;
                $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_k;
                if ($prctr_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
                if ($cc_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
                if ($tax_basmountex != '') {
                    $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaik * $tax_basmountex);
                }
                $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);


                $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                $field_data = array("$komponen_biaya_k", "$nama_komponen_k", "$prctr_biaya", "$no_invoice_v", "$nilai_claim", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_k", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_k", "$pajak_claim2", "$total_claim2");
                $tablename = "EX_KOMPONEN_INV";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);
            }
        }
        //sratr !!!!!!!!!!!!!!!!!
        //POTONGAN OA EKSPEDITUR //
        $ketPOX = '';
        $nilaiPOEX = 0;
        $jumPoex = 0;
        $sqlPOEX = "
               SELECT
                    MPOAT.*,MPO.KETERANGAN
            FROM
                    M_POTONGAN_OA_TRANS MPOAT
            JOIN M_POTONGAN_OA MPO ON MPO.BA_NUMBER = MPOAT.BA_NUMBER
            WHERE
                    MPOAT.NO_INVOICE = '$no_invoice'
            AND MPOAT.IS_DELETE = '0'
            and ROWNUM = 1
          ";

        $queryPOEX = oci_parse($conn, $sqlPOEX);
        oci_execute($queryPOEX);
        while ($rowkPOEX = oci_fetch_array($queryPOEX)) {
            $nilaiPOEX += $rowkPOEX['NILAI_TRANSAKSI'];
            $ketPOXNum .=$rowkPOEX['BA_NUMBER'] . '';
            $ketNum = $rowkPOEX['NUM'].'/'.$no_invoice.'/'.$rowkPOEX['KETERANGAN'];
            $jumPoex++;
        }

        if ($nilaiPOEX > 0) {
            //get komponen biaya
            if($org_v == '7000'){
                $kodePOEX = 'DistransSI';
            }else if ($org_v == '5000'){
                $kodePOEX = 'DistransSG';
            }
            $sqlKomPOEX = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kodePOEX' and ORG='$org_v' AND DELETE_MARK = '0'";
            $queryKomPOEX = oci_parse($conn, $sqlKomPOEX);
            oci_execute($queryKomPOEX);
            $rowPOEX = oci_fetch_array($queryKomPOEX);

            $kode_komp_poex = $rowPOEX['KODE_KOMPONEN'];
            $namaKomponenPOEX = $rowPOEX['NAMA_KOMPONEN'].'('.$ketPOXNum.')';
            $namaGL = $rowPOEX['NAMA_GL'];
            $noGLPoex = $rowPOEX['NO_GL'];
            $costCenter = $rowPOEX['COST_CENTER'];
            $taxBaseAmountPoex = trim($row['TAX_BASE_AMOUNT']);
            $ketPOEX = $rowPOEX['KETERANGAN'];
            $taxCodePOEX = $rowPOEX['TAX_CODE'];
            $kodeKomponenPOEX = $rowPOEX['KODE_KOMPONEN'];
            $profitCntrPOex = $rowPOEX['PRCTR'];
            $ketPajak = $rowPOEX['NAMA_TAX'];
            $noInv = $no_invoice;
            $subTot = 0 - $nilaiPOEX;

            if (!isset($_POST['komponen_biaya_poex'])) {
                $_POST['komponen_biaya_poex'] = $kode_komp_poex;
                $_POST['nama_komponen_poex'] = $namaKomponenPOEX;
                $_POST['nilaikpoex'] = $nilaiPOEX;
                $_POST['keterangan_poex'] = $ketPOEX;
                $_POST['pajak_poex'] = $taxCodePOEX;
                $_POST['ketpajak_poex'] = $ketPajak;
                $_POST['no_gl_poex'] = $noGLPoex;
            }
        }

        if (isset($_POST['komponen_biaya_poex']) and $_POST['komponen_biaya_poex'] != "") {
            $komponen_biaya_poex = $_POST['komponen_biaya_poex'];
            $nama_komponen_poex = $_POST['nama_komponen_poex'];
            $nilaikpoex = $_POST['nilaikpoex'];
            $keterangan_poex = $_POST['keterangan_poex'];
            $pajak_poex = $_POST['pajak_poex'];
            $no_gl_poex = $_POST['no_gl_poex'];
            $ketpajak_poex = $_POST['ketpajak_poex'];

            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke;
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_poex; // ?
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaiPOEX;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; // ?
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $taxCodePOEX; // ?
            if ($profitCntrPOex != "") {//profit center ?
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $profitCntrPOex;
            }
            if ($costCenter != "") { //costcenter ?
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $costCenter;
            }
            if ($taxBaseAmountPoex != '') {
                $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaiPOEX * $taxBaseAmountPoex);
            }
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'NO_INVOICE', 'DELETE_MARK', 'CREATE_DATE', 'CREATED_BY', 'SUB_TOTAL', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            $field_data = array("$komponen_biaya_poex", "$nama_komponen_poex", "$noInv", "0", "SYSDATE", "$user_name", "$subTot", "SYSDATE", "$user_name", "$ketNum", "$no_gl_poex", "$org_v", "$nama_org_v", "$taxCodePOEX", '', $subTot);
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
        } //
        //end !!!!!!!!!!!!!!!!!
        if ($total_glondong < 1)
            $total_glondong = 0;

        $total_invoice = $total_shp_cost + $total_d - $total_k - $total_gl_klaim - $total_glondong - $nilaiPOEX;

        #add by iljas - Wapu 17.07.2012
        //pajak 11%
        if ($kode_pajak_baru != 'WN' && $kode_pajak_baru != 'WX' && $kode_pajak_baru != 'RN' && $kode_pajak_baru != 'WX' && $kode_pajak_baru!= 'YY' && $kode_pajak_baru != 'DN') {
			$total_invoice += round($pajak_shp_cost, 0); //termasuk pajak 10
		}
        $fce->X_HEADERDATA["GROSS_AMOUNT"] = $total_invoice;
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $nomor_inv = $fce->X_INVOICEDOCNUMBER;
            $nomor_acc = $fce->X_ACCNUMBER;
            $tipeBAPIinvoce = trim($fce->X_RETURN["TYPE"]);
            $msgBAPIinvoce = trim($fce->X_RETURN["MESSAGE"]);
            $tipe = $fce->X_RETURN["TYPE"];
            $msg = $fce->X_RETURN["MESSAGE"];
            $id_ret = $fce->X_RETURN["ID"];
            $num_ret = $fce->X_RETURN["NUMBER"];
            $show_ket.= $tipe;
            $show_ket.= ' ';
            $show_ket.= $id_ret;
            $show_ket.= ' ';
            $show_ket.= $num_ret;
            $show_ket.= ' ';
            $show_ket.= $msg;
            $show_ket.= ' ';
            $show_ket.= ' Dengan No' . $nomor_inv . " Dan No ACC " . $nomor_acc;
            $show_ket.= '<br>';

            $fce->Close();
        }
        $sap->Close();

        if ($nomor_inv != "" && $tipeBAPIinvoce == "S") {
            $status = "INVOICED"; //else $status="PROGRESS";		

            $field_names = array('NO_INV_SAP', 'APPROVE_BY', 'STATUS', 'TANGGAL_APPROVE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ACCOUNTING_DOC');
            $field_data = array("$nomor_inv", "$user_name", "$status", "SYSDATE", "SYSDATE", "$user_name", "$nomor_acc");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_v");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            //dendak3 update table temp

            $field_names = array('NO_PPL', 'DATE_PPL', 'DATE_UPDATE', 'UPDATE_BY', 'NOTE');
            $field_data = array("$nomor_inv", "SYSDATE", "SYSDATE", "$user_name", "generate_ppl");
            $tablename = "EX_DENDAK3_SALDO";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_v");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $no_rek = $_POST['no_rek'];
            $nama_bank = $_POST['nama_bank'];
            $cabang_bank = $_POST['cabang_bank'];
            // $no_rek = $_POST['no_rek'];
            //$sql_cetak= "SELECT * FROM EX_SET_CETAK WHERE ORG ='$userOrg'";
            $sql_cetak = "SELECT * FROM EX_SET_CETAK WHERE ORG ='$orgInvoice'";
            $query_cetak = oci_parse($conn, $sql_cetak);
            oci_execute($query_cetak);

            $row_cetak = oci_fetch_array($query_cetak);
            $unit_v = $row_cetak[UNIT_KERJA];
            $lokasi_v = $row_cetak[LOKASI];
            $kepala_v = $row_cetak[KEPALA];
            $atasan_v = $row_cetak[ATASAN];
            $verifikasi_v = $row_cetak[VERIFIKASI];
            $lead_verifikasi_v = $row_cetak[LEAD_VERIFIKASI];
            $email_kasi = $row_cetak[EMAIL_KASI];
            $email_kabiro = $row_cetak[EMAIL_KABIRO];
            $tahun_nr = date("Y");
            if ($pass_trn > 1)
                $flag = "";
            else
                $flag = "OK";
            $field_names = array('NO_INVOICE_SAP', 'NO_REKENING', 'BANK', 'CURR', 'BANK_CABANG', 'TGL_APPROVED', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KEPALA', 'ATASAN', 'VERIFIKASI', 'LEAD_VERIFIKASI', 'UNIT', 'LOKASI', 'ACCOUNTING_DOC', 'TAHUN', 'BVTYP', 'FLAG_TRN', 'NPWP_VENDOR', 'SKBP', 'EMAIL_KASI', 'EMAIL_KABIRO', 'NITKU');
            $field_data = array("$nomor_inv", "$no_rek", "$nama_bank", "IDR", "$cabang_bank", "SYSDATE", "SYSDATE", "$user_name", "$kepala_v", "$atasan_v", "$verifikasi_v", "$lead_verifikasi_v", "$unit_v", "$lokasi_v", "$nomor_acc", "$tahun_nr", "$bvtyp", "$flag", "$statusNPWPvendor", "$skbp_vyi", "$email_kasi", "$email_kabiro", "$statusNITKUvendor");
            $tablename = "EX_INVOICE";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_v");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            //start !!!!!!!!!!!!!!!!!!!!!!!!!!!  
            //UPDATE TO MASTER POTONGAN OA          //
            $sqlUPOEX2 = "SELECT
                                        *
                                FROM
                                        M_POTONGAN_OA_TRANS
                                WHERE
                                        NO_INVOICE = '$no_invoice'
                                AND IS_DELETE = '0'";
            $queryPOEX2 = oci_parse($conn, $sqlUPOEX2);
            oci_execute($queryPOEX2);
            while ($rowkPOEX = oci_fetch_array($queryPOEX2)) {
                $nilaiPOEX = $rowkPOEX['NILAI_POTONGAN'];
                $baNumber = $rowkPOEX['BA_NUMBER'];
                $idPOEX = $rowkPOEX['ID'];
            }
            $column_names = array('LOG', 'LOG_DATE', 'UPDATE_BY', 'UPDATE_DATE', 'NO_PPL');
            $column_data = array("generate_ppl", "SYSDATE", "$user_name", "SYSDATE", "$nomor_inv");
            $tableName = "M_POTONGAN_OA_TRANS";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice");
            $fungsi->update($conn, $column_names, $column_data, $tableName, $field_id, $value_id); //
            
            //end !!!!!!!!!!!!!!!!!!!!!!!!!!!  
        }
        //echo "hgasdkjafha";
        $habis = "lihat_invoice_hdr.php";
        break;

//============================================================================================================================
    case "reverse_ppl":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $no_invoice_x = $_POST['no_invoice_x'];
        $no_invoice_sap = $_POST['no_invoice_sap'];
        $gjahr_x = $_POST['tahun'];
        $alasan = $_POST['alasan'];
        $reason = $_POST['reason'];
        $no_accdoc = $_POST['no_accdoc'];
        if ($reason == "" or $reason == "0" or $reason == "00")
            $reason = "01";

        $sap = new SAPConnection();
        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            echo $sap->PrintStatus();
            exit;
        }

        $fce = $sap->NewFunction("Z_ZAPPSD_CANCEL_INVOICE");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
        $fce->I_BELNR = $no_invoice_sap;
        $fce->I_GJAHR = $gjahr_x;
        $fce->I_REF = $reason;
        $show_ket .= $reason;
        // add by iljas 16 des 2011
        #echo "<br>Reverse Miro : ";
        $sql_acc = "SELECT ORG, ACCOUNTING_DOC FROM EX_TRANS_HDR WHERE NO_INVOICE = '$no_invoice_x' AND DELETE_MARK = '0' AND ACCOUNTING_DOC IS NOT NULL AND ROWNUM=1";
        $query_acc = oci_parse($conn, $sql_acc);
        oci_execute($query_acc);
        while ($row_acc = oci_fetch_array($query_acc)) {
            $fce->T_BELNR->row["BUKRS"] = $row_acc[ORG];
            $fce->T_BELNR->row["BELNR"] = $no_accdoc;
            $fce->T_BELNR->row["GJAHR"] = date("Y");
            $fce->T_BELNR->Append($fce->T_BELNR->row);
            $fce->I_BUKRS = $row_acc[ORG];
        }
        // add by iljas 16 des 2011
        $sql_acc = "SELECT * FROM EX_FB60 WHERE NO_INVOICE = '$no_invoice_x' AND DELETE_MARK = '0' ";
        $query_acc = oci_parse($conn, $sql_acc);
        oci_execute($query_acc);
        #echo "<br>Reverse FB60 :";
        while ($row_acc2 = oci_fetch_array($query_acc)) {
            $kunci1 = '';
            $kunci2 = '';
            $kunci3 = '';
            $awkey = $row_acc2[AWKEY];
            $kunci1 = substr($awkey, 0, 10);  //kode fb60
            $kunci2 = substr($awkey, 10, 4);   //com
            $kunci3 = substr($awkey, 14, 4); //tahun
            $fce->T_BELNR2->row["BUKRS"] = $kunci2; //company
            $fce->T_BELNR2->row["BELNR"] = $kunci1; //nomor fb60
            $fce->T_BELNR2->row["GJAHR"] = $kunci3; //tahun
            $fce->T_BELNR2->Append($fce->T_BELNR2->row);
        }
        $fce->I_NO_TURUNAN = $no_invoice_x;
        $fce->I_USERNAME = $user_name;
        #echo "<br>STOP debug by sisfo";
        #exit;

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $no_cancel = $fce->INVOICEDOCNUMBER_REVERSAL;
            $nomor_ref_num = $fce->NOACCREVERSAL_NUM;  // add by iljas 16 des 2011

            $fce->RETURN->Reset();
            while ($fce->RETURN->Next()) {
                $statusT = $fce->RETURN->row["TYPE"];
            }

            if ($no_cancel != "" or $statusT == 'S') {

                $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("1", "SYSDATE", "$user_name");
                $tablename = "EX_FB60";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("1", "SYSDATE", "$user_name");
                $tablename = "EX_FB60_DTL";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                $field_names = array('NO_INV_SAP', 'APPROVE_BY', 'STATUS', 'TANGGAL_APPROVE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("", "", "PROGRESS", "", "SYSDATE", "$user_name");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                //DENDA K3
                $field_names = array('NOTE', 'NO_PPL', 'UPDATE_BY', 'DATE_UPDATE');
                $field_data = array('Reverse ppl', '', "$user_name", "SYSDATE");
                $tablename = "EX_DENDAK3_SALDO";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                //start !!!!!!!!!!!!!!!!!!
                //POEX
                $field_names = array('LOG', 'NO_PPL', 'UPDATE_BY', 'UPDATE_DATE', 'LOG_DATE'); //
                $field_data = array('reverse ppl', '', "$user_name", "SYSDATE", "SYSDATE");
                $tablename = "M_POTONGAN_OA_TRANS";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id); //
                 
                //end !!!!!!!!!!!!!!!!!!

                $field_names = array('NO_INVOICE_SAP', 'CURR', 'TGL_APPROVED', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KEPALA', 'ATASAN', 'VERIFIKASI', 'LEAD_VERIFIKASI', 'UNIT', 'LOKASI', 'FLAG_TRN');
                $field_data = array("", "", "", "SYSDATE", "$user_name", "", "", "", "", "", "", "");
                $tablename = "EX_INVOICE";
                $field_id = array('NO_INVOICE');
                $value_id = array("$no_invoice_x");
                $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                $field_names = array('NO_CANCEL', 'NO_INVOICE', 'NO_INVOICE_SAP', 'CREATE_DATE', 'CREATED_BY', 'REASON');
                $field_data = array("$no_cancel", "$no_invoice_x", "$no_invoice_sap", "SYSDATE", "$user_name", "$reason $alasan");
                $tablename = "EX_LOG_CANCEL";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);

                //add by iljas 16 des 2011
                $show_ket .= " NO PPL $no_invoice_sap dengan NO INVOICE $no_invoice_x Berhasil di reverse dengan NO $no_cancel dan <b>Nomor Doc.Clering :</b> $nomor_ref_num<br>";
                $fce->RETURN->Reset();
                while ($fce->RETURN->Next()) {
                    $show_ket .= "<br> " . $fce->RETURN->row["TYPE"];
                    $show_ket .= " " . $fce->RETURN->row["NUMBER"];
                    $show_ket .= " " . $fce->RETURN->row["MESSAGE"];
                }
            } else {
                $fce->RETURN->Reset();
                while ($fce->RETURN->Next()) {
                    $show_ket .= "<br> " . $fce->RETURN->row["TYPE"];
                    $show_ket .= " " . $fce->RETURN->row["NUMBER"];
                    $show_ket .= " " . $fce->RETURN->row["MESSAGE"];
                }
            }
        }
        $fce->Close();
        $sap->Close();
        $habis = "reverse_ppl.php";
        break;
//============================================================================================================================
    case "reverse_ppl_manual":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $no_invoice_x = $_POST['no_invoice_x'];
        $no_invoice_sap = $_POST['no_invoice_sap'];
        $gjahr_x = $_POST['tahun'];
        $alasan = $_POST['alasan'];
        $reason = $_POST['reason'];
        if ($reason == "" or $reason == "0" or $reason == "00")
            $reason = "01";

        $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
        $field_data = array("1", "SYSDATE", "$user_name");
        $tablename = "EX_FB60";
        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice_x");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
        $field_data = array("1", "SYSDATE", "$user_name");
        $tablename = "EX_FB60_DTL";
        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice_x");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $field_names = array('NO_INV_SAP', 'APPROVE_BY', 'STATUS', 'TANGGAL_APPROVE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
        $field_data = array("", "", "PROGRESS", "", "SYSDATE", "$user_name");
        $tablename = "EX_TRANS_HDR";
        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice_x");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $field_names = array('NO_INVOICE_SAP', 'TGL_APPROVED', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KEPALA', 'ATASAN', 'VERIFIKASI', 'LEAD_VERIFIKASI', 'UNIT', 'LOKASI', 'FLAG_TRN');
        $field_data = array("", "", "SYSDATE", "$user_name", "", "", "", "", "", "", "");
        $tablename = "EX_INVOICE";
        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice_x");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

        $field_names = array('NO_CANCEL', 'NO_INVOICE', 'NO_INVOICE_SAP', 'CREATE_DATE', 'CREATED_BY', 'REASON');
        $field_data = array("MANUAL", "$no_invoice_x", "$no_invoice_sap", "SYSDATE", "$user_name", "$reason $alasan");
        $tablename = "EX_LOG_CANCEL";
        $fungsi->insert($conn, $field_names, $field_data, $tablename);

        $show_ket .= " NO PPL $no_invoice_sap dengan NO INVOICE $no_invoice_x Berhasil di reverse dengan NO $no_cancel <br>";
        $habis = "reverse_ppl_manual.php";
        break;
//============================================================================================================================

    case "generate_ppl_turunan":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $no_invoice = $_POST['no_invoice'];

        $sap = new SAPConnection();

        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            echo $sap->PrintStatus();
            exit;
        }

        $sql = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********'  AND SOLD_TO != '**********' GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $total_invoice = 0;
        $oa_semen_v = 0;
        while ($row = oci_fetch_array($query)) {

//		$no_gl_shp_v=$row[NO_GL_SHP];
            $no_invoice_v = $row[NO_INVOICE];
            $no_invoice_ex_v = $row[NO_INV_VENDOR];
            $no_inv_sap_v = $row[NO_INV_SAP];
            $no_acc_doc_v = $row[ACCOUNTING_DOC];
            $warna_plat_v = $row[WARNA_PLAT];
            $vendor_v = $row[VENDOR];

            $sold_to_v = $row[SOLD_TO];
            $klaim_ktg_v = $row[KLAIM_KTG];
            $klaim_rezak_v = $row[KLAIM_REZAK];
            $klaim_semen_v = $row[KLAIM_SEMEN];
            $klaim_all_v = $row[KLAIM_REZAK] + $row[KLAIM_SEMEN];
            $klaim_pdpks_v = $row[KLAIM_PDPKS];
            $kel = $row[KELOMPOK_TRANSAKSI];
            $oa_semen_v += $row[OA_SEMEN];

            $klaim_lebih_v = $row[KLAIM_LEBIH];
            $klaim_pdpks_lebih_v = $row[PDPKS_LEBIH];
            $klaim_lebih_all_v = $klaim_lebih_v;
            $distributor = $row[SOLD_TO];
            $klaim_all_new_v = 0;
            if ($klaim_semen_v > $klaim_lebih_v)
                $klaim_semen_v = $klaim_semen_v - $klaim_lebih_v;
            else
                $klaim_semen_v = 0;

            if ($klaim_rezak_v > 1 or $klaim_semen_v > 1) {
                $sql_bn = "SELECT ORG,NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS,TANGGAL_INVOICE,NAMA_PENGELOLA,PENGELOLA,BVTYP FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND SOLD_TO = '$distributor' ORDER BY ID DESC";
                // STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
                $query_bn = oci_parse($conn, $sql_bn);
                oci_execute($query_bn);
                $row_bn = oci_fetch_array($query_bn);

                $nama_vendor_v = $row_bn[NAMA_VENDOR];
                $nama_pengelola_v = $row_bn[NAMA_PENGELOLA];
                $kode_pengelola_v = $row_bn[PENGELOLA];
                $nama_sold_to_v = $row_bn[NAMA_SOLD_TO];
                $no_rek_dis_v = $row_bn[NO_REK_DIS];
                $nama_bank_dis_v = $row_bn[NAMA_BANK_DIS];
                $cabang_bank_dis_v = $row_bn[BANK_CABANG_DIS];
                $tanggal_invoice_v = $row_bn[TANGGAL_INVOICE];
                $bvtyp_v = $row_bn[BVTYP];
                $org_v = $row_bn[ORG];

                $fce = $sap->NewFunction("Z_ZAPPSD_ACC_DOC_POST");
                if ($fce == false) {
                    $sap->PrintStatus();
                    exit;
                }
                if ($klaim_rezak_v > 1) {
                    $kode_claim_rezak = 'SG0004';
                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' and org='$org_v' AND DELETE_MARK = '0'";
                    $query_gl = oci_parse($conn, $mialo);
                    oci_execute($query_gl);
                    $row_gl = oci_fetch_array($query_gl);
                    $no_gl_claim_rezak = $row_gl['NO_GL'];
                    $nama_claim_rezak = $row_gl['NAMA_KOMPONEN'];
                    $keterangan_claim_rezak = $row_gl['KETERANGAN'];
                    $pajak_claim = $row_gl['TAX_CODE'];
                    $prctr_v = $row_gl['PRCTR'];
                    $kostl_v = $row_gl['COST_CENTER'];
                    $prctr_vKLAMREZAK = $prctr_v;

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_v; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_rezak_v;
                    //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                    $ket_sapin .= " KLAIM REZAK ";
                    $klaim_all_new_v = $klaim_all_new_v + $klaim_rezak_v;
                }

                if ($klaim_semen_v > 1) {
                    $kode_claim_semen = 'SG0003';
                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' and org='$org_v' AND DELETE_MARK = '0'";
                    $query_gl = oci_parse($conn, $mialo);
                    oci_execute($query_gl);
                    $row_gl = oci_fetch_array($query_gl);
                    $no_gl_claim_semen = $row_gl['NO_GL'];
                    $nama_claim_semen = $row_gl['NAMA_KOMPONEN'];
                    $keterangan_claim_semen = $row_gl['KETERANGAN'];
                    $pajak_claim = $row_gl['TAX_CODE'];
                    $prctr_v = $row_gl['PRCTR'];
                    $kostl_v = $row_gl['COST_CENTER'];
                    $prctr_vKLAMSEMEN = $prctr_v;

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_v; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_semen_v;
                    //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                    $ket_sapin .= " KLAIM SEMEN ";
                    $klaim_all_new_v = $klaim_all_new_v + $klaim_semen_v;
                }

                $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = (-1) * $klaim_all_new_v;
                //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);

                $fce->ACCOUNTPAYABLE->row["ITEMNO_ACC"] = '**********';
                $fce->ACCOUNTPAYABLE->row["VENDOR_NO"] = $kode_pengelola_v;
                $fce->ACCOUNTPAYABLE->row["COMP_CODE"] = $org_v; //'2000';
                $fce->ACCOUNTPAYABLE->row["ALLOC_NMBR"] = $no_invoice;
                $fce->ACCOUNTPAYABLE->row["BLINE_DATE"] = date("Ymd");
                $fce->ACCOUNTPAYABLE->row["PYMT_METH"] = 'C';
                $fce->ACCOUNTPAYABLE->row["PMNT_BLOCK"] = '3';
                $fce->ACCOUNTPAYABLE->row["PARTNER_BK"] = $bvtyp_v;
                //	$fce->ACCOUNTPAYABLE->row["ITEM_TEXT"] = $no_invoice;
                //	$fce->ACCOUNTPAYABLE->row["TAX_CODE"] = $no_invoice;
                $fce->ACCOUNTPAYABLE->Append($fce->ACCOUNTPAYABLE->row);

                $fce->DOCUMENTHEADER["BUS_ACT"] = "RMRP";
                $fce->DOCUMENTHEADER["USERNAME"] = $user_name;
                $fce->DOCUMENTHEADER["HEADER_TXT"] = $ket_sapin;
                $fce->DOCUMENTHEADER["COMP_CODE"] = $org_v; //'2000';
                $fce->DOCUMENTHEADER["DOC_DATE"] = date("Ymd");
                $fce->DOCUMENTHEADER["PSTNG_DATE"] = date("Ymd");
                $fce->DOCUMENTHEADER["FISC_YEAR"] = date("Y");
                $fce->DOCUMENTHEADER["FIS_PERIOD"] = "00";
                $fce->DOCUMENTHEADER["DOC_TYPE"] = "KR";

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $obj_key = $fce->OBJ_KEY;
                    $no_doc = substr($obj_key, 0, 10);
                    $tahun = date("Y");
                    if ($obj_key != '$' and strlen($no_doc) > 5) {
                        if ($klaim_rezak_v > 0) {
                            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'KODE_PAJAK', 'PAJAK', 'SUB_TOTAL', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY');
                            $field_data = array("$kode_claim_rezak", "$nama_claim_rezak", "$no_invoice", "$klaim_rezak_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "CLAIM REZAK", "$no_gl_claim_rezak", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMREZAK", "", "", "$klaim_rezak_v", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key");
                            $tablename = "EX_FB60_DTL";
                            $fungsi->insert($conn, $field_names, $field_data, $tablename);
                            $ket_in = " KLAIM REZAK ";
                        }
                        if ($klaim_semen_v > 0) {
                            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'KODE_PAJAK', 'PAJAK', 'SUB_TOTAL', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY');
                            $field_data = array("$kode_claim_semen", "$nama_claim_semen", "$no_invoice", "$klaim_semen_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "CLAIM SEMEN", "$no_gl_claim_semen", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMSEMEN", "", "", "$klaim_semen_v", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key");
                            $tablename = "EX_FB60_DTL";
                            $fungsi->insert($conn, $field_names, $field_data, $tablename);
                            $ket_in .= " KLAIM SEMEN ";
                        }
                        $field_names = array('NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY', 'BVTYP', 'NO_REK', 'BANK', 'BANK_CABANG');
                        $field_data = array("$no_invoice", "$klaim_all_new_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "$ket_in", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMREZAK", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key", "$bvtyp_v", "$no_rek_dis_v", "$nama_bank_dis_v", "$cabang_bank_dis_v");
                        $tablename = "EX_FB60";
                        $fungsi->insert($conn, $field_names, $field_data, $tablename);
                        $show_ket .= "<br> Sukses, No Accounting : " . $no_doc . " Untuk Vendor " . $kode_pengelola_v . " / " . $nama_pengelola_v;
                    } else {
                        $fce->RETURN->Reset();
                        while ($fce->RETURN->Next()) {
                            $show_ket .= "<br> " . $fce->RETURN->row["TYPE"];
                            $show_ket .= " " . $fce->RETURN->row["NUMBER"];
                            $show_ket .= " " . $fce->RETURN->row["MESSAGE"];
                        }
                        $show_ket .= "<br> Gagal, Untuk Vendor " . $kode_pengelola_v . " / " . $nama_pengelola_v;
                    }
                }
                $fce->Close();
            }
        }

        $field_names = array('FLAG_TRN');
        $field_data = array("OK");
        $tablename = "EX_INVOICE";
        $field_id = array('NO_INVOICE');
        $value_id = array("$no_invoice");
        $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);


//			$fce->ACCOUNTTAX->row["ITEMNO_ACC"] = $pajak_k;
//			$fce->ACCOUNTTAX->row["GL_ACCOUNT"] = $pajak_k;
//			$fce->ACCOUNTTAX->row["COND_KEY"] = $pajak_k;
//			$fce->ACCOUNTTAX->row["ACCT_KEY"] = $pajak_k;
//			$fce->ACCOUNTTAX->row["TAX_CODE"] = $pajak_k;
//			$fce->ACCOUNTTAX->row["TAX_RATE"] = $pajak_k;
//			$fce->ACCOUNTTAX->Append($fce->ACCOUNTTAX->row);

        $sap->Close();
        $habis = "lihat_ppl_trn.php";
        break;
//============================================================================================================================
    case "generate_ppl_turunan_bulanan":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $kode_distributor = $_POST['kode_distributor'];
        $id_bulan = $_REQUEST['bulan'];
        $id_tahun = $_REQUEST['tahun'];
        $blnthn = $id_bulan . $id_tahun;
        $jumHari = cal_days_in_month(CAL_GREGORIAN, $id_bulan, $id_tahun);

        $blnthn2 = $id_bulan . '-' . $id_tahun;
        $tgl_awal = '01-' . $blnthn2;
        $tgl_akhir = $jumHari . '-' . $blnthn2;
        if (trim($kode_distributor) == '') {
            echo $show_ket = "Kode Distributor harus diisi";
            exit;
        }

        $sap = new SAPConnection();
        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            echo $sap->PrintStatus();
            exit;
        }

        $sql = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, 
SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, 
SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH, PLANT 
FROM EX_TRANS_HDR 
WHERE DELETE_MARK = '0' AND
SOLD_TO = '" . $kode_distributor . "' AND
DELETE_MARK = '0' AND
NO_INVOICE IS NOT NULL AND
SOLD_TO != '**********' AND
SOLD_TO != '**********' AND
TGL_CLEARING BETWEEN TO_Date('" . $tgl_awal . "', 'DD-MM-YYYY') AND TO_Date('" . $tgl_akhir . "', 'DD-MM-YYYY') 
GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC,PLANT ORDER BY SOLD_TO ASC";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $total_invoice = 0;
        $oa_semen_v = 0;
        while ($row = oci_fetch_array($query)) {
//		$no_gl_shp_v=$row[NO_GL_SHP];
            $no_invoice_v = $row[NO_INVOICE];
            $no_invoice = $no_invoice_v;
            $no_invoice_ex_v = $row[NO_INV_VENDOR];
            $no_inv_sap_v = $row[NO_INV_SAP];
            $no_acc_doc_v = $row[ACCOUNTING_DOC];
            $warna_plat_v = $row[WARNA_PLAT];
            $vendor_v = $row[VENDOR];
            $tgl_now = date('Ymd');

            $sold_to_v = $row[SOLD_TO];
            $klaim_ktg_v = $row[KLAIM_KTG];
            $klaim_rezak_v = $row[KLAIM_REZAK];
            $klaim_semen_v = $row[KLAIM_SEMEN];
            $klaim_all_v = $row[KLAIM_REZAK] + $row[KLAIM_SEMEN];
            $klaim_pdpks_v = $row[KLAIM_PDPKS];
            $kel = $row[KELOMPOK_TRANSAKSI];
            $oa_semen_v += $row[OA_SEMEN];

            $klaim_lebih_v = $row[KLAIM_LEBIH];
            $klaim_pdpks_lebih_v = $row[PDPKS_LEBIH];
            $klaim_lebih_all_v = $klaim_lebih_v;
            $distributor = $row[SOLD_TO];
            $klaim_all_new_v = 0;

            if ($klaim_semen_v > $klaim_lebih_v)
                $klaim_semen_v = $klaim_semen_v - $klaim_lebih_v;
            else
                $klaim_semen_v = 0;

            if ($klaim_rezak_v > 1 or $klaim_semen_v > 1) {
                $sql_bn = "SELECT ORG,NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS,TANGGAL_INVOICE,NAMA_PENGELOLA,PENGELOLA,BVTYP FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' AND SOLD_TO = '$distributor' ORDER BY ID DESC";
                // STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'
                $query_bn = oci_parse($conn, $sql_bn);
                oci_execute($query_bn);
                $row_bn = oci_fetch_array($query_bn);

                $nama_vendor_v = $row_bn[NAMA_VENDOR];
                $nama_pengelola_v = $row_bn[NAMA_PENGELOLA];
                $kode_pengelola_v = $row_bn[PENGELOLA];
                $nama_sold_to_v = $row_bn[NAMA_SOLD_TO];
                $no_rek_dis_v = $row_bn[NO_REK_DIS];
                $nama_bank_dis_v = $row_bn[NAMA_BANK_DIS];
                $cabang_bank_dis_v = $row_bn[BANK_CABANG_DIS];
                $tanggal_invoice_v = $row_bn[TANGGAL_INVOICE];
                $bvtyp_v = $row_bn[BVTYP];
                $org_v = $row_bn[ORG];


                $fce = $sap->NewFunction("Z_ZAPPSD_ACC_DOC_POST");
                if ($fce == false) {
                    $sap->PrintStatus();
                    exit;
                }
                if ($klaim_rezak_v > 1) {
                    $kode_claim_rezak = 'SG0004';
                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' and ORG='$org_v' AND DELETE_MARK = '0' ";
                    $query_gl = oci_parse($conn, $mialo);
                    oci_execute($query_gl);
                    $row_gl = oci_fetch_array($query_gl);
                    $no_gl_claim_rezak = $row_gl['NO_GL'];
                    $nama_claim_rezak = $row_gl['NAMA_KOMPONEN'];
                    $keterangan_claim_rezak = $row_gl['KETERANGAN'];
                    $pajak_claim = $row_gl['TAX_CODE'];
                    $prctr_v = $row_gl['PRCTR'];
                    $kostl_v = $row_gl['COST_CENTER'];
                    $prctr_vKLAMREZAK = $prctr_v;

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_v; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_rezak_v;
                    //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                    $ket_sapin .= " KLAIM REZAK ";
                    $klaim_all_new_v = $klaim_all_new_v + $klaim_rezak_v;
                }

                if ($klaim_semen_v > 1) {
                    $kode_claim_semen = 'SG0003';
                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' and ORG='$org_v' AND DELETE_MARK = '0'";
                    $query_gl = oci_parse($conn, $mialo);
                    oci_execute($query_gl);
                    $row_gl = oci_fetch_array($query_gl);
                    $no_gl_claim_semen = $row_gl['NO_GL'];
                    $nama_claim_semen = $row_gl['NAMA_KOMPONEN'];
                    $keterangan_claim_semen = $row_gl['KETERANGAN'];
                    $pajak_claim = $row_gl['TAX_CODE'];
                    $prctr_v = $row_gl['PRCTR'];
                    $kostl_v = $row_gl['COST_CENTER'];
                    $prctr_vKLAMSEMEN = $prctr_v;

                    $fce->ACCOUNTGL->row["ITEMNO_ACC"] = '**********';
                    $fce->ACCOUNTGL->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                    $fce->ACCOUNTGL->row["PROFIT_CTR"] = $prctr_v; //'**********'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    //$fce->ACCOUNTGL->row["TAX_CODE"] = '';
                    $fce->ACCOUNTGL->Append($fce->ACCOUNTGL->row);

                    $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                    $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                    $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                    $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = $klaim_semen_v;
                    //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                    $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);
                    $ket_sapin .= " KLAIM SEMEN ";
                    $klaim_all_new_v = $klaim_all_new_v + $klaim_semen_v;
                }

                $fce->CURRENCYAMOUNT->row["ITEMNO_ACC"] = '**********';
                $fce->CURRENCYAMOUNT->row["CURR_TYPE"] = "00";
                $fce->CURRENCYAMOUNT->row["CURRENCY"] = "IDR";
                $fce->CURRENCYAMOUNT->row["AMT_DOCCUR"] = (-1) * $klaim_all_new_v;
                //			$fce->CURRENCYAMOUNT->row["AMT_BASE"] = $pajak_k;
                $fce->CURRENCYAMOUNT->Append($fce->CURRENCYAMOUNT->row);

                $fce->ACCOUNTPAYABLE->row["ITEMNO_ACC"] = '**********';
                $fce->ACCOUNTPAYABLE->row["VENDOR_NO"] = $kode_pengelola_v;
                $fce->ACCOUNTPAYABLE->row["COMP_CODE"] = $org_v; //'2000';
                $fce->ACCOUNTPAYABLE->row["ALLOC_NMBR"] = $no_invoice_v;
                $fce->ACCOUNTPAYABLE->row["BLINE_DATE"] = date("Ymd");
                $fce->ACCOUNTPAYABLE->row["PYMT_METH"] = 'C';
                $fce->ACCOUNTPAYABLE->row["PMNT_BLOCK"] = '3';
                $fce->ACCOUNTPAYABLE->row["PARTNER_BK"] = $bvtyp_v;
                //	$fce->ACCOUNTPAYABLE->row["ITEM_TEXT"] = $no_invoice;
                //	$fce->ACCOUNTPAYABLE->row["TAX_CODE"] = $no_invoice;
                $fce->ACCOUNTPAYABLE->Append($fce->ACCOUNTPAYABLE->row);

                $fce->DOCUMENTHEADER["BUS_ACT"] = "RMRP";
                $fce->DOCUMENTHEADER["USERNAME"] = $user_name;
                $fce->DOCUMENTHEADER["HEADER_TXT"] = $ket_sapin;
                $fce->DOCUMENTHEADER["COMP_CODE"] = $org_v; //'2000';
                $fce->DOCUMENTHEADER["DOC_DATE"] = date("Ymd");
                $fce->DOCUMENTHEADER["PSTNG_DATE"] = date("Ymd");
                $fce->DOCUMENTHEADER["FISC_YEAR"] = date("Y");
                $fce->DOCUMENTHEADER["FIS_PERIOD"] = "00";
                $fce->DOCUMENTHEADER["DOC_TYPE"] = "KR";

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $obj_key = $fce->OBJ_KEY;
                    $no_doc = substr($obj_key, 0, 10);
                    $tahun = date("Y");
                    if ($obj_key != '$' and strlen($no_doc) > 5) {
                        if ($klaim_rezak_v > 0) {
                            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'KODE_PAJAK', 'PAJAK', 'SUB_TOTAL', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY');
                            $field_data = array("$kode_claim_rezak", "$nama_claim_rezak", "$no_invoice", "$klaim_rezak_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "CLAIM REZAK", "$no_gl_claim_rezak", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMREZAK", "", "", "$klaim_rezak_v", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key");
                            $tablename = "EX_FB60_DTL";
                            $fungsi->insert($conn, $field_names, $field_data, $tablename);
                            $ket_in = " KLAIM REZAK ";
                        }
                        if ($klaim_semen_v > 0) {
                            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'KODE_PAJAK', 'PAJAK', 'SUB_TOTAL', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY');
                            $field_data = array("$kode_claim_semen", "$nama_claim_semen", "$no_invoice", "$klaim_semen_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "CLAIM SEMEN", "$no_gl_claim_semen", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMSEMEN", "", "", "$klaim_semen_v", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key");
                            $tablename = "EX_FB60_DTL";
                            $fungsi->insert($conn, $field_names, $field_data, $tablename);
                            $ket_in .= " KLAIM SEMEN ";
                        }
                        $field_names = array('NO_INVOICE', 'TOTAL', 'CREATE_DATE', 'CREATED_BY', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'DELETE_MARK', 'KETERANGAN', 'ORG', 'NAMA_ORG', 'PROFIT_CENTER', 'SOLD_TO', 'NAMA_SOLD_TO', 'NO_DOC_HDR', 'VENDOR', 'NAMA_VENDOR', 'TAHUN', 'AWKEY', 'BVTYP', 'NO_REK', 'BANK', 'BANK_CABANG');
                        $field_data = array("$no_invoice", "$klaim_all_new_v", "SYSDATE", "$user_name", "SYSDATE", "$user_name", "0", "$ket_in", "$org_v", "PT. SEMEN INDONESIA", "$prctr_vKLAMREZAK", "$sold_to_v", "$nama_sold_to_v", "$no_doc", "$kode_pengelola_v", "$nama_pengelola_v", "$tahun", "$obj_key", "$bvtyp_v", "$no_rek_dis_v", "$nama_bank_dis_v", "$cabang_bank_dis_v");
                        $tablename = "EX_FB60";
                        $fungsi->insert($conn, $field_names, $field_data, $tablename);
                        $show_ket .= "<br> Sukses, No Accounting : " . $no_doc . " Untuk Vendor " . $kode_pengelola_v . " / " . $nama_pengelola_v;
                    } else {
                        $fce->RETURN->Reset();
                        while ($fce->RETURN->Next()) {
                            $show_ket .= "<br> " . $fce->RETURN->row["TYPE"];
                            $show_ket .= " " . $fce->RETURN->row["NUMBER"];
                            $show_ket .= " " . $fce->RETURN->row["MESSAGE"];
                        }
                        $show_ket .= "<br> Gagal, Untuk Vendor " . $kode_pengelola_v . " / " . $nama_pengelola_v;
                    }
                }
                $fce->Close();
            }

            $field_names = array('FLAG_TRN');
            $field_data = array("OK");
            $tablename = "EX_INVOICE";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            #Sudah run fb60
            $field_names = array('STATUS_RUNFB60');
            $field_data = array("$tgl_now");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        }



        $sap->Close();
        $habis = "run_ppl_trn_bulanan.php";
        break;
//============================================================================================================================
//============================================================================================================================
    case "generate_ppl1000":
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $userOrg = $_SESSION['user_org'];


        $no_rek = $_REQUEST['no_rek'];
        $nama_bank = $_REQUEST['nama_bank'];
        $cabang_bank = $_REQUEST['cabang_bank'];

        $no_invoice = $_REQUEST['no_invoice'];

        #(WAPU) 1 - Include untuk menghitung Total Groos Amount ------------------
        $nilai_invoice = 0;
        include_once("wapu_formula.php");
        $total_cekcek = 0;

        #cek apakah invoice ini masuk bulan Juli 2012 -> dst...
        $bool_periodewapu = false;
        $sql_faktur = "select no_invoice, to_char(tgl_pajak_ex,'YYYYMMDD') as tglfakturpajak from ex_invoice where tgl_pajak_ex is not null and to_char(tgl_pajak_ex,'YYYYMMDD') >= '********' and no_invoice = '" . $no_invoice . "' order by to_char(tgl_pajak_ex,'YYYYMMDD')";
        $query_faktur = @oci_parse($conn, $sql_faktur);
        @oci_execute($query_faktur);
        while ($row_fkt = @oci_fetch_array($query_faktur)) {
            $bool_periodewapu = true;
        }
        #(/WAPU) 1 ---------------------------------------------------------------
        //
	// set parameter bapi miro	
        $sap = new SAPConnection();
        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            $sap->PrintStatus();
            exit;
        }

        unset($cekCEK1000);
        $sql_CEK1000 = "
                    SELECT NO_INVOICE,NO_TAGIHAN 
                    FROM EX_TRANS_HDR 
                    WHERE DELETE_MARK = '0' AND NO_INVOICE='$no_invoice'
                    group by NO_INVOICE,NO_TAGIHAN
                ";
        $query_CEK1000 = oci_parse($conn, $sql_CEK1000);
        oci_execute($query_CEK1000);
        $row_CEK1000 = oci_fetch_array($query_CEK1000);
        $cekCEK1000 = trim($row_CEK1000["NO_TAGIHAN"]);
        if ($cekCEK1000 != '') {
            $fce = $sap->NewFunction("Z_ZAPPSD_BAPI_INVOICE_SPJ");
        } else {
            $fce = $sap->NewFunction("Z_ZAPPSD_BAPI_INVOICE");
        }
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }


        $sql_del = "UPDATE EX_KOMPONEN_INV SET DELETE_MARK = '1' , LAST_UPDATE_DATE = SYSDATE , LAST_UPDATED_BY = '$user_name'  WHERE NO_INVOICE = '$no_invoice' AND DELETE_MARK = '0'";
        $query_del = oci_parse($conn, $sql_del);
        oci_execute($query_del);

        $sql = "SELECT EX_TRANS_HDR.*,to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $total_shp_cost = 0;
        $total_gl_klaim = 0;
        $total_klaim_ktg = 0;
        ;
        $total_klaim_semen = 0;
        $total_klaim_rezak = 0;
        $total_klaim_pdpks = 0;
        $pajak_shp_cost = 0;
        $total_glondong = 0;
        $n = 1;
        $pass_trn = 0;
        while ($row = oci_fetch_array($query)) {
            $no_invoice_v = $row[NO_INVOICE];
            $no_invoice_ex_v = $row[NO_INV_VENDOR];
            $spj_v[] = $row[NO_SHP_TRN];
            $tgl_kirim_v[] = $row[TANGGAL_KIRIM];
            $tgl_datang_v[] = $row[TANGGAL_DATANG1];
            $tgl_bongkar_v[] = $row[TANGGAL_BONGKAR1];
            $produk_v[] = $row[KODE_PRODUK];
            $nama_produk_v[] = $row[NAMA_PRODUK];
            $shp_trn_v[] = $row[NO_SHP_TRN];
            $shp_trn_vcek = $row[NO_SHP_TRN];
            $plant_v = $row[PLANT];

            $warna_plat_v = $row[WARNA_PLAT];
            $type_plat_v = trim($row[VEHICLE_TYPE]);
            if ($type_plat_v == '205') {
                $warna_plat_v = 'HITAM';
            }
            $kel = $row[KELOMPOK_TRANSAKSI];
            $no_pajak_ex = trim($row[NO_PAJAK_EX]);
            $no_pajak_in = str_replace("-", "", $no_pajak_ex);
            $no_pajak_in = str_replace(".", "", $no_pajak_in);
            $kode_faktur_wapu = substr($no_pajak_in, 0, 3);
            $faktur_bool = false;
            if ($kode_faktur_wapu == '030') {
                $faktur_bool = true;
            }

            #add by iljas ---------------------------------- Wapu 12-07-2012
            $inco_v = $row[INCO];
            $inco_bool = false;
            if (trim(strtoupper($inco_v)) == 'FRC' && trim(strtoupper($kel)) == 'DARAT' && trim(strtoupper($warna_plat_v)) == 'HITAM') {
                $inco_bool = true;
            } else if (trim(strtoupper($kel)) == 'LAUT') {
                $inco_bool = true;
            }
            $keterangan_otomatis = "INVOICE " . $no_invoice_v;
            $kode_pajak_baru = "";
            if ($bool_periodewapu && $inco_bool && ($nilai_invoice > 10000000)) {
                if ($kode_faktur_wapu == '030' || $kode_faktur_wapu2 == '03' || $kode_faktur_wapu == '040' || $kode_faktur_wapu2 == '04') {
                    $kode_pajak_baru = "WN";
                    $keterangan_otomatis = "INVOICE OA";
                } else {
                    $pesan = "<br>Maaf, Invoice ini masuk WAPU";
                    echo $pesan .= "<br>Faktur Pajak harus 030";
                    exit;
                }
            }
            #end - add by iljas ---------------------------- Wapu 12-07-2012

            $nama_plant_v = $row[NAMA_PLANT];
            $warna_plat_v = $row[WARNA_PLAT];
            $type_plat_v = trim($row[VEHICLE_TYPE]);
            if ($type_plat_v == '205') {
                $warna_plat_v = 'HITAM';
            }
            $nama_vendor_v = $row[NAMA_VENDOR];
            $vendor_v = $row[VENDOR];

            $tanggal_invoice_v = $row[TANGGAL_INVOICE1];
            $sal_dis_v[] = $row[SAL_DISTRIK];
            $nama_sal_dis_v[] = $row[NAMA_SAL_DIS];
            $sold_to_v[] = $row[SOLD_TO];
            $nama_sold_to_v[] = $row[NAMA_SOLD_TO];
            $ship_to_v[] = $row[SHIP_TO];
            $qty_v[] = $row[QTY_SHP];
            $qty_kantong_rusak_v[] = $row[QTY_KTG_RUSAK];
            $qty_semen_rusak_v[] = $row[QTY_SEMEN_RUSAK];
            $id_v[] = $row[ID];
            $no_pol_v[] = $row[NO_POL];
            $shp_cost_v[] = $row[SHP_COST];
            //echo " nilai cost hdr ".$row[SHP_COST];
            $total_klaim_all_v[] = $row[TOTAL_KLAIM_ALL];
            $no_pajak_ex = $row[NO_PAJAK_EX];
            $ebeln_v = $row[EBELN];
            $ebelp_v = $row[EBELP];
            $prctr_v = $row[PRCTR];
            $kostl_v = $row[KOSTL];
            $kostl_v2000 = $kostl_v;
            $org_v = $row[ORG];
            $nama_org_v = $row[NAMA_ORG];
            if ($row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********' or $row[SOLD_TO] == '**********') {
                // do not change this sscript please...
                //	$total_glondong += $row[TOTAL_KLAIM_ALL];
                //	$total_glondong += $row[TOTAL_KTG_RUSAK];

                $total_glondong += $row[TOTAL_SEMEN_RUSAK]; //remove by iljas - glondong
                //	$total_glondong += $row[TOTAL_KTG_REZAK];
                //	$total_glondong += $row[PDPKS];
                //$total_glondong = $total_glondong - $row[KLAIM_LEBIH];
                //$total_glondong = $total_glondong - $row[PDPKS_LEBIH];
            } else {
                //$total_gl_klaim+=$row[TOTAL_KLAIM_ALL];
                //$pass_trn += $row[TOTAL_SEMEN_RUSAK]+$row[TOTAL_KTG_REZAK];
                $total_klaim_ktg+=$row[TOTAL_KTG_RUSAK];
                $total_klaim_semen+=$row[TOTAL_SEMEN_RUSAK];
                $total_klaim_rezak+=$row[TOTAL_KTG_REZAK];
                $total_klaim_pdpks+=$row[PDPKS];
            }
            $approve_v = $row[TANGGAL_APPROVE];
            $kel = $row[KELOMPOK_TRANSAKSI];
            $inco = $row[INCO];
            $fknum = $row[KODE_SHP_COST];
            $no_gl_shp = $row[NO_GL_SHP];
            $pajak_n_v = $row[PAJAK_N];

            #ditambah oleh iljas---
//		$update_shpcost_tmp = "update EX_TRANS_COST 
//								set SHP_COST=$row[SHP_COST]
//							WHERE DELETE_MARK ='0' AND KODE_SHP_COST  = '$fknum'";
//
//		$query_shpcost_tmp= oci_parse($conn, $update_shpcost_tmp);
//		$query_shpcost = @oci_execute($query_shpcost_tmp);
            #if($query_shpcost) $hsill = "OK";
            #else $hsill = "XX";
            #---

            $sql_num = "SELECT * FROM EX_TRANS_COST WHERE DELETE_MARK ='0' AND NO_SHP_TRN = '$shp_trn_vcek' ORDER BY FKPOS ASC ";
            $query_num = oci_parse($conn, $sql_num);
            oci_execute($query_num);

            $cek = 1;
            $nilai_per_shp = 0;
            while ($row_num = oci_fetch_array($query_num)) {
                #---
                //if($cek>1) continue;
                //$cek++;
                #/---

                $no_sheet = $row_num[NO_ENTRY_SHEET];
                $panjang = strlen(strval($n));
                if ($panjang == 1)
                    $no_ke = '00000' . $n;
                if ($panjang == 2)
                    $no_ke = '0000' . $n;
                if ($panjang == 3)
                    $no_ke = '000' . $n;
                if ($panjang == 4)
                    $no_ke = '00' . $n;
                if ($panjang == 5)
                    $no_ke = '0' . $n;
                if ($panjang == 6)
                    $no_ke = $n;
                //echo " <br> nilai shp ". $row_num[SHP_COST];
                $total_data_shp = round($row_num[SHP_COST]); //+round(0.1*$row[SHP_COST],0);
                $total_shp_cost+=round($row_num[SHP_COST]);
                $nilai_per_shp += $row_num[SHP_COST];
                //item data entri    
                $fce->X_ITEMDATA->row["INVOICE_DOC_ITEM"] = $no_ke; //'000001';
                $fce->X_ITEMDATA->row["PO_NUMBER"] = $row_num[EBELN]; //$ebeln_v;
                $fce->X_ITEMDATA->row["PO_ITEM"] = $row_num[EBELP]; //$ebelp_v;
                if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )) {
                    $kode_pjk_shp = 'VN';
                    if ($pajak_n_v == "N") {
                        $kode_pjk_shp = 'VZ';
                    } elseif ($pajak_n_v == "Y") {
                        $kode_pjk_shp = "VN"; // pass mahmud bisa di blank diganti VN biar afdol...
                    }
                    if ($kode_pajak_baru != '')
                        $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut

                        
//kondisi pajak pada invoice ke dua spj lebih 1000
                    $sql_IN21000 = "
                                    select 
                                    case when NO_INVOICE<>NO_TAGIHAN and NO_TAGIHAN is not null then 1
                                    else 0
                                    end as STATUS
                                    from (
                                    SELECT NO_INVOICE,NO_TAGIHAN FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v'
                                    group by NO_INVOICE,NO_TAGIHAN
                                    )tb1
                                ";
                    $queryIN21000 = oci_parse($conn, $sql_IN21000);
                    oci_execute($queryIN21000);
                    $rowIN21000 = oci_fetch_array($queryIN21000);
                    $cek21000 = $rowIN21000["STATUS"];
                    if ($cek21000 == 1) {
                        $kode_pjk_shp = "VZ";
                    }

                    $fce->X_ITEMDATA->row["TAX_CODE"] = $kode_pjk_shp; //VN Tax code untuk PPN 10 % 
                } else {//($warna_plat_v=="KUNING")
                    $kode_pjk_shp = 'VZ';
                    if ($kode_pajak_baru != '')
                        $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
                    $fce->X_ITEMDATA->row["TAX_CODE"] = $kode_pjk_shp; //VN Tax code untuk PPN 10 % 
                    $kode_pjk_shp = 'VZ';
                }
                $fce->X_ITEMDATA->row["ITEM_AMOUNT"] = $total_data_shp;
                $fce->X_ITEMDATA->row["QUANTITY"] = '1';
                $fce->X_ITEMDATA->row["PO_UNIT"] = 'LE'; //$row[SATUAN_SHP];
                $fce->X_ITEMDATA->row["SHEET_NO"] = $no_sheet;
                //@by liyantanto
                $fce->X_ITEMDATA->row["ITEM_TEXT"] = $vendor_v . "-" . $no_invoice_v . "-" . $shp_trn_vcek; //kodevendor+noinvoice+spj
                $fce->X_ITEMDATA->row["SHEET_ITEM"] = '0000000010'; // mulai dari 0000000010
                $fce->X_ITEMDATA->Append($fce->X_ITEMDATA->row);
                $n++;
            }
            //echo "nilai_per_shp" .$nilai_per_shp;
        }
//	echo "<br> Total SHP COST ".$total_shp_cost;
//	echo "<br> kode pajak ".$kode_pjk_shp;
        if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )) {
            $pajak_shp_cost += 0.1 * $total_shp_cost;
            // khusus spj 1000 lebih maka pajak digabungkan menjadi satu di tagihan pertama.
            // tagihan pertama jika pjak_n = Y
            // dan unutk nilai pajak ambil semua data yang no tagihan sama dengan no invoice 
            $sql_IN1000 = "
                    select count(*) as JML from (
                    SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_TAGIHAN = '$no_invoice'
                    group by NO_INVOICE
                    )";
            $queryIN1000 = oci_parse($conn, $sql_IN1000);
            oci_execute($queryIN1000);
            $rowIN1000 = oci_fetch_array($queryIN1000);
            $cek1000 = $rowIN1000["JML"];

            if ($pajak_n_v == "Y") {
                $pajak_shp_cost = 0;
                $sql_pajak_1000 = "SELECT SUM(SHP_COST) AS JUM_SHPCOST FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_TAGIHAN = '$no_invoice'";
                $query_pajak1000 = oci_parse($conn, $sql_pajak_1000);
                oci_execute($query_pajak1000);
                $row_pajak1000 = oci_fetch_array($query_pajak1000);
                $kena_pajak = $row_pajak1000["JUM_SHPCOST"];
                $pajak_shp_cost = 0.1 * $kena_pajak;
                $kode_pjk_shp = "VN";
                if ($kode_pajak_baru != '') {
                    $kode_pjk_shp = $kode_pajak_baru;
                }//Wajib Pungut

                $fce->X_TAXDATA->row["TAX_CODE"] = $kode_pjk_shp; //$row[SATUAN_SHP];
                if ($cek1000 == 0) {
                    $fce->X_TAXDATA->row["TAX_AMOUNT"] = $pajak_shp_cost;
                }
                $fce->X_TAXDATA->row["TAX_BASE_AMOUNT"] = $kena_pajak; // mulai dari 0000000010
                $fce->X_TAXDATA->Append($fce->X_TAXDATA->row);
            } elseif ($pajak_n_v == "N") {
                $pajak_shp_cost = 0;
                $kode_pjk_shp = "VZ";
                if ($kode_pajak_baru != '')
                    $kode_pjk_shp = $kode_pajak_baru; //Wajib Pungut
            }
        }else //($warna_plat_v=="HITAM")
            $pajak_shp_cost = 0;

        $UNTUK_SHP = $total_shp_cost + $pajak_shp_cost;




        $total = count($shp_trn_v);

        $sql_tgl = "SELECT to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EX1 FROM EX_INVOICE WHERE DELETE_MARK ='0' AND NO_INVOICE  = '$no_invoice' ";
        $query_tgl = oci_parse($conn, $sql_tgl);
        oci_execute($query_tgl);

        $row_tgl = oci_fetch_array($query_tgl);
        $tgl_pajak_x = $row_tgl[TGL_PAJAK_EX1];
        if ($tgl_pajak_x == "")
            $tgl_pajak_x = date("Ymd");
        //header entri    
        if ($kode_pjk_shp == "VZ") {
            $fce->X_HEADERDATA["CALC_TAX_IND"] = '';
        } else {
            if ($pajak_n_v == "Y") {
                $fce->X_HEADERDATA["CALC_TAX_IND"] = ''; // isi X jika hitung pajak dari total
            } else {
                $fce->X_HEADERDATA["CALC_TAX_IND"] = 'X'; // isi X jika hitung pajak dari total
            }
        }
        $fce->X_HEADERDATA["INVOICE_IND"] = 'X';
        $fce->X_HEADERDATA["DOC_TYPE"] = 'RE';
        $fce->X_HEADERDATA["DOC_DATE"] = $tgl_pajak_x;

        //if($org_v=='2000'){ $datepostingnew='20131231'; }else{$datepostingnew=date("Ymd");}//
        $fce->X_HEADERDATA["PSTNG_DATE"] = date("Ymd");
        $fce->X_HEADERDATA["COMP_CODE"] = $org_v;
        $fce->X_HEADERDATA["CURRENCY"] = 'IDR';
        //$fce->X_HEADERDATA["DIFF_INV"] = $vendor_v; 
        unset($ketKODEPAJAK);
        if ($no_pajak_ex != "" && $cek1000 != "0") {
            $no_pajak_in = str_replace("-", "", $no_pajak_ex);
            $no_pajak_in = str_replace(".", "", $no_pajak_in);
            $fce->X_HEADERDATA["REF_DOC_NO"] = $kode_wn . $no_pajak_in; // no pajak vendor dari turunan
            $ketKODEPAJAK = $kode_wn . $no_pajak_in;
        }
        $fce->X_HEADERDATA["PYMT_METH"] = 'T'; // Transfer
        $bvtyp = $_POST['bvtyp'];
        $fce->X_HEADERDATA["PARTNER_BK"] = $bvtyp; // patner bank vendor
        $fce->X_HEADERDATA["HEADER_TXT"] = $no_invoice_v; // no rekap invoice dari turunan
        if ($pajak_n_v == "Y" or $pajak_n_v == "N") {
            $fce->X_HEADERDATA["PMNT_BLOCK"] = '3'; // APPROVED MANUAL
        } else {
            $fce->X_HEADERDATA["PMNT_BLOCK"] = '3'; // APPROVED UNTUK DISTRAN langsung ke verifikasi
        }
        $ket_input = trim($keterangan_otomatis);  //Wajib Pungut
        if ($ket_input != '')
            $fce->X_HEADERDATA["ITEM_TEXT"] = $ket_input . '-' . $ketKODEPAJAK;  //Wajib Pungut

        if ($cekCEK1000 != '') {
            if ($cek21000 <> 1) {
                $fce->X_HEADERDATA["ITEM_TEXT"] = $keterangan_otomatis;
            } else {
                $fce->X_HEADERDATA["ITEM_TEXT"] = $keterangan_otomatis . '-' . $kode_wn . $no_pajak_in;
            }
        }
        $ke_gl = 0;


        if ($total_glondong > 1) {// unutk klaim glondong
            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

//			$no_gl_claim_ktg = '71410009';
            $kode_claim_gld = 'SG0006';
//			$nama_claim_ktg = 'BIAYA KLAIM KANTONG';
//			$keterangan_claim_ktg = 'BIAYA KLAIM KANTONG';

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_gld' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $nama_claim_gld = $row['NAMA_KOMPONEN'];
            $keterangan_claim_gld = $row['KETERANGAN'];
            $no_gl_claim_gld = $row['NO_GL'];
            $tax_gld = $row['TAX_CODE'];
            $rate_gld = $row['RATE'];
            $prctr_v = $row['PRCTR'];
            $kostl_v = $row['COST_CENTER'];
            //echo "<br> Klaim Total Glondong ".$total_glondong;
            $pajak_gld = $total_glondong * $rate_gld;
            $total_gld = $total_glondong + $pajak_gld;

            $total_gl_klaim2 = (-1) * $total_glondong;
            $pajak_gld2 = (-1) * $pajak_gld;
            $pajak_shp_cost+=$pajak_gld2;
            $total_gld2 = $total_gl_klaim2 + $pajak_gld2;

            //gl account data entri unutk cost claim   
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_gld;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_glondong;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_gld; //'VZ'
            $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
            $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';//$kostl_v;
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$kode_claim_gld", "$nama_claim_gld", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_gld", "$no_gl_claim_gld", "$org_v", "$nama_org_v", "$tax_gld", "$pajak_gld2", "$total_gld2");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
        }

        $sql_new = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI, SUM(KLAIM_ALL_LEBIH) AS KLAIM_LEBIH, SUM(TOTAL_KLAIM_ALL) AS KLAIM_ALL, SUM(KLAIM_LEBIH) AS KLEBIH, SUM(PDPKS_LEBIH) AS PDPKS_LEBIH FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' GROUP BY NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";

        $query_new = oci_parse($conn, $sql_new);
        oci_execute($query_new);
        while ($row_new = oci_fetch_array($query_new)) {
            $sold_to_new_v = $row_new[SOLD_TO];
            $klaim_ktg_new_v = $row_new[KLAIM_KTG];
            $klaim_rezak_new_v = $row_new[KLAIM_REZAK];
            $klaim_semen_new_v = $row_new[KLAIM_SEMEN];
            $klaim_pdpks_new_v = $row_new[KLAIM_PDPKS];
            $klaim_all_lebih_new_v = $row_new[KLAIM_LEBIH];
            $klaim_all_new_v = $row_new[KLAIM_ALL];

            $klaim_pdpks_lebih_new_v = $row_new[PDPKS_LEBIH];
            $klaim_lebih_new_v = $row_new[KLEBIH];

            if ($klaim_pdpks_new_v > $klaim_pdpks_lebih_new_v)
                $klaim_pdpks_new_v = $klaim_pdpks_new_v - $klaim_pdpks_lebih_new_v;
            else
                $klaim_pdpks_new_v = 0;

            //@liyantanto klaim lebih tidak masuk dalam perhitungan
            /* if($klaim_semen_new_v > $klaim_lebih_new_v)
              $klaim_semen_new_v = $klaim_semen_new_v - $klaim_lebih_new_v;
              else
              $klaim_semen_new_v = 0;
             */

            if ($klaim_all_new_v > $klaim_all_lebih_new_v) {

                if ($klaim_ktg_new_v > 1) {// unutk klaim kantong
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_ktg = '71410009';
                    $kode_claim_ktg = 'SG0002';
                    //			$nama_claim_ktg = 'BIAYA KLAIM KANTONG';
                    //			$keterangan_claim_ktg = 'BIAYA KLAIM KANTONG';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_ktg' and ORG='$org_v' AND DELETE_MARK = '0'";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_ktg = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_ktg = $row['KETERANGAN'];
                    $no_gl_claim_ktg = $row['NO_GL'];
                    $tax_ktg = $row['TAX_CODE'];
                    $rate_ktg = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_vce = $row['COST_CENTER'];

                    //kondisi jika gp
                    $soltmapping = substr($sold_to_new_v, 0, 7);
                    if ($org_v == '7000' && $soltmapping != '0000007') {
                        $kostl_v = $kostl_vce;
                    } else if ($soltmapping == '0000007') {
                        $kostl_v = $row['COST_CENTER_ETC'];
                    } else {
                        $kostl_v = $kostl_v2000;
                    }

                    $pajak_ktg = $klaim_ktg_new_v * $rate_ktg;
                    $total_ktg = $klaim_ktg_new_v + $pajak_ktg;
                    $total_gl_klaim2 = (-1) * $klaim_ktg_new_v;
                    $pajak_ktg2 = (-1) * $pajak_ktg;
                    $pajak_shp_cost+=$pajak_ktg2;
                    $total_ktg2 = $total_gl_klaim2 + $pajak_ktg2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_ktg;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_ktg;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_ktg; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'';//'**********';//;//PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v;
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_ktg", "$nama_claim_ktg", "$kostl_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_ktg", "$no_gl_claim_ktg", "$org_v", "$nama_org_v", "$tax_ktg", "$pajak_ktg2", "$total_ktg2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_ktg_new_v;
                    //$pass_trn += $row[TOTAL_SEMEN_RUSAK]+$row[TOTAL_KTG_REZAK];
                }


                if ($klaim_semen_new_v > 1) {// unutk klaim semen
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_semen = '71410009';
                    $kode_claim_semen = 'SG0003';
                    //			$nama_claim_semen = 'BIAYA KLAIM SEMEN';
                    //			$keterangan_claim_semen = 'BIAYA KLAIM SEMEN';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_semen' and ORG='$org_v' AND DELETE_MARK = '0'";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_semen = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_semen = $row['KETERANGAN'];
                    $no_gl_claim_semen = $row['NO_GL'];
                    $tax_semen = $row['TAX_CODE'];
                    $rate_semen = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_semen = $klaim_semen_new_v * $rate_semen;
                    $total_semen = $klaim_semen_new_v + $pajak_semen;
                    $total_gl_klaim2 = (-1) * $klaim_semen_new_v;
                    $pajak_semen2 = (-1) * $pajak_semen;
                    $pajak_shp_cost+=$pajak_semen2;
                    $total_semen2 = $total_gl_klaim2 + $pajak_semen2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_semen;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_semen;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_semen; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_semen", "$nama_claim_semen", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_semen", "$no_gl_claim_semen", "$org_v", "$nama_org_v", "$tax_semen", "$pajak_semen2", "$total_semen2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_semen_new_v;
                    $pass_trn += $klaim_semen_new_v;
                }


                if ($klaim_rezak_new_v > 1) {// unutk klaim rezak
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_rezak = '71410009';
                    $kode_claim_rezak = 'SG0004';
                    //			$nama_claim_rezak = 'BIAYA KLAIM REZAK';
                    //			$keterangan_claim_rezak = 'BIAYA KLAIM REZAK';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' and ORG='$org_v' AND DELETE_MARK = '0'";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_rezak = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_rezak = $row['KETERANGAN'];
                    $no_gl_claim_rezak = $row['NO_GL'];
                    $tax_rezak = $row['TAX_CODE'];
                    $rate_rezak = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_rezak = $klaim_rezak_new_v * $rate_rezak;
                    $total_rezak = $klaim_rezak_new_v + $pajak_rezak;
                    $total_gl_klaim2 = (-1) * $klaim_rezak_new_v;
                    $pajak_rezak2 = (-1) * $pajak_rezak;
                    $pajak_shp_cost+=$pajak_rezak2;
                    $total_rezak2 = $total_gl_klaim2 + $pajak_rezak2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_rezak;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_rezak;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_rezak; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_rezak", "$nama_claim_rezak", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_rezak", "$no_gl_claim_rezak", "$org_v", "$nama_org_v", "$tax_rezak", "$pajak_rezak2", "$total_rezak2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_rezak_new_v;
                    $pass_trn += $klaim_rezak_new_v;
                }

                if ($klaim_pdpks_new_v > 1) {
                    $ke_gl++;
                    $panjang = strlen(strval($ke_gl));
                    if ($panjang == 1)
                        $nogl_ke = '00000' . $ke_gl;
                    if ($panjang == 2)
                        $nogl_ke = '0000' . $ke_gl;
                    if ($panjang == 3)
                        $nogl_ke = '000' . $ke_gl;
                    if ($panjang == 4)
                        $nogl_ke = '00' . $ke_gl;
                    if ($panjang == 5)
                        $nogl_ke = '0' . $ke_gl;
                    if ($panjang == 6)
                        $nogl_ke = $ke_gl;

                    //			$no_gl_claim_pdpks = '71410009';
                    $kode_claim_pdpks = 'SG0005';
                    //			$nama_claim_pdpks = 'BIAYA KLAIM PDPKS';
                    //			$keterangan_claim_pdpks = 'BIAYA KLAIM PDPKS';

                    $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_pdpks' and ORG='$org_v' AND DELETE_MARK = '0'";
                    $query = oci_parse($conn, $mialo);
                    oci_execute($query);
                    $row = oci_fetch_array($query);
                    $nama_claim_pdpks = $row['NAMA_KOMPONEN'];
                    $keterangan_claim_pdpks = $row['KETERANGAN'];
                    $no_gl_claim_pdpks = $row['NO_GL'];
                    $tax_pdpks = $row['TAX_CODE'];
                    $rate_pdpks = $row['RATE'];
                    $prctr_v = $row['PRCTR'];
                    $kostl_v = $row['COST_CENTER'];

                    $pajak_pdpks = $klaim_pdpks_new_v * $rate_pdpks;
                    $total_pdpks = $klaim_pdpks_new_v + $pajak_pdpks;
                    $total_gl_klaim2 = (-1) * $klaim_pdpks_new_v;
                    $pajak_pdpks2 = (-1) * $pajak_pdpks;
                    $pajak_shp_cost+=$pajak_pdpks2;
                    $total_pdpks2 = $total_gl_klaim2 + $pajak_pdpks2;

                    //gl account data entri unutk cost claim   
                    $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                    $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim_pdpks;
                    $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $total_pdpks;
                    $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                    $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                    $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $tax_pdpks; //'VZ'
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_v; //'**********';//$prctr_v;PROFIT_CENTER
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $kostl_v; //'';
                    $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                    $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                    //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                    $field_data = array("$kode_claim_pdpks", "$nama_claim_pdpks", "$prctr_v", "$no_invoice_v", "$total_gl_klaim2", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_claim_pdpks", "$no_gl_claim_pdpks", "$org_v", "$nama_org_v", "$tax_pdpks", "$pajak_pdpks2", "$total_pdpks2");
                    $tablename = "EX_KOMPONEN_INV";
                    $fungsi->insert($conn, $field_names, $field_data, $tablename);

                    $total_gl_klaim+=$klaim_pdpks_new_v;
                    //$pass_trn += $klaim_rezak_new_v;
                }
            }// end jika klaim normal lebih besar dari pada klaim lebih
            // jika klaimlebih > klaim normal maka tidak dianggap 0/ tidak ada klaim 
        }// end while	

        $jumlahd = $_POST['jumlahd'];
        $komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
        $total_d = 0;

        if (isset($_POST['komponen_biaya_d1']) and $_POST['komponen_biaya_d1'] != "") {
            $ke_gl++;

            $komponen_biaya_d1 = $_POST['komponen_biaya_d1'];
            $nama_komponen_d1 = $_POST['nama_komponen_d1'];
            $nilaid1 = $_POST['nilaid1'];
            $keterangan_d1 = $_POST['keterangan_d1'];
            $pajak_d1 = $_POST['pajak_d1'];
            $no_gl_d1 = $_POST['no_gl_d1'];
            $total_d+=$nilaid1;

            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d1' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $no_gl_claim = $row['NO_GL'];
            $rate_claim = $row['RATE'];
            $cc_biaya = $row['COST_CENTER'];
            $prctr_biaya = $row['PRCTR'];
            $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

            $pajak_claim = $nilaid1 * $rate_claim;
            $total_claim = $nilaid1 + $pajak_claim;
            $pajak_shp_cost+=$pajak_claim;

            //gl account data entri    
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaid1;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'S'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_d1;
            if ($prctr_biaya != "")
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
            if ($cc_biaya != "")
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
//		if($pajak_d1=="VN")
//		$fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = $nilaid1;//$kostl_v;
//                if($tax_basmountex!=''){
//                    $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaid1*$tax_basmountex);
//                }
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$komponen_biaya_d1", "$nama_komponen_d1", "$kostl_v", "$no_invoice_v", "$nilaid1", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_d1", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_d1", "$pajak_claim", "$total_claim");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);

            for ($i = 2; $i <= $jumlahd; $i++) {
                $ke_gl++;
                $panjang = strlen(strval($ke_gl));
                if ($panjang == 1)
                    $nogl_ke = '00000' . $ke_gl;
                if ($panjang == 2)
                    $nogl_ke = '0000' . $ke_gl;
                if ($panjang == 3)
                    $nogl_ke = '000' . $ke_gl;
                if ($panjang == 4)
                    $nogl_ke = '00' . $ke_gl;
                if ($panjang == 5)
                    $nogl_ke = '0' . $ke_gl;
                if ($panjang == 6)
                    $nogl_ke = $ke_gl;


                $komp_d = "komponen_biaya_d" . $i;
                $nama_komp_d = "nama_komponen_d" . $i;
                $komp_nilaid = "nilaid" . $i;
                $komp_ketd = "keterangan_d" . $i;
                $komp_pajakd = "pajak_d" . $i;
                $komp_gld = "no_gl_d" . $i;

                $komponen_biaya_d = $_POST[$komp_d];
                $nama_komponen_d = $_POST[$nama_komp_d];
                $nilaid = $_POST[$komp_nilaid];
                $keterangan_d = $_POST[$komp_ketd];
                $pajak_d = $_POST[$komp_pajakd];
                $no_gl_d = $_POST[$komp_gld];
                $total_d+=$nilaid;

                $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_d' and ORG='$org_v' AND DELETE_MARK = '0'";
                $query = oci_parse($conn, $mialo);
                oci_execute($query);
                $row = oci_fetch_array($query);
                $no_gl_claim = $row['NO_GL'];
                $rate_claim = $row['RATE'];
                $cc_biaya = $row['COST_CENTER'];
                $prctr_biaya = $row['PRCTR'];
                $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

                $pajak_claim = $nilaid * $rate_claim;
                $total_claim = $nilaid + $pajak_claim;
                $pajak_shp_cost+=$pajak_claim;

                //gl account data entri    
                $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
                $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaid;
                $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'S'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_d;
                if ($prctr_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
                if ($cc_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
//                        if($tax_basmountex!=''){
//                            $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaid*$tax_basmountex);
//                        }
                $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);

                $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
                $field_data = array("$komponen_biaya_d", "$nama_komponen_d", "$kostl_v", "$no_invoice_v", "$nilaid", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_d", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_d", "$pajak_claim", "$total_claim");
                $tablename = "EX_KOMPONEN_INV";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);
            }
        }

        $jumlahk = $_POST['jumlahk'];
        $komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
        $total_k = 0;


        if (isset($_POST['komponen_biaya_k1']) and $_POST['komponen_biaya_k1'] != "") {
            $komponen_biaya_k1 = $_POST['komponen_biaya_k1'];
            $nama_komponen_k1 = $_POST['nama_komponen_k1'];
            $nilaik1 = $_POST['nilaik1'];
            $keterangan_k1 = $_POST['keterangan_k1'];
            $pajak_k1 = $_POST['pajak_k1'];
            $no_gl_k1 = $_POST['no_gl_k1'];
            $total_k+=$nilaik1;
            $ke_gl++;
            $panjang = strlen(strval($ke_gl));
            if ($panjang == 1)
                $nogl_ke = '00000' . $ke_gl;
            if ($panjang == 2)
                $nogl_ke = '0000' . $ke_gl;
            if ($panjang == 3)
                $nogl_ke = '000' . $ke_gl;
            if ($panjang == 4)
                $nogl_ke = '00' . $ke_gl;
            if ($panjang == 5)
                $nogl_ke = '0' . $ke_gl;
            if ($panjang == 6)
                $nogl_ke = $ke_gl;

            $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k1' and ORG='$org_v' AND DELETE_MARK = '0'";
            $query = oci_parse($conn, $mialo);
            oci_execute($query);
            $row = oci_fetch_array($query);
            $no_gl_claim = $row['NO_GL'];
            $rate_claim = $row['RATE'];
            $cc_biaya = $row['COST_CENTER'];
            $prctr_biaya = $row['PRCTR'];
            $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

            $pajak_claim = $nilaik1 * $rate_claim;
            $total_claim = $nilaik1 + $pajak_claim;

            $pajak_claim2 = (-1) * $nilaik1 * $rate_claim;
            $nilai_claim = (-1) * $nilaik1;
            $total_claim2 = $nilai_claim + $pajak_claim2;

            $pajak_shp_cost+=$pajak_claim2;

            //gl account data entri    
            $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
            $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
            $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaik1;
            $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
            $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
            $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_k1;
            if ($prctr_biaya != "")
                $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
            if ($cc_biaya != "")
                $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
            if ($tax_basmountex != '') {
                $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaik1 * $tax_basmountex);
            }
            $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);


            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("$komponen_biaya_k1", "$nama_komponen_k1", "$prctr_biaya", "$no_invoice_v", "$nilai_claim", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_k1", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_k1", "$pajak_claim2", "$total_claim2");
            $tablename = "EX_KOMPONEN_INV";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);

            for ($i = 2; $i <= $jumlahk; $i++) {
                $ke_gl++;
                $panjang = strlen(strval($ke_gl));
                if ($panjang == 1)
                    $nogl_ke = '00000' . $ke_gl;
                if ($panjang == 2)
                    $nogl_ke = '0000' . $ke_gl;
                if ($panjang == 3)
                    $nogl_ke = '000' . $ke_gl;
                if ($panjang == 4)
                    $nogl_ke = '00' . $ke_gl;
                if ($panjang == 5)
                    $nogl_ke = '0' . $ke_gl;
                if ($panjang == 6)
                    $nogl_ke = $ke_gl;

                $komp_k = "komponen_biaya_k" . $i;
                $nama_komp_k = "nama_komponen_k" . $i;
                $komp_nilaik = "nilaik" . $i;
                $komp_ketk = "keterangan_k" . $i;
                $komp_pajakk = "pajak_k" . $i;
                $komp_glk = "no_gl_k" . $i;

                $komponen_biaya_k = $_POST[$komp_k];
                $nama_komponen_k = $_POST[$nama_komp_k];
                $nilaik = $_POST[$komp_nilaik];
                $keterangan_k = $_POST[$komp_ketk];
                $pajak_k = $_POST[$komp_pajakk];
                $no_gl_k = $_POST[$komp_glk];
                $total_k+=$nilaik;

                $mialo = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$komponen_biaya_k' and ORG='$org_v' AND DELETE_MARK = '0'";
                $query = oci_parse($conn, $mialo);
                oci_execute($query);
                $row = oci_fetch_array($query);
                $no_gl_claim = $row['NO_GL'];
                $rate_claim = $row['RATE'];
                $cc_biaya = $row['COST_CENTER'];
                $prctr_biaya = $row['PRCTR'];
                $tax_basmountex = trim($row['TAX_BASE_AMOUNT']);

                $pajak_claim = $nilaik * $rate_claim;
                $total_claim = $nilaik + $pajak_claim;

                $pajak_claim2 = (-1) * $nilaik * $rate_claim;
                $nilai_claim = (-1) * $nilaik;
                $total_claim2 = $nilai_claim + $pajak_claim2;

                $pajak_shp_cost+=$pajak_claim2;

                //gl account data entri    
                $fce->X_GLACCOUNTDATA->row["INVOICE_DOC_ITEM"] = $nogl_ke; //'000001';
                $fce->X_GLACCOUNTDATA->row["GL_ACCOUNT"] = $no_gl_claim;
                $fce->X_GLACCOUNTDATA->row["ITEM_AMOUNT"] = $nilaik;
                $fce->X_GLACCOUNTDATA->row["DB_CR_IND"] = 'H'; //Debet  / Credit Indicator (S = Debet , H = Kredit)
                $fce->X_GLACCOUNTDATA->row["COMP_CODE"] = $org_v;
                $fce->X_GLACCOUNTDATA->row["TAX_CODE"] = $pajak_k;
                if ($prctr_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["PROFIT_CTR"] = $prctr_biaya;
                if ($cc_biaya != "")
                    $fce->X_GLACCOUNTDATA->row["COSTCENTER"] = $cc_biaya; //$kostl_v;
                if ($tax_basmountex != '') {
                    $fce->X_GLACCOUNTDATA->row["TAX_BASE_AMOUNT"] = ($nilaik * $tax_basmountex);
                }
                $fce->X_GLACCOUNTDATA->Append($fce->X_GLACCOUNTDATA->row);


                $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'PROFIT_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
                $field_data = array("$komponen_biaya_k", "$nama_komponen_k", "$prctr_biaya", "$no_invoice_v", "$nilai_claim", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "$keterangan_k", "$no_gl_claim", "$org_v", "$nama_org_v", "$pajak_k", "$pajak_claim2", "$total_claim2");
                $tablename = "EX_KOMPONEN_INV";
                $fungsi->insert($conn, $field_names, $field_data, $tablename);
            }
        }
        if ($total_glondong < 1)
            $total_glondong = 0;

        $total_invoice = $total_shp_cost + $total_d - $total_k - $total_gl_klaim - $total_glondong;

        #add by iljas - Wapu 17.07.2012
        if ($kode_pajak_baru != 'WN' && $kode_pajak_baru != 'DN') {
            $total_invoice += round($pajak_shp_cost, 0); //termasuk pajak 10%
        }
        echo "<br>  Pajak shp cost ". $pajak_shp_cost ." total_shp_cost ". $total_shp_cost .  " TOTAL DEBET ". $total_d . " tOTAL kREDIT ".$total_k. " total inv ". $total_invoice;

        $fce->X_HEADERDATA["GROSS_AMOUNT"] = $total_invoice;

        $fce->Call();
//                echo "<pre>";
//                print_r($fce);
//                echo "</pre>";
        if ($fce->GetStatus() == SAPRFC_OK) {
            $nomor_inv = $fce->X_INVOICEDOCNUMBER;
            $nomor_acc = $fce->X_ACCNUMBER;
            //echo "aaaaaaa";
            $tipe = $fce->X_RETURN["TYPE"];
            $msg = $fce->X_RETURN["MESSAGE"];
            $id_ret = $fce->X_RETURN["ID"];
            $num_ret = $fce->X_RETURN["NUMBER"];
            $show_ket.= $tipe;
            $show_ket.= ' ';
            $show_ket.= $id_ret;
            $show_ket.= ' ';
            $show_ket.= $num_ret;
            $show_ket.= ' ';
            $show_ket.= $msg;
            $show_ket.= ' ';
            $show_ket.= ' Dengan No' . $nomor_inv . " Dan No ACC " . $nomor_acc;
            $show_ket.= '<br>';

            $fce->Close();
        }
        $sap->Close();

        if ($cek1000 == 0) {
            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("SG0001", "OA SEMEN", "", "$no_invoice", "$total_shp_cost", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "ONGKOS ANGKUT", "$no_gl_shp", "$org_v", "$nama_org_v", "VZ", "$pajak_shp_cost", "$UNTUK_SHP");
        } else {
            $field_names = array('KODE_KOMPONEN', 'NAMA_KOMPONEN', 'COST_CENTER', 'NO_INVOICE', 'SUB_TOTAL', 'CREATE_DATE', 'CREATED_BY', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KETERANGAN', 'NO_GL', 'ORG', 'NAMA_ORG', 'KODE_PAJAK', 'PAJAK', 'TOTAL');
            //,'ALAMAT_VENDOR','DISTRIK_VENDOR','NO_REKENING','BANK','CURR');
            $field_data = array("SG0001", "OA SEMEN", "", "$no_invoice", "$total_shp_cost", "SYSDATE", "$user_name", "0", "SYSDATE", "$user_name", "ONGKOS ANGKUT", "$no_gl_shp", "$org_v", "$nama_org_v", "$kode_pjk_shp", "$pajak_shp_cost", "$UNTUK_SHP");
        }
        //        $total_shp_cost
        //        $total_invoice        
        $tablename = "EX_KOMPONEN_INV";
        $fungsi->insert($conn, $field_names, $field_data, $tablename);

        if ($nomor_inv != "") {
            $status = "INVOICED"; //else $status="PROGRESS";		

            $field_names = array('NO_INV_SAP', 'APPROVE_BY', 'STATUS', 'TANGGAL_APPROVE', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'ACCOUNTING_DOC');
            $field_data = array("$nomor_inv", "$user_name", "$status", "SYSDATE", "SYSDATE", "$user_name", "$nomor_acc");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_v");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $no_rek = $_POST['no_rek'];
            $nama_bank = $_POST['nama_bank'];
            $cabang_bank = $_POST['cabang_bank'];
            $no_rek = $_POST['no_rek'];

            $sql_cetak = "SELECT * FROM EX_SET_CETAK WHERE ORG ='$userOrg'";
            $query_cetak = oci_parse($conn, $sql_cetak);
            oci_execute($query_cetak);

            $row_cetak = oci_fetch_array($query_cetak);
            $unit_v = $row_cetak[UNIT_KERJA];
            $lokasi_v = $row_cetak[LOKASI];
            $kepala_v = $row_cetak[KEPALA];
            $atasan_v = $row_cetak[ATASAN];
            $verifikasi_v = $row_cetak[VERIFIKASI];
            $tahun_nr = date("Y");
            if ($pass_trn > 1)
                $flag = "";
            else
                $flag = "OK";
            $field_names = array('NO_INVOICE_SAP', 'NO_REKENING', 'BANK', 'CURR', 'BANK_CABANG', 'TGL_APPROVED', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'KEPALA', 'ATASAN', 'VERIFIKASI', 'UNIT', 'LOKASI', 'ACCOUNTING_DOC', 'TAHUN', 'BVTYP', 'FLAG_TRN');
            $field_data = array("$nomor_inv", "$no_rek", "$nama_bank", "IDR", "$cabang_bank", "SYSDATE", "SYSDATE", "$user_name", "$kepala_v", "$atasan_v", "$verifikasi_v", "$unit_v", "$lokasi_v", "$nomor_acc", "$tahun_nr", "$bvtyp", "$flag");
            $tablename = "EX_INVOICE";
            $field_id = array('NO_INVOICE');
            $value_id = array("$no_invoice_v");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
        }
        //echo "hgasdkjafha";
        $habis = "lihat_invoice_hdr1000.php";
        break;
//============================================================================================================================        
//============================================================================================================================
    case "delete_ppl_trn_bulanan":
        $belnr = $_POST['BELNR'];
        $gjahr = date('Y', strtotime($_POST['tgl_clearing']));
        $bukrs = $_POST['bukrs'];

        // $show_ket .= "Data Klaim ".$belnr." dengan gjahr : ".$gjahr." dengan bukrs : ".$bukrs." Berhasil di hapus... <br>";

        $sap = new SAPConnection();
        $sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK)
            $sap->Open();
        if ($sap->GetStatus() != SAPRFC_OK) {
            $sap->PrintStatus();
            exit;
        }

        $fce = $sap->NewFunction("ZPOSTING_DOC_DELETE");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }

        $fce->BELNR = $belnr;
        $fce->BUKRS = $bukrs;
        $fce->GJAHR = $gjahr;

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            $field_data = array("1", "SYSDATE", "$user_name");
            $tablename = "EX_FB60";
            $field_id = array('NO_DOC_HDR', 'ORG');
            $value_id = array("$belnr", "$bukrs");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            
            $field_names = array('DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            $field_data = array("1", "SYSDATE", "$user_name");
            $tablename = "EX_FB60_DTL";
            $field_id = array('NO_DOC_HDR', 'ORG');
            $value_id = array("$belnr", "$bukrs");
            $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $show_ket .= "<br> Data Klaim ".$belnr." Berhasil di hapus... <br>";
            
        }else{
            $show_ket .= "<br> Data Klaim ".$belnr." Telah di hapus sebelumnya <br>";
        }
        $fce->Close();
        $sap->Close();
        
        $habis = "run_ppl_trn_bulanan.php";
        break;
//============================================================================================================================        
}
?>
