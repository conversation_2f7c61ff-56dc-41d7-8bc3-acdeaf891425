<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$halaman_id=224;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
//Translate
require_once('../include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

function sapcode($kode)
{
    $panjang=strlen(strval($kode));
    if($panjang==1)$sapcode='000000000'.$kode;
    if($panjang==2)$sapcode='00000000'.$kode;
    if($panjang==3)$sapcode='0000000'.$kode;
    if($panjang==4)$sapcode='000000'.$kode;
    if($panjang==5)$sapcode='00000'.$kode;
    if($panjang==6)$sapcode='0000'.$kode;
    if($panjang==7)$sapcode='000'.$kode;
    if($panjang==8)$sapcode='00'.$kode;
    if($panjang==9)$sapcode='0'.$kode;
    if($panjang==10)$sapcode=$kode;
    return $sapcode;
}

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
                <SCRIPT LANGUAGE="JavaScript">
                <!--
                    alert("You are not authorized to access this page.... \n Login Please...");
                //-->
                </SCRIPT>

<a href="../index.php">Login....</a>
<?
exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="disp_bill_head_adm.php";
$total = 0;


$currentPage="disp_bill_head_adm.php";
$komen="";
if(isset($_POST['cari'])){
        
        //$no_bill_sap = $_POST['no_bill_sap'];
      //  $org = $_POST['org'];
        $X_VBELN_FR = $_POST['x_vbeln_fr'];
        $X_VBELN_TO = $_POST['x_vbeln_to'];             
               
                $X_TGL_1 = substr($_POST['x_tgl_1'],6,4).substr($_POST['x_tgl_1'],3,2).substr($_POST['x_tgl_1'],0,2);   
                $X_TGL_2 = substr($_POST['x_tgl_2'],6,4).substr($_POST['x_tgl_2'],3,2).substr($_POST['x_tgl_2'],0,2);

        if (strtotime($X_TGL_2) - strtotime($X_TGL_1) > 31 * 24 * 60 * 60 || $X_TGL_2 == "" || $X_TGL_1 == "") {
				$warningMessage = "Tanggal ".$X_TGL_1." sampai ".$X_TGL_2." lebih dari 31 hari.";
				
				echo "<script>alert('$warningMessage');</script>";

                $responseRequest = array(
                    'ResponseCode' => 404,
                    'ResponseMessage' => 'No Data Found',
                    );
                echo json_encode("No Data Found");
            } else {
                //echo "Tanggal ".$X_TGL_1." sampai ".$X_TGL_2." lebih dari 31 hari.";

        $X_KUNNR = $_POST['sold_to'];
        echo "KUNNR:".$X_KUNNR;
        $X_FKART = $_POST['bill_type'];

        $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
           echo $sap->PrintStatus();
           exit;
        }

        $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALINV_HEAD2");
        if ($fce == false ) {
           $sap->PrintStatus();
           exit;
        }
        
        //header entri      
        $fce->X_VBELN_FR    = $X_VBELN_FR; 
        $fce->X_VBELN_TO    = $X_VBELN_TO;      
        $fce->X_TGL_1       = $X_TGL_1; 
        $fce->X_TGL_2       = $X_TGL_2;             
        $fce->X_KUNNR = sapcode($X_KUNNR); // sold to       
        $fce->X_VKORG = $user_org;
        $fce->X_FKART = $X_FKART;

        // incompany
        if(count($mp_coics)>0){
            foreach ($mp_coics as $keyOrg2 => $valorgm2){
                if($keyOrg2=='2000'){
                    continue;
                }
                $fce->LRI_VKORG->row['SIGN']='I';
                $fce->LRI_VKORG->row['OPTION']='EQ';
                $fce->LRI_VKORG->row['LOW']=$keyOrg2;
                $fce->LRI_VKORG->row['HIGH']='';
                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
            }
        }
        
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {  
           # echo "masuk";  
            $fce->ZHEAD->Reset();
            while ( $fce->ZHEAD->Next() ){
            $NO_ACC_SAP[]= $fce->ZHEAD->row["NO_ACC_SAP"];
            $NO_BILL_SAP[]= $fce->ZHEAD->row["NO_BILL_SAP"];
            $BILL_TYPE[] = $fce->ZHEAD->row["BILL_TYPE"];
            $TGL_BILL[]= $fce->ZHEAD->row["TGL_BILL"];
            $TOT_AMOUNT[]= $fce->ZHEAD->row["TOT_AMOUNT"];
            $CURR[]= $fce->ZHEAD->row["CURR"];
            $KUNRG[]= $fce->ZHEAD->row["KUNRG"];
            $KUNAG[]= $fce->ZHEAD->row["KUNAG"];
                        if($user_org=='6000'){
                            $NAME1[]= $fce->ZHEAD->row["SOLDTO_NAME"];
                        }else{
                            $NAME1[]= $fce->ZHEAD->row["NAME1"];                            
                        }
                        $NAME2[]= $fce->ZHEAD->row["NAME2"];
            //$total = $total + 1;
        }
        }else
        $fce->PrintStatus();   

        $fce->Close();  
        $sap->Close();  
        $total = count($NO_BILL_SAP);       
            }
}  

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Application SGG Online: See the Billing Data :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
<!--
function popup(url) 
{
 var width  = 1200;
 var height = 500;
 var left   = (screen.width  - width)/2;
 var top    = (screen.height - height)/2;
 var params = 'width='+width+', height='+height;
 params += ', top='+top+', left='+left;
 params += ', directories=no';
 params += ', location=no';
 params += ', menubar=no';
 params += ', resizable=no';
 params += ', scrollbars=yes';
 params += ', status=no';
 params += ', toolbar=no';
 newwin=window.open(url,'windowname5', params);
 if (window.focus) {newwin.focus()}
 return false;
}
function getXMLHTTP() { 
        var xmlhttp=false;  
        try{
            xmlhttp=new XMLHttpRequest();
        }
        catch(e)    {       
            try{            
                xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
            }
            catch(e){
                try{
                xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                }
                catch(e1){
                    xmlhttp=false;
                }
            }
        }
            
        return xmlhttp;
    }  
function finddistr() {
        var com_org = document.getElementById('org');       
        var strURL="cari_distr.php?org="+com_org.value;
        popUp(strURL);
        } 
          
function ketik_distr(obj) {
    var com_org = document.getElementById('org');       
    var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
    var req = getXMLHTTP();
    if (req) {
        req.onreadystatechange = function() { 
            if (req.readyState == 4) {
                // only if "OK"
                if (req.status == 200) {    
                    document.getElementById("distrdiv").innerHTML=req.responseText;                     
                } else {
                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                }
            }               
        }           
        req.open("GET", strURL, true);
        req.send(null);
    }
}

// -->
</script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2"><?php $translatebhsxx->__1('List Billing');?></th>
</tr></table></div>
<?    
    //if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;<?php $translatebhsxx->__1('Form Search Billing');?> </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('sold_to','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso"><?php $translatebhsxx->__1('Distributor');?> </td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
      <input name="sold_to" id="sold_to" class="inputlabel" type="text" size="10" maxlength="10" value="" onChange="ketik_distr(this)"/>
      <input name="nama_sold_to" id="nama_sold_to" class="inputlabel" type="text" size="50" value="" readonly="true"/>      
      <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/></div></td>
    </tr>
    <tr>
      <td  class="puso"><?php $translatebhsxx->__1('Billing Date');?>: </td>
      <td  class="puso">&nbsp;</td>
      <td ><div id="div">
        <input name="x_tgl_1" id="x_tgl_1" type="text" size="10" maxlength="10" onClick="return showCalendar('x_tgl_1');" value="<? if($_POST["x_tgl_1"]) echo $_POST["x_tgl_1"]; ?>"/>
        &nbsp; <?php $translatebhsxx->__1('to');?> &nbsp;
        <input name="x_tgl_2" id="x_tgl_2"  type="text" size="10"  onclick="return showCalendar('x_tgl_2');" value="<? if($_POST["x_tgl_2"]) echo $_POST["x_tgl_2"]; ?>" />
      </div></td>
    </tr>
    <tr>
      <td  class="puso"><?php $translatebhsxx->__1('No. Billing');?> </td>
      <td  class="puso">:</td>
      <td ><div id="distrdiv">
      <input name="x_vbeln_fr" id="x_vbeln_fr" type="text" size="15" value="<? if($_POST["x_vbeln_fr"]) echo $_POST["x_vbeln_fr"]; ?>" />&nbsp;&nbsp;<?php $translatebhsxx->__1('to');?>&nbsp;&nbsp;
      <input name="x_vbeln_to" id="x_vbeln_to" type="text" size="15" value="<? if($_POST["x_vbeln_to"]) echo $_POST["x_vbeln_to"]; ?>" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;     
      <!-- <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/> --></div>
      </td>
    </tr>
    <tr>
        <td  class="puso"><?php $translatebhsxx->__1('Billing Type');?></td>
        <td  class="puso">:</td>
        <td ><select name="bill_type" id="bill_type">
          <option value="">---<?php $translatebhsxx->__1('Select Billing');?>---</option>
          <option value="ZF2">Billing Standar</option>
          <option value="ZRE">Billing Return</option>
           <option value="ZF2P">Billing Project</option>
        </select></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td rowspan="2">
        <input name="cari" type="submit" class="button" id="cari" value="<?php $translatebhsxx->__1('Find');?>" />
        <?php if (!empty($NO_BILL_SAP)) { ?>
            <input type="button" class="button" id="exportBtn"
                value="<?php $translatebhsxx->__1('Export');?>" onclick="document.forms['export'].submit();" />
        <?php } ?>
      </td>   
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? //} ?>
<br />
<br />
<?
    if($total>0){
?>
    <div align="center">
    <table width="95%" align="center">
    <tr>
    <th align="right" colspan="4"><span>
     </span></th>
    </tr>
    </table>
    </div> 
    <div align="center">
    <table width="95%" align="center" class="adminlist">
    <tr>
    <th align="left" colspan="4"><span class="style5">&nbsp;Table Billing <?=$nama_sold_to?></span></th>
    </tr>
    </table>
    </div> 
    <div align="center">
    <table width="95%" align="center" class="adminlist">
      <tr class="quote">
        <td align="center"><strong >Option</strong></td>
        <td align="center"><strong >No.</strong></td>
        <td align="center"><strong >No. Invoice (Acc)</strong></td>
        <td align="center"><strong >No. Billing</strong></td>
        <td align="center"><strong >Billing Type</strong></td>
         <td align="center"><strong>Date</strong></td>
         <td align="center"><strong>Amount</strong></td>
         <td align="center"><strong>Currency</strong></td>
         <td align="center"><strong>Payer</strong></td>
         <td align="center"><strong>Sold to Party</strong></td>
         <td align="center"><strong>Name Payer</strong></td>
         <td align="center"><strong>Name Sold To Party</strong></td>
      </tr >

  <?  for($i=0; $i<$total;$i++) {

        $b=$i+1;
        if(($i % 2) == 0)   {    
        echo "<tr class='row0'>";
            }
        else    {   
        echo "<tr class='row1'>";
            }   
        // 20100101
        // 01234567
        $TGL_BILL1 = substr($TGL_BILL[$i],6,2)."-".substr($TGL_BILL[$i],4,2)."-".substr($TGL_BILL[$i],0,4);
         
        ?>
        <td align="center"><? #echo '<a href="disp_bill_detail.php?no_bill='.$NO_BILL_SAP[$i].'" target="_blank">[ Detail ]</a>'  ?>
        <a href="javascript: void(0)" onClick="popup('disp_bill_head_detail_adm.php?no_bill=<?=$NO_BILL_SAP[$i];?>&soldto=<?=$KUNAG[$i];?>')">[ Detail ]</a>
        </td>     
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $NO_ACC_SAP[$i]; ?></td>
        <td align="center"><? echo $NO_BILL_SAP[$i]; ?></td>
        <td align="center"><? echo $BILL_TYPE[$i]; ?></td>
        <td align="center"><? echo $TGL_BILL1; ?></td>
        <td align="right"><? echo number_format($TOT_AMOUNT[$i],2,",","."); ?></td>
        <td align="center"><? echo $CURR[$i]; ?></td>
        <td align="center"><? echo $KUNRG[$i]; ?></td>
        <td align="center"><? echo $KUNAG[$i]; ?></td>
        <td align="center"><? echo $NAME1[$i]; ?></td>
        <td align="center"><? echo $NAME2[$i]; ?></td>
        </tr>

      <? } ?>
     <!--  <tr class="quote">
        <td colspan="11" align="center">
        <a href="disp_bill_head.php" target="isi" class="button">Back</a>        </td>
        </tr> -->
    </table>
<br />
<div class="nonPrint" >
<form name="export"  method="post" action="disp_bill_head_adm_xls.php">
<input name="sold_to" id="sold_to" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$X_KUNNR;?>"/>
<input name="vbeln1" type="hidden" id="vbeln1" size=12 value="<?=$X_VBELN_FR;?>"/>
<input name="vbeln2" type="hidden" id="vbeln2" size=12 value="<?=$X_VBELN_TO;?>"/>
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$X_TGL_1;?>"/>
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$X_TGL_2;?>"/>
<input name="Print" type="button" id="Print" value="<?php $translatebhsxx->__1('Print');?>"  onclick="javascript:window.print();" class="button" />     
&nbsp;&nbsp;
<?
// if($user_org=='6000'){
?>
<!--<input name="excel" type="Submit" id="excel" value="<?php $translatebhsxx->__1('Export');?>" class="button"/>  -->
<?    
// }
?>

&nbsp;&nbsp;
<a href="disp_bill_head.php" target="isi" class="button"><?php $translatebhsxx->__1('Back');?></a>
</form>
</div>
    </div>
    <?
    }?>
<div align="center">

<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
